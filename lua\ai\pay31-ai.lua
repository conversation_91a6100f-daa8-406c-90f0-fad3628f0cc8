---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by Administrator.
--- DateTime: 2024-8-20 21:47:47
---


sgs.ai_skill_cardask["@luajinjingGive"] = function(self, data)
	local seoi = data:toPlayer()
    if self.player:is<PERSON><PERSON>cheng() then return "." end 
    if seoi and self:isFriend(seoi) then 
        return "."
    else
        local cards = sgs.QList2Table(self.player:getHandcards())
        self:sortByUseValue(cards, true)
        for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
            local callback = sgs.ai_cardneed[skill:objectName()]
            if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](self.player, cards[1], self) then
                return "."
            end
        end
        if cards[1]:isKindOf("Peach") or cards[1]:isKindOf("Analeptic") or cards[1]:isKindOf("Ofuda") then return "." end 
        if cards[1]:isKindOf("Slash") and seoi:canSlash(self.player, cards[1], false) then return "." end 
        return "$" .. cards[1]:getEffectiveId() 
    end 
end 

sgs.ai_skill_cardask["@luaxiangyaoDiscard"] = function(self, data)
    local cards = sgs.QList2Table(self.player:getHandcards())
    if #cards == 0 then return "." end 
    local tenkai_tsurubami = self.room:getTag("luaxiangyaoTP"):toPlayer() 
    local pattern = data:toString()
    if self.player:getMark("luaxiangyaoBeiSha") == 0 then
        return "."
    end 
	self:sortByKeepValue(cards)
    if self:isFriend(tenkai_tsurubami) then return "." end 
    if not self:damageIsEffective(tenkai_tsurubami, sgs.DamageStruct_Normal, self.player) then return "." end 
	local slash = sgs.Sanguosha:cloneCard("slash")
    if not self:slashIsEffective(slash, self.player, tenkai_tsurubami, false) then 
        slash:deleteLater()
        return "." 
    end
    slash:deleteLater()
    if self.player:getMark("luaxiangyaoBeiSha") == 1 then
        if cards[1]:isKindOf("Peach") or cards[1]:isKindOf("Jink") or (cards[1]:isKindOf("Analeptic") and self.player:getHp() == 1) then
            return "."
        end 
        if tenkai_tsurubami:getMark("drank") == 0 then 
            local result = sgs.ai_skill_cardask["slash-jink"](self, ".", "Jink", tenkai_tsurubami)
            if (not result) or (result == "") or (result == ".") then return "." end 
        end 
    end 
    local function canDiscard(cardX)
        if cardX:getSuit() == sgs.Card_Heart and string.match(pattern, "heart") then
            return true
        elseif cardX:getSuit() == sgs.Card_Diamond and string.match(pattern, "diamond") then
            return true
        elseif cardX:getSuit() == sgs.Card_Club and string.match(pattern, "club") then
            return true
        elseif cardX:getSuit() == sgs.Card_Spade and string.match(pattern, "spade") then
            return true
        end
        return false
    end 
    for _, card in ipairs(cards) do
        if canDiscard(card) then return "$" .. card:getEffectiveId() end 
    end 
end 

sgs.ai_skill_use["@@luaduanjie"] = function(self, prompt, method) 
    self.room:writeToConsole("Hello World! luaduanjie")
 
    local yuyuko = self.room:findPlayerBySkillName("luafanhun")
    local komachi = self.room:findPlayerBySkillName("luaguihang")
     
    
    if not self.player:hasSkill("luaxiangyao") then  
        local count = 0
        for _, enemy in ipairs(self.enemies) do
            count = count + 0.1
            if enemy:getHp() == 1 then 
                if self:isWeak(enemy) then count = count + 0.7 end 
                count = count + 0.2 
                if yuyuko and self:isFriend(yuyuko) and enemy:getMark("@luafanhun") <= 3 then count = count + 0.7 end 
                if komachi and self:isFriend(komachi) and not komachi:isKongcheng() then count = count + 0.5 end 
                if enemy:isKongcheng() then count = count + 0.9 end 
                if enemy:isLord() then count = count + 0.5 end 
            end  
        end 
        if count >= 2 then
            local n
            for _, enemy in ipairs(self.enemies) do
                if enemy:getHp() == 1 then 

                end
            end  
            return "#luaduanjie:.:->" .. friend:objectName()
        end 
    end 


    local targets = {}
	local friends = self.friends_noself
	self:sort(friends, "defense")
    if self.player:getPhase() == sgs.Player_Play then
        if self:getOverflow() < 0 or #friends == 0 then
            table.insert(targets, self.player:objectName())
        end 
    else
        table.insert(targets, self.player:objectName())
    end  
    if not self.player:hasSkill("luaxiangyao") then  
        friends = self.frieds
    else    
        if #targets < 2 and #friends > 0 then table.insert(targets, friends[1]:objectName()) end 
        if #targets < 2 and #friends > 1 then table.insert(targets, friends[2]:objectName()) end 
    end
    return "#luaduanjie:.:->" .. table.concat(targets, "+")
end 
 
local luaxiangyao_skill = {} -- 初始化 luaxiangyao_skill 为空表 
luaxiangyao_skill.name="luaxiangyao" -- 设置 name
table.insert(sgs.ai_skills, luaxiangyao_skill) -- 把这个表加入到 sgs.ai_skills 中   self:HasGou(false, player)
luaxiangyao_skill.getTurnUseCard = function(self)
	return sgs.Card_Parse("#luaxiangyao:.:") 
end 

sgs.ai_skill_use_func["#luaxiangyao"] = function(card, use, self) --未完成

end 


sgs.ai_skill_discard["luachuanmao"] = function(self, discard_num, min_num, optional, include_equip)
	local cards = sgs.QList2Table(self.player:getCards("he"))
	local to_discard = {}
	local compare_func = function(a, b)
		return self:getKeepValue(a) < self:getKeepValue(b)
	end
	table.sort(cards, compare_func)
	
	for _, card in ipairs(cards) do
        if #to_discard >= discard_num then break end
        table.insert(to_discard, card:getEffectiveId())
    end
    return to_discard
end 

sgs.ai_skill_discard["luazhisi"] = function(self, discard_num, min_num, optional, include_equip)
    --我想来想去感觉也只有弃光一个选项，祸灵梦没有任何理由不开技能
    local x_reimu
	local card = self.room:getTag("luazhisiTC"):toCard()
    for _, aplayer in sgs.qlist(self.room:getAlivePlayers()) do
        if aplayer:hasFlag("luazhisiSource") then x_reimu = aplayer end 
    end 
    if not x_reimu then return {} end 
    if self:isFriend(x_reimu) then return {} end 

    if not x_reimu:inMyAttackRange(self.player) then 
        if not card:isKindOf("Slash") then return {} end  
    end 
    
    local cardsB = {}
	local to_discard = {} 
	for _, c in sgs.qlist(self.player:getHandcards()) do
		local flag = string.format("%s_%s_%s", "visible", x_reimu, self.player:objectName())
		if (c:hasFlag("visible") or c:hasFlag(flag))  then
            if (c:isKindOf("Peach") or c:isKindOf("Jink")) then 
                table.insert(to_discard, c:getEffectiveId())
            end 
        else
            table.insert(cardsB, c:getEffectiveId()) 
		end
	end 

    --一张牌的时候

    if self:HasGou(true, _player) and self.player then return false end

end 