---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by Administrator.
--- DateTime: 2023-4-22 14:57:23
---
module("extensions.pay15",package.seeall)
extension = sgs.Package("pay15")

cirnoX = sgs.General(extension,"cirnoX","luacai",3,false,true,false)  
sanaeX = sgs.General(extension,"sanaeX","god",3,false,true,false)   
eternityX = sgs.General(extension, "eternityX", "god", 3, false, true, false)
reisenX = sgs.General(extension, "reisenX", "luayue", 3, false, true, false)

 

luacirnoa = sgs.CreateTriggerSkill{
    name = "luacirnoa",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
luacirnob = sgs.CreateTriggerSkill{
    name = "luacirnob",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
cirnoX:addSkill(luacirnoa)
cirnoX:addSkill(luacirnob)
  

luasanaea = sgs.CreateTriggerSkill{
    name = "luasanaea",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
luasanaeb = sgs.CreateTriggerSkill{
    name = "luasanaeb",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
luasanaec = sgs.CreateTriggerSkill{
    name = "luasanaec",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
sanaeX:addSkill(luasanaea)
sanaeX:addSkill(luasanaeb)
sanaeX:addSkill(luasanaec)

luaeternitya = sgs.CreateTriggerSkill{
    name = "luaeternitya",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
luaeternityb = sgs.CreateTriggerSkill{
    name = "luaeternityb",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
luaeternityc = sgs.CreateTriggerSkill{
    name = "luaeternityc",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
eternityX:addSkill(luaeternitya)
eternityX:addSkill(luaeternityb)
eternityX:addSkill(luaeternityc)

luareisena = sgs.CreateTriggerSkill{
    name = "luareisena",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
luareisenb = sgs.CreateTriggerSkill{
    name = "luareisenb",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
reisenX:addSkill(luareisena)
reisenX:addSkill(luareisenb)

sgs.LoadTranslationTable {
    ["pay15"] = "镜花水月", --注意这里每次要加逗号 

	["cirnoX"] = "琪露诺",	
	["#cirnoX"]= "勇者",	
	["designer:cirnoX"] = "Paysage",	
    ["illustrator:cirnoX"] = "あるは", 
	["luacirnoa"] = "七剑",	 
	[":luacirnoa"] = "分发起始手牌时，你将牌堆顶的七张牌置于武将牌上，称为“剑”；出牌阶段，你可以弃置一张“剑”，使用一张攻击范围2以上的武器牌并获得“决战”“无双”直到结束阶段。",
	["luacirnob"] = "正义",
	[":luacirnob"] = "每阶段限一次，你可以将与你武器牌同花色的一张牌当任意基本牌或【无懈可击】使用或打出。", 
 
 
	["sanaeX"] = "东风谷早苗",
	["designer:sanaeX"] = "Paysage",
	["illustrator:sanaeX"] = "amber",
	["#sanaeX"]= "现代人的现人神",
	["luasanaea"] = "神迹",
	[":luasanaea"] = "一名角色的回合结束时，你可以将你于这回合获得的一张牌当作任意牌使用。", 
	["luasanaeb"] = "秘术",
	[":luasanaeb"] = "锁定技，你每受到1点伤害，你获得一个“秘术”标记，你每有一个“秘术”标记，你的手牌上限+1。你的回合外，你的回复量-1。",
	["luasanaec"] = "客星",
	[":luasanaec"] = "一名角色任意阶段开始时，你可以弃置一个“秘术”标记并摸一张牌。",
	
	["eternityX"] = "爱塔妮缇拉尔瓦 ",
	["#eternityX"]= "常世神",
	["designer:eternityX"] = "Paysage",
	["illustrator:eternityX"] = "明ノ宮 飛鳥",
	["luaeternitya"] = "赐福",	  
	[":luaeternitya"] = "游戏开始或出牌阶段开始时，你可以与一名其他角色互相交换一张手牌。若其给出的点数较大，其翻面。",
	["luaeternityb"] = "永眠",	
	[":luaeternityb"] = "锁定技，除技能以外，角色的武将牌不会翻面，那些角色于你出牌阶段结束时摸一张牌并执行一个额外的出牌阶段。", 
	["luaeternityc"] = "祝巫",	
	[":luaeternityc"] = "武将牌背面朝上的角色可以翻面防止你受到的一次伤害。",  
	
	["reisenX"] = "优昙华院",
	["#reisenX"]= "国士无双",
	["luareisena"] = "月睨",
	[":luareisena"] = "每当你因群体锦囊以外造成伤害时，你可以使用一张牌并摸一张牌。此法使用的牌指定目标后，你可以弃置其一张牌。你于“月睨”期间不能再发动“月睨”。",  
	["luareisenb"] = "国士",
	[":luareisenb"] = "出牌阶段，你可以回复一点体力然后对一名其他角色造成一点伤害。你每第4次发动“国士”时，改为“减少一点体力上限并弃一张牌。”",	
}













