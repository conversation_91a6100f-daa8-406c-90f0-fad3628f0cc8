function SmartAI:canAttack(enemy, attacker, nature)
	attacker = attacker or self.player
	nature = nature or sgs.DamageStruct_Normal
	local damage = 1
	if nature == sgs.DamageStruct_Fire and not enemy:hasArmorEffect("silver_lion")
		and not (enemy:getMark("@qiangweiw") > 0 and enemy:hasSkill("luaqiangwei")) then
		if enemy:hasArmorEffect("vine") then damage = damage + 1 end
		if enemy:getMark("@gale") > 0 then damage = damage + 1 end
		if enemy:hasSkill("ranshang") then damage = damage + 1 end	--yun
	end
	if #self.enemies == 1 or self:hasSkills("jueqing") then return true end
	if self:getDamagedEffects(enemy, attacker) or (self:needToLoseHp(enemy, attacker, false, true) and #self.enemies > 1) or not sgs.isGoodTarget(enemy, self.enemies, self) then return false end
	if self:objectiveLevel(enemy) <= 2 or self:cantbeHurt(enemy, self.player, damage) or not self:damageIsEffective(enemy, nature, attacker) then return false end
	if nature ~= sgs.DamageStruct_Normal and enemy:isChained() and not self:isGoodChainTarget(enemy, self.player, nature) then return false end
	return true
end
local function prohibitUseDirectly(card, player)
    if player:isCardLimited(card, card:getHandlingMethod()) then return true end
    if card:isKindOf("Peach") and player:getMark("Global_PreventPeach") > 0 then return true end
    return false
end


function hasExplicitRebel(room)
	room = room or global_room
	for _, player in sgs.qlist(room:getAllPlayers()) do
		if sgs.isRolePredictable() and  sgs.evaluatePlayerRole(player) == "rebel" then return true end
		if sgs.compareRoleEvaluation(player, "rebel", "loyalist") == "rebel" then return true end
	end
	return false
end

function sgs.isGoodHp(player, bool)
	local goodHp = (player:getHp() > 2 or ((not bool) and player:getHp() > 1)) or getCardsNum("Peach", player) >= 1 or getCardsNum("Analeptic", player) >= 1
					or hasBuquEffect(player)
					or (player:hasSkill("niepan") and player:getMark("@nirvana") > 0)
					or (player:hasSkill("fuli") and player:getMark("@laoji") > 0)
	if goodHp then
		return goodHp
	else
		for _, p in sgs.qlist(global_room:getOtherPlayers(player)) do
			if sgs.compareRoleEvaluation(p,"rebel","loyalist") == sgs.compareRoleEvaluation(player,"rebel","loyalist")
					and getCardsNum("Peach", p) > 0 and not global_room:getCurrent():hasSkill("wansha") then
				return true
			end
		end
		return false
	end
end

function sgs.isGoodTarget(player, targets, self, isSlash) --判断卖血技防御 或者 孙笨等
	local arr = {"jieming", "guixin", "fangzhu", "neoganglie", "nosmiji", "xuehen", "vsganglie", "ganglie", "nosganglie", "nosyiji", "luayuetuan", "luagongcheng", "luafengmo"}	--yun xueji, yiji不厉害，暂不考虑
	local kp_skill = false	--yun
	local m_skill = false
	local attacker = global_room:getCurrent()
	if attacker and attacker:hasSkills("luajiejie|luayanxun") then return true end
	if targets and type(targets) == "table" then
		if #targets == 1 then return true end
		local foundtarget = false
		for i = 1, #targets, 1 do
			if sgs.isGoodTarget(targets[i]) then	--and not self:cantbeHurt(targets[i]) then	--yun有警告
				foundtarget = true
				break
			end
		end
		if not foundtarget then return true end
	end

	for _, masochism in ipairs(arr) do
		if player:hasSkill(masochism) then
			if attacker and attacker:hasSkill("jueqing") then m_skill = false
			elseif attacker and attacker:hasSkills("nosdanshou") then m_skill = false	--yun
			elseif attacker and attacker:hasSkill("tieji") and isSlash then m_skill = false	--yun
			elseif attacker and attacker:hasSkill("LuaFengyin") then m_skill = false	--yun
			elseif masochism == "jieming" and self and self:getJiemingChaofeng(player) > -4 then m_skill = false
				--elseif masochism == "yiji" and self and not self:findFriendsByType(sgs.Friend_Draw, player) then m_skill = false
			elseif masochism == "nosyiji" and self and not self:findFriendsByType(sgs.Friend_Draw, player) then m_skill = false	--yun
			else
				m_skill = true
				break
			end
		end
	end
	if player:hasSkill("luaouxiang") and self then
		local count = 0
		for _, enemy in ipairs(self.enemies) do
			count = count + enemy:getHandcardNum()
		end
		if count > 4 and #self.enemies > 1 then return false end
	end
	if player:hasSkills("nosmiji|yyuanlao|yinghun|yinghun_po") and not player:isWounded() then kp_skill = true end	--yun	xueji不厉害，暂不考虑
	if player:hasSkill("baobian") and player:getHp() >= 4 then kp_skill = true end
	if player:hasSkills("luaqiji|luasanaex") and player:getHp() >= 3 then kp_skill = true end
	if player:hasSkill("longhun") and player:getCards("he"):length() > 2 and player:getHp() == 2 then kp_skill = true end
	if player:hasSkill("hunzi") and player:getMark("hunzi") == 0 and player:getHp() == 2 then kp_skill = true end
	if player:hasSkill("yhunzi") and player:getMark("yhunzi") == 0 and player:getHp() == 2 then kp_skill = true end

	if not (attacker and attacker:hasSkill("jueqing")) and player:hasSkill("huilei") and not player:isLord() and player:getHp() == 1 then
		if attacker and attacker:getHandcardNum() >= 4 then return false end
		return sgs.compareRoleEvaluation(player, "rebel", "loyalist") == "rebel"
	end

	if not (attacker and attacker:hasSkill("jueqing")) and player:hasSkill("wuhun") and not player:isLord()
			and ((attacker and attacker:isLord()) or player:getHp() <= 2) then
		return false
	end

	--[[
	if player:hasLordSkill("shichou") and player:getMark("@hate") == 0 then
		for _, p in sgs.qlist(player:getRoom():getOtherPlayers(player)) do
			if p:getMark("hate_" .. player:objectName()) > 0 and p:getMark("@hate_to") > 0 then
				return false
			end
		end
	end
	]]	--yun

	if isSlash and self and (self:hasCrossbowEffect() or self:getCardsNum("Crossbow") > 0) and self:getCardsNum("Slash") > player:getHp() then
		return true
	end

	if player:hasSkill("hunzi") and player:getMark("hunzi") == 0 and player:isLord() and player:getHp() == 2 and sgs.current_mode_players["loyalist"] > 0 then
		return false
	end

	if player:hasSkill("yhunzi") and player:getMark("yhunzi") == 0 and player:isLord() and player:getHp() == 2 and sgs.current_mode_players["loyalist"] > 0 then
		return false
	end

	if (m_skill or kp_skill) and sgs.isGoodHp(player) then
		return false
	else
		return true
	end
end

function sgs.getDefenseSlash(player, self)
	if not player then return 0 end
	local attacker = self and self.player or global_room:getCurrent()
	local defense = getCardsNum("Jink", player, attacker)

	local knownJink = getKnownCard(player, attacker, "Jink", true)

	if sgs.card_lack[player:objectName()]["Jink"] == 1 and knownJink == 0 then defense = 0 end

	defense = defense + knownJink * 1.2

	local hasEightDiagram = false

	if (player:hasArmorEffect("eight_diagram") or (player:hasSkills("bazhen|linglong") and not player:getArmor()))
			and not IgnoreArmor(attacker, player) then
		hasEightDiagram = true
	end
	if player:hasArmorEffect("tengu") then
		defense = defense + 0.7
	end
	if hasEightDiagram then
		defense = defense + 1.3
		if player:hasSkills("tiandu|luatiandu") then defense = defense + 0.6 end
		if player:hasSkill("gushou") then defense = defense + 0.4 end
		if player:hasSkills("leiji") then defense = defense + 0.4 end
		if player:hasSkills("nosleiji") then defense = defense + 0.4 end
		if player:hasSkills("olleiji") then defense = defense + 0.4 end
		if player:hasSkill("noszhenlie") then defense = defense + 0.2 end
		if player:hasSkill("hongyan") then defense = defense + 0.2 end
		if player:hasSkill("yqinxue") then defense = defense + 0.4 end  --yun
	end

	if getCardsNum("Jink", player, global_room:getCurrent()) >= 1 then
		if player:hasSkill("mingzhe") then defense = defense + 0.2 end
		if player:hasSkill("gushou") then defense = defense + 0.2 end
		if player:hasSkills("tuntian+zaoxian") then defense = defense + 1.5 end

		if player:hasSkill("yqinxue") then defense = defense + 0.2 end	--yun
		if player:hasSkill("yyuanlv") then defense = defense + 0.2 end
	end
	local seija = global_room:findPlayerBySkillName("luafanze")
	if seija and seija:inMyAttackRange(player) and player:getHandcardNum() == 1 then defense = defense - 1 end
	if (player:getMark("@qiangweiw") == 0 and player:hasSkill("luaqiangwei")) then defense = defense - 1 end
	if player:hasSkill("luacaihuo") then
		local count = 0
		for i = 0, 500 do
			local str = "luacaihuo" .. tostring(i)
			if player:getMark(str) > 0 then
				count = count + 1
			end
		end
		defense = defense + 1.5 - 0.2*count
	end
	if player:hasSkill("luajieyouX") then defense = defense - 1.5 end
	if player:hasSkill("luaqiji") and player:getHp() >= 3 then defense = defense + 1 end --早苗
	if player:hasSkill("luacaihuosp") then defense = defense + 2 end
	if player:hasSkill("luacuiruo") then defense = defense - 0.5 end
	if player:hasSkill("luaguoshi") then defense = defense + 2 end
	if player:hasSkill("luasanaex") then defense = defense + 1 end
	if player:hasSkill("luaqijin") and not player:isWounded() then defense = defense + 1 end
	if player:hasSkill("LuaQiyuan") then defense = defense + 1 end
	if player:hasSkill("luahuaxu") and player:getMaxHp() < 3 then defense = defense + 2 end
	if player:hasSkill("luatianhu")  then defense = defense + 0.5 end
	if player:hasSkill("lualvzhi")  then defense = defense - 0.5 end
	if player:hasSkill("lualvzhiu")  then defense = defense - 0.5 end
	if player:hasSkill("luajinjing")  then defense = defense - 0.5 end
	if player:getMark("@lualongyan") > 0  then defense = defense - 1.5 end
	if player:hasSkill("luatianyan")  then defense = defense + 0.5 end
	if player:hasSkill("luayuetuan") and player:hasSkill("luayuechong") then defense = defense + 0.5 end --pay 小黄兔小蓝兔
	if player:hasSkill("luayuetuan") and player:hasSkill("luayuechong") then defense = defense + 0.5 end
	if player:hasSkill("luayequ") then defense = defense + 1 end
	if player:hasSkill("aocai") and player:getPhase() == sgs.Player_NotActive then defense = defense + 0.5 end
	if player:hasSkill("luajicai") then defense = defense + player:getHandcardNum() end
	if player:hasSkill("wanrong") and not hasManjuanEffect(player) then defense = defense + 0.5 end
	if player:hasSkill("luahongye") and player:isKongcheng() and not player:hasFlag("luahongye4") then defense = defense + 2 end
	if player:hasSkill("Luayuyi") and not player:isKongcheng() then defense = defense + 0.6 end
	if player:hasSkill("luaxinyang") then defense = defense - 1.25 end
	if player:hasSkill("luakuangxiang") then defense = defense - 1 end
	if player:hasSkill("luaouxiang") then defense = defense + 4 end
	if (player:getMark("luaxinyang") > 0) then defense = defense + 1 end
	if player:hasSkill("luaqijin") and player:getHp() < 3 then defense = defense - 1 end
	if player:hasSkill("Luayuyi") and player:hasSkill("Lualeidian") and (not player:isKongcheng())
			and (player:getHandcardNum() == player:getMaxCards()) then defense = defense + 0.6 end

	local hujiaJink = 0
	if player:hasLordSkill("hujia") then
		local lieges = global_room:getLieges("wei", player)
		for _, liege in sgs.qlist(lieges) do
			if sgs.compareRoleEvaluation(liege,"rebel","loyalist") == sgs.compareRoleEvaluation(player,"rebel","loyalist") then
				hujiaJink = hujiaJink + getCardsNum("Jink", liege, global_room:getCurrent())
				if liege:hasArmorEffect("eight_diagram") then hujiaJink = hujiaJink + 0.8 end
			end
		end
		defense = defense + hujiaJink
	end

	if player:getMark("@tied") > 0 and not attacker:hasSkill("jueqing") then defense = defense + 1 end

	if attacker:canSlashWithoutCrossbow() and attacker:getPhase() == sgs.Player_Play then
		if self and self:canLiegong(player, attacker) then defense = 0 end	--yun
	end

	local jiangqin = global_room:findPlayerBySkillName("niaoxiang")
	local need_double_jink = attacker:hasSkills("wushuang|drwushuang")
			or (attacker:hasSkill("roulin") and player:isFemale())
			or (player:hasSkill("roulin") and attacker:isFemale())
			or (jiangqin and jiangqin:isAdjacentTo(player) and attacker:isAdjacentTo(player) and self and self:isFriend(jiangqin, attacker))
	if need_double_jink and getKnownCard(player, attacker, "Jink", true, "he") < 2
			and getCardsNum("Jink", player) < 1.5
			and (not player:hasLordSkill("hujia") or hujiaJink < 2) then
		defense = 0
	end

	if attacker:hasSkill("dahe") and player:hasFlag("dahe") and getKnownCard(player, attacker, "Jink", true, "he") == 0 and getKnownNum(player) == player:getHandcardNum()
			and not (player:hasLordSkill("hujia") and hujiaJink >= 1) then
		defense = 0
	end

	local jink = sgs.Sanguosha:cloneCard("jink")
	if player:isCardLimited(jink, sgs.Card_MethodUse) then defense = 0 end

	if player:hasFlag("QianxiTarget") then
		local red = player:getMark("@qianxi_red") > 0
		local black = player:getMark("@qianxi_black") > 0
		if red then
			if player:hasSkill("qingguo") or (player:hasSkill("longhun") and player:isWounded()) then
				defense = defense - 1
			else
				defense = 0
			end
		elseif black then
			if player:hasSkill("qingguo") then
				defense = defense - 1
			end
		end
	end

	if not player:hasSkills("luawangxiang|luafenxing") then
		defense = defense + math.min(player:getHp() * 0.45, 10)
	else
		defense = defense + 2
	end

	if attacker and attacker:hasSkill("luadaidu") and not player:isWounded() then
		defense = defense - math.min(player:getHp() * 0.45, 10)
		defense = defense - 1
	end
	if attacker and attacker:hasSkill("luamengyan") then
		if not player:hasSkill("luamengyan") then defense = defense - 1 end
	end

	if attacker and not attacker:hasSkill("jueqing") then
		local m = sgs.masochism_skill:split("|")
		for _, masochism in ipairs(m) do
			if player:hasSkill(masochism) and sgs.isGoodHp(player) then
				defense = defense + 1
			end
		end
		if player:hasSkill("jieming") then defense = defense + 4 end
		if player:hasSkill("yiji") then defense = defense + 3 end
		if player:hasSkill("guixin") then defense = defense + 4 end
		if player:hasSkill("nosyiji") then defense = defense + 4 end	--yun

		if player:hasSkill("yuce") and not player:isKongcheng() then defense = defense + 2 end	--yun
		if player:hasSkill("ygangzhi") then defense = defense + 3 end
		if player:hasSkills("tianxiang|ol_tianxiang") then defense = defense + player:getHandcardNum() * 0.5 end
		if player:hasSkill("jiushi") and not player:faceUp() and player:getHp() > 1 then defense = defense + 1 end
		if player:getMark("@tied") > 0 then defense = defense + 1 end
	end

	if attacker and attacker:hasSkills("jueqing|tieji") then	--yun
		local m = sgs.masochism_skill:split("|")
		for _, masochism in ipairs(m) do
			if player:hasSkill(masochism) then
				defense = defense - 5
			end
		end
	end
	local wuxiao = false
	if attacker and attacker:hasSkills("luajingdan|tieji|luahuaifei|luahakurei") then wuxiao = true end
	if attacker and not attacker:isChained() and player:hasSkill("luamaobu") then defense = defense + 1.5 end
	if not sgs.isGoodTarget(player) then defense = defense + 10 end

	if player:hasSkills("nosrende|rende") and player:getHp() > 2 then defense = defense + 1 end
	if player:hasSkill("kuanggu") and player:getHp() > 1 then defense = defense + 0.2 end

	if player:hasSkill("LuaHebao") and (player:getMark("LuaHebao") == 0) and (player:getHp() > 4)
			and (((player:getHandcardNum() > 5) and (player:getPhase() == sgs.Player_NotActive)) or (player:getHandcardNum() > 7)) then defense = defense + 1 end --pay 阿空

	for  _, aplayer in sgs.qlist(global_room:getAlivePlayers()) do
		if self and aplayer:distanceTo(player) <= 1 and (aplayer:objectName() ~= player:objectName()) and self:isFriend(aplayer, player)
				and (aplayer:hasSkill("luahuixuan") or player:hasSkill("luahuixuan")) then
			for _, card in sgs.list(aplayer:getHandcards()) do
				if card:hasFlag("luashouhuc") then defense = math.max(9, defense) end --pay 阿吽
			end
		end
	end
	if player:hasSkill("luahuadie") then defense = defense + 0.75 end
	if player:hasSkill("luaxinshi") then defense = defense + player:getHandcardNum() * 0.3 end --pay 慧音
	if player:hasSkill("kofkuanggu") and player:getHp() > 1 then defense = defense + 0.2 end
	if player:hasSkill("kuanggu_po") and player:getHp() > 1 then defense = defense + 0.2 end
	if player:hasSkill("luachaofan") then defense = defense - 2 end
	if player:hasSkill("LuaQiyuan") then
		defense = defense + 0.75
		local notmark = true
		for _, aplayer in sgs.qlist(global_room:getAllPlayers()) do
			if aplayer:getMark("@LuaYizuo") > 0 then
				notmark = false
			end
		end
		if not notmark then defense = defense + 0.75 end
	end
	if player:hasSkill("luajingjie") then defense = defense - 0.75 end
	if player:getMark("@LuaYizuo") > 0 then defense = defense + 1 end
	if player:hasSkill("luatianzhao") and not wuxiao then defense = defense + 1 end
	if player:hasSkill("LuaYuanzu") and player:hasSkill("LuaShanguang") and (player:getHandcardNum() > 0) and not wuxiao then defense = defense + 0.35 * player:getHandcardNum() end
	if player:hasSkill("Luaxinwu") then defense = defense - 1 end --pay 秦心
	if player:hasSkill("zaiqi") and player:getLostHp() < 2 then defense = defense + 1 end	--yun
	if player:hasSkill("yxianfeng") and player:getLostHp() < 2 then defense = defense + 1 end
	if player:hasSkill("luajianglin") then defense = defense + 0.75 end
	if player:hasSkill("luayuling") then defense = defense + 2 end
	if player:hasSkill("nosmiji") and player:getLostHp() < 1 then defense = defense + 2 end
	if player:hasSkills("yyuanlao|yqinku") and player:getLostHp() < 1 then defense = defense + 2 end
	if player:hasSkill("baobian") and player:getHp() > 3 then defense = defense + 2 end
	if player:hasSkill("luaxianfeng") then defense = defense - 0.5 end
	if player:hasSkill("luaxianfeng") and player:getHp() < 4 then defense = defense - 0.5 end
	if player:hasSkill("xiangle") then defense = defense + 0.5 end
	if player:hasSkill("renwang") then defense = defense + 0.2 end
	if player:hasSkill("lualongwen") then defense = defense - 2 end
	if player:hasSkill("tianming") then defense = defense + 0.2 end
	if player:hasSkill("luaniluan") then defense = defense - 0.3 end  --pay	
	if player:hasSkill("luatianshi") then defense = defense - 0.5 end  --pay	
	if player:hasSkill("luahuapu") then defense = defense - 0.5 end  --pay
	if player:getHp() > getBestHp(player) then defense = defense + 0.8 end
	if ((player:getHp() <= 2) and not player:hasSkill("luafenxing"))
			or (player:hasSkill("luafenxing") and player:getHandcardNum() <= 1) then defense = defense - 0.4 end

	local playernum = global_room:alivePlayerCount()
	if (player:getSeat() - attacker:getSeat()) % playernum >= playernum - 2
			and playernum > 3 and (player:getHandcardNum() <= 2 or player:getMark("@ofudaa") > 0)
			and player:getHp() <= 2 then
		defense = defense - 0.4
	end
	if player:getMark("@ofudaa") > 0 then
		defense = defense - 0.5
		local m = sgs.masochism_skill:split("|")
		for _, masochism in ipairs(m) do
			if player:hasSkill(masochism) then
				defense = defense - 1
			end
		end
	end

	local caoang = global_room:findPlayerBySkillName("kangkai")	--yun
	local can_kangkai
	if caoang and self and self:isFriend(caoang, player) and caoang:distanceTo(player) == 1 then can_kangkai = true end
	if player:hasSkill("kangkai") then defense = defense + 0.4 end
	if can_kangkai and not player:hasSkill("kangkai") then defense = defense + 0.5 end

	if player:getHandcardNum() == 0 and hujiaJink == 0 and not player:hasSkill("kongcheng") and not attacker:hasSkill("Luashenqiang2") then
		if player:getHp() <= 1 then defense = defense - 2.5 end
		if player:getHp() == 2 then defense = defense - 1.5 end
		if not hasEightDiagram then defense = defense - 2 end
		if attacker:hasWeapon("guding_blade") and self and self:canHitDown(player, attacker) then	--yun
			defense = defense - 2
		end
	end

	local has_fire_slash
	local cards = sgs.QList2Table(attacker:getHandcards())
	for i = 1, #cards, 1 do
		if (attacker:hasWeapon("fan") and cards[i]:objectName() == "slash" and not cards[i]:isKindOf("ThunderSlash")) or cards[i]:isKindOf("FireSlash")  then
			has_fire_slash = true
			break
		end
	end

	if player:hasArmorEffect("vine") and not IgnoreArmor(attacker, player) and has_fire_slash then
		defense = defense - 0.6
	end

	if isLord(player) then
		defense = defense - 0.4
		if sgs.isLordInDanger() then defense = defense - 0.7 end
	end

	if not player:faceUp() then defense = defense - 0.35 end
	if attacker and (attacker:hasSkill("luabenwo") and not player:inMyAttackRange(attacker)) then defense = defense - 1 end
	if player:containsTrick("indulgence") and not player:containsTrick("YanxiaoCard") then defense = defense - 0.15 end
	if player:containsTrick("supply_shortage") and not player:containsTrick("YanxiaoCard") then defense = defense - 0.15 end

	if (attacker:hasSkill("roulin") and player:isFemale()) or (attacker:isFemale() and player:hasSkill("roulin")) then
		defense = defense - 2.4
	end
	if player:hasSkill("LuaFeixiang") and (player:getPile("qizhi2") and (not player:getPile("qizhi2"):isEmpty())) then defense = defense - player:getPile("qizhi2"):length() end --pay 天子
	if not hasEightDiagram then   --杀嘲讽
		if player:hasSkill("jijiu") then defense = defense - 3 end

		if player:hasSkill("dimeng") then defense = defense - 2.5 end
		if player:hasSkill("guzheng") and knownJink == 0 then defense = defense - 2.5 end
		if player:hasSkill("qiaobian") then defense = defense - 2.4 end
		if player:hasSkill("jieyin") then defense = defense - 2.3 end
		if player:hasSkills("noslijian|lijian") then defense = defense - 2.2 end
		if player:hasSkill("nosmiji") and player:isWounded() then defense = defense - 1.5 end
		if player:hasSkill("xiliang") and knownJink == 0 then defense = defense - 2 end
		if player:hasSkill("shouye") then defense = defense - 2 end
		if player:hasSkill("yquanshi") then defense = defense - 2 end  --yun
		if player:hasSkill("yshuntian") then defense = defense - 1.5 end  --yun
		if player:hasSkill("zhengnan") then defense = defense - 2.5 end  --yun
		if player:hasSkill("liewei") then defense = defense - 2.49 end  --yun
	end
	if player:getMark("@LuaBisha2") == 1 then defense = defense - 2.4 end
	if player:getMark("@LuaBisha2") == 2 then defense = defense - 99 end

	if attacker:hasSkill("luajiawei") and attacker:getPhase() == sgs.Player_Play then  --pay 二童子
		if player:getHp() > attacker:getHp() then
			defense = defense - player:getHp() * 2
		end
	end

	return defense
end

sgs.ai_compare_funcs["defenseSlash"] = function(a, b)
	return sgs.getDefenseSlash(a) < sgs.getDefenseSlash(b)
end

function SmartAI:slashProhibit(card, enemy, from)
	local mode = self.room:getMode()
	if mode:find("_mini_36") then return self.player:hasSkill("keji") end
	card = card or sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
	from = from or self.player
	if self.room:isProhibited(from, enemy, card) then return true end
	local nature = card:isKindOf("FireSlash") and sgs.DamageStruct_Fire
			or card:isKindOf("ThunderSlash") and sgs.DamageStruct_Thunder
	for _, askill in sgs.qlist(enemy:getVisibleSkillList(true)) do
		local filter = sgs.ai_slash_prohibit[askill:objectName()]
		if filter and type(filter) == "function" and filter(self, from, enemy, card) then return true end
	end

	if from:hasSkill("luacuiruo") and not self:isFriend(enemy, from) then
		local slash_num, analeptic_num, jink_num, ofuda_num
		if from:objectName() == self.player:objectName() then
			slash_num = self:getCardsNum("Slash")
			analeptic_num = self:getCardsNum("Analeptic")
			jink_num = self:getCardsNum("Jink")
			ofuda_num = self:getCardsNum("Ofuda")
		else
			slash_num = getCardsNum("Slash", from, self.player)
			analeptic_num = getCardsNum("Analpetic", from, self.player)
			jink_num = getCardsNum("Jink", from, self.player)
			ofuda_num = getCardsNum("Jink", from, self.player)
		end
		if self.player:getHandcardNum() == 2 then
			if self.player:hasSkill("beifa") then self.player:setFlags("stack_overflow_xiangle") end
			local needkongcheng = self:needKongcheng()
			self.player:setFlags("-stack_overflow_xiangle")
			if needkongcheng and slash_num + analeptic_num + jink_num < 2 then return true end
		end
		if slash_num + analeptic_num + math.max(jink_num - 1, 0) < 2 then return true end
	end
	if self:isFriend(enemy, from) then
		if card:isKindOf("FireSlash") or from:hasWeapon("fan") or from:hasSkill("zonghuo") then
			if not from:hasSkill("jueqing") and enemy:hasArmorEffect("vine")	--yun
					and not (enemy:isChained() and self:isGoodChainTarget(enemy, from, nil, nil, card)) then return true end
		end
		if enemy:isChained() and (card:isKindOf("NatureSlash") or from:hasSkill("zonghuo")) and self:slashIsEffective(card, enemy, from)
				and (not self:isGoodChainTarget(enemy, from, nature, nil, card) and not from:hasSkill("jueqing")) then return true end
		if getCardsNum("Jink",enemy, from) == 0 and enemy:getHp() < 2 and self:slashIsEffective(card, enemy, from) then return true end
		if enemy:isLord() and self:isWeak(enemy) and self:slashIsEffective(card, enemy, from) then return true end
		if from:hasWeapon("guding_blade") and self:canHitDown(enemy, from) then return true end	--yun
	else
		if (card:isKindOf("NatureSlash") or from:hasSkill("zonghuo")) and not from:hasSkill("jueqing") and enemy:isChained()
				and not self:isGoodChainTarget(enemy, from, nature, nil, card) and self:slashIsEffective(card, enemy, from) then
			return true
		end
		local nue = self.room:findPlayerBySkillName("Luamengyan")
		if nue and nue:isAlive() and nue:inMyAttackRange(enemy) and not self:slashIsEffective(card, nue, from) then return true end
	end

	local caijue = false
	local shikieiki = self.room:findPlayerBySkillName("luapanjue")
	if shikieiki and self:isFriend(shikieiki) and not shikieiki:isKongcheng() then caijue = true end
	local kitcho = self.room:findPlayerBySkillName("lualongwen")
	if kitcho and self:isEnemy(kitcho) and kitcho:hasFlag("luamoulue") and kitcho:hasSkill("luamoulue") and not caijue
			and kitcho:getHp() == 1 and kitcho:containsTrick("longwen") and enemy:objectName() ~= kitcho:objectName() then return true end

	return not self:slashIsEffective(card, enemy, from)
end

function SmartAI:canLiuli(other, another)
	if not other:hasSkill("liuli") then return false end
	if type(another) == "table" then
		if #another == 0 then return false end
		for _, target in ipairs(another) do
			if target:getHp() < 3 and self:canLiuli(other, target) then return true end
		end
		return false
	end

	if not self:needToLoseHp(another, self.player, true, true) or not self:getDamagedEffects(another, self.player, true) then return false end	--yun
	local n = other:getHandcardNum()
	if n > 0 and (other:distanceTo(another) <= other:getAttackRange()) then return true
	elseif other:getWeapon() and other:getOffensiveHorse() and (other:distanceTo(another) <= other:getAttackRange()) then return true
	elseif other:getWeapon() or other:getOffensiveHorse() then return other:distanceTo(another) <= 1
	else return false end
end
--第五个函数用于蕾米莉亚·斯卡蕾特，防止死循环特供版 --杀有效，但是不造成伤害是返回false的
function SmartAI:slashIsEffective(slash, to, from, ignore_armor, remilia)
	if not slash or not to then self.room:writeToConsole(debug.traceback()) return end
	if not remilia and self.player:hasSkill("luashizi") then remilia = true end
	from = from or self.player
	if to:hasSkill("zuixiang") and to:isLocked(slash) then return false end
	if to:hasSkill("yizhong") and not to:getArmor() then
		if slash:isBlack() then
			return false
		end
	end
	if to:hasSkill("xiemu") and slash:isBlack() and to:getMark("@xiemu_" .. from:getKingdom()) > 0 then return end
	if to:getMark("@late") > 0 then return false end

	if to:hasSkill("ywushen") and to:getHp() < 2 and slash:isBlack() and not from:hasSkill("jueqing") then --yun
		return false
	end
	if self:isFriend(to, from) then	--yun
		if from:hasSkill("luacuiruo") then return false end
		if to:hasSkill("xiangle") then return false end
		if not from:hasSkill("jueqing") and to:hasSkill("yuce") and not to:isKongcheng() and to:getHp() > 1
				and not self:hasHeavySlashDamage(from, slash, to) then
			return false
		end
		if not from:hasSkill("jueqing") and from:hasSkill("ybaobian") and from:getKingdom() == to:getKingdom()
				and self:getTWBaobian(to, from) < 1 then
			return false
		end
	end

	local natures = {
		Slash = sgs.DamageStruct_Normal,
		FireSlash = sgs.DamageStruct_Fire,
		ThunderSlash = sgs.DamageStruct_Thunder,
	}

	local nature = natures[slash:getClassName()]
	self.equipsToDec = sgs.getCardNumAtCertainPlace(slash, from, sgs.Player_PlaceEquip)
	if from:hasSkill("zonghuo") then nature = sgs.DamageStruct_Fire end
	local eff = self:damageIsEffective(to, nature, from)
	self.equipsToDec = 0
	if not eff then return false end

	if not ignore_armor and from:objectName() == self.player:objectName() then	--yun
		if to:getArmor() and from:hasSkills("moukui|jianchu|olpojun") then
			if self:isEnemy(to) then
				return true
			end
		end
	end

	if IgnoreArmor(from, to, slash) or ignore_armor then
		return true
	end

	if to:hasArmorEffect("renwang_shield") and slash:isBlack() then return false end
	if to:hasArmorEffect("vine") and not slash:isKindOf("NatureSlash") then
		local skill_name = slash:getSkillName() or ""
		local can_convert = false
		if skill_name == "guhuo" or skill_name == "nosguhuo" or skill_name == "zhuhai" then	--yun
			can_convert = true
		else
			local skill = sgs.Sanguosha:getSkill(skill_name)
			if not skill or skill:inherits("FilterSkill") then
				can_convert = true
			end
		end
		return can_convert and (from:hasWeapon("fan") or from:hasSkill("zonghuo") or (from:hasSkill("lihuo") and not self:isWeak(from)))
	end
	if to:hasSkill("luahuoqiu") and (slash:isKindOf("ThunderSlash") or slash:isKindOf("FireSlash")) then
		return false
	end
	if slash:isKindOf("ThunderSlash") then
		local f_slash
		if remilia then
			for _, slashX in ipairs(self:getCards("Slash")) do
				if slashX:isKindOf("FireSlash") then f_slash = slashX end
			end
		else
			f_slash = self:getCard("FireSlash")
		end
		if f_slash and self:hasHeavySlashDamage(from, f_slash, to, true) > self:hasHeavySlashDamage(from, slash, to, true)
				and (not to:isChained() or self:isGoodChainTarget(to, from, sgs.DamageStruct_Fire, nil, f_slash)) then
			return self:slashProhibit(f_slash, to, from)
		end
	elseif slash:isKindOf("FireSlash") then
		local t_slash
		if remilia then
			for _, slashX in ipairs(self:getCards("Slash")) do
				if slashX:isKindOf("ThunderSlash") then t_slash = slashX end
			end
		else
			t_slash = self:getCard("ThunderSlash")
		end
		if t_slash and self:hasHeavySlashDamage(from, t_slash, to, true) > self:hasHeavySlashDamage(from, slash, to, true)
				and (not to:isChained() or self:isGoodChainTarget(to, from, sgs.DamageStruct_Thunder, nil, t_slash)) then
			return self:slashProhibit(t_slash, to, from)
		end
	end

	return true
end

function SmartAI:slashIsAvailable(player, slash)
	player = player or self.player
	slash = slash or self:getCard("Slash", player)
	if not slash or not slash:isKindOf("Slash") then slash = sgs.Sanguosha:cloneCard("slash") end
	assert(slash)
	return slash:isAvailable(player)
end

function sgs.isJinkAvailable(from, to, slash, judge_considered)
	return (not judge_considered and from:hasSkills("tieji|nostieji"))
			--or self:canLiegong(to, from)	--yun
			or (from:hasFlag("ZhaxiangInvoked") and slash and slash:isRed())
			or (from:hasSkill("luabenwo") and not to:inMyAttackRange(from))
end

function SmartAI:findWeaponToUse(enemy)
	local weaponvalue = {}
	local hasweapon
	for _, c in sgs.qlist(self.player:getHandcards()) do
		if c:isKindOf("Weapon") then
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			self:useEquipCard(c, dummy_use)
			if dummy_use.card then
				weaponvalue[c] = self:evaluateWeapon(c, self.player, enemy)
				hasweapon = true
			end
		end
	end
	if not hasweapon then return end
	if self.player:hasSkill("luajunzhen") then return end
	if self.player:hasSkill("luajinshu") then return end
	if self.player:hasSkill("luahongtu") and self.player:getMark("@luahongtu") == 0 then return end
	if self.player:getWeapon() then weaponvalue[self.player:getWeapon()] = self:evaluateWeapon(self.player:getWeapon(), self.player, enemy) end
	local max_value, max_card = -1000
	for c, v in pairs(weaponvalue) do
		if v > max_value then max_card = c max_value = v end
	end
	if self.player:getWeapon() and self.player:getWeapon():getEffectiveId() == max_card:getEffectiveId() then return false end
	if self.player:hasSkill("luachaofan") and (not self.player:hasFlag("forbidChaofan")) and max_card:getNumber() <= self.player:getMark("chaofan") then
		return nil
	end
	return max_card
end

function SmartAI:isPriorFriendOfSlash(friend, card, source)
	source = source or self.player
	local huatuo = self.room:findPlayerBySkillName("jijiu")
	if not self:hasHeavySlashDamage(source, card, friend) and card:getSkillName() ~= "lihuo"
			and ((self:findLeijiTarget(friend, 50, source, -1) or (self:findLeijiTarget(friend, 50, source, 1) and friend:isWounded()))
				or (friend:isLord() and source:hasSkill("guagu") and friend:getLostHp() >= 1 and getCardsNum("Jink", friend, source) == 0)
				or (friend:hasSkill("jieming") and source:hasSkill("nosrende") and (huatuo and self:isFriend(huatuo, source)))
				or (friend:hasSkill("hunzi") and friend:getMark("hunzi") < 1 and friend:getHp() == 2 and self:getDamagedEffects(friend, source))	--yun
				or (friend:hasSkill("yhunzi") and friend:getMark("yhunzi") < 1 and friend:getHp() == 2 and self:getDamagedEffects(friend, source))
				or self:needChuanxin(friend, source))	--yun
				or self:getTWBaobian(friend, source) > 2 --yun
				or self:hasNosQiuyuanEffect(source, friend)
				then
		return true
	end
	if not source:hasSkill("jueqing") and card:isKindOf("NatureSlash") and friend:isChained() and self:isGoodChainTarget(friend, source, nil, nil, card) then return true end
	return
end


function koishiuseSlash(self, card, no_distance, rangefix, basicnum)
	if (self:getOverflow() < 0) and card then
		local targets = {}
		local forbidden = {}
		self:sort(self.enemies, "defenseSlash")
		for _, enemy in ipairs(self.enemies) do
			if (not self:slashProhibit(card, enemy)) and sgs.isGoodTarget(enemy, self.enemies, self, true) 
				and (self:isWeak(enemy) and (self:YouMu2(enemy, true) or self:hasHeavySlashDamage(self.player, card, enemy))) then
				if self:hasNosQiuyuanEffect(self.player, enemy) or self:hasQiuyuanEffect(self.player, enemy) then table.insert(forbidden, enemy)
				elseif not self:getDamagedEffects(enemy, self.player, true) then table.insert(targets, enemy)
				else table.insert(forbidden, enemy) end
			end
		end		
		if #targets == 0 and #forbidden > 0 then targets = forbidden end
		for _, target in ipairs(targets) do
			if  (self.player:canSlash(target, card, not no_distance, rangefix)
							or (self.predictedRange and self.player:distanceTo(target, rangefix) <= self.predictedRange))
						and self:objectiveLevel(target) > 3
						and self:slashIsEffective(card, target, self.player)
						and not ((target:hasSkill("xiangle") or self.player:hasSkill("luacuiruo")) and basicnum < 2) then
				return false
			end 
		end 
		return true 
	end 
	return false 
end 
function SmartAI:useCardSlash(card, use)
	if not use.isDummy and not self:slashIsAvailable(self.player, card) then return end

	local basicnum = 0
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	for _, acard in ipairs(cards) do
		if acard:getTypeId() == sgs.Card_TypeBasic and not acard:isKindOf("Peach") then basicnum = basicnum + 1 end
	end
	local no_distance = sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_DistanceLimit, self.player, card) > 50
						or self.player:hasFlag("slashNoDistanceLimit")
						or card:getSkillName() == "qiaoshui"
	self.slash_targets = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, self.player, card)
	if use.isDummy and use.extra_target then self.slash_targets = self.slash_targets + use.extra_target end
	if self.player:hasSkill("duanbing") then self.slash_targets = self.slash_targets + 1 end
	if self.player:hasSkill("luajianji") and use.extra_target then self.room:writeToConsole("sp youmu test F" .. self.slash_targets) end
	local rangefix = 0
	if card:isVirtualCard() then
		if self.player:getWeapon() and card:getSubcards():contains(self.player:getWeapon():getEffectiveId()) then
			if self.player:getWeapon():getClassName() ~= "Weapon" then
				local a = sgs.weapon_range[self.player:getWeapon():getClassName()]
				if not a then a = 3 end  
				rangefix = a - self.player:getAttackRange(false)	--yun
			end
		end
		if self.player:getOffensiveHorse() and card:getSubcards():contains(self.player:getOffensiveHorse():getEffectiveId()) then
			rangefix = rangefix + 1
		end
	end
	
	if use.isDummy and use.distance then 
		rangefix = rangefix - use.distance
	end 
	
	
	local function canAppendTarget(target)
		if use.to:contains(target) then return false end
		if use.isDummy and use.extra_target then
			local targets = sgs.PlayerList()
			return card:targetFilter(targets, target, self.player)
		elseif use.isDummy and use.distance then
			return true
		else
			local targets = sgs.PlayerList()
			for _, to in sgs.qlist(use.to) do
				targets:append(to)
			end
			return card:targetFilter(targets, target, self.player)
		end
	end
	--self.room:writeToConsole("青兰测试")
	if not use.isDummy and self.player:hasSkill("qingnang") and self:isWeak() and self:getOverflow() == 0 then return end
	local nazrin = self.room:findPlayerBySkillName("luatanbao")
	if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
		if card:hasFlag("prelingbai") then return end
	end
	if self.player:hasSkill("luabenwo") and koishiuseSlash(self, card, no_distance, rangefix, basicnum) then return end 

	if self.player:hasSkill("lualueying") then
		local callback = sgs.ai_skill_use["@@lualueying"]
		local slashes_1 = self:getCards("Slash")
		if (#slashes_1 == 2) and (self.player:getMark("drank") == 0) and callback and callback(self) and callback(self) ~= "." then
			return
		end
	end
	if self.player:hasSkill("luaniluan") and self.player:getPhase() == sgs.Player_Play and self.player:getNextAlive():isLord()
		and not card:isKindOf("SkillCard") then 
		local analeptic = self:searchForAnaleptic(use, target, use.card)
		local couan = analeptic and self:shouldUseAnaleptic(target, use.card) and analeptic:getEffectiveId() ~= card:getEffectiveId() 
		if (not couan) and not (card:isKindOf("ThunderSlash") or card:isKindOf("FireSlash")) and not self.player:hasFlag("luaniluana") then 
			return nil 
		end 
	end
	for _, friend in ipairs(self.friends_noself) do
		local slash_prohibit = false
		slash_prohibit = self:slashProhibit(card, friend)
		if self:isPriorFriendOfSlash(friend, card) then
			if not slash_prohibit then
				if (not use.current_targets or not table.contains(use.current_targets, friend:objectName()))
					and (self.player:canSlash(friend, card, not no_distance, rangefix)
						or (use.isDummy and self.predictedRange and (self.player:distanceTo(friend, rangefix) <= self.predictedRange)))
					and self:slashIsEffective(card, friend) then
					use.card = card
					if use.to and canAppendTarget(friend) then
						use.to:append(friend)
					end
					if not use.to or self.slash_targets <= use.to:length() then return end
				end
			end
		end
	end
	local function koishi(target)
		if (self.player:hasSkill("luabenwo") and not target:inMyAttackRange(self.player) and not target:isChained()) and not self:isGoodChainTarget(target, nil, nil, nil, card, 2) then 
			if card and (card:isKindOf("ThunderSlash") or card:isKindOf("FireSlash")) then self.room:writeToConsole("恋恋测试S");return true end 
		end 
		return false
	end
	local function Hifu(target)
		if self.player:getPhase() == sgs.Player_Play and (not self.player:hasFlag("luatanmi2") or self.player:hasUsed("#luatanmi")) then
			if self.player:getTag("luatanmi") and self.player:getTag("luatanmi"):toString() ~= "" then
				return false
			end
			if self:isWeak(target) then return false end
			return true
		end
		return false
	end

	local targets = {}
	local forbidden = {}
	self:sort(self.enemies, "defenseSlash")
	local function teimu(target)
		if self.player:hasSkill("luasuide") and target:getMark("@suide") then
			return true
		end
		return false
	end
	for _, enemy in ipairs(self.enemies) do
		if not self:slashProhibit(card, enemy) and (sgs.isGoodTarget(enemy, self.enemies, self, true) or self.player:hasSkill("luarebin2")) then
			if (self:hasNosQiuyuanEffect(self.player, enemy) or self:hasQiuyuanEffect(self.player, enemy) or teimu(enemy)) and not self.player:hasSkill("luarebin2") then table.insert(forbidden, enemy)
			elseif not self:getDamagedEffects(enemy, self.player, true) and not (koishi(enemy) and not Hifu(enemy)) then table.insert(targets, enemy)
			else table.insert(forbidden, enemy) end
		end
	end
	local Pay_loyalist_adjust
	if self.player:getRole() == "loyalist" and #targets == 0 then
		for _,p in sgs.qlist(self.room:getAlivePlayers()) do
			if not self:isFriend(p) and not self:isEnemy(p) then
				if (self:hasNosQiuyuanEffect(self.player, p) or self:hasQiuyuanEffect(self.player, p) or teimu(p)) and not self.player:hasSkill("luarebin2") then table.insert(forbidden, p)
				elseif not self:getDamagedEffects(p, self.player, true) and not (koishi(p) and not Hifu(p)) then
					table.insert(targets, p)
					Pay_loyalist_adjust = true
				else table.insert(forbidden, p) end
			end
		end
	end
	if #targets == 0 and #forbidden > 0 then  --AtomDamageCount2(target, source, nature, card)
		if not ((self:getOverflow() <= 0) and self.player:hasSkill("luabenwo") and (self:AtomDamageCount2(forbidden[1], self.player, nil, card) <= 1)) then
			targets = forbidden
		end 
	end

	if self.player:hasSkill("luayinxi") and self:getOverflow() <= 0 and self.player:getHp() > 1 then
		local slashes = self:getCards("Slash")
		if #slashes == 1 then
			local callback = sgs.ai_skill_playerchosen.luayinxi
			if callback(self, targets) then return end
		end
	end
	if self.player:hasSkill("luazhuni") and self.player:getMark("luazhuni") > 0 and self:Mio() then
		local slashes = self:getCards("Slash")
		if #slashes == 1 then return end
	end
	if self.player:hasSkill("luabenwo") and card and not card:isKindOf("NatureSlash") then --pay 古明地恋用
		self:sort(targets, "defenseSlash")
		local new_targets = {}
		for _, enemy in ipairs(targets) do
			if self:isGoodChainTarget(enemy, nil, nil, nil, card) and self:YouMu2(enemy, true) then 
				table.insert(new_targets, enemy)
			end 
		end 
		for _, enemy in ipairs(targets) do
			if self:YouMu2(enemy, true) then
				local bool_X = false
				for _, enemy2 in ipairs(new_targets) do
					if enemy2:objectName() == enemy:objectName() then bool_X = true end
				end 
				if not bool_X then table.insert(new_targets, enemy) end 
			end 
		end 
		for _, enemy in ipairs(targets) do
			local bool_X = false
			for _, enemy2 in ipairs(new_targets) do
				if enemy2:objectName() == enemy:objectName() then bool_X = true end
			end 
			if not bool_X then table.insert(new_targets, enemy) end 		
		end 
		targets = new_targets
	end 
	if #targets == 1 and card:getSkillName() == "lihuo" and not targets[1]:hasArmorEffect("vine") then return end
	
	for _, target in ipairs(targets) do
		if self.player:hasSkill("chixin") then
			local chixin_list = self.player:property("chixin"):toString():split("+")			
			if table.contains(chixin_list, target:objectName()) then continue end
		end
		local canliuli = false
		local use_wuqian = self.player:hasSkill("wuqian") and self.player:getMark("@wrath") >= 2
							and not target:isLocked(sgs.Sanguosha:cloneCard("jink"))
							and (not self.player:hasSkill("wushuang")
								or target:getArmor() and target:hasArmorEffect(target:getArmor():objectName()) and not self.player:hasWeapon("qinggang_sword"))
							and (self:hasHeavySlashDamage(self.player, card, target)
								or (getCardsNum("Jink", target, self.player) < 2 and getCardsNum("Jink", target, self.player) >= 1 and target:getHp() <= 2))
		for _, friend in ipairs(self.friends_noself) do
			if self:canLiuli(target, friend) and self:slashIsEffective(card, friend) and #targets > 1 and friend:getHp() < 3 then canliuli = true end
		end

		if (not use.current_targets or not table.contains(use.current_targets, target:objectName()))
			and (self.player:canSlash(target, card, not no_distance, rangefix)
				or (use.isDummy and self.predictedRange and self.player:distanceTo(target, rangefix) <= self.predictedRange))
			and (self:objectiveLevel(target) > 3 or (self.player:getRole() == "loyalist" and Pay_loyalist_adjust))
			and not (self.player:hasSkill("luafenxing") and (self.player:getHandcardNum() <= 2) and (target:getHp() > 1))
			and (self:slashIsEffective(card, target, self.player, use_wuqian) or self.player:hasSkill("luarebin2"))
			and not ((target:hasSkill("xiangle") or self.player:hasSkill("luacuiruo")) and basicnum < 2 and not self.player:hasSkill("luarebin2")) and not canliuli
			and not (not self:isWeak(target) and #self.enemies > 1 and #self.friends > 1 and self.player:hasSkill("keji")
				and self:getOverflow() > 0 and not self:hasCrossbowEffect())
			and not (self.player:hasSkills("Luayuelong|luayueni") and card and card:isBlack() and (not ((self:YouMu2(target, true) and self:isWeak(target)) or (self:getOverflow() > 0)))
				and card:getSkillName() ~= "Luayuelong" and card:getSkillName() ~= "luayueni" and not use.shoutu) then

			if target:getHp() > 1 and target:hasSkill("jianxiong") and self.player:hasWeapon("spear") and card:getSkillName() == "spear" then
				local ids, isGood = card:getSubcards(), true
				for _, id in sgs.qlist(ids) do
					local c = sgs.Sanguosha:getCard(id)
					if isCard("Peach", c, target) or isCard("Analeptic", c, target) then isGood = false break end
				end
				if not isGood then continue end
			end

			-- fill the card use struct
			local usecard = card
			if not use.to or use.to:isEmpty() then
				if self.player:hasWeapon("spear") and card:getSkillName() == "spear" then
				elseif self.player:hasWeapon("crossbow") and self:getCardsNum("Slash") > 1 then
				elseif (not use.isDummy) then
					local cardF = self:findWeaponToUse(target)
					if cardF and not (self.player:getTag("luatanmi") and self.player:getTag("luatanmi"):toString() ~= "") then
						use.card = cardF
						return
					end
				end
				if (target:isChained() or (self.player:hasSkill("luabenwo") and not target:inMyAttackRange(self.player) and not target:isChained())) and self:isGoodChainTarget(target, nil, nil, nil, card) and not use.card then
					
					if self:hasCrossbowEffect() and card:isKindOf("NatureSlash") then
						local slashes = self:getCards("Slash")
						for _, slash in ipairs(slashes) do
							if not slash:isKindOf("NatureSlash") and self:slashIsEffective(slash, target)
								and not self:slashProhibit(slash, target) and not self.player:hasSkill("jueqing") then	--yun
								usecard = slash
								break
							end
						end
					-- elseif (card:isKindOf("ThunderSlash") or card:isKindOf("FireSlash")) and self:YouMu2(target, true) then
						-- local slash = self:getCard("NatureSlash")
						-- if slash and self:slashIsEffective(slash, target) and not self:slashProhibit(slash, target) then usecard = slash end
					end
				end
				
				local godsalvation = self:getCard("GodSalvation")
				if not use.isDummy and godsalvation and godsalvation:getId() ~= card:getId() and self:willUseGodSalvation(godsalvation) and
					(not target:isWounded() or not self:hasTrickEffective(godsalvation, target, self.player)) and self.player:getPhase() == sgs.Player_Play
					and ((not self.player:hasFlag("jianji")) or (self.player:getPhase() ~= sgs.Player_Play) or (self.player:getMark("jianji") > 1))
						and not (self.player:getTag("luatanmi") and self.player:getTag("luatanmi"):toString() ~= "") then
					use.card = godsalvation
					return
				end
			end
			use.card = use.card or usecard
			if use.to and not use.to:contains(target) and canAppendTarget(target) then

				use.to:append(target)
			end
			if not use.isDummy then
				local ofuda = self:searchForOfuda(use, target, use.card)
				if ofuda and self:canBeOfudaTarget(target, ofuda) and ofuda:getEffectiveId() ~= card:getEffectiveId()
						and not (self.player:hasSkill("LuaChunguang") and ofuda:getNumber() <= self.player:getLostHp() * 2 and #self.enemies > 1)
						and not (#self.enemies >= 2 and self.player:hasSkill("luaxiongshi") and self.player:getMark("luaxiongshi") >= 1)
						and ((not self.player:hasFlag("jianji")) or (self.player:getPhase() ~= sgs.Player_Play) or (self.player:getMark("jianji") > 1)) then
					use.card = ofuda
					local targets_num = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, self.player, ofuda)

					local Targets = {}
					local Enemies = self.enemies
					self:sort(Enemies, "hp")
					for _, to in ipairs(Enemies) do
						if self:canBeOfudaTarget(to, ofuda) then
							table.insert(Targets, to)
						end
					end

					if use.to then
						use.to = sgs.SPlayerList()
						for _, to in ipairs(Targets) do
							use.to:append(to)
							if use.to:length() == targets_num then return end
						end
						return
					end
				end

				local analeptic = self:searchForAnaleptic(use, target, use.card)
				if analeptic and self:shouldUseAnaleptic(target, use.card) and analeptic:getEffectiveId() ~= card:getEffectiveId()
					and not (self.player:hasSkill("LuaChunguang") and analeptic:getNumber() <= self.player:getLostHp() * 2 and #self.enemies > 1)
					and not (self.player:hasSkill("LuaChunguang") and analeptic:getNumber() <= self.player:getLostHp() * 2 and #self.enemies > 1)
					and not (#self.enemies >= 2 and self.player:hasSkill("luaxiongshi") and self.player:getMark("luaxiongshi") >= 1)
					and ((not self.player:hasFlag("jianji")) or (self.player:getPhase() ~= sgs.Player_Play) or (self.player:getMark("jianji") > 1)) then
					--card:isVirtualCard()
					use.card = analeptic
					if use.to then
						if analeptic:isKindOf("BasicCard") then
							use.to = sgs.SPlayerList()
						else
							use.to = sgs.SPlayerList()
							use.to:append(self.player)
						end
					end
					return
				end
				if self.player:hasSkill("jilve") and self.player:getMark("@bear") > 0 and not self.player:hasFlag("JilveWansha") and target:getHp() == 1 and not self.room:getCurrent():hasSkill("wansha")
					and (target:isKongcheng() or getCardsNum("Jink", target, self.player) < 1 or sgs.card_lack[target:objectName()]["Jink"] == 1) then
					use.card = sgs.Card_Parse("@JilveCard=.")
					sgs.ai_skill_choice.jilve = "wansha"
					if use.to then use.to = sgs.SPlayerList() end
					return
				end
				if self.player:hasSkill("duyi") and self.room:getDrawPile():length() > 0 and not self.player:hasUsed("DuyiCard")
					and (target:getHp() <= 2 or self:hasHeavySlashDamage(self.player, card, target)) and not target:isKongcheng() then	--yun
					sgs.ai_duyi = { id = self.room:getDrawPile():first(), tg = target }
					use.card = sgs.Card_Parse("@DuyiCard=.")
					if use.to then use.to = sgs.SPlayerList() end
					return
				end
				
			end
			if (not use.to) or (self.slash_targets <= use.to:length()) then 
				return 
			end
		end
	end

	for _, friend in ipairs(self.friends_noself) do
		local slash_prohibit = self:slashProhibit(card, friend)
		if (not use.current_targets or not table.contains(use.current_targets, friend:objectName()))
			and not self:hasHeavySlashDamage(self.player, card, friend) and card:getSkillName() ~= "lihuo"
			and (not use.to or not use.to:contains(friend))
			and ((self.player:hasSkill("pojun") and friend:getHp() > 4 and getCardsNum("Jink", friend, self.player) == 0 and friend:getHandcardNum() < 3)
				or (self:getDamagedEffects(friend, self.player) and not (friend:isLord() and #self.enemies < 1))
				or (self:needToLoseHp(friend, self.player, true, true) and not (friend:isLord() and #self.enemies < 1))) then

			if not slash_prohibit then
				if ((self.player:canSlash(friend, card, not no_distance, rangefix))
					or (use.isDummy and self.predictedRange and self.player:distanceTo(friend, rangefix) <= self.predictedRange))
					and self:slashIsEffective(card, friend) then
					use.card = card
					if use.to and canAppendTarget(friend) then
						use.to:append(friend)
					end
					if not use.to or self.slash_targets <= use.to:length() then return end
				end
			end
		end
	end
end

-- askForUseSlashTo看这里
sgs.ai_skill_use.slash = function(self, prompt)
	local parsedPrompt = prompt:split(":")
    self.room:writeToConsole("grani test 0 " .. parsedPrompt[1])
	local callback = sgs.ai_skill_cardask[parsedPrompt[1]] -- for askForUseSlashTo
	if (parsedPrompt[1] == "kxslash" or parsedPrompt[1] == "LuaGuishen" or parsedPrompt[1] == "luaqijinr") and callback(self, nil, nil, nil, nil, prompt) == "." then return "." end
	if parsedPrompt[1] == "luawangshiz" or parsedPrompt[1] == "luahuixuany" then 
		local target = self.room:getTag(parsedPrompt[1]):toPlayer()
		if callback(self) ~= "." then return callback(self) .. "->" .. target:objectName() else return "." end 
	end 
	if parsedPrompt[1] == "@luatongling" then
		local seiga = self.room:getTag("tonglingTarge")
		seiga = seiga:toPlayer()
		if self:isFriend(seiga) and self.player:getPhase() == sgs.Player_Play then return "." end 
		if self:isFriend(seiga) and (seiga:getMark("@tongling") == #self.enemies - 1) then return "." end 
	end
	if self.player:hasFlag("slashTargetFixToOne") and type(callback) == "function" then
		self.room:writeToConsole(parsedPrompt[1])
		local slash
		local target
		for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
			if player:hasFlag("SlashAssignee") then target = player break end
		end

		local target2 = nil
		if #parsedPrompt >= 3 then target2 = findPlayerByObjectName(self.room, parsedPrompt[3]) end
		if not target then return "." end
		local ret = callback(self, nil, nil, target, target2, prompt)
		if ret == nil or ret == "." then return "." end
		slash = sgs.Card_Parse(ret)
		local no_distance = sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_DistanceLimit, self.player, slash) > 50 
							or self.player:hasFlag("slashNoDistanceLimit")
							or slash:getSkillName() == "qiaoshui"	--yun
		local targets = {}
		local use = { to = sgs.SPlayerList() }
		if self.player:canSlash(target, slash, not no_distance) then use.to:append(target) else return "." end

		if parsedPrompt[1] ~= "@niluan-slash" and target:hasSkill("xiansi") and target:getPile("counter"):length() > 1
			and not (self:needKongcheng() and self.player:isLastHandCard(slash, true)) then
			return "@XiansiSlashCard=.->" .. target:objectName()
		end

		self:useCardSlash(slash, use)
		for _, p in sgs.qlist(use.to) do table.insert(targets, p:objectName()) end
		if table.contains(targets, target:objectName()) then return ret .. "->" .. table.concat(targets, "+") end
		return "."
	end

	local useslash, target
	local slashes = self:getCards("Slash")
	self:sort(self.enemies, "defenseSlash")
	for _, slash in ipairs(slashes) do
		local no_distance = sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_DistanceLimit, self.player, slash) > 50 
							or self.player:hasFlag("slashNoDistanceLimit")
							or slash:getSkillName() == "qiaoshui"	--yun
		for _, friend in ipairs(self.friends_noself) do
			local slash_prohibit = false
			slash_prohibit = self:slashProhibit(card, friend)
			if not self:hasHeavySlashDamage(self.player, card, friend)
				and self.player:canSlash(friend, slash, not no_distance) and not self:slashProhibit(slash, friend)
				and self:slashIsEffective(slash, friend)
				and ((self:findLeijiTarget(friend, 50, source, -1) or (self:findLeijiTarget(friend, 50, source, 1) and friend:isWounded()))
					or (friend:isLord() and self.player:hasSkill("guagu") and friend:getLostHp() >= 1 and getCardsNum("Jink", friend, self.player) == 0)
					or (friend:hasSkill("jieming") and self.player:hasSkill("nosrende") and (huatuo and self:isFriend(huatuo))))
				and not (self.player:hasFlag("slashTargetFix") and not friend:hasFlag("SlashAssignee"))
				and not (slash:isKindOf("XiansiSlashCard") and friend:getPile("counter"):length() < 2) then
				useslash = slash
				target = friend
				break
			end
		end
	end
	if not useslash then
		for _, slash in ipairs(slashes) do
			local no_distance = sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_DistanceLimit, self.player, slash) > 50 
								or self.player:hasFlag("slashNoDistanceLimit")
								or slash:getSkillName() == "qiaoshui"	--yun
			local function to_slash(enemy, slashQ, no_distanceW, promt)
				return self.player:canSlash(enemy, slashQ, not no_distanceW) and not self:slashProhibit(slashQ, enemy)
						and (self:slashIsEffective(slashQ, enemy) or promt == "LuaduzhuaA" or self.player:hasSkill("luarebin2")) and (sgs.isGoodTarget(enemy, self.enemies, self) or promt == "LuaduzhuaA" or self.player:hasSkill("luarebin2"))
						and not (self.player:hasFlag("slashTargetFix") and not enemy:hasFlag("SlashAssignee"))
			end

			if not useslash then
				for _, enemy in ipairs(self.enemies) do
					if to_slash(enemy, slash, no_distance, parsedPrompt[1]) then
						useslash = slash
						target = enemy
						break
					end
				end
				if parsedPrompt[1] == "@luatongling" and not useslash then --痛击我的队友	
					local seiga = self.room:getTag("tonglingTarge"):toPlayer()
					if self:isFriend(seiga) then return "." end
					local players = sgs.QList2Table(self.room:getAllPlayers())
					self:sort(players, "defenseSlash2")
					for _, player in ipairs(players) do
						if to_slash(player, slash, no_distance) then
							useslash = slash
							target = player
							break
						end
					end
				end
			end 
		end
	end
	if useslash and target then
		local targets = {}
		local use = { to = sgs.SPlayerList() }
		use.to:append(target)

		if target:hasSkill("xiansi") and target:getPile("counter"):length() > 1 and not (self:needKongcheng() and self.player:isLastHandCard(slash, true)) then
			return "@XiansiSlashCard=.->" .. target:objectName()
		end

		self:useCardSlash(useslash, use)
		for _, p in sgs.qlist(use.to) do table.insert(targets, p:objectName()) end
		if table.contains(targets, target:objectName()) then return useslash:toString() .. "->" .. table.concat(targets, "+") end
	end
	return "."
end

sgs.ai_skill_playerchosen.dangzuosha2 = function(self, targets, slashX)
	local slash = self.room:getTag("dangzuoshaTC"):toCard()
	if type(targets) ~= "table" then targets = sgs.QList2Table(targets) end
	self:sort(targets, "defenseSlash")
	for _, target in ipairs(targets) do
		if self:isEnemy(target) and not self:slashProhibit(slash, target) and sgs.isGoodTarget(target, targets, self) and self:slashIsEffective(slash, target) then
			return target
		end
	end
	return nil
end
sgs.ai_skill_playerchosen.slash_extra_targets = function(self, targets, slashX) -- 杀 额外
	local slash = sgs.Sanguosha:cloneCard("slash")
	if slashX then slash = slashX end
	if type(targets) ~= "table" then targets = sgs.QList2Table(targets) end
	self:sort(targets, "defenseSlash")
	for _, target in ipairs(targets) do
		if self:isEnemy(target) and not self:slashProhibit(slash, target) and sgs.isGoodTarget(target, targets, self) and self:slashIsEffective(slash, target) then
			return target
		end
	end
	return nil
end

sgs.ai_skill_playerchosen.zero_card_as_slash = function(self, targets)
	local slash = sgs.Sanguosha:cloneCard("slash")
	local targetlist = sgs.QList2Table(targets)
	local arrBestHp, canAvoidSlash, forbidden = {}, {}, {}
	self:sort(targetlist, "defenseSlash")

	for _, target in ipairs(targetlist) do
		if self:isEnemy(target) and not self:slashProhibit(slash ,target) and sgs.isGoodTarget(target, targetlist, self) then
			if self:slashIsEffective(slash, target) then
				if self:getDamagedEffects(target, self.player, true) or self:needLeiji(target, self.player) then
					table.insert(forbidden, target)
				elseif self:needToLoseHp(target, self.player, true, true) then
					table.insert(arrBestHp, target)
				else
					return target
				end
			else
				table.insert(canAvoidSlash, target)
			end
		end
	end
	for i=#targetlist, 1, -1 do
		local target = targetlist[i]
		if not self:slashProhibit(slash, target) then
			if self:slashIsEffective(slash, target) then
				if self:isFriend(target) and (self:needToLoseHp(target, self.player, true, true)
					or self:getDamagedEffects(target, self.player, true) or self:needLeiji(target, self.player)) then
						return target
				end
			else
				table.insert(canAvoidSlash, target)
			end
		end
	end

	if #canAvoidSlash > 0 then return canAvoidSlash[1] end
	if #arrBestHp > 0 then return arrBestHp[1] end

	self:sort(targetlist, "defenseSlash")
	targetlist = sgs.reverse(targetlist)
	for _, target in ipairs(targetlist) do
		if target:objectName() ~= self.player:objectName() and not self:isFriend(target) and not table.contains(forbidden, target) then
			return target
		end
	end

	return targetlist[1]
end

sgs.ai_card_intention.Slash = function(self, card, from, tos)  --杀仇恨值
	if sgs.ai_liuli_effect then
		sgs.ai_liuli_effect = false
		if sgs.ai_liuli_user then
			sgs.updateIntention(from, sgs.ai_liuli_user, 10)
			sgs.ai_liuli_user = nil
		end
		return
	end
	
	if sgs.ai_collateral then sgs.ai_collateral = false return end
	if card:hasFlag("lualiuzhi") then return end
	if card:hasFlag("nosjiefan-slash") then return end
	--self.room:writeToConsole("魔理沙测试Y")
	self.room:writeToConsole(from:objectName())
	if from:hasFlag("luaaoshu") then 
		self.room:writeToConsole("marisa test X")
		return 
	end
	if card:hasFlag("luamengyan") then 
		local target = self.room:getTag("luamengyanacd"):toPlayer()
		if target then sgs.updateIntention(from, target, 20) end 
	end 
	
	if card:getSkillName() == "luatongling" then return end
	if card:getSkillName() == "luamoulue" then
		local kitcho = self.room:findPlayerBySkillName("luamoulue")
		if kitcho and kitcho:isAlive() then
			sgs.updateIntention(kitcho, tos[1], 80)
		end
		return
	end
	if card:getSkillName() == "luamoucheng" then
		local yukari = self.room:findPlayerBySkillName("luamoucheng")
		if yukari and yukari:isAlive() then
			sgs.updateIntention(yukari, tos[1], 80)
		end
		return
	end

	if from:hasFlag("luatongling") then return end
	if card:getSkillName() == "luaniluan" then return end
	if card:getSkillName() == "mizhao" then return end
	if string.find(card:getSkillName(), "yonglve") then return end	--yun
	for _, to in ipairs(tos) do
		if to:hasFlag("jili") then return end	--yun
		if not self:slashIsEffective(card, to, from) then return end	--yun
		if to:hasSkill("luabainian") then return end
		local value = 80
		speakTrigger(card, from, to)
		if to:hasSkills("yiji|nosyiji|nosqiuyuan|qiuyuan") then value = 0 end	--yun
		if to:hasSkills("nosleiji|leiji|olleiji") and (getCardsNum("Jink", to, from) > 0 or to:hasArmorEffect("eight_diagram")) and not self:hasHeavySlashDamage(from, card, to)
				--and (hasExplicitRebel(self.room) or sgs.explicit_renegade) --yun
				and not self:canLiegong(to, from) then
			local enemies = self:getEnemies(to)	--yun
			if #enemies < 1 then value = 80
			else
				value = 0
			end
		end
		if not self:hasHeavySlashDamage(from, card, to) and (self:getDamagedEffects(to, from, true) or self:needToLoseHp(to, from, true, true)) then value = 0 end
		if from:hasSkill("pojun") and to:getHp() > (2 + self:hasHeavySlashDamage(from, card, to, true)) then value = 0 end
		if self:needLeiji(to, from) then
			local enemies = self:getEnemies(to)	--yun
			if #enemies < 1 then value = 80
			else value = from:getState() == "online" and 0 or 0	--yun
			end
		end
		if to:hasSkill("fangzhu") and to:isLord() and sgs.turncount < 2 then value = 10 end
		if self:needChuanxin(to, from) then value = 0 end	--yun
		if self:getTWBaobian(to, from) > 0 then value = 0 end	--yun
		sgs.updateIntention(from, to, value)
	end
end

sgs.ai_skill_cardask["slash-jink"] = function(self, data, pattern, target)
	local isdummy = type(data) == "number"
	local slash
	if type(data) == "userdata" then
		local effect = data:toSlashEffect()
		slash = effect.slash
	else
		if data and type(data) == "number" and data > 0 then
			slash = sgs.Sanguosha:getCard(data)
		else
			slash = sgs.Sanguosha:cloneCard("slash")
		end
	end

	local function getJink()
		if target and target:hasSkill("dahe") and self.player:hasFlag("dahe") then
			for _, card in ipairs(self:getCards("Jink")) do
				if card:getSuit() == sgs.Card_Heart then return card:getId() end
			end
			return "."
		end
		return self:getCardId("Jink", nil, nil, nil, slash, target) or not isdummy and "."
	end

	local cards = sgs.QList2Table(self.player:getHandcards())
	if (not target or self:isFriend(target)) and slash:hasFlag("nosjiefan-slash") then return "." end
	if not target then return getJink() end

	if sgs.ai_skill_cardask.nullfilter(self, data, pattern, target) then return "." end


	if self.player:hasSkill("LuaWeishi") then
		if (not target) or (not target:hasSkills("luajiejie|luayanxun|LuaFengyin")) then
			local id = getJink()
			if type(id) == "number" then self.room:writeToConsole("junko test") end
			if id and type(id) == "number" and id >= 0 then
				local card = sgs.Sanguosha:getCard(id)
				if card:getNumber() <= 2 then return "." end
				if card:getNumber() >= 5 and self.player:isWounded() and card:getNumber() <= self.player:getHp() * 2 then return "." end
			end
		end
	end

	if self.player:hasSkill("luamaobu") and self:getOverflow() <= 0 and not target:isChained() then return "." end
	if not self:hasHeavySlashDamage(target, slash, self.player) and self:getDamagedEffects(self.player, target, slash) then return "." end
	if slash:isKindOf("NatureSlash") and self.player:isChained() and self:isGoodChainTarget(self.player, target, nil, nil, slash) then return "." end
	if self.player:getMark("@luatianyan") == 1 and self.player:getHp() > 1 and self.player:hasSkill("luatianyan") then return "." end
	if self:isFriend(target) then
		if self:findLeijiTarget(self.player, 50, target) then return getJink() end
		if target:hasSkill("jieyin") and not self.player:isWounded() and self.player:isMale() and not self.player:hasSkills("leiji|nosleiji|olleiji") then return "." end
		if not target:hasSkill("jueqing") then
			if (target:hasSkill("nosrende") or (target:hasSkill("rende") and not target:hasUsed("RendeCard"))) and self.player:hasSkill("jieming") then return "." end
			if target:hasSkill("pojun") and (not self.player:faceUp() or self.player:getHp() > 3) then return "." end	--yun
			if self:needChuanxin(self.player, target) then return "." end	--yun
			if self:getTWBaobian(self.player, target) > 0 then return "." end	--yun
		end
	else
		if self:hasHeavySlashDamage(target, slash) then return getJink() end

		local current = self.room:getCurrent()
		if current and current:hasSkills("juece|nosjuece") and self.player:getHp() > 0 then	--yun
			local use = false
			for _, card in ipairs(self:getCards("Jink")) do
				if not self.player:isLastHandCard(card, true) then
					use = true
					break
				end
			end
			if not use then return not isdummy and "." end
		end
		if self.player:getHandcardNum() == 1 and self:needKongcheng() then return getJink() end
		if not self:hasLoseHandcardEffective() and not self.player:isKongcheng() then return getJink() end
		if target:hasSkill("mengjin") and not (target:hasSkill("nosqianxi") and target:distanceTo(self.player) == 1) then
			if self:doNotDiscard(self.player, "he", true) then return getJink() end
			if self.player:getCards("he"):length() == 1 and not self.player:getArmor() then return getJink() end
			if self.player:hasSkills("jijiu|qingnang") and self.player:getCards("he"):length() > 1 then return "." end
			if self:canUseJieyuanDecrease(target) then return "." end
			if (self:getCardsNum("Peach") > 0 or (self:getCardsNum("Analeptic") > 0 and self:isWeak()))
					and not self.player:hasSkills("tuntian+zaoxian") and not self:willSkipPlayPhase() then
				return "."
			end
		end
		if self.player:getHp() > 1 and getKnownCard(target, self.player, "Slash") >= 1 and getKnownCard(target, self.player, "Analeptic") >= 1 and self:getCardsNum("Jink") == 1
				and (target:getPhase() < sgs.Player_Play or self:slashIsAvailable(target) and target:canSlash(self.player)) then
			return "."
		end
		if not (target:hasSkill("nosqianxi") and target:distanceTo(self.player) == 1) then
			if target:hasWeapon("axe") then
				if target:hasSkills(sgs.lose_equip_skill) and target:getEquips():length() > 1 and target:getCards("he"):length() > 2 then return not isdummy and "." end
				if target:getHandcardNum() - target:getHp() > 2 and not self:isWeak() and not self:getOverflow() then return not isdummy and "." end
			elseif target:hasWeapon("blade") then
				if slash:isKindOf("NatureSlash") and self.player:hasArmorEffect("vine")
						or self.player:hasArmorEffect("renwang_shield")
						or self:hasEightDiagramEffect()
						or self:hasHeavySlashDamage(target, slash)
						or (self.player:getHp() == 1 and #self.friends_noself == 0) then
				elseif (self:getCardsNum("Jink") <= getCardsNum("Slash", target, self.player) or self.player:hasSkill("qingnang")) and self.player:getHp() > 1
						or self.player:hasSkill("jijiu") and getKnownCard(self.player, self.player, "red") > 0
						or self:canUseJieyuanDecrease(target)
				then
					return not isdummy and "."
				end
			end
		end
	end
	return getJink()
end

sgs.dynamic_value.damage_card.Slash = true

sgs.ai_use_value.Slash = 4.5
sgs.ai_keep_value.Slash = 3.6
sgs.ai_use_priority.Slash = 2.6

function SmartAI:canHit(to, from, conservative)	--yun	出闪时用
	from = from or self.room:getCurrent()
	to = to or self.player
	local jink = sgs.Sanguosha:cloneCard("jink")
	if to:isCardLimited(jink, sgs.Card_MethodUse) then return true end
	if self:canLiegong(to, from) then return true end
	if getCardsNum("Jink", to, from) == 0 then return true end
	if not self:isFriend(to, from) then
		if from:hasWeapon("axe") and from:getCards("he"):length() > 2 then return true end
		if from:hasWeapon("blade") and getCardsNum("Jink", to, from) <= getCardsNum("Slash", from, from) then return true end
		if from:hasSkill("mengjin")
			and not (from:hasSkill("nosqianxi") and not from:hasSkill("jueqing") and from:distanceTo(to) == 1)  
			and not self:hasHeavySlashDamage(from, nil, to) and not self:needLeiji(to, from) then
				if self:doNotDiscard(to, "he", true) then
				elseif to:getCards("he"):length() == 1 and not to:getArmor() then
				elseif self:canUseJieyuanDecrease(from, to) then return false
				elseif self:willSkipPlayPhase() then
				elseif (getCardsNum("Peach", to, from) > 0 or getCardsNum("Analeptic", to, from) > 0) then return true
				elseif not self:isWeak(to) and to:getArmor() and not self:needToThrowArmor(to) then return true
				elseif not self:isWeak(to) and to:getDefensiveHorse() then return true
				end
		end
		if from:hasSkill("jianchu") and to:getCards("he"):length() > 0 then return true end
	end

	local hasHeart, hasRed, hasBlack
	for _, card in ipairs(self:getCards("Jink"), to) do
		if card:getSuit() == sgs.Card_Heart then hasHeart = true end
		if card:isRed() then hasRed = true end
		if card:isBlack() then hasBlack = true end
	end
	if to:hasFlag("dahe") and not hasHeart then return true end
	if to:getMark("@qianxi_red") > 0 and not hasBlack then return true end
	if to:getMark("@qianxi_black") > 0 and not hasRed then return true end
	if not conservative and self:hasHeavySlashDamage(from, nil, to) then conservative = true end
	if not conservative and from:hasSkills("moukui|olpojun") then conservative = true end
	if not conservative and self:hasEightDiagramEffect(to) and not IgnoreArmor(from, to) then return false end
	local need_double_jink = from and (from:hasSkill("wushuang")
			or (from:hasSkill("roulin") and to:isFemale()) or (from:isFemale() and to:hasSkill("roulin")))
	
	local jiangqin = global_room:findPlayerBySkillName("niaoxiang")
	if jiangqin and jiangqin:isAdjacentTo(to) and from:isAdjacentTo(to) and self and self:isFriend(jiangqin, from) then	 
		need_double_jink = true
	end
	
	if to:objectName() == self.player:objectName() then
		if getCardsNum("Jink", to, from) == 0 then return true end
		if need_double_jink and getCardsNum("Jink", to, from) < 2 then return true end
	end
	
	if need_double_jink and getCardsNum("Jink", to, from) < 2 then return true end
	return false
end

function SmartAI:useCardPeach(card, use)
	local mustusepeach = false
	if not self.player:isWounded() then return end
	if self.player:hasSkill("yongsi") and self:getCardsNum("Peach") > self:getOverflow(nil, true) then
		use.card = card
		return
	end 
 
	local canUse = false
	if self.player:getMark("@luajulian1") > 0 then
		for _, friend in ipairs(self.friends_noself) do
			if not self.room:isProhibited(self.player, friend, card) and friend:isWounded() then canUse = true end
		end
		if not canUse then return end
	end
	if self.player:getMark("luajinlun") > 0 then
		use.card = card
		return
	end
	if self.player:hasSkill("yqinsheng") and self:getOverflow() > 0 then	--yun
		use.card = card
		return
	end 

	if self:needKongcheng() then	--pay
		local no_eat
		for _, friend in ipairs(self.friends_noself) do
			if self:isWeak(friend) then
				no_eat = true
				break
			end
		end
		if not no_eat then
			mustusepeach = true
		end
	end 
	
	local result = 0  --1（奇数）表示吃，2（偶数）表示不吃，优先级高听谁的
    for _, askill in sgs.qlist(self.player:getVisibleSkillList(true)) do
        local callback = sgs.pay_ai_card.Peach[askill:objectName()]
		if callback then 
			local AAA = callback(self, card, use, mustusepeach)
			if type(callback) == "function" and AAA and AAA > 0 and AAA > result then 
				result = AAA
			end
		end 
    end
	if result % 2 == 0 and result > 0 then 
		return
	else
		use.card = card
		return
	end 	 
	 
	if self.player:hasSkill("luashijie|luaqiji|luafengmo") and not mustusepeach then
		if self.player:getPhase() == sgs.Player_Play and (self:getOverflow() <= 0) then return end
	end
	if self.player:hasSkill("luafengshou") and not mustusepeach then
		local rh = self:getOverflow()
		local count = 0
		local cards = sgs.QList2Table(self.player:getHandcards())
		for _, cardH in ipairs(cards) do
			if cardH:isKindOf("BasicCard") and cardH:isRed() then count = count + 1 end
		end
		if count >= rh then return end
	end  

	if self.player:hasSkills("shushen|qice|jiyu") and self.player:isWounded() then	--yun
		use.card = card
		return
	end
	if self.player:getMark("Luaxiuyue1") == 1 and self.player:getHp() == 4 then
		return
	end
	if self.player:hasSkill("zhanjue") and self.player:isWounded() and self.player:getMark("zhanjuedraw") < 2 and self.player:getHandcardNum() > 1 then	--yun
		use.card = card
		return
	end
	
	if self.player:hasSkill("longhun") and not self.player:isLord() and
			math.min(self.player:getMaxCards(), self.player:getHandcardNum()) + self.player:getCards("e"):length() > 3 then return end

	local peaches = 0
	local cards = self.player:getHandcards()
	local lord= getLord(self.player)

	cards = sgs.QList2Table(cards)
	for _,cardH in ipairs(cards) do
		if isCard("Peach", cardH, self.player) then peaches = peaches + 1 end
	end
 

	if self.player:isLord() and (self.player:hasSkill("hunzi") and self.player:getMark("hunzi") == 0 or (self.player:hasSkill("yhunzi") and self.player:getMark("yhunzi") == 0))
			and self.player:getHp() < 4 and self.player:getMaxCards() > peaches then return end	--yun

	local sbliubei = self.room:findPlayerBySkillName("shichou")	--yun
	if sbliubei and sbliubei:hasLordSkill("shichou") and sbliubei:getMark("xhate") == 1 then
		if self.player:getMark("hate_" .. sbliubei:objectName()) > 0 and self.player:getMark("@hate_to") > 0 and self:isEnemy(sbliubei, self.player)
				and self.player:getMaxCards() >= peaches then return end
	end

	if (self.player:hasSkill("nosrende") or (self.player:hasSkill("rende") and not self.player:hasUsed("RendeCard"))) and self:findFriendsByType(sgs.Friend_Draw) then return end

	if self.player:hasArmorEffect("silver_lion") or (self.player:getMark("@qiangweiw") > 0 and self.player:hasSkill("luaqiangwei")) then
		for _, cardH in sgs.qlist(self.player:getHandcards()) do
			if cardH:isKindOf("Armor") and self:evaluateArmor(cardH) > 0 then
				use.card = cardH
				return
			end
		end
	end

	local SilverLion, OtherArmor
	for _, cardH in sgs.qlist(self.player:getHandcards()) do
		if cardH:isKindOf("SilverLion") then
			SilverLion = cardH
		elseif cardH:isKindOf("Armor") and not cardH:isKindOf("SilverLion") and self:evaluateArmor(cardH) > 0 then
			OtherArmor = true
		end
	end
	if SilverLion and OtherArmor then
		use.card = SilverLion
		return
	end

	for _, enemy in ipairs(self.enemies) do
		if self.player:getHandcardNum() < 3 and
				(self:hasSkills(sgs.drawpeach_skill,enemy) or getCardsNum("Dismantlement", enemy) >= 1
						or enemy:hasSkill("jixi") and enemy:getPile("field"):length() >0 and enemy:distanceTo(self.player) == 1
						or enemy:hasSkill("qixi") and getKnownCard(enemy, self.player, "black", nil, "he") >= 1
						or getCardsNum("Snatch", enemy) >= 1 and enemy:distanceTo(self.player) == 1
						or (enemy:hasSkill("tiaoxin") and (self.player:inMyAttackRange(enemy) and self:getCardsNum("Slash") < 1 or not self.player:canSlash(enemy))))
		then
			mustusepeach = true
			break
		end
	end

	local jinxuandi = self.room:findPlayerBySkillName("wuling")
	if jinxuandi and jinxuandi:getMark("@water") > 0 and self.player:getLostHp() >= 2 then
		mustusepeach = true
	end

	if self.player:getHp() == 1 and not (lord and self:isFriend(lord) and lord:getHp() < 2 and self:isWeak(lord)) then
		mustusepeach = true
	end

	if self.player:hasSkill("yzuojun") or self.player:hasSkill("yongsi") or self.player:hasSkill("yquanshi") or self.player:hasSkill("shude")  --yun
			or (self.player:hasSkill("ynongquan") and self.player:getLostHp() > 1)
			or (card and card:getSkillName() == "ynongquan")
			or (card and self.player:getPile("wooden_ox"):contains(card:getEffectiveId()) and not self:needToLoseHp(self.player, nil, false, false, true))
	then
		use.card = card
		return
	end

	if self.player:hasSkill("taoxi") and self.player:hasFlag("TaoxiRecord") and self.player:isWounded() then	--yun
		local taoxi_id = self.player:getTag("TaoxiId"):toInt()
		if taoxi_id and taoxi_id == card:getEffectiveId() then
			use.card = card
			return
		end
	end

	if mustusepeach or (self.player:hasSkill("nosbuqu") and self.player:getHp() < 1 and self.player:getMaxCards() == 0) or peaches > self.player:getMaxCards() then
		use.card = card
		return
	end

	if self:getOverflow() <= 0 and #self.friends_noself > 0 and not self:hasSkill("luasanaey") then
		return
	end

	if self:getOverflow() > 0 and self:getCardsNum("Peach") > 1 then	--yun
		if self:getCardsNum("Peach") + self:getCardsNum("Jink") > self.player:getMaxCards() then use.card = card end
	end

	if self.player:hasSkill("luayuekuang") and self:getOverflow() <= 0 then --pay 克劳恩皮丝
		return
	end

	if self.player:hasSkills("kuanggu|kofkuanggu") and not self.player:hasSkill("jueqing") and self.player:getLostHp() == 1 and self.player:getOffensiveHorse() then
		return
	end

	if self:needToLoseHp(self.player, nil, nil, nil, true) then return end


	if lord and self:isFriend(lord) and lord:getHp() <= 2 and self:isWeak(lord) then
		if self.player:isLord() then use.card = card end
		if self:getCardsNum("Peach") > 1 and self:getCardsNum("Peach") + self:getCardsNum("Jink") > self.player:getMaxCards() then use.card = card end
		return
	end

	self:sort(self.friends, "hp")
	if (self.friends[1]:objectName()==self.player:objectName() or self.player:getHp()<2) and self:getOverflow() > 0 then	--yun
		use.card = card
		return
	end

	if #self.friends > 1 and ((not hasBuquEffect(self.friends[2]) and self.friends[2]:getHp() < 3 and self:getOverflow() < 2)
			or (not hasBuquEffect(self.friends[1]) and self.friends[1]:getHp() < 2 and peaches <= 1 and self:getOverflow() < 3)) then
		return
	end

	if self.player:hasSkill("jieyin") and self:getOverflow() > 0 then
		self:sort(self.friends, "hp")
		for _, friend in ipairs(self.friends) do
			if friend:isWounded() and friend:isMale() then return end
		end
	end

	if self.player:hasSkill("ganlu") and not self.player:hasUsed("GanluCard") then
		local dummy_use = {isDummy = true}
		self:useSkillCard(sgs.Card_Parse("@GanluCard=."),dummy_use)
		if dummy_use.card then return end
	end

	use.card = card
end

sgs.ai_card_intention.Peach = function(self, card, from, tos)
	if from:hasFlag("luaaoshu") then return end
	if card:hasFlag("lualiuzhi") then return end
	for _, to in ipairs(tos) do
		if to:hasSkill("wuhun") then continue end
		if to:hasFlag("jili") then continue end	--yun
		if not sgs.isRolePredictable() and from:objectName() ~= to:objectName()
			and sgs.current_mode_players["renegade"] > 0 and sgs.evaluatePlayerRole(to) == "rebel"
			and (sgs.evaluatePlayerRole(from) == "loyalist" or sgs.evaluatePlayerRole(from) == "renegade") then
			sgs.outputRoleValues(from, 100)
			sgs.role_evaluation[from:objectName()]["renegade"] = sgs.role_evaluation[from:objectName()]["renegade"] + 100
			sgs.outputRoleValues(from, 100)
		end
		sgs.updateIntention(from, to, -120)
	end
end

sgs.ai_use_value.Peach = 6
sgs.ai_keep_value.Peach = 7
sgs.ai_use_priority.Peach = 0.9

sgs.ai_use_value.Jink = 8.9
sgs.ai_keep_value.Jink = 5.2

sgs.dynamic_value.benefit.Peach = true

sgs.ai_keep_value.Weapon = 2.08
sgs.ai_keep_value.Armor = 2.06
sgs.ai_keep_value.Horse = 2.04

sgs.weapon_range.Weapon = 1
sgs.weapon_range.Pundarika = 1
sgs.weapon_range.Roukanken = 3
sgs.weapon_range.Crossbow = 1
sgs.weapon_range.DoubleSword = 2
sgs.weapon_range.QinggangSword = 2
sgs.weapon_range.IceSword = 2
sgs.weapon_range.GudingBlade = 2
sgs.weapon_range.Gohei = 2
sgs.weapon_range.Shourinken = 2
sgs.weapon_range.ErinBow = 5
sgs.weapon_range.Hakkero = 5
sgs.weapon_range.Axe = 3
sgs.weapon_range.Blade = 3
sgs.weapon_range.Spear = 3
sgs.weapon_range.Halberd = 4
sgs.weapon_range.KylinBow = 5

sgs.ai_skill_invoke.double_sword = function(self, data)
	return not self:needKongcheng(self.player, true)
end

function sgs.ai_slash_weaponfilter.double_sword(self, to, player)
	return player:distanceTo(to) <= math.max(sgs.weapon_range.DoubleSword, player:getAttackRange()) and player:getGender() ~= to:getGender()
end

function sgs.ai_weapon_value.double_sword(self, enemy, player)
	if enemy and enemy:isMale() ~= player:isMale() then return 4 end
end

function SmartAI:getExpectedJinkNum(use)
	local jink_list = use.from:getTag("Jink_" .. use.card:toString()):toStringList()
	local index, jink_num = 1, 1
	for _, p in sgs.qlist(use.to) do
		if p:objectName() == self.player:objectName() then
			local n = tonumber(jink_list[index])
			if n == 0 then return 0
			elseif n > jink_num then jink_num = n end
		end
		index = index + 1
	end
	return jink_num
end

sgs.ai_skill_cardask["double-sword-card"] = function(self, data, pattern, target)
	if self.player:isKongcheng() then return "." end
	local use = data:toCardUse()
	local jink_num = self:getExpectedJinkNum(use)
	if jink_num > 1 and self:getCardsNum("Jink") == jink_num then return "." end

	if self:needKongcheng(self.player, true) and self.player:getHandcardNum() <= 2 then
		if self.player:getHandcardNum() == 1 then
			local card = self.player:getHandcards():first()
			return (jink_num > 0 and isCard("Jink", card, self.player)) and "." or ("$" .. card:getEffectiveId())
		end
		if self.player:getHandcardNum() == 2 then
			local first = self.player:getHandcards():first()
			local last = self.player:getHandcards():last()
			local jink = isCard("Jink", first, self.player) and first or (isCard("Jink", last, self.player) and last)
			if jink then
				return first:getEffectiveId() == jink:getEffectiveId() and ("$"..last:getEffectiveId()) or ("$"..first:getEffectiveId())
			end
		end
	end
	if target and self:isFriend(target) then return "." end
	if self:needBear() then return "." end
	if target and self:needKongcheng(target, true) then return "." end
	local cards = self.player:getHandcards()
	for _, card in sgs.qlist(cards) do
		if (card:isKindOf("Slash") and self:getCardsNum("Slash") > 1)
			or (card:isKindOf("Jink") and self:getCardsNum("Jink") > 2)
			or card:isKindOf("Disaster")
			or (card:isKindOf("EquipCard") and not self:hasSkills(sgs.lose_equip_skill))
			or (not self.player:hasSkill("nosjizhi") and (card:isKindOf("Collateral") or card:isKindOf("GodSalvation")	--yun
				or card:isKindOf("FireAttack") or card:isKindOf("IronChain") or card:isKindOf("AmazingGrace"))) then
			return "$" .. card:getEffectiveId()
		end
	end
	return "."
end

function sgs.ai_weapon_value.qinggang_sword(self, enemy)
	if enemy and enemy:getArmor() and enemy:hasArmorEffect(enemy:getArmor():objectName()) then return 3 end
end
function sgs.ai_weapon_value.gohei(self, enemy)
	if enemy and enemy:getArmor() and enemy:hasArmorEffect(enemy:getArmor():objectName())
		and enemy:hasArmorEffect("renwang_shield") then return 2.9 end
	return 2
end

function sgs.ai_slash_weaponfilter.qinggang_sword(self, enemy, player)
	if player:distanceTo(enemy) > math.max(sgs.weapon_range.QinggangSword, player:getAttackRange()) then return end
	if enemy:getArmor() and enemy:hasArmorEffect(enemy:getArmor():objectName())
		and (sgs.card_lack[enemy:objectName()] == 1 or getCardsNum("Jink", enemy, self.player) < 1) then
		return true
	end
end

function sgs.ai_slash_weaponfilter.gohei(self, enemy, player)
	if player:distanceTo(enemy) > math.max(sgs.weapon_range.Gohei, player:getAttackRange()) then return end
	if enemy:getArmor() and enemy:hasArmorEffect(enemy:getArmor():objectName()) and enemy:hasArmorEffect("renwang_shield")
			and (sgs.card_lack[enemy:objectName()] == 1 or getCardsNum("Jink", enemy, self.player) < 1) then
		return true
	end
end

sgs.ai_skill_invoke.ice_sword = function(self, data)
	local damage = data:toDamage()
	local target = damage.to
	if self:isFriend(target) then
		if self:getDamagedEffects(target, self.players, true) or self:needToLoseHp(target, self.player, true) then return false
		elseif target:isChained() and self:isGoodChainTarget(target, self.player, nil, nil, damage.card) then return false
		elseif self:isWeak(target) or damage.damage > 1 then return true
		elseif target:getLostHp() < 1 then return false end
		return true
	else
		if self:isWeak(target) then return false end
		if damage.damage > 1 or self:hasHeavySlashDamage(self.player, damage.card, target) then return false end
		local function getEQP(player)
			local p = player:getEquips():length()
			if player:hasArmorEffect("gale_shell") then p = p - 1 end 
			return p 
		end 
		if target:hasSkill("LuaTaiji") and getEQP(target) < 2 then --pay 美铃
			if target:getHp() > target:getHandcards() then return false end 			
			if target:getHp() == target:getHandcards() and target:getHp() == 2 then return false end 
		end 

		if target:hasSkill("lirang") and #self:getFriendsNoself(target) > 0 then return false end
		--if self.player:hasSkill("tieji") or self:canLiegong(target, self.player) then return false end	--yun
		if target:hasSkills("tuntian+zaoxian") and target:getPhase() == sgs.Player_NotActive then return false end
		if self:doNotDiscard(target, "he", false, 2) then return false end	--yun
		if self:needToLoseHp(target, self.player, true) then return true end	--yun
		if target:getArmor() and self:evaluateArmor(target:getArmor(), target) > 3 
			and not (target:hasArmorEffect("silver_lion") and target:isWounded()) then 
			return true
		end
		if target:getCards("he"):length()<4 and target:getCards("he"):length()>1 then return true end
		return false
	end
end

function sgs.ai_slash_weaponfilter.guding_blade(self, to)
	return self:canHitDown(to)	--yun
end

function sgs.ai_weapon_value.guding_blade(self, enemy)
	if not enemy then return end
	local value = 2
	if self:canHitDown(enemy) then value = 4 end	--yun
	return value
end

function SmartAI:needToThrowAll(player)
	player = player or self.player
	if player:hasSkill("conghui") then return false end
	if not player:hasSkill("yongsi") then return false end
	if player:getPhase() == sgs.Player_NotActive or player:getPhase() == sgs.Player_Finish then return false end
	local zhanglu = self.room:findPlayerBySkillName("xiliang")
	if zhanglu and self:isFriend(zhanglu, player) then return false end
	local erzhang = self.room:findPlayerBySkillName("guzheng")
	if erzhang and not zhanglu and self:isFriend(erzhang, player) then return false end

	self.yongsi_discard = nil
	local index = 0

	local kingdom_num = 0
	local kingdoms = {}
	for _, ap in sgs.qlist(self.room:getAlivePlayers()) do
		if not kingdoms[ap:getKingdom()] then
			kingdoms[ap:getKingdom()] = true
			kingdom_num = kingdom_num + 1
		end
	end

	local cards = self.player:getCards("he")
	local Discards = {}
	for _, card in sgs.qlist(cards) do
		local shouldDiscard = true
		if card:isKindOf("Axe") then shouldDiscard = false end
		if isCard("Peach", card, player) or isCard("Slash", card, player) then
			local dummy_use = { isDummy = true }
			self:useBasicCard(card, dummy_use)
			if dummy_use.card then shouldDiscard = false end
		end
		if card:getTypeId() == sgs.Card_TypeTrick then
			local dummy_use = { isDummy = true }
			self:useTrickCard(card, dummy_use)
			if dummy_use.card then shouldDiscard = false end
		end
		if shouldDiscard then
			if #Discards < 2 then table.insert(Discards, card:getId()) end
			index = index + 1
		end
	end

	if #Discards == 2 and index < kingdom_num then
		self.yongsi_discard = Discards
		return true
	end
	return false
end

sgs.ai_skill_cardask["@axe"] = function(self, data, pattern, target)
	if target and self:isFriend(target) then return "." end
	local effect = data:toSlashEffect()
	local allcards = self.player:getCards("he")
	allcards = sgs.QList2Table(allcards)
	if self:hasHeavySlashDamage(self.player, effect.slash, target)
	  or (#allcards - 3 >= self.player:getHp())
	  or (self.player:hasSkills("kuanggu|kuanggu_po") and self.player:isWounded() and self.player:distanceTo(effect.to) == 1)	--yun
	  or (effect.to:getHp() == 1 and not effect.to:hasSkill("buqu") and not effect.to:hasSkill("nosbuqu"))	--yun
	  or (self:needKongcheng() and self.player:getHandcardNum() > 0)
	  or (self:hasSkills(sgs.lose_equip_skill, self.player) and self.player:getEquips():length() > 1 and self.player:getHandcardNum() < 2)
	  or self:needToThrowAll() then
		local discard = self.yongsi_discard
		if discard then return "$"..table.concat(discard, "+") end

		local hcards = {}
		for _, c in sgs.qlist(self.player:getHandcards()) do
			if not (isCard("Slash", c, self.player) and self:hasCrossbowEffect()) then table.insert(hcards, c) end
		end
		self:sortByKeepValue(hcards)
		local cards = {}
		local hand, armor, def, off = 0, 0, 0, 0
		if self:needToThrowArmor() then
			table.insert(cards, self.player:getArmor():getEffectiveId())
			armor = 1
		end
		if (self:hasSkills(sgs.need_kongcheng) or not self:hasLoseHandcardEffective()) and self.player:getHandcardNum() > 0 then
			hand = 1		
			for _, card in ipairs(hcards) do
				if (not (self:hasSkill("LuaShengong") and (self.player:getHandcardNum() == 1) and card:isRed())) 
				and ((not (self:hasSkill("LuaShengong") and (#self.friends_noself > 0) and card:isBlack())) or (effect.to:getHp() == 1)) then --pay 永琳
					table.insert(cards, card:getEffectiveId())
					if #cards == 2 then break end
				end 
			end
		end
		if #cards < 2 and self:hasSkills(sgs.lose_equip_skill, self.player) then
			if #cards < 2 and self.player:getOffensiveHorse() then
				off = 1
				table.insert(cards, self.player:getOffensiveHorse():getEffectiveId())
			end
			if #cards < 2 and self.player:getArmor() then
				armor = 1
				table.insert(cards, self.player:getArmor():getEffectiveId())
			end
			if #cards < 2 and self.player:getDefensiveHorse() then
				def = 1
				table.insert(cards, self.player:getDefensiveHorse():getEffectiveId())
			end
		end

		if #cards < 2 and hand < 1 and self.player:getHandcardNum() > 2 then
			hand = 1
			for _, card in ipairs(hcards) do
				table.insert(cards, card:getEffectiveId())
				if #cards == 2 then break end
			end
		end

		if #cards < 2 and off < 1 and self.player:getOffensiveHorse() then
			off = 1
			table.insert(cards, self.player:getOffensiveHorse():getEffectiveId())
		end
		if #cards < 2 and hand < 1 and self.player:getHandcardNum() > 0 then
			hand = 1
			for _, card in ipairs(hcards) do
				if (not (self:hasSkill("LuaShengong") and (self.player:getHandcardNum() == 1) and card:isRed())) 
				and ((not (self:hasSkill("LuaShengong") and (#self.friends_noself > 0) and card:isBlack())) or (effect.to:getHp() == 1)) then --pay 永琳
					table.insert(cards, card:getEffectiveId())
					if #cards == 2 then break end
				end 
			end
		end
		if #cards < 2 and armor < 1 and self.player:getArmor() then
			armor = 1
			table.insert(cards, self.player:getArmor():getEffectiveId())
		end
		if #cards < 2 and def < 1 and self.player:getDefensiveHorse() then
			def = 1
			table.insert(cards, self.player:getDefensiveHorse():getEffectiveId())
		end

		if #cards == 2 then
			local num = 0
			local Rednum = 0
			for _, id in ipairs(cards) do
				if self.player:hasEquip(sgs.Sanguosha:getCard(id)) then num = num + 1 end
				if sgs.Sanguosha:getCard(id):isRed() then Rednum = Rednum + 1 end 
			end
			self.equipsToDec = num
			local eff = self:damageIsEffective(effect.to, effect.nature, self.player)
			self.equipsToDec = 0
			
			for _,p in sgs.qlist(self.room:findPlayersBySkillName("luaguixu")) do  --pay 小爱
				if p and p:getEquips() and not p:getEquips():isEmpty() then 
					local z = #hcards
					if not self.player:getEquips():isEmpty() then 
						z = z + self.player:getEquips():length()
					end 
					z = z - 2
					if z < p:getEquips():length() then return "." end 
				end 
				
			end 
			
			if not eff then return "." end
			return "$" .. table.concat(cards, "+")
		end
	end
end


function sgs.ai_slash_weaponfilter.axe(self, to, player)
	return player:distanceTo(to) <= math.max(sgs.weapon_range.Axe, player:getAttackRange()) and self:getOverflow(player) > 0
end

function sgs.ai_weapon_value.axe(self, enemy, player)
	if player:hasSkills("luoyi|neoluoyi|pojun|jiushi|jiuchi|jie|wenjiu|shenli|jieyuan|wansha|ytiangang|yxianfeng|yxiaoshou|yjieyuan|luajiuchi") then return 6 end	--yun
	if enemy and self:getOverflow() > 0 then return 3.1 end
	if enemy and enemy:getHp() < 3 then return 3 - enemy:getHp() end
end

sgs.ai_skill_cardask["blade-slash"] = function(self, data, pattern, target)
	if target and self:isFriend(target) and not self:findLeijiTarget(target, 50, self.player) then
		return "."
	end
	for _, slash in ipairs(self:getCards("Slash")) do
		if self:slashIsEffective(slash, target) and (self:isWeak(target) or self:getOverflow() > 0) then
			return slash:toString()
		end
	end
	return "."
end

function sgs.ai_weapon_value.blade(self, enemy)
	if not enemy and not self.player:hasWeapon("axe") then return math.min(self:getCardsNum("Slash"), 3) end
end

function sgs.ai_weapon_value.hakkero(self, enemy)
	if self.player:hasSkill("luadanmu") then return 2.9 end
	return 2
end

function cardsView_spear(self, player, skill_name)
	local cards = player:getCards("he")
	cards = sgs.QList2Table(cards)
	if skill_name ~= "fuhun" or player:hasSkill("wusheng") then
		for _, acard in ipairs(cards) do
			if isCard("Slash", acard, player) then return end
		end
	end
	cards = player:getCards("h")
	cards = sgs.QList2Table(cards)
	local newcards = {}
	for _, card in ipairs(cards) do
		if not isCard("Slash", card, player) and not (not card:isKindOf("BasicCard") and self.player:hasSkill("luazhishu"))
				and not isCard("Peach", card, player) and not (isCard("ExNihilo", card, player) and player:getPhase() == sgs.Player_Play) then
			if self.player:hasSkill("luaqiuwen") then
				if not ((card:isBlack() or ((card:getSuit() == sgs.Card_Heart) and self.player:hasSkill("luachuanming"))) and not card:isKindOf("BasicCard")) then
					table.insert(newcards, card)
				end
			else
				table.insert(newcards, card)
			end
		end
	end
	if #newcards < 2 then return end
	sgs.ais[player:objectName()]:sortByKeepValue(newcards)

	local card_id1 = newcards[1]:getEffectiveId()
	local card_id2 = newcards[2]:getEffectiveId()

	local card_str = ("slash:%s[%s:%s]=%d+%d"):format(skill_name, "to_be_decided", 0, card_id1, card_id2)
	return card_str
end

function sgs.ai_cardsview.spear(self, class_name, player)
	if class_name == "Slash" then
		return cardsView_spear(self, player, "spear")
	end
end

function turnUse_spear(self, inclusive, skill_name)
	local cards = self.player:getCards("h")
	if self.player:hasSkill("luaboli") then return end
	cards = sgs.QList2Table(cards)
	if skill_name ~= "fuhun" or self.player:hasSkill("wusheng") then
		for _, acard in ipairs(cards) do
			if isCard("Slash", acard, self.player) then return end
		end
	end
	if (self.player:getMark("luaguitu") == 0) and self.player:hasSkill("luaguitu") and (self:getEnemyNumBySeat(self.room:getCurrent(), self.player) - self.player:getHp() >= 2) then okdesu = true end
	self:sortByUseValue(cards)
	local newcards = {}
	local function Check_F(cardF)
		for _, askill in sgs.qlist(self.player:getVisibleSkillList(true)) do
			local filter = sgs.ai_cardneed[askill:objectName()]
			if filter and type(filter) == "function" and sgs.ai_cardneed[askill:objectName()](self.player, cardF, self) then return true end
		end
	end
	for _, card in ipairs(cards) do
		if not isCard("Slash", card, self.player) and not isCard("Peach", card, self.player)
				and not (isCard("Banquet", card, self.player) and #self.friends > 1) and not Check_F(card)
				and not (isCard("ExNihilo", card, self.player) and self.player:getPhase() == sgs.Player_Play) then table.insert(newcards, card) end
	end

	if #cards <= self.player:getHp() - 1 and self.player:getHp() <= 4 and not self:hasHeavySlashDamage(self.player)
			and not self:hasSkills("kongcheng|lianying|noslianying|paoxiao|shangshi|noshangshi|yfuhuo|kuangcai|LuaShengong") then return end	--yun
	if #newcards < 2 then return end
	if self:hasSkills("luachaofan") and self.player:getHandcardNum() < 3 then return end 
	local card_id1 = newcards[1]:getEffectiveId()
	local card_id2 = newcards[2]:getEffectiveId()

	if newcards[1]:isBlack() and newcards[2]:isBlack() then
		local black_slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuitBlack)
		local nosuit_slash = sgs.Sanguosha:cloneCard("slash")

		self:sort(self.enemies, "defenseSlash")
		for _, enemy in ipairs(self.enemies) do
			if self.player:canSlash(enemy) and not self:slashProhibit(nosuit_slash, enemy) and self:slashIsEffective(nosuit_slash, enemy)
					and self:canAttack(enemy) and self:slashProhibit(black_slash, enemy) and self:isWeak(enemy) then
				local redcards, blackcards = {}, {}
				for _, acard in ipairs(newcards) do
					if acard:isBlack() then table.insert(blackcards, acard) else table.insert(redcards, acard) end
				end
				if #redcards == 0 then break end

				local redcard, othercard

				self:sortByUseValue(blackcards, true)
				self:sortByUseValue(redcards, true)
				redcard = redcards[1]

				othercard = #blackcards > 0 and blackcards[1] or redcards[2]
				if redcard and othercard then
					card_id1 = redcard:getEffectiveId()
					card_id2 = othercard:getEffectiveId()
					break
				end
			end
		end
	end
	if self.player:hasSkill("lualueying") then
		if sgs.Sanguosha:getCard(card_id1):isKindOf("Slash") and sgs.Sanguosha:getCard(card_id2):isKindOf("Slash") then return end
		if sgs.Sanguosha:getCard(card_id1):objectName() == sgs.Sanguosha:getCard(card_id2):objectName() then return end
	end
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill("luaguihuo") then
			if sgs.Sanguosha:getCard(card_id1):getSuit() == sgs.Card_Spade then return end
			if sgs.Sanguosha:getCard(card_id2):getSuit() == sgs.Card_Spade then return end
		end
	end
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill("luahongyi") and friend:getMark("@hongyired") > 0 then
			if sgs.Sanguosha:getCard(card_id1):isRed() then return end
			if sgs.Sanguosha:getCard(card_id2):isRed() then return end
		end
	end

	local card_str = ("slash:%s[%s:%s]=%d+%d"):format(skill_name, "to_be_decided", 0, card_id1, card_id2)
	local slash = sgs.Card_Parse(card_str)
	return slash
end

local Spear_skill = {}
Spear_skill.name = "spear"
table.insert(sgs.ai_skills, Spear_skill)
Spear_skill.getTurnUseCard = function(self, inclusive)
	return turnUse_spear(self, inclusive, "spear")
end

function sgs.ai_weapon_value.spear(self, enemy, player)
	if enemy and getCardsNum("Slash", player, self.player) == 0 then
		if self:getOverflow(player) > 0 then return 2
		elseif player:getHandcardNum() > 2 then return 1
		end
	end
	return 0
end

function sgs.ai_slash_weaponfilter.fan(self, to, player)
	return player:distanceTo(to) <= math.max(sgs.weapon_range.Fan, player:getAttackRange())
		and to:hasArmorEffect("vine")
end

sgs.ai_skill_invoke.kylin_bow = function(self, data)
	local damage = data:toDamage()
	local from = damage.from	--yun
	local card = damage.card	--yun
	local to = damage.to
	
	if from:hasSkill("kuangfu") and to:getCards("e"):length() == 1 then return false end	--yun
	if from:hasSkill("qiaomeng") and card and card:isBlack() and to:getCards("e"):length() == 1 then return false end	--yun
	
	local dengai = to:hasSkill("tuntian") and to:hasSkill("zaoxian") and to:getPhase() == sgs.Player_NotActive
	if self:isFriend(to) and to:getOffensiveHorse() then
		return self:hasSkills(sgs.lose_equip_skill, to) or dengai
	end
	if self:isEnemy(to) then
		if to:getDefensiveHorse() then return true end
		if to:getOffensiveHorse() and self:doNotDiscard(to, "e") then return false end	--yun
	end
	return true
end

sgs.ai_skill_choice.kylin_bow = function(self,choices)	--yun
	return "dhorse" or "ohorse"
end

function sgs.ai_slash_weaponfilter.kylin_bow(self, to, player)
	return player:distanceTo(to) <= math.max(sgs.weapon_range.KylinBow, player:getAttackRange())
		and (to:getDefensiveHorse() or to:getOffensiveHorse())
end

function sgs.ai_weapon_value.kylin_bow(self, enemy)
	if enemy and (enemy:getOffensiveHorse() or enemy:getDefensiveHorse()) then return 1 end
end

sgs.ai_skill_invoke.eight_diagram = function(self, data)
	local dying = 0
	local handang = self.room:findPlayerBySkillName("nosjiefan")
	local current = self.room:getCurrent()	--yun
	if self:needChuanxin(self.player, current) then return false end	--yun
	if self:getTWBaobian(self.player, current) > 0 then return false end	--yun
	
	for _, aplayer in sgs.qlist(self.room:getAlivePlayers()) do
		if aplayer:getHp() < 1 and not aplayer:hasSkill("nosbuqu") then dying = 1 break end
	end
	if handang and self:isFriend(handang) and dying > 0 then return false end

	local heart_jink = false
	for _, card in sgs.qlist(self.player:getCards("he")) do
		if card:getSuit() == sgs.Card_Heart and isCard("Jink", card, self.player) then
			heart_jink = true
			break
		end
	end

	if self:hasSkills("tiandu|leiji|nosleiji|olleiji|gushou|yqinxue|luatiandu") then	--yun
		if self.player:hasFlag("dahe") and not heart_jink then return true end
		if sgs.hujiasource and not self:isFriend(sgs.hujiasource) and (sgs.hujiasource:hasFlag("dahe") or self.player:hasFlag("dahe")) then return true end
		if sgs.tianzhaosource and not self:isFriend(sgs.tianzhaosource) and (sgs.tianzhaosource:hasFlag("dahe") or self.player:hasFlag("dahe")) then return true end
		if sgs.lianlisource and not self:isFriend(sgs.lianlisource) and (sgs.lianlisource:hasFlag("dahe") or self.player:hasFlag("dahe")) then return true end
		if self.player:hasFlag("dahe") and handang and self:isFriend(handang) and dying > 0 then return true end
	end
	if self.player:getHandcardNum() == 1 and self:getCardsNum("Jink") == 1 and self.player:hasSkills("zhiji|beifa") and self:needKongcheng() then
		local enemy_num = self:getEnemyNumBySeat(self.room:getCurrent(), self.player, self.player)
		if self.player:getHp() > enemy_num and enemy_num <= 1 then return false end
	end
	if handang and self:isFriend(handang) and dying > 0 then return false end
	if self.player:hasFlag("dahe") then return false end
	if sgs.hujiasource and (not self:isFriend(sgs.hujiasource) or sgs.hujiasource:hasFlag("dahe")) then return false end
	if sgs.tianzhaosource and (not self:isFriend(sgs.tianzhaosource) or sgs.tianzhaosource:hasFlag("dahe")) and not sgs.tianzhaosource:hasFlag("shenpaning") then return false end
	if sgs.lianlisource and (not self:isFriend(sgs.lianlisource) or sgs.lianlisource:hasFlag("dahe")) then return false end
	if self:getDamagedEffects(self.player, nil, true) or self:needToLoseHp(self.player, nil, true, true) then return false end	--yun
	if self:getCardsNum("Jink") == 0 then return true end
	local zhangjiao = self.room:findPlayerBySkillName("guidao")
	if zhangjiao and self:isEnemy(zhangjiao) then
		if getKnownCard(zhangjiao, self.player, "black", false, "he") > 1 then return false end
		if self:getCardsNum("Jink") > 1 and getKnownCard(zhangjiao, self.player, "black", false, "he") > 0 then return false end
	end
	if self.player:getPile("incantation"):length() > 0 then	--yun 
		if self:getCardsNum("Jink") > 0 then return false end
		if sgs.Sanguosha:getCard(self.player:getPile("incantation"):first()):isBlack() then return false end
	end
	return true
end

function sgs.ai_armor_value.eight_diagram(player, self)
	local haszj = self:hasSkills("guidao", self:getEnemies(player))
	if haszj then
		return 2
	end
	if player:hasSkills("tiandu|leiji|nosleiji|olleiji|noszhenlie|gushou|yqinxue|luatiandu") then	--yun
		return 6
	end

	if self.role == "loyalist" and self.player:getKingdom()=="wei" and not self.player:hasSkills("bazhen|linglong") and getLord(self.player) and getLord(self.player):hasLordSkill("hujia") then
		return 5
	end
	local shikieiki = self.room:findPlayerBySkillName("luapanjue")
	if shikieiki and not self:isFriend(shikieiki) then return -5 end
	return 4
end
function sgs.ai_armor_value.tengu(player, self)
	return 4.5
end
function sgs.ai_armor_value.renwang_shield(player, self)
	if player:hasSkill("yizhong") then return 0 end
	if player:hasSkills("bazhen|linglong") then return 0 end
	if player:hasSkills("leiji|nosleiji|olleiji") and getKnownCard(player, self.player, "Jink", true) > 1 and player:hasSkill("guidao")
		and getKnownCard(player, self.player, "black", false, "he") > 0 then
			return 0
	end
	return 4.5
end

function sgs.ai_armor_value.silver_lion(player, self)
	if self:hasWizard(self:getEnemies(player), true) then
		for _, player in sgs.qlist(self.room:getAlivePlayers()) do
			if player:containsTrick("lightning") then return 5 end
		end
	end
	if self.player:isWounded() and not self.player:getArmor() then return 9 end
	if self.player:isWounded() and self:getCardsNum("Armor", "h") >= 2 and not self.player:hasArmorEffect("silver_lion") then return 8 end
	return 1
end

sgs.ai_use_priority.OffensiveHorse = 2.69

sgs.ai_use_priority.Axe = 2.688
sgs.ai_use_priority.Halberd = 2.685
sgs.ai_use_priority.KylinBow = 2.68
sgs.ai_use_priority.Hakkero = 2.68
sgs.ai_use_priority.Blade = 2.675
sgs.ai_use_priority.GudingBlade = 2.67
sgs.ai_use_priority.DoubleSword =2.665
sgs.ai_use_priority.spear = 2.66
-- sgs.ai_use_priority.fan = 2.655
sgs.ai_use_priority.IceSword = 2.65
sgs.ai_use_priority.QinggangSword = 2.645
sgs.ai_use_priority.Crossbow = 2.63
sgs.ai_use_priority.Gohei = 2.645
sgs.ai_use_priority.Shourinken = 2.645
sgs.ai_use_priority.ErinBow = 2.645

sgs.ai_use_priority.SilverLion = 1.0
-- sgs.ai_use_priority.Vine = 0.95
sgs.ai_use_priority.EightDiagram = 0.8
sgs.ai_use_priority.RenwangShield = 0.85
sgs.ai_use_priority.DefensiveHorse = 2.75

sgs.dynamic_value.damage_card.ArcheryAttack = true
sgs.dynamic_value.damage_card.SavageAssault = true

sgs.ai_use_value.ArcheryAttack = 3.8
sgs.ai_use_priority.ArcheryAttack = 3.5
sgs.ai_keep_value.ArcheryAttack = 3.38

sgs.ai_use_value.SavageAssault = 3.9
sgs.ai_use_priority.SavageAssault = 3.5
sgs.ai_keep_value.SavageAssault = 3.36

sgs.ai_use_value.ReligionBattle = 3.8
sgs.ai_use_priority.ReligionBattle = 6
sgs.ai_keep_value.ReligionBattle = 3.34


sgs.ai_skill_cardask.aoe = function(self, data, pattern, target, name)  -- 南蛮看这里
	
	if self.room:getMode():find("_mini_35") and self.player:getLostHp() == 1 and name == "archery_attack" then return "." end
	if sgs.ai_skill_cardask.nullfilter(self, data, pattern, target) then return "." end

	local aoe
	if type(data) == "userdata" then aoe = data:toCardEffect().card else aoe = sgs.Sanguosha:cloneCard(name) end
	assert(aoe ~= nil)
	local menghuo = self.room:findPlayerBySkillName("huoshou")
	local attacker = target
	if menghuo and aoe:isKindOf("SavageAssault") then attacker = menghuo end

	if self.player:hasSkill("luamaobu") and self:getOverflow() <= 0 and not target:isChained() then return "." end --sp猫车
	
	if not self:damageIsEffective(nil, nil, attacker) then return "." end
	if self:getDamagedEffects(self.player, attacker) or self:needToLoseHp(self.player, attacker) then return "." end

	if self.player:hasSkill("wuyan") and not attacker:hasSkill("jueqing") then return "." end
	if attacker:hasSkill("wuyan") and not attacker:hasSkill("jueqing") then return "." end
	if self.player:getMark("@fenyong") > 0 and not attacker:hasSkill("jueqing") then return "." end
	
	if self.player:hasSkill("ywushen") and self.player:getHp() < 2 and not attacker:hasSkill("jueqing") and aoe and aoe:isBlack() then return "." end  --yun
	
	if not attacker:hasSkill("jueqing") and self.player:hasSkill("jianxiong") and (self.player:getHp() > 1 or self:getAllPeachNum() > 0)
		and not self:willSkipPlayPhase() then
		if not self:needKongcheng(self.player, true) and self:getAoeValue(aoe) > -10 then return "." end
		if sgs.ai_qice_data then
			local damagecard = sgs.ai_qice_data:toCardUse().card
			if damagecard:subcardsLength() > 2 then self.jianxiong = true return "." end
			for _, id in sgs.qlist(damagecard:getSubcards()) do
				local card = sgs.Sanguosha:getCard(id)
				if not self:needKongcheng(self.player, true) and isCard("Peach", card, self.player) then return "." end
			end
		end
	end

	local current = self.room:getCurrent()
	if current and current:hasSkill("juece") and self:isEnemy(current) and self.player:getHp() > 0 then
		local classname = (name == "savage_assault" and "Slash" or "Jink")
		local use = false
		for _, card in ipairs(self:getCards(classname)) do
			if not self.player:isLastHandCard(card, true) then
				use = true
				break
			end
		end
		if not use then return "." end
	end
	if name == "savage_assault" then
		return self:getCardId("Slash", nil, nil, nil, aoe, target) or "."
	else
		return self:getCardId("Jink", nil, nil, nil, aoe, target) or "."
	end
end

sgs.ai_skill_cardask["savage-assault-slash"] = function(self, data, pattern, target)
	return sgs.ai_skill_cardask.aoe(self, data, pattern, target, "savage_assault")
end

sgs.ai_skill_cardask["archery-attack-jink"] = function(self, data, pattern, target)
	return sgs.ai_skill_cardask.aoe(self, data, pattern, target, "archery_attack")
end

sgs.ai_keep_value.Nullification = 3.8
sgs.ai_use_value.Nullification = 8

function SmartAI:useCardAmazingGrace(card, use)
	if self.player:hasSkill("noswuyan") then use.card = card return end --not (self.player:hasSkill("luafenxing") and (self.player:getHandcardNum() <= 2) and (target:getHp() > 1))
	--self.room:writeToConsole("五谷测试")
	local at = false
	if self.room:getLord():getNextAlive():objectName() == self.player:objectName() then at = true end
	if self.room:getLord():getNextAlive():getNextAlive():objectName() == self.player:objectName() then at = true end
	if (self.role == "lord" or (self.role == "loyalist" and at)) and sgs.turncount <= 2 and self.player:aliveCount() > 5 then return end
	if (self:getOverflow() <= 0) and self.player:hasSkill("luafenxing") and (self.player:getHandcardNum() <= 2) then return end
	if self.player:hasSkill("luazhishu") then return end
	if self.player:hasSkill("luachenti") and self.player:getHandcardNum() == 2 and self:getOverflow() < 1 then return end
	local value = 1
	if self:needKongcheng() then value = value + 1 end
	local suf, coeff = 0.8, 0.8
	if self:needKongcheng() and self.player:getHandcardNum() == 1 or self.player:hasSkills("nosjizhi|jizhi") then
		suf = 0.6
		coeff = 0.6
	end
	if self:HifuCard(card) then value = value + 1 end
	if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceSpecial) and self.player:getPileName(card:getId()) == "&zui" then value = value + 1.5 end
	for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		local index = 0
		if self:hasTrickEffective(card, player, self.player) then
			if self:isFriend(player) then index = 1 elseif self:isEnemy(player) then index = -1 end
		end
		value = value + index * suf
		if value < 0 then return end
		if (self.player:hasSkill("luafenxing") and (self.player:getHandcardNum() <= 2) and (self:getOverflow() <= 0)) and (value < 1.2) then return end
		if self.player:hasSkill("luatanmi") and (self:getOverflow() <= 0) and not self.player:hasFlag("luatanmi2") and (value < 1.2) then return end
		if self.player:hasSkill("luafenxing") and (value < 1.2) then return end
		suf = suf * coeff
	end
	use.card = card
end

function SmartAI:getAmazingGraceValue(card)
	local value = 1
	local suf, coeff = 0.8, 0.8
	if self:needKongcheng() and self.player:getHandcardNum() == 1 or self.player:hasSkills("nosjizhi|jizhi") then
		suf = 0.6
		coeff = 0.6
	end
	if self:HifuCard(card) then value = value + 1 end
	if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceSpecial) and self.player:getPileName(card:getId()) == "&zui" then value = value + 1.5 end
	for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		local index = 0
		if self:hasTrickEffective(card, player, self.player) then
			if self:isFriend(player) then index = 1 elseif self:isEnemy(player) then index = -1 end
		end
		value = value + index * suf
		if player:hasSkill("luasanaex") and not player:hasFlag("luasanaex") then
			if self:isFriend(player) then
				value = value + 3
			else
				value = value - 3
			end
		end
		if value < 0 then return 0 end
		if (self.player:hasSkill("luafenxing") and (self.player:getHandcardNum() <= 2) and (self:getOverflow() <= 0)) and (value < 1.2) then return 0 end
		if self.player:hasSkill("luatanmi") and (self:getOverflow() <= 0) and not self.player:hasFlag("luatanmi2") and (value < 1.2) then return 0 end
		if self.player:hasSkill("luafenxing") and (value < 1.2) then return 0 end
		suf = suf * coeff
	end
	return value
end
sgs.ai_use_value.AmazingGrace = 3
sgs.ai_keep_value.AmazingGrace = -1
sgs.ai_use_priority.AmazingGrace = 1.2
sgs.dynamic_value.benefit.AmazingGrace = true

function SmartAI:godSalvationValue(card)
	if not card then self.room:writeToConsole(debug.traceback()) return false end
	local good, bad = 0, 0
	local wounded_friend = 0
	local wounded_enemy = 0

	local liuxie = self.room:findPlayerBySkillName("huangen")
	if liuxie then
		if self:isFriend(liuxie) then
			if self.player:hasSkill("noswuyan") and liuxie:getHp() > 0 then return 0, 0, 0 end
			good = good + 7 * liuxie:getHp()
		else
			if self.player:hasSkill("noswuyan") and self:isEnemy(liuxie) and liuxie:getHp() > 1 and #self.enemies > 1 then return 0, 0, 0 end
			bad = bad + 7 * liuxie:getHp()
		end
	end

	if self.player:hasSkill("noswuyan") and (self.player:isWounded() or self.player:hasSkills("nosjizhi|jizhi")) then return 0, 0, 0 end
	if self.player:hasSkill("noswuyan") then return 0, 0, 0 end

	if self.player:hasSkills("nosjizhi|jizhi") then good = good + 6 end
	if (self:needKongcheng() and self.player:getHandcardNum() == 1) or not self:hasLoseHandcardEffective() then good = good + 5 end

	for _, friend in ipairs(self.friends) do
		good = good + 10 * getCardsNum("Nullification", friend, self.player)
		if self:hasTrickEffective(card, friend, self.player) then
			if friend:isWounded() then
				if (not friend:hasSkill("luasanaey")) or friend:getPhase() ~= sgs.Player_NotActive then
					wounded_friend = wounded_friend + 1
				end
				good = good + 10
				if friend:isLord() then	--yun 	
					if friend:getMark("hunzi_jie") > 0 then good = good -10
					elseif friend:hasSkill("hunzi") and friend:getMark("hunzi") == 0 and friend:getHp() == 1 then good = good - 20
					elseif friend:hasSkill("yhunzi") and friend:getMark("yhunzi") == 0 and friend:getHp() == 1 then good = good - 20
					else good = good + 10 / math.max(friend:getHp(), 1)
					end
				end
				if self:hasSkills(sgs.masochism_skill, friend) then
					good = good + 5
				end
				if friend:hasSkill("luasanaey") and friend:hasSkill("luasanaex") then
					if friend:getPhase() ~= sgs.Player_NotActive then
						good = good + 5
					else
						good = good - 15
					end
				end
				if friend:getHp() <= 1 and self:isWeak(friend) then
					good = good + 5
					if friend:isLord() and not (friend:getMark("hunzi_jie") > 0
							or (friend:hasSkill("hunzi") and friend:getMark("hunzi") == 0) or (friend:hasSkill("yhunzi") and friend:getMark("yhunzi") == 0)) then	--yun
						good = good + 10
					end
				else
					if friend:isLord() and not (friend:getMark("hunzi_jie") > 0
							or (friend:hasSkill("hunzi") and friend:getMark("hunzi") == 0 and friend:getHp() >= 1) or (friend:hasSkill("yhunzi") and friend:getMark("yhunzi") == 0 and friend:getHp() >= 1)) then	--yun
						good = good + 5
					end
				end
				if self:needToLoseHp(friend, nil, nil, true, true) then good = good - 3 end
			elseif friend:hasSkills("danlao|sheyan") then good = good + 5	--yun
			end
		end
	end

	for _, enemy in ipairs(self.enemies) do
		bad = bad + 10 * getCardsNum("Nullification", enemy, self.player)
		if self:hasTrickEffective(card, enemy, self.player) then
			if enemy:isWounded() then
				if (not enemy:hasSkill("luasanaey")) or enemy:getPhase() ~= sgs.Player_NotActive then
					wounded_enemy = wounded_enemy + 1
				end
				bad = bad + 10
				if enemy:isLord() and not (enemy:getMark("hunzi_jie") > 0
						or (enemy:hasSkill("hunzi") and enemy:getMark("hunzi") == 0) or (enemy:hasSkill("yhunzi") and enemy:getMark("yhunzi") == 0)) then	--yun
					bad = bad + 10 / math.max(enemy:getHp(), 1)
				end
				if self:hasSkills(sgs.masochism_skill, enemy) then
					bad = bad + 5
				end

				if enemy:hasSkill("luasanaey") and enemy:hasSkill("luasanaex") then
					if enemy:getPhase() ~= sgs.Player_NotActive then
						bad = bad + 5
					else
						bad = bad - 15
					end
				end
				if enemy:getHp() <= 1 and self:isWeak(enemy) then
					bad = bad + 5
					if enemy:isLord() and not (enemy:getMark("hunzi_jie") > 0
							or (enemy:hasSkill("hunzi") and enemy:getMark("hunzi") == 0) or (enemy:hasSkill("yhunzi") and enemy:getMark("yhunzi") == 0)) then	--yun
						bad = bad + 10
					end
				else
					if enemy:isLord() and not (enemy:getMark("hunzi_jie") > 0
							or (enemy:hasSkill("hunzi") and enemy:getMark("hunzi") == 0) or (enemy:hasSkill("yhunzi") and enemy:getMark("yhunzi") == 0)) then	--yun
						bad = bad + 5
					end
				end
				if self:needToLoseHp(enemy, nil, nil, true, true) then bad = bad - 3 end
			elseif enemy:hasSkills("danlao|sheyan") then bad = bad + 5	--yun
			end
		end
	end
	local value = good - bad
	if self.player:hasSkill("luabeihuan") and self.player:hasSkill("lualihe") then value = value - 10 end
	if self:HifuCard(card) then value = value + 5 end
	if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceSpecial) and self.player:getPileName(card:getId()) == "&zui" then value = value + 7 end
	--[[self.player:addMark("tanzhen2")
	if self.player:getMark("tanzhen2") > 12 then
		self.room:writeToConsole("si xun huan" .. tostring(self.player:getMark("tanzhen2")))
		self.room:writeToConsole(debug.traceback())
	end]]--
	self.room:writeToConsole("god Salvation value is" .. value)
	return value, wounded_friend, wounded_enemy
end 
function SmartAI:willUseGodSalvation(card)
	if not card then self.room:writeToConsole(debug.traceback()) return false end
	local value, wounded_friend, wounded_enemy = self:godSalvationValue(card)
	if (self:getOverflow() <= 0) and self.player:hasSkill("luafenxing") and (self.player:getHandcardNum() <= 2) then return end
	if self.player:hasSkill("luafenxing") and (value < 10) then return end
	if self.player:hasSkill("luachenti") and self.player:getHandcardNum() == 2 and self:getOverflow() < 1 and (value < 10) then return end
	return (value > 5 and wounded_friend > 0)  or (wounded_friend == 0 and wounded_enemy == 0 and self.player:hasSkills("nosjizhi|jizhi"))
end

function SmartAI:useCardGodSalvation(card, use)
	if self:willUseGodSalvation(card) then
		use.card = card
	end
end

sgs.ai_use_priority.GodSalvation = 1.1
sgs.ai_keep_value.GodSalvation = 3.32
sgs.dynamic_value.benefit.GodSalvation = true
sgs.ai_card_intention.GodSalvation = function(self, card, from, tos)
	if card:hasFlag("lualiuzhi") then return end
	local can, first
	for _, to in ipairs(tos) do
		if to:isWounded() and not first then
			first = to
			can = true
		elseif first and to:isWounded() and not self:isFriend(first, to) then
			can = false
			break
		end
	end
	if can then
		sgs.updateIntention(from, first, -10)
	end
end

function SmartAI:JijiangSlash(player)
	if not player then self.room:writeToConsole(debug.traceback()) return 0 end
	if not player:hasLordSkill("jijiang") then return 0 end
	local slashs = 0
	for _, p in sgs.qlist(self.room:getOtherPlayers(player)) do
		local slash_num = getCardsNum("Slash", p, self.player)
		if p:getKingdom() == "shu" and slash_num >= 1 and sgs.card_lack[p:objectName()]["Slash"] ~= 1 and
			(sgs.turncount <= 1 and sgs.ai_role[p:objectName()] == "neutral" or self:isFriend(player, p)) then
				slashs = slashs + slash_num
		end
	end
	return slashs
end

function SmartAI:useCardDuel(duel, use)
	if self.player:hasSkill("wuyan") and not self.player:hasSkill("jueqing") then return end
	if self.player:hasSkill("noswuyan") then return end
	local forTanbao = false
	local nazrin = self.room:findPlayerBySkillName("luatanbao")
	if nazrin and nazrin:isAlive() and self:isFriend(nazrin) and duel:hasFlag("prelingbai") then
		forTanbao = true
	end
	if self.player:getHp() >= 2 and #self.enemies > 1 and self.player:hasSkill("luamaoyou") and self:getOverflow() <= 0
			and not self.player:hasSkill("luashidan") and self.player:getMark("@luamaoyou") >= 1 and (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) then return end

	local enemies = self:exclude(self.enemies, duel)
	local friends = self:exclude(self.friends_noself, duel)
	duel:setFlags("AI_Using")
	local n1 = self:getCardsNum("Slash")
	duel:setFlags("-AI_Using")
	if self.player:hasSkill("wushuang") or use.isWuqian then
		n1 = n1 * 2
	end
	local huatuo = self.room:findPlayerBySkillName("jijiu")
	local targets = {}

	local canUseDuelTo=function(target)
		return self:hasTrickEffective(duel, target) and self:damageIsEffective(target,sgs.DamageStruct_Normal) and not self.room:isProhibited(self.player, target, duel)
	end

	for _, friend in ipairs(friends) do
		if (not use.current_targets or not table.contains(use.current_targets, friend:objectName())) and not forTanbao
			and friend:hasSkill("jieming") and canUseDuelTo(friend) and self.player:hasSkill("nosrende") and (huatuo and self:isFriend(huatuo)) then
			table.insert(targets, friend)
		end
	end

	for _, enemy in ipairs(enemies) do
		if (not use.current_targets or not table.contains(use.current_targets, enemy:objectName())) and (not forTanbao or self:isWeak(enemy))
			and self.player:hasFlag("duelTo_" .. enemy:objectName()) and canUseDuelTo(enemy) then
			table.insert(targets, enemy)
		end
	end
	if self.player:getRole() == "loyalist" and #targets == 0 then
		for _,p in sgs.qlist(self.room:getAlivePlayers()) do
			if not self:isFriend(p) and not self:isEnemy(p) then
				if (not use.current_targets or not table.contains(use.current_targets, p:objectName())) and (not forTanbao or self:isWeak(p))
						and self.player:hasFlag("duelTo_" .. p:objectName()) and canUseDuelTo(p) then
					table.insert(targets, p)
				end
			end
		end
	end
	local cmp = function(a, b)
		local v1 = getCardsNum("Slash", a) + a:getHp()
		local v2 = getCardsNum("Slash", b) + b:getHp()

		if self:getDamagedEffects(a, self.player) then v1 = v1 + 20 end
		if self:getDamagedEffects(b, self.player) then v2 = v2 + 20 end

		if not self:isWeak(a) and a:hasSkill("jianxiong") and not self.player:hasSkill("jueqing") then v1 = v1 + 10 end
		if not self:isWeak(b) and b:hasSkill("jianxiong") and not self.player:hasSkill("jueqing") then v2 = v2 + 10 end

		if self:needToLoseHp(a) then v1 = v1 + 5 end
		if self:needToLoseHp(b) then v2 = v2 + 5 end

		if self:hasSkills(sgs.masochism_skill, a) then v1 = v1 + 5 end
		if self:hasSkills(sgs.masochism_skill, b) then v2 = v2 + 5 end

		if not self:isWeak(a) and a:hasSkill("jiang") then v1 = v1 + 5 end
		if not self:isWeak(b) and b:hasSkill("jiang") then v2 = v2 + 5 end

		if self.player:hasSkill("luadaidu") then
			if not a:isWounded() then v1 = v1 - 5 end
			if not b:isWounded() then v2 = v2 - 5 end
		end

		if a:hasLordSkill("jijiang") then v1 = v1 + self:JijiangSlash(a) * 2 end
		if b:hasLordSkill("jijiang") then v2 = v2 + self:JijiangSlash(b) * 2 end

		if v1 == v2 then return sgs.getDefenseSlash(a, self) < sgs.getDefenseSlash(b, self) end

		return v1 < v2
	end

	table.sort(enemies, cmp) 
	if (self.player:hasSkill("shinue")) and (self:getOverflow() < 1) and (self.player:getMaxCards() > 2) then
		local bool = false
		for _, enemy in ipairs(enemies) do	
			if canUseDuelTo(enemy) then 
				local ph = enemy:getHp()
				if (ph == 1) then bool = true end
			end 
		end 
		if (not bool) then return end 
	end 
	for _, enemy in ipairs(enemies) do
		local useduel
		local n2 = getCardsNum("Slash", enemy)
		if enemy:hasSkill("wushuang") then n2 = n2 * 2 end
		if enemy:hasSkill("LuaYuanzu") then  -- pay 魅魔
			local p = enemy:getHandcardNum() * 0.35
			if math.random() < p then n2 = n2 + 1 end  
		end 
		local koishi = (self.player:hasSkill("luabenwo") and not enemy:inMyAttackRange(self.player)) --pay 恋恋
		if sgs.card_lack[enemy:objectName()]["Slash"] == 1 then n2 = 0 end
		useduel = n1 >= n2 or self:needToLoseHp(self.player, nil, nil, true) or koishi
					or (self.player:hasSkill("LuaFeixiang") and self.player:getPile("qizhi2"):length() > 1 and not self:isWeak())
					or self:getDamagedEffects(self.player, enemy) or (n2 < 1 and sgs.isGoodHp(self.player))
					or ((self:hasSkill("jianxiong") or self.player:getMark("shuangxiong") > 0) and sgs.isGoodHp(self.player))
		if (self.player:hasSkill("Luayuelong") and duel:isBlack() and (not ((self:YouMu2(enemy, true) and self:isWeak(enemy)) or (self:getOverflow() > 0)))
			and duel and duel:getSkillName() ~= "Luayuelong" and not use.shoutu ) then
			useduel = false  
		end 
		if (not use.current_targets or not table.contains(use.current_targets, enemy:objectName())) 
			and not (self.player:hasSkill("luafenxing") and (self.player:getHandcardNum() <= 2) and (enemy:getHp() > 1) and (self:getOverflow() <= 0)) 
			and self:objectiveLevel(enemy) > 3 and canUseDuelTo(enemy) and not self:cantbeHurt(enemy) and useduel and sgs.isGoodTarget(enemy, enemies, self) then
			if not self:PayContains(targets, enemy) then table.insert(targets, enemy) end
		end
	end

	if #targets > 0 then

		local godsalvation = self:getCard("GodSalvation")
		if godsalvation and godsalvation:getId() ~= duel:getId() and self:willUseGodSalvation(godsalvation) then
			local use_gs = true
			for _, p in ipairs(targets) do
				if not p:isWounded() or not self:hasTrickEffective(godsalvation, p, self.player) then break end
				use_gs = false
			end
			if use_gs then
				use.card = godsalvation
				return
			end
		end


		local targets_num = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, self.player, duel)
		if use.isDummy and use.xiechan then targets_num = 100 end
		if use.isDummy and use.extra_target then targets_num = targets_num + use.extra_target end
		local enemySlash = 0
		local setFlag = false
		local lx = self.room:findPlayerBySkillName("huangen")

		use.card = duel
		if not self:YukiMai(use) then return else use.card = duel end
		for i = 1, #targets, 1 do
			local n2 = getCardsNum("Slash", targets[i])
			if sgs.card_lack[targets[i]:objectName()]["Slash"] == 1 then n2 = 0 end
			if self:isEnemy(targets[i]) then enemySlash = enemySlash + n2 end

			if not use.isDummy then
				local ofuda = self:searchForOfuda(use, targets[i], use.card)
				if ofuda and self:canBeOfudaTarget(targets[i], ofuda) and ofuda:getEffectiveId() ~= duel:getEffectiveId()
						and not (self.player:hasSkill("LuaChunguang") and ofuda:getNumber() <= self.player:getLostHp() * 2 and #self.enemies > 1)
						and ((not self.player:hasFlag("jianji")) or (self.player:getPhase() ~= sgs.Player_Play) or (self.player:getMark("jianji") > 1)) then
					use.card = ofuda
					local Targets_num = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, self.player, ofuda)

					local Targets = {}
					local Enemies = self.enemies
					self:sort(Enemies, "hp")
					for _, to in ipairs(Enemies) do
						if self:canBeOfudaTarget(to, ofuda) then
							table.insert(Targets, to)
						end
					end

					if use.to then
						use.to = sgs.SPlayerList()
						for _, to in ipairs(Targets) do
							use.to:append(to)
							if use.to:length() == Targets_num then return end
						end
						return
					end
				end
			end

			if not use.isDummy and self.player:hasSkill("duyi") and targets[i]:getHp() == 1 and not targets[i]:isKongcheng()	--yun
				and self.room:getDrawPile():length() > 0 and not self.player:hasUsed("DuyiCard") then
				sgs.ai_duyi = { id = self.room:getDrawPile():first(), tg = targets[i] }
				use.card = sgs.Card_Parse("@DuyiCard=.")
				if use.to then use.to = sgs.SPlayerList() end
				return
			end
			if use.to then
				if i == 1 and not use.current_targets then
					use.to:append(targets[i])
					if not use.isDummy and math.random() < 0.5 then self:speak("duel", self.player:isFemale()) end
				elseif (n1 >= enemySlash) and not targets[i]:hasSkill("danlao") and not (lx and self:isEnemy(lx) and lx:getHp() > targets_num / 2) then  --待处理 恋恋
					use.to:append(targets[i])
				end
				if not setFlag and self.player:getPhase() == sgs.Player_Play and self:isEnemy(targets[i]) then
					self.player:setFlags("duelTo" .. targets[i]:objectName())
					setFlag = true
				end
				if use.to:length() == targets_num then return end
			end
		end
	end

end

sgs.ai_card_intention.Duel = function(self, card, from, tos)
	if from:hasFlag("luaaoshu") then return end
	if card:hasFlag("lualiuzhi") then return end
	if string.find(card:getSkillName(), "luajuezhan") then return end
	if string.find(card:getSkillName(), "lijian") then return end
	if string.find(card:getSkillName(), "liyu") then return end
	local white = self.room:findPlayerBySkillName("Luajingzhe")

	for _, to in ipairs(tos) do
		if to:hasFlag("jili") then continue end	--yun
		if to:hasSkill("luabainian") then continue end
		if white and string.find(card:getSkillName(), "Luajingzhe") and self.room:getCurrent() == white:objectName() then
			sgs.updateIntention(white, to, 80)
		else
			sgs.updateIntention(from, to, 80)
		end

	end
end

sgs.ai_use_value.Duel = 3.7
sgs.ai_use_priority.Duel = 2.9
sgs.ai_keep_value.Duel = 3.42

sgs.dynamic_value.damage_card.Duel = true

sgs.ai_skill_cardask["duel-slash"] = function(self, data, pattern, target)  --决斗出杀
	local duel = sgs.Sanguosha:cloneCard("duel")
	if self.player:getPhase() == sgs.Player_Play then return self:getCardId("Slash", nil, nil, nil, duel, target) end

	if sgs.ai_skill_cardask.nullfilter(self, data, pattern, target) then return "." end
	if self.player:hasFlag("AIGlobal_NeedToWake") and self.player:getHp() > 1 then return "." end
	if (target:hasSkill("wuyan") or self.player:hasSkill("wuyan")) and not target:hasSkill("jueqing") then return "." end
	if self:getTWBaobian(self.player, target) > 0 then	--yun
		return "."
	end

	if self.player:hasSkill("luabingpu") and (self.player:getMark("@baka") == 0) and self.player:getHp() > 2 then
		local black = {}
		local red = {}
		local allcards = sgs.QList2Table(self.player:getCards("he"))
		for _, card in ipairs(allcards) do
			if card:isBlack() then table.insert(black, card) end
		end
		for _, card in ipairs(allcards) do
			if card:isRed() then table.insert(red, card) end
		end
		if not ((#red == 1) and (red[1]:isKindOf("ExNihilo") or (red[1]:isKindOf("Peach") and self.player:isWounded()))) then
			return "."
		end
	end
	if self.player:hasSkill("luaouxiang") then
		for _, friend in ipairs(self.friends) do
			for _, card in sgs.list(friend:getHandcards()) do
				if card:isKindOf("Analeptic") or card:isKindOf("Peach") then return "." end
			end
		end
	end
	if self.player:getMark("@fenyong") > 0 and self.player:hasSkill("fenyong") and not target:hasSkill("jueqing") then return "." end
	if self.player:hasSkill("wuhun") and self:isEnemy(target) and target:isLord() and #self.friends_noself > 0 then return "." end
	if self.player:getHp() >= 3 and self.player:hasSkill("lualueying") then return "." end
	if self.player:hasSkill("Luahongzhuan") then --pay 蕾米
		local slashes = self:getCards("Slash")
		local RedArr = {}
		local BlackArr = {}
		for _, slash in ipairs(slashes) do
			if not slash:isRed() and not prohibitUseDirectly(slash, self.player) then
				table.insert(BlackArr, slash)
			elseif slash:isRed() and not prohibitUseDirectly(slash, self.player) then
				table.insert(RedArr, slash)
			end
		end
		if #BlackArr == 0 and #RedArr > 0 then
			local bizhong = (target:getHandcardNum() < #slashes)
			if not bizhong and (self:getOverflow() < 2 or #RedArr < 2) then return "." end
		end
	end

	if self:cantbeHurt(target) then return "." end


	if self:isFriend(target) and target:hasSkills("rende|nosrende") and self.player:hasSkill("jieming") then return "." end	--yun
	if self:isEnemy(target) and not self:isWeak() and self:getDamagedEffects(self.player, target) then return "." end
	if not self:isFriend(target) and target:hasSkill("naman") then return "." end	--yun

	if self:isFriend(target) then
		if self:getDamagedEffects(self.player, target) or self:needToLoseHp(self.player, target) then return "." end
		if self:getDamagedEffects(target, self.player) or self:needToLoseHp(target, self.player) then
			return self:getCardId("Slash")
		else
			if target:isLord() and not sgs.isLordInDanger() and not sgs.isGoodHp(self.player) then return self:getCardId("Slash") end
			if self.player:isLord() and sgs.isLordInDanger() then return self:getCardId("Slash") end
			return "."
		end
	end
	if self.player:hasSkill("luafenxing") and getCardsNum("Slash", target, self.player) > 0.9 then return end
	if (not self:isFriend(target) and (self:getCardsNum("Slash") >= getCardsNum("Slash", target, self.player) or (self:getCardsNum("Slash") > 1) and not target:hasSkill("wushuang")))	--yun
			or (not self:isFriend(target) and self:hasSkills("yshanzhan|yyuanlv|naman"))
			or (not self:isFriend(target) and not self:hasLoseHandcardEffective(self.player) and not self.player:isKongcheng())
			or (target:getHp() > 2 and self.player:getHp() <= 1 and self:getCardsNum("Peach") == 0 and not hasBuquEffect(self.player)) then
		return self:getCardId("Slash", self.player, nil, true, duel, target)
	else return "." end
end

function SmartAI:Wriggle()
	if not self.player:isWounded() and self:getOverflow() > 1 then return false end
	if self.player:isWounded() and self:getOverflow() > 2 then return false end
	local count = 0
	local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
	if self.player:getNextAlive():faceUp() and self:isEnemy(self.player:getNextAlive())
			and self.player:getNextAlive():canSlash(self.player, slash, true) then count = count + 1 end
	if self.player:getNextAlive():getNextAlive():faceUp() and self:isEnemy(self.player:getNextAlive():getNextAlive())
			and self.player:getNextAlive():getNextAlive():canSlash(self.player, slash, true) then count = count + 1 end
	if self:isFriend(self.player:getNextAlive()) then count = 0 end
	if self:getOverflow() >= -1 and count < self.player:getHp() and #self.friends_noself > 0 then return true end
	return false
end
function SmartAI:useCardExNihilo(card, use)
	local xiahou = self.room:findPlayerBySkillName("yanyu")
	if xiahou and self:isEnemy(xiahou) and xiahou:getMark("YanyuDiscard2") > 0 then return end
	if not self.player:isWounded() and self.player:getHp() > 2 and #self.friends > 1 and self.player:hasSkill("luashenjun")
		and self.player:hasUsed("#luashenjun") and (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) then return end
	if self:getCardsNum("Slash") >= 1 and self.player:getHp() >= 2 and #self.friends > 1 and self.player:hasSkill("luamaoyou")
		and not self.player:hasSkill("luashidan") and self.player:getMark("@luamaoyou") >= 1 and (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) then return end
	if self.player:hasSkill("luachongqun2") then
		if self:Wriggle() and (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) then return end
	end
	local canUse = false
	if self.player:getMark("@luajulian1") > 0 then
		for _, friend in ipairs(self.friends_noself) do
			if not self.room:isProhibited(self.player, friend, card) then canUse = true end
		end
		if not canUse then return end
	end
	use.card = card
	if not use.isDummy then
		if not self:YukiMai(use) then return else use.card = card end
		self:speak("lucky")
	end
end

sgs.ai_card_intention.ExNihilo = function(self, card, from, tos)
	for _, to in ipairs(tos) do
		if to:hasFlag("jili") then continue end	--yun
		sgs.updateIntention(from, to, -80)
	end
end

sgs.ai_keep_value.ExNihilo = 3.9
sgs.ai_use_value.ExNihilo = 10
sgs.ai_use_priority.ExNihilo = 9.3

sgs.dynamic_value.benefit.ExNihilo = true

function SmartAI:getDangerousCard(who)
	local weapon = who:getWeapon()
	local armor = who:getArmor()
	if weapon and (weapon:isKindOf("Crossbow") or weapon:isKindOf("GudingBlade")) then
		for _, friend in ipairs(self.friends) do
			if weapon:isKindOf("Crossbow") and who:distanceTo(friend) <= 1 and getCardsNum("Slash", who, self.player) > 0 then
				return weapon:getEffectiveId()
			end
			if weapon:isKindOf("Crossbow") and who:hasSkill("luatianzhao") then
				return weapon:getEffectiveId()
			end
			if weapon:isKindOf("GudingBlade") and who:inMyAttackRange(friend) and who:hasSkills("luatielun|luayueshi|luahongpei") then
				return weapon:getEffectiveId()
			end
			if weapon:isKindOf("GudingBlade") and who:inMyAttackRange(friend) and self:canHitDown(friend, who) and getCardsNum("Slash", who) > 0 then	--yun
				return weapon:getEffectiveId()
			end
		end
	end
	if (weapon and weapon:isKindOf("spear") and who:hasSkill("paoxiao") and who:getHandcardNum() >= 1 ) then return weapon:getEffectiveId() end
	if weapon and weapon:isKindOf("Axe") and not who:hasSkill("ol_liegong") then	--yun
		if (who:hasSkills("luoyi|pojun|jiushi|jiuchi|jie|wenjiu|shenli|jieyuan|neoluoyi|nosluoyi|wansha|ytiangang|yxianfeng|yxiaoshou|yshanzhan|yyuanlao|yjieyuan|luajiuchi")
			or self:getOverflow(who) > 0 or who:getCardCount() >= 4) then
			return weapon:getEffectiveId()
		end
	end
	if armor and armor:isKindOf("EightDiagram") and who:hasSkills("leiji|nosleiji|olleiji") then return armor:getEffectiveId() end	--yun

	local offhorse = who:getOffensiveHorse() --yun
	local defhorse = who:getDefensiveHorse()
	local treasure = who:getTreasure()
	if treasure then
		if treasure:isKindOf("WoodenOx") and who:getPile("wooden_ox"):length() > 3 then
			return treasure:getEffectiveId()
		end
	end
	if treasure then
		if treasure:isKindOf("Wanbaochui") then
			return treasure:getEffectiveId()
		end
	end
	if offhorse and who:hasSkills("shenji|yqijiang|dujin|zhaoxin|zhaolie|yzhaoxiang") then return offhorse:getEffectiveId() end
	if defhorse and who:hasSkills("yqijiang|anjian|dujin|yzhaoxiang") then return defhorse:getEffectiveId() end
	if armor and (who:hasSkill("yzhaoxiang") or who:hasSkill("dujin")) then return armor:getEffectiveId() end
	if treasure and who:hasSkills("dujin|yzhaoxiang") then return treasure:getEffectiveId() end
	if weapon and who:hasSkills("liegong|liegong_po|qiangxi|anjian|dujin|yzhaoxiang|zhaoxin|zhaolie|yfeidao") then return weapon:getEffectiveId() end	--yun
	
	local lord = self.room:getLord()
	if lord and lord:hasLordSkill("hujia") and self:isEnemy(lord) and armor and armor:isKindOf("EightDiagram") and who:getKingdom() == "wei" then
		return armor:getEffectiveId()
	end

	if (weapon and weapon:isKindOf("SPMoonSpear") and self:hasSkills("guidao|longdan|guicai|jilve|huanshi|qingguo|kanpo|longhun", who)) then	--yun
		return weapon:getEffectiveId()
	end

	if weapon then
		for _, friend in ipairs(self.friends) do
			if who:distanceTo(friend) < who:getAttackRange(false) and self:isWeak(friend) and not self:doNotDiscard(who, "e", true) then return weapon:getEffectiveId() end
		end
	end
end

function SmartAI:getValuableCard(who, bool)  --找有用装备,bool表示需要更紧急的
	local weapon = who:getWeapon()
	local armor = who:getArmor()
	local offhorse = who:getOffensiveHorse()
	local defhorse = who:getDefensiveHorse()
	local treasure = who:getTreasure()
	self:sort(self.friends, "hp")
	local friend
	if #self.friends > 0 then friend = self.friends[1] end
	if friend and self:isWeak(friend) and who:distanceTo(friend) <= who:getAttackRange(false) and not self:doNotDiscard(who, "e", true) then
		if weapon and who:distanceTo(friend) > 1 then
			return weapon:getEffectiveId()
		end
		if offhorse and who:distanceTo(friend) > 1 then
			return offhorse:getEffectiveId()
		end
	end

	if weapon then
		if (weapon:isKindOf("MoonSpear") and who:hasSkill("keji") and who:getHandcardNum() > 5) or who:hasSkills(sgs.need_weapon_skill)	--yun 
				or who:hasSkills("liuli") or who:hasSkill("luazhengyi") then
			return weapon:getEffectiveId()
		end
	end

	if weapon then
		if (weapon:isKindOf("axe") and who:hasSkill("LuaShanguang") and (who:hasSkill("LuaYuanzu") or (who:getJudgingArea():length() > 1))) then -- pay 魅魔
			return weapon:getEffectiveId()
		end
	end

	local equips = sgs.QList2Table(who:getEquips())
	for _, equip in ipairs(equips) do
		if who:hasSkill("longhun") and equip:getSuit() ~= sgs.Card_Diamond then return equip:getEffectiveId() end
		if who:hasSkills("guose|nosguose|yanxiao") and equip:getSuit() == sgs.Card_Diamond then return equip:getEffectiveId() end	--yun
		if who:hasSkill("baobian") and who:getHp() <= 2 then return equip:getEffectiveId() end
		if who:hasSkills("qixi|duanliang|yinling|guidao|huomo") and equip:isBlack() then return equip:getEffectiveId() end	--yun
		if who:hasSkills("wusheng|jijiu|xueji|nosfuhun") and equip:isRed() then return equip:getEffectiveId() end
		if who:hasSkills(sgs.need_equip_skill) and not (who:hasSkills(sgs.lose_equip_skill)) then return equip:getEffectiveId() end	--yun	
		if self:hasSkills("yjuedou|yjinshen", who) and equip:getSuit() == sgs.Card_Spade then return equip:getEffectiveId() end  --yun
	end

	if armor and self:evaluateArmor(armor, who) > 3 and not self:needToThrowArmor(who) and not self:doNotDiscard(who, "e") then
		return armor:getEffectiveId()
	end

	if weapon and not weapon:isKindOf("Crossbow") then
		if who:hasSkills("luahakurei|luajianji") then -- pay sp妖梦
			return weapon:getEffectiveId()
		end
	end

	if treasure then	--yun	顺序置前
		if treasure:isKindOf("WoodenOx") and who:getPile("wooden_ox"):length() > 0 then
			return treasure:getEffectiveId()
		end
	end

	if bool then return end

	if treasure then	--yun	顺序置前
		if treasure:isKindOf("Wanbaochui") then
			return treasure:getEffectiveId()
		end
	end


	if offhorse then
		if who:hasSkills("nosqianxi|kuanggu|kuanggu_po|duanbing|qianxi|kangkai|liuli|luajianji") then	--yun
			return offhorse:getEffectiveId()
		end
	end

	if defhorse and not self:doNotDiscard(who, "e")
			and not (self.player:hasWeapon("kylin_bow") and self.player:canSlash(who) and self:slashIsEffective(sgs.Sanguosha:cloneCard("slash"), who, self.player)
			and (getCardsNum("Jink", who, self.player) < 1 or sgs.card_lack[who:objectName()].Jink == 1)) then
		return defhorse:getEffectiveId()
	end

	local wuguotai = self.room:findPlayerBySkillName("buyi")	--yun	其他都是"e"，可能会有bug
	if who:getHandcardNum() == 1 and not self:doNotDiscard(who, "h") then
		local card = who:getHandcards():first()
		if who:hasSkills(sgs.dont_kongcheng_skill) then return card:getEffectiveId() end
		if wuguotai and self:isFriend(who, wuguotai) and self:isWeak(who) then return card:getEffectiveId() end
	end

	if armor and not self:needToThrowArmor(who) and not self:doNotDiscard(who, "e") then
		return armor:getEffectiveId()
	end

	if offhorse and who:getHandcardNum() > 1 then
		if not self:doNotDiscard(who, "e", true) then
			for _, friendA in ipairs(self.friends) do
				if who:distanceTo(friendA) == who:getAttackRange() and who:getAttackRange() > 1 then
					return offhorse:getEffectiveId()
				end
			end
		end
	end

	if armor and who:hasSkill("luaguixu") and who:getMark("@guixu") > 0 and who:hasFlag("luajunzhenk") then
		return armor:getEffectiveId()
	end
	if weapon and who:hasSkill("luaguixu") and who:getMark("@guixu") > 0 and who:hasFlag("luajunzhenk") then
		return weapon:getEffectiveId()
	end
	if treasure and who:hasSkill("luaguixu") and who:getMark("@guixu") > 0 and who:hasFlag("luajunzhenk") then
		return treasure:getEffectiveId()
	end
	if offhorse and who:hasSkill("luaguixu") and who:getMark("@guixu") > 0 and who:hasFlag("luajunzhenk") then
		return offhorse:getEffectiveId()
	end
	if defhorse and who:hasSkill("luaguixu") and who:getMark("@guixu") > 0 and who:hasFlag("luajunzhenk") then
		return defhorse:getEffectiveId()
	end

	if weapon and who:getHandcardNum() > 1 then
		if not self:doNotDiscard(who, "e", true) then
			for _, friendA in ipairs(self.friends) do
				if (who:distanceTo(friendA) <= who:getAttackRange()) and (who:distanceTo(friendA) > 1) then
					return weapon:getEffectiveId()
				end
			end
		end
	end

end

function SmartAI:useCardSnatchOrDismantlement(card, use) --self.room:writeToConsole("姬海棠测试3")
	local isSkillCard = card:isKindOf("YinlingCard") or card:isKindOf("DanshouCard") or card:isKindOf("LuaSkillCard") or card:isKindOf("LuaYao")
			or card:getSkillName() == "luatongxin" or card:getSkillName() == "luahongye"
	local isJixi = card:getSkillName() == "jixi"
	local isDiscard = (not card:isKindOf("Snatch")) and (card:getSkillName() ~= "Luaweiwo")
	local name = card:isKindOf("YinlingCard") and "yinling" or card:isKindOf("DanshouCard") and "danshou" or card:objectName()
	if card:isKindOf("LuaSkillCard") then
		name = "#" .. card:objectName()
	end
	local using_2013 = (name == "dismantlement") and self.room:getMode() == "02_1v1" and sgs.GetConfig("1v1/Rule", "Classical") ~= "Classical"
	if not isSkillCard and self.player:hasSkill("noswuyan") then return end
	if not isSkillCard and isDiscard and card:getNumber() > 9 and self.player:hasSkills("luayueshi|luatongxin") then return end  --pay 露米娅
	if self.player:hasSkill("yjieliang") and card:getSuit() == sgs.Card_Spade and card:isKindOf("Dismantlement") then return end	--yun

	local players = self.room:getOtherPlayers(self.player)
	local tricks
	local usecard = false

	local targets = {}
	local targets_num = isSkillCard and 1 or (1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, self.player, card))
	if use.isDummy and use.extra_target then targets_num = targets_num + use.extra_target end
	local lx = self.room:findPlayerBySkillName("huangen")
	local function BetterNot(target)
		if not isDiscard then return false end
		if target:hasSkill("luasanku") and target:getJudgingArea():length() < 2 then return true end
		if target:hasSkill("luashikong") and target:getMark("luashikong2") < 2 then return true end
		if target:hasSkill("luayuechong") then return true end
		return false
	end
	local addTarget = function(player, cardid)
		if not table.contains(targets, player:objectName())    
				and (not use.current_targets or not table.contains(use.current_targets, player:objectName()))
				and not (use.to and use.to:length() > 0 and player:hasSkill("danlao"))
				and not (use.to and use.to:length() > 0 and lx and self:isEnemy(lx) and lx:getHp() > targets_num / 2)
				and ((card:getSkillName() ~= "Luaweiwo") or not player:inMyAttackRange(self.player))
		then
			if not usecard then
				-- if card:isKindOf("LuaSkillCard") then
				-- use.card = sgs.Card_Parse(name .. ":" .. cardid ..":")
				-- else
				use.card = card
				usecard = true
				--end 
			end
			table.insert(targets, player:objectName())
			if usecard and use.to and use.to:length() < targets_num then
				if not self:YukiMai(use) then return else use.card = card end
				use.to:append(player)
				if not use.isDummy then
					sgs.Sanguosha:getCard(cardid):setFlags("AIGlobal_SDCardChosen_" .. name)
					if use.to:length() == 1 and math.random() < 0.5 then self:speak(use.card:getClassName(), self.player:isFemale()) end
				end
			end
			if #targets == targets_num then return true end
		end
	end

	players = self:exclude(players, card)
	if not isSkillCard and not using_2013 then
		for _, player in ipairs(players) do
			if not player:getJudgingArea():isEmpty() and (self:hasTrickEffective(card, player) or isSkillCard) --这种情况不用管梅莉，要死人了你特么还不拆
					and ((player:containsTrick("lightning") and self:getFinalRetrial(player) == 2) or #self.enemies == 0) then
				tricks = player:getCards("j")
				for _, trick in sgs.qlist(tricks) do
					if trick:isKindOf("Lightning") and (not isDiscard or self.player:canDiscard(player, trick:getId())) then
						if addTarget(player, trick:getEffectiveId()) then return end
					end
				end
			end
		end
	end

	local enemies = {}
	if #self.enemies == 0 and self:getOverflow() > 0 then
		local lord = self.room:getLord()
		for _, player in ipairs(players) do
			if not self:isFriend(player) and (self:hasTrickEffective(card, player) or isSkillCard) then
				if lord and self.player:isLord() then
					local kingdoms = {}
					if lord:getGeneral():isLord() then table.insert(kingdoms, lord:getGeneral():getKingdom()) end
					if lord:getGeneral2() and lord:getGeneral2():isLord() then table.insert(kingdoms, lord:getGeneral2():getKingdom()) end
					if not table.contains(kingdoms, player:getKingdom()) and not lord:hasSkill("yongsi") then table.insert(enemies, player) end
				elseif lord and player:objectName() ~= lord:objectName() then
					table.insert(enemies, player)
				elseif not lord then
					table.insert(enemies, player)
				end
			end
		end
		enemies = self:exclude(enemies, card)  --这就是顺手控制距离的函数
		local temp = {}
		for _, enemy in ipairs(enemies) do
			if (enemy:hasSkills("tuntian+guidao") and not enemy:containsTrick("gainb")) and enemy:hasSkills("zaoxian|jixi|ziliang|leiji|nosleiji|olleiji") then continue end	--yun
			if self:hasTrickEffective(card, enemy) or isSkillCard then
				table.insert(temp, enemy)
			end
		end
		enemies = temp
		self:sort(enemies, "defense")
		enemies = sgs.reverse(enemies)
	else
		enemies = self:exclude(self.enemies, card)
		local temp = {}
		for _, enemy in ipairs(enemies) do
			if (enemy:hasSkills("tuntian+guidao") and not enemy:containsTrick("gainb")) and enemy:hasSkills("zaoxian|jixi|ziliang|leiji|nosleiji|olleiji") then continue end	--yun
			if self:hasTrickEffective(card, enemy) or isSkillCard then
				table.insert(temp, enemy)
			end
		end
		enemies = temp
		self:sort(enemies, "defense")
	end

	if not isSkillCard and not using_2013 then
		for _, enemy in ipairs(enemies) do
			if enemy:getMark("@LuaBisha2") == 2 and (self:hasTrickEffective(card, enemy) or isSkillCard) and self.player:hasSkill("LuaBisha")
					and self:isEnemy(enemy) and not self.player:hasFlag("LuaBishaX") then  --三 步 必 杀
				local valuable = self:getValuableCard(enemy)
				if valuable and (not isDiscard or self.player:canDiscard(enemy, valuable)) and not self:doNotDiscard(enemy, "e") then
					if addTarget(enemy, valuable) then return end
				else
					if addTarget(enemy, enemy:getRandomHandCardId()) then return end
				end
			end
		end
	end

	if self.player:hasSkill("yjieliang") and not isDiscard then	--yun	顺黑桃 梅莉莉gainb定是黑桃
		for _, enemy in ipairs(enemies) do
			if not self:doNotDiscard(enemy, "e") and self:hasTrickEffective(card, enemy) then
				local cardchosen
				local equips = sgs.QList2Table(enemy:getEquips())
				for _,equip in ipairs(equips) do
					if equip:getSuit() == sgs.Card_Spade then
						cardchosen = equip:getEffectiveId()
						break
					end
				end
				if cardchosen then
					addTarget(enemy, cardchosen)
				end
			end
		end
		for _, enemy in ipairs(enemies) do
			local cards = sgs.QList2Table(enemy:getHandcards())
			local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
			if #cards == 1 and not self:doNotDiscard(enemy, "h") and self:hasTrickEffective(card, enemy) then
				for _, cc in ipairs(cards) do
					if (cc:hasFlag("visible") or cc:hasFlag(flag)) and (cc:getSuit() == sgs.Card_Spade) then
						addTarget(enemy, self:getCardRandomly(enemy, "h"))
					end
				end
			end
		end
	end

	for _, enemy in ipairs(enemies) do	--拆危险
		if not enemy:isNude() and (self:hasTrickEffective(card, enemy) or isSkillCard) and enemy:hasSkill("luahuapu") and enemy:getHandcardNum() > 2 then
			if not self:doNotDiscard(enemy, "h") and self:hasTrickEffective(card, enemy) then
				addTarget(enemy, self:getCardRandomly(enemy, "h"))
			end
		end
	end

	for _, enemy in ipairs(enemies) do	--拆危险
		if not enemy:isNude() and (self:hasTrickEffective(card, enemy) or isSkillCard)
				and not enemy:containsTrick("gainb") then --有梅莉莉罩我你拆迁办不管用 啊
			local dangerous = self:getDangerousCard(enemy)
			if dangerous and (not isDiscard or self.player:canDiscard(enemy, dangerous)) then
				if addTarget(enemy, dangerous) then return end
			end
		end
	end

	if (self.player:hasSkill("luafenxing") and (self.player:getHandcardNum() <= 2) and (self:getOverflow() <= 0)) and isDiscard then
		return
	end

	self:sort(self.friends_noself, "defense")	--拆乐/兵
	local friends = self:exclude(self.friends_noself, card)
	if not isSkillCard and not using_2013 then
		for _, friend in ipairs(friends) do
			if (friend:containsTrick("indulgence") or friend:containsTrick("supply_shortage")) and not friend:containsTrick("YanxiaoCard") and not friend:containsTrick("gainb")
					and (self:hasTrickEffective(card, friend) or isSkillCard) then
				local cardchosen
				tricks = friend:getJudgingArea()
				for _, trick in sgs.qlist(tricks) do
					if trick:isKindOf("Indulgence") and (not isDiscard or self.player:canDiscard(friend, trick:getId())) then
						if friend:getHp() <= friend:getHandcardNum()
						--or friend:isLord() or name == "snatch" 	--yun
						then cardchosen = trick:getEffectiveId()
							break
						end
					end
					if not cardchosen and trick:isKindOf("SupplyShortage") and (not isDiscard or self.player:canDiscard(friend, trick:getId())) then	--yun
						cardchosen = trick:getEffectiveId()
						break
					end
					if not cardchosen and trick:isKindOf("Indulgence") and (not isDiscard or self.player:canDiscard(friend, trick:getId())) then	--yun
						cardchosen = trick:getEffectiveId()
						break
					end
				end
				if cardchosen then
					if addTarget(friend, cardchosen) then return end
				end
			end
		end
	end

	if not isSkillCard and not using_2013 then  --拆宴会
		for _, enemy in ipairs(enemies) do
			if (enemy:containsTrick("banquet")) and (self:hasTrickEffective(card, enemy) or isSkillCard) then
				local cardchosen
				tricks = enemy:getJudgingArea()
				for _, trick in sgs.qlist(tricks) do
					if trick:isKindOf("Banquet") and (not isDiscard or self.player:canDiscard(enemy, trick:getId())) then
						cardchosen = trick:getEffectiveId()
					end
				end
				if addTarget(enemy, cardchosen) then return end
			end
		end
	end

	if card and card:isRed() and self.player:hasSkill("luayewang") and self.player:isWounded()
		and card:isKindOf("Dismantlement") then
		local has_redslash = false
		for _,card_0 in sgs.qlist(self.player:getHandcards()) do
			if card_0:isKindOf("Slash") and card_0:isRed() then has_redslash = true end
		end
		if not has_redslash then
			return
		end
	end

	if not isSkillCard and not using_2013 then  --拆伪境
		for _, friend in ipairs(friends) do
			if (friend:containsTrick("gaina")) and (self:hasTrickEffective(card, friend) or isSkillCard) then
				local cardchosen
				tricks = friend:getJudgingArea()
				for _, trick in sgs.qlist(tricks) do
					if trick:isKindOf("gaina") and (not isDiscard or self.player:canDiscard(friend, trick:getId())) then
						cardchosen = trick:getEffectiveId()
					end
				end
				if addTarget(friend, cardchosen) then return end
			end
		end
	end

	local new_enemies = table.copyFrom(enemies)
	local compare_JudgingArea = function(a, b)
		return a:getJudgingArea():length() > b:getJudgingArea():length()
	end
	table.sort(new_enemies, compare_JudgingArea)
	local yanxiao_card, yanxiao_target, yanxiao_prior
	if not isSkillCard and not using_2013 then
		for _, enemy in ipairs(new_enemies) do
			for _, acard in sgs.qlist(enemy:getJudgingArea()) do
				if (acard:isKindOf("YanxiaoCard")) and (not isDiscard or self.player:canDiscard(enemy, acard:getId())) and (self:hasTrickEffective(card, enemy) or isSkillCard) then
					yanxiao_card = acard
					yanxiao_target = enemy
					if enemy:containsTrick("indulgence") or enemy:containsTrick("supply_shortage") then yanxiao_prior = true end	--拆优先言笑
					break
				end
			end
			if yanxiao_card and yanxiao_target then break end
		end
		if yanxiao_prior and yanxiao_card and yanxiao_target then
			if addTarget(yanxiao_target, yanxiao_card:getEffectiveId()) then return end
		end
	end

	if self:slashIsAvailable() then	--杀人
		local dummyuse = { isDummy = true, to = sgs.SPlayerList() }
		self:useCardSlash(sgs.Sanguosha:cloneCard("slash"), dummyuse)
		if not dummyuse.to:isEmpty() then
			local tos = self:exclude(dummyuse.to, card)
			for _, to in ipairs(tos) do
				if to:getHandcardNum() == 1 and to:getHp() <= 2 and not self:doNotDiscard(to, "h") and not self:hasSkills("kongcheng|tianming", to)
						and (self:hasTrickEffective(card, to) or isSkillCard)
						and not to:containsTrick("gainb")
						and (not self:hasEightDiagramEffect(to) or IgnoreArmor(self.player, to))
						and not self:hasSheyanEffect(to, card) then
					if addTarget(to, to:getRandomHandCardId()) then return end
				end
			end
		end
	end


	for _, enemy in ipairs(enemies) do	--拆有用装备
		if not enemy:isNude() and (self:hasTrickEffective(card, enemy) or isSkillCard) and not enemy:containsTrick("gainb") then
			local valuable = self:getValuableCard(enemy)
			if valuable and (not isDiscard or self.player:canDiscard(enemy, valuable)) and not self:doNotDiscard(enemy, "e") then
				if addTarget(enemy, valuable) then return end
			end
		end
	end
	if card:getSkillName() == "luatongxin" then return end
	if card:getSkillName() == "lualindong" then return end
	if card:getSkillName() == "luazhishu" then return end
	if card:getSkillName() == "luajiance" then return end
	if self.player:hasSkill("luachenti") and self.player:getHandcardNum() == 2 and self:getOverflow() < 1 then return end
	if not isSkillCard and isDiscard and self.player:hasSkill("lualinglan") and card:getSuit() == sgs.Card_Club then return end
	if not isSkillCard and isDiscard and self.player:hasSkill("luabeihuan") then return end

	if not isSkillCard and not using_2013 then
		for _, enemy in ipairs(enemies) do
			if enemy:getMark("@LuaBisha2") == 1 and (self:hasTrickEffective(card, enemy) or isSkillCard) and self.player:hasSkill("LuaBisha")
					and self:isEnemy(enemy) and not self.player:hasFlag("LuaBishaX") then  --三 步 必 杀
				local valuable = self:getValuableCard(enemy)
				if valuable and (not isDiscard or self.player:canDiscard(enemy, valuable)) and not self:doNotDiscard(enemy, "e") then
					if addTarget(enemy, valuable) then return end
				else
					if addTarget(enemy, enemy:getRandomHandCardId()) then return end
				end
			end
		end
	end

	local wuguotai = self.room:findPlayerBySkillName("buyi")	--yun	拆最后手牌
	for _, enemy in ipairs(enemies) do
		if enemy:getHandcardNum() == 1 and not self:doNotDiscard(enemy, "h") and (self:hasTrickEffective(card, enemy) or isSkillCard)	--yun 
				and (not isDiscard or self.player:canDiscard(enemy, "h")) and not enemy:containsTrick("gainb") then
			if enemy:hasSkills(sgs.dont_kongcheng_skill) or (wuguotai and self:isFriend(enemy, wuguotai) and self:isWeak(enemy))
					and not self:hasSheyanEffect(enemy, card) and not BetterNot(enemy) then
				local do_not_hui = false
				local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
				for _, cc in sgs.qlist(enemy:getHandcards()) do
					if (cc:hasFlag("visible") or cc:hasFlag(flag)) and (cc:isKindOf("Hui")) then
						do_not_hui = true
					end
				end
				if not do_not_hui and addTarget(enemy, self:getCardRandomly(enemy, "h")) then return end
			end
		end
	end
	for _, enemy in ipairs(enemies) do
		if (self.player:hasSkill("Luayuelong") and card and card:isBlack() and (not ((self:YouMu2(enemy, true) and self:isWeak(enemy)) or (self:getOverflow() > 0)))
				and card:getSkillName() ~= "Luayuelong" and not use.shoutu) then return end -- pay 兔子
	end
	local hasLion, target	--拆狮子
	for _, friend in ipairs(friends) do
		if (self:hasTrickEffective(card, friend) or isSkillCard) and self:needToThrowArmor(friend) and (not isDiscard or self.player:canDiscard(friend, friend:getArmor():getEffectiveId()))
				and not self.player:containsTrick("gainb") then
			hasLion = true
			target = friend
		end
	end
	if self.player:hasSkill("luaqiuwen") and isDiscard then
		return
	end 
	if self.player:hasSkill("luatanmi") and (self:getOverflow() <= 0) and not self.player:hasFlag("luatanmi2") and isDiscard then return end
	if self.player:hasSkill("lualushou") and isDiscard then return end
	if not isSkillCard and isDiscard and self.player:hasSkill("luashuangfeng") then return end
	if hasLion and (not isDiscard or self.player:canDiscard(target, target:getArmor():getEffectiveId())) and (self:hasTrickEffective(card, target) or isSkillCard) then
		if addTarget(target, target:getArmor():getEffectiveId()) then return end
	end

	if self.player:hasSkill("luabenwo") then -- pay 恋恋
		for _, enemy in ipairs(enemies) do
			local equips = enemy:getEquips()
			if not equips:isEmpty() and not self:doNotDiscard(enemy, "e") and (not enemy:isChained()) and (self:hasTrickEffective(card, enemy) or isSkillCard) then
				local cardchosen
				if enemy:getDefensiveHorse() and (not isDiscard or self.player:canDiscard(enemy, enemy:getDefensiveHorse():getEffectiveId())) then
					cardchosen = enemy:getDefensiveHorse():getEffectiveId()
				elseif enemy:getArmor() and not self:needToThrowArmor(enemy) and (not isDiscard or self.player:canDiscard(enemy, enemy:getArmor():getEffectiveId())) then
					cardchosen = enemy:getArmor():getEffectiveId()
				elseif enemy:getOffensiveHorse() and (not isDiscard or self.player:canDiscard(enemy, enemy:getOffensiveHorse():getEffectiveId())) then
					cardchosen = enemy:getOffensiveHorse():getEffectiveId()
				elseif enemy:getWeapon() and (not isDiscard or self.player:canDiscard(enemy, enemy:getWeapon():getEffectiveId())) then
					cardchosen = enemy:getWeapon():getEffectiveId()
				end
				if cardchosen then
					if addTarget(enemy, cardchosen) then return end
				end
			end
			if (not enemy:inMyAttackRange(self.player)) and (not enemy:isChained()) and not self:doNotDiscard(enemy, "h") and (self:hasTrickEffective(card, enemy) or isSkillCard)
					and (not isDiscard or self.player:canDiscard(enemy, "h")) and (enemy:getHandcardNum() > 0) then
				if enemy:hasSkills(sgs.dont_kongcheng_skill) or (wuguotai and self:isFriend(enemy, wuguotai) and self:isWeak(enemy)) then
					if addTarget(enemy, self:getCardRandomly(enemy, "h")) then return end
				end
			end

		end
	end

	for _, enemy in ipairs(enemies) do	--拆集火防御
		if not enemy:isNude() and (self:hasTrickEffective(card, enemy) or isSkillCard) and not enemy:containsTrick("gainb") then
			if enemy:hasSkills(sgs.concentrated_skill) and not BetterNot(enemy) then	--yun
				local cardchosen
				local equips = { enemy:getDefensiveHorse(), enemy:getArmor(), enemy:getOffensiveHorse(), enemy:getWeapon(),enemy:getTreasure() }
				for _, equip in ipairs(equips) do
					if equip and (not enemy:hasSkill("jijiu") or equip:isRed()) and (not isDiscard or self.player:canDiscard(enemy, equip:getEffectiveId())) then
						cardchosen = equip:getEffectiveId()
						break
					end
				end

				if not cardchosen and enemy:getDefensiveHorse() and (not isDiscard or self.player:canDiscard(enemy, enemy:getDefensiveHorse():getEffectiveId())) then cardchosen = enemy:getDefensiveHorse():getEffectiveId() end
				if not cardchosen and enemy:getArmor() and not self:needToThrowArmor(enemy) and (not isDiscard or self.player:canDiscard(enemy, enemy:getArmor():getEffectiveId())) then
					cardchosen = enemy:getArmor():getEffectiveId()
				end

				if cardchosen then
					if addTarget(enemy, cardchosen) then return end
				end
			end
		end
	end

	for _, enemy in ipairs(enemies) do	--拆桃酒
		local cards = sgs.QList2Table(enemy:getHandcards())
		local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
		if #cards <= 2 and not enemy:isKongcheng() and not self:doNotDiscard(enemy, "h", true) and (self:hasTrickEffective(card, enemy) or isSkillCard)
				and not enemy:containsTrick("gainb") and not BetterNot(enemy) then
			for _, cc in ipairs(cards) do
				if (cc:hasFlag("visible") or cc:hasFlag(flag)) and (cc:isKindOf("Peach") or cc:isKindOf("Analeptic")) then
					if addTarget(enemy, self:getCardRandomly(enemy, "h")) then return end
				end
			end
		end
	end



	for _, enemy in ipairs(enemies) do	--拆八卦
		if enemy:hasArmorEffect("eight_diagram") and enemy:getArmor() and not self:needToThrowArmor(enemy) and not enemy:containsTrick("gainb")
				and (not isDiscard or self.player:canDiscard(enemy, enemy:getArmor():getEffectiveId())) and (self:hasTrickEffective(card, enemy) or isSkillCard) then
			addTarget(enemy, enemy:getArmor():getEffectiveId())
		end
	end
	if self.player:hasSkill("luawangyue") then
		for _, enemy in ipairs(enemies) do	--拆装备
			if enemy:hasEquip() and enemy:getEquips():length() == 1 and not self:doNotDiscard(enemy, "e") and (self:hasTrickEffective(card, enemy) or isSkillCard)
					and not self:hasSheyanEffect(enemy, card) and not enemy:containsTrick("gainb") and not BetterNot(enemy) then
				local cardchosen
				if enemy:getDefensiveHorse() and (not isDiscard or self.player:canDiscard(enemy, enemy:getDefensiveHorse():getEffectiveId())) then
					cardchosen = enemy:getDefensiveHorse():getEffectiveId()
				elseif enemy:getArmor() and not self:needToThrowArmor(enemy) and (not isDiscard or self.player:canDiscard(enemy, enemy:getArmor():getEffectiveId())) then
					cardchosen = enemy:getArmor():getEffectiveId()
				elseif enemy:getOffensiveHorse() and (not isDiscard or self.player:canDiscard(enemy, enemy:getOffensiveHorse():getEffectiveId())) then
					cardchosen = enemy:getOffensiveHorse():getEffectiveId()
				elseif enemy:getWeapon() and (not isDiscard or self.player:canDiscard(enemy, enemy:getWeapon():getEffectiveId())) then
					cardchosen = enemy:getWeapon():getEffectiveId()
				end
				if cardchosen then
					if addTarget(enemy, cardchosen) then return end
				end
			end
		end
	end
	for _, enemy in ipairs(self.enemies) do  --拆伪境
		for _, acard in sgs.qlist(enemy:getJudgingArea()) do
			if (acard:isKindOf("gainb")) and (not isDiscard or self.player:canDiscard(enemy, acard:getId())) and (self:hasTrickEffective(card, enemy) or isSkillCard) then
				if addTarget(enemy, acard:getEffectiveId()) then return end
			end
		end
	end


	if self.player:getHp() >= 2 and #self.enemies > 1 and self.player:hasSkill("luamaoyou") and self:getOverflow() <= 0
			and not self.player:hasSkill("luashidan") and self.player:getMark("@luamaoyou") >= 1 and (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) then return end


	if not isSkillCard and not using_2013 then  --拆闪光
		for _, enemy in ipairs(enemies) do
			if (enemy:containsTrick("light")) and (self:hasTrickEffective(card, enemy) or isSkillCard) then
				local cardchosen
				tricks = enemy:getJudgingArea()
				for _, trick in sgs.qlist(tricks) do
					if trick:isKindOf("light") and (not isDiscard or self.player:canDiscard(enemy, trick:getId())) then
						cardchosen = trick:getEffectiveId()
					end
				end
				if addTarget(enemy, cardchosen) then return end
			end
		end
	end

	if card:getSkillName() == "luanianli" and self.player:getPile("luanianli"):length() <= 1 then
		return
	end


	if not isSkillCard and not using_2013	--拆言笑
			and yanxiao_card and yanxiao_target and (not isDiscard or self.player:canDiscard(yanxiao_target, yanxiao_card:getId())) then
		if addTarget(yanxiao_target, yanxiao_card:getEffectiveId()) then return end
	end

	if self.player:hasSkill("luahuapu") and isDiscard and card and card:getSuit() == sgs.Card_Club then
		return
	end

	for _, zhugeliang in ipairs(friends) do	--yun	帮助空城
		if zhugeliang:hasSkill("kongcheng") and zhugeliang:getHandcardNum() == 1 and self:getEnemyNumBySeat(self.player, zhugeliang) > 0
				and self:isWeak(zhugeliang) and (not isDiscard or self.player:canDiscard(zhugeliang, "h")) and not zhugeliang:containsTrick("gainb")
				and (self:hasTrickEffective(card, zhugeliang) or isSkillCard) then
			if addTarget(zhugeliang, self:getCardRandomly(zhugeliang, "h")) then return end
		end
	end

	if (not isSkillCard) and isDiscard and card:isBlack() and self.player:hasSkill("luelangchao")
			and (not self.player:hasUsed("#luelangchao")) then return end

	for _, enemy in ipairs(enemies) do	--拆需要手牌的人
		if not enemy:isKongcheng() and not self:doNotDiscard(enemy, "h") and (self:hasTrickEffective(card, enemy) or isSkillCard) and not enemy:containsTrick("gainb")
				and enemy:hasSkills(sgs.cardneed_skill) and (not isDiscard or self.player:canDiscard(enemy, "h"))
				and not self:hasSheyanEffect(enemy, card) then
			if addTarget(enemy, self:getCardRandomly(enemy, "h")) then return end
		end
	end

	if not isSkillCard and not using_2013 then  --拆望梅止渴
		for _, enemy in ipairs(enemies) do
			if (enemy:containsTrick("need_maribel")) and (self:hasTrickEffective(card, enemy) or isSkillCard) then
				local cardchosen
				tricks = enemy:getJudgingArea()
				for _, trick in sgs.qlist(tricks) do
					if trick:isKindOf("NeedMaribel") and (not isDiscard or self.player:canDiscard(enemy, trick:getId())) then
						cardchosen = trick:getEffectiveId()
					end
				end
				if addTarget(enemy, cardchosen) then return end
			end
		end
	end

	for _, enemy in ipairs(enemies) do	--拆装备
		if enemy:hasEquip() and not self:doNotDiscard(enemy, "e") and (self:hasTrickEffective(card, enemy) or isSkillCard)
				and not self:hasSheyanEffect(enemy, card) and not enemy:containsTrick("gainb") and not BetterNot(enemy) then
			local cardchosen
			if enemy:getDefensiveHorse() and (not isDiscard or self.player:canDiscard(enemy, enemy:getDefensiveHorse():getEffectiveId())) then
				cardchosen = enemy:getDefensiveHorse():getEffectiveId()
			elseif enemy:getArmor() and not self:needToThrowArmor(enemy) and (not isDiscard or self.player:canDiscard(enemy, enemy:getArmor():getEffectiveId())) then
				cardchosen = enemy:getArmor():getEffectiveId()
			elseif enemy:getOffensiveHorse() and (not isDiscard or self.player:canDiscard(enemy, enemy:getOffensiveHorse():getEffectiveId())) then
				cardchosen = enemy:getOffensiveHorse():getEffectiveId()
			elseif enemy:getWeapon() and (not isDiscard or self.player:canDiscard(enemy, enemy:getWeapon():getEffectiveId())) then
				cardchosen = enemy:getWeapon():getEffectiveId()
			end
			if cardchosen then
				if addTarget(enemy, cardchosen) then return end
			end
		end
	end

	if name == "snatch" or self:getOverflow() > 0 or self.player:hasFlag("luajianjiQ") then	--随便拆
		for _, enemy in ipairs(enemies) do
			local equips = enemy:getEquips()
			if not enemy:isNude() and not self:doNotDiscard(enemy, "he") and (self:hasTrickEffective(card, enemy) or isSkillCard)
					and not self:hasSheyanEffect(enemy, card) and not BetterNot(enemy) then
				local cardchosen
				if not equips:isEmpty() and not self:doNotDiscard(enemy, "e") then
					cardchosen = self:getCardRandomly(enemy, "e")
				else
					cardchosen = self:getCardRandomly(enemy, "h") end
				if cardchosen then
					if addTarget(enemy, cardchosen) then return end
				end
			end
		end
		if self.player:getRole() == "loyalist" or self.player:isLord() then
			for _,p in sgs.qlist(self.room:getAlivePlayers()) do
				if not self:isFriend(p) and not self:isEnemy(p) then
					local equips = p:getEquips()
					if not p:isNude() and not self:doNotDiscard(p, "he") and (self:hasTrickEffective(card, p) or isSkillCard)
							and not self:hasSheyanEffect(p, card) and not BetterNot(p) then
						local cardchosen
						if not equips:isEmpty() and not self:doNotDiscard(p, "e") then
							cardchosen = self:getCardRandomly(p, "e")
						else
							cardchosen = self:getCardRandomly(p, "h") end
						if cardchosen then
							if addTarget(p, cardchosen) then return end
						end
					end
				end
			end
		end
	end

	for _, enemy in ipairs(enemies) do	--拆装备
		if enemy:hasEquip() and not self:doNotDiscard(enemy, "e") and (self:hasTrickEffective(card, enemy) or isSkillCard)
				and not self:hasSheyanEffect(enemy, card) and not enemy:containsTrick("gainb") then
			local cardchosen
			if enemy:getDefensiveHorse() and (not isDiscard or self.player:canDiscard(enemy, enemy:getDefensiveHorse():getEffectiveId())) then
				cardchosen = enemy:getDefensiveHorse():getEffectiveId()
			elseif enemy:getArmor() and not self:needToThrowArmor(enemy) and (not isDiscard or self.player:canDiscard(enemy, enemy:getArmor():getEffectiveId())) then
				cardchosen = enemy:getArmor():getEffectiveId()
			elseif enemy:getOffensiveHorse() and (not isDiscard or self.player:canDiscard(enemy, enemy:getOffensiveHorse():getEffectiveId())) then
				cardchosen = enemy:getOffensiveHorse():getEffectiveId()
			elseif enemy:getWeapon() and (not isDiscard or self.player:canDiscard(enemy, enemy:getWeapon():getEffectiveId())) then
				cardchosen = enemy:getWeapon():getEffectiveId()
			end
			if cardchosen then
				if addTarget(enemy, cardchosen) then return end
			end
		end
	end

	if name == "snatch" or self:getOverflow() > 0 then	--随便拆
		for _, enemy in ipairs(enemies) do
			local equips = enemy:getEquips()
			if not enemy:isNude() and not self:doNotDiscard(enemy, "he") and (self:hasTrickEffective(card, enemy) or isSkillCard)
					and not self:hasSheyanEffect(enemy, card) then
				local cardchosen
				if not equips:isEmpty() and not self:doNotDiscard(enemy, "e") then
					cardchosen = self:getCardRandomly(enemy, "e")
				else
					cardchosen = self:getCardRandomly(enemy, "h") end
				if cardchosen then
					if addTarget(enemy, cardchosen) then return end
				end
			end
		end
		if self.player:getRole() == "loyalist" or self.player:isLord() then
			for _,p in sgs.qlist(self.room:getAlivePlayers()) do
				if not self:isFriend(p) and not self:isEnemy(p) then
					local equips = p:getEquips()
					if not p:isNude() and not self:doNotDiscard(p, "he") and (self:hasTrickEffective(card, p) or isSkillCard)
							and not self:hasSheyanEffect(p, card) then
						local cardchosen
						if not equips:isEmpty() and not self:doNotDiscard(p, "e") then
							cardchosen = self:getCardRandomly(p, "e")
						else
							cardchosen = self:getCardRandomly(p, "h") end
						if cardchosen then
							if addTarget(p, cardchosen) then return end
						end
					end
				end
			end
		end
	end
end

SmartAI.useCardSnatch = SmartAI.useCardSnatchOrDismantlement

sgs.ai_use_value.Snatch = 9
sgs.ai_use_priority.Snatch = 4.3
sgs.ai_keep_value.Snatch = 3.46

sgs.dynamic_value.control_card.Snatch = true

SmartAI.useCardDismantlement = SmartAI.useCardSnatchOrDismantlement

sgs.ai_use_value.Dismantlement = 5.6
sgs.ai_use_priority.Dismantlement = 4.4
sgs.ai_keep_value.Dismantlement = 3.44

sgs.dynamic_value.control_card.Dismantlement = true

sgs.ai_choicemade_filter.cardChosen.snatch = function(self, player, promptlist)
	local from = findPlayerByObjectName(self.room, promptlist[4])
	local to = findPlayerByObjectName(self.room, promptlist[5])
	if from and to then
		local id = tonumber(promptlist[3])
		local place = self.room:getCardPlace(id)
		local card = sgs.Sanguosha:getCard(id)
		local intention = 70
		if to:hasFlag("jili") then to:setFlags("-jili") intention = 0 end	--yun
		if to:hasSkills("tuntian+zaoxian") and to:getPile("field") == 2 and to:getMark("zaoxian") == 0 then intention = 0 end
		if place == sgs.Player_PlaceDelayedTrick then
			if not card:isKindOf("Disaster") then intention = -intention else intention = 0 end
			if card:isKindOf("YanxiaoCard") then intention = -intention end
			if card:isKindOf("gainb") or card:isKindOf("light") or card:isKindOf("xiehuia")
				or card:isKindOf("Banquet") or card:isKindOf("NeedMaribel") then intention = -intention end
		elseif place == sgs.Player_PlaceEquip then
			if card:isKindOf("Armor") and self:evaluateArmor(card, to) <= -2 then intention = 0 end
			if card:isKindOf("SilverLion") then
				if to:getLostHp() > 1 then
					if to:hasSkills(sgs.use_lion_skill) then
						intention = self:willSkipPlayPhase(to) and -intention or 0
					else
						intention = self:isWeak(to) and -intention or 0
					end
				else
					intention = 0
				end
			elseif to:hasSkills(sgs.lose_equip_skill) then
				if self:isWeak(to) and (card:isKindOf("DefensiveHorse") or card:isKindOf("Armor")) then
					intention = math.abs(intention)
				else
					intention = 0
				end
			end
			if promptlist[2] == "snatch" and (card:isKindOf("OffensiveHorse") or card:isKindOf("Weapon")) and self:isFriend(from, to) then
				local canAttack
				for _, p in sgs.qlist(self.room:getOtherPlayers(from)) do
					if from:inMyAttackRange(p) and self:isEnemy(p, from) then canAttack = true break end
				end
				if not canAttack then intention = 0 end
			end
		elseif place == sgs.Player_PlaceHand then
			if self:needKongcheng(to, true) and to:getHandcardNum() == 1 then
				intention = 0
			end
		end
		sgs.updateIntention(from, to, intention)
	end
end

sgs.ai_choicemade_filter.cardChosen.dismantlement = sgs.ai_choicemade_filter.cardChosen.snatch

function SmartAI:useCardCollateral(card, use)
	if self.player:hasSkill("noswuyan") then return end
	local fromList = sgs.QList2Table(self.room:getOtherPlayers(self.player))
	local toList   = sgs.QList2Table(self.room:getAlivePlayers())

	local cmp = function(a, b)
		local alevel = self:objectiveLevel(a)
		local blevel = self:objectiveLevel(b)

		if alevel ~= blevel then return alevel > blevel end

		local anum = getCardsNum("Slash", a)
		local bnum = getCardsNum("Slash", b)

		if anum ~= bnum then return anum < bnum end
		return a:getHandcardNum() < b:getHandcardNum()
	end

	table.sort(fromList, cmp)
	self:sort(toList, "defense")

	local needCrossbow = false
	for _, enemy in ipairs(self.enemies) do
		if self.player:canSlash(enemy) and self:objectiveLevel(enemy) > 3
			and sgs.isGoodTarget(enemy, self.enemies, self) and not self:slashProhibit(nil, enemy) then
			needCrossbow = true
			break
		end
	end

	needCrossbow = needCrossbow and self:getCardsNum("Slash") > 2 and not self.player:hasSkill("paoxiao") and not self.player:hasSkill("kuangcai")	--yun
	if self.player:hasSkill("luachenti") and self.player:getHandcardNum() == 2 and self:getOverflow() < 1 then return end
	if needCrossbow then
		for i = #fromList, 1, -1 do
			local friend = fromList[i]
			if (not use.current_targets or not table.contains(use.current_targets, friend:objectName()))
				and friend:getWeapon() and friend:getWeapon():isKindOf("Crossbow") and self:hasTrickEffective(card, friend) then
				for _, enemy in ipairs(toList) do
					if friend:canSlash(enemy, nil) and friend:objectName() ~= enemy:objectName() then
						if not use.isDummy then self.room:setPlayerFlag(self.player, "needCrossbow") end
						use.card = card
						if use.to then use.to:append(friend) end
						if use.to then use.to:append(enemy) end
						return
					end
				end
			end
		end
	end

	local n = nil
	local final_enemy = nil
	for _, enemy in ipairs(fromList) do
		if (not use.current_targets or not table.contains(use.current_targets, enemy:objectName()))
			and self:hasTrickEffective(card, enemy)
			and not self:hasSkills(sgs.lose_equip_skill, enemy)
			and not (enemy:hasSkill("weimu") and card:isBlack())
			and not (enemy:hasSkill("xiemu") and card:isBlack() and enemy:getMark("@xiemu_" .. self.player:getKingdom()) > 0)
			and not (enemy:hasSkill("tuntian") and enemy:hasSkill("zaoxian"))
			and self:objectiveLevel(enemy) >= 0
			and enemy:getWeapon() then

			for _, enemy2 in ipairs(toList) do
				if enemy:canSlash(enemy2) and self:objectiveLevel(enemy2) > 3 and enemy:objectName() ~= enemy2:objectName() then
					n = 1
					final_enemy = enemy2
					break
				end
			end

			if not n then
				for _, enemy2 in ipairs(toList) do
					if enemy:canSlash(enemy2) and self:objectiveLevel(enemy2) <=3 and self:objectiveLevel(enemy2) >=0 and enemy:objectName() ~= enemy2:objectName() then
						n = 1
						final_enemy = enemy2
						break
					end
				end
			end

			if not n then
				for _, friend in ipairs(toList) do
					if enemy:canSlash(friend) and self:objectiveLevel(friend) < 0 and enemy:objectName() ~= friend:objectName()
							and (self:needToLoseHp(friend, enemy, true, true) or self:getDamagedEffects(friend, enemy, true)) then
						n = 1
						final_enemy = friend
						break
					end
				end
			end

			if not n then
				for _, friend in ipairs(toList) do
					if enemy:canSlash(friend) and self:objectiveLevel(friend) < 0 and enemy:objectName() ~= friend:objectName()
							and (getKnownCard(friend, self.player, "Jink", true, "he") >= 2 or getCardsNum("Slash", enemy) < 1) then
						n = 1
						final_enemy = friend
						break
					end
				end
			end

			if n then
				use.card = card
				if use.to then use.to:append(enemy) end
				if use.to then use.to:append(final_enemy) end
				return
			end
		end
		n = nil
	end

	for _, friend in ipairs(fromList) do
		if (not use.current_targets or not table.contains(use.current_targets, friend:objectName()))
			and friend:getWeapon() and (getKnownCard(friend, self.player, "Slash", true, "he") > 0 or getCardsNum("Slash", friend) > 1 and friend:getHandcardNum() >= 4)
			and self:hasTrickEffective(card, friend)
			and self:objectiveLevel(friend) < 0
			and not self.room:isProhibited(self.player, friend, card) then

			for _, enemy in ipairs(toList) do
				if friend:canSlash(enemy, nil) and self:objectiveLevel(enemy) > 3 and friend:objectName() ~= enemy:objectName()
						and sgs.isGoodTarget(enemy, self.enemies, self) and not self:slashProhibit(nil, enemy) then
					use.card = card
					if use.to then use.to:append(friend) end
					if use.to then use.to:append(enemy) end
					return
				end
			end
		end
	end

	self:sortEnemies(toList)

	for _, friend in ipairs(fromList) do
		if (not use.current_targets or not table.contains(use.current_targets, friend:objectName()))
			and friend:getWeapon() and friend:hasSkills(sgs.lose_equip_skill)
			and self:hasTrickEffective(card, friend)
			and self:objectiveLevel(friend) < 0
			and not (friend:getWeapon():isKindOf("Crossbow") and getCardsNum("Slash", friend) > 1)
			and not self.room:isProhibited(self.player, friend, card) then

			for _, enemy in ipairs(toList) do
				if friend:canSlash(enemy, nil) and friend:objectName() ~= enemy:objectName() then
					use.card = card
					if use.to then use.to:append(friend) end
					if use.to then use.to:append(enemy) end
					return
				end
			end
		end
	end
end

sgs.ai_use_value.Collateral = 5.8
sgs.ai_use_priority.Collateral = 2.75
sgs.ai_keep_value.Collateral = 3.40

sgs.ai_card_intention.Collateral = function(self,card, from, tos)
	-- assert(#tos == 1)
	if card:hasFlag("lualiuzhi") then return end
	sgs.ai_collateral = true
end

sgs.dynamic_value.control_card.Collateral = true

sgs.ai_skill_cardask["collateral-slash"] = function(self, data, pattern, target2, target, prompt)
	-- self.player = killer
	-- target = user
	-- target2 = victim
	if self:isFriend(target) and (target:hasFlag("needCrossbow") or
			(getCardsNum("Slash", target, self.player) >= 2 and self.player:getWeapon():isKindOf("Crossbow"))) then
		if target:hasFlag("needCrossbow") then self.room:setPlayerFlag(target, "-needCrossbow") end
		return "."
	end
	if self:isFriend(target) and target:hasSkill("luayigong") and target:getPhase() == sgs.Player_Play and target:usedTimes("#luayigong") < 2 then
		return "."
	end
	if self:isFriend(target2) and self:needLeiji(target2, self.player) then
		for _, slash in ipairs(self:getCards("Slash")) do
			if self:slashIsEffective(slash, target2) then
				return slash:toString()
			end
		end
	end

	if target2 and (self:getDamagedEffects(target2, self.player, true) or self:needToLoseHp(target2, self.player, true)) then
		for _, slash in ipairs(self:getCards("Slash")) do
			if self:slashIsEffective(slash, target2) and self:isFriend(target2) then
				return slash:toString()
			end
			if not self:slashIsEffective(slash, target2, self.player, true) and self:isEnemy(target2) then
				return slash:toString()
			end
		end
		for _, slash in ipairs(self:getCards("Slash")) do
			if not self:getDamagedEffects(target2, self.player, true) and self:isEnemy(target2) then
				return slash:toString()
			end
		end
	end


	if target2 and not self:hasSkills(sgs.lose_equip_skill) and self:isEnemy(target2) then
		for _, slash in ipairs(self:getCards("Slash")) do
			if self:slashIsEffective(slash, target2) then
				return slash:toString()
			end
		end
	end
	if target2 and not self:hasSkills(sgs.lose_equip_skill) and self:isFriend(target2) then
		for _, slash in ipairs(self:getCards("Slash")) do
			if not self:slashIsEffective(slash, target2) then
				return slash:toString()
			end
		end
		for _, slash in ipairs(self:getCards("Slash")) do
			if (target2:getHp() > 3 and not self:canHit(target2, self.player, self:hasHeavySlashDamage(self.player, slash, target2)))	--yun
					and target2:getRole() ~= "lord" and self.player:getHandcardNum() > 1 then
				return slash:toString()
			end
			if self:needToLoseHp(target2, self.player) then return slash:toString() end
		end
	end
	self:speak("collateral", self.player:isFemale())
	return "."
end

local function hp_subtract_handcard(a,b)
	local diff1 = a:getHp() - a:getHandcardNum()
	local diff2 = b:getHp() - b:getHandcardNum()

	return diff1 < diff2
end

function SmartAI:enemiesContainsTrick(EnemyCount)
	local trick_all, possible_indul_enemy, possible_ss_enemy = 0, 0, 0
	local indul_num = self:getCardsNum("Indulgence")
	local ss_num = self:getCardsNum("SupplyShortage")
	local enemy_num, temp_enemy = 0

	local zhanghe = self.room:findPlayerBySkillName("qiaobian")
	if zhanghe and (not self:isEnemy(zhanghe) or zhanghe:isKongcheng() or not zhanghe:faceUp()) then zhanghe = nil end

	if self.player:hasSkill("guose") then
		for _, acard in sgs.qlist(self.player:getCards("he")) do
			if acard:getSuit() == sgs.Card_Diamond then indul_num = indul_num + 1 end
		end
	end

	if self.player:hasSkill("duanliang") then
		for _, acard in sgs.qlist(self.player:getCards("he")) do
			if acard:isBlack() then ss_num = ss_num + 1 end
		end
	end

	for _, enemy in ipairs(self.enemies) do
		if not enemy:containsTrick("YanxiaoCard") then
			if enemy:containsTrick("indulgence") then
				if not enemy:hasSkills("keji|conghui") and  (not zhanghe or self:playerGetRound(enemy) >= self:playerGetRound(zhanghe)) then
					trick_all = trick_all + 1
					if not temp_enemy or temp_enemy:objectName() ~= enemy:objectName() then
						enemy_num = enemy_num + 1
						temp_enemy = enemy
					end
				end
			else
				possible_indul_enemy = possible_indul_enemy + 1
			end
			if self.player:distanceTo(enemy) == 1 or self.player:hasSkill("duanliang") and self.player:distanceTo(enemy) <= 2 then
				if enemy:containsTrick("supply_shortage") then
					if not self:hasSkills("shensu|jisu", enemy) and (not zhanghe or self:playerGetRound(enemy) >= self:playerGetRound(zhanghe)) then
						trick_all = trick_all + 1
						if not temp_enemy or temp_enemy:objectName() ~= enemy:objectName() then
							enemy_num = enemy_num + 1
							temp_enemy = enemy
						end
					end
				else
					possible_ss_enemy  = possible_ss_enemy + 1
				end
			end
		end
	end
	indul_num = math.min(possible_indul_enemy, indul_num)
	ss_num = math.min(possible_ss_enemy, ss_num)
	if not EnemyCount then
		return trick_all + indul_num + ss_num
	else
		return enemy_num + indul_num + ss_num
	end
end

function SmartAI:playerGetRound(player, source)
	if not player then return self.room:writeToConsole(debug.traceback()) end
	source = source or self.room:getCurrent()
	if player:objectName() == source:objectName() then return 0 end
	local players_num = self.room:alivePlayerCount()
	local round = (player:getSeat() - source:getSeat()) % players_num
	return round
end

function SmartAI:getIndulgenceValue(enemy)
	local zhanghe = self.room:findPlayerBySkillName("qiaobian")
	local zhanghe_seat = zhanghe and zhanghe:faceUp() and not zhanghe:isKongcheng() and self:isEnemy(zhanghe) and zhanghe:getSeat() or 0	--yun

	local sb_daqiao = self.room:findPlayerBySkillName("yanxiao")
	local yanxiao = sb_daqiao and self:isEnemy(sb_daqiao) and sb_daqiao:faceUp() and	--yun
					(getKnownCard(sb_daqiao, self.player, "diamond", nil, "he") > 0
					or sb_daqiao:getHandcardNum() + self:ImitateResult_DrawNCards(sb_daqiao, sb_daqiao:getVisibleSkillList(true)) > 3
					or sb_daqiao:containsTrick("YanxiaoCard"))
					
	if enemy:containsTrick("indulgence") or enemy:containsTrick("YanxiaoCard") then return -100 end
	if enemy:hasSkill("qiaobian") and not enemy:containsTrick("supply_shortage") and not enemy:containsTrick("indulgence") then return -100 end
	if zhanghe_seat > 0 and (self:playerGetRound(zhanghe) <= self:playerGetRound(enemy) and self:enemiesContainsTrick() <= 1 or not enemy:faceUp()) then
		return -100 end
	if yanxiao and (self:playerGetRound(sb_daqiao) <= self:playerGetRound(enemy) and self:enemiesContainsTrick(true) <= 1 or not enemy:faceUp()) then
		return -100 end
	local value = enemy:getHandcardNum() - enemy:getMaxCards()
	if enemy:hasSkills("noslijian|lijian|fanjian|neofanjian|dimeng|jijiu|jieyin|anxu|yongsi|zhiheng|manjuan|nosrende|rende|qixi|jixi"..
		"mingce|yjieliang|yquanshi|kuangcai") then value = value + 10 end	--yun
	if enemy:hasSkill("dujin") then
		if enemy:getCards("e"):length() > 3 then value = value + 12
		elseif enemy:getCards("e"):length() > 1 then value = value + 10 
		else value = value + 5 
		end
	end  --luahongcai
	if enemy:hasSkill("yyuanlao") then value = value + 5 * enemy:getLostHp() end	
	if enemy:hasSkills("houyuan|qice|guose|nosguose|duanliang|yanxiao|nosjujian|luoshen|nosjizhi|jizhi|jilve|wansha|sizhan|yxiaoshou|yzhaoxiang") then value = value + 5 end
	if enemy:hasSkills("guzheng|luoying|xiliang|guixin|lihun|yinling|gongxin|shenfen|ganlu|jueji|zhenggong|tiaoxin|ytiaodou|shangyi|yjuedou|yjuezhan|yshuntian|tianyi|xianzhen|quhu") then value = value + 3 end
	if enemy:hasSkills("ynongquan") then value = value + 1 end		
	if enemy:getMark("@LuaBisha2") == 2 and (value > - 70) and self.player:hasSkill("LuaBisha") and not self.player:hasFlag("LuaBishaX") then value = value + 70 end   --pay
	if enemy:getMark("@LuaBisha2") == 1 and (value > - 70) and self.player:hasSkill("LuaBisha") and not self.player:hasFlag("LuaBishaX") then value = value + 25 end 
	if enemy:hasSkill("Luaxinwu") then value = value + 2 end
	if self:isWeak(enemy) then value = value + 3 end
	if enemy:isLord() then value = value + 3 end
	if (self.player:hasSkill("luabenwo") and not enemy:inMyAttackRange(self.player) and not enemy:isChained()) then value = value + 3 end
	if self:objectiveLevel(enemy) < 3 then value = value - 10 end
	if self:objectiveLevel(enemy) < 3 then value = value - 10 end
	if not enemy:faceUp() then value = value - 10 end
	
	if enemy:hasSkills("keji|shensu|conghui") then value = value - enemy:getHandcardNum() end
	if enemy:hasSkill("luashuangyue") then value = value - 10 end
	if enemy:hasSkills("lirang|longluo") then value = value - 5 end
	if enemy:hasSkills("luatiandu|nostuxi|tuxi|noszhenlie|guanxing|qinyin|zongshi|tiandu|xiaoguo|yyuanlv") then value = value - 3 end	--yun
	if enemy:hasSkills("conghui|luachuanming|luaouxiang") then value = value - 20 end
	if enemy:hasSkill("luaqunxing") then value = value - 10 end
	if self:needBear(enemy) then value = value - 20 end
	if not sgs.isGoodTarget(enemy, self.enemies, self) then value = value - 1 end
	if getKnownCard(enemy, self.player, "Dismantlement", true) > 0 then value = value + 2 end
	value = value + (self.room:alivePlayerCount() - self:playerGetRound(enemy)) / 2
	return value
end 
function SmartAI:useCardIndulgence(card, use)
	local enemies = {}

	if #self.enemies == 0 then
		if sgs.turncount <= 1 and self.role == "lord" and not sgs.isRolePredictable()
			and sgs.evaluatePlayerRole(self.player:getNextAlive()) == "neutral"
			and not (self.player:hasLordSkill("shichou") and self.player:getNextAlive():getKingdom() == "shu") then
			enemies = self:exclude({self.player:getNextAlive()}, card)
		end
	else
		enemies = self:exclude(self.enemies, card)
	end
	if (self.player:getRole() == "loyalist" or self.player:isLord()) and #enemies == 0 then
		for _,p in sgs.qlist(self.room:getAlivePlayers()) do
			if not self:isFriend(p) and not self:isEnemy(p) then
				if not self.room:isProhibited(self.player, p, card) then
					table.insert(enemies, p)
				end
			end
		end
	end

	if #enemies == 0 then return end

	local cmp = function(a,b)
		return self:getIndulgenceValue(a) > self:getIndulgenceValue(b)
	end

	table.sort(enemies, cmp)

	local target = enemies[1]
	if (self:getIndulgenceValue(target) <= 0) and (self.player:hasSkill("Luayuelong") and card and card:isBlack() and not ((self:YouMu2(target, true) and self:isWeak(target)) or (self:getOverflow() > 0))
				and card:getSkillName() ~= "Luayuelong" and not use.shoutu ) then return end -- pay 受兔
	if (self.player:hasSkill("luafenxing") and (self.player:getHandcardNum() <= 2) and (self:getIndulgenceValue(target) <= -15) and (self:getOverflow() <= 0)) then return end
	if self.player:hasSkill("luachenti") and self.player:getHandcardNum() == 2 and self:getOverflow() < 1 and (self:getIndulgenceValue(target) <= -15) then return end
	if self:getIndulgenceValue(target) > -100 then
		use.card = card
		if use.to then use.to:append(target) end
		return
	end
end

sgs.ai_use_value.Indulgence = 8
sgs.ai_use_priority.Indulgence = 0.5
sgs.ai_card_intention.Indulgence = 120
sgs.ai_keep_value.Indulgence = 3.5

sgs.dynamic_value.control_usecard.Indulgence = true

function SmartAI:HifuCard(card)
	if self.player:hasSkill("luatanmi") then
		if self.player:getPhase() == sgs.Player_Draw then return true end
		if self.player:getTag("luatanmi") and self.player:getTag("luatanmi"):toString() ~= "" then
			local uaqiuwen = self.player:getTag("luatanmi"):toString()
			uaqiuwen = uaqiuwen:split("|")
			if #uaqiuwen > 0 then
				if card:getTypeId() == sgs.Sanguosha:getCard(uaqiuwen[1]):getTypeId() then
					return true
				end
			end
		end
	end
end
function SmartAI:willUseLightning(card)
	if not card then self.room:writeToConsole(debug.traceback()) return false end
	if self.player:containsTrick("lightning") then return end
	if self.player:hasSkill("weimu") and card:isBlack() then return end
	if self.room:isProhibited(self.player, self.player, card) then return end

	local function hasDangerousFriend()
		local hashy = false
		for _, aplayer in ipairs(self.enemies) do
			if aplayer:hasSkill("hongyan") and #self.friends <= #self.enemies then hashy = true break end
		end
		for _, aplayer in ipairs(self.friends) do
			if aplayer:hasSkill("luachuanming") and #self.friends <= #self.enemies then hashy = true break end
		end
		for _, aplayer in ipairs(self.enemies) do
			if aplayer:hasSkill("guanxing") or (aplayer:hasSkill("gongxin") and hashy)
			or aplayer:hasSkill("xinzhan") or aplayer:hasSkill("luaqunxing") then
				if self:isFriend(aplayer:getNextAlive()) then return true end
			end
		end
		return false
	end

	if self:getFinalRetrial(self.player) == 2 then
		return
	elseif self:getFinalRetrial(self.player) == 1 then
		return true
	elseif not hasDangerousFriend() then
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return end
		end
		if self:ayalight(card) then return true end 
		local Megumu = self.room:findPlayerBySkillName("luahonggguang")
		if Megumu and self:isFriend(Megumu) then
			return true
		end 
		if card:isBlack() and self.player:hasSkill("luelangchao") then return end
		if self:HifuCard(card) then return true end
		if self.player:hasSkill("luabeihuan") and self.player:hasSkill("lualihe") then return end
		local players = self.room:getAllPlayers()
		players = sgs.QList2Table(players)
		if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceSpecial) and self.player:getPileName(card:getId()) == "&zui" then return true end
		local friends = 0
		local enemies = 0
		if self.player:hasSkill("luahongtu") and self.player:getMark("@luahongtu") == self.player:getHp() then
			return true
		end
		for _,player in ipairs(players) do
			if self:objectiveLevel(player) >= 4 and not player:hasSkill("hongyan") and not player:hasSkill("wuyan")
					and not (player:hasSkill("weimu") and card:isBlack()) then
				enemies = enemies + 1
			elseif self:isFriend(player) and not player:hasSkill("hongyan") and not player:hasSkill("wuyan")
					and not (player:hasSkill("weimu") and card:isBlack()) then
				friends = friends + 1 
			end
		end

		local ratio

		if friends == 0 then ratio = 999
		else ratio = enemies/friends
		end

		if ratio > 1.5 or (ratio >= 1 and self:needKongcheng()) then
			return true
		end
	end
end

function SmartAI:willUseGirlChoosen(card)
	if not card then self.room:writeToConsole(debug.traceback()) return false end
	if self.player:containsTrick("girl_choosen") then return end
	if self.player:hasSkill("weimu") and card:isBlack() then return end
	if self.room:isProhibited(self.player, self.player, card) then return end

	local function hasDangerousFriend()
		local hashy = false
		for _, aplayer in ipairs(self.enemies) do
			if aplayer:hasSkill("hongyan") and #self.friends <= #self.enemies then hashy = true break end
		end
		for _, aplayer in ipairs(self.friends) do
			if aplayer:hasSkill("luachuanming") and #self.friends <= #self.enemies then hashy = true break end
		end
		for _, aplayer in ipairs(self.enemies) do
			if aplayer:hasSkill("guanxing") or (aplayer:hasSkill("gongxin") and hashy)
					or aplayer:hasSkill("xinzhan") then
				if self:isFriend(aplayer:getNextAlive()) then return true end
			end
		end
		return false
	end

	if self:getFinalRetrial(self.player) == 2 then
		return
	elseif self:getFinalRetrial(self.player) == 1 then
		return true
	elseif not hasDangerousFriend() then
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return end
		end
		if self:ayalight(card) then return true end
		if card:isBlack() and self.player:hasSkill("luelangchao") then return end
		if self:HifuCard(card) then return true end
		if self.player:hasSkill("luabeihuan") and self.player:hasSkill("lualihe") then return end
		local players = self.room:getAllPlayers()
		players = sgs.QList2Table(players)
		if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceSpecial) and self.player:getPileName(card:getId()) == "&zui" then return true end
		local friends = 0
		local enemies = 0
		if self.player:hasSkill("luahongtu") and self.player:getMark("@luahongtu") == self.player:getHp() then
			return true
		end
		for _,player in ipairs(players) do
			if self:objectiveLevel(player) >= 4 and not player:hasSkill("hongyan") and not player:hasSkill("wuyan")
					and not (player:hasSkill("weimu") and card:isBlack()) then
				enemies = enemies + 1
			elseif self:isFriend(player) and not player:hasSkill("hongyan") and not player:hasSkill("wuyan")
					and not (player:hasSkill("weimu") and card:isBlack()) then
				friends = friends + 1
			end
		end

		local ratio

		if friends == 0 then ratio = 999
		else ratio = friends/enemies
		end

		if ratio >= 1.5 or (ratio >= 1 and self:needKongcheng()) then
			return true
		end
	end
end

function SmartAI:useCardLightning(card, use)
	if self:willUseLightning(card) then
		use.card = card
	end
end

function SmartAI:useCardGirlChoosen(card, use)
	if self:willUseGirlChoosen(card) then
		use.card = card
	end
end

sgs.ai_use_priority.Lightning = 0
sgs.dynamic_value.lucky_chance.Lightning = true
sgs.ai_keep_value.Lightning = -1

sgs.ai_keep_value.GirlChoosen = -1

sgs.ai_skill_askforag.amazing_grace = function(self, card_ids)

	local NextPlayerCanUse, NextPlayerisEnemy
	local NextPlayer = self.player:getNextAlive()
	if sgs.turncount > 1 and not self:willSkipPlayPhase(NextPlayer) then
		if self:isFriend(NextPlayer) and sgs.evaluatePlayerRole(NextPlayer) ~= "neutral" then
			NextPlayerCanUse = true
		else
			NextPlayerisEnemy = true
		end
	end
	for _, enemy in ipairs(self.enemies) do
		if enemy:hasSkill("lihun") and enemy:faceUp() and not NextPlayer:faceUp() and NextPlayer:getHandcardNum() > 4 and NextPlayer:isMale() then
			NextPlayerCanUse = false
		end
	end

	local cards = {}
	local trickcard = {}
	for _, card_id in ipairs(card_ids) do
		local acard = sgs.Sanguosha:getCard(card_id)
		table.insert(cards, acard)
		if acard:isKindOf("TrickCard") then
			table.insert(trickcard , acard)
		end
	end

	local nextfriend_num = 0
	local aplayer = self.player:getNextAlive()
	for i =1, self.player:aliveCount() do
		if self:isFriend(aplayer) then
			aplayer = aplayer:getNextAlive()
			nextfriend_num = nextfriend_num + 1
		else
			break
		end
	end

	local players = sgs.QList2Table(self.room:getAllPlayers())
	local to_seat = (self.room:getCurrent():getSeat() - self.player:getSeat()) % #players
	local targets = {}
	for _, p in ipairs(players) do
		if ((((p:getSeat() - self.player:getSeat()) % #players) < to_seat)) and p:objectName() ~= self.player:objectName() then
			table.insert(targets, p)
		end
	end

	local SelfisCurrent
	if self.room:getCurrent():objectName() == self.player:objectName() then SelfisCurrent = true end

---------------

	local needbuyi
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill("buyi") and self.player:getHp() == 1 then
			needbuyi = true
		end
	end
	if needbuyi then
		local maxvaluecard, minvaluecard
		local maxvalue, minvalue = -100, 100
		for _, bycard in ipairs(cards) do
			if not bycard:isKindOf("BasicCard") then
				local value = self:getUseValue(bycard)
				if value > maxvalue then
					maxvalue = value
					maxvaluecard = bycard
				end
				if value < minvalue then
					minvalue = value
					minvaluecard = bycard
				end
			end
		end
		if minvaluecard and NextPlayerCanUse then
			return minvaluecard:getEffectiveId()
		end
		if maxvaluecard then
			return maxvaluecard:getEffectiveId()
		end
	end

	local friendneedpeach, peach
	local peachnum, jinknum = 0, 0
	if NextPlayerCanUse then
		if (not self.player:isWounded() and NextPlayer:isWounded()) or
			(self.player:getLostHp() < self:getCardsNum("Peach")) or
			(not SelfisCurrent and self:willSkipPlayPhase() and self.player:getHandcardNum() + 2 > self.player:getMaxCards()) then
			friendneedpeach = true
		end
	end
	for _, card in ipairs(cards) do
		if isCard("Peach", card, self.player) then
			peach = card:getEffectiveId()
			peachnum = peachnum + 1
		end
		if card:isKindOf("Jink") then jinknum = jinknum + 1 end
	end
	if (not friendneedpeach and peach) or peachnum > 1 then return peach end

	local exnihilo, jink, analeptic, nullification, snatch, dismantlement, indulgence
	for _, card in ipairs(cards) do
		if isCard("ExNihilo", card, self.player) then
			if not NextPlayerCanUse or (not self:willSkipPlayPhase() and (self.player:hasSkills("nosjizhi|jizhi|zhiheng|nosrende|rende") or not NextPlayer:hasSkills("nosjizhi|jizhi|zhiheng|nosrende|rende"))) then
				exnihilo = card:getEffectiveId()
			end
		elseif isCard("Jink", card, self.player) then
			jink = card:getEffectiveId()
		elseif isCard("Analeptic", card, self.player) or isCard("Banquet", card, self.player) then
			analeptic = card:getEffectiveId()
		elseif isCard("Nullification", card, self.player) then
			nullification = card:getEffectiveId()
		elseif isCard("Snatch", card, self.player) then
			snatch = card
		elseif isCard("Dismantlement", card, self.player) then
			dismantlement = card
		elseif isCard("Indulgence", card, self.player) then
			indulgence = card:getEffectiveId()
		end

	end

	for _, target in sgs.qlist(self.room:getAlivePlayers()) do
		if self:willSkipPlayPhase(target) or self:willSkipDrawPhase(target) then
			if nullification then return nullification
			elseif self:isFriend(target) and snatch and self:hasTrickEffective(snatch, target, self.player) and
				not self:willSkipPlayPhase() and self.player:distanceTo(target) == 1 then
				return snatch:getEffectiveId()
			elseif self:isFriend(target) and dismantlement and self:hasTrickEffective(dismantlement, target, self.player) and
				not self:willSkipPlayPhase() and self.player:objectName() ~= target:objectName() then
				return dismantlement:getEffectiveId()
			end
		end
	end

	if SelfisCurrent then
		if exnihilo then return exnihilo end
		if (analeptic) and (self:getCardsNum("Jink") == 0 or (self:isWeak() and self:getOverflow() <= 0)) then
			return analeptic
		end
		if indulgence then return indulgence end
	else
		if self:getCardsNum("Jink") < 1 and sgs.getDefenseSlash(self.player) <= 2
			and self.player:getHp() <= 2 then
			if jink or analeptic or exnihilo then return jink or analeptic or exnihilo end
		else
			if exnihilo or indulgence then return exnihilo or indulgence end
		end
	end

	if exnihilo and not self:isFriend(NextPlayer) then
		return exnihilo
	end
	for _, card in ipairs(cards) do
		if card:isKindOf("Wanbaochui") and not self:isFriend(NextPlayer) then
			return card:getEffectiveId()
		end
		if card:isKindOf("Wanbaochui") and SelfisCurrent then
			return card:getEffectiveId()
		end
	end

	if nullification and (self:getCardsNum("Nullification") < 2 or not NextPlayerCanUse) then
		return nullification
	end

	if jinknum == 1 and jink and self:isEnemy(NextPlayer) and (NextPlayer:isKongcheng() or sgs.card_lack[NextPlayer:objectName()]["Jink"] == 1) then
		return jink
	end

	self:sortByUseValue(cards)
	for _, card in ipairs(cards) do
		for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
			local callback = sgs.ai_cardneed[skill:objectName()]
			if type(callback) == "function" and callback(self.player, card, self) then
				return card:getEffectiveId()
			end
		end
	end

	local eightdiagram, silverlion, vine, renwang, DefHorse, OffHorse
	local weapon, crossbow, halberd, double, qinggang, axe, gudingdao
	for _, card in ipairs(cards) do
		if card:isKindOf("EightDiagram") then eightdiagram = card:getEffectiveId()
		elseif card:isKindOf("SilverLion") then silverlion = card:getEffectiveId()
		elseif card:isKindOf("Vine") then vine = card:getEffectiveId()
		elseif card:isKindOf("RenwangShield") then renwang = card:getEffectiveId()
		elseif card:isKindOf("DefensiveHorse") and not self:getSameEquip(card) then DefHorse = card:getEffectiveId()
		elseif card:isKindOf("OffensiveHorse") and not self:getSameEquip(card) then OffHorse = card:getEffectiveId()
		elseif card:isKindOf("Crossbow") then crossbow = card
		elseif card:isKindOf("Halberd") then halberd = card:getEffectiveId()
		elseif card:isKindOf("DoubleSword") then double = card:getEffectiveId()
		elseif card:isKindOf("QinggangSword") then qinggang = card:getEffectiveId()
		elseif card:isKindOf("GudingBlade") then gudingdao = card:getEffectiveId()
		elseif card:isKindOf("Axe") then axe = card:getEffectiveId() end
		if card:isKindOf("Weapon") then weapon = card:getEffectiveId() end
	end

	if eightdiagram then
		local lord = getLord(self.player)
		if not self:hasSkills("yizhong|bazhen|linglong") and self:hasSkills("luatiandu|tiandu|leiji|nosleiji|olleiji|noszhenlie|gushou|hongyan|yqinxue") and not self:getSameEquip(card) then	--yun
			return eightdiagram
		end
		if NextPlayerisEnemy and self:hasSkills("luatiandu|tiandu|leiji|nosleiji|noszhenlie|gushou|hongyan|yqinxue", NextPlayer) and not self:getSameEquip(card, NextPlayer) then	--yun
			return eightdiagram
		end
		if self.role == "loyalist" and self.player:getKingdom()=="wei" and not self.player:hasSkills("bazhen|linglong") and
			lord and lord:hasLordSkill("hujia") and (lord:objectName() ~= NextPlayer:objectName() and NextPlayerisEnemy or lord:getArmor()) then
			return eightdiagram
		end
	end

	if silverlion then
		local lightning, canRetrial
		for _, Aplayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do
			if Aplayer:hasSkills("leiji|nosleiji|olleiji") and self:isEnemy(Aplayer) then
				return silverlion
			end
			if Aplayer:containsTrick("lightning") then
				lightning = true
			end
			if self:hasSkills("guicai|guidao", Aplayer) and self:isEnemy(Aplayer) then
				canRetrial = true
			end
		end
		if lightning and canRetrial then return silverlion end
		if self.player:isChained() then
			for _, friend in ipairs(self.friends) do
				if friend:hasArmorEffect("vine") and friend:isChained() then
					return silverlion
				end
			end
		end
		if self.player:isWounded() then return silverlion end
	end

	if vine then
		if sgs.ai_armor_value.vine(self.player, self) > 0 and self.room:alivePlayerCount() <= 3 then
			return vine
		end
	end

	if renwang then
		if sgs.ai_armor_value.renwang_shield(self.player, self) > 0 and self:getCardsNum("Jink") == 0 then return renwang end
	end

	if DefHorse and (not self.player:hasSkill("leiji|nosleiji|olleiji") or self:getCardsNum("Jink") == 0) then	--yun
		local before_num, after_num = 0, 0
		for _, enemy in ipairs(self.enemies) do
			if enemy:canSlash(self.player, nil, true) then
				before_num = before_num + 1
			end
			if enemy:canSlash(self.player, nil, true, 1) then
				after_num = after_num + 1
			end
		end
		if before_num > after_num and (self:isWeak() or self:getCardsNum("Jink") == 0) then return DefHorse end
	end

	if analeptic then
		local slashs = self:getCards("Slash")
		for _, enemy in ipairs(targets) do
			if self:isEnemy(enemy) and self:isWeak(enemy) and not sgs.Sanguosha:getCard(analeptic):isKindOf("Banquet") then
				return analeptic
			end
		end
		for _, enemy in ipairs(self.enemies) do
			local hit_num = 0
			for _, slash in ipairs(slashs) do
				if self:slashIsEffective(slash, enemy) and self.player:canSlash(enemy, slash) and self:slashIsAvailable() then
					hit_num = hit_num + 1
					if getCardsNum("Jink", enemy) < 1
						or enemy:isKongcheng()
						or self:canLiegong(enemy, self.player)
						or self.player:hasSkills("tieji|wushuang|dahe|qianxi")
						or self.player:hasSkill("roulin") and enemy:isFemale()
						or (self.player:hasWeapon("axe") or self:getCardsNum("Axe") > 0) and self.player:getCards("he"):length() > 4
						then
						return analeptic
					end
				end
			end
			if (self.player:hasWeapon("blade") or self:getCardsNum("Blade") > 0) and getCardsNum("Jink", enemy) <= hit_num then return analeptic end
			if self:hasCrossbowEffect(self.player) and hit_num >= 2 then return analeptic end
		end
	end

	if weapon and (self:getCardsNum("Slash") > 0 and self:slashIsAvailable() or not SelfisCurrent) then
		local current_range = (self.player:getWeapon() and sgs.weapon_range[self.player:getWeapon():getClassName()]) or 1
		local nosuit_slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		local slash = SelfisCurrent and self:getCard("Slash") or nosuit_slash

		self:sort(self.enemies, "defense")

		if crossbow then
			if #self:getCards("Slash") > 1 or self:hasSkills("kurou|keji|yshanzhan")	--yun
				or (self:hasSkills("luoshen|yongsi|luoying|guzheng") and not SelfisCurrent and self.room:alivePlayerCount() >= 4) then
				return crossbow:getEffectiveId()
			end
			if self.player:hasSkill("guixin") and self.room:alivePlayerCount() >= 6 and (self.player:getHp() > 1 or self:getCardsNum("Peach") > 0) then
				return crossbow:getEffectiveId()
			end
			if self.player:hasSkill("rende") then
				for _, friend in ipairs(self.friends_noself) do
					if getCardsNum("Slash", friend) > 1 then
						return crossbow:getEffectiveId()
					end
				end
			end
			if self:isEnemy(NextPlayer) then
				local CanSave, huanggai, zhenji
				for _, enemy in ipairs(self.enemies) do
					if enemy:hasSkill("buyi") then CanSave = true end
					if enemy:hasSkill("jijiu") and getKnownCard(enemy, self.player, "red", nil, "he") > 1 then CanSave = true end
					if enemy:hasSkill("chunlao") and enemy:getPile("wine"):length() > 1 then CanSave = true end
					if enemy:hasSkill("kurou") then huanggai = enemy end
					if enemy:hasSkill("keji") then return crossbow:getEffectiveId() end
					if self:hasSkills("luoshen|yongsi|guzheng|yshanzhan", enemy) then return crossbow:getEffectiveId() end	--yun
					if enemy:hasSkill("luoying") and crossbow:getSuit() ~= sgs.Card_Club then return crossbow:getEffectiveId() end	--yun
				end
				if huanggai then
					if huanggai:getHp() > 2 then return crossbow:getEffectiveId() end
					if CanSave then return crossbow:getEffectiveId() end
				end
				if getCardsNum("Slash", NextPlayer) >= 3 and NextPlayerisEnemy then return crossbow:getEffectiveId() end
			end
		end

		if halberd then
			if self.player:hasSkills("nosrende|rende") and self:findFriendsByType(sgs.Friend_Draw) then return halberd end
			if SelfisCurrent and self:getCardsNum("Slash") == 1 and self.player:getHandcardNum() == 1 then return halberd end
		end

		if gudingdao then
			local range_fix = current_range - 2
			for _, enemy in ipairs(self.enemies) do
				if self.player:canSlash(enemy, slash, true, range_fix) and enemy:isKongcheng() and self:canHitDown(enemy) and	--yun
				(not SelfisCurrent or (self:getCardsNum("Dismantlement") > 0 or (self:getCardsNum("Snatch") > 0 and self.player:distanceTo(enemy) == 1))) then
					return gudingdao
				end
			end
		end

		if axe then
			local range_fix = current_range - 3
			local FFFslash = self:getCard("FireSlash")
			for _, enemy in ipairs(self.enemies) do
				if enemy:hasArmorEffect("vine") and FFFslash and self:slashIsEffective(FFFslash, enemy) and
					self.player:getCardCount(true) >= 3 and self.player:canSlash(enemy, FFFslash, true, range_fix) then
					return axe
				elseif self:getCardsNum("Analeptic") > 0 and self.player:getCardCount(true) >= 4 and
					self:slashIsEffective(slash, enemy) and self.player:canSlash(enemy, slash, true, range_fix) then
					return axe
				end
			end
		end

		if double then
			local range_fix = current_range - 2
			for _, enemy in ipairs(self.enemies) do
				if self.player:getGender() ~= enemy:getGender() and self.player:canSlash(enemy, nil, true, range_fix) then
					return double
				end
			end
		end

		if qinggang then
			local range_fix = current_range - 2
			for _, enemy in ipairs(self.enemies) do
				if self.player:canSlash(enemy, slash, true, range_fix) and self:slashIsEffective(slash, enemy, self.player, true) then
					return qinggang
				end
			end
		end

	end

	local snatch, dismantlement, indulgence, supplyshortage, collateral, duel, aoe, godsalvation, fireattack, lightning
	local new_enemies = {}
	if #self.enemies > 0 then new_enemies = self.enemies
	else
		for _, aplayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do
			if sgs.evaluatePlayerRole(aplayer) == "neutral" then
				table.insert(new_enemies, aplayer)
			end
		end
	end
	for _, card in ipairs(cards) do
		for _, enemy in ipairs(new_enemies) do
			if card:isKindOf("Snatch") and self:hasTrickEffective(card, enemy, self.player) and self.player:distanceTo(enemy) == 1 and not enemy:isNude() then
				snatch = card:getEffectiveId()
			elseif not enemy:isNude() and card:isKindOf("Dismantlement") and self:hasTrickEffective(card, enemy, self.player) then
				dismantlement = card:getEffectiveId()
			elseif card:isKindOf("Indulgence") and self:hasTrickEffective(card, enemy, self.player) and not enemy:containsTrick("indulgence") then
				indulgence = card:getEffectiveId()
			elseif card:isKindOf("SupplyShortage")  and self:hasTrickEffective(card, enemy, self.player) and not enemy:containsTrick("supply_shortage") then
				supplyshortage = card:getEffectiveId()
			elseif card:isKindOf("Collateral") and self:hasTrickEffective(card, enemy, self.player) and enemy:getWeapon() then
				collateral = card:getEffectiveId()
			elseif card:isKindOf("Duel") and self:hasTrickEffective(card, enemy, self.player) and
					(self:getCardsNum("Slash") >= getCardsNum("Slash", enemy, self.player) or self.player:getHandcardNum() > 4) then
				duel = card:getEffectiveId()
			elseif card:isKindOf("AOE") then
				local dummy_use = {isDummy = true}
				self:useTrickCard(card, dummy_use)
				if dummy_use.card then
					aoe = card:getEffectiveId()
				end
			elseif card:isKindOf("FireAttack") and self:hasTrickEffective(card, enemy, self.player) then
				local FFF
				local jinxuandi = self.room:findPlayerBySkillName("wuling")
				if jinxuandi and jinxuandi:getMark("@fire") > 0 then FFF = true end
				if self.player:hasSkill("shaoying") then FFF = true end
				if enemy:getHp() == 1 or enemy:hasArmorEffect("vine") or enemy:getMark("@gale") > 0 then FFF = true end
				if FFF then
					local suits= {}
					local suitnum = 0
					for _, hcard in sgs.qlist(self.player:getHandcards()) do
						if hcard:getSuit() == sgs.Card_Spade then
							suits.spade = true
						elseif hcard:getSuit() == sgs.Card_Heart then
							suits.heart = true
						elseif hcard:getSuit() == sgs.Card_Club then
							suits.club = true
						elseif hcard:getSuit() == sgs.Card_Diamond then
							suits.diamond = true
						end
					end
					for k, hassuit in pairs(suits) do
						if hassuit then suitnum = suitnum + 1 end
					end
					if suitnum >= 3 or (suitnum >= 2 and enemy:getHandcardNum() == 1 ) then
						fireattack = card:getEffectiveId()
					end
				end
			elseif card:isKindOf("GodSalvation") and self:willUseGodSalvation(card) then
				godsalvation = card:getEffectiveId()
			elseif card:isKindOf("Lightning") and self:getFinalRetrial() == 1 then
				lightning = card:getEffectiveId()
			end
		end

		for _, friend in ipairs(self.friends_noself) do
			if (self:hasTrickEffective(card, friend) and (self:willSkipPlayPhase(friend, true) or self:willSkipDrawPhase(friend, true))) or
				self:needToThrowArmor(friend) then
				if isCard("Snatch", card, self.player) and self.player:distanceTo(friend) == 1 then
					snatch = card:getEffectiveId()
				elseif isCard("Dismantlement", card, self.player) then
					dismantlement = card:getEffectiveId()
				end
			end
		end
	end

	if snatch or dismantlement or indulgence or supplyshortage or collateral or duel or aoe or godsalvation or fireattack or lightning then
		if not self:willSkipPlayPhase() or not NextPlayerCanUse then
			return snatch or dismantlement or indulgence or supplyshortage or collateral or duel or aoe or godsalvation or fireattack or lightning
		end
		if #trickcard > nextfriend_num + 1 and NextPlayerCanUse then
			return lightning or fireattack or godsalvation or aoe or duel or collateral or supplyshortage or indulgence or dismantlement or snatch
		end
	end

	if weapon and not self.player:getWeapon() and self:getCardsNum("Slash") > 0 and (self:slashIsAvailable() or not SelfisCurrent) then
		local inAttackRange
		for _, enemy in ipairs(self.enemies) do
			if self.player:inMyAttackRange(enemy) then
				inAttackRange = true
				break
			end
		end
		if not inAttackRange then return weapon end
	end

	self:sortByCardNeed(cards, true)
	for _, card in ipairs(cards) do
		if not card:isKindOf("TrickCard") and not card:isKindOf("Peach") then
			return card:getEffectiveId()
		end
	end

	return cards[1]:getEffectiveId()
end

--WoodenOx
local wooden_ox_skill = {}
wooden_ox_skill.name = "wooden_ox"
table.insert(sgs.ai_skills, wooden_ox_skill)
wooden_ox_skill.getTurnUseCard = function(self)
	self.wooden_ox_assist = nil
	if self.player:hasUsed("WoodenOxCard") or self.player:isKongcheng() or not self.player:hasTreasure("wooden_ox") then return end
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByUseValue(cards, true)
	local card, friend = self:getCardNeedPlayer(cards)
	
	if card and friend and friend:objectName() ~= self.player:objectName() and (self:getOverflow() > 0 or self:isWeak(friend)) then
		self.wooden_ox_assist = friend
		if #self.friends_noself >= 1 then	--yun
			local nextp = self.player:getNextAlive()
			for i = 1, self.room:alivePlayerCount() do
				if not self:willSkipPlayPhase(nextp) and nextp:faceUp() then
					if self:isFriend(nextp) then self.wooden_ox_assist = nextp break end
				else
					nextp = nextp:getNextAlive()
				end
			end
		end
		return sgs.Card_Parse("@WoodenOxCard=" .. card:getEffectiveId())
	end
	if self:getOverflow() > 0 or (self:needKongcheng() and #cards == 1) 
		or (self.player:hasSkills(sgs.lose_equip_skill) and #self.friends_noself >= 1)	--yun 	
		or (self.player:hasSkill("zhanjue") and self.player:getMark("zhanjuedraw") < 2 and self.player:getHandcardNum() > 1) 
		or (self.player:hasSkill("qice") and self.player:getHandcardNum() > 1) then
		self.wooden_ox_assist = nil
		if #self.friends_noself >= 1 then	--yun
			local nextp = self.player:getNextAlive()
			for i = 1, self.room:alivePlayerCount() do
				if not self:willSkipPlayPhase(nextp) and nextp:faceUp() then
					if self:isFriend(nextp) then self.wooden_ox_assist = nextp break end
				else
					nextp = nextp:getNextAlive()
				end
			end
		end
		if not self.wooden_ox_assist and self.player:hasSkills(sgs.lose_equip_skill) then
			for _, friend in ipairs(self.friends_noself) do
				if friend then self.wooden_ox_assist = friend break end
			end
		end
		return sgs.Card_Parse("@WoodenOxCard=" .. cards[1]:getEffectiveId())
	end
end

sgs.ai_skill_use_func.WoodenOxCard = function(card, use, self)
	use.card = card
end

sgs.ai_skill_playerchosen.wooden_ox = function(self, targets)
	if self.player:hasSkill("luajunzhen") then return end
	return self.wooden_ox_assist
end

sgs.ai_playerchosen_intention.wooden_ox = -10
sgs.ai_card_intention.LuaYao = -80 --pay
 
sgs.ai_use_priority.WoodenOxCard = 0

function SmartAI:useCardLuaYao(card, use)	
	local friends_noself = self.friends_noself
	self:sort(friends_noself, "hp")
	for _, friend in ipairs(friends_noself) do
		if friend:isWounded() then 
			use.card = card
			if use.to then
				use.to:append(friend)
			end
			return
		end 
	end 
end 
sgs.ai_use_priority.LuaYao = 5
sgs.ai_use_value.LuaYao = 5.7
sgs.ai_keep_value.LuaYao = 2

sgs.ai_skill_use.sakura = function(self, prompt)
	local sakura = self:getCards("sakura")
	self.room:writeToConsole("樱测试")
	if (not sakura) or (#sakura == 0) then return "." end 
	self.room:writeToConsole("樱测试2")
	local cardX = self.room:getTag("sakuraX"):toCard()
	local target = self.room:getTag("sakuraY")
	if (not target) or (not target:toPlayer()) then return "." end 
	target = target:toPlayer()
	
	if self:isFriend(target) and (cardX:isKindOf("Slash") or cardX:isKindOf("ArcheryAttack")) then
		if self:findLeijiTarget(self.player, 50, target) then return "." end
		if target:hasSkill("jieyin") and not self.player:isWounded() and self.player:isMale() and not self.player:hasSkills("leiji|nosleiji|olleiji") then return "." end
		if not target:hasSkill("jueqing") then
			if (target:hasSkill("nosrende") or (target:hasSkill("rende") and not target:hasUsed("RendeCard"))) and self.player:hasSkill("jieming") then return "." end
			if target:hasSkill("pojun") and (not self.player:faceUp() or self.player:getHp() > 3) then return "." end	--yun
			if self:needChuanxin(self.player, target) then return "." end	--yun
			if self:getTWBaobian(self.player, target) > 0 then return "." end	--yun
		end	
	elseif self:isFriend(target) then 
		return "."
	end  
	if (not self:isFriend(target)) and (self:getCardsNum("Jink") == 0) and (cardX:isKindOf("Slash") or cardX:isKindOf("ArcheryAttack")) then 
		if self:findLeijiTarget(self.player, 50, target) then return "." end
		if self:needChuanxin(self.player, target) then return "." end	--yun
		if self:getTWBaobian(self.player, target) > 0 then return "." end	--yun
		self:sortByKeepValue(sakura) 
		return sakura[1]:toString() 
	elseif (not self:isFriend(target)) and cardX:isKindOf("TrickCard") and self:getCardsNum("Nullification") == 0 then 
		if (cardX:isKindOf("Duel") and not target:hasSkill("wuyan")) or cardX:isKindOf("Snatch") or cardX:isKindOf("SupplyShortage") then 
			self:sortByKeepValue(sakura) 
			return sakura[1]:toString() 		
		end 
		if cardX:isKindOf("Indulgence") and self:willSkipPlayPhase() and (self:getOverflow(target) >= 0 or self:canKillEnermyAtOnce()) then  
			self:sortByKeepValue(sakura) 
			return sakura[1]:toString() 		
		end 		
		if cardX:isKindOf("Duel") then
            --敌方拆友方威胁牌、价值牌、最后一张手牌->命中
            if self:getDangerousCard(self.player) or self:getValuableCard(self.player) then return sakura[1]:toString()  end
			if self.player:getHandcardNum() == 1 and not self:needKongcheng(self.player) then
				if (getKnownCard(self.player, self.player, "TrickCard", false) == 1 or getKnownCard(self.player, self.player, "EquipCard", false) == 1 or getKnownCard(self.player, self.player, "Slash", false) == 1) then
					return nil
				end
				self:sortByKeepValue(sakura) 
				return sakura[1]:toString() 
			end
		end 
		if cardX:isKindOf("FireAttack") and not self:damageIsEffective(self.player, sgs.DamageStruct_Fire) then return "." end 
		if cardX:isKindOf("FireAttack") and target:objectName() ~= self.player:objectName() and not target:hasSkill("wuyan") then
			if target:getHandcardNum() > 2
				or self:isWeak(self.player)
				or self.player:hasArmorEffect("vine")
				or self.player:getMark("@gale") > 0
				or self.player:hasSkill("ranshang")	--yun
				or self.player:isChained() and not self:isGoodChainTarget(self.player, target) then 
				self:sortByKeepValue(sakura)
				self.room:writeToConsole("樱测试3")
				return sakura[1]:toString() 	
			end
        end		
		if cardX:isKindOf("SavageAssault") and (self:getCardsNum("Slash") == 0) then 
			self:sortByKeepValue(sakura)
			self.room:writeToConsole("樱测试3")
			return sakura[1]:toString() 			
		end 
	end 
end 
sgs.ai_use_value.sakura = 9.1
sgs.ai_keep_value.sakura = 5.6

function SmartAI:useCardquanxiang(card, use)
	if self.player:hasSkill("wuyan") then return end
	if self.player:hasSkill("noswuyan") then return end
	if self.player:getHandcardNum() <= 1 then return end
	if self.player:isCardLimited(card, sgs.Card_MethodUse) then return end

	if (self.player:hasSkill("luafenxing") and (self.player:getHandcardNum() <= 2) and (self:getOverflow() <= 0)) then return end
	local targets_num = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, self.player, card)
	local enermy = self.enemies
	local enermy2 = {}
	self:sort(enermy, "handcard")
	for _, to in ipairs(enermy) do
		if self:hasTrickEffective(card, to) and not self.room:isProhibited(self.player, to, card)
				and self.player:inMyAttackRange(to) and not to:isKongcheng()
				and (self.player:getHandcardNum() > to:getHandcardNum()) and to:faceUp() then
			table.insert(enermy2, to)
		end
	end

	self:sort(self.friends_noself, "defense")
	for _, to in ipairs(self.friends_noself) do
		if self:hasTrickEffective(card, to) and not self.room:isProhibited(self.player, to, card)
				and self.player:inMyAttackRange(to) and not to:isKongcheng()
				and not to:faceUp() then
			table.insert(enermy2, to)
		end
	end

	local q = self.player:getHandcardNum() - self.player:getMaxCards()
	local qcard = self:getMaxCard(self.player)
	if qcard:getId() == card:getId() then  
		local acards = sgs.QList2Table(self.player:getHandcards())
		local compare_func = function(a, b)
			return a:getNumber() > b:getNumber()
		end
		table.sort(acards, compare_func) 
		for _, car in ipairs(acards) do
			if car:getId() ~= card:getId() then qcard = car;break end 
		end 
	end 
	local p = 0.4 + 0.15*qcard:getNumber() - 1.2
	
	local function pandin(acard)
		if qcard:getNumber() <= acard:getNumber() then 
			return false
		end 
		return true
	end 
	if (q > 0) or (math.random() < p) or (self.player:hasSkill("luajingjuan") and sgs.Slash_IsAvailable(self.player) and self.player:getHandcardNum() > 3) then 
		use.card = card
		if use.to then 
			for _, to in ipairs(enermy2) do
				local count = 0
				for _, cardP in sgs.qlist(to:getHandcards()) do
					local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), to:objectName())
					if (cardP:hasFlag("visible") or cardP:hasFlag(flag)) and pandin(cardP) then
						count = count + 1 
					end 
				end 
				if count == to:getHandcardNum() then 	
					use.to:append(to)
					if use.to:length() == targets_num then return end
				end 			
			end 

			for _, to in ipairs(enermy2) do
				if not use.to:contains(to) then 
					use.to:append(to)
					if use.to:length() == targets_num then return end		
				end 
			end 
		end 
		if use.to and use.to:isEmpty() then use.card = nil end
	end 
end 

sgs.ai_use_priority.quanxiang = 5
sgs.ai_use_value.quanxiang = 7

sgs.ai_card_intention.quanxiang = function(self, card, from, tos)  --杀仇恨值
	for _, to in ipairs(tos) do
		if to:faceUp() then 
			sgs.updateIntention(from, to, 60)
		else
			sgs.updateIntention(from, to, -60)
		end 
	end 
end 

function SmartAI:useCardyuzhi(card, use)
	if self.player:hasUsed("yuzhi") then return end
	local targets = sgs.SPlayerList()
	for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if self.player:inMyAttackRange(p) then targets:append(p) end
	end
	local toUse = self:findPlayerToDamage(1, self.player, sgs.DamageStruct_Normal, targets, false, 0, true)
	local targets_num = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, self.player, card)
	if #toUse == 0 then return false end
	use.card = card
	if use.to then
		for _, to in ipairs(toUse) do
			use.to:append(to)
			if use.to:length() == targets_num then return end
		end
	end
end 
sgs.ai_use_priority.yuzhi = 4.5
sgs.ai_card_intention.yuzhi = 80

local function CanHui_Discard(self)
	if self:getOverflow() > 1 and not self.player:hasSkills("luatanmi|luashizi|luahongtu|luajianta|kuanggu") then return true end
end
local function useHui_NormalDamage(self, card, use)

	local damage = self:AtomDamageCount2(self.player, self.player, nil, card)
	if damage == 0 then
		use.card = card
		return
	end
	local hp = self.player:getHp()
	local amSafe = ( damage < hp )
	local peachNum = nil
	if not amSafe then
		peachNum = self:getCardsNum("Peach")
		amSafe = ( damage < hp + peachNum )
	end
	if amSafe then
		if self:hasSkills("kuanggu|luawangyue") then
			use.card = card
			return
		elseif self.player:hasSkill("kuangbao") and hp > 3 then
			use.card = card
			return
		end
		peachNum = peachNum or self:getCardsNum("Peach")  --self:damageIsEffective(enemy, nil, self.player)
		if self:hasSkills("guixin|yiji|nosyiji|chengxiang|noschengxiang") and peachNum > 0 then
			use.card = card
			return
		elseif self.player:hasSkill("jieming") and peachNum > 0 and self:getJiemingChaofeng(self.player) > 0 then
			use.card = card
			return
		elseif self.player:hasSkill("luayuechong") and (self.player:getHp() > 1 or self.player:getHandcardNum() > 2) then
			use.card = card
			return
		elseif self.player:hasSkill("luajinlun") and (self.player:getHp() > 1 or self.player:getHandcardNum() <= 2) then
			use.card = card
			return
		elseif self.player:hasSkill("luahongye") and self.player:getHp() > 1
				and (self:getOverflow() > 0 or (self.player:getHandcardNum() == 1 or self.player:isKongcheng() and (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceSpecial))) then
			use.card = card
			return
		elseif (self.player:getPile("qizhi2") and (not self.player:getPile("qizhi2"):isEmpty()) and self.player:getHp() > 1) then
			use.card = card
			return
		elseif self.player:getHandcardNum() >= 1 and (self.player:getLostHp() == 0 or self.player:getCardCount(true) >= 3) and self:needKongcheng() then
			use.card = card
			return
		elseif self:getOverflow() > 1 then
			use.card = card
			return
		elseif self:needKongcheng() and peachNum > 0.5 then
			use.card = card
			return
		elseif self.player:getCardCount(true) >= 3 and self:needToThrowArmor() then
			use.card = card
			return
		elseif self:getOverflow() > 0 then
			for _, c in sgs.qlist(self.player:getHandcards()) do
				if c:isKindOf("Peach") and self:OverFlowPeach(c) then
					use.card = card
					return
				end
			end
		end
	else
		if self.role == "renegade" or self.role == "lord" then
			return
		elseif self:getAllPeachNum() > 0 or self:getOverflow() <= 0 then
			return
		elseif self.player:hasSkill("wuhun") and self:needDeath(self.player) then
			use.card = card
			return
		end
	end
end
function SmartAI:useCardHui(card, use)
	local nazrin = self.room:findPlayerBySkillName("luatanbao")
	if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
		if card:hasFlag("prelingbai") then return end
	end
	useHui_NormalDamage(self, card, use)
end

sgs.ai_use_value["Hui"] = -10
sgs.ai_keep_value["Hui"] = 10
sgs.ai_card_intention.Hui = 120

sgs.ai_skill_choice.hui = function(self, choices, data)
	local card = data:toCard()
	if not card then card = sgs.Sanguosha:cloneCard("hui", sgs.Card_NoSuit, 0) end
	if (self.player:hasSkill("luajinlun") and not self:isWeak()) or self.player:hasSkill("luaxinshi") then return "damage" end
	if self.player:hasSkill("luafenxing") then return "damage" end
	if self.player:hasSkill("luawangyou") and not self:isWeak() then return "damage" end
	local damage = self:AtomDamageCount2(self.player, self.player, nil, card)
	if damage == 0 then
		return "damage"
	end
	if self:getCardsNum("Peach") > 0 or (self:isWeak() and self:getCardsNum("Analeptic") > 0) then return "damage" end --有桃吃的通用策略
	if self:needKongcheng() and self.player:getCardCount(true) >= 2 and not self.player:isKongcheng()
		and not self.player:hasSkill("luayanfeng") then return "discard" end
	if self.player:hasSkill("luahongye") and self.player:isKongcheng() then return "damage" end
	if self.player:hasSkill("luahongye") then return "discard" end
	if CanHui_Discard(self) then return "discard" end --牌太多的通用策略
	if (self.player:getPile("qizhi2") and (not self.player:getPile("qizhi2"):isEmpty()) and self.player:getHp() > 1) then return "damage" end
	if self.player:getCardCount(true) >= 2 and self:needToThrowArmor() then return "discard" end
	if (self.player:getHandcardNum() == 2 or (self.player:getCardCount(true) >= 2 and self.player:getHandcardNum() == 1))
		and self:needKongcheng() then return "discard" end
	if self:getCardsNum("Peach") > 0.9 then return "damage" end
	if self:getCardsNum("Analeptic") > 0.9 and self.player:getHp() == 1 then return "damage" end
	--if self.player:getHp() > 2 or (self.player:getHp() == 2 and self.player:getPhase() == sgs.Player_Play) then return "damage" end
	if self.player:getCardCount(true) >= 2 then return "discard" end
	return "damage"
end

function SmartAI:searchForOfuda(use, enemy, slash)
	if not self.toUse then return nil end
	if not use.to then return nil end

	local ofuda = self:getCard("Ofuda")
	if not ofuda then return nil end

	local ofudaAvail = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_Residue, self.player, ofuda)
	local ofudaAvail2 = 0
	for _, enemyX in ipairs(self.enemies) do
		if self.player:inMyAttackRange(enemyX) then
			if self:canBeOfudaTarget(enemyX, ofuda, slash) then
				ofudaAvail2 = ofudaAvail2 + 1
			end
		end
	end
	ofudaAvail = math.min(ofudaAvail, ofudaAvail2)
	local slashAvail = 0

	for _, card in ipairs(self.toUse) do
		if ofudaAvail == 1 and card:getEffectiveId() ~= slash:getEffectiveId() and card:isKindOf("Slash") then return nil end
		if card:isKindOf("Slash") then slashAvail = slashAvail + 1 end
	end

	if ofudaAvail > 1 and ofudaAvail < slashAvail then return nil end

	local cards = self.player:getHandcards()
	cards = sgs.QList2Table(cards)
	self:fillSkillCards(cards)
	local allcards = self.player:getCards("he")
	allcards = sgs.QList2Table(allcards)

	if self.player:getPhase() == sgs.Player_Play then


		--[[
		if self.player:hasSkill("luatianzhao") and not self.player:hasFlag("Global_luatianzhaoAnaFailed") then
			self.room:setPlayerFlag(self.player, "tianzhaojiu")
			return sgs.Card_Parse("#luatianzhao:.:")
		end
		if self.player:hasSkill("luazhengyi") and not self.player:hasFlag("luazhengyi") then
			local a0 = self:justice(true, slash)
			if a0 then
				local card_stR = ("analeptic:luazhengyi[%s:%s]=%d"):format(a0:getSuitString(), a0:getNumberString(), a0:getEffectiveId())
				local analeptiC = sgs.Card_Parse(card_stR)
				assert(analeptiC)
				return analeptiC
			end
		end
		if self.player:hasSkill("luaqiji") and self.player:usedTimes("guhuo_select") <= self.player:getLostHp() and self.player:hasFlag("qiji9_success")
				and (self.player:getHandcardNum() > 1) then
			self.room:setPlayerFlag(self.player, "qijijiu")
			return sgs.Card_Parse("#guhuo_select:.:")
		end
		]]--
	end

	local card_str = self:getCardId("Ofuda")
	if card_str then return sgs.Card_Parse(card_str) end
	local forTanbao = false
	local nazrin = self.room:findPlayerBySkillName("luatanbao")
	if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
		forTanbao = true
	end
	for _, ofu in ipairs(cards) do
		if (ofu:getClassName() == "Ofuda") and not (ofu:getEffectiveId() == slash:getEffectiveId())
			and not (ofu:hasFlag("prelingbai") and forTanbao) then
			local bool = self.player:hasSkill("luachaofan") and (not self.player:hasFlag("forbidChaofan")) and ofu:getNumber() <= self.player:getMark("chaofan")
			local bool2 = self.player:hasSkill("luachaofan") and self.player:getMark("chaofan") == 0 and (not self.player:hasFlag("forbidChaofan")) and ofu:getNumber() > self.player:getMark("chaofan")
			if not bool and not bool2 then
				return ofu
			end
		end
	end
end

function SmartAI:useCardOfuda(card, use)
	if self.player:isCardLimited(card, sgs.Card_MethodUse) then return end
	if self.player:hasSkill("luahuapu") and card:getSuit() == sgs.Card_Club then
		return
	end
	local nazrin = self.room:findPlayerBySkillName("luatanbao")
	if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
		if card:hasFlag("prelingbai") then return end
	end
	local slashes = {}
	for _, cardQ in sgs.list(self.player:getHandcards()) do
		if cardQ:isKindOf("Slash") and not self.player:isCardLimited(cardQ, sgs.Card_MethodDiscard) then
			table.insert(slashes, cardQ:getId())
		end
	end
	local count = 0
	for _, cardX in sgs.list(self.player:getHandcards()) do
		if cardX:isKindOf("Duel") or cardX:isKindOf("Snatch") or cardX:isKindOf("FaithCollection") or cardX:isKindOf("Indulgence")
				or cardX:isKindOf("ExNihilo") or cardX:isKindOf("AOE") or cardX:isKindOf("FireAttack") then
			count = count + 1
		end
	end
	local targets = {}
	local enemies = self.enemies
	self:sort(enemies, "hp")
	for _, to in ipairs(enemies) do
		if self:canBeOfudaTarget(to, card) then
			table.insert(targets, to)
		end
	end
	local boolX = false
	if self.player:hasSkill("luajingjuan") and not self.player:isKongcheng() and self.room:getCardPlace(card:getEffectiveId()) == sgs.Player_PlaceHand then
		local all = self:Hiziri()
		if #all == 0 then return v end
		local compare_func = function(a, b)
			return #a > #b
		end
		table.sort(all, compare_func)
		local coulduse = true
		for _, cardX in ipairs(all[1]) do
			if cardX:getId() == card:getId() then coulduse = false end
		end
		if coulduse then boolX = true end
	end
	if self.player:getPhase() == sgs.Player_NotActive then
		boolX = true
	end
	if self.player:hasSkill("luashenquan") and self.player:getHandcardNum() > 2 then
		boolX = true
	end
	if (count > 1 or boolX or (self.player:hasSkill("luazhonggong") and not boolX and #slashes >= 2)) and #targets > 0 then
		local targets_num = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, self.player, card)
		use.card = card
		if use.to then
			for _, to in ipairs(targets) do
				use.to:append(to)
				if use.to:length() == targets_num then return end
			end
		end
	end
end
sgs.ai_use_value["Ofuda"] = 4.2
sgs.ai_keep_value["Ofuda"] = 3.6
sgs.ai_use_priority.Ofuda = 7
sgs.ai_skill_choice.ofuda = function(self, choices, data)  --没有考虑幽幽子问题
	self.room:writeToConsole("Ofuda test3")
	local target = data:toPlayer()
	local x = 0
	local y = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
	if self.player:getHp() == 1 and not self.player:hasSkill("luafenxing") then
		x = self:getCardsNum("Analeptic") + self:getCardsNum("Peach")
	end
	if not self:damageIsEffective(self.player, nil, target) then return "ofuda2" end
	if self.player:hasSkill("luafenxing") then return "ofuda2" end

	local canJink = true
	local jink = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
	if self.player:isCardLimited(jink, sgs.Card_MethodUse) then canJink = false end
	local bool1 = target:hasSkill("Luashenqiang") and target:getHandcardNum() >= self.player:getHandcardNum()
	local bool2 = false
	if target:hasSkills("LuaFengyin|luaquxie") then
		local cards = sgs.QList2Table(target:getHandcards())
		local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), target:objectName())
		if #cards > 2 then
			bool2 = true
		else
			for _, cc in ipairs(cards) do
				if (cc:hasFlag("visible") or cc:hasFlag(flag)) and (cc:getSuit() == sgs.Card_Diamond) then
					bool2 = true
				end
			end

		end
	end
	if canJink and not bool1 and not target:hasSkills("luajingdan|tieji") and not bool2 then
		x = x + self:getCardsNum("Jink")
	end

	if target:hasSkill("LuaShanguang") and target:getJudgingArea():length() > 2 and self:getCardsNum("Jink") >= 1 and canJink then return "ofuda1" end
	if target:hasSkill("LuaShanguang") and target:getJudgingArea():length() >= self.player:getHp() and self:getCardsNum("Jink") >= 1 and canJink
		and not self.player:hasSkill("luafenxing") then return "ofuda1" end

	if x < 1 and not (self.player:getHandcardNum() == 1 and self:needKongcheng()) and not (self.player:hasSkills(sgs.masochism_skill)) then
		return "ofuda2"
	end
	if y == 1 and self.player:getHandcards():length() == 1 then return "ofuda2" end

	if self:isWeak() and self.player:getHandcardNum() > 1 and self:getCardsNum("Peach") >= 1 then return "ofuda1" end
	if self:isWeak() and self.player:hasSkill("luajinxi") and self.player:getPile("yuanqi")
			and self.player:getPile("yuanqi"):length() > 1 then return "ofuda1" end
	local hcards = sgs.QList2Table(self.player:getHandcards())
	self:sortByUseValue(hcards, true)
	if hcards[1]:isKindOf("Peach") or hcards[1]:isKindOf("ExNihilo") or hcards[1]:isKindOf("Indulgence") or hcards[1]:isKindOf("Snatch") or hcards[1]:isKindOf("AOE")
			or hcards[1]:isKindOf("FaithCollection") or (hcards[1]:isKindOf("IronChain") and target:hasSkill(sgs.shuxin_skill)) then return "ofuda2" end

	for _, skill in sgs.qlist(target:getVisibleSkillList(true)) do
		local callback = sgs.ai_cardneed[skill:objectName()]
		if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](target, hcards[1], self) then
			return "ofuda2"
		end
	end

	if self.player:getHandcardNum() == 1 and self:needKongcheng() then return "ofuda1" end
	if self.player:hasSkill("luayuechong") then return "ofuda1" end
	if target:hasSkill("LuaShanguang") and target:getJudgingArea():length() > 1 and self:getCardsNum("Jink") >= 1 and canJink then return "ofuda1" end

	if not self:isWeak() and target:getHandcardNum() < 4 and not self.player:hasSkill("Luayuyi") then return "ofuda2" end

	return "ofuda1"
end
sgs.ai_card_intention.Ofuda = 40

sgs.ai_skill_cardask["@ofuda"] = function(self, data, pattern, target)
	local hcards = sgs.QList2Table(self.player:getHandcards())
	self:sortByUseValue(hcards, true)
	return "$" ..  hcards[1]:getEffectiveId()
end


function SmartAI:canBeOfudaTarget(to, ofuda, cardX)
	if (self.player:isCardLimited(ofuda, sgs.Card_MethodUse) or self.player:isProhibited(to, ofuda, to:getSiblings())) then return false end
	local no_distance = sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_DistanceLimit, self.player, ofuda) > 50
	if not self.player:inMyAttackRange(to) and not no_distance then return false end
	local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), to:objectName())
	for _, acard in sgs.qlist(to:getHandcards()) do
		if (acard:hasFlag("visible") or acard:hasFlag(flag)) and (acard:isKindOf("Hui")) then
			return false
		end
	end
	if to:isKongcheng() then return false end
	if not self:damageIsEffective(to, sgs.DamageStruct_Normal, self.player) then
		return false
	end
	if cardX then
		local boolx = (getCardsNum("Peach", to, self.player) >= 1) or (getCardsNum("Analeptic", to, self.player) >= 1)
		if cardX:isKindOf("Slash") then
			if self:YouMu2(to, true) and not (boolx and to:getHp() == 1) then return false end
		end
		if cardX:isKindOf("Duel") then
			if getCardsNum("Slash", to, self.player) < 1 and not (boolx and to:getHp() == 1) then return false end
		end
	end
	return true
end

function SmartAI:useCardBanquet(card, use)
	if self.player:hasSkill("LuaWeishi") then return end
	if self.player:hasSkill("luajingjuan") and not self.player:isKongcheng() and self.room:getCardPlace(card:getEffectiveId()) == sgs.Player_PlaceHand then
		local all = self:Hiziri()
		if #all == 0 then return v end
		local compare_func = function(a, b)
			return #a > #b
		end
		table.sort(all, compare_func)
		local coulduse = true
		for _, cardX in ipairs(all[1]) do
			if cardX:getId() == card:getId() then coulduse = false end
		end
		if coulduse then use.card = card end
	end
	if self.player:hasSkill("luamoulue") and self:Kitcho() then
		use.card = card
	end
	if self.player:hasSkill("LuaShanguang") then
		if self.player:getJudgingArea():length() > 1 then
			local redcards, blackcards = {}, {}
			for _, acard in ipairs(self.player:getHandcards()) do
				if acard:isBlack() then table.insert(blackcards, acard) else table.insert(redcards, acard) end
			end
			if #redcards > 1 then
				use.card = card
			end
		end
	else

		local joon = self.room:findPlayerBySkillName("niaoxiang")
		if joon and joon:isAlive() and self:isEnemy(joon) then
			return
		end
		if self:getOverflow() > 0 and #self.friends_noself > 0 then
			use.card = card
		end
	end
end
sgs.ai_use_value["Banquet"] = 6.1
sgs.ai_keep_value["Banquet"] = 3.42
sgs.ai_use_priority.Banquet = 2
sgs.ai_playerchosen_intention.Banquet = -20

sgs.ai_skill_playerchosen.banquet = function(self, targets)
	local friend = self:Kitcho(nil, true, true, sgs.QList2Table(targets))
	if friend then
		return friend
	end
	local targetlist = sgs.QList2Table(targets)
	for _, target in ipairs(targetlist) do	--杀敌
		if self:isFriend(target) then return target end
	end
end

function SmartAI:useCardNeedMaribel(card, use)
	if self.player:hasSkill("luahuapu") and card:getSuit() == sgs.Card_Club then
		return
	end
	for _, kitcho in ipairs(self.friends) do
		if kitcho:hasFlag("luamoulue") and kitcho:hasSkill("luamoulue") and not kitcho:containsTrick("need_maribel")
			and not self.player:isProhibited(kitcho, card, kitcho:getSiblings()) then
			use.card = card
			if use.to then
				use.to:append(kitcho)
				return
			end
		end
	end

	self:sort(self.friends, "handcard2")
	for _, friend in ipairs(self.friends) do
		if friend:isWounded() and self:getIndulgenceValue(friend) > 3 and not friend:containsTrick("need_maribel")
				and not self.player:isProhibited(friend, card, friend:getSiblings())  then
			use.card = card
			if use.to then
				use.to:append(friend)
				return
			end
		end
	end

	self:sort(self.friends, "defense")
	for _, friend in ipairs(self.friends) do
		if friend:isWounded() and not friend:containsTrick("need_maribel") and not self.player:isProhibited(friend, card, friend:getSiblings()) then
			use.card = card
			if use.to then
				use.to:append(friend)
				return
			end
		end
	end

	if self:getOverflow() > 0 then
		sgs.ai_use_priority.NeedMaribel = 1
		for _, friend in ipairs(self.friends) do
			if not friend:containsTrick("need_maribel") and not self.player:isProhibited(friend, card, friend:getSiblings())  then
				use.card = card
				if use.to then
					use.to:append(friend)
					return
				end
			end
		end
	end
end

sgs.ai_use_value["NeedMaribel"] = 3.7
sgs.ai_keep_value["NeedMaribel"] = 2.8
sgs.ai_use_priority.NeedMaribel = 3.4
sgs.ai_card_intention.NeedMaribel = -10 --pay

sgs.ai_keep_value["Wanbaochui"] = 5

local wanbaochui_skill = {}
wanbaochui_skill.name = "wanbaochui"
table.insert(sgs.ai_skills, wanbaochui_skill)
wanbaochui_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#wanbaochui") or not self.player:hasTreasure("wanbaochui") then return end
	if self.player:hasSkill("luatianhu") and not self.player:hasUsed("#luatianhu") then return end
	return sgs.Card_Parse("#wanbaochui:.:")
end
sgs.ai_skill_use_func["#wanbaochui"] = function(card, use, self)
	use.card = sgs.Card_Parse("#wanbaochui:.:")
	if use.to then use.to = sgs.SPlayerList() end
	return
end
sgs.ai_use_priority.wanbaochui = 7.2

sgs.ai_skill_cardask["@dangzuosha"] = function(self, data)
	for _, slash in ipairs(self:getCards("Ofuda")) do
		return slash:toString()
	end
	return "."
end

function SmartAI:useCardFaithCollection(card, use)
	if (self.player:isCardLimited(card, sgs.Card_MethodUse)) then return false end
	local x = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, self.player, card)
	if use.isDummy and use.extra_target then x = x + use.extra_target end
	local targets = sgs.SPlayerList()
	if not self.player:isWounded() and self.player:getHp() > 2 and #self.enemies > 1 and self.player:hasSkill("luashenjun")
			and self.player:hasUsed("#luashenjun") and self:getOverflow() <= 0 then return end
	if self.player:getHp() >= 2 and #self.enemies > 1 and self.player:hasSkill("luamaoyou") and self:getOverflow() <= 0
			and not self.player:hasSkill("luashidan") and self.player:getMark("@luamaoyou") >= 1 and (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) then return end
	local function canbeTarget(targetX)
		local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), targetX:objectName())
		for _, acard in sgs.qlist(targetX:getHandcards()) do
			if (acard:hasFlag("visible") or acard:hasFlag(flag)) and (acard:isKindOf("Hui")) then
				return false
			end
		end
		return self:hasTrickEffective(card, targetX) and not self.player:isProhibited(targetX, card, targetX:getSiblings())
			and not targets:contains(targetX) and x > 0
	end

	local friends = self.friends_noself
	for _, friend in ipairs(friends) do
		if (friend:containsTrick("indulgence") or friend:containsTrick("supply_shortage")) and not friend:containsTrick("YanxiaoCard")
				and canbeTarget(friend) then
			targets:append(friend)
			x = x - 1
		end
	end

	for _, friend in ipairs(friends) do
		if self:needToThrowArmor(friend) and canbeTarget(friend) then
			targets:append(friend)
			x = x - 1
		end
	end

	for _, enemy in ipairs(self.enemies) do
		if not (enemy:containsTrick("indulgence") or enemy:containsTrick("supply_shortage")) then
			for _, cardX in sgs.qlist(enemy:getCards("hej")) do
				if not (self:needToThrowArmor(enemy) and self.room:getCardPlace(cardX:getEffectiveId()) == sgs.Player_PlaceEquip and cardX:isKindOf("Armor")) and canbeTarget(enemy) then
					targets:append(enemy)
					x = x - 1
				end
			end
		end
	end
	if (self.player:getRole() == "loyalist" or self.player:isLord()) and targets:isEmpty() then
		for _,p in sgs.qlist(self.room:getAlivePlayers()) do
			if not self:isFriend(p) and not self:isEnemy(p) then
				if not (p:containsTrick("indulgence") or p:containsTrick("supply_shortage")) then
					for _, cardX in sgs.qlist(p:getCards("hej")) do
						if not (self:needToThrowArmor(p) and self.room:getCardPlace(cardX:getEffectiveId()) == sgs.Player_PlaceEquip and cardX:isKindOf("Armor")) and canbeTarget(p) then
							targets:append(p)
							x = x - 1
						end
					end
				end
			end
		end
	end
	if not targets:isEmpty() then
		use.card = card
		if use.to then
			use.to = targets
			return
		end
	end
end

sgs.ai_use_value["FaithCollection"] = 8,7
sgs.ai_keep_value["FaithCollection"] = 3.6
sgs.ai_use_priority.FaithCollection = 4.3

sgs.ai_card_intention.FaithCollection = function(self, card, from, tos)  --杀仇恨值
	for _, to in ipairs(tos) do
		if to:containsTrick("indulgence") or to:containsTrick("supply_shortage") or self:needToThrowArmor(to) then
			sgs.updateIntention(from, to, -30)
		else
			sgs.updateIntention(from, to, 30)
		end
	end
end

sgs.ai_skill_invoke.gohei = true
sgs.ai_skill_invoke.hakkero = true

sgs.ai_skill_playerchosen.yanshi = function(self, targets, slashX)
	return self:findPlayerToDiscard("hej", true, false)
end

