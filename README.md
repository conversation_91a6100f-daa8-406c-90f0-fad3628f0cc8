# QSanguosha-v2 🎮

<div align="center">

**基于Qt和Lua的开源三国杀游戏平台**

[![License](https://img.shields.io/badge/license-GPL%20v3-blue.svg)](#许可证)
[![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)](#系统要求)
[![Language](https://img.shields.io/badge/language-C%2B%2B%20%7C%20Lua-orange.svg)](#技术栈)
[![Version](https://img.shields.io/badge/version-v2--20190208-green.svg)](#版本信息)

[🚀 快速开始](#快速开始) • [📖 文档](#文档中心) • [🛠️ 开发](#开发指南) • [🤝 贡献](#贡献指南) • [📞 支持](#获取帮助)

</div>

---

## 📋 项目简介

QSanguosha-v2是一个功能完整、高度可扩展的开源三国杀游戏实现。项目基于Qt跨平台框架和Lua脚本引擎开发，提供了丰富的游戏内容、强大的扩展能力和优秀的用户体验。

### ✨ 核心特性

- 🎮 **完整游戏体验** - 300+武将、200+卡牌、30+扩展包
- 🤖 **智能AI系统** - 多难度AI对手，支持各扩展包
- 🌐 **多人联机** - 局域网和互联网联机对战
- 🎨 **丰富皮肤** - 多套界面主题，支持自定义
- 🔧 **强大扩展** - 基于Lua的插件系统
- 🌍 **多语言支持** - 完整的国际化框架
- 📱 **跨平台** - Windows、Linux、macOS全支持

### 🎯 项目数据

| 内容类型 | 数量 | 说明 |
|----------|------|------|
| **武将角色** | 300+ | 涵盖所有官方扩展包 |
| **卡牌种类** | 200+ | 基本牌、锦囊牌、装备牌 |
| **扩展包** | 30+ | 官方和社区扩展 |
| **游戏模式** | 10+ | 标准、国战、1v1、3v3等 |
| **AI脚本** | 50+ | 各扩展包智能AI |

---

## 🚀 快速开始

### 系统要求

| 组件 | 最低要求 | 推荐配置 |
|------|----------|----------|
| **操作系统** | Windows 7+ / Ubuntu 16.04+ / macOS 10.12+ | Windows 10+ / Ubuntu 20.04+ |
| **内存** | 2GB RAM | 4GB+ RAM |
| **存储** | 1GB 可用空间 | 2GB+ 可用空间 |
| **显卡** | 支持OpenGL 2.0 | 独立显卡 |

### 💾 安装运行

#### 方式一：直接运行（推荐）
```bash
# 1. 下载项目
git clone https://github.com/Mogara/QSanguosha-v2.git
cd QSanguosha-v2

# 2. 直接运行
./QSanguosha.exe  # Windows
./QSanguosha      # Linux/macOS
```

#### 方式二：从源码编译
```bash
# 安装Qt开发环境
# Ubuntu: sudo apt-get install qt5-default
# CentOS: sudo yum install qt5-qtbase-devel

# 编译项目
qmake QSanguosha.pro
make
./QSanguosha
```

### 🎮 开始游戏

1. **启动游戏** - 运行QSanguosha.exe
2. **选择模式** - 单机练习或联机对战
3. **配置游戏** - 选择扩展包和游戏规则
4. **开始对局** - 享受三国杀的策略乐趣！

---

## 📖 文档中心

我们提供了完整的文档体系，涵盖从入门到高级开发的各个方面：

### 📚 核心文档

| 文档 | 描述 | 适合人群 | 预计时间 |
|------|------|----------|----------|
| [🎯 项目介绍与分析](docs/项目介绍与分析.md) | 项目概述、技术架构、核心特色 | 初学者、技术人员 | 30分钟 |
| [📁 文件夹构成分析](docs/QSanguosha-v2文件夹构成详细分析.md) | 项目结构、目录功能、架构设计 | 开发者 | 20分钟 |

### 🎨 界面定制

| 文档 | 描述 | 适合人群 | 预计时间 |
|------|------|----------|----------|
| [🎨 界面美化指南](docs/界面美化完整指南.md) | 背景更换、按钮定制、皮肤制作 | 美化爱好者、UI设计师 | 2小时 |

### 🛠️ 开发指南

| 文档 | 描述 | 适合人群 | 预计时间 |
|------|------|----------|----------|
| [⚔️ 武将开发指南](docs/武将开发完整指南.md) | 武将创建、技能实现、AI开发 | 内容创作者、Lua开发者 | 6小时 |
| [🃏 卡牌制作指南](docs/QSanguosha-v2卡牌制作完整指南.md) | 卡牌设计、效果实现、平衡调整 | 卡牌设计师 | 4小时 |
| [🏛️ 新势力添加指南](docs/新势力添加指南.md) | 势力创建、配置设置、资源准备 | 扩展开发者 | 2小时 |

### 🚀 快速导航

**新手推荐学习路径：**
1. 📖 阅读《项目介绍与分析》了解基础
2. 🎨 跟随《界面美化指南》个性化界面
3. ⚔️ 学习《武将开发指南》创建内容
4. 🏛️ 参考《新势力添加指南》扩展游戏

**完整文档中心：** [📚 docs/README.md](docs/README.md)

---

## 🛠️ 开发指南

### 🏗️ 技术栈

| 技术 | 版本 | 用途 |
|------|------|------|
| **Qt** | 5.x | 跨平台GUI框架 |
| **Lua** | 5.1+ | 脚本引擎和扩展开发 |
| **C++** | C++11+ | 核心游戏逻辑 |
| **SQLite** | 3.x | 数据存储 |

### 📁 项目结构

```
QSanguosha-v2/
├── 🎮 核心系统
│   ├── lua/           # Lua脚本引擎
│   ├── extensions/    # 扩展包系统
│   └── scenarios/     # 游戏场景
├── 🎨 资源文件
│   ├── image/         # 图片资源
│   ├── audio/         # 音频资源
│   ├── font/          # 字体文件
│   └── skins/         # 界面皮肤
├── 🌐 本地化
│   └── lang/          # 多语言支持
└── 📖 文档
    └── docs/          # 完整文档
```

### 🔧 开发环境

#### 推荐工具
- **Qt Creator** - 主要IDE
- **Visual Studio Code** - Lua脚本编辑
- **GIMP/Photoshop** - 图像处理
- **Git** - 版本控制

#### 快速开发示例

**创建武将：**
```lua
local new_general = sgs.General(
    "general_name",     -- 武将名称
    "kingdom",          -- 所属势力
    4,                  -- 体力值
    true,               -- 是否为男性
    {"skill1", "skill2"} -- 技能列表
)
```

**创建卡牌：**
```lua
local new_card = sgs.CreateBasicCard{
    name = "card_name",
    suit = sgs.Card_Heart,
    number = 5,
    on_effect = function(self, effect)
        -- 卡牌效果实现
    end
}
```

---

## 🎨 自定义与扩展

### 🖼️ 界面美化
- **背景更换** - 支持自定义游戏背景
- **皮肤制作** - 完整的主题系统
- **按钮定制** - 自定义按钮样式
- **布局调整** - 灵活的界面配置

### 🔧 内容扩展
- **武将创建** - 使用Lua创建新武将
- **卡牌设计** - 实现自定义卡牌效果
- **模式开发** - 开发新的游戏模式
- **AI编程** - 为新内容编写AI逻辑

### 📦 扩展包管理
```bash
# 启用扩展包
# 编辑 lua/config.lua
package_names = {
    "StandardCard",
    "your_extension",  -- 添加你的扩展包
}
```

---

## 🤝 贡献指南

### 🌟 参与方式

| 贡献类型 | 描述 | 如何参与 |
|----------|------|----------|
| **🐛 Bug报告** | 发现并报告问题 | [提交Issue](https://github.com/Mogara/QSanguosha-v2/issues) |
| **💡 功能建议** | 提出新功能想法 | [功能请求](https://github.com/Mogara/QSanguosha-v2/issues) |
| **📝 文档改进** | 完善项目文档 | [提交PR](https://github.com/Mogara/QSanguosha-v2/pulls) |
| **🎨 内容创作** | 武将、卡牌、皮肤 | [分享作品](https://github.com/Mogara/QSanguosha-v2/discussions) |
| **🔧 代码贡献** | 修复bug、新功能 | [提交PR](https://github.com/Mogara/QSanguosha-v2/pulls) |

### 📋 贡献流程
1. **Fork项目** → 2. **创建分支** → 3. **提交更改** → 4. **创建PR**

### 📏 代码规范
- **C++代码** - 遵循Qt编码规范
- **Lua脚本** - 4空格缩进，清晰注释
- **文档** - Markdown格式，简洁明了

---

## 📞 获取帮助

### 🆘 常见问题

<details>
<summary><strong>Q: 游戏无法启动怎么办？</strong></summary>

**A:** 请检查：
1. Qt运行库是否正确安装
2. 系统是否满足最低要求
3. 以管理员权限运行
4. 查看错误日志文件
</details>

<details>
<summary><strong>Q: 如何添加新武将？</strong></summary>

**A:** 请参考：
1. 阅读[武将开发指南](docs/武将开发完整指南.md)
2. 在`extensions/`目录创建Lua文件
3. 在`lua/config.lua`中启用扩展包
</details>

<details>
<summary><strong>Q: 联机游戏连接失败？</strong></summary>

**A:** 请检查：
1. 网络连接是否正常
2. 防火墙设置
3. 服务器地址和端口
4. 尝试局域网模式
</details>

### 💬 社区支持
- **GitHub Issues** - [问题报告](https://github.com/Mogara/QSanguosha-v2/issues)
- **GitHub Discussions** - [社区讨论](https://github.com/Mogara/QSanguosha-v2/discussions)
- **官方论坛** - [QSanguosha.org](http://qsanguosha.org)

---

## 🔗 相关链接

### 🌐 官方资源
- **GitHub仓库** - [Mogara/QSanguosha-v2](https://github.com/Mogara/QSanguosha-v2)
- **发布页面** - [Releases](https://github.com/Mogara/QSanguosha-v2/releases)
- **项目Wiki** - [Wiki文档](https://github.com/Mogara/QSanguosha-v2/wiki)

### 📚 学习资源
- **Qt文档** - [Qt Documentation](https://doc.qt.io/)
- **Lua指南** - [Lua.org](https://www.lua.org/docs.html)
- **三国杀规则** - [官方规则](http://www.sanguosha.com/)

---

## 📄 许可证

本项目采用 [GPL v3](LICENSE) 许可证。

```
QSanguosha-v2 - 开源三国杀游戏平台
Copyright (C) 2010-2024 Mogara Team

This program is free software: you can redistribute it and/or modify
it under the terms of the GNU General Public License as published by
the Free Software Foundation, either version 3 of the License, or
(at your option) any later version.
```

---

## 🙏 致谢

感谢所有为QSanguosha-v2项目做出贡献的开发者、设计师、测试人员和社区成员！

- **Mogara Team** - 项目核心开发团队
- **社区贡献者** - 武将、卡牌、皮肤等内容创作
- **Qt Project** - 优秀的跨平台框架
- **Lua.org** - 轻量级脚本语言

---

<div align="center">

### 🌟 如果这个项目对你有帮助，请给我们一个Star！

**开始你的QSanguosha-v2开发之旅吧！** 🚀

[⬆️ 回到顶部](#qsanguosha-v2-)

</div>
