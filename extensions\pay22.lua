﻿module("extensions.pay22", package.seeall)
extension = sgs.Package("pay22")

kosuzu = sgs.General(extension,"kosuzu","luacai",3,false,false,false)
kutaka = sgs.General(extension,"kutaka","luadi",3,false,false,false)
kutakaA = sgs.General(extension,"kutakaA","luadi",3,false,true,true)
kutakaB = sgs.General(extension,"kutakaB","luadi",3,false,true,true)
kutakaC = sgs.General(extension,"kutakaC","luadi",3,false,true,true)
komachi = sgs.General(extension,"komachi","luadi",3,false,false,false)

shizuha = sgs.General(extension,"shizuha","luafeng",3,false,false,false)
mayumi = sgs.General(extension,"mayumi","god",99,false,false,false,1)
ichirin = sgs.General(extension,"ichirin","lualian", 4,false,false,false)
htmsken = sgs.General(extension,"htmsken$","qun", 5,true,true,true) 
minamoto = sgs.General(extension,"minamoto$","luafeng", 4,false,false,false)
renko = sgs.General(extension,"renko","qun", 3,false,false,false)
rinnosuke = sgs.General(extension,"rinnosuke","luacai", 3,true,false,false)
eika = sgs.General(extension,"eika","luadi", 2,false,false,false)

local function isFriendQ(room, target, player)
	if player:getRole() == "rebel" then
		return target:getRole() == "rebel"
	end
	if player:getRole() == "loyalist" or player:getRole() == "lord" then
		return target:getRole() == "loyalist" or target:getRole() == "lord"
	end
end

luazhishu_list = {}

luazhishu = sgs.CreateTriggerSkill{
	name = "luazhishu",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.CardsMoveOneTime, sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.CardsMoveOneTime then
			luazhishu_list = {}
			local move = data:toMoveOneTime()
			if not move.from then return false end
			if not move.from:hasSkill(self:objectName()) then return false end
			for _,id in sgs.qlist(move.card_ids) do
				if bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) == sgs.CardMoveReason_S_REASON_DISCARD then
					table.insert(luazhishu_list, id)
				end
			end
		elseif event == sgs.EventPhaseEnd and player:getPhase() == sgs.Player_Discard then
			local ids_A = sgs.IntList()
			for _,id in pairs(luazhishu_list) do
				if not sgs.Sanguosha:getCard(id):isKindOf("BasicCard") then
					if room:getCardPlace(id) == sgs.Player_DiscardPile then ids_A:append(id) end
				end
			end
			if ids_A:length() > 0 and room:askForSkillInvoke(player, self:objectName(), data) then
				local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				dummy:addSubcards(ids_A)
				player:addToPile("Shu", dummy)
				local discard_ids = room:getDiscardPile()
				local trickcard = sgs.IntList()
				for _, id in sgs.qlist(discard_ids) do
					local card = sgs.Sanguosha:getCard(id)
					if card:isKindOf("ExNihilo") or card:isKindOf("Peach") then
						trickcard:append(id)
					end
				end
				if trickcard:length() > 0 then
					room:fillAG(trickcard, player)
					local card_id = room:askForAG(player, trickcard, false, "luazhishu")
					local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
					dummy_0:addSubcard(card_id)
					room:clearAG()
					player:obtainCard(dummy_0)
				end
			end
		end
		return false
	end
}

luahanling = sgs.CreateTriggerSkill{
	name = "luahanling" ,
	priority = 10,
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local kosuzu = room:findPlayerBySkillName("luahanling")
		if not kosuzu or (not kosuzu:isAlive()) then return end
		for _, p in sgs.qlist(room:getAllPlayers()) do
			if not p:hasSkill("luahanling2") and not p:hasSkill("luahanling") then room:writeToConsole("兼听测试" .. p:objectName()); room:attachSkillToPlayer(p, "luahanling2") end
		end
	end
}

luazhishu2 = sgs.CreateTriggerSkill{
	name = "#luazhishu",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damaged},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.Damaged then
			local damage = data:toDamage()
			if damage.damage == 0 then return false end
			if player:objectName() == damage.to:objectName() and player:hasSkill("luazhishu") and player:getPile("Shu")
				and player:getPile("Shu"):length() > 0 and damage.nature == sgs.DamageStruct_Fire then
				local qizhi = player:getPile("Shu")
				local dummy = sgs.Sanguosha:cloneCard("jink")
				if not qizhi:isEmpty() then
					room:fillAG(qizhi, player)
					local card_id = room:askForAG(player, qizhi, false, "luazhishu2")
					local card = sgs.Sanguosha:getCard(card_id)
					if not card then return false end
					dummy:addSubcard(card)
					room:throwCard(dummy, player, player)
					qizhi:removeOne(card_id)
					room:clearAG(player)
				end
			end
		end
	end
}

luayaojuanCard = sgs.CreateSkillCard{
	name = "luayaojuan",
	filter = function(self, targets, to_select)
		return (#targets == 0)
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		local skill_list = {}
		for _, skill in sgs.qlist(effect.from:getVisibleSkillList(true)) do
			table.insert(skill_list, skill:objectName())
		end
		local skill = room:askForChoice(effect.from, "luayaojuan", table.concat(skill_list,"+"))
		room:detachSkillFromPlayer(effect.from, skill)
		effect.from:getRoom():damage(sgs.DamageStruct("luayaojuan", effect.from, effect.to, 2))
	end
}
luayaojuan = sgs.CreateZeroCardViewAsSkill{
	name = "luayaojuan",
	view_as = function(self, cards)
		return luayaojuanCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:hasSkill("luayaojuan") and player:getPile("Shu") and player:getPile("Shu"):length() > 3 and not player:hasUsed("#luayaojuan")
	end,
}
kosuzu:addSkill(luazhishu)
kosuzu:addSkill(luazhishu2)
kosuzu:addSkill(luahanling)
kosuzu:addSkill(luayaojuan)



luafenshui = sgs.CreateTriggerSkill{
	name = "luafenshui",
	global = true,
	events = {sgs.EventPhaseEnd},
	view_as_skill = luafenshuiVS,
	on_trigger = function(self, triggerEvent, player, data)
		if triggerEvent == sgs.EventPhaseEnd then
			local room = player:getRoom()
			if room:getCurrent():objectName() == player:objectName() and player:getPhase() == sgs.Player_Play then
				for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
					if p:getHandcardNum() == 2 and room:askForDiscard(p, self:objectName(), 1, 1, true, true, "@luafenshui") then
						local judge = sgs.JudgeStruct()
						judge.pattern = "."
						judge.good = true
						judge.reason = self:objectName()
						judge.who = player
						room:judge(judge)
						if judge.card:isRed() then
							local _data = sgs.QVariant()
							_data:setValue(player)
							room:setTag("luafenshuiTP", _data)
							local choice = room:askForChoice(p, "luafenshui", "recover+loseHp", _data)
							if choice == "recover" then
								if player:isWounded() then
									room:recover(player, sgs.RecoverStruct(p, nil, 1))
								end
							else
								local function canLoseHp()
									for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
										if hecatiaX and isFriendQ(room, player, hecatiaX) and player:objectName() ~= hecatiaX:objectName()
												and player:getHp() == hecatiaX:getHp() then
											room:notifySkillInvoked(hecatiaX, "luayiti")
											return false
										end 
									end 
									for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
										if Erin and Erin:getKingdom() == player:getKingdom() then
											room:notifySkillInvoked(Erin, "luajiance")
											return false
										end 
									end 
									return true
								end 
								if canLoseHp() then room:loseHp(player, 1) end 
							end
						else
							local _data = sgs.QVariant()
							_data:setValue(player)
							room:setTag("luafenshuiTP", _data)
							local x = player:getLostHp()
							if x > 0  then
								local choice = room:askForChoice(p, "luafenshui2", "draw+discard", _data)
								if choice == "discard" then
									room:askForDiscard(player, "luafenshui2", x, x, false, true)
								else
									player:drawCards(x)
								end
							end
						end
					end
				end
			end
		end
	end
}

chentiCard = sgs.CreateSkillCard{
	name = "luachenti",
	filter = function(self, targets, to_select)
		return true
	end,
	on_use = function(self, room, source, targets)
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			room:setPlayerMark(p, "@chenti", 0)
		end
		for _, p in ipairs(targets) do
			room:setPlayerMark(p, "@chenti", 1)
		end
	end
}
luachenti = sgs.CreateZeroCardViewAsSkill{
	name = "luachenti",
	view_as = function(self, cards)
		return chentiCard:clone()
	end,
	enabled_at_play = function(self, player)
		return true
	end,
}

luachenti2 = sgs.CreateTriggerSkill{
	name = "#luachenti" ,
	events = {sgs.HpRecover} ,
	frequency = sgs.Skill_Frequent ,
	global = true,
	on_trigger = function(self, event, player, data)
		if event == sgs.HpRecover then
			local room = player:getRoom()
			local recover = data:toRecover()
			if player:getMark("@chenti") > 0 then
				for _, p in sgs.qlist(room:findPlayersBySkillName("luachenti")) do
					if room:askForSkillInvoke(player, "luachenti") then
						player:drawCards(1)
						room:setPlayerFlag(player, "luaaoshuNull")
						room:askForUseCard(player, "TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand",
						 "@LuaQiyuan", -1, sgs.Card_MethodUse, false)
						room:setPlayerFlag(player, "-luaaoshuNull")
					end
				end
			end
		end
		return false
	end
}

kutaka:addSkill(luafenshui)
kutaka:addSkill(luachenti)
kutaka:addSkill(luachenti2)

kutakaA:addSkill(luafenshui)
kutakaA:addSkill(luachenti)

kutakaB:addSkill(luafenshui)
kutakaB:addSkill(luachenti)

kutakaC:addSkill(luafenshui)
kutakaC:addSkill(luachenti)

luahongyeCard = sgs.CreateSkillCard{
	name = "luahongye",
	will_throw = false,
	filter = function(self, targets, to_select)
		if sgs.Self:getHandcardNum() == 3 then
			return to_select:objectName() == sgs.Self:objectName()
		else
			return #targets == 0
		end
	end,
	on_use = function(self, room, source, targets)
		if source:getHandcardNum() == 3 then
			room:setPlayerFlag(source, "luahongye1")
			room:throwCard(self:getSubcards():first(), source, source)
			source:drawCards(1)
		else
			room:setPlayerFlag(source, "luahongye2")
			local id = room:askForCardChosen(source, targets[1], "hej", self:objectName(), false, sgs.Card_MethodDiscard)
			for _, c in sgs.qlist(targets[1]:getCards("hej")) do
				room:setCardFlag(c, "-AIGlobal_SDCardChosen_".."dismantlement")
			end
			room:throwCard(id, targets[1], source)
		end

	end
}
luahongye = sgs.CreateViewAsSkill{
	name = "luahongye" ,
	n = 1 ,
	view_filter = function(self, selected, to_select)
		if sgs.Self:getHandcardNum() == 3 then
			return to_select:isKindOf("BasicCard")
		end
		return true
	end ,
	view_as = function(self, cards)
		if #cards == 0 and sgs.Self:getHandcardNum() == 3 then return nil end
		if #cards ~= 0 and sgs.Self:getHandcardNum() == 2 then return nil end
		local yizuo_card = luahongyeCard:clone()
		if #cards > 0 then
			for _, c in ipairs(cards) do
				yizuo_card:addSubcard(c)
			end
		end
		return yizuo_card
	end ,
	enabled_at_play = function(self, player)
		return (player:getHandcardNum() == 3 and not player:hasFlag("luahongye1")) or (player:getHandcardNum() == 2 and not player:hasFlag("luahongye2"))
	end
}
luahongye2 = sgs.CreateTriggerSkill{
	name = "#luahongye",
	frequency = sgs.Skill_Frequent,
	events = {sgs.Damaged},
	on_trigger = function(self, event, player, data, room)
		local damage = data:toDamage()
		if damage.damage == 0 then return false end
		if player:hasSkill("luahongye") and not player:hasFlag("luahongye4") and player:isKongcheng()
			and room:askForSkillInvoke(player, "luahongye") then
			room:setPlayerFlag(player, "luahongye4")
			room:recover(player, sgs.RecoverStruct(player, nil, 1))
			player:drawCards(1)
		end
	end
}
luahongye3 = sgs.CreateTriggerSkill{
	name = "#luahongye2" ,
	global = true,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Start and player:objectName() == room:getCurrent():objectName() then
			local toziko = player:getRoom():findPlayerBySkillName("luahongye")
			if toziko then
				if toziko:hasFlag("luahongye1") then room:setPlayerFlag(toziko, "-luahongye1") end
				if toziko:hasFlag("luahongye2") then room:setPlayerFlag(toziko, "-luahongye2") end
				if toziko:hasFlag("luahongye4") then room:setPlayerFlag(toziko, "-luahongye4") end
			end
		end
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}
luashenqiu = sgs.CreateTriggerSkill{
	name = "luashenqiu",
	events = {sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseChanging and data:toPhaseChange().to == sgs.Player_NotActive then
			local x = 1
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:isKongcheng() then x = x + 1 end
			end
			if player:getHp() < x then
				local r = math.min(player:getMaxHp(),x) - player:getHp()
				room:recover(player, sgs.RecoverStruct(player, nil, r))
			end
		end
	end
}

shizuha:addSkill(luahongye)
shizuha:addSkill(luahongye2)
shizuha:addSkill(luahongye3)
shizuha:addSkill(luashenqiu)

luasilian = sgs.CreateTriggerSkill{
	name = "luasilian",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.DamageCaused},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if (not damage.card or (not damage.card:isKindOf("AOE"))) and damage.by_user and not damage.chain and not damage.transfer and damage.to:isAlive() then
			if room:askForSkillInvoke(player, self:objectName(), data) then
				local judge = sgs.JudgeStruct()
				judge.pattern = ".|heart"
				judge.good = true
				judge.reason = self:objectName()
				judge.who = damage.to
				room:judge(judge)
				if judge:isGood() then
					if damage.to and not damage.to:isNude() then
						local card_id = room:askForCardChosen(player, damage.to, "he", self:objectName())
						local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, player:objectName())
						room:obtainCard(player, sgs.Sanguosha:getCard(card_id), reason, room:getCardPlace(card_id) ~= sgs.Player_PlaceHand)
					end
				else
                    damage.to:gainMark("@luasilian")
				end
			end
		end
		return false
	end
}
luasilian2 = sgs.CreateTriggerSkill
{
	name = "#luasilian2",
	events = {sgs.EventPhaseStart},
	global = true,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:objectName() == room:getCurrent():objectName() and player:getPhase() == sgs.Player_Start then
			if player:getMark("@luasilian") > 0 then
				local function canLoseHp()
					for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
						if hecatiaX and isFriendQ(room, player, hecatiaX) and player:objectName() ~= hecatiaX:objectName()
								and player:getHp() == hecatiaX:getHp() then
							room:notifySkillInvoked(hecatiaX, "luayiti")
							return false
						end 
					end 
					for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
						if Erin and Erin:getKingdom() == player:getKingdom() then
							room:notifySkillInvoked(Erin, "luajiance")
							return false
						end 
					end 
					return true
				end 
				if canLoseHp() then room:loseHp(player, 1) end 
			end
		end
	end
}
luachuandu = sgs.CreateDistanceSkill{
	name = "luachuandu",
	correct_func = function(self, from, to)
		if from:hasSkill("luachuandu") and to:isWounded() then
			return -2
		end
	end,
}
luaguihang = sgs.CreateTriggerSkill{
	name = "luaguihang",
	events = {sgs.Dying},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local dying = data:toDying()
		local _player = dying.who
		if _player:objectName() == player:objectName() then return false end
		if _player:getHp() ~= 0 then return false end
		if not _player:isKongcheng() and not player:isKongcheng() and room:askForSkillInvoke(player, self:objectName(), data) then
			room:setPlayerFlag(_player, "luaguihang_Target")
			local success = player:pindian(_player, "luaguihang", nil)
			if success then
				room:setPlayerProperty(_player, "hp", sgs.QVariant(-1))
			else

			end
			room:setPlayerFlag(_player, "-luaguihang_Target")
		end
	end
}
luaguihang2 = sgs.CreateTriggerSkill{
	name = "#luaguihang",
	events = {sgs.Pindian},
	on_trigger = function(self, event, player, data)
		local pindian = data:toPindian()
		local winner
		local loser
		if pindian.from_number > pindian.to_number then
			winner = pindian.from
			loser = pindian.to
		elseif pindian.from_number < pindian.to_number then
			winner = pindian.to
			loser = pindian.from
		end
		if player:hasSkill("luaguihang") and pindian.reason == "luaguihang" and (not loser or loser:objectName() == player:objectName()) then
			player:obtainCard(pindian.to_card)
		end
		return false
	end,
	priority = -1
}
komachi:addSkill(luasilian)
komachi:addSkill(luasilian2)
komachi:addSkill(luachuandu)
komachi:addSkill(luaguihang)
komachi:addSkill(luaguihang2)


luajuntuan = sgs.CreateTriggerSkill{
	name = "luajuntuan",
	events = {sgs.RoundStart, sgs.HpRecover, sgs.TargetConfirmed},
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data, room)
		if event == sgs.HpRecover then
			player:gainMark("@luajuntuan")
			if player:getMark("luabu") > 0 then
				local targets = sgs.SPlayerList()
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if p:isWounded() and p:objectName() ~= player:objectName() then
						targets:append(p)
					end
				end
				local to = room:askForPlayerChosen(player, targets, "luajuntuan", "luajuntuan-invoke", true, true)
				if to then
					room:recover(to, sgs.RecoverStruct(player, nil, 1))
				end
			end
			if player:getMark("@luajuntuan") == 3 then
				room:handleAcquireDetachSkills(player, "luajiang")
				player:addMark("luagong")
			elseif player:getMark("@luajuntuan") == 6 then
				room:handleAcquireDetachSkills(player, "luahongyuan")
			elseif player:getMark("@luajuntuan") == 12 then
				player:addMark("luabu")
			elseif player:getMark("@luajuntuan") == 20 then
				room:recover(player, sgs.RecoverStruct(player, nil, player:getLostHp()))
				room:acquireSkill(player, "mashu")
			end
		elseif event == sgs.TargetConfirmed then
			if player:getMark("luagong") > 0 then
				local use = data:toCardUse()
				if (use.from and use.from:objectName() == player:objectName()) then
					if use.card:isKindOf("Slash") and room:askForSkillInvoke(player, "luajuntuan", data) then
						local archery_attack = sgs.Sanguosha:cloneCard("archery_attack", sgs.Card_NoSuit, 0)
						local targets = sgs.SPlayerList()
						local targets2 = sgs.SPlayerList()
						for _, p in sgs.qlist(room:getAlivePlayers()) do
							if not player:isProhibited(p, archery_attack, player:getSiblings()) and p:objectName() ~= player:objectName() then
								targets:append(p)
							end
						end
						if targets:length() < 2 then return false end
						local to1 = room:askForPlayerChosen(player, targets, "luajuntuan2", "luajuntuan2-invoke", true, true)
						if not to1 then return false end
						targets:removeOne(to1)
						targets2:append(to1)
						local to2 = room:askForPlayerChosen(player, targets, "luajuntuan2", "luajuntuan2-invoke", true, true)
						targets:removeOne(to2)
						targets2:append(to2) 
						room:setPlayerFlag(player, "luajuntuanAOE")
						for _, p in sgs.qlist(targets2) do
							room:setPlayerFlag(p, "luajuntuanAOEs")
						end
						room:useCard(sgs.CardUseStruct(archery_attack, player, sgs.SPlayerList()), true)
						room:setPlayerFlag(player, "-luajuntuanAOE")
						for _, p in sgs.qlist(targets2) do
							room:setPlayerFlag(p, "-luajuntuanAOEs")
						end
						return false
					end
				end
			end
		end
	end
}
luajuntuan2 = sgs.CreateTriggerSkill{
	name = "#luajuntuan",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.PreCardUsed},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.PreCardUsed then
			local use = data:toCardUse()
			local card = use.card
			if use.from:hasFlag("luajuntuanAOE") then
				use.to = sgs.SPlayerList()
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if p:hasFlag("luajuntuanAOEs") then use.to:append(p) end
				end
			end
			data:setValue(use)
			return false
		end
	end,
	can_trigger = function(self, target)
		return target
	end,
}
luajuntuan3 = sgs.CreateTriggerSkill{
	name = "#luajuntuan2",
	global = true,
	priority = 10,
	events = {sgs.TurnStart},
	--frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data, room)
		if room:getCurrent():isLord() and player:getMark("@extra_turn") == 0 and room:getCurrent():objectName() == player:objectName() then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName("luajuntuan")) do
				room:writeToConsole("mayumi tewst")
				room:recover(p2, sgs.RecoverStruct(p2))
			end
		end
	end
}

luajuntuan4 = sgs.CreateTriggerSkill{
	name = "#luajuntuan4" ,
	global = true,
	events = {sgs.DamageCaused} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.DamageCaused then
			local damage = data:toDamage()
			if damage.to and damage.to:isAlive() and damage.from and damage.from:objectName() == player:objectName() and damage.from:getMark("@luajuntuan") > 19
					and damage.from:hasSkill("luajuntuan") and damage.to:objectName() ~= player:objectName() then
				damage.damage = damage.damage + damage.from:getHp()
				data:setValue(damage)
			end
		end
	end
}
mayumi:addSkill(luajuntuan)
mayumi:addSkill(luajuntuan2)
mayumi:addSkill(luajuntuan3)
mayumi:addSkill(luajuntuan4)

luashenquan2 = sgs.CreateTriggerSkill{
	name = "#luashenquan",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.PreCardUsed},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.PreCardUsed then
			local use = data:toCardUse()
			local card = use.card
			if use.from:hasFlag("shenquanAOE") then
				use.to = sgs.SPlayerList()
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if p:hasFlag("shenquanAOEs") then use.to:append(p) end
				end
			end
			data:setValue(use)
			return false
		end
	end,
	can_trigger = function(self, target)
		return target
	end,
}

luashenquanCard = sgs.CreateSkillCard{
	name = "luashenquan",
	will_throw = true,
	filter = function(self, targets, to_select)
		local card = sgs.Sanguosha:getCard(self:getSubcards():at(0))
		return #targets < 2 and not sgs.Self:isProhibited(to_select, card, to_select:getSiblings())
				and not (card:isKindOf("Peach") and not to_select:isWounded())
	end,
	feasible = function(self, targets)
		return #targets == 2
	end,
	on_use = function(self, room, source, targets)
		local card0 = sgs.Sanguosha:getCard(self:getSubcards():at(0))

		if card0:isKindOf("AOE") or card0:isKindOf("AmazingGrace") or card0:isKindOf("GodSalvation") then
			room:setPlayerFlag(source, "shenquanAOE")
			for _, p in ipairs(targets) do
				room:setPlayerFlag(p, "shenquanAOEs")
			end
			room:useCard(sgs.CardUseStruct(card0, source, sgs.SPlayerList()))
			room:setPlayerFlag(source, "-shenquanAOE")
			for _, p in ipairs(targets) do
				room:setPlayerFlag(p, "-shenquanAOEs")
			end
			return false
		end
		local splist = sgs.SPlayerList()
		for _, p in ipairs(targets) do
			splist:append(p)
		end
		room:useCard(sgs.CardUseStruct(card0, source, splist))

	end
}
luashenquan3 = sgs.CreateTriggerSkill{
	name = "#luashenquan2",
	global = true,
	priority = 2,
	events = {sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data, room)
		local change = data:toPhaseChange()
		if change.to == sgs.Player_NotActive and room:getCurrent():objectName() == player:objectName() then
			for _, splayer in sgs.qlist(room:getAlivePlayers()) do

				if splayer:hasFlag("luashenquan") then
					local function canLoseHp()
						for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
							if hecatiaX and isFriendQ(room, splayer, hecatiaX) and splayer:objectName() ~= hecatiaX:objectName()
									and splayer:getHp() == hecatiaX:getHp() then
								room:notifySkillInvoked(hecatiaX, "luayiti")
								return false
							end 
						end 
						for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
							if Erin and Erin:getKingdom() == splayer:getKingdom() then
								room:notifySkillInvoked(Erin, "luajiance")
								return false
							end 
						end 
						return true
					end 
					if canLoseHp() then room:loseHp(splayer, 1) end 
					room:setPlayerFlag(splayer, "-luashenquan")
				end
			end
		end
	end
}
luashenquanVS = sgs.CreateOneCardViewAsSkill{
	name = "luashenquan",
	view_filter = function(self, card)
		if card:isKindOf("TrickCard") and not card:isNDTrick() then
			return false
		end
		return not card:isKindOf("EquipCard") and not card:isKindOf("Jink") and not card:isKindOf("Nullification")
				and not sgs.Self:isCardLimited(card, sgs.Card_MethodUse)
	end,
	view_as = function(self, card)
		local qnc = luashenquanCard:clone()
		qnc:addSubcard(card)
		qnc:setSkillName(self:objectName())
		return qnc
	end,
	enabled_at_play = function(self,player)
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return string.startsWith(pattern, "@@luashenquan")
	end
}
luashenquan = sgs.CreateTriggerSkill{
	name = "luashenquan",
	events = {sgs.CardFinished, sgs.Damage},
	view_as_skill = luashenquanVS,
	on_trigger = function(self, event, player, data, room)
		local use = data:toCardUse()
		if use.from and use.from:objectName() == player:objectName() and use.to:length() == 1 and use.to:at(0):objectName() ~= player:objectName()
				and use.card and not use.card:isKindOf("Jink") and not use.card:isKindOf("SkillCard")
				and room:askForSkillInvoke(player, self:objectName()) and room:askForDiscard(player, self:objectName(), 1, 1, true, true) then
			room:setPlayerFlag(player, "luashenquan")
			player:drawCards(1)
			if room:askForUseCard(player, "@@luashenquan", "@luashenquan") then

			end
		end
	end
}

luarudao = sgs.CreateTriggerSkill{
	name = "luarudao",
	events = {sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data, room)
		local change = data:toPhaseChange()
		if change.to == sgs.Player_NotActive and room:getCurrent():objectName() == player:objectName() and player:faceUp()
			and player:askForSkillInvoke(self:objectName()) then
			player:addMark("luarudao")
			room:setPlayerMark(player, "@extra_turn", 1)
			player:gainAnExtraTurn()
			room:setPlayerMark(player, "@extra_turn", 0)
		elseif change.to == sgs.Player_RoundStart and player:getMark("luarudao") > 0 then
			player:turnOver()
			room:setPlayerMark(player, "luarudao", 0)
		end
	end
}
ichirin:addSkill(luashenquan3)
ichirin:addSkill(luashenquan2)
ichirin:addSkill(luashenquan)
ichirin:addSkill(luarudao)

luashiyong = sgs.CreateTriggerSkill{
	name = "luashiyong",
	events = sgs.DamageCaused,
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local room = player:getRoom()
		if damage.from and damage.from:objectName() and damage.from:getHp() >= damage.to:getHp() and damage.to:hasSkill("luashiyong") then
			damage.damage = damage.damage + 1
			data:setValue(damage)
		end
	end,
	can_trigger = function(self, target)
		return target and target:isAlive()
	end
}
htmsken:addSkill(luashiyong)
luajisi = sgs.CreateTriggerSkill{
	name = "luajisi",
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if not p:hasSkill("luajisir") then room:writeToConsole("luajisir test" .. p:objectName()); room:attachSkillToPlayer(p, "luajisir") end
				end
			end
		end
	end
}

htmsken:addSkill(luajisi)

luagengxinCard = sgs.CreateSkillCard{
	name = "luagengxin",
	filter = function(self, targets, to_select)
		return (#targets == 0) and not to_select:isKongcheng()
	end,
	on_effect = function(self, effect)
		local list = effect.to:getHandcards()
		local ids =  sgs.IntList()
		for _, card in sgs.qlist(list) do
			ids:append(card:getId())
		end
		local room = effect.to:getRoom()
		room:fillAG(ids)
		room:getThread():delay()
		room:getThread():delay()
		room:clearAG()
		local id2 = room:askForCardChosen(effect.from, effect.to, "h", self:objectName(), true, sgs.Card_MethodDiscard)
		if id2 then
			local y = 0 - sgs.Sanguosha:getCard(id2):getNumber()
			room:throwCard(id2, effect.to, effect.from)
			local choices = {"1", "2", "3", "4", "5", "6", "7", "8"}
			local choice = room:askForChoice(effect.to, "luagengxin", table.concat(choices, "+"))
			local x = tonumber(choice)
			local card_ids = room:getNCards(x)
			for _, id in sgs.qlist(card_ids) do
				local _card = sgs.Sanguosha:getCard(id)
				y = y + _card:getNumber()
			end
			room:fillAG(card_ids)
			room:getThread():delay()
			room:getThread():delay()
			room:clearAG()
			local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			dummy_0:addSubcards(card_ids)
			room:clearAG()
			if y < 0 then
				effect.to:obtainCard(dummy_0)
			end
		end
	end
}
luagengxin = sgs.CreateZeroCardViewAsSkill{
	name = "luagengxin$",
	view_as = function(self, cards)
		return luagengxinCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:hasLordSkill(self:objectName()) and not player:hasUsed("#luagengxin")
	end,
}

htmsken:addSkill(luagengxin)

local function firstToUpper(str)
	return (str:gsub("^%l", string.upper))
end
jinyuCard = sgs.CreateSkillCard{
	name = "luajinyu",
	handling_method = sgs.Card_MethodDiscard,
	filter = function(self, targets, to_select)
		if #targets >= 1 then return false end
		return true
	end,
	on_effect = function(self, effect)
		local room = effect.to:getRoom()
		room:setPlayerMark(effect.to, "@skill_invalidity", 1)
		effect.from:drawCards(1)
		room:setPlayerCardLimitation(effect.from, "use,response,discard", firstToUpper(card:objectName()), false)
	end
}
luajinyu = sgs.CreateOneCardViewAsSkill{
	name = "luajinyu",
	filter_pattern = ".",
	view_as = function(self, card)
		local skillcard = jinyuCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self, player)
		return true
	end,
}

luachihe = sgs.CreateTriggerSkill{
	name = "luachihe",
	frequency = sgs.Skill_Compulsory, --Frequent, NotFrequent, Compulsory, Limited, Wake
	events = {sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data)
		if player:getPhase() == sgs.Player_Discard then
			if player:getHandcardNum() > player:getMaxCards() then
				local discard_ids = room:getDrawPile()
				local trickcard = sgs.IntList()
				for _, id in sgs.qlist(discard_ids) do
					local card = sgs.Sanguosha:getCard(id)
					if card:isKindOf("FireSlash") then
						trickcard:append(id)
					end
				end
				discard_ids = room:getDiscardPile()
				for _, id in sgs.qlist(discard_ids) do
					local card = sgs.Sanguosha:getCard(id)
					if card:isKindOf("FireSlash") then
						trickcard:append(id)
					end
				end
				if trickcard:length() > 0 then
					local tp = room:askForPlayerChosen(player, room:getAllPlayers(), "luachihe", "@luachihe", true, true)
					if tp then
						room:fillAG(trickcard, tp)
						local card_id = room:askForAG(tp, trickcard, false, "luazhishu")
						local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
						dummy_0:addSubcard(card_id)
						room:clearAG()
						tp:obtainCard(dummy_0)

						local roomx = tp:getRoom()
						local thread = roomx:getThread()
						roomx:setPlayerFlag(tp, "LuaGuanwei")
						local old_phase = tp:getPhase()
						tp:setPhase(sgs.Player_Play)

						roomx:broadcastProperty(tp, "phase")
						if not thread:trigger(sgs.EventPhaseStart, roomx, tp) then
							thread:trigger(sgs.EventPhaseProceeding, roomx, tp)
						end
						thread:trigger(sgs.EventPhaseEnd, roomx, tp)
						tp:setPhase(old_phase)
						roomx:broadcastProperty(tp, "phase")
					end
				end
			end
		end
	end
}

luafuzheng = sgs.CreateTriggerSkill{
	name = "luafuzheng",
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if not p:hasSkill("luafuzhengr") then room:attachSkillToPlayer(p, "luafuzhengr") end
				end
			end
		end
	end
}

local function ewjd(tp)
	local roomx = tp:getRoom()
	local thread = roomx:getThread()
	roomx:setPlayerFlag(tp, "LuaGuanwei")
	local old_phase = tp:getPhase()
	tp:setPhase(sgs.Player_Play)
	roomx:broadcastProperty(tp, "phase")
	if not thread:trigger(sgs.EventPhaseStart, roomx, tp) then
		thread:trigger(sgs.EventPhaseProceeding, roomx, tp)
	end
	thread:trigger(sgs.EventPhaseEnd, roomx, tp)
	tp:setPhase(old_phase)
	roomx:broadcastProperty(tp, "phase")
end
local function zhuShaNiZei(room, tp, player)
	for _,p in sgs.qlist(room:getAlivePlayers()) do
		if p:isAlive() and tp:isAlive() then
			room:setPlayerFlag(tp, "slashTargetFix")
			room:setPlayerFlag(tp, "slashNoDistanceLimit")
			room:setPlayerFlag(tp, "slashTargetFixToOne")
			room:setPlayerFlag(p, "SlashAssignee")
			if not room:askForUseSlashTo(p, tp, "luazhunir", false) then
				room:damage(sgs.DamageStruct("luazhuni", player, p))
			end
			room:setPlayerFlag(tp, "-slashTargetFix")
			room:setPlayerFlag(tp, "-slashNoDistanceLimit")
			room:setPlayerFlag(tp, "-slashTargetFixToOne")
			room:setPlayerFlag(p, "-SlashAssignee")
		end
	end
end
luazhuni = sgs.CreateTriggerSkill{
	name = "luazhuni",
	events = {sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data, room)
		if player:getPhase() == sgs.Player_Discard then
			if player:getMark("@luafuzheng") > 0 then
				if player:getMark("@luafuzheng") < 5 then
					if player:askForSkillInvoke("luazhuni", data) then
						local splist = room:getAlivePlayers()
						local y = player:getMark("@luafuzheng")
						x = math.min(room:getAlivePlayers():length() + 1, y)
						local names = {}
						for i = 1,x do
							table.insert(names, tostring(i))
						end
						x = room:askForChoice(player, "luazhuni", table.concat(names, "+"))
						player:loseMark("@luafuzheng", x)
						x = tonumber(x) - 1
						if x == 0 then return false end
						while not splist:isEmpty() do
							local tp = room:askForPlayerChosen(player, splist, "luazhuni", "@luazhuni", false, true)
							if tp then
								splist:removeOne(tp)
								ewjd(tp)
							else
								break
							end
							x = x - 1
							if x <= 0 then break end
						end
					end
					return false
				else
					if player:askForSkillInvoke("luazhuni", data) and player:getMark("luazhuni") > 0 then
						local splist = room:getAlivePlayers()
						local y = player:getMark("@luafuzheng") - 1
						x = math.min(room:getAlivePlayers():length() + 1, y)
						local names = {}
						for i = 1,x do
							table.insert(names, tostring(i))
						end
						x = room:askForChoice(player, "luazhuni", table.concat(names, "+"))
						player:loseMark("@luafuzheng", x)
						x = tonumber(x) - 1

						if x == 0 then return false end
						while not splist:isEmpty() do
							local tp = room:askForPlayerChosen(player, splist, "luazhuni", "@luazhuni", false, true)
							if tp then
								splist:removeOne(tp)
								ewjd(tp)
							else
								break
							end
							x = x - 1
							if x <= 0 then break end
						end
						return false
					end
					if player:getMark("luazhuni") == 0 then
						local choice = room:askForChoice(player, "luazhuniK", "luazhuni1+luazhuni2")
						if choice == "luazhuni1" then
							local splist = room:getAlivePlayers()
							local y = player:getMark("@luafuzheng") - 1
							x = math.min(room:getAlivePlayers():length() + 1, y)
							local names = {}
							for i = 1,x do
								table.insert(names, tostring(i))
							end
							x = room:askForChoice(player, "luazhuni", table.concat(names, "+"))
							player:loseMark("@luafuzheng", x)
							x = tonumber(x) - 1
							if x == 0 then return false end
							while not splist:isEmpty() do
								local tp = room:askForPlayerChosen(player, splist, "luazhuni", "@luazhuni", false, true)
								if tp then
									splist:removeOne(tp)
									ewjd(tp)
								else
									break
								end
								x = x - 1
								if x <= 0 then break end
							end
						else
							local tp = room:askForPlayerChosen(player, room:getOtherPlayers(player), "luazhuni2", "@luazhuni2", false, true)
							if tp then
								zhuShaNiZei(room, tp, player)
							end
							player:loseMark("@luafuzheng", 5)
							room:setPlayerMark(player, "luazhuni", 1)
						end
					end
					return false
				end
			end
		end
	end
}
luahejun = sgs.CreateTriggerSkill{
	name = "luahejun$",
	global = true,
	frequency = sgs.Skill_Frequent,
	events = {sgs.BeforeCardsMove},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local move = data:toMoveOneTime()
		if player:hasLordSkill(self:objectName()) then
			if event == sgs.BeforeCardsMove then
				if move.from and move.from:objectName() and move.from:objectName() == player:objectName() and (move.to_place == sgs.Player_DiscardPile)
					and not player:hasFlag("luahejun") then
					local duel = sgs.Sanguosha:cloneCard("duel", sgs.Card_NoSuit, 0)
					for _,card_idX in sgs.qlist(move.card_ids) do
						local card = sgs.Sanguosha:getCard(card_idX)
						if card:isKindOf("Slash") then
							duel:addSubcard(card_idX)
						end
					end
					local i = 0
					if duel:getSubcards():length() > 0 and player:askForSkillInvoke("luahejun", data) then
						local old_card_ids = {}
						for _,card_idX in sgs.qlist(move.card_ids) do
							table.insert(old_card_ids, card_idX)
						end
						for _, card_idY in ipairs(old_card_ids) do
							if (duel:getSubcards():contains(card_idY)) then
								move.card_ids:removeOne(card_idY)
								move.from_places:removeAt(i)
							else
								i = i + 1
							end
						end
						local tr = room:askForPlayerChosen(player, room:getOtherPlayers(player), "luahejun", "@luahejun", true, true)
						if tr then
							tr:obtainCard(duel)
						end
						data:setValue(move)
						room:setPlayerFlag(player, "luahejun")
					end
				end
				return false
			end
		end
	end
}
luahejun2 = sgs.CreateTriggerSkill{
	name = "#luahejun" ,
	global = true,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local toziko = player:getRoom():findPlayerBySkillName("luahejun")
		if toziko and toziko:hasFlag("luahejun") then
			room:setPlayerFlag(toziko, "-luahejun")
		end
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}

minamoto:addSkill(luafuzheng)
minamoto:addSkill(luazhuni)
minamoto:addSkill(luahejun)
minamoto:addSkill(luahejun2)


luaqinjian = sgs.CreateTriggerSkill{
	name = "luaqinjian" ,
	events = {sgs.TargetConfirmed},
	global = true,
	on_trigger = function(self, event, player, data, room)
		local use = data:toCardUse()
		if use.card:isKindOf("EquipCard") and not use.card:isKindOf("SkillCard") and use.from and use.from:objectName() == player:objectName() then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				local _data = sgs.QVariant()
				_data:setValue(player)
				p2:getRoom():setTag("luaqinjianTP", _data)
				if room:askForSkillInvoke(p2, "luaqinjian", data) then
					player:drawCards(1)
				end
				p2:getRoom():removeTag("luaqinjianTP")
			end
		end
	end
}

luaxianglin = sgs.CreateTriggerSkill{
	name = "luaxianglin",
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if not p:hasSkill("luaxianglinr") then room:writeToConsole("jiantin test" .. p:objectName()); room:attachSkillToPlayer(p, "luaxianglinr") end
				end
			end
		end
	end
}
rinnosuke:addSkill(luaqinjian)
rinnosuke:addSkill(luaxianglin)


luaxinlun = sgs.CreateTriggerSkill {
	name = "luaxinlun",
	events = {sgs.CardUsed, sgs.CardResponded},
	frequency = sgs.Skill_Frequent ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local card
		if event == sgs.CardUsed then
			card = data:toCardUse().card
		else
			--if data:toCardResponse().m_isUse then
				card = data:toCardResponse().m_card
			--end
		end 
		if card and not card:isKindOf("SkillCard") and player:getMark("luaxinlun") < 2 then 
			if card:getNumber() == room:getLord():getMark("@clock_time") then
				if room:askForSkillInvoke(player, self:objectName(), data) then
					local choice = room:askForChoice(player, "luaxinlun", "luaxinlun1+luaxinlun2")
					local x = room:getLord():getMark("@clock_time")
					if choice == "luaxinlun1" then
						room:setPlayerMark(room:getLord(), "@clock_time", x + 1)
					else
						room:setPlayerMark(room:getLord(), "@clock_time", x - 1)
					end
					x = math.ceil(x / 2)
					local card_ids = room:getNCards(x)
					room:fillAG(card_ids)
					local card_id = room:askForAG(player, card_ids, false, "luaxinlun")
					room:takeAG(player, card_id, false)
					room:clearAG()
					local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
					dummy:addSubcard(card_id)
					player:obtainCard(dummy)
					card_ids:removeOne(card_id)
					local dummy2 = sgs.Sanguosha:cloneCard("yuzhi", sgs.Card_NoSuit, 0)
					dummy2:addSubcards(card_ids)
					local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, player:objectName(), self:objectName(), nil)
					room:moveCardTo(dummy2, player, nil,  sgs.Player_DrawPile, reason, true)
					room:setPlayerMark(player, "luaxinlun", player:getMark("luaxinlun") + 1)
				end
			end
		end
	end
}

luaxinlun2 = sgs.CreateTriggerSkill{
	name = "#luaxinlun" ,
	global = true,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Start then
			local toziko = player:getRoom():findPlayerBySkillName("luaxinlun")
			if toziko then
				room:setPlayerMark(player, "luaxinlun", 0)
			end
		end
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 2
}

local function getLordMark(player)
	if player:getRole() == "lord" and player:getMark("@clock_time") < 14 then return player:getMark("@clock_time") end
	for _, p in sgs.qlist(player:getSiblings()) do
		if p:getRole() == "lord" and p:getMark("@clock_time") < 14 then return p:getMark("@clock_time") end
	end
	return 0
end
xianlunCard = sgs.CreateSkillCard{
	name = "luaxianlun",
	will_throw = false,
	--handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		if #targets > 0 then return false end
		local card = sgs.Self:getTag("luaxianlun")
		local response = false
		if card then
			card = card:toCard()
		else
			response = true
			card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, getLordMark(sgs.Self))
			card = sgs.self
		end
		if not card then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end
		if self:getUserString() and self:getUserString() ~= "" then card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, getLordMark(sgs.Self));response = true end
		card:addSubcards(self:getSubcards())
		card:setSkillName(self:objectName())
		if card and card:targetFixed() and not response then
			return false
		end
		if response then return true end
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end

		return card and card:targetFilter(qtargets, to_select, sgs.Self) and not sgs.Self:isProhibited(to_select, card, qtargets)
	end,
	feasible = function(self, targets)
		local card = sgs.Self:getTag("luaxianlun")
		local response = false
		if card then
			card = card:toCard()
		else
			response = true
			card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, getLordMark(sgs.Self))
		end
		if not card then card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, getLordMark(sgs.Self));response = true end
		if self:getUserString() and self:getUserString() ~= "" then card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, getLordMark(sgs.Self));response = true end
		card:addSubcards(self:getSubcards())
		card:setSkillName(self:objectName())
		card:setNumber(getLordMark(sgs.Self))
		if response then return true end
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		return card and card:targetsFeasible(qtargets, sgs.Self)
	end,
	on_validate = function(self, card_use)
		local use_card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, getLordMark(card_use.from))
		use_card:addSubcards(self:getSubcards())
		use_card:setSkillName(self:objectName())
		local available = true
		for _,p in sgs.qlist(card_use.to) do
			if card_use.from:isProhibited(p,use_card)then
				available = false
				break
			end
		end
		available = available and use_card:isAvailable(card_use.from)
		if not available then return nil end
		use_card:setNumber(getLordMark(card_use.from))
		return use_card
	end,
	on_validate_in_response = function(self, user)
		local room = user:getRoom()
		local aocaistring = self:getUserString()
		local use_card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, room:getLord():getMark("@clock_time"))
		if string.find(aocaistring, "+")  then
			local uses = {}
			for _, name in pairs(aocaistring:split("+")) do
				table.insert(uses, name)
			end
			local name = room:askForChoice(user, "luaxianlun", table.concat(uses, "+"))
			use_card = sgs.Sanguosha:cloneCard(name, sgs.Card_NoSuit, room:getLord():getMark("@clock_time"))
		end
		use_card:addSubcards(self:getSubcards())
		use_card:setSkillName("luaxianlun")
		use_card:setNumber(room:getLord():getMark("@clock_time"))
		return use_card
	end
}
luaxianlun = sgs.CreateViewAsSkill{
	name = "luaxianlun",
	n = 999,
	view_filter = function(self, selected, to_select)
		local n = to_select:getNumber()
		if n > getLordMark(sgs.Self) then return false end
		for _, card in ipairs(selected) do
			n = n + card:getNumber()
			if n > getLordMark(sgs.Self) then return false end
		end
		return true
	end,
	view_as = function(self, cards)
		if #cards < 1 then return nil end
		local n = 0
		for _, card in ipairs(cards) do
			n = n + card:getNumber()
		end
		if n < getLordMark(sgs.Self) then return nil end
		if (sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY) then
			local c = sgs.Self:getTag("luaxianlun"):toCard()
			if c then
				local card = xianlunCard:clone()
				for _, acard in ipairs(cards) do
					card:addSubcard(acard)
				end
				card:setUserString(c:objectName())
				return card
			end
		else
			local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
			if pattern == "slash" then
				pattern = "slash+thunder_slash+fire_slash"
			end
			local acard = xianlunCard:clone()
			for _, bcard in ipairs(cards) do
				acard:addSubcard(bcard)
			end
			if pattern == "peach+analeptic" then
				if sgs.Self:hasFlag("Global_PreventPeach") then
					pattern = "analeptic"
				else
					pattern = "peach"
				end
			end
			acard:setUserString(pattern)
			return acard
		end
		return nil
	end,
	enabled_at_play = function(self, player)
		return not player:isNude()
	end,
	enabled_at_response = function(self, player, pattern)
		if player:isNude() or string.sub(pattern, 1, 1) == "." or string.sub(pattern, 1, 1) == "@" then
			return false
		end
		return true
	end,
}
luaxianlun:setGuhuoDialog("l")

luadintai = sgs.CreateTriggerSkill{
	name = "luadintai" ,
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
			local change = data:toPhaseChange()
			if change.to == sgs.Player_Start then
				local lord = room:getLord()
				if lord:getMark("luadintai") == 0 then
					room:setPlayerMark(lord, "@clock_time", room:getTag("TurnLengthCount"):toInt() + 6)
					room:setPlayerMark(lord, "luadintai", 1)
				end
			end
			return false
		end
	end ,
	priority = 10
}
renko:addSkill(luaxinlun)
renko:addSkill(luaxinlun2)
renko:addSkill(luaxianlun)
renko:addSkill(luadintai)
luafuze = sgs.CreateTriggerSkill{
	name = "luafuze",
	global = true,
	events = {sgs.Damaged, sgs.DamageForseen},
	on_trigger = function(self, event, player, data, room)
		local damage = data:toDamage()
		if damage.damage == 0 then return false end
		for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
			room:setPlayerFlag(p, "luafuze" .. damage.to:getSeat())
			if event == sgs.Damaged then
				local canInvoke = true
				--[[
				for i = 0,20 do
					if i ~= damage.to:getSeat() then
						if p:hasFlag("luafuze" .. i) then
							canInvoke = false
						end
					end
				end]]--
				if canInvoke and isFriendQ(room, p, damage.to) and not damage.to:isNude()
					and room:askForSkillInvoke(damage.to, self:objectName(), data) then
					local card1 = room:askForCard(damage.to, ".|.|.|.!", "@luafuze", sgs.QVariant(), sgs.Card_MethodNone)
					local ex_nihilo = sgs.Sanguosha:cloneCard("ex_nihilo", sgs.Card_NoSuit, 0)
					if card1 and not damage.to:isCardLimited(card1, sgs.Card_MethodUse)
							and not damage.to:isCardLimited(ex_nihilo, sgs.Card_MethodUse) then
						ex_nihilo:addSubcard(card1)
						room:useCard(sgs.CardUseStruct(ex_nihilo, damage.to, damage.to))
					end
				end
			end
		end
	end
}
luafuze2 = sgs.CreateTriggerSkill{
	name = "#luafuze",
	events = {sgs.EventPhaseChanging},
	global = true,
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if p:hasSkill("luafuze") then
						for i = 0,20 do
							room:setPlayerFlag(p, "-luafuze" .. i)
						end
					end
				end
			end
		end
	end
}

lualeiluan = sgs.CreateTriggerSkill{
	name = "lualeiluan",
	global = true,
	priority = 11,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardsMoveOneTime, sgs.BeforeCardsMove},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.CardsMoveOneTime then
			local move = data:toMoveOneTime()
			if move.card_ids:length() == 4 then return false end
			for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				if move.to and isFriendQ(room, p, move.to) then
					local _moveto
					for _, pxj in sgs.qlist(room:getAlivePlayers()) do
						if move.to and move.to:objectName() == pxj:objectName() then
							_moveto = pxj
							break
						end
					end
					if _moveto and bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) == sgs.CardMoveReason_S_REASON_DRAW then
						if _moveto:getHandcardNum() >= _moveto:getMaxHp() * 2 then
							room:setPlayerFlag(p, "lualeiluan")
							for _, pa in sgs.qlist(room:getAlivePlayers()) do 
								room:setPlayerMark(pa, "@nocard", 1)
							end 
							while _moveto:getHandcardNum() > 1 do
								room:setPlayerFlag(_moveto, "luaaoshuNull")
								local card = room:askForCard(_moveto, "TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand", "@lualeiluan", data, sgs.Card_MethodNone)
								room:setPlayerFlag(_moveto, "-luaaoshuNull")
								if card then
									if not card:cardIsAvailable(_moveto) then
										room:throwCard(card, _moveto, _moveto)
									else
										if _moveto:isCardLimited(card, sgs.Card_MethodUse) then
											room:throwCard(card, _moveto, _moveto)
										else
											local Carddata3 = sgs.QVariant() -- ai用
											Carddata3:setValue(card)
											_moveto:getRoom():setTag("luaqiuwenTC", Carddata3)
											if not room:askForUseCard(_moveto, card:toString(), "@lualeiluan1") then
												_moveto:getRoom():removeTag("luaqiuwenTC")
												room:throwCard(card, _moveto, _moveto)
											end
											_moveto:getRoom():removeTag("luaqiuwenTC")
										end
									end
								else
									room:askForDiscard(_moveto, self:objectName(), 1, 1, false, false, "lualeiluan2")
								end
							end
							for _, pa in sgs.qlist(room:getAlivePlayers()) do 
								room:setPlayerMark(pa, "@nocard", 0)
							end 
							room:setPlayerFlag(p, "-lualeiluan")
						end
					end
				end
			end
		elseif event == sgs.BeforeCardsMove then
			for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				if p:hasFlag("lualeiluan") then
					local move = data:toMoveOneTime()
					local _moveto
					for _, pa in sgs.qlist(room:getAlivePlayers()) do
						if move.to and move.to:objectName() == pa:objectName() then
							_moveto = pa
							break
						end
					end
					if _moveto and (move.to_place == sgs.Player_PlaceHand or move.to_place == sgs.Player_PlaceEquip)
						and (not move.from or move.to:objectName() ~= move.from:objectName()) then
						local toDiscard = sgs.IntList()
						local old_card_ids = {}
						for _,card_idX in sgs.qlist(move.card_ids) do
							table.insert(old_card_ids, card_idX)
						end
						--[[
						local i = 0
						for _, card_idY in ipairs(old_card_ids) do
							if true then
								move.card_ids:removeOne(card_idY)
								move.from_places:removeAt(i)
								toDiscard:append(card_idY)
								room:writeToConsole("aaaaa" .. tostring(card_idY))
								local aaa = sgs.Sanguosha:getCard(card_idY)
								room:throwCard(aaa, _moveto)
							else
								i = i + 1
							end
						end ]]--
						move.to_place = sgs.Player_DiscardPile
						move.to = nil
						data:setValue(move)
						--local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
						--dummy:addSubcards(toDiscard)
						--local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_THROW, _moveto:objectName(), "", self:objectName(), "")
						--room:moveCardTo(dummy, _moveto, nil, sgs.Player_DiscardPile, reason, true)
						--room:throwCard(dummy, _moveto)
						-- 我的建议是不要在BeforeCardsMove 里面再移动卡牌，包出bug的  2024年8月1日07:05:28
					end
					return false
				end
			end
		end
	end
}
eika:addSkill(luafuze)
eika:addSkill(luafuze2)
eika:addSkill(lualeiluan)



sgs.LoadTranslationTable {
	["pay22"] = "翼展鸿图", --注意这里每次要加逗号
	["kosuzu"] = "本居小铃",
	["#kosuzu"] = "识文解意的爱书人",
	["designer:kosuzu"] = "Paysage",
	["illustrator:kosuzu"] = "藤原",
	["luazhishu"] = "知书",
	["delay_trick"] = "延时类锦囊",
	[":luazhishu"] = "弃牌阶段结束时，你可以将于本阶段弃置的非基本牌置于武将牌上，称为“书”，然后获得弃牌堆中的一张【无中生有】或桃。锁定技，当你受到火焰伤害后，你须弃置两张“书”（不足则全弃）。",
	["luahanling"] = "翰林",
	["Shu"] = "书",
	["luahanling2"] = "翰林",
	[":luahanling"] = "一名其他角色于其出牌阶段限一次，可以交给你一张手牌并获得一张“书”。",
	["luayaojuan"] = "妖卷",
	[":luayaojuan"] = "出牌阶段限一次，若你有三张以上的“书”，你可以失去一项技能，并对一名角色造成两点伤害。",



	["kutaka"] = "庭渡久侘歌",
	["kutakaA"] = "庭渡久侘歌",
	["kutakaB"] = "庭渡久侘歌",
	["kutakaC"] = "庭渡久侘歌",
	["#kutaka"] = "金鸡报晓",
	["designer:kutaka"] = "Paysage",
	["@luafenshui"] = "你可以弃一张牌来对当前回合角色发动“分水”",
	["luafenshui"] = "分水",
	[":luafenshui"] = "一名角色出牌阶段结束时，若你手牌数为2，你可以弃一张牌令其判定：若为红，其回复/流失一点体力；若为黑，其摸/弃X张牌（均由你选择）（X为其已损失体力值）。",
	["luachenti"] = "晨啼",
	["recover"] = "回复一点体力",
	["loseHp"] = "流失一点体力",
	[":luachenti"] = "出牌阶段，你可以指定任意名角色，直到你下次发动“晨啼”为止，其回复体力后可以摸一张牌并使用一张牌（不计入次数限制）。",

	["shizuha"] = "秋静叶",
	["#shizuha"] = "一叶知秋",
	["designer:shizuha"] = "Paysage",
	["illustrator:shizuha"] = "c7肘",
	["luahongye"] = "红叶",
	[":luahongye"] = "每回合每项限一次，若你手牌数为：3，你可以重铸一张基本牌；2，你可以弃置一名角色区域里的一张牌；1.当前回合角色手牌上限-1；0，你受到伤害后回复一点体力并摸一张牌。",
	["luashenqiu"] = "知秋",
	[":luashenqiu"] = "锁定技，回合结束时，你回复体力至X+1（X为场上没有手牌的角色数）。",

	["komachi"] = "小野塚小町",
	["#komachi"] = "彼者",
	["designer:komachi"] = "Paysage",
	["luasilian"] = "死镰",
	[":luasilian"] = "群体锦囊以外你对其他角色造成伤害后，你可以令其判定：若不为红桃，其于其之后每个准备阶段流失一点体力（不可叠加）；否则你获得其一张牌。",
	["luachuandu"] = "川渡",
	[":luachuandu"] = "锁定技，你与已受伤的角色计算距离时-2。",
	["luaguihang"] = "归航",
	[":luaguihang"] = "一名其他角色濒死时，若其体力值为0，你可以与其拼点，若你赢，其将体力值改为-1；若你没赢，你获得其拼点牌。",

	["mayumi"] = "杖刀偶磨弓",
	["#mayumi"] = "植轮百夫长",
	["designer:mayumi"] = "Paysage",
	["luajuntuan"] = "军团",
	["@luajuntuan"] = "军团",
	["luajuntuan-invoke"] = "你可令一名其他角色回复一点体力",
	["luajuntuan2-invoke"] = "你可选择一名其他角色成为被射的对象（挨个选择，缺一不可）",
	[":luajuntuan"] = "每轮开始时，你回复一点体力。根据游戏中你回复体力的次数，执行如下效果:" ..
			"<br>3：你获得“激昂”且使用【杀】后视为你对其他两名角色使用了一张【万箭齐发】；<br/>" ..
			"6：你获得“弘援”；<br>12，你回复体力后可令一名其他角色回复一点体力；<br/>" ..
			"20：你回复体力至满并获得“马术”，此后你使用【杀】造成的伤害+X（X为你体力值）。",
	["luajiang"] = "激昂",
	[":luajiang"] = "出牌时，你可以表现得很激昂。",
	["luahongyuan"] = "弘援",
	[":luahongyuan"] = "摸牌阶段，你可以少摸一张牌，令任意其他角色各摸一张牌。",
	["luahongyuan-invoke"] = "请选择要摸牌的角色。（挨个选择）",

	["ichirin"] = "云山&一轮",
	["#ichirin"] = "入道使",
	["designer:ichirin"] = "Paysage",
	["luashenquan"] = "神拳",
	[":luashenquan"] = "你指定唯一其他角色的牌使用结算后，你可以弃一张牌，摸一张牌，对两名其他角色使用一张牌（无距离与次数限制）。发动“神拳”的回合结束时，你流失一点体力。",
	["@luashenquan"] = "你可以发动“神拳”",
	["~luashenquan"] = "选择要使用的那张牌→选择要使用的那两个对象→点击确定。",
	["luarudao"] = "入道",
	[":luarudao"] = "你的回合结束时，若你的武将牌正面朝上，你可以执行一个额外的回合并翻面。",

	["htmsken"] = "肯",
	["#htmsken"] = "幻天漫杀主催",
	["designer:htmsken"] = "Paysage",
	["luashiyong"] = "恃武",
	[":luashiyong"] = "锁定技，体力值不少于你的角色对你造成的伤害+1。",
	["luajisi"] = "集思",
	["luajisir"] = "集思",
	["luajisira"] = "你可以使用一张牌",
	[":luajisir"] = "你可以交给肯神一张手牌，然后他可以令你展示牌堆顶的X张牌并让你获得之（X为他与你的体力值之差）",
	[":luajisi"] = "所有角色于其出牌阶段限一次，其可以正面朝上交给你一张手牌，然后你可以令其展示牌堆顶的X张牌并获得之（X为其与你的体力值之差）。若以此法展示的牌点数和比交给你的牌点数小，你可以使用一张牌。",
	["luagengxin"] = "更新",
	[":luagengxin"] = "主公技，出牌阶段限一次，你可以展示一名角色的手牌，并从其中弃置一张。然后其可以展示牌堆顶的至多八张牌，若以此法展示的牌点数和比弃置牌点数小，其全部获得之。",

	["minamoto"] = "源水脈",
	["#minamoto"] = "治军兴政",
	["designer:minamoto"] = "Paysage",
	["illustrator:minamoto"] = "ABO",
	["luafuzheng"] = "赋政",
	["luafuzhengr"] = "赋政",
	[":luafuzhengr"] = "你可以摸一张牌，然后源水脈获得一个“政”标记，你弃一张牌。",
	[":luafuzheng"] = "一名角色于其出牌阶段限一次，其可以摸一张牌，然后你获得一个“政”标记，其弃一张牌。",
	["luazhuni"] = "诛逆",
	["@luazhuni"] = "选一名角色执行额外的出牌阶段",
	["@luahejun"] = "选要获得这些【杀】的角色",
	["@luazhuni2"] = "令所有角色对你指定的一名角色使用一张【杀】",
	["luazhuni1"] = "执行额外的出牌阶段",
	["luazhuni2"] = "令所有角色对你指定的一名角色使用一张【杀】",
	["luazhunir"] = "对指定的一名角色使用一张【杀】，或是受到造成的一点伤害",
	[":luazhuni"] = "弃牌阶段结束时，你可以：①弃置X+1枚“政”令X名角色执行一个额外的出牌阶段；②弃置五枚“政”，令所有角色对你指定的一名角色使用一张【杀】，或是受到你对其造成的一点伤害（每局游戏限一次）。",
	["luahejun"] = "合军",
	[":luahejun"] = "主公技，每阶段限一次，你的【杀】置入弃牌堆前，你可以交给一名其他角色。",

	["rita"] = "丽塔专属",
	["#vollerei"] = "彼岸双生",
	["vollerei"] = "希儿·芙乐艾",
	["luamingan"] = "明暗",
	[":luamingan"] = "当“诞终”为表时，你使用的红桃牌可以额外指定一个目标；当“诞终”为里时，你使用的黑桃牌只能指定一个目标。",
	["luadanzhong"] = "诞终",
	[":luadanzhong"] = "转化技，每回合限一次。表：当你使用非【桃】牌指定了不止一个目标时，你可以对其中一个目标造成1点伤害；里:当你使用牌仅指定了一个目标时，你可以对一名不为此牌目标且体力值大于1的角色造成1点伤害。",

	["rinnosuke"] = "森近霖之助",
	["#rinnosuke"] = "香霖堂店主",
	["designer:rinnosuke"] = "Paysage",
	["luaqinjian"] = "清鉴",
	[":luaqinjian"] = "一名角色使用装备牌指定目标时，你可以令其摸一张牌。",
	["luaxianglin"] = "香霖",
	["luaxianglinr"] = "香霖",
	[":luaxianglinr"] = "你可以弃置一张装备牌令森近霖之助回复一点体力。",
	[":luaxianglin"] = "任意其他角色于其出牌阶段，可以弃置一张装备牌令你回复一点体力。",

	["renko"] = "宇佐见莲子",
	["#renko"] = "月之妖鸟",
	["designer:renko"] = "まりさ",
	["illustrator:renko"] = "muraryö",
	["luaxinlun"] = "星轮",
	[":luaxinlun"] = "每回合限两次，每当你使用或打出牌后，若此牌点数为X，你可以令当前轮数+1或-1，然后展示牌堆顶的X/2（向上取整）张牌并获得其中一张（X为轮数）。",
	["luaxinlun1"] = "当前轮数加一",
	["luaxinlun2"] = "当前轮数减一",
	["luaxianlun"] = "弦论",
	[":luaxianlun"] = "当你需要使用或打出一张基本牌时，你可以将任意张点数和为轮数的牌当作该点数的基本牌使用打出。",
	["luadintai"] = "定态",
	[":luadintai"] = "锁定技，你参与的对局从第6轮开始",

	["eika"] = "戎璎花",
	["#eika"] = "河源的偶像水子",
	["designer:eika"] = "Paysage",
	["illustrator:eika"] = "muraryö",
	["luafuze"] = "福泽",
	["@luafuze"] = "将一张牌当作【无中生有】使用",
	[":luafuze"] = "友方角色受到一点伤害后，其可以将一张牌当作【无中生有】使用",
	["lualeiluan"] = "累卵",
	["lualeiluan2"] = "因 累卵 效果，你必须弃置一张牌。",
	["@lualeiluan"] = "请选择要使用或弃置的一张牌,现在不必指定目标",
	["@lualeiluan1"] = "请选择使用这张牌",
	[":lualeiluan"] = "锁定技，友方角色摸牌后，若其手牌数不少于2X，其须对每张手牌选择弃置或使用之，直到手牌数为一。且此期间所有角色获得的牌均置于弃牌堆（X为其体力上限）。",
}

































