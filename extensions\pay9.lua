module("extensions.pay9", package.seeall)
extension = sgs.Package("pay9", sgs.Package_CardPack)



local skills = sgs.SkillList()
quanxiang = sgs.CreateTrickCard{
    name = "quanxiang",
    class_name = "quanxiang",
    subtype = "single_target_trick",
    subclass = sgs.LuaTrickCard_TypeSingleTargetTrick,
    target_fixed = false,
    suit = sgs.Card_Spade,
    number = 13,
    filter = function(self, targets, to_select)
        if to_select:isKongcheng() then return false end
        if not self then return false end
        if not sgs.Self then return false end
        if to_select:objectName() ~= sgs.Self:objectName() and to_select:getHandcardNum() < sgs.Self:getHandcardNum() then
            return sgs.Self:inMyAttackRange(to_select,  - sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_DistanceLimit, sgs.Self, self))
                    and #targets < 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, sgs.Self, self)
        end
    end,
    is_cancelable = function(self, effect)
        if self and self:getSkillName() == "Luaweiguang" then return false end
        return true
    end,
    available = function(self,player)
        if player:isCardLimited(self, sgs.Card_MethodUse) then return false end
        return player:usedTimes("quanxiang") < 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_Residue, player, self)
    end,
    on_effect = function(self, effect)
        local source = effect.from
        local target = effect.to
        local room = source:getRoom()
        --room:writeToConsole(self:getEffectiveId())
        --if not room:askForNullification(self, source, target, true) then
        if not target:isKongcheng() and not effect.from:isKongcheng() then
            if source:hasSkill("Luaweiguang") then room:setPlayerFlag(source, "quanxiangS") end
            local success = source:pindian(target, "quanxiang")
            if source:hasFlag("quanxiangS") then room:setPlayerFlag(source, "-quanxiangS") end
            local data = sgs.QVariant()
            data:setValue(target)
        end
        --end
    end
}

faith_collection = sgs.CreateTrickCard{
    name = "faith_collection",
    class_name = "FaithCollection",
    subtype = "single_target_trick",
    subclass = sgs.LuaTrickCard_TypeSingleTargetTrick,
    target_fixed = false,
    suit = sgs.Card_Diamond,
    number = 11,
    filter = function(self, targets, to_select)
        if to_select:isAllNude() then return false end
        if not self then return false end
        if not sgs.Self then return false end
        return #targets < 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, sgs.Self, self)
    end,
    is_cancelable = function(self, effect)
        return true
    end,
    available = function(self,player)
        if player:isCardLimited(self, sgs.Card_MethodUse) then return false end
        return true
    end,
    on_effect = function(self, effect)
        local room = effect.to:getRoom()
        local card = room:askForCardChosen(effect.to, effect.to, "hej", self:objectName())
        card = sgs.Sanguosha:getCard(card)
        effect.from:obtainCard(card)
    end
}
local faith_collection2 = faith_collection:clone(sgs.Card_Diamond, 1)
blossom = sgs.CreateTrickCard{
    name = "blossom",
    class_name = "Blossom",
    subtype = "single_target_trick",
    subclass = sgs.LuaTrickCard_TypeSingleTargetTrick,
    target_fixed = false,
    suit = sgs.Card_Club,
    number = 1,
    filter = function(self, targets, to_select)
        return false
    end,
    is_cancelable = function(self, effect)
        return true
    end,
    available = function(self,player)
        if player:isCardLimited(self, sgs.Card_MethodUse) then return false end
        return player:usedTimes("Shoot") < 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_Residue, player, self)
    end,
    on_effect = function(self, effect)
    end
}


religion_battle = sgs.CreateTrickCard{
    name = "religion_battle",
    class_name = "ReligionBattle",
    subtype = "AOE",
    subclass = sgs.LuaTrickCard_TypeAOE,
    target_fixed = false,
    suit = sgs.Card_Spade,
    number = 10,
    is_cancelable = function(self, effect)
        return true
    end,
    available = function(self,player)
        if player:isCardLimited(self, sgs.Card_MethodUse) then return false end
        local canUse = false
        local players = player:getSiblings()
        players:append(player)
        for _, p in sgs.qlist(players) do
            if p:isDead() or player:isProhibited(p, self) then continue end
            canUse = true
            break
        end
        return canUse and self:cardIsAvailable(player)
    end,
    about_to_use = function(self, room, card_use)
        local source = card_use.from
        local targets = sgs.SPlayerList()
        local other_players = room:getAlivePlayers()
        for _, player in sgs.qlist(other_players) do
            local skill = room:isProhibited(source, player, self)
            if skill ~= nil then
                local log_message = sgs.LogMessage()
                log_message.type = "#SkillAvoid"
                log_message.from = player
                log_message.arg = skill:objectName()
                log_message.arg2 = self:objectName()
                room:broadcastSkillInvoke(skill:objectName())
            else
                targets:append(player)
            end
        end

        local use = card_use
        use.to = targets
        self:cardOnUse(room, use)
    end,
    on_effect = function(self, effect)
        local room = effect.to:getRoom()
        room:setPlayerFlag(effect.to, "RB")
        local count = 0
        for _, cardQ in sgs.list(effect.to:getHandcards()) do
            if cardQ:isKindOf("Ofuda") and not effect.to:isCardLimited(cardQ, sgs.Card_MethodUse) then count = count + 1 end
        end
        for _, cardQ in sgs.list(effect.to:getPile("wooden_ox")) do
            if cardQ:isKindOf("Ofuda") and not effect.to:isCardLimited(cardQ, sgs.Card_MethodUse) then count = count + 1 end
        end
        local id
        if count > 0 then
            id = room:askForCard(effect.to, "Ofuda", "@dangzuosha", sgs.QVariant(), sgs.Card_MethodNone)
        end
        if id then
            local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
            slash:setSkillName(self:objectName())
            slash:addSubcard(id)
            local x = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, effect.to, slash)
            local targets_list = sgs.SPlayerList()
            for  _, target in sgs.qlist(room:getAlivePlayers()) do
                if effect.to:canSlash(target, slash, false) then
                    targets_list:append(target)
                end
            end
            if targets_list:length() > 0 then
                local targets = sgs.SPlayerList()
                while x > 0 do
                    local Carddata2 = sgs.QVariant() -- ai用
                    Carddata2:setValue(slash)
                    room:setTag("dangzuoshaTC", Carddata2)
                    local target = room:askForPlayerChosen(effect.to, targets_list, "dangzuosha2", "@dangzuosha2", true)
                    if not target then break end
                    room:removeTag("dangzuoshaTC")
                    targets:append(target)
                    targets_list:removeOne(target)
                    if targets_list:length() == 0 then break end
                    x = x - 1
                end
                if targets:length() > 0 then
                    room:useCard(sgs.CardUseStruct(slash, effect.to, targets), false)
                end
            end
        else
            if effect.to:getState() == "robot" or effect.to:isWounded() then
                
				room:setPlayerFlag(effect.to, "luaaoshuNull")
                if room:askForUseCard(effect.to, "TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand", "@RB", -1, sgs.Card_MethodUse, false) then
                    if effect.to:getMark("drank") > 0 then
                        room:setPlayerMark(effect.to, "drank", 1)
                    end
                else
                    room:damage(sgs.DamageStruct(self, nil, effect.to, 1, sgs.DamageStruct_Normal))
                end
				room:setPlayerFlag(effect.to, "-luaaoshuNull")
                room:setPlayerFlag(effect.to, "-RB")
            else
                if room:askForUseCard(effect.to, "TrickCard+^Nullification,BasicCard+^Jink+^Peach,EquipCard|.|.|hand", "@RB", -1, sgs.Card_MethodUse, false) then
                    if effect.to:getMark("drank") > 0 then
                        room:setPlayerMark(effect.to, "drank", 1)
                    end
                else
                    room:damage(sgs.DamageStruct(self, nil, effect.to, 1, sgs.DamageStruct_Normal))
                end
                room:setPlayerFlag(effect.to, "-RB")
            end
        end
    end
}


need_maribel = sgs.CreateTrickCard{
    name = "need_maribel",
    class_name = "NeedMaribel",
    subtype = "delayed_trick",
    target_fixed = false,
    suit = sgs.Card_Club,
    number = 11,
    subclass = sgs.LuaTrickCard_TypeDelayedTrick, -- LuaTrickCard_TypeNormal, LuaTrickCard_TypeSingleTargetTrick, LuaTrickCard_TypeDelayedTrick, LuaTrickCard_TypeAOE, LuaTrickCard_TypeGlobalEffect
    filter = function(self, targets, to_select)
        return #targets == 0 and not to_select:containsTrick("need_maribel")
    end,
    is_cancelable = function(self, effect)
        return true
    end,
    available = function(self,player)
        if player:isCardLimited(self, sgs.Card_MethodUse) then return false end
        return true
    end,
    about_to_use = function(self, room, use)
        local wrapped = sgs.Sanguosha:getWrappedCard(self:getEffectiveId())
        use.card = wrapped

        local data = sgs.QVariant()
        data:setValue(use)
        local thread = room:getThread()
        thread:trigger(sgs.PreCardUsed, room, use.from, data)
        use = data:toCardUse()

        local logm = sgs.LogMessage()
        logm.from = use.from
        logm.to = use.to
        logm.type = "#UseCard"
        logm.card_str = self:toString()
        room:sendLog(logm)

        local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_USE, use.from:objectName(), use.to:first():objectName(), self:getSkillName(), "")
        room:moveCardTo(self, use.from, use.to:first(), sgs.Player_PlaceDelayedTrick, reason, true)

        thread:trigger(sgs.CardUsed, room, use.from, data)
        use = data:toCardUse()
        thread:trigger(sgs.CardFinished, room, use.from, data)
    end,
    on_effect = function(self, effect) --if target:isSkipped(sgs.Player_Discard) then return false end
        local target = effect.to
        local player = effect.to
        local room = effect.to:getRoom()
        if target and target:isAlive() then
            local judge = sgs.JudgeStruct()
            judge.pattern = "."
            judge.good = true
            judge.reason = self:objectName()
            judge.who = target
            room:judge(judge)
            if judge:isGood() then
                if judge.card:getSuit() == sgs.Card_Club then
                    if judge.who:isWounded() then
                        local recover = sgs.RecoverStruct()	--yun
                        recover.who = target
                        recover.recover = 1
                        room:recover(target, recover)
                    end
                else
                    target:drawCards(1)
                    room:setPlayerFlag(target, "need_maribel")
                end
            end
            local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_NATURAL_ENTER, target:objectName())
            target:getRoom():throwCard(self, reason, nil)
        end
    end
}



banquet = sgs.CreateTrickCard{
    name = "banquet",
    class_name = "Banquet",
    subtype = "delayed_trick",
    target_fixed = true,
    suit = sgs.Card_Heart,
    number = 9,
    subclass = sgs.LuaTrickCard_TypeDelayedTrick,
    filter = function(self, targets, to_select)
        return #targets == 0 and not to_select:containsTrick("banquet")
    end,
    is_cancelable = function(self, effect)
        return true
    end,
    available = function(self,player)
        if player:isCardLimited(self, sgs.Card_MethodUse) then return false end
        return true
    end,
    about_to_use = function(self, room, use)
        use.to:append(use.from)
        local wrapped = sgs.Sanguosha:getWrappedCard(self:getEffectiveId())
        use.card = wrapped

        local data = sgs.QVariant()
        data:setValue(use)
        local thread = room:getThread()
        thread:trigger(sgs.PreCardUsed, room, use.from, data)
        use = data:toCardUse()

        local logm = sgs.LogMessage()
        logm.from = use.from
        logm.to = use.to
        logm.type = "#UseCard"
        logm.card_str = self:toString()
        room:sendLog(logm)

        local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_USE, use.from:objectName(), use.to:first():objectName(), self:getSkillName(), "")
        room:moveCardTo(self, use.from, use.to:first(), sgs.Player_PlaceDelayedTrick, reason, true)

        thread:trigger(sgs.CardUsed, room, use.from, data)
        use = data:toCardUse()
        thread:trigger(sgs.CardFinished, room, use.from, data)
    end,
    on_use = function(self, room, source, targets)
        --[[
        local analeptic = sgs.Sanguosha:cloneCard("analeptic")
        room:useCard(sgs.CardUseStruct(analeptic, targets[1], targets[1]), true)]]--
    end,
    on_effect = function(self, effect)
        local target = effect.to
        local player = effect.to
        local room = effect.to:getRoom()
        local target2
        if target and target:isAlive() then
            local judge = sgs.JudgeStruct()
            judge.pattern = ".|spade"
            judge.good = false
            judge.reason = self:objectName()
            judge.who = target
            room:judge(judge)
            if judge:isGood() then
                local humans = sgs.SPlayerList()
                for _, p in sgs.qlist(room:getOtherPlayers(player)) do
                    if not p:containsTrick("banquet") then humans:append(p) end
                end
                if humans:length() > 0 then
                    target2 = room:askForPlayerChosen(target, humans, "banquet", "banquet2", false, false)
                    if target2 then
                        local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, target:objectName())
                        room:moveCardTo(self, target, target2, sgs.Player_Judge, reason, true)

                    end
                end
                local analeptic = sgs.Sanguosha:cloneCard("analeptic")
                room:useCard(sgs.CardUseStruct(analeptic, target, target), true)
                return
            end
        end
        local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_NATURAL_ENTER, target:objectName())
        target:getRoom():throwCard(self, reason, nil)
    end
}
girl_choosen = sgs.CreateTrickCard{
    name = "girl_choosen",
    class_name = "GirlChoosen",
    subtype = "delayed_trick",
    target_fixed = true,
    suit = sgs.Card_Heart,
    number = 1,
    subclass = sgs.LuaTrickCard_TypeDelayedTrick, -- LuaTrickCard_TypeNormal, LuaTrickCard_TypeSingleTargetTrick, LuaTrickCard_TypeDelayedTrick, LuaTrickCard_TypeAOE, LuaTrickCard_TypeGlobalEffect
    filter = function(self, targets, to_select)
        return #targets == 0 and not to_select:containsTrick("girl_choosen")
    end,
    about_to_use = function(self, room, use)
        use.to:append(use.from)
        local wrapped = sgs.Sanguosha:getWrappedCard(self:getEffectiveId())
        use.card = wrapped

        local data = sgs.QVariant()
        data:setValue(use)
        local thread = room:getThread()
        thread:trigger(sgs.PreCardUsed, room, use.from, data)
        use = data:toCardUse()

        local logm = sgs.LogMessage()
        logm.from = use.from
        logm.to = use.to
        logm.type = "#UseCard"
        logm.card_str = self:toString()
        room:sendLog(logm)

        local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_USE, use.from:objectName(), use.to:first():objectName(), self:getSkillName(), "")
        room:moveCardTo(self, use.from, use.to:first(), sgs.Player_PlaceDelayedTrick, reason, true)

        thread:trigger(sgs.CardUsed, room, use.from, data)
        use = data:toCardUse()
        thread:trigger(sgs.CardFinished, room, use.from, data)
    end,
    is_cancelable = function(self, effect)
        return true
    end,
    available = function(self,player)
        if player:isCardLimited(self, sgs.Card_MethodUse) then return false end
        return true
    end,
    on_effect = function(self, effect)
        local target = effect.to
        local player = effect.to
        local room = effect.to:getRoom()
        local target2
        if target and target:isAlive() then
            local judge = sgs.JudgeStruct()
            judge.pattern = ".|heart|2~9"
            judge.good = true
            judge.reason = self:objectName()
            judge.who = target
            room:judge(judge)
            if judge:isGood() then
                room:setPlayerMark(effect.to, "girl_choosenA", 1)
                local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_NATURAL_ENTER, target:objectName())
                target:getRoom():throwCard(self, reason, nil)
                return
            end
            local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, target:objectName())
            room:moveCardTo(self, target, target:getNextAlive(), sgs.Player_Judge, reason, true)
        end
    end
}



hui = sgs.CreateBasicCard{--BUG:can't view as
    name = "hui",
    class_name = "Hui",
    subtype = "attack_card",
    target_fixed = true,
    can_recast = false,
    suit = sgs.Card_Diamond,
    number = 6,
    about_to_use = function(self, room, use)
        if use.to:length() == 0 and not use.to:contains(use.from) then
            use.to:append(use.from)
        end
        self:cardOnUse(room, use)
    end,
    available = function(self,player)
        if player:isCardLimited(self, sgs.Card_MethodUse) then return false end
        return true
    end,
    on_use = function(self, room, source, targets)
        for _, target in ipairs(targets) do
            if (target:getCardCount(true) < 2) then
                room:damage(sgs.DamageStruct(self:objectName(), target, target, 1, sgs.DamageStruct_Normal))
            else
                local _data = sgs.QVariant()
                _data:setValue(self)
                local choice = room:askForChoice(target, "hui", "damage+discard", _data)
                if choice == "damage" then
                    room:damage(sgs.DamageStruct(self, target, target, 1, sgs.DamageStruct_Normal))
                else
                    room:askForDiscard(target, "hui", 2, 2, false, true)
                end
            end
        end
		if room:getCardPlace(self:getId()) == 7 then  
			local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_NATURAL_ENTER, source:objectName())
			room:moveCardTo(self, nil, sgs.Player_DiscardPile, reason, true) 
		end 
    end
}
local hui2 = hui:clone(sgs.Card_Club, 7)
local hui3 = hui:clone(sgs.Card_Spade, 12)

ofuda = sgs.CreateBasicCard{--BUG:can't view as
    name = "ofuda",
    class_name = "Ofuda",
    subtype = "attack_card",
    target_fixed = false,
    can_recast = false,
    suit = sgs.Card_Diamond,
    number = 2,
    filter = function(self, targets, to_select)  --
        if not sgs.Self then return end
        return sgs.Self:inMyAttackRange(to_select, - sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_DistanceLimit, sgs.Self, self))
                and #targets < 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, sgs.Self, self)
    end,
    available = function(self,player)
        if player:isCardLimited(self, sgs.Card_MethodUse) then return false end
        return true
    end,
    on_effect = function(self, effect)
        local room = effect.from:getRoom()
        if effect.to:getMark("@ofudaa") == 0 then
            if effect.to:isKongcheng() then
                room:setPlayerMark(effect.to, "@ofudaa", 1)
                room:setPlayerCardLimitation(effect.to, "use,response,discard", ".|.|.|hand", true)
                room:setPlayerMark(effect.to, "@skill_invalidity", 1)
            else
                local _data = sgs.QVariant()
                _data:setValue(effect.from)
                local choice = room:askForChoice(effect.to, self:objectName(), "ofuda1+ofuda2", _data)
                if choice == "ofuda1" then
                    local card1 = room:askForCard(effect.to, ".!", "@ofuda", sgs.QVariant(), sgs.Card_MethodNone)
                    effect.from:obtainCard(card1)
                else
                    room:setPlayerMark(effect.to, "@ofudaa", 1)
                    room:setPlayerCardLimitation(effect.to, "use,response,discard", ".|.|.|hand", true)
                    room:setPlayerMark(effect.to, "@skill_invalidity", 1)
                end
            end
        end 
    end
}

local ofuda3 = ofuda:clone(sgs.Card_Spade, 11)
local need_maribel2 = need_maribel:clone(sgs.Card_Club, 12)
gohei_grow2 = sgs.CreateAttackRangeSkill{
    name = "gohei_grow2",
    extra_func = function(self,player)
        if player:hasWeapon("gohei") then return player:getMark("@gohei") end
    end,
}
gohei_grow = sgs.CreateTriggerSkill{
    name = "gohei_grow" ,
    priority = 3,
    events = {sgs.EventPhaseStart},
    can_trigger = function(self, target)
        return target and target:isAlive() and target:hasWeapon("gohei")
    end,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if event == sgs.EventPhaseStart then
            if player:getPhase() == sgs.Player_Start then
                player:gainMark("@gohei")

                room:writeToConsole("gohei test1" .. player:getMark("@gohei"))
            end
        end
    end
}
goheiChange = sgs.CreateTriggerSkill{
    name = "goheiChange" ,
    events = {sgs.PreCardUsed} ,
    priority = 3,
    global = true,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        local use = data:toCardUse()
        if not use.from then return false end
        if use.from:objectName() ~= player:objectName() then return false end
        if (not player:hasWeapon("gohei")) or (not use.card:isKindOf("Slash")) then return false end
        local card = use.card
        if not card:isVirtualCard() then
            if player:askForSkillInvoke("gohei", data) then
                local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_Diamond, use.card:getNumber())
                if use.card:isKindOf("FireSlash") then
                    slash = sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_Diamond, use.card:getNumber())
                elseif use.card:isKindOf("ThunderSlash") then
                    slash = sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_Diamond, use.card:getNumber())
                end
                local drank = card:hasFlag("drank")
                if drank then
                    room:setCardFlag(slash, "drank")
                    slash:setTag("drank", sgs.QVariant(1))
                end
                slash:addSubcard(use.card)
                use.card = slash
                data:setValue(use)
            end
        end
        return false
    end
}
luayewang3 = sgs.CreateFilterSkill{
    name = "luayewang3",
    view_filter = function(self,to_select)
        local room = sgs.Sanguosha:currentRoom()
        local place = room:getCardPlace(to_select:getEffectiveId())
        return ((to_select:isBlack()) and (place == sgs.Player_PlaceHand) and to_select:hasFlag("luayewang"))
                or ((to_select:isRed()) and (place == sgs.Player_PlaceHand) and to_select:hasFlag("luayewang"))
    end,
    view_as = function(self, card)
        if (card:isBlack()) and card:hasFlag("luayewang") then
            local slash = sgs.Sanguosha:cloneCard("jink", card:getSuit(), card:getNumber())
            slash:setSkillName(self:objectName())
            local _card = sgs.Sanguosha:getWrappedCard(card:getId())
            _card:takeOver(slash)
            _card:setModified(true)
            return _card
        else
            local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
            slash:setSkillName(self:objectName())
            local _card = sgs.Sanguosha:getWrappedCard(card:getId())
            _card:takeOver(slash)
            _card:setModified(true)
            return _card
        end
    end
}
lualianmeng3 = sgs.CreateFilterSkill{
    name = "lualianmeng3",
    view_filter = function(self,to_select)
        local room = sgs.Sanguosha:currentRoom()
        local place = room:getCardPlace(to_select:getEffectiveId())
        return (place == sgs.Player_PlaceHand) and to_select:hasFlag("lualianmeng")
    end,
    view_as = function(self, card)
        if card:hasFlag("lualianmeng") then
            local suit
            if card:hasFlag("lualianmengheart") then
                suit = sgs.Card_Heart
            elseif card:hasFlag("lualianmengdiamond") then
                suit = sgs.Card_Diamond
            elseif card:hasFlag("lualianmengclub") then
                suit = sgs.Card_Club
            else
                suit = sgs.Card_Spade
            end
            local _card = sgs.Sanguosha:getWrappedCard(card:getId())
            _card:setSkillName(self:objectName())
            _card:setSuit(suit)
            _card:setModified(true)
            return _card
        end
    end
}
 
hakkero_gain = sgs.CreateTriggerSkill{
    name = "hakkero_gain",
    events = {sgs.CardsMoveOneTime},
    can_trigger = function(self, target)
        return target and target:isAlive() and target:hasWeapon("hakkero") and not target:hasFlag("hakkero")
    end,
    on_trigger = function(self, event, player, data, room)
        local move = data:toMoveOneTime()
        if move.from and move.from:objectName() == player:objectName() and (move.from_places:contains(sgs.Player_PlaceHand))
                and player:hasWeapon("hakkero") and not player:hasFlag("hakkero")
                and move.to_place == sgs.Player_DiscardPile and player:getPhase() == sgs.Player_Play then
            if bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) == sgs.CardMoveReason_S_REASON_DISCARD then
                for _,id in sgs.qlist(move.card_ids) do
                    card = sgs.Sanguosha:getCard(id)
                    if card and card:isKindOf("Slash") then
                        if player:askForSkillInvoke("hakkero", data) then
                            player:drawCards(1)
                            room:setPlayerFlag(player, "hakkero")
                            break
                        end
                    end
                end
            end
        end
    end
}

erin_bow_slash = sgs.CreateTriggerSkill{
    name = "erin_bow_slash",
    events = {sgs.TargetConfirmed},
    can_trigger = function(self, target)
        return target and target:isAlive() and target:hasWeapon("erin_bow") and not target:hasFlag("erinbow")
    end,
    on_trigger = function(self, event, player, data, room) 
        local use = data:toCardUse()
        if (not use.from) or (use.from:objectName() ~= player:objectName()) then return end 
        room:writeToConsole("Hello World! 2025-3-1 16:46:15")
        if not player:hasWeapon("erin_bow") or not use.card then return false end
        room:writeToConsole("Hello World! 2025-3-1 16:46:23")
        if use.to:length() ~= 1 and not use.card:isKindOf("SkillCard") then
            local targets = sgs.SPlayerList()
            for _, p in sgs.qlist(use.to) do
                if player:canSlash(p, nil, false) then
                    targets:append(p)
                end
            end 
            local target = room:askForPlayerChosen(player, targets, self:objectName(), self:objectName(), true, false)
            if target then
				local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				slash:setSkillName("erin_bow_slash")
                room:useCard(sgs.CardUseStruct(slash, player, target)) 
                room:setPlayerFlag(player, "erinbow")
            end 
        end 
    end
}


wanbaochuiCard = sgs.CreateSkillCard{
    name = "wanbaochui",
    target_fixed = true ,
    on_use = function(self, room, source, targets)
        local card_ids = room:getNCards(4)
        room:fillAG(card_ids)
        local card_id = room:askForAG(source, card_ids, false, self:objectName())
        card_ids:removeOne(card_id)
        local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
        dummy:addSubcard(card_id)
        source:obtainCard(dummy)
        for _, card_idA in sgs.qlist(card_ids) do
            local dummy2 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
            dummy2:addSubcard(card_idA)
            room:throwCard(dummy2, nil)
        end
        room:clearAG()
    end
}
wanbaochuiSkill = sgs.CreateZeroCardViewAsSkill{
    name = "wanbaochui",
    view_as = function(self, cards)
        return wanbaochuiCard:clone()
    end,
    enabled_at_play = function(self, player)
        return player:hasTreasure("wanbaochui") and not player:hasUsed("#wanbaochui")
    end,
}

dangzuosha = sgs.CreateTriggerSkill{
    name = "dangzuosha" ,
    events = {sgs.CardAsked},
    global = true,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        local pattern = data:toStringList()[1]
        if (pattern == "slash") and not player:isNude() then
            local count = 0
            for _, cardQ in sgs.list(player:getHandcards()) do
                if cardQ:isKindOf("Ofuda") and not player:isCardLimited(cardQ, sgs.Card_MethodUse) then count = count + 1 end
            end
            for _, cardQ in sgs.list(player:getPile("wooden_ox")) do
                local c = sgs.Sanguosha:getCard(cardQ)
                if c:isKindOf("Ofuda") and not player:isCardLimited(c, sgs.Card_MethodUse) then count = count + 1 end
            end
            local id
            if count > 0 then
                id = room:askForCard(player, "Ofuda", "@dangzuosha", sgs.QVariant(), sgs.Card_MethodNone)
            end
            if id then
                local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
                slash:setSkillName(self:objectName())
                slash:addSubcard(id)
                room:provide(slash)
                return true
            end
        end
    end,
    can_trigger = function(self, target)
        return target
    end
}

dolldefense = sgs.CreateTriggerSkill {
    name = "dolldefense",
    global = true,
    events = { sgs.TargetConfirmed },
    frequency = sgs.Skill_Frequent,
    on_trigger = function(self, event, player, data, room)
        local use = data:toCardUse()
        if use.to:contains(player) and use.from and use.from:objectName() ~= player:objectName() and not use.from:hasFlag("shanghai")
            and not use.card:isKindOf("EquipCard") and not use.card:isKindOf("SkillCard") and use.to:length() == 1
            and ((player:getDefensiveHorse() and player:getDefensiveHorse():objectName() == "hongrai")
                or (player:getOffensiveHorse() and player:getOffensiveHorse():objectName() == "shanghai"))
                and room:askForSkillInvoke(player, "shanghai", data) then
            room:setPlayerFlag(player, "shanghai")
            if player:getDefensiveHorse() and player:getDefensiveHorse():objectName() == "hongrai"
                and (not player:getOffensiveHorse() or player:getOffensiveHorse():objectName() ~= "shanghai") then
                local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
                dummy:addSubcard(player:getDefensiveHorse())
                room:throwCard(dummy, player, player)
            elseif player:getOffensiveHorse() and player:getOffensiveHorse():objectName() == "shanghai"
                    and (not player:getDefensiveHorse() or player:getDefensiveHorse():objectName() ~= "hongrai") then
                local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
                dummy:addSubcard(player:getOffensiveHorse())
                room:throwCard(dummy, player, player)
            elseif player:getOffensiveHorse() and player:getOffensiveHorse():objectName() == "shanghai"
                    and player:getDefensiveHorse() and player:getDefensiveHorse():objectName() == "hongrai" then
                local choice = room:askForChoice(player, "shanghai", "Shanghai+Hongrai")
                if choice ==  "Shanghai" then
                    local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
                    dummy:addSubcard(player:getOffensiveHorse())
                    room:throwCard(dummy, player, player)
                else
                    local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
                    dummy:addSubcard(player:getDefensiveHorse())
                    room:throwCard(dummy, player, player)
                end
            end
            use.to = sgs.SPlayerList()
            data:setValue(use)
            room:setPlayerFlag(player, "-shanghai")
            return
        end
    end
}

yanshiSkill2 = sgs.CreateTriggerSkill{
    name = "yanshi2",
    global = true,
    events = { sgs.CardsMoveOneTime },
    on_trigger = function(self, event, player, data, room)
        if event == sgs.CardsMoveOneTime then
            local move = data:toMoveOneTime()
            if move.to and move.to:objectName() == player:objectName() and not move.card_ids:isEmpty()
                    and move.to_place == sgs.Player_PlaceHand then
                player:addMark("yanshiMeili")
            elseif move.from and move.from:objectName() == player:objectName() and not move.card_ids:isEmpty() then
                local reason = move.reason.m_reason
                local reasonx = bit32.band(reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)
                local Yes = reasonx == sgs.CardMoveReason_S_REASON_DISCARD
                if Yes then
                    local i = 0
                    for _,id in sgs.qlist(move.card_ids) do
                        if move.from_places:at(i) == sgs.Player_PlaceHand then
                            player:addMark("yanshiRenko")
                            break
                        end
                        i = i + 1
                    end
                end
            else
                return false
            end
        end
    end
}

luapundarika = sgs.CreateTriggerSkill{
    name = "luapundarika" ,
    events = {sgs.TargetConfirmed},
    can_trigger = function(self, target)
        return target and target:isAlive() and target:hasWeapon("pundarika") and not target:hasFlag("luapundarika")
    end,
    on_trigger = function(self, event, player, data)
        local use = data:toCardUse()
        if use.from and (use.from:objectName() == player:objectName()) then
            if (use.card:isKindOf("Slash")) then
                if player:askForSkillInvoke(self:objectName(),data) then
                    player:getRoom():setPlayerFlag(player, "luapundarika")
                    player:drawCards(2)
                end
            end
        end
    end
}

luashourinken = sgs.CreateTriggerSkill{
    name = "luashourinken" , 
	global = true, 
    events = {sgs.TargetConfirmed}, 
    can_trigger = function(self, target)
        return true
    end,
    on_trigger = function(self, event, player, data)
        local use = data:toCardUse()
        local room = player:getRoom() 
        if use.from and player:hasWeapon("shourinken") and player:getPhase() == sgs.Player_Play then
            if (use.card:isKindOf("Slash")) and room:getCurrent():getMark("xuezhanda") == 2 then 
				for _, t in sgs.qlist(use.to) do
                    local _data = sgs.QVariant()
                    _data:setValue(p)
                    if player:askForSkillInvoke(self:objectName(), _data) then 
                        local to_throw = room:askForCardChosen(player, t, "he", self:objectName(), false, sgs.Card_MethodDiscard)
                        room:throwCard(sgs.Sanguosha:getCard(to_throw), t, player) 
                    end
                end 
            end
        end
    end
}

yanshiSkill = sgs.CreateTriggerSkill{
    name = "yanshi",
    global = true,
    events = {sgs.EventPhaseChanging},
    on_trigger = function(self, event, player, data, room)
        if event == sgs.EventPhaseChanging then
            local change = data:toPhaseChange()
            if change.to == sgs.Player_NotActive then
                local a = player:getMark("yanshiRenko") > 1
                local b = player:getMark("yanshiMeili") > 1
                if player:hasTreasure("yanshi") then
                    if a then player:drawCards(1) end
                    if b then
                        local qtargets = sgs.SPlayerList()
                        for _,p in sgs.qlist(room:getAlivePlayers()) do
                            if not p:isAllNude() then qtargets:append(p) end
                        end
                        local target = room:askForPlayerChosen(player, qtargets, "yanshi", "yanshi-invoke", true)
                        if not target then return end
                        local id = room:askForCardChosen(player, target, "hej", self:objectName(), false, sgs.Card_MethodDiscard)
                        room:throwCard(id, target, player)
                    end
                end
                for _,p in sgs.qlist(room:getAlivePlayers()) do
                    room:setPlayerMark(p, "yanshiRenko", 0)
                    room:setPlayerMark(p, "yanshiMeili", 0)
                end
            end
        end
    end,
}

luapundarika2 = sgs.CreateDistanceSkill{
    name = "luapundarika2",
    correct_func = function(self, from)
        if from:hasWeapon("pundarika") then
            return 1
        else
            return 0
        end
    end,
}

luatengu = sgs.CreateTriggerSkill{
    name = "luatengu" ,
    events = {sgs.TargetConfirmed, sgs.HpLost},
    can_trigger = function(self, target)
        return target and target:isAlive() and (target:getArmor() and target:getArmor():objectName() == "tengu")
    end,
    on_trigger = function(self, event, player, data, room)
        if event == sgs.TargetConfirmed then
            local use = data:toCardUse()
            if use.to:contains(player) and use.card and use.card:isKindOf("Slash") then
                player:drawCards(1)
                return false
            end
        else
            local lose = data:toInt()
            for i = 0, lose - 1, 1 do
                player:drawCards(1)
            end
        end
    end
}

nimble_fabric_skill = sgs.CreateTriggerSkill{
	name = "nimble_fabric_skill",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardEffected, sgs.DamageForseen},
    can_trigger = function(self, target)
        return target and target:isAlive() and (target:getArmor() and target:getArmor():objectName() == "nimble_fabric")
    end,
	on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if event == sgs.CardEffected then   
            local effect = data:toCardEffect()
            if effect.from and effect.from:hasWeapon("qinggang_sword") then return false end  
            if effect.card and (effect.card:isKindOf("Slash") and not (effect.card:isKindOf("ThunderSlash") or effect.card:isKindOf("FireSlash"))) then 
                return true
            end
        elseif event == sgs.DamageForseen then  
			local damage = data:toDamage()  
            if damage.from and damage.from:hasWeapon("qinggang_sword") then return false end 
            if damage.to and damage.to:getArmor() and damage.to:getArmor():isKindOf("NimbleFabric") then
                if player:objectName() ~= damage.to:objectName() then return false end
                if damage.nature == sgs.DamageStruct_Thunder then 
					for _, shikieki in sgs.qlist(room:findPlayersBySkillName("luashuojiao")) do 
						if shikieki and shikieki:isAlive() then return false end 
					end 
                    damage.damage = damage.damage + 1
                    room:notifySkillInvoked(damage.to, "nimble_fabric_skill") 
                    data:setValue(damage) 
                end
                if damage.nature == sgs.DamageStruct_Normal and (not damage.card) then 
                    return true 
                end 
            end
        end 
	end
}
if not sgs.Sanguosha:getSkill("yanshi2") then skills:append(yanshiSkill2) end
if not sgs.Sanguosha:getSkill("yanshi") then skills:append(yanshiSkill) end
if not sgs.Sanguosha:getSkill("goheiChange") then skills:append(goheiChange) end
if not sgs.Sanguosha:getSkill("gohei_grow") then skills:append(gohei_grow) end
if not sgs.Sanguosha:getSkill("gohei_grow2") then skills:append(gohei_grow2) end
if not sgs.Sanguosha:getSkill("luayewang3") then skills:append(luayewang3) end
if not sgs.Sanguosha:getSkill("lualianmeng3") then skills:append(lualianmeng3) end
if not sgs.Sanguosha:getSkill("hakkero_gain") then skills:append(hakkero_gain) end
if not sgs.Sanguosha:getSkill("wanbaochui") then skills:append(wanbaochuiSkill) end
if not sgs.Sanguosha:getSkill("dangzuosha") then skills:append(dangzuosha) end
if not sgs.Sanguosha:getSkill("dolldefense") then skills:append(dolldefense) end
if not sgs.Sanguosha:getSkill("luapundarika") then skills:append(luapundarika) end
if not sgs.Sanguosha:getSkill("luashourinken") then skills:append(luashourinken) end
if not sgs.Sanguosha:getSkill("luapundarika2") then skills:append(luapundarika2) end
if not sgs.Sanguosha:getSkill("luatengu") then skills:append(luatengu) end
if not sgs.Sanguosha:getSkill("nimble_fabric_skill") then skills:append(nimble_fabric_skill) end
if not sgs.Sanguosha:getSkill("erin_bow_slash") then skills:append(erin_bow_slash) end



erin_bow = sgs.CreateWeapon{
    name = "erin_bow",
    class_name = "ErinBow",
    suit = sgs.Card_Heart,
    number = 5,
    range = 5,
    on_install = function(self, player)
        local room = player:getRoom()
        local skill = sgs.Sanguosha:getTriggerSkill("erin_bow_slash")
        if skill then room:getThread():addTriggerSkill(skill) end
    end
}

gohei = sgs.CreateWeapon{
    name = "gohei",
    class_name = "Gohei",
    suit = sgs.Card_Diamond,
    number = 10,
    range = 2,
    on_install = function(self, player)
        local room = player:getRoom()
        local skill = sgs.Sanguosha:getTriggerSkill("gohei_grow")
        if skill then room:getThread():addTriggerSkill(skill) end
    end
}

hakkero = sgs.CreateWeapon{
    name = "hakkero",
    class_name = "Hakkero",
    suit = sgs.Card_Spade,
    number = 6,
    range = 5,
    on_install = function(self, player)
        local room = player:getRoom()
        local skill = sgs.Sanguosha:getTriggerSkill("hakkero_gain")
        if skill then room:getThread():addTriggerSkill(skill) end
    end
}

wanbaochui = sgs.CreateTreasure{
    name = "wanbaochui",
    class_name = "Wanbaochui",
    suit = sgs.Card_Heart,
    number = 3,
    on_install = function(self, player)
        local room = player:getRoom()
        room:handleAcquireDetachSkills(player, "wanbaochui")
    end,
    on_uninstall = function(self, player)
        local room = player:getRoom()
        room:handleAcquireDetachSkills(player, "-wanbaochui")
        if not player:isKongcheng() and not player:hasFlag("DontDiscard") then
            room:askForDiscard(player, self:objectName(), 1, 1, false, false)
        end
    end
}

yanshi = sgs.CreateTreasure{
    name = "yanshi",
    class_name = "Yanshi",
    suit = sgs.Card_Spade,
    number = 7,
    on_install = function(self, player)
        local room = player:getRoom()
        --room:handleAcquireDetachSkills(player, "luayanshi")
    end,
    on_uninstall = function(self, player)
        local room = player:getRoom()
        --room:handleAcquireDetachSkills(player, "-yanshi")
    end
}

pundarika = sgs.CreateWeapon{
    name = "pundarika",
    class_name = "Pundarika",
    suit = sgs.Card_Club,
    number = 1,
    range = 1,
    on_install = function(self, player)
        local room = player:getRoom()
        local skill = sgs.Sanguosha:getTriggerSkill("luapundarika")
        if skill then room:getThread():addTriggerSkill(skill) end
    end,
}

shourinken = sgs.CreateWeapon{
    name = "shourinken",
    class_name = "Shourinken",
    suit = sgs.Card_Club,
    number = 2,
    range = 2,
    on_install = function(self, player)
        local room = player:getRoom()
        local skill = sgs.Sanguosha:getTriggerSkill("luashourinken")
        if skill then room:getThread():addTriggerSkill(skill) end
    end,
}


roukanken = sgs.CreateWeapon{
    name = "roukanken",
    class_name = "Roukanken",
    suit = sgs.Card_Club,
    number = 3,
    range = 3,
    on_uninstall = function(self, player)
        local room = player:getRoom()
    end,
}

roukankenx = sgs.CreateArmor{
    name = "roukankenx",
    class_name = "Roukankenx",
    suit = sgs.Card_Club,
    number = 3,
    on_install = function(self, player)
        local room = player:getRoom()
        room:writeToConsole("RoukankenArFlag Clear!")
        player:setFlags("-RoukankenArFlag") 
    end,
}

tengu = sgs.CreateArmor{
    name = "tengu",
    class_name = "Tengu",
    suit = sgs.Card_Diamond,
    number = 13,
    on_install = function(self, player)
        local room = player:getRoom()
        local skill = sgs.Sanguosha:getTriggerSkill("luatengu")
        if skill then room:getThread():addTriggerSkill(skill) end
    end,
}

nimble_fabric = sgs.CreateArmor{
    name = "nimble_fabric",
    class_name = "NimbleFabric",
    suit = sgs.Card_Club,
    number = 10,
    on_install = function(self, player)
        local room = player:getRoom()
        local skill = sgs.Sanguosha:getTriggerSkill("nimble_fabric_skill")
        if skill then room:getThread():addTriggerSkill(skill) end
    end,
}
local ThunderSlash = sgs.Sanguosha:getEngineCard(111)
local ThunderSlash2 = sgs.Sanguosha:cloneCard(ThunderSlash:objectName(), sgs.Card_Spade, 1)
local ThunderSlash3 = sgs.Sanguosha:cloneCard(ThunderSlash:objectName(), sgs.Card_Spade, 8)
local ThunderSlash4 = sgs.Sanguosha:cloneCard(ThunderSlash:objectName(), sgs.Card_Club, 4)
local ThunderSlash5 = sgs.Sanguosha:cloneCard(ThunderSlash:objectName(), sgs.Card_Club, 5)
local ThunderSlash6 = sgs.Sanguosha:cloneCard(ThunderSlash:objectName(), sgs.Card_Club, 6)

local IronChain = sgs.Sanguosha:getEngineCard(131)
local IronChain2 = sgs.Sanguosha:cloneCard(IronChain:objectName(), sgs.Card_Spade, 2)
local IronChain3 = sgs.Sanguosha:cloneCard(IronChain:objectName(), sgs.Card_Club, 9)

local ASlash = sgs.Sanguosha:getEngineCard(0)
local ASlash2 = sgs.Sanguosha:cloneCard(ASlash:objectName(), sgs.Card_Spade, 3)
local ASlash3 = sgs.Sanguosha:cloneCard(ASlash:objectName(), sgs.Card_Spade, 4)
local ASlash4 = sgs.Sanguosha:cloneCard(ASlash:objectName(), sgs.Card_Club, 8)
local ASlash5 = sgs.Sanguosha:cloneCard(ASlash:objectName(), sgs.Card_Diamond, 3)
local ASlash6 = sgs.Sanguosha:cloneCard(ASlash:objectName(), sgs.Card_Heart, 5)

local Analeptic = sgs.Sanguosha:getEngineCard(123)
local Analeptic2 = sgs.Sanguosha:cloneCard(Analeptic:objectName(), sgs.Card_Spade, 9)

local APeach = sgs.Sanguosha:getEngineCard(45)
local APeach2 = sgs.Sanguosha:cloneCard(APeach:objectName(), sgs.Card_Diamond, 12)
local APeach3 = sgs.Sanguosha:cloneCard(APeach:objectName(), sgs.Card_Heart, 4)
local APeach4 = sgs.Sanguosha:cloneCard(APeach:objectName(), sgs.Card_Heart, 6)
local APeach5 = sgs.Sanguosha:cloneCard(APeach:objectName(), sgs.Card_Heart, 8)

local FireSlash = sgs.Sanguosha:getEngineCard(123)
local FireSlash2 = sgs.Sanguosha:cloneCard(FireSlash:objectName(), sgs.Card_Diamond, 4)

local AJink = sgs.Sanguosha:getEngineCard(30)
local AJink2 = sgs.Sanguosha:cloneCard(AJink:objectName(), sgs.Card_Diamond, 5)
local AJink3 = sgs.Sanguosha:cloneCard(AJink:objectName(), sgs.Card_Diamond, 7)
local AJink4 = sgs.Sanguosha:cloneCard(AJink:objectName(), sgs.Card_Diamond, 8)
local AJink5 = sgs.Sanguosha:cloneCard(AJink:objectName(), sgs.Card_Diamond, 9)
local AJink6 = sgs.Sanguosha:cloneCard(AJink:objectName(), sgs.Card_Heart, 11)

local ANullification = sgs.Sanguosha:getEngineCard(97)
local ANullification2 = sgs.Sanguosha:cloneCard(ANullification:objectName(), sgs.Card_Club, 13)
local ANullification3 = sgs.Sanguosha:cloneCard(ANullification:objectName(), sgs.Card_Heart, 12)

local AmazingGrace = sgs.Sanguosha:getEngineCard(70)
local AmazingGrace2 = sgs.Sanguosha:cloneCard(AmazingGrace:objectName(), sgs.Card_Heart, 7)

local Dismantlement = sgs.Sanguosha:getEngineCard(90)
local Dismantlement2 = sgs.Sanguosha:cloneCard(Dismantlement:objectName(), sgs.Card_Spade, 5)

local ExNihilo = sgs.Sanguosha:getEngineCard(80)
local ExNihilo2 = sgs.Sanguosha:cloneCard(ExNihilo:objectName(), sgs.Card_Heart, 10)


shourinken:setParent(extension)

--黑桃 1-13
ThunderSlash2:setParent(extension)
IronChain2:setParent(extension)
ASlash2:setParent(extension)
ASlash3:setParent(extension)
Dismantlement2:setParent(extension)
hakkero:setParent(extension)
yanshi:setParent(extension)
ThunderSlash3:setParent(extension)
Analeptic2:setParent(extension)
religion_battle:setParent(extension)
ofuda3:setParent(extension)
hui3:setParent(extension)
quanxiang:setParent(extension)

--梅花 1-13

pundarika:setParent(extension)
local shanghai = sgs.Sanguosha:cloneCard("OffensiveHorse", sgs.Card_Club, 2)
shanghai:setObjectName("shanghai")
shanghai:setParent(extension)
roukanken:setParent(extension)
roukankenx:setParent(extension)
ThunderSlash4:setParent(extension)
ThunderSlash5:setParent(extension)
ThunderSlash6:setParent(extension)
hui2:setParent(extension)
ASlash4:setParent(extension)
IronChain3:setParent(extension)
nimble_fabric:setParent(extension)
need_maribel2:setParent(extension)
need_maribel:setParent(extension)
ANullification2:setParent(extension)
--方块 1-13

faith_collection2:setParent(extension)
ofuda:setParent(extension)
ASlash5:setParent(extension)
FireSlash2:setParent(extension)
AJink2:setParent(extension)
AJink3:setParent(extension)
hui:setParent(extension)
AJink4:setParent(extension)
AJink5:setParent(extension)
gohei:setParent(extension)
faith_collection:setParent(extension)
APeach2:setParent(extension)
tengu:setParent(extension)
--红桃
girl_choosen:setParent(extension)
local hongrai = sgs.Sanguosha:cloneCard("DefensiveHorse", sgs.Card_Heart, 2)
hongrai:setObjectName("hongrai")
hongrai:setParent(extension)
wanbaochui:setParent(extension)
APeach3:setParent(extension)
ASlash6:setParent(extension)
APeach4:setParent(extension)
AmazingGrace2:setParent(extension)
APeach5:setParent(extension)
banquet:setParent(extension)
ExNihilo2:setParent(extension)
AJink6:setParent(extension)
ANullification3:setParent(extension)
ofuda:clone(2, 13):setParent(extension)
--
--
erin_bow:setParent(extension)

local mamizou_cards = {}
for i = 0, 1000 do
	if i < 161 then 
		local card = sgs.Sanguosha:getEngineCard(i)
		if card == nil then break end
		if not (Set(sgs.Sanguosha:getBanPackages()))[card:getPackage()] and not table.contains(mamizou_cards, card:objectName()) 
			and not table.contains(sgs.Sanguosha:getBanPackages(), card:getPackage())
			and not card:isKindOf("DoubleSword") and not card:isKindOf("Roukankenx") and not card:isKindOf("Halberd") then
			table.insert(mamizou_cards, card:objectName())
			local cards = sgs.Sanguosha:cloneCard(card:objectName(), sgs.Card_Spade, 13)
			if cards then 
				cards:setParent(extension)
			end 
		end
	end 
end 
ofuda:clone(0, 13):setParent(extension)
hui:clone(0, 13):setParent(extension)
faith_collection:clone(0, 13):setParent(extension)
banquet:clone(0, 13):setParent(extension)
religion_battle:clone(0, 13):setParent(extension)
wanbaochui:clone(0, 13):setParent(extension)


sgs.Sanguosha:addSkills(skills)

--添加卡牌扩展包
sgs.LoadTranslationTable{
    ["gohei"] = "博丽御币",
    ["Gohei"] = "博丽御币",
    [":gohei"] = "装备牌·武器\
	<b>攻击范围</b>：X\
	<b>武器效果</b>：\
	◆锁定技，该武器的攻击范围初始为2，此后你的每个回合开始阶段增加1。\
	◆当你使用【杀】时，你可以将此【杀】的花色改为方块。",

    ["hakkero"] = "八卦炉",
    ["Hakkero"] = "八卦炉",
    [":hakkero"] = "装备牌·武器\
	<b>攻击范围</b>：5\
	<b>武器效果</b>：\
	出牌阶段限一次，当你的【杀】因弃置而置于弃牌堆时，你可以摸一张牌。",

    ["pundarika"] = "妙法莲华经",
    ["luapundarika"] = "妙法莲华经",
    ["Pundarika"] = "妙法莲华经",
    [":pundarika"] = "装备牌·武器\
	<b>攻击范围</b>：1\
	<b>武器效果</b>：\
	◆锁定技，你计算与其他角色的距离始终+1。\
	◆每回合限一次，你使用【杀】后，你可以摸两张牌。",

    ["shourinken"] = "丙子椒林剑",
    ["luashourinken"] = "丙子椒林剑",
    ["Shourinken"] = "丙子椒林剑",
    [":shourinken"] = "装备牌·武器\
	<b>攻击范围</b>：2\
	<b>武器效果</b>：\
	◆每回合使用的第二张【杀】指定目标后，若此时是你出牌阶段，你可以弃置目标一张牌。",

    ["erin_bow"] = "思兼神弓",
    ["luaerinbow"] = "思兼神弓",
    ["ErinBow"] = "思兼神弓",
    [":erin_bow"] = "装备牌·武器\
	<b>攻击范围</b>：5\
	<b>武器效果</b>：\
	◆每回合限一次，你使用牌指定多名角色时，可以视为对一名其他角色使用一张【杀】。",

    ["roukanken"] = "妖梦之灵",
    ["luaroukanken"] = "妖梦之灵",
    ["Roukanken"] = "妖梦之灵",
    [":roukanken"] = "装备牌·武器\
	<b>攻击范围</b>：3\
	<b>武器效果</b>：\
	◆这张牌从装备区被切换后，你可以获得之，并对攻击范围内的一名角色造成一点伤害\
	◆这张牌可以装备在防具区，使你使用【杀】次数+1。",

    ["roukankenx"] = "妖梦之灵",
    ["luaroukankenx"] = "妖梦之灵",
    ["Roukankenx"] = "妖梦之灵",
    [":roukankenx"] = "装备牌·防具\
	<b>攻击范围</b>：3\
	<b>武器效果</b>：\
	◆这张牌从装备区被切换后，你可以获得之，并对攻击范围内的一名角色造成一点伤害\
	◆这张牌可以装备在防具区，使你使用【杀】次数+1。",
    ["weaponPay"] = "装备在武器区",
    ["armorPay"] = "装备在防具区",
    ["lualouguan-invoke"] = "你可以用妖梦之灵砍人<br/> <b>操作提示</b>: 选择一名角色→点击确定<br/>",

    ["shanghai"] = "上海人形",
    ["Shanghai"] = "上海人形",
    [":shanghai"] = "装备牌·坐骑\
	<b>坐骑效果</b>：你与其他角色计算距离时-1。当一名其他角色使用牌唯一指定目标为你时，你可以弃置此牌，取消之。",
	
    ["hongrai"] = "蓬莱人形",
    ["Hongrai"] = "蓬莱人形",
    [":hongrai"] = "装备牌·坐骑\
	<b>坐骑效果</b>：你与其他角色计算距离时+1。当一名其他角色使用牌唯一指定目标为你时，你可以弃置此牌，取消之。",


    ["yuzhi"] = "弹幕",
    ["Yuzhi"] = "弹幕",
    [":yuzhi"] = "锦囊牌\
	<b>时机</b>：每回合限一张\
	<b>目标</b>：攻击范围内的一名其他角色\
	<b>效果</b>：对其造成一点伤害。",

    ["dangzuosha"] = "当做杀",
    ["dangzuosha2"] = "当做杀",
    ["@dangzuosha"] = "你可以把下列亮起的牌于此时当做【杀】使用或打出。",
    ["@dangzuosha2"] = "请选择【杀】所要指定的目标。",

    ["wanbaochui"] = "万宝槌",
    ["Wanbaochui"] = "万宝槌",
    [":wanbaochui"] = "装备牌·宝物\
	<b>宝物效果</b>：\
	出牌阶段限一次，你可以展示牌堆顶的四张牌，并从那其中选一张获得之。当此牌被弃置时，你须弃一张手牌。",

    ["tengu"] = "天狗之盾",
    ["Tengu"] = "天狗之盾",
    [":tengu"] = "装备牌·防具\
	<b>防具效果</b>：\
	锁定技，当你成为杀的目标，或是流失体力后，你摸一张牌。",
    
    ["nimble_fabric"] = "闪避布",
    ["nimble_fabric_skill"] = "闪避布",
    ["NimbleFabric"] = "闪避布",
    [":nimble_fabric"] = "装备牌·防具\
	<b>防具效果</b>：\
	锁定技，普通【杀】对你无效。当你受到非属性伤害后，伤害来源必须展示此伤害牌，否则防止此伤害。\
    锁定技，你受到的雷属性伤害+1。",

    ["yanshi"] = "燕石博物志",
    ["Yanshi"] = "燕石博物志",
	["yanshi-invoke"] = "你可以使用“燕石博物志”的效果<br/> <b>操作提示</b>: 选择一名要被你弃牌的角色→点击确定<br/>",
    [":yanshi"] = "装备牌·宝物\
	<b>宝物效果</b>：\
	每个回合结束时：\
	若你于本回合弃置了两次以上的手牌，你摸一张牌；\
	若你于本回合获得了两次以上的手牌，你弃置一名角色区域里的一张牌。",


}