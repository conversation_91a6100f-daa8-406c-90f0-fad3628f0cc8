# QSanguosha-v2 武将开发完整指南

## 概述

在QSanguosha-v2中，武将是通过Lua脚本定义的，包括武将的基本属性、技能实现和AI逻辑。本指南将详细介绍如何从零开始创建一个完整的武将，包括代码实现、翻译文件和AI逻辑。

## 第一部分：开发环境准备

### 1. 文件结构
```
extensions/
├── your_package.lua        # 你的扩展包文件
└── ...

lua/ai/
├── your_package-ai.lua     # 对应的AI文件
└── ...

lang/zh_CN/Package/
├── YourPackage.lua         # 翻译文件
└── ...
```

### 2. 基本依赖
- 了解Lua编程语言基础
- 熟悉三国杀游戏规则
- 理解QSanguosha的技能系统

## 第二部分：武将创建步骤

### 第一步：创建扩展包

```lua
-- 创建扩展包
extension_your_package = sgs.Package("your_package")

-- 创建武将
your_general = sgs.General(extension_your_package, "your_general", "wei", 4, true, false, false)
```

#### 武将参数说明：
- `extension_your_package`: 所属扩展包
- `"your_general"`: 武将内部名称（英文）
- `"wei"`: 势力（wei/shu/wu/qun/god等）
- `4`: 体力值
- `true`: 是否为男性（true=男，false=女）
- `false`: 是否为双将武将
- `false`: 是否隐藏武将

### 第二步：技能类型详解

#### 1. 触发技能 (TriggerSkill)
```lua
skill_name = sgs.CreateTriggerSkill{
    name = "skill_name",                    -- 技能名称
    frequency = sgs.Skill_NotFrequent,      -- 技能频率
    events = {sgs.EventPhaseStart},         -- 触发时机
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        -- 技能逻辑实现
        return false  -- 是否阻止后续技能触发
    end
}
```

#### 2. 视为技能 (ViewAsSkill)
```lua
skill_card = sgs.CreateSkillCard{
    name = "skill_card",
    target_fixed = false,                   -- 是否指定目标
    filter = function(self, targets, to_select)
        -- 目标选择逻辑
        return true
    end,
    on_effect = function(self, effect)
        -- 技能效果实现
    end
}

skill_vs = sgs.CreateViewAsSkill{
    name = "skill_name",
    n = 1,                                  -- 需要的卡牌数量
    view_filter = function(self, selected, to_select)
        -- 卡牌选择过滤
        return true
    end,
    view_as = function(self, cards)
        -- 转化逻辑
        local card = skill_card:clone()
        for _, c in ipairs(cards) do
            card:addSubcard(c)
        end
        return card
    end
}
```

#### 3. 锁定技能
```lua
compulsory_skill = sgs.CreateTriggerSkill{
    name = "compulsory_skill",
    frequency = sgs.Skill_Compulsory,       -- 锁定技标识
    events = {sgs.DamageInflicted},
    on_trigger = function(self, event, player, data)
        -- 锁定技逻辑（自动触发）
        return false
    end
}
```

### 第三步：常用技能模板

#### 1. 摸牌技能
```lua
draw_skill = sgs.CreateTriggerSkill{
    name = "draw_skill",
    events = {sgs.EventPhaseStart},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if player:getPhase() == sgs.Player_Start then
            if room:askForSkillInvoke(player, self:objectName()) then
                player:drawCards(2)  -- 摸2张牌
            end
        end
        return false
    end
}
```

#### 2. 伤害技能
```lua
damage_skill = sgs.CreateTriggerSkill{
    name = "damage_skill",
    events = {sgs.DamageCaused},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        local damage = data:toDamage()
        
        if room:askForSkillInvoke(player, self:objectName()) then
            damage.damage = damage.damage + 1  -- 伤害+1
            data:setValue(damage)
        end
        return false
    end
}
```

#### 3. 防御技能
```lua
defense_skill = sgs.CreateTriggerSkill{
    name = "defense_skill",
    events = {sgs.DamageInflicted},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        local damage = data:toDamage()
        
        if room:askForSkillInvoke(player, self:objectName()) then
            damage.damage = damage.damage - 1  -- 伤害-1
            if damage.damage <= 0 then
                return true  -- 防止伤害
            end
            data:setValue(damage)
        end
        return false
    end
}
```

### 第四步：技能触发时机

#### 常用事件类型：
```lua
-- 回合阶段
sgs.TurnStart           -- 回合开始
sgs.EventPhaseStart     -- 阶段开始
sgs.EventPhaseEnd       -- 阶段结束

-- 卡牌相关
sgs.CardUsed           -- 使用卡牌时
sgs.CardFinished       -- 卡牌结算完毕
sgs.TargetSpecified    -- 指定目标时
sgs.TargetConfirmed    -- 确认目标时

-- 伤害相关
sgs.DamageCaused       -- 造成伤害时
sgs.DamageInflicted    -- 受到伤害时
sgs.Damaged            -- 受到伤害后
sgs.DamageForseen      -- 伤害预见时

-- 其他事件
sgs.Death              -- 死亡时
sgs.GameStart          -- 游戏开始
sgs.DrawNCards         -- 摸牌时
```

### 第五步：将技能添加到武将

```lua
-- 添加技能到武将
your_general:addSkill(skill_name)
your_general:addSkill(skill_name2)

-- 如果有关联技能，也要添加
if skill_name2 then
    your_general:addSkill(skill_name2)
end
```

## 第三部分：翻译文件

### 创建翻译文件

创建 `lang/zh_CN/Package/YourPackage.lua`：

```lua
return {
    -- 扩展包名
    ["your_package"] = "你的扩展包",
    
    -- 武将信息
    ["your_general"] = "你的武将",
    ["&your_general"] = "武将",  -- 简称
    ["#your_general"] = "称号",
    ["designer:your_general"] = "设计者",
    ["illustrator:your_general"] = "画师",
    ["cv:your_general"] = "配音",
    
    -- 技能信息
    ["skill_name"] = "技能名",
    [":skill_name"] = "技能描述：详细的技能效果说明。",
    
    -- 技能提示信息
    ["@skill_name"] = "请选择技能目标",
    ["~skill_name"] = "请选择要使用的卡牌",
    ["$skill_name1"] = "技能台词1",
    ["$skill_name2"] = "技能台词2",
    
    -- 死亡台词
    ["~your_general"] = "死亡台词...",
    
    -- 胜利台词
    ["$your_general:VICTORY"] = "胜利台词。",
    
    -- 技能动画
    ["$skill_nameAnimate"] = "anim=skill/skill_name",
    
    -- 其他提示信息
    ["#skill_name-effect"] = "%from 发动了"%arg"，效果描述",
    ["skill_name-invoke"] = "是否发动"技能名"？",
    
    -- 日志信息
    ["$SkillNameUse"] = "%from 发动"技能名"，产生了效果",
    
    -- AI相关提示（可选）
    ["skill_name:option"] = "技能选项",
    
    -- 模式相关（如果需要）
    ["mode_rule"] = "模式规则说明",
    
    -- 特殊情况提示
    ["skill_name_tip"] = "技能使用提示",
    
    -- 错误信息（调试用）
    ["skill_name_error"] = "技能发动失败",
    
    -- 成就相关（如果有成就系统）
    ["achievement_skill_master"] = "技能大师：使用该技能100次",
}
```

### 翻译文件规范

#### 1. 命名规范
- 武将名：`["general_name"] = "中文名"`
- 技能名：`["skill_name"] = "技能中文名"`
- 技能描述：`[":skill_name"] = "详细描述"`
- 技能台词：`["$skill_name1"] = "台词内容"`

#### 2. 特殊标记说明
- `&` - 武将简称
- `#` - 武将称号
- `:` - 技能描述（**限制120字以内**）
- `@` - 选择提示
- `~` - 操作提示
- `$` - 台词或动画
- `designer:` - 设计者
- `illustrator:` - 画师
- `cv:` - 配音

#### 3. 技能描述规范
- **字数限制**：每个技能描述必须控制在120字以内
- **语言要求**：简洁明了，避免冗余表达
- **内容完整**：在字数限制内准确描述技能机制
- **格式统一**：使用标准的技能描述格式

## 第四部分：AI开发指南

### 1. AI文件结构
创建 `lua/ai/your_package-ai.lua`：

```lua
-- AI优先级和价值评估
sgs.ai_skill_invoke.skill_name = function(self, data)
    -- 技能发动的AI判断
    return true  -- 是否发动技能
end

sgs.ai_skill_use["@@skill_name"] = function(self, prompt)
    -- 技能使用的AI逻辑
    local cards = self.player:getCards("he")
    if not cards:isEmpty() then
        return "@skill_name:" .. cards:first():getEffectiveId()
    end
    return "."
end

-- 目标选择AI
sgs.ai_skill_playerchosen.skill_name = function(self, targets)
    -- 选择目标的AI逻辑
    for _, target in ipairs(targets) do
        if self:isEnemy(target) then
            return target
        end
    end
    return targets[1]
end
```

### 2. AI评估函数
```lua
-- 卡牌价值评估
sgs.ai_use_value.SkillCard = function(card, player, purpose)
    return 6  -- 返回使用价值(1-10)
end

-- 保留价值评估
sgs.ai_keep_value.SkillCard = function(card, player)
    return 3  -- 返回保留价值(1-10)
end
```

### 3. 武将强度评估
```lua
local your_general_value = {
    attack = 7.5,   -- 攻击能力
    defense = 8.0,  -- 防御能力
    support = 6.0,  -- 辅助能力
    control = 5.0   -- 控制能力
}

-- 武将配合度（与其他武将的协同效果）
sgs.ai_chaofeng.your_general = 3  -- 仇恨值

-- 特殊情况处理
sgs.ai_cardneed.your_general = function(to, card, self)
    -- 武将需要的卡牌类型
    return card:isKindOf("Slash") or card:isKindOf("Jink")
end
```

## 第五部分：完整示例：创建赵云

### 武将代码示例 (extensions/example_generals.lua)

```lua
-- 创建扩展包
extension_example = sgs.Package("example_generals")

-- 创建武将：赵云，蜀势力，4血男武将
example_zhaoyun = sgs.General(extension_example, "example_zhaoyun", "shu", 4, true, false, false)

-- 技能1：龙胆 - 你可以将【杀】当【闪】使用或打出，或将【闪】当【杀】使用或打出
longdan_card = sgs.CreateSkillCard{
    name = "longdan",
    will_throw = false,
    filter = function(self, targets, to_select)
        local card = sgs.Sanguosha:getCard(self:getSubcards():first())
        if card:isKindOf("Slash") then
            return false  -- 杀当闪使用，无需目标
        elseif card:isKindOf("Jink") then
            return (#targets == 0) and to_select:objectName() ~= sgs.Self:objectName() 
                   and sgs.Self:canSlash(to_select, nil, false)
        end
        return false
    end,
    feasible = function(self, targets)
        local card = sgs.Sanguosha:getCard(self:getSubcards():first())
        if card:isKindOf("Slash") then
            return #targets == 0
        elseif card:isKindOf("Jink") then
            return #targets == 1
        end
        return false
    end,
    on_validate = function(self, card_use)
        local user = card_use.from
        local room = user:getRoom()
        local card = sgs.Sanguosha:getCard(self:getSubcards():first())
        
        room:notifySkillInvoked(user, "longdan")
        
        if card:isKindOf("Slash") then
            local jink = sgs.Sanguosha:cloneCard("jink", card:getSuit(), card:getNumber())
            jink:addSubcard(self:getSubcards():first())
            jink:setSkillName("longdan")
            return jink
        elseif card:isKindOf("Jink") then
            local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
            slash:addSubcard(self:getSubcards():first())
            slash:setSkillName("longdan")
            return slash
        end
        return nil
    end
}

longdan_vs = sgs.CreateOneCardViewAsSkill{
    name = "longdan",
    filter_pattern = "Slash,Jink",
    view_as = function(self, card)
        local longdan = longdan_card:clone()
        longdan:addSubcard(card)
        return longdan
    end,
    enabled_at_play = function(self, player)
        return sgs.Slash_IsAvailable(player)
    end,
    enabled_at_response = function(self, player, pattern)
        return pattern == "jink" or pattern == "slash"
    end
}

-- 技能2：冲阵 - 当你发动"龙胆"使用或打出牌时，你可以摸一张牌
chongzhen = sgs.CreateTriggerSkill{
    name = "chongzhen",
    events = {sgs.CardUsed, sgs.CardResponsed},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        local card = nil
        
        if event == sgs.CardUsed then
            local use = data:toCardUse()
            card = use.card
        else
            local response = data:toCardResponse()
            card = response.m_card
        end
        
        if card and card:getSkillName() == "longdan" then
            if room:askForSkillInvoke(player, self:objectName()) then
                player:drawCards(1)
            end
        end
        return false
    end
}

-- 将技能添加到武将
example_zhaoyun:addSkill(longdan_vs)
example_zhaoyun:addSkill(chongzhen)

-- 扩展包加载完成标记
sgs.LoadTranslationTable{
    ["example_generals"] = "示例武将包",
}
```

### 翻译文件示例 (lang/zh_CN/Package/ExampleGenerals.lua)

```lua
return {
    -- 扩展包信息
    ["example_generals"] = "示例武将包",
    ["designer:example_generals"] = "QSanguosha开发团队",
    
    -- 赵云
    ["example_zhaoyun"] = "赵云",
    ["&example_zhaoyun"] = "赵云",
    ["#example_zhaoyun"] = "少年将军",
    ["designer:example_zhaoyun"] = "官方",
    ["illustrator:example_zhaoyun"] = "KayaK",
    ["cv:example_zhaoyun"] = "神奇陆夫人",
    
    -- 赵云技能
    ["longdan"] = "龙胆",
    [":longdan"] = "你可以将【杀】当【闪】使用或打出，或将【闪】当【杀】使用或打出。", -- 32字 ✅
    ["@longdan"] = "龙胆：你可以将杀当闪使用，或将闪当杀使用",
    ["~longdan"] = "选择一张【杀】或【闪】→点击确定",
    ["$longdan1"] = "能进能退，乃真正法器！",
    ["$longdan2"] = "吾乃常山赵子龙也！",
    
    ["chongzhen"] = "冲阵",
    [":chongzhen"] = "当你发动"龙胆"使用或打出牌时，你可以摸一张牌。", -- 24字 ✅
    ["$chongzhen1"] = "冲锋陷阵，不惧生死！",
    ["$chongzhen2"] = "取敌将首级，如探囊取物！",
    
    -- 死亡台词
    ["~example_zhaoyun"] = "这，就是失败的滋味吗？",
    
    -- 胜利台词
    ["$example_zhaoyun:VICTORY"] = "能战胜你们，我很高兴。",
    
    -- 技能动画
    ["$longdanAnimate"] = "anim=skill/longdan",
    
    -- 其他提示信息
    ["#longdan-jink"] = "%from 发动了"%arg"，将 %card 当【闪】使用",
    ["#longdan-slash"] = "%from 发动了"%arg"，将 %card 当【杀】使用",
    ["#chongzhen-draw"] = "%from 发动了"%arg"，摸了一张牌",
    
    -- 选择提示
    ["longdan-slash"] = "请选择【杀】的目标",
    
    -- 日志信息
    ["$LongdanUse"] = "%from 发动"龙胆"，将 %card 当 %arg 使用",
    ["$ChongzhenDraw"] = "%from 发动"冲阵"，摸了一张牌",
    
    -- AI相关提示（可选）
    ["longdan:slash"] = "龙胆杀",
    ["longdan:jink"] = "龙胆闪",
    
    -- 特殊情况提示
    ["longdan_slash_tip"] = "龙胆：将【闪】当【杀】使用",
    ["longdan_jink_tip"] = "龙胆：将【杀】当【闪】使用",
    ["chongzhen_tip"] = "冲阵：发动龙胆后可以摸一张牌",
}
```

### AI文件示例 (lua/ai/example_generals-ai.lua)

```lua
-- 龙胆技能AI
sgs.ai_view_as.longdan = function(card, player, card_place)
    local suit = card:getSuitString()
    local number = card:getNumberString()
    local card_id = card:getEffectiveId()
    
    if card:isKindOf("Slash") then
        return ("jink:longdan[%s:%s]:%d"):format(suit, number, card_id)
    elseif card:isKindOf("Jink") then
        return ("slash:longdan[%s:%s]:%d"):format(suit, number, card_id)
    end
end

-- 龙胆技能使用价值
sgs.ai_use_value.longdan = function(card, player, purpose)
    if card:isKindOf("Slash") then
        return 8.5  -- 杀当闪的价值
    elseif card:isKindOf("Jink") then
        return 6.0  -- 闪当杀的价值
    end
    return 0
end

-- 冲阵技能发动判断
sgs.ai_skill_invoke.chongzhen = function(self, data)
    return true  -- 总是发动冲阵摸牌
end

-- 武将强度评估
local example_zhaoyun_value = {
    attack = 7.5,   -- 攻击能力
    defense = 8.0,  -- 防御能力
    support = 6.0,  -- 辅助能力
    control = 5.0   -- 控制能力
}

-- 武将配合度
sgs.ai_chaofeng.example_zhaoyun = 3  -- 仇恨值

-- 特殊情况处理
sgs.ai_cardneed.example_zhaoyun = function(to, card, self)
    -- 赵云需要杀和闪（因为可以互相转换）
    return card:isKindOf("Slash") or card:isKindOf("Jink")
end
```

## 第六部分：高级技能实现

### 1. 多选择技能
```lua
multi_choice_skill = sgs.CreateTriggerSkill{
    name = "multi_choice",
    events = {sgs.EventPhaseStart},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if player:getPhase() == sgs.Player_Start then
            local choices = "choice1+choice2+choice3"
            local choice = room:askForChoice(player, self:objectName(), choices)
            
            if choice == "choice1" then
                player:drawCards(1)
            elseif choice == "choice2" then
                player:drawCards(2)
                player:throwCard(player:getCards("h"):first(), sgs.Card_MethodDiscard)
            elseif choice == "choice3" then
                -- 其他效果
            end
        end
        return false
    end
}
```

### 2. 判定技能
```lua
judge_skill = sgs.CreateTriggerSkill{
    name = "judge_skill",
    events = {sgs.EventPhaseStart},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if player:getPhase() == sgs.Player_Start then
            local judge = sgs.JudgeStruct()
            judge.pattern = ".|red"  -- 判定红色成功
            judge.good = true
            judge.reason = self:objectName()
            judge.who = player
            
            room:judge(judge)
            
            if judge.card:isRed() then
                player:drawCards(2)  -- 判定成功效果
            end
        end
        return false
    end
}
```

### 3. 距离技能
```lua
distance_skill = sgs.CreateDistanceSkill{
    name = "distance_skill",
    correct_func = function(self, from, to)
        if from:hasSkill(self:objectName()) then
            return -1  -- 攻击距离+1
        end
        return 0
    end
}
```

### 4. 手牌上限技能
```lua
maxcards_skill = sgs.CreateMaxCardsSkill{
    name = "maxcards_skill",
    extra_func = function(self, target)
        if target:hasSkill(self:objectName()) then
            return target:getLostHp()  -- 手牌上限+已损失体力
        end
        return 0
    end
}
```

## 第七部分：调试和测试

### 1. 功能测试
```lua
-- 添加调试信息
room:writeToConsole("Debug: " .. player:objectName() .. " triggered " .. self:objectName())

-- 检查技能状态
if player:hasSkill("skill_name") then
    -- 技能存在时的逻辑
end
```

### 2. 性能优化
- 减少不必要的循环
- 优化条件判断顺序
- 避免重复计算

### 3. 兼容性测试
- 与现有武将的交互
- 特殊模式下的表现
- 边界情况处理

## 第八部分：平衡性设计原则

### 1. 强度控制
- 避免过强的无条件效果
- 设置合理的发动条件
- 考虑技能的使用频率

### 2. 交互性
- 提供对手的应对手段
- 避免单方面压制
- 鼓励策略性思考

### 3. 趣味性
- 创新的机制设计
- 符合角色背景
- 增加游戏变数

## 总结

通过这个完整的指南，你可以从基础到高级，全面掌握QSanguosha-v2的武将开发技术：

1. **基础开发**：武将创建、技能实现、翻译文件
2. **AI开发**：智能AI逻辑、价值评估、行为控制
3. **高级技能**：复杂机制、特殊效果、系统集成
4. **测试优化**：功能测试、性能优化、平衡调整

创造出独特而平衡的游戏内容，为QSanguosha-v2社区贡献你的创意！

## 快速参考

### 文件结构
```
extensions/your_package.lua              # 武将代码
lua/ai/your_package-ai.lua              # AI逻辑
lang/zh_CN/Package/YourPackage.lua      # 翻译文件
```

### 基本模板
```lua
-- 扩展包
extension = sgs.Package("package_name")

-- 武将
general = sgs.General(extension, "general_name", "kingdom", hp, male, dual, hidden)

-- 技能
skill = sgs.CreateTriggerSkill{...}

-- 添加技能
general:addSkill(skill)
```
