# QSanguosha-v2 新势力添加完整指南

## 概述

在QSanguosha-v2中，势力系统是游戏的核心组成部分。除了传统的魏蜀吴群神五个势力外，游戏还支持添加自定义势力。本指南将详细介绍如何添加新势力，包括配置文件修改、图片资源准备和翻译文件设置。

## 第一部分：势力系统分析

### 1. 现有势力结构

#### 官方势力
- **wei** (魏) - 蓝色 `#547998`
- **shu** (蜀) - 红色 `#D0796C`
- **wu** (吴) - 绿色 `#4DB873`
- **qun** (群) - 灰色 `#8A807A`
- **god** (神) - 金色 `#96943D`

#### 自定义势力（已存在）
根据 `lua/config.lua` 文件分析，游戏已经包含了多个自定义势力：
- **luahong** (红) - 绿色 `#4DB873`
- **luayao** (妖) - 紫色 `#ff00ff`
- **luayue** (月) - 蓝色 `#547998`
- **luafeng** (风) - 绿色 `#4DB873`
- **luadi** (地) - 橙色 `#ff9c00`
- **lualian** (莲) - 青色 `#04b1c1`
- **lualing** (灵) - 米色 `#fcf7d5`
- **luacai** (彩) - 粉色 `#e5004f`
- **luaxi** (夕) - 棕色 `#81511c`
- **luaxing** (星) - 紫色 `#8f82bc`

### 2. 势力系统文件结构

```
lua/config.lua                    # 势力配置主文件
image/kingdom/                    # 势力图片资源
├── frame/                        # 势力边框图片
│   ├── wei.png                   # 魏势力边框
│   ├── shu.png                   # 蜀势力边框
│   ├── wu.png                    # 吴势力边框
│   ├── qun.png                   # 群势力边框
│   ├── god.png                   # 神势力边框
│   └── your_kingdom.png          # 自定义势力边框
├── icon/                         # 势力图标
│   ├── wei.png                   # 魏势力图标
│   ├── shu.png                   # 蜀势力图标
│   ├── wu.png                    # 吴势力图标
│   ├── qun.png                   # 群势力图标
│   ├── god.png                   # 神势力图标
│   └── your_kingdom.png          # 自定义势力图标
└── corner/                       # 势力角标
    ├── wei.png                   # 魏势力角标
    ├── shu.png                   # 蜀势力角标
    ├── wu.png                    # 吴势力角标
    ├── qun.png                   # 群势力角标
    ├── god.png                   # 神势力角标
    └── your_kingdom.png          # 自定义势力角标
```

## 第二部分：添加新势力步骤

### 第一步：修改配置文件

#### 1. 编辑 lua/config.lua

找到 `kingdoms` 和 `kingdom_colors` 配置项：

```lua
config = {
    -- 其他配置...
    
    -- 在现有势力列表中添加新势力
    kingdoms = { 
        "wei", "shu", "wu", "qun", "god", 
        "luahong", "luayao", "luayue", "luafeng", "luadi", 
        "lualian", "lualing", "luacai", "luaxi", "luaxing",
        "your_new_kingdom"  -- 添加你的新势力
    },
    
    -- 为新势力设置颜色
    kingdom_colors = {
        wei = "#547998",
        shu = "#D0796C",
        wu = "#4DB873",
        qun = "#8A807A",
        god = "#96943D",
        
        luahong = "#4DB873",
        luayao = "#ff00ff",
        luayue = "#547998",
        luafeng = "#4DB873",
        luadi = "#ff9c00",
        lualian = "#04b1c1",
        lualing = "#fcf7d5",
        luacai = "#e5004f",
        luaxi = "#81511c",
        luaxing = "#8f82bc",
        
        your_new_kingdom = "#your_color_code",  -- 设置新势力颜色
    },
    
    -- 其他配置...
}
```

#### 2. 势力命名规范

- **势力ID**: 使用英文小写，如 `your_kingdom`
- **颜色代码**: 使用十六进制颜色代码，如 `#FF5733`
- **建议颜色**: 选择与现有势力区分明显的颜色

#### 3. 颜色选择建议

| 颜色类型 | 十六进制代码 | 效果 |
|---------|-------------|------|
| 深蓝色 | `#1E3A8A` | 沉稳、理智 |
| 紫色 | `#7C3AED` | 神秘、高贵 |
| 橙色 | `#EA580C` | 活力、热情 |
| 深绿色 | `#059669` | 自然、和谐 |
| 深红色 | `#DC2626` | 力量、激情 |
| 金色 | `#D97706` | 尊贵、财富 |
| 银色 | `#6B7280` | 科技、现代 |
| 青色 | `#0891B2` | 清新、智慧 |

### 第二步：准备图片资源

#### 1. 势力边框 (frame)

**文件位置**: `image/kingdom/frame/your_kingdom.png`

**规格要求**:
- 尺寸: 150×150px (建议)
- 格式: PNG (支持透明)
- 用途: 武将头像边框

**设计要点**:
- 体现势力特色和主题
- 与势力颜色协调
- 保持清晰的边框效果

#### 2. 势力图标 (icon)

**文件位置**: `image/kingdom/icon/your_kingdom.png`

**规格要求**:
- 尺寸: 32×32px 或 64×64px
- 格式: PNG (支持透明)
- 用途: 势力标识图标

**设计要点**:
- 简洁明了的图标设计
- 在小尺寸下仍然清晰可辨
- 体现势力的核心特征

#### 3. 势力角标 (corner)

**文件位置**: `image/kingdom/corner/your_kingdom.png`

**规格要求**:
- 尺寸: 根据现有文件调整
- 格式: PNG (支持透明)
- 用途: 卡牌角落的势力标识

**设计要点**:
- 适合放置在卡牌角落
- 与整体设计风格一致
- 清晰的势力识别

### 第三步：创建武将

#### 1. 在扩展包中使用新势力

```lua
-- 创建扩展包
extension_new_kingdom = sgs.Package("new_kingdom_package")

-- 创建使用新势力的武将
new_general = sgs.General(extension_new_kingdom, "new_general", "your_new_kingdom", 4, true, false, false)

-- 添加技能
-- ... 技能实现代码 ...

-- 将技能添加到武将
new_general:addSkill(skill_name)
```

#### 2. 武将参数说明

- `"new_general"`: 武将内部名称
- `"your_new_kingdom"`: 使用你添加的新势力ID
- `4`: 体力值
- `true`: 性别 (true=男, false=女)
- `false`: 是否为双将
- `false`: 是否为隐藏武将

### 第四步：翻译文件

#### 1. 创建翻译文件

创建或编辑 `lang/zh_CN/Package/YourPackage.lua`:

```lua
return {
    -- 扩展包名
    ["new_kingdom_package"] = "新势力扩展包",
    
    -- 势力翻译
    ["your_new_kingdom"] = "你的势力名",
    
    -- 武将信息
    ["new_general"] = "武将中文名",
    ["&new_general"] = "武将简称",
    ["#new_general"] = "武将称号",
    ["designer:new_general"] = "设计者",
    ["illustrator:new_general"] = "画师",
    
    -- 技能翻译
    ["skill_name"] = "技能名",
    [":skill_name"] = "技能描述",
    
    -- 其他翻译...
}
```

#### 2. 势力名称建议

- 选择有意义的中文名称
- 体现势力的特色和背景
- 避免与现有势力重名
- 考虑名称的简洁性和易读性

## 第三部分：高级配置

### 1. 势力背景设置

如果要为新势力设置专用背景，需要修改皮肤配置文件：

#### 编辑 skins/defaultSkin.image.json

```json
{
    "tableBg": "image/system/backdrop/tableBg.jpg",
    "tableBgwei": "image/system/backdrop/wei.jpg",
    "tableBgshu": "image/system/backdrop/shu.jpg",
    "tableBgwu": "image/system/backdrop/wu.jpg",
    "tableBgqun": "image/system/backdrop/qun.jpg",
    "tableBggod": "image/system/backdrop/god.jpg",
    "tableBgyour_new_kingdom": "image/system/backdrop/your_kingdom.jpg"
}
```

### 2. 势力特殊规则

#### 在扩展包中添加势力特殊规则

```lua
-- 势力特殊技能或规则
kingdom_special_skill = sgs.CreateTriggerSkill{
    name = "kingdom_special",
    frequency = sgs.Skill_Compulsory,
    events = {sgs.GameStart},
    on_trigger = function(self, event, player, data)
        if player:getKingdom() == "your_new_kingdom" then
            -- 势力特殊效果
            player:drawCards(1)
        end
        return false
    end
}

-- 为所有该势力武将添加特殊技能
sgs.LoadTranslationTable{
    ["kingdom_special"] = "势力特技",
    [":kingdom_special"] = "锁定技，游戏开始时，你摸一张牌。",
}
```

### 3. 势力互动规则

#### 设置势力间的特殊关系

```lua
-- 势力联盟或敌对关系
function isAlly(kingdom1, kingdom2)
    local allies = {
        ["your_new_kingdom"] = {"wei", "shu"},  -- 新势力与魏蜀联盟
        ["wei"] = {"your_new_kingdom"},
        ["shu"] = {"your_new_kingdom"}
    }
    
    if allies[kingdom1] then
        for _, ally in ipairs(allies[kingdom1]) do
            if ally == kingdom2 then
                return true
            end
        end
    end
    return false
end
```

## 第四部分：实战示例

### 示例：添加"仙"势力

#### 1. 修改配置文件

```lua
-- lua/config.lua
config = {
    kingdoms = { 
        "wei", "shu", "wu", "qun", "god", 
        "luahong", "luayao", "luayue", "luafeng", "luadi", 
        "lualian", "lualing", "luacai", "luaxi", "luaxing",
        "xian"  -- 添加仙势力
    },
    kingdom_colors = {
        -- 现有势力颜色...
        xian = "#E6E6FA",  -- 淡紫色，体现仙气
    },
}
```

#### 2. 准备图片资源

创建以下文件：
- `image/kingdom/frame/xian.png` - 仙势力边框（淡紫色边框，带仙云装饰）
- `image/kingdom/icon/xian.png` - 仙势力图标（仙鹤或云朵图案）
- `image/kingdom/corner/xian.png` - 仙势力角标

#### 3. 创建仙势力武将

**太上老君技能设计概览**：

| 技能名 | 类型 | 效果概述 | 联动机制 | 字数统计 |
|--------|------|----------|----------|----------|
| **炼丹** | 视为技能 | 红色牌当桃，获得"丹"标记 | 为八卦提供判定加成 | 42字 ✅ |
| **八卦** | 触发技能 | 判定闪避，有"丹"时必成功 | 消耗"丹"标记强化效果 | 48字 ✅ |
| **仙法** | 限定技能 | 三选一强力效果，消耗所有"丹" | 根据"丹"数量增强效果 | 71字 ✅ |
| **长生** | 锁定技能 | 完全免疫，仙法获得 | 仙法第三选项的结果 | 28字 ✅ |

**联动设计理念**：
- **炼丹→八卦**：炼制的丹药增强八卦判定成功率
- **炼丹→仙法**：积累的丹药数量影响仙法威力
- **八卦→仙法**：八卦消耗丹药，与仙法形成资源竞争
- **仙法→长生**：仙法可以获得长生状态，形成终极防御

## 技能联动机制详解

### 🔗 **"丹"标记系统**

**"丹"标记**是技能联动的核心资源：
- **获得方式**：每次使用炼丹技能获得1个
- **消耗方式**：八卦技能可选择消耗1个，仙法技能消耗所有
- **上限机制**：无上限，但实际游戏中很难超过5个
- **显示方式**：在武将牌上显示当前丹药数量

### 🛡️ **长生门槛设计**

**7个丹药门槛的设计理念**：
- **高门槛**：需要至少7次炼丹才能获得长生
- **长期规划**：鼓励玩家进行长期的资源积累
- **风险收益**：高投入换取强力的无敌状态
- **策略选择**：在积累过程中面临多次资源分配选择

**获得长生的时间成本**：
- **最快路径**：4回合（每回合炼丹2次，第4回合达到7个丹药）
- **理想情况**：考虑到手牌限制，通常需要5-6回合
- **实际情况**：考虑到战斗需求和八卦消耗，通常需要7-8回合
- **竞争压力**：八卦技能会消耗丹药，但影响相对减小

### 🚀 **炼丹效率提升影响**

**每回合限两次的设计影响**：
- **积累速度翻倍**：理论上每回合可获得2个丹药
- **长生可行性**：从理论技能变为实用技能
- **策略灵活性**：有更多资源进行八卦防御
- **游戏节奏**：中期就能考虑使用仙法

**平衡性考量**：
- **手牌需求**：需要更多红色牌支撑
- **机会成本**：每回合2次出牌机会的投入
- **目标竞争**：治疗目标可能不足
- **资源压力**：对红色牌的依赖增加

### ⚡ **联动效果矩阵**

| 丹药数量 | 获得时间 | 八卦效果 | 仙法治疗 | 仙法伤害 | 长生持续 |
|----------|----------|----------|----------|----------|----------|
| 0个 | 起始 | 正常判定 | 回复1点 | 造成1点 | ❌ 无法获得 |
| 2个 | 1回合 | 可必成功 | 回复3点 | 造成3点 | ❌ 无法获得 |
| 4个 | 2回合 | 可必成功 | 回复3点 | 造成3点 | ❌ 无法获得 |
| 6个 | 3回合 | 可必成功 | 回复3点 | 造成3点 | ❌ 无法获得 |
| 7个 | 4回合 | 可必成功 | 回复3点 | 造成3点 | ✅ 2回合 |
| 8个+ | 4+回合 | 可必成功 | 回复3点 | 造成3点 | ✅ 3+回合 |

### 🎯 **策略选择分析**

#### **长生流派**：
- 积累7+丹药→使用仙法获得长生→无敌状态
- 适合中后期决战和绝境翻盘
- 每回合2次炼丹大幅缩短积累时间

#### **爆发流派**：
- 积累3-6丹药→使用仙法伤害→快速清场
- 适合中期发力和压制对手
- 风险较高但收益明显

#### **防御流派**：
- 丹药主要用于八卦→保证防御成功率
- 适合面对高压攻击和持久战
- 稳健但缺乏爆发力

#### **平衡流派**：
- 根据局势灵活分配丹药用途
- 需要较高的策略判断能力
- 适应性强但无明显优势

**技能描述规范**：
- 每个技能描述严格控制在120字以内
- 简化复杂机制，保留核心效果
- 确保描述清晰易懂

**字数统计验证**：

| 技能名 | 技能描述 | 字数 | 联动机制 | 状态 |
|--------|----------|------|----------|------|
| 炼丹 | 出牌阶段限两次，你可以将一张红色牌当【桃】使用，然后获得一个"丹"标记。若目标为你，则额外回复1点体力。 | 42字 | 产生"丹"标记 | ✅ |
| 八卦 | 当你需要使用或打出【闪】时，你可以进行判定：若结果为红色，则视为你使用了一张【闪】。若你有"丹"，可消耗一个"丹"令此判定必定成功。 | 48字 | 消耗"丹"强化 | ✅ |
| 仙法 | 限定技，出牌阶段，你可以消耗所有"丹"并选择一项：1.所有角色回复体力；2.对所有其他角色造成雷电伤害；3.获得"长生"（需至少7个"丹"）。效果根据消耗的"丹"数量增强。 | 79字 | 消耗"丹"增强 | ✅ |
| 长生 | 锁定技，你不能成为【杀】的目标；当你受到伤害时，防止此伤害。 | 28字 | 仙法获得 | ✅ |

```lua
-- extensions/xian_package.lua
extension_xian = sgs.Package("xian_package")

-- 创建仙势力武将：太上老君
taishanglaojun = sgs.General(extension_xian, "taishanglaojun", "xian", 3, true, false, false)

--[[
技能1：炼丹 - 出牌阶段限两次，你可以将一张红色牌当【桃】使用，然后获得一个"丹"标记。若目标为你，则额外回复1点体力。
]]

-- 炼丹技能卡
liandan_card = sgs.CreateSkillCard{
    name = "liandan",
    will_throw = false,
    filter = function(self, targets, to_select)
        return #targets == 0 and (to_select:isDying() or to_select:isWounded())
    end,
    feasible = function(self, targets)
        return #targets == 1
    end,
    on_validate = function(self, card_use)
        local user = card_use.from
        local room = user:getRoom()

        room:notifySkillInvoked(user, "liandan")

        local peach = sgs.Sanguosha:cloneCard("peach", sgs.Card_NoSuit, 0)
        peach:addSubcard(self:getSubcards():first())
        peach:setSkillName("liandan")

        -- 如果目标是自己，标记为特殊炼丹
        if card_use.to:length() == 1 and card_use.to:first():objectName() == user:objectName() then
            room:setPlayerMark(user, "liandan_self", 1)
        end

        return peach
    end
}

liandan_vs = sgs.CreateOneCardViewAsSkill{
    name = "liandan",
    filter_pattern = ".|red",
    view_as = function(self, card)
        local liandan = liandan_card:clone()
        liandan:addSubcard(card)
        return liandan
    end,
    enabled_at_play = function(self, player)
        return player:getMark("liandan_used") < 2
    end,
    enabled_at_response = function(self, player, pattern)
        return pattern == "peach" and player:getMark("liandan_used") < 2
    end
}

-- 炼丹后续效果
liandan_effect = sgs.CreateTriggerSkill{
    name = "#liandan_effect",
    events = {sgs.CardFinished},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        local use = data:toCardUse()

        if use.card and use.card:getSkillName() == "liandan" then
            -- 获得一个"丹"标记（联动核心）
            room:addPlayerMark(player, "dan", 1)

            -- 如果是对自己使用，额外回复1点体力
            if player:getMark("liandan_self") > 0 then
                room:setPlayerMark(player, "liandan_self", 0)
                local recover = sgs.RecoverStruct()
                recover.who = player
                recover.recover = 1
                room:recover(player, recover)
            end

            -- 增加使用次数标记
            room:addPlayerMark(player, "liandan_used", 1)
        end
        return false
    end
}

-- 重置炼丹标记
liandan_clear = sgs.CreateTriggerSkill{
    name = "#liandan_clear",
    events = {sgs.EventPhaseEnd},
    on_trigger = function(self, event, player, data)
        if player:getPhase() == sgs.Player_Play then
            player:getRoom():setPlayerMark(player, "liandan_used", 0)
        end
        return false
    end
}

--[[
技能2：八卦 - 当你需要使用或打出【闪】时，你可以进行判定：若结果为红色，则视为你使用了一张【闪】。若你有"丹"标记，则可以消耗一个"丹"标记令此判定必定成功。
]]

bagua = sgs.CreateTriggerSkill{
    name = "bagua",
    events = {sgs.CardAsked},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        local pattern = data:toStringList()[1]

        if pattern ~= "jink" then return false end

        if room:askForSkillInvoke(player, self:objectName()) then
            room:notifySkillInvoked(player, self:objectName())

            -- 检查是否有"丹"标记，可以选择消耗
            local dan_count = player:getMark("dan")
            local use_dan = false

            if dan_count > 0 then
                local choice = room:askForChoice(player, self:objectName(), "judge+usedan")
                if choice == "usedan" then
                    room:removePlayerMark(player, "dan", 1)
                    use_dan = true
                end
            end

            if use_dan then
                -- 消耗丹药，必定成功
                room:provide(sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0))
                return true
            else
                -- 正常判定
                local judge = sgs.JudgeStruct()
                judge.pattern = ".|red"
                judge.good = true
                judge.reason = self:objectName()
                judge.who = player

                room:judge(judge)

                if judge.card:isRed() then
                    room:provide(sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0))
                    return true
                end
            end
        end
        return false
    end
}

--[[
技能3：仙法 - 限定技，出牌阶段，你可以消耗所有"丹"标记并选择一项：1.所有角色回复1点体力；2.对所有其他角色造成1点雷电伤害；3.获得"长生"直到回合结束。（效果根据消耗的"丹"数量增强）
]]

-- 仙法技能卡
xianfa_card = sgs.CreateSkillCard{
    name = "xianfa",
    target_fixed = true,
    on_use = function(self, room, source, targets)
        room:notifySkillInvoked(source, "xianfa")

        -- 获取并消耗所有"丹"标记
        local dan_count = source:getMark("dan")
        if dan_count > 0 then
            room:setPlayerMark(source, "dan", 0)
        end

        -- 根据丹药数量决定可选项
        local choices = "heal+damage"
        if dan_count >= 7 then
            choices = choices .. "+changsheng"
        end

        local choice = room:askForChoice(source, "xianfa", choices)

        if choice == "heal" then
            -- 选项1：全体回复体力（丹药增强效果）
            local heal_amount = 1 + math.min(dan_count, 2)  -- 最多回复3点
            for _, player in sgs.qlist(room:getAllPlayers()) do
                if player:isWounded() then
                    local recover = sgs.RecoverStruct()
                    recover.who = source
                    recover.recover = heal_amount
                    room:recover(player, recover)
                end
            end

        elseif choice == "damage" then
            -- 选项2：雷电伤害（丹药增强伤害）
            local damage_amount = 1 + math.min(dan_count, 2)  -- 最多造成3点伤害
            for _, player in sgs.qlist(room:getOtherPlayers(source)) do
                local damage = sgs.DamageStruct()
                damage.from = source
                damage.to = player
                damage.damage = damage_amount
                damage.nature = sgs.DamageStruct_Thunder
                room:damage(damage)
            end

        elseif choice == "changsheng" then
            -- 选项3：获得长生（需要至少7个丹药）
            if dan_count >= 7 then
                local duration = dan_count - 5  -- 7个丹药=2回合，每多1个丹药+1回合
                room:setPlayerMark(source, "changsheng", duration)
            else
                -- 丹药不足，无法获得长生
                room:sendLog("#xianfa-changsheng-fail", source, self:objectName(), tostring(dan_count))
            end
        end
    end
}

xianfa_vs = sgs.CreateZeroCardViewAsSkill{
    name = "xianfa",
    view_as = function(self)
        return xianfa_card:clone()
    end,
    enabled_at_play = function(self, player)
        return player:getMark("xianfa_used") == 0
    end
}

-- 仙法使用标记
xianfa_used = sgs.CreateTriggerSkill{
    name = "#xianfa_used",
    events = {sgs.CardFinished},
    on_trigger = function(self, event, player, data)
        local use = data:toCardUse()
        if use.card and use.card:getSkillName() == "xianfa" then
            player:getRoom():setPlayerMark(player, "xianfa_used", 1)
        end
        return false
    end
}

--[[
技能4：长生 - 锁定技，你不能成为【杀】的目标；当你受到伤害时，防止此伤害。
（此技能通过仙法获得，回合结束时失去）
]]

changsheng = sgs.CreateProhibitSkill{
    name = "changsheng",
    is_prohibited = function(self, from, to, card)
        return to:getMark("changsheng") > 0 and card:isKindOf("Slash")
    end
}

changsheng_prevent = sgs.CreateTriggerSkill{
    name = "#changsheng_prevent",
    events = {sgs.DamageInflicted},
    on_trigger = function(self, event, player, data)
        if player:getMark("changsheng") > 0 then
            player:getRoom():notifySkillInvoked(player, "changsheng")
            return true  -- 防止伤害
        end
        return false
    end
}

-- 长生技能失效（支持多回合持续）
changsheng_clear = sgs.CreateTriggerSkill{
    name = "#changsheng_clear",
    events = {sgs.TurnEnd},
    on_trigger = function(self, event, player, data)
        local duration = player:getMark("changsheng")
        if duration > 0 then
            player:getRoom():removePlayerMark(player, "changsheng", 1)
        end
        return false
    end
}

-- 将技能添加到武将
taishanglaojun:addSkill(liandan_vs)
taishanglaojun:addSkill(liandan_effect)
taishanglaojun:addSkill(liandan_clear)
taishanglaojun:addSkill(bagua)
taishanglaojun:addSkill(xianfa_vs)
taishanglaojun:addSkill(xianfa_used)
taishanglaojun:addSkill(changsheng)
taishanglaojun:addSkill(changsheng_prevent)
taishanglaojun:addSkill(changsheng_clear)
```

#### 4. 翻译文件

```lua
-- lang/zh_CN/Package/XianPackage.lua
return {
    ["xian_package"] = "仙势力扩展包",
    ["xian"] = "仙",

    ["taishanglaojun"] = "太上老君",
    ["&taishanglaojun"] = "老君",
    ["#taishanglaojun"] = "道德天尊",
    ["designer:taishanglaojun"] = "QSanguosha开发团队",
    ["illustrator:taishanglaojun"] = "官方",
    ["cv:taishanglaojun"] = "神秘仙音",

    -- 炼丹技能
    ["liandan"] = "炼丹",
    [":liandan"] = "出牌阶段限两次，你可以将一张红色牌当【桃】使用，然后获得一个"丹"标记。若目标为你，则额外回复1点体力。", -- 42字 ✅
    ["@liandan"] = "炼丹：你可以将一张红色牌当【桃】使用",
    ["~liandan"] = "选择一张红色牌→选择目标→点击确定",
    ["$liandan1"] = "九转金丹，起死回生！",
    ["$liandan2"] = "仙丹妙药，药到病除。",
    ["#liandan-dan"] = "%from 炼制成功，获得了一个"丹"标记",
    ["#liandan-self"] = "%from 的炼丹对自己生效，额外回复了1点体力",

    -- 八卦技能
    ["bagua"] = "八卦",
    [":bagua"] = "当你需要使用或打出【闪】时，你可以进行判定：若结果为红色，则视为你使用了一张【闪】。若你有"丹"，可消耗一个"丹"令此判定必定成功。", -- 48字 ✅
    ["bagua:judge"] = "正常判定",
    ["bagua:usedan"] = "消耗丹药（必定成功）",
    ["$bagua1"] = "太极生两仪，两仪生四象！",
    ["$bagua2"] = "八卦推演，趋吉避凶。",
    ["$bagua3"] = "丹药助我，无往不利！",
    ["#bagua-judge"] = "%from 发动了"%arg"，进行了判定",
    ["#bagua-success"] = "%from 的"%arg"判定成功，视为使用了【闪】",
    ["#bagua-usedan"] = "%from 消耗了一个"丹"标记，"%arg"必定成功",

    -- 仙法技能
    ["xianfa"] = "仙法",
    [":xianfa"] = "限定技，出牌阶段，你可以消耗所有"丹"并选择一项：1.所有角色回复体力；2.对所有其他角色造成雷电伤害；3.获得"长生"（需至少7个"丹"）。效果根据消耗的"丹"数量增强。", -- 79字 ✅
    ["xianfa:heal"] = "所有角色回复体力",
    ["xianfa:damage"] = "造成雷电伤害",
    ["xianfa:changsheng"] = "获得长生",
    ["$xianfa1"] = "仙法无边，造化万物！",
    ["$xianfa2"] = "天地玄黄，宇宙洪荒！",
    ["$xianfa3"] = "道法自然，长生不老！",
    ["#xianfa-consume"] = "%from 消耗了 %arg 个"丹"标记",
    ["#xianfa-heal"] = "%from 发动了"%arg"，令所有角色回复了 %arg2 点体力",
    ["#xianfa-damage"] = "%from 发动了"%arg"，造成了 %arg2 点雷电伤害",
    ["#xianfa-changsheng"] = "%from 发动了"%arg"，获得了 %arg2 回合的长生",
    ["#xianfa-changsheng-fail"] = "%from 的"丹"标记不足（%arg/7），无法获得长生",

    -- 长生技能
    ["changsheng"] = "长生",
    [":changsheng"] = "锁定技，你不能成为【杀】的目标；当你受到伤害时，防止此伤害。", -- 28字 ✅
    ["$changsheng1"] = "长生不老，万劫不磨！",
    ["$changsheng2"] = "仙体护身，百毒不侵。",
    ["#changsheng-prohibit"] = "%from 的"%arg"使其不能成为【杀】的目标",
    ["#changsheng-prevent"] = "%from 的"%arg"防止了伤害",
    ["#changsheng-duration"] = "%from 的"长生"还将持续 %arg 回合",

    -- 丹标记相关
    ["dan"] = "丹",
    ["#dan-mark"] = "丹药标记：%arg 个",
    ["@dan-count"] = "当前丹药数量：%arg",

    -- 死亡台词
    ["~taishanglaojun"] = "道可道，非常道...名可名，非常名...",

    -- 胜利台词
    ["$taishanglaojun:VICTORY"] = "道法自然，顺应天理，此乃胜负之道。",

    -- 技能动画
    ["$liandanAnimate"] = "anim=skill/liandan",
    ["$baguaAnimate"] = "anim=skill/bagua",
    ["$xianfaAnimate"] = "anim=skill/xianfa",

    -- 其他提示信息
    ["liandan-invoke"] = "是否发动"炼丹"？",
    ["bagua-invoke"] = "是否发动"八卦"进行判定？",
    ["xianfa-invoke"] = "是否发动"仙法"？",

    -- 日志信息
    ["$LiandanUse"] = "%from 发动"炼丹"，将 %card 当【桃】使用",
    ["$BaguaJudge"] = "%from 发动"八卦"，判定结果为 %card",
    ["$XianfaUse"] = "%from 发动"仙法"，选择了 %arg",

    -- AI相关提示
    ["liandan:peach"] = "炼丹桃",
    ["bagua:jink"] = "八卦闪",
    ["xianfa:option"] = "仙法选项",

    -- 特殊情况提示
    ["liandan_tip"] = "炼丹：将红色牌当桃使用，对自己使用可回复2点体力",
    ["bagua_tip"] = "八卦：判定红色可视为使用闪，并可分配判定牌",
    ["xianfa_tip"] = "仙法：限定技，三种强力效果任选其一",
    ["changsheng_tip"] = "长生：完全免疫伤害和杀，回合结束失效",

    -- 成就相关
    ["achievement_xian_master"] = "仙道大师：使用太上老君获得10次胜利",
    ["achievement_liandan_master"] = "炼丹宗师：炼丹技能使用100次",
    ["achievement_xianfa_master"] = "仙法至尊：成功使用仙法的三种效果各10次",
}
```

#### 5. AI文件示例

```lua
-- lua/ai/xian_package-ai.lua

-- 炼丹技能AI
sgs.ai_view_as.liandan = function(card, player, card_place)
    local suit = card:getSuitString()
    local number = card:getNumberString()
    local card_id = card:getEffectiveId()

    if card:isRed() then
        return ("peach:liandan[%s:%s]:%d"):format(suit, number, card_id)
    end
end

sgs.ai_skill_use["@@liandan"] = function(self, prompt)
    local cards = self.player:getCards("he")
    local red_cards = {}
    local used_count = self.player:getMark("liandan_used")

    -- 如果已经用了2次，不能再用
    if used_count >= 2 then return "." end

    for _, card in sgs.qlist(cards) do
        if card:isRed() then
            table.insert(red_cards, card)
        end
    end

    if #red_cards == 0 then return "." end

    -- 优先选择价值较低的红色牌
    local best_card = nil
    local min_value = 999

    for _, card in ipairs(red_cards) do
        local value = self:getKeepValue(card)
        if value < min_value then
            min_value = value
            best_card = card
        end
    end

    if best_card then
        -- 选择目标：优先自己（如果受伤），然后是队友
        local targets = {}

        if self.player:isWounded() then
            table.insert(targets, self.player:objectName())
        else
            for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
                if player:isWounded() and not self:isEnemy(player) then
                    table.insert(targets, player:objectName())
                end
            end
        end

        if #targets > 0 then
            return "@liandan:" .. best_card:getEffectiveId() .. "->" .. targets[1]
        end
    end

    return "."
end

-- 炼丹技能发动判断
sgs.ai_skill_invoke.liandan = function(self, data)
    return true  -- 总是发动炼丹
end

-- 八卦技能发动判断
sgs.ai_skill_invoke.bagua = function(self, data)
    return true  -- 总是尝试八卦
end

-- 八卦选择逻辑（判定 vs 消耗丹药）
sgs.ai_skill_choice.bagua = function(self, choices)
    local dan_count = self.player:getMark("dan")

    -- 如果没有丹药，只能正常判定
    if dan_count == 0 then
        return "judge"
    end

    -- 如果生命危险或丹药较多，优先使用丹药
    local hp_ratio = self.player:getHp() / self.player:getMaxHp()
    if hp_ratio < 0.5 or dan_count >= 3 then
        return "usedan"
    else
        return "judge"  -- 正常情况下先尝试判定
    end
end

-- 仙法技能发动判断
sgs.ai_skill_invoke.xianfa = function(self, data)
    -- 根据局势和丹药数量判断是否使用限定技
    local hp_ratio = self.player:getHp() / self.player:getMaxHp()
    local dan_count = self.player:getMark("dan")
    local enemy_count = 0
    local wounded_count = 0

    for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
        if self:isEnemy(player) then
            enemy_count = enemy_count + 1
        end
        if player:isWounded() then
            wounded_count = wounded_count + 1
        end
    end

    -- 考虑丹药数量的发动条件（每回合可获得2个丹药，积累更快）
    if dan_count >= 8 then
        return true  -- 丹药充足时积极使用
    elseif dan_count >= 7 and hp_ratio < 0.4 then
        return true  -- 可以获得长生且血量较低时使用
    elseif dan_count >= 4 and (hp_ratio < 0.3 or enemy_count >= 2) then
        return true  -- 丹药较多且局势危险时使用
    elseif dan_count >= 2 and hp_ratio < 0.2 then
        return true  -- 有丹药且血量极危险时使用
    elseif dan_count == 0 and hp_ratio < 0.1 then
        return true  -- 无丹药但濒死时使用
    end

    return false
end

-- 仙法选择逻辑
sgs.ai_skill_choice.xianfa = function(self, choices)
    local hp_ratio = self.player:getHp() / self.player:getMaxHp()
    local dan_count = self.player:getMark("dan")  -- 即将消耗的丹药数量
    local enemy_count = 0
    local wounded_allies = 0
    local enemy_hp_total = 0

    for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
        if self:isEnemy(player) then
            enemy_count = enemy_count + 1
            enemy_hp_total = enemy_hp_total + player:getHp()
        elseif player:isWounded() then
            wounded_allies = wounded_allies + 1
        end
    end

    -- 根据丹药数量和局势决策
    local enhanced_damage = 1 + math.min(dan_count, 2)  -- 增强后的伤害
    local enhanced_heal = 1 + math.min(dan_count, 2)    -- 增强后的回复
    local can_changsheng = dan_count >= 7               -- 是否能获得长生
    local changsheng_duration = can_changsheng and (dan_count - 5) or 0  -- 长生持续时间

    -- 决策逻辑
    if can_changsheng and hp_ratio < 0.3 and changsheng_duration >= 2 then
        return "changsheng"  -- 血量极低且能获得较长长生时间
    elseif enemy_count >= 2 and enhanced_damage >= 2 and enemy_hp_total <= enhanced_damage * enemy_count then
        return "damage"      -- 能够清场或重创敌人时选择伤害
    elseif wounded_allies >= 2 and enhanced_heal >= 2 then
        return "heal"        -- 队友多人受伤且回复量足够时选择治疗
    elseif can_changsheng and hp_ratio < 0.5 then
        return "changsheng"  -- 能获得长生且血量较低时优先保命
    elseif enemy_count >= 2 then
        return "damage"      -- 敌人较多时选择伤害
    else
        return "heal"        -- 默认选择治疗
    end
end

-- 武将强度评估
local taishanglaojun_value = {
    attack = 6.0,   -- 攻击能力（仙法伤害）
    defense = 9.5,  -- 防御能力（炼丹回复+八卦闪避+长生无敌）
    support = 8.5,  -- 辅助能力（全体治疗）
    control = 7.0   -- 控制能力（雷电伤害+长生免疫）
}

-- 武将配合度
sgs.ai_chaofeng.taishanglaojun = 4  -- 较高仇恨值（因为有强力限定技）

-- 特殊情况处理
sgs.ai_cardneed.taishanglaojun = function(to, card, self)
    -- 太上老君需要红色牌（炼丹用）
    return card:isRed()
end

-- 技能使用价值评估
sgs.ai_use_value.liandan = function(card, player, purpose)
    return 8.5  -- 炼丹的使用价值很高
end

sgs.ai_keep_value.liandan = function(card, player)
    return 3.0  -- 保留价值中等
end

-- 长生技能期间的特殊AI
sgs.ai_skill_invoke.changsheng = function(self, data)
    return true  -- 长生是被动技能，总是生效
end

-- 武将评估完成
```

## 第五部分：测试和调试

### 1. 功能测试

#### 测试清单：
- [ ] 势力颜色正确显示
- [ ] 势力图标正确显示
- [ ] 势力边框正确显示
- [ ] 武将可以正常选择
- [ ] 势力名称正确翻译
- [ ] 游戏逻辑正常运行

#### 测试方法：
1. 启动游戏
2. 创建房间
3. 选择新势力武将
4. 开始游戏验证效果

### 2. 常见问题排除

#### 问题1：势力不显示
**可能原因**：
- 配置文件语法错误
- 势力ID拼写错误
- 图片文件缺失

**解决方法**：
1. 检查 `lua/config.lua` 语法
2. 确认势力ID一致性
3. 验证图片文件存在

#### 问题2：颜色不正确
**可能原因**：
- 颜色代码格式错误
- 缓存问题

**解决方法**：
1. 检查十六进制颜色代码格式
2. 重启游戏清除缓存

#### 问题3：图片显示异常
**可能原因**：
- 图片尺寸不合适
- 图片格式不支持
- 文件路径错误

**解决方法**：
1. 调整图片到合适尺寸
2. 使用PNG格式
3. 检查文件路径和命名

## 第六部分：进阶技巧

### 1. 势力主题包制作

#### 创建完整的势力主题包

```
your_kingdom_theme/
├── extensions/
│   └── your_kingdom.lua          # 势力武将包
├── image/
│   ├── kingdom/                  # 势力图片
│   ├── generals/                 # 武将头像
│   └── backdrop/                 # 势力背景
├── lang/
│   └── zh_CN/Package/            # 翻译文件
└── README.md                     # 说明文档
```

### 2. 势力平衡性设计

#### 设计原则：
- **数量平衡**：每个势力的武将数量相对均衡
- **强度平衡**：避免某个势力过强或过弱
- **特色鲜明**：每个势力有独特的玩法风格
- **互动性**：势力间有合理的制衡关系

### 3. 社区分享

#### 分享你的势力包：
1. 制作完整的安装包
2. 编写详细的说明文档
3. 在社区论坛发布
4. 收集反馈并持续改进

## 总结

添加新势力到QSanguosha-v2需要以下步骤：

1. **配置修改**：在 `lua/config.lua` 中添加势力ID和颜色
2. **图片准备**：制作势力边框、图标和角标
3. **武将创建**：使用新势力创建武将
4. **翻译设置**：添加势力和武将的中文翻译
5. **测试验证**：确保所有功能正常工作

通过这个完整的指南，你可以成功添加具有独特特色的新势力，丰富游戏的多样性和趣味性！

## 快速参考

### 关键文件位置
```
lua/config.lua                           # 势力配置主文件
image/kingdom/frame/your_kingdom.png     # 势力边框
image/kingdom/icon/your_kingdom.png      # 势力图标
image/kingdom/corner/your_kingdom.png    # 势力角标
lang/zh_CN/Package/YourPackage.lua       # 翻译文件
```

### 配置模板
```lua
-- 在 kingdoms 数组中添加
"your_new_kingdom"

-- 在 kingdom_colors 中添加
your_new_kingdom = "#your_color_code",
```
