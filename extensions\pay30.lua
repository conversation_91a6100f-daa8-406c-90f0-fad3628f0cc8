module("extensions.pay30",package.seeall)
extension = sgs.Package("pay30")
math.randomseed(tostring(os.time()):reverse():sub(1, 7))

yukariex = sgs.General(extension,"yukariex$","luayao",3,false,false,false)
yukariexA = sgs.General(extension,"yukariexA$","luayao",3,false,true,true)
yukariexB = sgs.General(extension,"yukariexB$","luayao",3,false,true,true)
yukariexC = sgs.General(extension,"yukariexC$","luayao",3,false,true,true)
yukariexD = sgs.General(extension,"yukariexD$","luayao",3,false,true,true)



local function isFriendQ(room, target, player)
	if player:getRole() == "rebel" then
		return target:getRole() == "rebel"
	end
	if player:getRole() == "loyalist" or player:getRole() == "lord" then
		return target:getRole() == "loyalist" or target:getRole() == "lord"
	end
end


local maixue = {
	"simayi", "xiah<PERSON>un", "guojia", "xunyu", "xunyou", "toyohime", "sumire<PERSON>", "kana", "keine", "toyosatomimi",
}
local OncePerTurn = {   --未实现的有 大妖精【祝福】 高丽野阿吽【守护】 娜兹铃【灵摆】 秦心【心舞】
	sunquan = {"zhiheng"},
	huanggai = {"kurou"},
	sunshangxiang = {"jieyin"},
	chengong = {"mingce"},
	masu = {"xinzhan"},
	wuguotai = {"ganlu"},

	shinki = {"shenpan"},
	parsee = {"duhuo"},
	medicine = {"linglan"},
	sumireko = {"chaogan"},
	ran = {"tianhu"},
	
	reimu = {"hakurei"},
	fujiwara = {"honghun"},
	suika = {"suiyue"},
	jz_reimu = {"boli"},
	tokiko = {"jieao"},
 
	sekibanki = {"yanguang"}, 
	doremi = {"mimeng"},
	yuyuko = {"youqu"},
	patchouli = {"xianzhe"},

	rumia = {"yueshi"},
	tewi = {"xingyun"},
	tatara = {"jinglian"},
	futo = {"zonghuo"},
	sp_suwako = {"jiangsui"},

	toyosatomimi = {"wangshi"},
	mihana = {"shenjun"},
	sangetsusei = {"xieli"}, 
}
local newDuel = function(card)
	return sgs.Sanguosha:cloneCard("iron_chain", card:getSuit(), card:getNumber())
end
local swapEquip = function(first, second)
	local room = first:getRoom()
	local equips1, equips2 = sgs.IntList(), sgs.IntList()
	for _, equip in sgs.qlist(first:getEquips()) do
		equips1:append(equip:getId())
	end
	for _, equip in sgs.qlist(second:getEquips()) do
		equips2:append(equip:getId())
	end
	local exchangeMove = sgs.CardsMoveList()
	local move1 = sgs.CardsMoveStruct(equips1, second, sgs.Player_PlaceEquip, 
			sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_SWAP, first:objectName(), second:objectName(), "ganlu", ""))
	local move2 = sgs.CardsMoveStruct(equips2, first, sgs.Player_PlaceEquip,
			sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_SWAP, first:objectName(), second:objectName(), "ganlu", ""))
	exchangeMove:append(move2)
	exchangeMove:append(move1)
	room:moveCardsAtomic(exchangeMove, false)
end
local OncePerTurnSkills = {

	kurouFilter = function(self, selected, to_select)
		return #selected < 1
	end,
	kurouCard = sgs.CreateSkillCard{
		name = "kurou",
		target_fixed = true,
		on_use = function(self, room, source, targets)
			room:loseHp(source)
			room:setPlayerProperty(source, "jingjie_exR", sgs.QVariant(""))
		end
	},
	kurouMin = 1,

	zhihengFilter = function(self, selected, to_select)
		return not sgs.Self:isJilei(to_select)
	end,
	zhihengCard = sgs.CreateSkillCard{
		name = "zhiheng",
		target_fixed = true,
		mute = true,
		on_use = function(self, room, source, targets)
			if source:isAlive() then
				room:drawCards(source, self:subcardsLength(), "zhiheng")
				room:setPlayerProperty(source, "jingjie_exR", sgs.QVariant(""))
			end
		end
	},
	zhihengMin = 1,

	jieyinFilter = function(self, selected, to_select)
		if #selected > 1 or sgs.Self:isJilei(to_select) then return false end
		return not to_select:isEquipped()
	end,
	jieyinCard = sgs.CreateSkillCard{
		name = "jieyin" ,
		filter = function(self, targets, to_select)
			if #targets ~= 0 then return false end
			return to_select:isMale() and to_select:isWounded() and to_select:objectName() ~= sgs.Self:objectName()
		end ,
		on_effect = function(self, effect)
			local room = effect.from:getRoom()
			local recover = sgs.RecoverStruct()
			recover.card = self
			recover.who = effect.from
			room:recover(effect.from, recover, true)
			room:recover(effect.to, recover, true)
			room:setPlayerProperty(effect.from, "jingjie_exR", sgs.QVariant(""))
		end
	},
	jieyinMin = 2,

	qingnangFilter = function(self, selected, to_select)
		if #selected > 0 or sgs.Self:isJilei(to_select) then return false end
		return not to_select:isEquipped()
	end,	
	qingnangCard = sgs.CreateSkillCard{
		name = "qingnang",
		target_fixed = false,
		will_throw = true,
		filter = function(self, targets, to_select)
			return (#targets == 0) and (to_select:isWounded())
		end,
		feasible = function(self, targets)
			if #targets == 1 then
				return targets[1]:isWounded()
			end
			return #targets == 0 and sgs.Self:isWounded()
		end,
		on_use = function(self, room, source, targets)
			local target = targets[1] or source
			local effect = sgs.CardEffectStruct()
			effect.card = self
			effect.from = source
			effect.to = target
			room:cardEffect(effect)
			room:setPlayerProperty(effect.from, "jingjie_exR", sgs.QVariant(""))
		end,
		on_effect = function(self, effect)
			local dest = effect.to
			local room = dest:getRoom()
			local recover = sgs.RecoverStruct()
			recover.card = self
			recover.who = effect.from
			room:recover(dest, recover)
			room:setPlayerProperty(effect.from, "jingjie_exR", sgs.QVariant(""))
		end
	},
	qingnangMin = 1,	

	mingceFilter = function(self, selected, to_select)
		return to_select:isKindOf("EquipCard") or to_select:isKindOf("Slash")
	end, 
	mingceCard = sgs.CreateSkillCard{
		name = "mingce" ,
		will_throw = false ,
		handling_method = sgs.Card_MethodNone ,
		on_effect = function(self, effect)
			room:setPlayerProperty(effect.from, "jingjie_exR", sgs.QVariant(""))
			local room = effect.to:getRoom()
			local targets = sgs.SPlayerList()
			if sgs.Slash_IsAvailable(effect.to) then
				for _, p in sgs.qlist(room:getOtherPlayers(effect.to)) do
					if effect.to:canSlash(p) then
						targets:append(p)
					end
				end
			end
			local target
			local choicelist = {"draw"}
			if (not targets:isEmpty()) and effect.from:isAlive() then
				target = room:askForPlayerChosen(effect.from, targets, self:objectName(), "@dummy-slash2:" .. effect.to:objectName())
				target:setFlags("LuaMingceTarget")
				table.insert(choicelist, "use")
			end
			effect.to:obtainCard(self)
			local choice = room:askForChoice(effect.to, self:objectName(), table.concat(choicelist, "+"))
			if target and target:hasFlag("LuaMingceTarget") then target:setFlags("-LuaMingceTarget") end
			if choice == "use" then
				if effect.to:canSlash(target, nil, false) then
					local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
					slash:setSkillName("_LuaMingce")
					room:useCard(sgs.CardUseStruct(slash, effect.to, target), false)
				end
			elseif choice == "draw" then
				effect.to:drawCards(1)
			end
		end
	},	
	mingceMin = 1,	

	xinzhanFilter = function(self, selected, to_select)
		return false
	end, 
	xinzhanCard = sgs.CreateSkillCard{
		name = "xinzhan" ,
		target_fixed = true ,
		on_use = function(self, room, source, targets)
			room:setPlayerProperty(source, "jingjie_exR", sgs.QVariant(""))
			local cards = room:getNCards(3)
			local left = cards
			local hearts = sgs.IntList()
			local non_hearts = sgs.IntList()
			for _, card_id in sgs.qlist(cards) do
				local card = sgs.Sanguosha:getCard(card_id)
				if card:getSuit() == sgs.Card_Heart then
					hearts:append(card_id)
				else
					non_hearts:append(card_id)
				end
			end
			local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			if not hearts:isEmpty() then
				repeat
					room:fillAG(left, source, non_hearts)
					local card_id = room:askForAG(source, hearts, true, "LuaXinzhan")
					if (card_id == -1) then
						room:clearAG(source)
						break
					end
					hearts:removeOne(card_id)
					left:removeOne(card_id)
					dummy:addSubcard(card_id)
					room:clearAG(source)
				until hearts:isEmpty()
				if dummy:subcardsLength() > 0 then
					room:doBroadcastNotify(56, tostring(room:getDrawPile():length() + dummy:subcardsLength()))
					source:obtainCard(dummy)
					for _, id in sgs.qlist(dummy:getSubcards()) do
						room:showCard(source, id)
					end
				end
			end
			if not left:isEmpty() then
				room:askForGuanxing(source, left, sgs.Room_GuanxingUpOnly)
			end
		end ,
	},
	xinzhanMin = 0,	
 
	shenpanFilter = function(self, selected, to_select)
		return false
	end, 
	shenpanCard = sgs.CreateSkillCard{
		name = "shenpan",
		target_fixed = true,
		on_use = function(self, room, source, targetS)
			room:setPlayerProperty(source, "jingjie_exR", sgs.QVariant(""))
			room:setPlayerFlag(source, "shenpaning")
			local slash = room:askForCard(source, "slash", "shenpanW", sgs.QVariant(), sgs.Card_MethodResponse, player)
			local jink = room:askForCard(source, "jink", "shenpanX", sgs.QVariant(), sgs.Card_MethodResponse, player)
			local analeptic = room:askForCard(source, "Analeptic", "shenpanY", sgs.QVariant(), sgs.Card_MethodResponse, player)
			if slash and jink and analeptic then room:setPlayerFlag(source, "shenpaningtao") end
			local peach = room:askForCard(source, "peach", "shenpanZ", sgs.QVariant(), sgs.Card_MethodResponse, player)
			room:setPlayerFlag(source, "-shenpaning")
			room:setPlayerFlag(source, "-shenpaningtao")
			if slash and jink and analeptic and peach then
				local targets = room:getAlivePlayers()
				local target = room:askForPlayerChosen(source, targets, "shenpan", "shenpan", true)
				targets:removeOne(target)
				while target and not targets:isEmpty() do
					room:damage(sgs.DamageStruct(self:objectName(), source, target))
					targets:removeOne(target)
					if targets:isEmpty() then break end
					target = room:askForPlayerChosen(source, targets, "shenpan", "shenpan", true)
				end
			else
				local shen = sgs.IntList()
				if slash then
					if slash:getSubcards() then
						for _, id in sgs.qlist(slash:getSubcards()) do
							if room:getCardPlace(id) == sgs.Player_DiscardPile then shen:append(id) end
						end
					else
						if slash:getId() >= 0 and room:getCardPlace(slash:getId()) == sgs.Player_DiscardPile then shen:append(id) end
					end
				end
				if jink then
					if jink:getSubcards() then
						for _, id in sgs.qlist(jink:getSubcards()) do
							if room:getCardPlace(id) == sgs.Player_DiscardPile then shen:append(id) end
						end
					else
						if jink:getId() >= 0 and room:getCardPlace(jink:getId()) == sgs.Player_DiscardPile then shen:append(id) end
					end
				end
				if analeptic then
					if analeptic:getSubcards() then
						for _, id in sgs.qlist(analeptic:getSubcards()) do
							if room:getCardPlace(id) == sgs.Player_DiscardPile then shen:append(id) end
						end
					else
						if analeptic:getId() >= 0 and room:getCardPlace(analeptic:getId()) == sgs.Player_DiscardPile then shen:append(id) end
					end
				end
				if peach then
					if peach:getSubcards() then
						for _, id in sgs.qlist(peach:getSubcards()) do
							if room:getCardPlace(id) == sgs.Player_DiscardPile then shen:append(id) end
						end
					else
						if peach:getId() >= 0 and room:getCardPlace(peach:getId()) == sgs.Player_DiscardPile then shen:append(id) end
					end
				end
				if shen:isEmpty() then return end
				local move = sgs.CardsMoveStruct()
				move.from = nil
				move.from_place = sgs.Player_DiscardPile
				move.to = source
				move.to_place = sgs.Player_PlaceHand
				move.card_ids = shen
				move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_RECYCLE, source:objectName())
				room:moveCardsAtomic(move, true)
			end
		end 
	},
	shenpanMin = 0,	

	duhuoFilter = function(self, selected, to_select)
		return false
	end, 
	duhuoCard = sgs.CreateSkillCard{
		name = "luaduhuo",
		filter = function(self, targets, to_select)
			if (#targets ~= 0) then return false end
			return true
		end ,
		on_use = function(self, room, source, targets)
			local parsee = source
			local room = parsee:getRoom()
			local suit = room:askForSuit(parsee, "luaduhuo")
			
			for _,target in ipairs(targets) do 
				local card_id = room:askForCardChosen(target, parsee, "h", "luaduhuo")
				if parsee:objectName() == target:objectName() then 
					room:setPlayerFlag(parsee, "gluaduhuo")
				end 
				local card = sgs.Sanguosha:getCard(card_id)
				room:getThread():delay()
				room:throwCard(card, parsee, target)
				room:showCard(target, card_id)
				if card:getSuit() == suit then
					room:addPlayerMark(parsee, "@luaduhuo")
					room:damage(sgs.DamageStruct("luaduhuo", parsee, target, 1, sgs.DamageStruct_Fire))			
				end
				room:setPlayerFlag(parsee, "-gluaduhuo")
			end
		end  
	},
	duhuoMin = 0,	

	linglanFilter = function(self, selected, to_select)
		return not sgs.Self:isJilei(to_select) and #selected == 0 and to_select:getSuit() == sgs.Card_Club
	end ,
	linglanCard = sgs.CreateSkillCard{
		name = "linglan",
		filter = function(self, targets, to_select)
			return not to_select:isNude() and to_select:isWounded() and #targets == 0
		end ,
		on_effect = function(self, effect)
			local room = effect.from:getRoom() 
			if not effect.to:isNude() then
				local to_throw = room:askForCardChosen(effect.from, effect.to, "he", self:objectName(), false)
				room:obtainCard(effect.from, sgs.Sanguosha:getCard(to_throw), false)
				room:recover(effect.to, sgs.RecoverStruct(effect.from, nil, 1))
			end
		end
	},
	linglanMin = 1,	

	chaoganFilter = function(self, selected, to_select)
		return false
	end, 
	chaoganCard = sgs.CreateSkillCard{
		name = "chaogan" ,
		will_throw = true ,
		target_fixed = true ,
		on_use = function(self, room, source, targets)
			room:setPlayerProperty(source, "jingjie_exR", sgs.QVariant(""))
			local x = source:getMark("luachaogan")
			if x <= 2 then
				source:drawCards(x)
			else
				local card_ids = room:getNCards(x)
				local obtained = sgs.IntList()
				room:fillAG(card_ids,source)
				local id1 = room:askForAG(source,card_ids,false,self:objectName())
				card_ids:removeOne(id1)
				obtained:append(id1)
				room:takeAG(source,id1,false)
				local id2 = room:askForAG(source,card_ids,false,self:objectName())
				card_ids:removeOne(id2)
				obtained:append(id2)
				room:clearAG(source)
				room:askForGuanxing(source,card_ids,sgs.Room_GuanxingDownOnly)
				local dummy = sgs.Sanguosha:cloneCard("jink",sgs.Card_NoSuit,0)
				for _,id in sgs.qlist(obtained) do
					dummy:addSubcard(id)
				end
				source:obtainCard(dummy,false)
			end 
		end
	},
	chaoganMin = 0,	

	tianhuFilter = function(self, selected, to_select)
		return false
	end, 
	tianhuCard = sgs.CreateSkillCard{
		name = "tianhu",
		filter = function(self, targets, to_select)
			return (#targets == 0)
		end,
		on_effect = function(self, effect) 
			effect.to:drawCards(1)
			local room = effect.to:getRoom()
			if effect.to:getHandcardNum() == 1 then 
				effect.from:drawCards(1)
			end
		end
	},
	tianhuMin = 0,	

	boliFilter = function(self, selected, to_select)
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
			slash:addSubcard(to_select:getEffectiveId())
			slash:setSkillName("luaboli")
			slash:deleteLater()
			return slash:isAvailable(sgs.Self) and #selected < 1
		end
		return #selected < 1
	end,
	boliCard = sgs.CreateSkillCard{
		name = "boli",
		target_fixed = false,
		will_throw = false,
		filter = function(self, targets, to_select)
			if to_select:objectName() == sgs.Self:objectName() then return false end
			local card = sgs.Sanguosha:getCard(self:getSubcards():at(0))
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
			slash:addSubcard(card:getEffectiveId())
			slash:setSkillName("luaboli")
			slash:deleteLater()
			if #targets ~= 0 then return false end
			return not sgs.Self:isProhibited(to_select, slash)
		end,
		on_validate = function(self,carduse)
			local source = carduse.from
			local room = source:getRoom()
			local card = sgs.Sanguosha:getCard(self:getSubcards():first())
			local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
			slash:setSkillName(self:objectName())
			slash:addSubcard(card:getEffectiveId())
			source:drawCards(1)
			room:setPlayerFlag(source,"luaboli_unlimited")
			room:setPlayerFlag(source,"luaboli_used")
	
			room:writeToConsole("luashishenyCE0")
			local xx = source:getTag("luashisheny")
			if not xx then 
				xx = ""
			else
				xx = xx:toString()
			end 
			xx = analysisString(xx, "boli")	
			room:writeToConsole("luashishenyCE0 " .. xx)
			source:setTag("luashisheny", sgs.QVariant(xx)) 
	
			return slash
		end,
	},
	boliMin = 1,	

	honghunFilter = function(self, cards, to_select)
		return (#cards == 0) and (not sgs.Self:isJilei(to_select))
	end ,
	honghunCard = sgs.CreateSkillCard{
		name = "honghun" ,
		target_fixed = false ,
		will_throw = true ,
		filter = function(self, targets, to_select)
			if #targets == 0 then
				local card = self:getSubcards():first()
				local duel = newDuel(sgs.Sanguosha:getCard(card))
				duel:addSubcard(card)
				if to_select:isProhibited(sgs.Self, duel, sgs.Self:getSiblings()) then return false end
				if sgs.Self:isCardLimited(duel, sgs.Card_MethodUse) then return false end
				return true
			end
			return false
		end ,
		about_to_use = function(self, room, cardUse)
			local mokou = cardUse.from
			local logg = sgs.LogMessage()
			logg.from = mokou
			logg.to = cardUse.to
			logg.type = "#UseCard"
			logg.card_str = self:toString()
			room:sendLog(logg)
			local data = sgs.QVariant()
			data:setValue(cardUse)
			local thread = room:getThread()
			thread:trigger(sgs.PreCardUsed, room, mokou, data)
			local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_THROW, mokou:objectName(), nil, "luahonghun", nil)
			room:moveCardTo(self, mokou, nil, sgs.Player_DiscardPile, reason, true)
			thread:trigger(sgs.CardUsed, room, mokou, data)
			thread:trigger(sgs.CardFinished, room, mokou, data)
		end ,
		on_use = function(self, room, source, targets)
			local card = self:getSubcards():first()
			local humans = sgs.SPlayerList()
			humans:append(source)
			humans:append(targets[1])
			local from = source
			local duel = newDuel(sgs.Sanguosha:getCard(card))
			duel:addSubcard(card)
			duel:toTrick():setCancelable(true)
			duel:setSkillName(self:objectName())
			room:setPlayerProperty(source, "jingjie_exR", sgs.QVariant(""))
			if (not from:isCardLimited(duel, sgs.Card_MethodUse)) and (not from:isProhibited(targets[1], duel)) then
				room:useCard(sgs.CardUseStruct(duel, from, humans))
			end
		end
	},	
	honghunMin = 1,	

	suiyueFilter = function(self, cards, to_select)
		return false
	end ,
	suiyueCard = sgs.CreateSkillCard{
		name = "luasuiyue",
		filter = function(self, targets, to_select)
			local targets_list = sgs.PlayerList()
			for _, target in ipairs(targets) do
				targets_list:append(target)
			end
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			slash:setSkillName("luasuiyue")
			slash:deleteLater()
			return (#targets < 1) and not sgs.Self:isProhibited(to_select, slash, to_select:getSiblings())
					and sgs.Self:canSlash(to_select, true)
		end,
		on_effect = function(self, effect) 
			for _, cd in sgs.qlist(self:getSubcards()) do
				effect.from:addMark("luasuiyueY")
			end 
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)	
			slash:setSkillName("luasuiyue")
			effect.from:getRoom():useCard(sgs.CardUseStruct(slash, effect.from, effect.to))
		end 
	},
	suiyueMin = 0,	

	hakureiFilter = function(self, selected, to_select)
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
			slash:addSubcard(to_select:getEffectiveId())
			slash:setSkillName("luahakurei2")
			slash:deleteLater()
			return slash:isAvailable(sgs.Self) and #selected < 1
		end
		return #selected < 1
	end, 
	hakureiCard = sgs.CreateSkillCard{
		name = "luahakurei",
		target_fixed = false,
		will_throw = false,
		filter = function(self, targets, to_select)
			if to_select:objectName() == sgs.Self:objectName() then return false end
			if #targets ~= 0 then return false end
			return true --sgs.Self:inMyAttackRange(to_select)
		end,
		on_validate = function(self,carduse)
			local source = carduse.from
			local target = carduse.to:first()
			local room = source:getRoom()
			local card = sgs.Sanguosha:getCard(self:getSubcards():first())
			local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
			slash:setSkillName(self:objectName())
			slash:addSubcard(card:getEffectiveId())
			--八云紫相关
			room:writeToConsole("luashishenyCE0")
			local xx = source:getTag("luashisheny")
			if not xx then 
				xx = ""
			else
				xx = xx:toString()
			end 
			xx = analysisString(xx, "hakurei")	
			room:writeToConsole("luashishenyCE0 " .. xx)
			source:setTag("luashisheny", sgs.QVariant(xx)) 

			if source:inMyAttackRange(target) and slash:isAvailable(source) then
				if source:canSlash(target, slash, true) then
					local _data = sgs.QVariant()
					_data:setValue(target)
					local choice = room:askForChoice(source, "luahakurei", "luahakurei1+luahakurei2", _data)
					if choice == "luahakurei1" then
						room:setPlayerFlag(source,"luahakurei_unlimited")
						room:setPlayerFlag(source,"luahakurei_used")
						return slash
					else
						source:drawCards(1)
						room:setPlayerFlag(source,"luahakurei_used")
						return slash
					end
				end
			elseif not slash:isAvailable(source) and source:inMyAttackRange(target) then
				if source:canSlash(target, slash, true) then
					room:setPlayerFlag(source,"luahakurei_unlimited")
					room:setPlayerFlag(source,"luahakurei_used")
					return slash
				end
			elseif not source:inMyAttackRange(target) and slash:isAvailable(source) then
				if source:canSlash(target, slash, false) then
					room:setPlayerFlag(source,"luahakurei_used")
					return slash
				end
			end
			room:setPlayerFlag(source,"luahakurei_used")
		end,
	},	
	hakureiMin = 1,	

	jieaoFilter = function(self, selected, to_select)
		return not to_select:isEquipped()
	end,
	jieaoCard = sgs.CreateSkillCard{
		name = "luajieao",
		target_fixed = false,
		will_throw = false,
		filter = function(self, targets, to_select)
			if #targets == 0 then
				if not to_select:isKongcheng() then
					return to_select:objectName() ~= sgs.Self:objectName()
				end
			end
			return false
		end,
		on_use = function(self, room, source, targets)
			local success = source:pindian(targets[1], "luajieao", self)
			if success then
				room:damage(sgs.DamageStruct(self:objectName(), source, targets[1], 1, sgs.DamageStruct_Normal))
			else
				local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_Diamond, 0)
				slash:setSkillName("luajieao")
				room:useCard(sgs.CardUseStruct(slash, targets[1], source))
			end
			room:setPlayerProperty(source, "jingjie_exR", sgs.QVariant(""))
		end
	},
	jieaoMin = 1,	

	yanguangFilter = function(self, selected, to_select)
		return false
	end, 
	yanguangCard = sgs.CreateSkillCard{
		name = "luayanguang",
		filter = function(self, targets, to_select)
			local targets_list = sgs.PlayerList()
			for _, target in ipairs(targets) do
				targets_list:append(target)
			end
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			slash:setSkillName("luayanguang")
			slash:deleteLater()
			return (#targets < 1) and slash:targetFilter(targets_list, to_select, sgs.Self) and sgs.Self:canSlash(to_select, false)
		end,
		on_effect = function(self, effect)
			local room = effect.from:getRoom() 
			local function canLoseHp()
				for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
					if hecatiaX and isFriendQ(room, effect.from, hecatiaX) and effect.from:objectName() ~= hecatiaX:objectName()
							and effect.from:getHp() == hecatiaX:getHp() then
						room:notifySkillInvoked(hecatiaX, "luayiti")
						return false
					end 
				end 
				for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
					if Erin and Erin:getKingdom() == effect.from:getKingdom() then
						room:notifySkillInvoked(Erin, "luajiance")
						return false
					end 
				end 
				return true
			end 
			if canLoseHp() then room:loseHp(effect.from) end 
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			slash:setSkillName("luayanguang")
			effect.from:getRoom():useCard(sgs.CardUseStruct(slash, effect.from, effect.to)) 
		end
	},
	yanguangMin = 0,	

	
	mimengFilter = function(self, selected, to_select) 
		return #selected < 1 and (to_select:isKindOf("Jink"))
	end,
	mimengCard = sgs.CreateSkillCard{
		name = "luamimeng", 
		will_throw = false,
		filter = function(self, targets, to_select)
			local slash = sgs.Sanguosha:cloneCard("ex_nihilo", sgs.Card_SuitToBeDecided, -1)
			slash:addSubcard(self:getSubcards():at(0))
			slash:deleteLater()
			if sgs.Self:isCardLimited(slash, sgs.Card_MethodUse) then return end 
			if to_select:isProhibited(to_select, slash, to_select:getSiblings()) then return false end
			return (#targets == 0)  
		end ,
		on_effect = function(self, effect)
			local room = effect.from:getRoom() 
			local card = self:getSubcards():at(0)	
			local slash = sgs.Sanguosha:cloneCard("ex_nihilo", sgs.Card_SuitToBeDecided, -1)
			slash:addSubcard(self:getSubcards():at(0))
			room:useCard(sgs.CardUseStruct(slash, effect.from, effect.to)) 
		end 
	},
	mimengMin = 1,	

	youquFilter = function(self, selected, to_select)
		return false
	end, 
	youquCard = sgs.CreateSkillCard{
		name = "luayouqu",
		filter = function(self, targets, to_select)
			return (#targets == 0)
		end,
		on_effect = function(self, effect)
			local room = effect.to:getRoom()
			local to_use_S = {}
			local to_use = {"fire_slash", "thunder_slash", "slash", "peach", "analeptic", "ofuda", "hui"}
			for _, str in ipairs(to_use) do
				local card = sgs.Sanguosha:cloneCard(str)
				local boolR = true
				local x = sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_Residue, effect.to, card) + 1
				if card:isKindOf("Slash") and x - effect.to:usedTimes("Slash") < 1
					and effect.to:getPhase() == sgs.Player_Play then boolR = false end
				if not effect.to:isCardLimited(card, sgs.Card_MethodUse) and boolR then table.insert(to_use_S, str) end
	
			end
			local str_X = room:askForChoice(effect.to, "luayouqu", table.concat(to_use_S, "+"))
			local card1 = room:askForCard(effect.to, ".|.|.|.", "@luayouqu", sgs.QVariant(str_X), sgs.Card_MethodNone)
			if not card1 then return false end
			if effect.to:isCardLimited(card1, sgs.Card_MethodUse) then return false end
			local card = sgs.Sanguosha:cloneCard(str_X)
			local targets_list = sgs.SPlayerList()
			for _, target in sgs.qlist(room:getAllPlayers()) do
				if ((card:isKindOf("Slash") and effect.to:canSlash(target, card, true))
					or ((card:targetFixed() and effect.to:objectName() == target:objectName()))
					or (card:isKindOf("Ofuda") and effect.to:inMyAttackRange(target, - sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_DistanceLimit, effect.to, card))))
					and not effect.to:isProhibited(target, card) and not (card:isKindOf("Peach") and not target:isWounded())
					and card:isAvailable(effect.to) then
					targets_list:append(target)
				end
			end
			card:setSkillName(self:objectName())
			card:addSubcard(card1)
			local X = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, effect.to, card)
			local to_target = sgs.SPlayerList()
			while not targets_list:isEmpty() do
				local Carddata2 = sgs.QVariant() -- ai用
				Carddata2:setValue(card)
				effect.to:setTag("luayouquTC", Carddata2)
				local target_X = room:askForPlayerChosen(effect.to, targets_list, "luayouqu", "luayouquX", true)
				effect.to:removeTag("luayouquTC")
				if not target_X then break end
				to_target:append(target_X)
				X = X - 1
				if X <= 0 then break end
			end
			if not to_target:isEmpty() then
				room:useCard(sgs.CardUseStruct(card, effect.to, to_target), true)
				if card:isKindOf("Slash") and effect.to:getPhase() == sgs.Player_Play then room:addPlayerHistory(effect.to, "Slash", 1) end
				if not effect.from:isAlive() then return false end
				if card1:objectName() == str_X or (card1:isKindOf("Slash") and card:isKindOf("Slash")) then
					local choice = room:askForChoice(effect.from, "luayouqu2", "luayouqu+draw")
					if choice == "luayouqu" then
						room:addPlayerHistory(effect.from, "#luayouqu", 0)
					else
						effect.from:drawCards(1)
					end
				end
			end 
		end
	},
	youquMin = 0,	

	xianzheFilter = function(self, selected, to_select)
		return false
	end, 	
	xianzheCard = sgs.CreateSkillCard{
		name = "Luaxianzhe",
		target_fixed = true,
		will_throw = false,
		on_use = function(self, room, source, targets)
			source:turnOver()
			source:drawCards(1)
			room:setPlayerProperty(source, "jingjie_exR", sgs.QVariant(""))
			local discard_ids = room:getDiscardPile()
			local trickcard = sgs.IntList()
			local basiccard = sgs.IntList()
			for _, id in sgs.qlist(discard_ids) do 
				local card = sgs.Sanguosha:getCard(id)
				if card:isKindOf("TrickCard") then 
					trickcard:append(id)
				elseif (card:isKindOf("BasicCard") and not card:isKindOf("Peach")) then 
					basiccard:append(id)
				end 
			end 
			if trickcard:length() > 0 and basiccard:length() > 0 then 
				room:fillAG(trickcard, source)
				local card_id = room:askForAG(source, trickcard, false, "LuaxianzheT")
				room:clearAG()
				room:obtainCard(source, card_id)
				local Carddata2 = sgs.QVariant() -- ai用
				Carddata2:setValue(sgs.Sanguosha:getCard(card_id))
				room:setTag("LuaxianzheTC", Carddata2)
				
				room:fillAG(basiccard, source)
				local card_id2 = room:askForAG(source, basiccard, false, "LuaxianzheB")			
				room:clearAG()		
				room:obtainCard(source, card_id2)	
				local yiji_cards = sgs.IntList()
				yiji_cards:append(card_id)
				yiji_cards:append(card_id2)
				room:askForYiji(source, yiji_cards, self:objectName(), true, false, true, 1, room:getAlivePlayers())
				room:removeTag("LuaxianzheTC")
				room:removeTag("LuaxianzheT")
			end 
		end
	},
	xianzheMin = 0,	

	yueshiFilter = function(self, selected, to_select)
		return not to_select:isEquipped()
	end,
	yueshiCard = sgs.CreateSkillCard{
		name = "luayueshi",
		target_fixed = false,
		will_throw = false,
		filter = function(self, targets, to_select)
			if #targets == 0 then
				if not to_select:isKongcheng() then
					return to_select:objectName() ~= sgs.Self:objectName()
				end
			end
			return false
		end,
		on_use = function(self, room, source, targets)
			room:setPlayerProperty(source, "jingjie_exR", sgs.QVariant(""))
			local success = source:pindian(targets[1], "luayueshi", self)
			if success then 
				local book = false
				local qargets = sgs.SPlayerList()
				for _, target in sgs.qlist(room:getAlivePlayers()) do
					if target:isWounded() then 
						qargets:append(target)
					end 
				end 				
				if not qargets:isEmpty() then 
					local target = room:askForPlayerChosen(source, qargets, "luayueshi1", "luayueshi1", true, true)		
					if target then 
						room:addPlayerMark(target, "yueshi1") 
						room:recover(target, sgs.RecoverStruct(source))
						book = true
					end 
				end 
				if not book then 
					qargets = sgs.SPlayerList()
					for _, target in sgs.qlist(room:getAlivePlayers()) do
						if not target:isNude() then 
							qargets:append(target)
						end 
					end 	
					if not qargets:isEmpty() then 
						local target = room:askForPlayerChosen(source, qargets, "luayueshi2", "luayueshi2", false, true)
						room:addPlayerMark(target, "yueshi2") 
						local id = room:askForCardChosen(source, target, "he", "luayueshi", false, sgs.Card_MethodDiscard)
						room:throwCard(id, target, source)
						--target:drawCards(1)
					end 
				end 
			end 
		end
	},
	yueshiMin = 1,	

	xingyunFilter = function(self, selected, to_select)
		return false
	end, 
	xingyunCard = sgs.CreateSkillCard{
		name = "luaxingyun",
		filter = function(self, targets, to_select)
			return (#targets < 1) and not to_select:isNude()
		end,
		on_effect = function(self, effect)
			local room = effect.from:getRoom() 
			local card_1 = room:askForCard(effect.to, ".|.|.|.!", "@luaxingyun", sgs.QVariant(), sgs.Card_MethodDiscard)
			local card_2 = room:askForCard(effect.from, ".|.|.|.!", "@luaxingyun2", sgs.QVariant(), sgs.Card_MethodDiscard)
			if not card_1 then return end 
			if not card_2 then return end 
			local ids = sgs.IntList()
			ids:append(card_1:getId())
			ids:append(card_2:getId())
			local move = sgs.CardsMoveStruct()
			move.card_ids = ids
			move.to = nil
			move.to_place = sgs.Player_DiscardPile
			move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_DISCARD, effect.from:objectName(), self:objectName(), "")
			room:moveCardsAtomic(move, true)		
			
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			local targets_list = sgs.SPlayerList()
			for _, target in sgs.qlist(room:getAllPlayers()) do
				if effect.to:canSlash(target, slash, false) then
					targets_list:append(target)
				end
			end
			room:setPlayerFlag(effect.to, "xingyun")
			local Carddata2 = sgs.QVariant() -- ai用
			Carddata2:setValue(card_1)
			room:setTag("luaxingyun", Carddata2)		
			local target_0 = room:askForPlayerChosen(effect.from, targets_list, self:objectName(), "luaxingyun2", false, true)
			room:removeTag("luaxingyun")
			if target_0 then
				local slash2 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				slash2:setSkillName("luaxingyun")
				room:useCard(sgs.CardUseStruct(slash2, effect.to, target_0))
			end		
			
			if effect.to:hasFlag("xingyun") then 
				local ids2 = sgs.IntList()
				ids2:append(card_1:getId())
				ids2:append(card_2:getId())
				local move2 = sgs.CardsMoveStruct()
				move2.card_ids = ids2
				move2.to = effect.from
				move2.to_place = sgs.Player_PlaceHand
				move2.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, effect.from:objectName(), self:objectName(), "")
				room:moveCardsAtomic(move2, true)
			else
				local ids2 = sgs.IntList()
				ids2:append(card_1:getId())
				ids2:append(card_2:getId())
				local move2 = sgs.CardsMoveStruct()
				move2.card_ids = ids2
				move2.to = effect.to
				move2.to_place = sgs.Player_PlaceHand
				move2.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, effect.from:objectName(), self:objectName(), "")
				room:moveCardsAtomic(move2, true)
			end 
		end
	},
	xingyunMin = 0,	

	jinglianFilter = function(self, selected, to_select)
		return to_select:isKindOf("EquipCard") and #selected < 1
	end,
	jinglianCard = sgs.CreateSkillCard{
		name = "luajinglian" ,
		will_throw = false,
		filter = function(self, targets, to_select)
			if #targets > 0 then return false end
			return true
		end ,
		feasible = function(self, targets)
			return #targets == 1 --and (targets[1]:getHp() < targets[1]:getRoom():getLord():getMark("@clock_time"))
		end ,
		on_effect = function(self, effect)
			local room = effect.to:getRoom() 
			effect.to:drawCards(1)
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
			local dummy_0 = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
			dummy_0:addSubcards(self:getSubcards())
			effect.to:obtainCard(dummy_0)
			if not effect.to:isCardLimited(slash, sgs.Card_MethodUse) and not effect.to:isCardLimited(sgs.Sanguosha:getCard(self:getSubcards():at(0)), sgs.Card_MethodUse) then
				if sgs.Sanguosha:getCard(self:getSubcards():at(0)):isAvailable(effect.to) then
					room:useCard(sgs.CardUseStruct(sgs.Sanguosha:getCard(self:getSubcards():at(0)), effect.to, effect.to))
					local cardp = room:askForCard(effect.to, ".", "@luajinglian", sgs.QVariant(), sgs.Card_MethodNone)
					if not cardp then return false end
					slash:addSubcard(cardp)
					local targets_list = sgs.SPlayerList()
					for  _, target in sgs.qlist(room:getAlivePlayers()) do
						if effect.to:canSlash(target, slash, false) then
							targets_list:append(target)
						end
					end
					if targets_list:length() > 0 then
						local Carddata2 = sgs.QVariant() -- ai用
						Carddata2:setValue(cardp)
						room:setTag("luajinglianTC", Carddata2)
						local target = room:askForPlayerChosen(effect.to, targets_list, "luajinglian", "@luajinglian2", false)
						room:removeTag("luajinglianTC")
						if target then
							slash:setSkillName(self:objectName())
							room:useCard(sgs.CardUseStruct(slash, effect.to, target))
						end
					end
				end
			end 
		end
	},
	jinglianMin = 1,	

	zonghuoFilter = function(self, selected, to_select)
		return false
	end, 
	zonghuoCard = sgs.CreateSkillCard{
		name = "Luazonghuo" ,
		filter = function(self, targets, to_select)
			if #targets > 0 then return false end
			return not to_select:isNude()
		end ,
		feasible = function(self, targets)
			return #targets == 1 --and (targets[1]:getHp() < targets[1]:getRoom():getLord():getMark("@clock_time"))
		end ,
		on_effect = function(self, effect)
			local room = effect.to:getRoom() 
			local id1 = room:askForCardChosen(effect.from, effect.to, "he", self:objectName(), false)
			local carde = sgs.Sanguosha:getCard(id1)
			local acard = sgs.Sanguosha:cloneCard("fire_slash", carde:getSuit(), carde:getNumber())
			acard:addSubcard(carde)
			--if effect.to:canSlash(effect.to, acard) then
				room:useCard(sgs.CardUseStruct(acard, effect.to, effect.to))
			--end
		end
	},
	zonghuoMin = 0,	

	jiangsuiFilter = function(self, selected, to_select)
		return false
	end, 
	jiangsuiCard = sgs.CreateSkillCard{
		name = "luajiangsui",
		filter = function(self, targets, to_select)
			return #targets == 0
		end,
		on_effect = function(self, effect)
			local targets = {effect.to, effect.from} 
			local room = effect.from:getRoom()
			local x = 0
			local y = 0
			local i = 1
			local card_ids = room:getNCards(3)
			room:fillAG(card_ids)
			local _data = sgs.QVariant()
			_data:setValue(effect.to)
			room:setTag("luajiangsuiTP", _data)
			while (not card_ids:isEmpty()) do
				local card_id = room:askForAG(targets[i], card_ids, false, self:objectName())
				card_ids:removeOne(card_id)
				local card = sgs.Sanguosha:getCard(card_id)
				if i == 1 then x = x + card:getNumber() end
				if i == 2 then y = y + card:getNumber() end
				if i == 1 then room:setTag("luajiangsui", sgs.QVariant(tostring(card:getNumber()))) end
				room:takeAG(targets[i], card_id)
				i = 3 - i
			end
			room:removeTag("luajiangsui")
			room:removeTag("luajiangsuiTP")
			if x > y then effect.from:getRoom():damage(sgs.DamageStruct("luajiangsui", effect.from, targets[1])) end
			if y > x then effect.from:getRoom():damage(sgs.DamageStruct("luajiangsui", effect.from, targets[2])) end
			room:clearAG()
		end
	},
	jiangsuiMin = 0,	

	wangshiFilter = function(self, selected, to_select)
		return false
	end, 
	wangshiCard = sgs.CreateSkillCard{
		name = "luawangshi" ,
		filter = function(self, targets, to_select)
			return (#targets == 0) and (to_select:objectName() ~= sgs.Self:objectName())
		end,
		on_effect = function(self, effect)
			local room = effect.to:getRoom() 
	
			local playerdata = sgs.QVariant() --ai用
			playerdata:setValue(effect.from)
			room:setTag("Jiantingsource", playerdata)
	
			local playerdataQ = sgs.QVariant() --ai用
			playerdataQ:setValue(effect.to)
			room:setTag("Jiantingsource3", playerdataQ)
			local playerlist = sgs.SPlayerList()
	
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:objectName() ~= effect.to:objectName() then playerlist:append(p) end
			end
			local target = room:askForPlayerChosen(effect.from, playerlist, "luawangshi", "luawangshiS", false, true)
	
			local playerdataW = sgs.QVariant() --ai用
			playerdataW:setValue(target)
			room:setTag("luawangshiz", playerdataW)
	
			if not target then return end
			local card = room:askForUseSlashTo(effect.to, target, "luawangshiz", false, false, false)
			if card then
				-- local reasonG = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_GIVE, effect.to:objectName(), effect.from:objectName(), self:objectName(), "")
				-- room:moveCardTo(card, effect.to, effect.from, sgs.Player_PlaceHand, reasonG, false)
				effect.to:drawCards(1)
			else
				local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				slash:setSkillName(self:objectName())
				local use = sgs.CardUseStruct()
				use.card = slash
				use.from = effect.from
				use.to:append(effect.to)
				room:useCard(use)
			end
			room:removeTag("Jiantingsource")
			room:removeTag("luawangshiz")
			room:removeTag("Jiantingsource3")
		end
	},
	wangshiMin = 0,	

	shenjunFilter = function(self, selected, to_select)
		if #selected >= 1 then return false end 
		if to_select:isKindOf("TrickCard") and not to_select:isNDTrick() then
			if sgs.Self:getHp() ~= 2 then return false end
		end
		return not to_select:isKindOf("EquipCard") and not to_select:isKindOf("Jink") and not to_select:isKindOf("Nullification")
				and not sgs.Self:isCardLimited(to_select, sgs.Card_MethodUse)
	end,
	shenjunCard = sgs.CreateSkillCard{
		name = "luashenjun",
		will_throw = true,
		filter = function(self, targets, to_select)
			local card = sgs.Sanguosha:getCard(self:getSubcards():at(0))
			return #targets < sgs.Self:getHp() - 1 and not sgs.Self:isProhibited(to_select, card, to_select:getSiblings())
				and not (card:isKindOf("Peach") and not to_select:isWounded())
		end,
		on_use = function(self, room, source, targets)
			local card0 = sgs.Sanguosha:getCard(self:getSubcards():at(0))
			room:setPlayerProperty(source, "jingjie_exR", sgs.QVariant(""))
			local dummy = sgs.Sanguosha:cloneCard(card0:objectName(), sgs.Card_NoSuit, -1)
			if dummy:isKindOf("AOE") or dummy:isKindOf("AmazingGrace") or dummy:isKindOf("GodSalvation") then
				room:setPlayerFlag(source, "qucaiAOE")
				for _, p in ipairs(targets) do
					room:setPlayerFlag(p, "qucaiAOEs")
				end
				room:useCard(sgs.CardUseStruct(dummy, source, sgs.SPlayerList()))
				room:setPlayerFlag(source, "-qucaiAOE")
				for _, p in ipairs(targets) do
					room:setPlayerFlag(p, "-qucaiAOEs")
				end
				return false
			end
			local splist = sgs.SPlayerList()
			for _, p in ipairs(targets) do
				splist:append(p)
			end
			room:useCard(sgs.CardUseStruct(dummy, source, splist))
		end
	},
	shenjunMin = 0,	

	ganluFilter = function(self, selected, to_select)
		return false
	end,
	ganluCard = sgs.CreateSkillCard{
		name = "luaganlu" ,
		filter = function(self, targets, to_select)
			if #targets == 0 then
				return true
			elseif #targets == 1 then
				local n1 = targets[1]:getEquips():length()
				local n2 = to_select:getEquips():length()
				return math.abs(n1 - n2) <= sgs.Self:getLostHp()
			else
				return false
			end
		end ,
		feasible = function(self, targets)
			return #targets == 2
		end,
		on_use = function(self, room, source, targets)
			swapEquip(targets[1], targets[2])
		end
	}, 
	ganluMin = 0,	

	xieliFilter = function(self, selected, to_select)
		return false
	end,
	xieliCard = sgs.CreateSkillCard{
		name = "xieli" ,
		target_fixed = true ,
		on_use = function(self, room, source, targets)
			if source:isWounded() and source:getHandcardNum() >= source:getMaxCards() then
				room:recover(source, sgs.RecoverStruct(source, nil, 1))
			elseif not source:isWounded() and source:getHandcardNum() < source:getMaxCards() then
				source:drawCards(source:getMaxCards() - source:getHandcardNum(), self:objectName())
			else
				local choice = room:askForChoice(source, "luaxieli", "draw+recover")
				if choice == "draw" then
					source:drawCards(source:getMaxCards() - source:getHandcardNum(), self:objectName())
				else
					room:recover(source, sgs.RecoverStruct(source, nil, 1))
				end
			end
			room:setPlayerMark(source, "luaxieli", 1)
		end
	},
	xieliMin = 0,	
}
local MasochismSkills = {
	simayi = function(self, player, damage)
		local from = damage.from
		local room = player:getRoom()
		for i = 0, damage.damage - 1, 1 do
			local data = sgs.QVariant()
			data:setValue(from)
			if from and not from:isNude() and room:askForSkillInvoke(player, self:objectName(), data) then
				local card_id = room:askForCardChosen(player, from, "he", self:objectName())
				local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, player:objectName())
				room:obtainCard(player, sgs.Sanguosha:getCard(card_id), reason, room:getCardPlace(card_id) ~= sgs.Player_PlaceHand)
			else
				break
			end
		end
	end, 
	xiahoudun = function(self, player, damage) 
		local from = damage.from
		local room = player:getRoom()
		for i = 0, damage.damage - 1, 1 do
			if room:askForSkillInvoke(player, self:objectName()) then
				local judge = sgs.JudgeStruct()
				judge.pattern = "."
				judge.play_animation = false
				judge.reason = self:objectName()
				judge.who = player
				room:judge(judge)
				if (not from) or from:isDead() then return end
				if judge.card:isRed() then
					room:damage(sgs.DamageStruct(self:objectName(), player, from))
				elseif judge.card:isBlack() then
					if player:canDiscard(from, "he") then
						local id = room:askForCardChosen(player, from, "he", self:objectName(), false, sgs.Card_MethodDiscard)
						room:throwCard(id, from, player)
					end
				end
			end
		end
	end,
	guojia = function(self, player, damage)
		local room = player:getRoom() 
		local x = damage.damage
		for i = 0, x - 1, 1 do
			if not player:isAlive() then return end
			if not room:askForSkillInvoke(player, self:objectName()) then return end
			local _guojia = sgs.SPlayerList()
			_guojia:append(player)
			local yiji_cards = room:getNCards(2, false)
			local move = sgs.CardsMoveStruct(yiji_cards, nil, player, sgs.Player_PlaceTable, sgs.Player_PlaceHand,
							sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PREVIEW, player:objectName(), self:objectName(), nil))
			local moves = sgs.CardsMoveList()
			moves:append(move)
			room:notifyMoveCards(true, moves, false, _guojia)
			room:notifyMoveCards(false, moves, false, _guojia)
			local origin_yiji = sgs.IntList()
			for _, id in sgs.qlist(yiji_cards) do
				origin_yiji:append(id)
			end
			while room:askForYiji(player, yiji_cards, self:objectName(), true, false, true, -1, room:getAlivePlayers()) do
				local move = sgs.CardsMoveStruct(sgs.IntList(), player, nil, sgs.Player_PlaceHand, sgs.Player_PlaceTable,
							sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PREVIEW, player:objectName(), self:objectName(), nil))
				for _, id in sgs.qlist(origin_yiji) do
					if room:getCardPlace(id) ~= sgs.Player_DrawPile then
						move.card_ids:append(id)
						yiji_cards:removeOne(id)
					end
				end
				origin_yiji = sgs.IntList()
				for _, id in sgs.qlist(yiji_cards) do
					origin_yiji:append(id)
				end
				local moves = sgs.CardsMoveList()
				moves:append(move)
				room:notifyMoveCards(true, moves, false, _guojia)
				room:notifyMoveCards(false, moves, false, _guojia)
				if not player:isAlive() then return end
			end
			if not yiji_cards:isEmpty() then
				local move = sgs.CardsMoveStruct(yiji_cards, player, nil, sgs.Player_PlaceHand, sgs.Player_PlaceTable,
							sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PREVIEW, player:objectName(), self:objectName(), nil))
				local moves = sgs.CardsMoveList()
				moves:append(move)
				room:notifyMoveCards(true, moves, false, _guojia)
				room:notifyMoveCards(false, moves, false, _guojia)
				local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				for _, id in sgs.qlist(yiji_cards) do
					dummy:addSubcard(id)
				end
				player:obtainCard(dummy, false)
			end
		end
	end,
	xunyu = function(self, player, damage) 
		local room = player:getRoom()
		for i = 0, damage.damage - 1, 1 do
			local to = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "jieming-invoke", true, true)
			if not to then break end
			local upper = math.min(5, to:getMaxHp())
			local x = upper - to:getHandcardNum()
			if x <= 0 then
			else
				to:drawCards(x)
			end
		end
	end,
	xunyou = function(self, player, damage)
		if player:askForSkillInvoke(self:objectName(), sgs.QVariant():setValue(damage)) then
			player:drawCards(1, self:objectName())
			local room = player:getRoom()
			if player:isKongcheng() then return false end
			room:showAllCards(player)
			local cards = player:getHandcards()
			local color = cards:first():isRed()
			local same_color = true
			for _, card in sgs.qlist(cards) do
				if card:isRed() ~= color then
					same_color = false
					break
				end
			end
			if same_color and damage.from and damage.from:canDiscard(damage.from, "h") then
				room:askForDiscard(damage.from, self:objectName(), 1, 1)
			end
		end
	end,
	shanghai = function(self, player, damage)
		local room = player:getRoom() 
		local x = damage.damage
		for i = 0, x - 1, 1 do
			if not player:isAlive() then return end
			if room:askForSkillInvoke(player, self:objectName()) then		
				local to_givelist = sgs.SPlayerList()
				for _,p in sgs.qlist(room:getAlivePlayers()) do
					if not p:hasSkill("feiying") or not p:hasSkill("mashu") then
						to_givelist:append(p)
					end
				end
				if not to_givelist:isEmpty() then
					local target = room:askForPlayerChosen(player, to_givelist, self:objectName(), "Luashanhaia", true, true)
					if not target then return false end
					if target:hasSkill("feiying") then 
						room:acquireSkill(target, "mashu", true) 
					elseif target:hasSkill("mashu") then 
						room:acquireSkill(target, "feiying", true) 
					else
						local choice = room:askForChoice(player, "Luashanhai","feiying+mashu")
						if choice == "feiying" then room:acquireSkill(target, "feiying", true) end 
						if choice == "mashu" then room:acquireSkill(target, "mashu", true); room:addPlayerMark(player, "mashuX") end
					end 					
				end				
			end 
		end 	
	end,
	sumireko = function(self, player, damage) 
		if damage.damage == 0 then return false end 
		local room = player:getRoom()
		if player:objectName() == damage.to:objectName() then
			for i = 1, damage.damage, 1 do
				local card_id = room:drawCard()
				player:addToPile("luanianli", card_id)
			end
		end
	end,
	kana = function(self, player, damage)
		local room = player:getRoom() 
		local x = damage.damage
		if damage.damage == 0 then return false end
		for i = 0, x - 1, 1 do
			local discard_ids = room:getDiscardPile()
			local trickcard = sgs.IntList()
			for _, id in sgs.qlist(discard_ids) do 
				local card = sgs.Sanguosha:getCard(id)
				if card:isKindOf("TrickCard") then 
					trickcard:append(id)
				end 
			end 
			if trickcard:length() > 0 and room:askForSkillInvoke(player, self:objectName()) then 
				room:fillAG(trickcard, player)
				local card_id = room:askForAG(player, trickcard, false, "Luahuaimeng")
				local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				dummy_0:addSubcard(card_id)
				room:clearAG()
				player:obtainCard(dummy_0)
			end 
		end 	
	end,
	sagume = function(self, player, damage) 
		if damage.damage == 0 then return false end
		if player:objectName() == damage.to:objectName() then
			for i = 1, damage.damage, 1 do
				if player:getMark("luanizhou") > 0 then
					local choice = room:askForChoice(player, "luashehuo", "luashehuo1+luashehuo2")
					if choice == "luashehuo1" then
						local num = room:askForChoice(player, "luanizhou", "1+2+3+4+5+6+7+8+9+10+11+12+13")
						room:setPlayerMark(player, "luanizhou", tonumber(num))
						for j = 1,50 do
							room:setPlayerMark(player, "@nizhou" .. j, 0)
						end
						num = tostring(tonumber(num) + 1)
						room:setPlayerMark(player, "@nizhou" .. num, 1)
					else
						room:setPlayerMark(player, "luanizhouX", 1)
					end
				end
			end
		end	
	end,
	keine = function(self, player, damage)
		local room = player:getRoom() 
		if damage.damage == 0 then return false end
		--local players = room:getOtherPlayers(player) 
		local target1 = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "jingjie_ex", false, false)
		local target2 = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "jingjie_ex", false, false)
		targets = {target1, target2}
		local card_ids = sgs.IntList()
		if room:getDiscardPile():isEmpty() then return false end
		for i = 0, 3 do
			card_ids:append(room:getDiscardPile():at(i))
			if room:getDiscardPile():length() < i + 1 then break end
		end
		local pq = 1
		while (not card_ids:isEmpty()) do
			room:fillAG(card_ids)
			local card_id = room:askForAG(targets[pq], card_ids, false, self:objectName())
			card_ids:removeOne(card_id)
			room:clearAG()
			local card = sgs.Sanguosha:getCard(card_id)

			local move = sgs.CardsMoveStruct()
			move.from = nil
			move.from_place = sgs.Player_DiscardPile
			move.to = targets[pq]
			move.to_place = sgs.Player_PlaceHand
			move.card_ids = sgs.IntList()
			move.card_ids:append(card_id)
			move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, "luaxinshi")
			room:moveCardsAtomic(move, true)
			local ab_card_ids = sgs.IntList()
			for _,id in sgs.qlist(card_ids) do
				local c = sgs.Sanguosha:getCard(id)
				if c:isBlack() ~= card:isBlack() then
					ab_card_ids:append(id)
				end
			end
			card_ids = ab_card_ids 
			pq = pq + 1
			room:broadcastInvoke("clearAG")
		end
	end,
	cirno = function(self, player, damage)
		local room = player:getRoom() 
		if damage.damage == 0 then return false end
		if damage.from and damage.from:isAlive() and not damage.from:isNude() then 
			local playerdata = sgs.QVariant() -- ai用
			playerdata:setValue(damage.from)
			room:setTag("luabingpuTarget", playerdata)
				if room:askForSkillInvoke(player, self:objectName())  then	
					local to_give = room:askForCard(damage.from, ".", "@luabingpua", sgs.QVariant(), self:objectName())
					if to_give then 
						room:throwCard(to_give, damage.from, damage.from)	
						local Carddata2 = sgs.QVariant() -- ai用
						Carddata2:setValue(to_give)
						room:setTag("luabingpuC2", Carddata2)						
					elseif not damage.from:isNude() then 
						local handcards = damage.from:getHandcards()
						local allcards = {}
						if damage.from:canDiscard(damage.from, "h") then
							for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
								table.insert(allcards, c)
							end
						end 
						if damage.from:canDiscard(damage.from, "e") then
							local equips = damage.from:getEquips()
							for _,c in sgs.qlist(equips) do
								table.insert(allcards, c)
							end						
						end 
						if #allcards > 0 then 
							local r = math.random(1, #allcards)
							local card = allcards[r]
							room:throwCard(card, damage.from, damage.from)		
							local Carddata2 = sgs.QVariant() -- ai用
							Carddata2:setValue(card)
							room:setTag("luabingpuC2", Carddata2)									
						end 
					end 

					local to_give2 = room:askForCard(player, ".", "@luabingpub", sgs.QVariant(), self:objectName())
					if to_give2 then 
						room:throwCard(to_give2, player, player)			
					end 	

					if to_give and to_give2 then 
						if (to_give:isRed() and to_give2:isBlack()) or (to_give:isBlack() and to_give2:isRed())
							and (player:getMark("@baka") == 0) then 
							player:gainMark("@baka")
						end 
					end 
				end
			room:removeTag("luabingpuC2")
			room:removeTag("luabingpuTarget")
		end 
	end,
	toyosatomimi = function(self, player, damage)
		local room = player:getRoom() 
		local x = damage.damage
		for i = 0, x - 1, 1 do
			local target = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "jingjie_exC", false, false)
			if target and target:isAlive() then 
				local room = player:getRoom() 
				if not room:askForDiscard(player, self:objectName(), 1, 1, true, true) then
					player:drawCards(1)
					room:damage(sgs.DamageStruct(self:objectName(), player, target))
					room:recover(target, sgs.RecoverStruct(player, nil, 1))
				else
					local choices = {"Player_Start", "Player_Judge", "Player_Draw", "Player_Play", "Player_Discard", "Player_Finish"}
					local playerdata = sgs.QVariant() -- ai用
					playerdata:setValue(target)
					room:setTag("LuaGuanwei", playerdata)
					local choice = room:askForChoice(player, "LuaGuanwei", table.concat(choices, "+")) 
					room:removeTag("LuaGuanwei")
					local roomx = target:getRoom()
					target:setTag("LuaGuanwei", sgs.QVariant(choice))

					roomx:setPlayerFlag(target, "LuaGuanwei")
				end
			end 
		end
	end,
	toyohime = function(self, player, damage)
		local room = player:getRoom() 
		local x = damage.damage
		for i = 0, x - 1, 1 do
			if not player:isAlive() then return end
			if room:askForSkillInvoke(player, self:objectName()) then		
				local to_givelist = sgs.SPlayerList()
				for _,p in sgs.qlist(room:getAlivePlayers()) do
					if not p:hasSkill("feiying") or not p:hasSkill("mashu") then
						to_givelist:append(p)
					end
				end
				if not to_givelist:isEmpty() then
					local target = room:askForPlayerChosen(player, to_givelist, self:objectName(), "Luashanhaia", true, true)
					if not target then return false end
					if target:hasSkill("feiying") then 
						room:acquireSkill(target, "mashu", true) 
					elseif target:hasSkill("mashu") then 
						room:acquireSkill(target, "feiying", true) 
					else
						local choice = room:askForChoice(player, "Luashanhai","feiying+mashu")
						if choice == "feiying" then room:acquireSkill(target, "feiying", true) end 
						if choice == "mashu" then room:acquireSkill(target, "mashu", true); room:addPlayerMark(player, "mashuX") end
					end 					
				end				
			end 
		end 
	end 
}

local function XXXXOOOO()
	local result = {}
	for rowKey, row in pairs(OncePerTurn) do
		table.insert(result, rowKey)
	end 
	return result
end 
local function GetAvailableGeneralsForjingjie(yukari, suit, room)  
	local all 
	if suit == sgs.Card_Spade then
		all = maixue
	else
		all = XXXXOOOO()
	end 
	local jingjie_exs = {}
	local Hs_String = yukari:getTag("jingjie_exs"):toString()
	if Hs_String and Hs_String ~= "" then
		jingjie_exs = Hs_String:split("+")
	end 
	for i = 1, #jingjie_exs do 
		local jingjie_exs2 = {}
		jingjie_exs2 = jingjie_exs[i]:split("|")
		if suit == sgs.Card_Spade then
			if #jingjie_exs2 > 0 and jingjie_exs2[2] == "Spade" then 
				table.removeOne(all, jingjie_exs2[1])
			end 
		else
			if #jingjie_exs2 > 0 and jingjie_exs2[2] == "Heart" then 
				table.removeOne(all, jingjie_exs2[1])
			end 			
		end
	end
	for _,player in sgs.qlist(room:getAlivePlayers()) do
		local name = player:getGeneralName()
		if sgs.Sanguosha:isGeneralHidden(name) then
			local fname = sgs.Sanguosha:findConvertFrom(name);
			if fname ~= "" then name = fname end
		end
		table.removeOne(all,name)

		if player:getGeneral2() == nil then continue end

		name = player:getGeneral2Name();
		if sgs.Sanguosha:isGeneralHidden(name) then
			local fname = sgs.Sanguosha:findConvertFrom(name);
			if fname ~= "" then name = fname end
		end
		table.removeOne(all,name)
	end 
	room:writeToConsole("jingjieX " .. all[1])
	return all 
end 
local function checkGeneral(yukari, suit, room)
	local jingjie_exs3 = {} 
	local jingjie_exs4 = {} 
	local jingjie_exs = {}
	local Hs_String = yukari:getTag("jingjie_exs"):toString()
	if Hs_String and Hs_String ~= "" then
		jingjie_exs = Hs_String:split("+")
	end 
	for i = 1, #jingjie_exs do 
		local jingjie_exs2 = {}
		jingjie_exs2 = jingjie_exs[i]:split("|")
		if suit == sgs.Card_Spade then
			if #jingjie_exs2 > 0 and jingjie_exs2[2] == "Spade" then 
				table.insert(jingjie_exs3, jingjie_exs2[1])
			end 
		else
			if #jingjie_exs2 > 0 and jingjie_exs2[2] == "Heart" then 
				table.insert(jingjie_exs4, jingjie_exs2[1])
			end 
		end 
	end
	if suit == sgs.Card_Spade then 
		return jingjie_exs3
	else
		return jingjie_exs4
	end 
end
local function remove1General(yukari, suit, room, generalname)
	local Hs_String = yukari:getTag("jingjie_exs"):toString()
	if Hs_String and Hs_String ~= "" then 
		local modifiedString = ""
		if suit == sgs.Card_Spade then
			local stringToRemove = generalname .. "|Spade"
			modifiedString = Hs_String:gsub("%+"..stringToRemove, "") 
			if modifiedString == Hs_String then
				modifiedString = Hs_String:gsub(stringToRemove, "")
			end
		else
			local stringToRemove = generalname .. "|Heart"
			modifiedString = Hs_String:gsub("%+"..stringToRemove, "") 
			if modifiedString == Hs_String then
				modifiedString = Hs_String:gsub(stringToRemove, "")
			end
		end 
		room:writeToConsole("remove1General" .. modifiedString)
		yukari:setTag("jingjie_exs", sgs.QVariant(modifiedString))
	end 
end 
local function ShowMaiXue(yukari, suit, room)
	local jingjie_exs3 = checkGeneral(yukari, sgs.Card_Spade, room)
	local jingjie_exs4 = checkGeneral(yukari, sgs.Card_Heart, room)
	if #jingjie_exs3 > 0 and suit == sgs.Card_Spade then 
		return room:askForGeneral(yukari, "hundun+" .. table.concat(jingjie_exs3,"+"))
	elseif #jingjie_exs4 > 0 and suit == sgs.Card_Heart then 
		return room:askForGeneral(yukari, "huben+" .. table.concat(jingjie_exs4,"+"))
	else 
		return ""
	end 
end 
jingjie_exCard = sgs.CreateSkillCard{
	name = "jingjie_ex", 
	target_fixed = true,
	on_use = function(self, room, yukari, targets)
		local suitX = sgs.Sanguosha:getCard(self:getSubcards():first()):getSuit()
		if suitX == sgs.Card_Spade or suitX == sgs.Card_Heart then
			for i = 1, self:getSubcards():length() do 
				local all = GetAvailableGeneralsForjingjie(yukari, suitX, room)
				local rand = math.random(1, #all)
				local testM = { "suika" }  --测试用
				for j = 1, #all do
					for k = 1, #all do
						if all[j] == testM[k] then 
							rand = j
							break;
						end 
					end 
				end 
				local Hs_String = yukari:getTag("jingjie_exs"):toString()
				if Hs_String and Hs_String ~= "" then 
					if suitX == sgs.Card_Spade then  
						Hs_String = Hs_String .. "+" .. all[rand] .. "|Spade"
					else
						Hs_String = Hs_String .. "+" .. all[rand] .. "|Heart"
					end 
				else
					if suitX == sgs.Card_Spade then  
						Hs_String = all[rand] .. "|Spade"
					else
						Hs_String = all[rand] .. "|Heart" 
					end 
				end
				room:writeToConsole("jingjieX " .. Hs_String)
				yukari:setTag("jingjie_exs", sgs.QVariant(Hs_String))
			end 
			ShowMaiXue(yukari, suitX, room) 
		end 
	end
}
jingjie_ex = sgs.CreateViewAsSkill{
	name = "jingjie_ex",	
	n = 99,	
	view_filter = function(self, selected, to_select)
		local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
		if pattern and pattern == "@@jingjie_ex" then
			if #selected == 0 then 
				return to_select:hasFlag("jingjie_exTC") 
			else
				return to_select:hasFlag("jingjie_exTC") and selected[1]:getSuit() == to_select:getSuit()
			end
		end 
		return OncePerTurnSkills[sgs.Self:property("jingjie_exR"):toString() .. "Filter"](self, selected, to_select)
	end,	
	view_as = function(self, cards)
		local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
		if pattern and pattern == "@@jingjie_ex" then
			if #cards < 1 then return nil end  
			local card = jingjie_exCard:clone()
			for _, acard in ipairs(cards) do
				card:addSubcard(acard)
			end			 
			return card			 
		end 
		if #cards >= OncePerTurnSkills[sgs.Self:property("jingjie_exR"):toString() .. "Min"] then
			local card = OncePerTurnSkills[sgs.Self:property("jingjie_exR"):toString() .. "Card"]:clone()
			if sgs.Self:property("jingjie_exR"):toString() == "suiyue" then 
				card:addSubcards(sgs.Self:getHandcards())
				card:setSkillName("luasuiyue")				
			else
				for _, acard in ipairs(cards) do
					card:addSubcard(acard)
				end			 
			end  
			return card		
		end 
	end,	
	enabled_at_play = function()
		local Az = sgs.Self:property("jingjie_exR"):toString()
		return Az and Az ~= ""
	end,
	enabled_at_response = function(self, player, pattern)
        return pattern == "@@jingjie_ex"
	end,

}  
--[[
jingjie_ex = sgs.CreateTriggerSkill{
	name = "jingjie_ex",  
	events = {sgs.CardsMoveOneTime}, 
	view_as_skill = jingjie_exVS,
	on_trigger = function(self, event, player, data, room)
		local move = data:toMoveOneTime() 
		if move.to and move.to:objectName() == player:objectName() and player:hasSkill("jingjie_ex") 
			and (move.to_place == sgs.Player_PlaceHand or move.to_place == sgs.Player_PlaceEquip) and not move.card_ids:isEmpty() then 
				
			local ids2 = sgs.IntList()
			for _,id in sgs.qlist(move.card_ids) do
				if sgs.Sanguosha:getCard(id):getSuit() == sgs.Card_Heart then
					ids2:append(id)
					room:setCardFlag(sgs.Sanguosha:getCard(id), "jingjie_exTC")
				end 
			end 
			if not ids2:isEmpty() then 
				if room:askForUseCard(player, "@@jingjie_ex", "@jingjie_ex" , -1, sgs.Card_MethodNone) then  
				end 
			end  	

			for _, card in sgs.list(player:getHandcards()) do
				room:setCardFlag(card, "-jingjie_exTC")
			end 
			for _,card in sgs.qlist(player:getEquips())do
				room:setCardFlag(card, "-jingjie_exTC")
			end 

			local ids = sgs.IntList()
			for _,id in sgs.qlist(move.card_ids) do
				if sgs.Sanguosha:getCard(id):getSuit() == sgs.Card_Spade then
					ids:append(id)
					room:setCardFlag(sgs.Sanguosha:getCard(id), "jingjie_exTC")
				end 
			end 
			if not ids:isEmpty() then 
				if room:askForUseCard(player, "@@jingjie_ex", "@jingjie_ex" , -1, sgs.Card_MethodNone) then  
				end 
			end  
		
		end
		return false
	end
}]]
jingjie_ex2 = sgs.CreateMasochismSkill{
	name = "#jingjie_ex2",
	on_damaged = function(self, yukari, damage)
		local room = yukari:getRoom()
		local GeneralK = ShowMaiXue(yukari, sgs.Card_Spade, room)
		if GeneralK == "" then return false end 
		if GeneralK == "hundun" then return false end 
		remove1General(yukari, sgs.Card_Spade, room, GeneralK)
		MasochismSkills[GeneralK](self, yukari, damage)

	end
}

jingjie_exMenuCard = sgs.CreateSkillCard{
	name = "jingjie_exMenuCard",
	target_fixed = true,
	on_use = function(self, room, yukari, targetS)
		local choice = room:askForChoice(yukari, "jingjie_ex", "jingjie_ex_1+jingjie_ex_2+jingjie_ex_4+jingjie_ex_3")
		if choice == "jingjie_ex_1" then
			ShowMaiXue(yukari, sgs.Card_Spade, room)
		elseif choice == "jingjie_ex_2" then 
			local GeneralK = ShowMaiXue(yukari, sgs.Card_Heart, room) 
			if GeneralK and GeneralK ~= "" and GeneralK ~= "huben" then 
				if OncePerTurn[GeneralK] and #OncePerTurn[GeneralK] > 0 then  
					remove1General(yukari, sgs.Card_Heart, room, GeneralK) 
					room:setPlayerProperty(yukari, "jingjie_exR", sgs.QVariant(OncePerTurn[GeneralK][1]))
				end 
			end
		elseif choice == "jingjie_ex_4" then 
			
			local ids2 = sgs.IntList()
			for _, cardA in sgs.list(yukari:getHandcards()) do
				if cardA:getSuit() == sgs.Card_Heart then
					ids2:append(cardA:getEffectiveId())
					room:setCardFlag(cardA, "jingjie_exTC")
				end 
			end 
			if not ids2:isEmpty() then 
				if room:askForUseCard(yukari, "@@jingjie_ex", "@jingjie_ex" , -1, sgs.Card_MethodNone) then  
				end 
			end  	
			room:askForUseCard(yukari, "@@jingjie_ex", "@jingjie_ex" , -1, sgs.Card_MethodNone)

			local ids = sgs.IntList()
			for _, cardA in sgs.list(yukari:getHandcards()) do
				if cardA:getSuit() == sgs.Card_Spade then
					ids:append(cardA:getEffectiveId())
					room:setCardFlag(cardA, "jingjie_exTC")
				end 
			end 
			if not ids:isEmpty() then 
				if room:askForUseCard(yukari, "@@jingjie_ex", "@jingjie_ex" , -1, sgs.Card_MethodNone) then  
				end 
			end  
		end 
	end
}

jingjie_exMenu = sgs.CreateZeroCardViewAsSkill{
	name = "jingjie_exMenu&",
	view_as = function()
		return jingjie_exMenuCard:clone()
	end,
	enabled_at_play = function(self, player)
		return true
	end
}

luachaogany2 = sgs.CreateTriggerSkill{
	name = "#luachaogany",
	events = {sgs.TargetConfirmed, sgs.EventPhaseChanging} ,
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			if not use.card then return false end
			if not use.from then return false end
			if (player:objectName() ~= use.from:objectName()) or (not use.to) or (use.card:getTypeId() == sgs.Card_TypeEquip) then return false end
			if use.to:length() ~= 1 then return false end
			if not player:hasSkill("jingjie_ex") then return false end
			player:addMark("luachaogan")
		else
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				if player:getMark("luachaogan") > 0 then
					room:setPlayerMark(player, "luachaogan", 0)
				end
			end
		end
	end
}

luashishenyVS = sgs.CreateViewAsSkill{
	name = "luashisheny",	
	n = 99,	
	view_filter = function(self, selected, to_select)
		return OncePerTurnSkills[sgs.Self:property("luashishenyR"):toString() .. "Filter"](self, selected, to_select)
	end,	
	view_as = function(self, cards) 
		if #cards >= OncePerTurnSkills[sgs.Self:property("luashishenyR"):toString() .. "Min"] then
			local card = OncePerTurnSkills[sgs.Self:property("luashishenyR"):toString() .. "Card"]:clone()
			if sgs.Self:property("luashishenyR"):toString() == "suiyue" then 
				card:addSubcards(sgs.Self:getHandcards())
				card:setSkillName("luasuiyue")				
			else
				for _, acard in ipairs(cards) do
					card:addSubcard(acard)
				end			 
			end 
			return card		
		end 
	end,	
	enabled_at_play = function()
		return false
	end,
	enabled_at_response = function(self, player, pattern)
        return pattern == "@@luashisheny"
	end, 
}		
  

luashisheny = sgs.CreateTriggerSkill{
	name = "luashisheny",
	global = true,
	view_as_skill = luashishenyVS,
	events = {sgs.EventPhaseChanging, sgs.CardUsed},
	on_trigger = function(self, event, player, data) 
		local room = player:getRoom()
		local change = data:toPhaseChange()
		if event == sgs.EventPhaseChanging then
			if change.to == sgs.Player_NotActive then
				for _, yukari in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do  
					local xx = player:getTag("luashisheny")
					if xx and xx:toString() and xx:toString() ~= "" then
						xx = xx:toString()
						room:writeToConsole("luashishenyCE0 " .. xx)
						local skillName, num = chooseProduct(room, player, xx)
						if skillName and skillName ~= "" and num > 0 then
							if room:askForSkillInvoke(yukari, self:objectName(), data) then
								local choice = room:askForChoice(player, "luashisheny", "luashisheny1+luashisheny2") --
								if choice == "luashisheny2" then 
									yukari:drawCards(num)
								else
									room:setPlayerProperty(player, "luashishenyR", sgs.QVariant(skillName)) --
									room:askForUseCard(player, "@@luashisheny", "@luashisheny" , -1, sgs.Card_MethodNone) --
								end 
							end 
						end  
						player:removeTag("luashisheny")
					end 
				end 
			end
		else
			local use = data:toCardUse()
			local card = use.card
			if event == sgs.CardUsed and use.from and player:objectName() == use.from:objectName() then 
				if card:isKindOf("LuaSkillCard") then 
					room:writeToConsole("luashishenyCE0" .. card:objectName())
					if card:objectName() ~= "jingjie_exMenuCard" then
						room:setPlayerProperty(use.from, "jingjie_exR", sgs.QVariant(""))
					end 
					for _, values in pairs(OncePerTurn) do
						for _, value in ipairs(values) do
							local cardName = card:objectName()
							if string.sub(card:objectName(), 1, 3) == "lua" then
								cardName = string.sub(card:objectName(), 4)
							end 
							if value == cardName then
								room:writeToConsole("luashishenyCE0")
								local xx = player:getTag("luashisheny")
								if not xx then 
									xx = ""
								else
									xx = xx:toString()
								end 
								xx = analysisString(xx, value)	
								room:writeToConsole("luashishenyCE0 " .. xx)
								player:setTag("luashisheny", sgs.QVariant(xx)) 
							end
						end
					end 
				end
			end 			
		end 
	end
}

luashenjunyukari2 = sgs.CreateTriggerSkill{
	name = "#luashenjunyukari2",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.PreCardUsed},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.PreCardUsed then
			local use = data:toCardUse()
			local card = use.card
			if use.from:hasFlag("qucaiAOE") then 
				room:writeToConsole("luashenjun test 3")
				use.to = sgs.SPlayerList()
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if p:hasFlag("qucaiAOEs") then use.to:append(p) end
				end
			end
			data:setValue(use)
			return false
		end
	end,
	can_trigger = function(self, target)
		return target
	end,
}

luayemuex = sgs.CreateTriggerSkill{
	name = "luayemuex$", 
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageForseen, sgs.PreHpRecover},
	can_trigger = function(self,target)
		return target ~= nil
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if event == sgs.DamageForseen then
			for _, shikieki in sgs.qlist(room:findPlayersBySkillName("luashuojiao")) do
				local damage = data:toDamage()
				if damage.to and damage.nature == sgs.DamageStruct_Thunder then
					return false 
				end
			end 
			if damage.to and damage.to:objectName() == player:objectName() and player:isLord() and player:hasLordSkill("luayemuex") then 
				damage.damage = damage.damage + 1
				room:notifySkillInvoked(player, "luayemuex")
				data:setValue(damage)
			end
			return false
		elseif event == sgs.PreHpRecover then
			local rec = data:toRecover()
			if player:isLord() and player:hasLordSkill("luayemuex") then
				rec.recover = rec.recover + 1
				data:setValue(rec)
			end
			return false
		end
	end
}
luayemuex2 = sgs.CreateTriggerSkill{
	name = "#luayemuex2" ,
	priority = 2,
	global = true,
	events = {sgs.TurnStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:hasLordSkill("luayemuex") and player:getMark("luayemuex") == 0 
			and room:getCurrent():objectName() == player:objectName() then
			room:setPlayerProperty(player, "maxhp", sgs.QVariant(player:getMaxHp() + 1))
			room:setPlayerProperty(player, "hp", sgs.QVariant(player:getHp() + 1)) 
			room:setPlayerMark(player, "luayemuex", 1)
		end
	end
}
yukariex:addSkill(luashisheny)
yukariex:addSkill(jingjie_ex)
yukariex:addSkill(jingjie_ex2)
yukariex:addSkill(jingjie_exMenu)
yukariex:addSkill(luachaogany2)
yukariex:addSkill(luashenjunyukari2)
yukariex:addSkill(luayemuex)
yukariex:addSkill(luayemuex2)
 
yukariexA:addSkill(luashisheny)
yukariexA:addSkill(jingjie_ex)
yukariexA:addSkill(jingjie_ex2)
yukariexA:addSkill(jingjie_exMenu)
yukariexA:addSkill(luachaogany2)
yukariexA:addSkill(luashenjunyukari2)
yukariexA:addSkill(luayemuex)
yukariexA:addSkill(luayemuex2)
 
yukariexB:addSkill(luashisheny)
yukariexB:addSkill(jingjie_ex)
yukariexB:addSkill(jingjie_ex2)
yukariexB:addSkill(jingjie_exMenu)
yukariexB:addSkill(luachaogany2)
yukariexB:addSkill(luashenjunyukari2)
yukariexB:addSkill(luayemuex)
yukariexB:addSkill(luayemuex2)
 
yukariexC:addSkill(luashisheny)
yukariexC:addSkill(jingjie_ex)
yukariexC:addSkill(jingjie_ex2)
yukariexC:addSkill(jingjie_exMenu)
yukariexC:addSkill(luachaogany2)
yukariexC:addSkill(luashenjunyukari2)
yukariexC:addSkill(luayemuex)
yukariexC:addSkill(luayemuex2)
 
yukariexD:addSkill(luashisheny)
yukariexD:addSkill(jingjie_ex)
yukariexD:addSkill(jingjie_ex2)
yukariexD:addSkill(jingjie_exMenu)
yukariexD:addSkill(luachaogany2)
yukariexD:addSkill(luashenjunyukari2)
yukariexD:addSkill(luayemuex)
yukariexD:addSkill(luayemuex2)



sgs.LoadTranslationTable {
	["pay30"] = "新八云紫", --注意这里每次要加逗号
	["yukariex"] = "八云紫",
	["yukariexA"] = "八云紫",
	["yukariexB"] = "八云紫",
	["yukariexC"] = "八云紫",
	["yukariexD"] = "八云紫",
	["#yukariex"] = "神隐的主犯",
	["illustrator:yukariex"] = "菊月",
	["illustrator:yukariexA"] = "ファルケン",
	["illustrator:yukariexB"] = "镜Area",
	["illustrator:yukariexC"] = "羽羽斩",
	["illustrator:yukariexD"] = "久苍穹", 
    ["designer:yukariex"] = "Paysage",
    ["jingjie_ex"] = "境界", 
    ["jingjie_ex_1"] = "查看卖血武将牌", 
    ["jingjie_ex_2"] = "查看阶段技武将牌", 
    ["jingjie_ex_3"] = "我手贱点错了", 
    ["jingjie_ex_4"] = "抽取技能", 
    ["@luashisheny"] = "你可以发动 「出牌阶段限一次」 的技能", 
    ["~luashisheny"] = "按那个技能本来的样子发动", 
    ["luashisheny"] = "式神", 
    [":luashisheny"] = "一名角色回合结束时，若其使用过“出牌阶段限一次”的技能，你可令其选择一项：①再使用一次此技能；②令你摸X张牌（X为此技能本回合的发动次数）。", 
    ["luashisheny1"] = "使用一次你 “出牌阶段限一次”的技能", 
    ["luashisheny2"] = "令八云紫摸X张牌", 
 
    ["#jingjie_ex2"] = "境界（卖血技能）", 
    ["jingjie_exMenu"] = "境界", 
    ["jingjie_exmenu"] = "境界", 
    ["jingjie_exMenuCard"] = "境界", 
    ["jingjie_exMenuCard"] = "境界", 
    ["jingjie_exC"] = "请选择此技能的目标",  
    [":jingjie_ex"] = "你可以弃置任意张：①红桃牌，抽取等量有“出牌阶段限一次”技能的武将牌；" ..
		"黑桃牌，抽取等量有“你受到1点伤害后”技能的武将牌。当你需要使用该技能时，你可移除那张武将牌来使用之。", 
	["@jingjie_ex"] = "你可以发动“境界”",
	["~jingjie_ex"] = "选择要弃置的牌→点击确定",
    ["luayemuex"] = "夜幕", 
    [":luayemuex"] = "主公技，锁定技，你受到的伤害量与回复量+1。你的初始体力上限+1。", 
	
}