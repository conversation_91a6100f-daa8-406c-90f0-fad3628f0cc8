

extension_pay_c = sgs.Package("pay3")

sp_aya = sgs.General(extension_pay_c,"sp_aya","qun",3,false,true,true)
kana = sgs.General(extension_pay_c,"kana","luaxi",3,false,false,false)

yumemi = sgs.General(extension_pay_c,"yumemi","luaxi",3,false,false,false)
yumemiA = sgs.General(extension_pay_c,"yumemiA","luaxi",3,false,true,true)
yumemiB = sgs.General(extension_pay_c,"yumemiB","luaxi",3,false,true,true)
yumemiC = sgs.General(extension_pay_c,"yumemiC","luaxi",3,false,true,true)
sarielelis = sgs.General(extension_pay_c,"sarielelis$","luaxi",6,false,false,false)
lolice = sgs.General(extension_pay_c,"lolice","luaxi",3,false, false,false)
satsuki = sgs.General(extension_pay_c,"satsuki","luacai",3,false, false,false)
chiyuri = sgs.General(extension_pay_c,"chiyuri","luaxi",3,false, false,false)
jz_reimu = sgs.General(extension_pay_c,"jz_reimu","luaxi",5,false, false,false)
mugetsu = sgs.General(extension_pay_c,"mugetsu","luaxi",6,false, false,false)
yukimai = sgs.General(extension_pay_c,"yukimai","luaxi",3,false, false,false)
sagume = sgs.General(extension_pay_c,"sagume","luayue",3,false,true,false)
sagumeA = sgs.General(extension_pay_c,"sagumeA","luayue",3,false,true,true)
sagumeB = sgs.General(extension_pay_c,"sagumeB","luayue",3,false,true,true)
sagumeC = sgs.General(extension_pay_c,"sagumeC","luayue",3,false,true,true)
ellen = sgs.General(extension_pay_c,"ellen","luaxi",4,false,false,false)
elliy = sgs.General(extension_pay_c,"elliy","luaxi",4,false,false,false)
yumeko = sgs.General(extension_pay_c,"yumeko","god",4,false,false,false)


Luahuaimeng = sgs.CreateTriggerSkill{
	name = "Luahuaimeng",
	frequency = sgs.Skill_Frequent,
	events = {sgs.Damaged},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		local x = damage.damage
		if damage.damage == 0 then return false end
		for i = 0, x - 1, 1 do
			local discard_ids = room:getDiscardPile()
			local trickcard = sgs.IntList()
			for _, id in sgs.qlist(discard_ids) do 
				local card = sgs.Sanguosha:getCard(id)
				if card:isKindOf("TrickCard") then 
					trickcard:append(id)
				end 
			end 
			if trickcard:length() > 0 and room:askForSkillInvoke(player, self:objectName(),data) then 
				room:fillAG(trickcard, player)
				local card_id = room:askForAG(player, trickcard, false, "Luahuaimeng")
				local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				dummy_0:addSubcard(card_id)
				room:clearAG()
				player:obtainCard(dummy_0)
			end 
		end 
	end,
}

LualubiaoCard = sgs.CreateSkillCard{
	name = "Lualubiao" ,
	will_throw = false,
	target_fixed = false,
	filter = function(self, targets, to_select)
		if #targets == 0 then
			return to_select:objectName() ~= sgs.Self:objectName()
		end
		return false
	end,
	on_effect = function(self, effect)
		local kana = effect.from
		local room = kana:getRoom()
		local room2 = effect.to:getRoom()
		local card = self:getSubcards():first()
		
		local cd = sgs.Sanguosha:getCard(card)
		local pattern
		if cd:isKindOf("BasicCard") then
			pattern = "BasicCard"
		elseif cd:isKindOf("TrickCard") then
			pattern = "TrickCard"
		elseif cd:isKindOf("EquipCard") then
			pattern = "EquipCard"
		end
		if cd:isKindOf("Slash") then room2:setTag("LualubiaoAI", sgs.QVariant(1)) else room2:setTag("LualubiaoAI", sgs.QVariant(0)) end 
		local data_for_ai = sgs.QVariant(pattern)
		local type_i = pattern.."|.|.|hand"
		pattern = string.format("%s|.|.|hand", pattern)		
		local to_give = nil
		if effect.to and effect.to:isAlive() then
			if cd:isKindOf("EquipCard") then
				if not effect.to:getEquips():isEmpty() then
					local dummy_card = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
					for _, cd in sgs.qlist(effect.to:getEquips()) do
						dummy_card:addSubcard(cd)
					end
					local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, effect.from:objectName(), effect.to:objectName(), "Lualubiao", nil)
					room:moveCardTo(dummy_card, effect.to, effect.to, sgs.Player_PlaceHand, reason, false)
				end
			end 
			if not effect.to:isKongcheng() and kana and kana:isAlive()  then
				to_give = room:askForCard(effect.to, pattern, "@lualubiao", data_for_ai, sgs.NonTrigger, kana)
			end		
			if to_give then						
				local reasonG = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_GIVE, effect.to:objectName(), kana:objectName(), self:objectName(), "")
				room:moveCardTo(to_give, effect.to, kana, sgs.Player_PlaceHand, reasonG, false)
			else
				room:setPlayerCardLimitation(effect.to, "use,response,discard", type_i, false)
				local p = effect.to
				local playerdata = sgs.QVariant()
				playerdata:setValue(p)
				
				room2:setTag("LualubiaoTarget", playerdata)
				room2:setTag("LualubiaoSP", sgs.QVariant(type_i))
			end 
			room2:removeTag("LualubiaoAI")
		end 
	end, 
}
Lualubiao = sgs.CreateViewAsSkill{
	name = "Lualubiao" ,
	n = 1,
	view_filter = function(self, selected, to_select)
		return not to_select:isEquipped()
	end,
	view_as = function(self, cards)
		
		if #cards ~= 0 then
			local Lualubiao = LualubiaoCard:clone()
			for _, c in ipairs(cards) do
				Lualubiao:addSubcard(c)
			end
			return Lualubiao
		end
		
	end ,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#Lualubiao")
	end
}
LualubiaoClear = sgs.CreateTriggerSkill{
	name = "#LualubiaoClear-clear" ,
	frequency = sgs.Skill_Compulsory,
	global = true, --究极重要！全场技能必备，花了5个小时买一场教训！
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		if event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				local room = player:getRoom()
				local target2 = room:getTag("LualubiaoTarget")
				if target2 and target2:toPlayer() and target2:toPlayer():isAlive() and target2:toPlayer():objectName() == player:objectName() then 
					local str1 = room:getTag("LualubiaoSP"):toString()
					room:removePlayerCardLimitation(player,  "use,response,discard", str1.."$0")
					room:removeTag("LualubiaoSP")
					room:removeTag("LualubiaoTarget")
				end 
				return false
			end 
		end
		return false
	end ,
	can_trigger = function(self, target)
		local room = target:getRoom() 
		local target2 = room:getTag("LualubiaoTarget")
		return target2 and target2:toPlayer() and target2:toPlayer():isAlive() and target2:toPlayer():objectName() == target:objectName()
	end
}

kana:addSkill(Luahuaimeng)
kana:addSkill(Lualubiao)
kana:addSkill(LualubiaoClear)




shiziCard = sgs.CreateSkillCard{
	name = "luashizi",
	will_throw = false,
	--handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		local card = sgs.Self:getTag("luashizi")
		local response = false
		if card then 
			card = card:toCard()
		else		
			response = true			
			card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, 10)
			card = sgs.self
		end 
		if not card then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		if self:getUserString() and self:getUserString() ~= "" then card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, 10);response = true end
		card:addSubcards(self:getSubcards())
		card:setSkillName(self:objectName())
		if card and card:targetFixed() and not response then
			return false
		end
		--[[for i = 0, 1000 do
			local cardS = sgs.Sanguosha:getEngineCard(i)
			if cardS and cardS:objectName() == card:objectName() then
				if sgs.Self:getMark("Yumemi" .. i ) > 0 then return false end
			end
		end]]--
		if response then return true end 
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end

		return card and card:targetFilter(qtargets, to_select, sgs.Self) and not sgs.Self:isProhibited(to_select, card, qtargets)
	end,
	feasible = function(self, targets)
		local card = sgs.Self:getTag("luashizi")
		local response = false
		if card then 
			card = card:toCard()
		else
			response = true
			card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, 10)
		end 
		if not card then card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, 10);response = true end
		if self:getUserString() and self:getUserString() ~= "" then card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, 10);response = true end
		card:addSubcards(self:getSubcards())
		card:setSkillName(self:objectName())
		card:setNumber(10)
		--if card:isKindOf("BasicCard") then card = kcard:clone() end 
		if response then return true end 
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		--[[for i = 0, 1000 do
			local cardS = sgs.Sanguosha:getEngineCard(i)
			if cardS and cardS:objectName() == card:objectName() then
				if sgs.Self:getMark("Yumemi" .. i ) > 0 then return false end
			end
		end]]--
		return card and card:targetsFeasible(qtargets, sgs.Self)
	end,	
	on_validate = function(self, card_use)
		local use_card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, 10)
		--[[for i = 0, 1000 do
			local cardS = sgs.Sanguosha:getEngineCard(i)
			if cardS and cardS:objectName() == use_card:objectName() then
				if card_use.from:getMark("Yumemi" .. i ) > 0 then return end
			end
		end]]--
		use_card:addSubcards(self:getSubcards())
		use_card:setSkillName(self:objectName())
		local available = true
		for _,p in sgs.qlist(card_use.to) do
			if card_use.from:isProhibited(p,use_card)then
				available = false
				break
			end
		end
		available = available and use_card:isAvailable(card_use.from)
		if not available then return nil end
		use_card:setNumber(10)
		--[[for i = 0, 1000 do
			local card = sgs.Sanguosha:getEngineCard(i)
			if card and card:objectName() == use_card:objectName() then
				if card_use.from:getMark("Yumemi" .. i) == 0 then
					card_use.from:getRoom():setPlayerMark(card_use.from, "Yumemi" .. i, 1)
				end
			end
		end]]--
		card_use.from:getRoom():setPlayerFlag(card_use.from, "luashizix")
		return use_card		
	end,
	on_validate_in_response = function(self, user)
		local room = user:getRoom()
		local aocaistring = self:getUserString()
		local use_card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, 10)
		--[[for i = 0, 1000 do
			local cardS = sgs.Sanguosha:getEngineCard(i)
			if cardS and cardS:objectName() == use_card:objectName() then
				if user:getMark("Yumemi" .. i ) > 0 then return end
			end
		end]]--
		if string.find(aocaistring, "+")  then
			local uses = {}
			for _, name in pairs(aocaistring:split("+"))do
				table.insert(uses, name)
			end
			local name = room:askForChoice(user, "luashizi", table.concat(uses, "+"))
			use_card = sgs.Sanguosha:cloneCard(name, sgs.Card_NoSuit, 10)
		end
		use_card:addSubcards(self:getSubcards())
		use_card:setSkillName("luashizi")
		use_card:setNumber(10)
		--[[for i = 0, 1000 do
			local card = sgs.Sanguosha:getEngineCard(i)
			if card and card:objectName() == use_card:objectName() then
				if user:getMark("Yumemi" .. i) == 0 then
					user:getRoom():setPlayerMark(user, "Yumemi" .. i, 1)
				end
			end
		end]]--
		room:setPlayerFlag(user, "luashizix")
		return use_card	
	end
}
luashizi = sgs.CreateViewAsSkill{
	name = "luashizi",
	n = 999,
	view_filter = function(self, selected, to_select)
		local n = to_select:getNumber()
		if n > 10 then return false end 
		for _, card in ipairs(selected) do
			n = n + card:getNumber()
			if n > 10 then return false end 
		end 
		return true
	end,
	view_as = function(self, cards)
		if #cards < 1 then return nil end 
		local n = 0
		for _, card in ipairs(cards) do
			n = n + card:getNumber()
		end 
		if n < 10 then return nil end 
		if (sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY) then			
			local c = sgs.Self:getTag("luashizi"):toCard()
			if c then
				local card = shiziCard:clone()
				for _, acard in ipairs(cards) do
					card:addSubcard(acard)
				end			
				card:setUserString(c:objectName())	
				return card			
			end
		else
			local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
			if pattern == "slash" then 
				pattern = "slash+thunder_slash+fire_slash"
			end
			local acard = shiziCard:clone()
			for _, bcard in ipairs(cards) do
				acard:addSubcard(bcard)
			end			
			if pattern == "peach+analeptic" then
				if sgs.Self:hasFlag("Global_PreventPeach") then
					pattern = "analeptic"
				else
					pattern = "peach"
				end
			end
			acard:setUserString(pattern)
			return acard		
		end 
		return nil
	end,
	enabled_at_play = function(self, player)
		return not player:isNude() and not player:hasFlag("luashizix")
	end,
	enabled_at_response = function(self, player, pattern)
		if player:isNude() or string.sub(pattern, 1, 1) == "." or string.sub(pattern, 1, 1) == "@" then
			return false
		end
		return not player:hasFlag("luashizix")
	end,	
	enabled_at_nullification = function(self, player)		
		return not player:isNude() and not player:hasFlag("luashizix")
	end
}
luashizi:setGuhuoDialog("lr")
--[[
luashizi2 = sgs.CreateTriggerSkill{
	name = "#luashizi",
	global = true,
	events = {sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local change = data:toPhaseChange()
		if change.to ~= sgs.Player_Start then return false end
		if event == sgs.EventPhaseChanging then
			if player:objectName() == room:getLord():objectName() then
				for _, yumemi in sgs.qlist(room:findPlayersBySkillName("luashizi")) do
					for i = 0, 1000 do
						room:setPlayerMark(yumemi, "Yumemi" .. i, 0)
					end
				end
			end
		end
		return false
	end
}]]--
luashizi2 = sgs.CreateTriggerSkill{
	name = "#luashizi2" ,
	global = true,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Start then
			for _, yumemi in sgs.qlist(room:findPlayersBySkillName("luashizi")) do
				if yumemi then
					room:setPlayerFlag(yumemi, "-luashizix")
				end
			end 
		end
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}


luachaofan2 = sgs.CreateTargetModSkill{
	name = "#luachaofan",
	pattern = ".", 
	distance_limit_func = function(self, player, card) 
		if player:hasSkill("luachaofan") and player:getMark("@forbidChaofan") == 0 and player:getMark("luachaofanA") > 0
			and card:getNumber() > player:getMark("chaofan") then
			return 1000
		else
			return 0
		end
	end
}

luachaofan = sgs.CreateTriggerSkill
{
	name = "luachaofan",
	events = {sgs.CardUsed, sgs.CardResponded, sgs.EventPhaseStart},
	frequency = sgs.Skill_Frequent,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart then
			if player:getPhase() == sgs.Player_Start then
				room:setPlayerMark(player, "luachaofanA", 0)
				room:setPlayerMark(player, "@forbidChaofan", 0)
				if room:askForDiscard(player, "luachaofan", 1, 1, true, true) then
					room:setPlayerMark(player, "luachaofanA", 1)
				end
			end
			return false
		end
		local discard_ids = room:getDiscardPile()
		if discard_ids:isEmpty() then return false end 
		if player:getMark("luachaofanA") == 0 then return false end
		local x = sgs.Sanguosha:getCard(discard_ids:at(0)):getNumber()
		if x > 0 and x <= 13 then
			if player:getMark("@forbidChaofan") == 0 then
				if event == sgs.CardUsed then
					local card = data:toCardUse().card
					if card:isKindOf("SkillCard") then return false end
					if card:isKindOf("EquipCard") then return false end  
					if card:getNumber() > x then  
						if room:askForSkillInvoke(player, self:objectName()) then
							player:drawCards(1)  
						end 
					else
						room:setPlayerMark(player, "@forbidChaofan", 1)
					end
				elseif event == sgs.CardResponded then
					local card = data:toCardResponse().m_card
					if card:isKindOf("SkillCard") then return false end
					if card:isKindOf("EquipCard") then return false end  
					if card:getNumber() > x then
						if room:askForSkillInvoke(player, self:objectName()) then
							player:drawCards(1)  
						end 
					else
						room:setPlayerMark(player, "@forbidChaofan", 1)
					end
				end
			end 
		end
	end,
}
luachaofan3 = sgs.CreateTriggerSkill{
	name = "#luachaofan2",
	events = {sgs.BeforeCardsMove, sgs.CardsMoveOneTime},
	priority = 10,
	global = true,
	on_trigger = function(self, event, player, data, room)
		local discard_ids = room:getDiscardPile()
		if discard_ids:isEmpty() then return false end  
		local x = sgs.Sanguosha:getCard(discard_ids:at(0)):getNumber()
		for _, p in sgs.qlist(room:findPlayersBySkillName("luachaofan2")) do
			room:setPlayerMark(p, "chaofan", x)
		end 
		return false
	end,
	can_trigger = function(self, target)
		return target
	end
}
yumemi:addSkill(luachaofan)
yumemi:addSkill(luachaofan2) 
yumemi:addSkill(luachaofan3) 
yumemi:addSkill(luashizi)
yumemi:addSkill(luashizi2)

yumemiA:addSkill(luachaofan)
yumemiA:addSkill(luachaofan2) 
yumemiA:addSkill(luashizi)

yumemiB:addSkill(luachaofan)
yumemiB:addSkill(luachaofan2) 
yumemiB:addSkill(luashizi)

yumemiC:addSkill(luachaofan)
yumemiC:addSkill(luachaofan2) 
yumemiC:addSkill(luashizi)

--[[yumemiD:addSkill(luachaofan)
yumemiD:addSkill(luachaofan2)
yumemiD:addSkill(luachaofan3)
yumemiD:addSkill(luashizi)]]--


local function isFriendQ(room, target, player)
	if player:getRole() == "rebel" then
		return target:getRole() == "rebel"
	end
	if player:getRole() == "loyalist" or player:getRole() == "lord" then
		return target:getRole() == "loyalist" or target:getRole() == "lord"
	end
end
luanizhour = sgs.CreateTriggerSkill{
	name = "#luanizhou" ,
	events = {sgs.CardsMoveOneTime, sgs.EventPhaseChanging},
	global = true, --究极重要！全场技能必备，花了5个小时买一场教训！
	frequency = sgs.Skill_NotFrequent,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardsMoveOneTime then
			local sagume = room:findPlayerBySkillName("luanizhou")
			if sagume and sagume:isAlive() then
				local move = data:toMoveOneTime()

				if not (move.from_places:contains(sgs.Player_PlaceHand) or move.from_places:contains(sgs.Player_PlaceEquip)) then return false end
				if not move.from then return false end
				if move.from:objectName() ~= player:objectName() then return false end
				if move.to and move.from:objectName() == move.to:objectName() then return false end
				local from
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if move.from:objectName() == p:objectName() then
						from = p
						break
					end
				end
				local invoke = false
				for _,id in sgs.qlist(move.card_ids) do
					if id >= 0 then
						invoke = true
					end
				end
				if not invoke then return false end
				local sp = room:getCurrent():getMark("@luatianshii")
				room:setPlayerMark(room:getCurrent(), "@luatianshii", sp + move.card_ids:length())
				local damageC = 1
				if sagume:getMark("luanizhouX") > 0 then damageC = 2 end
				if sp <= sagume:getMark("luanizhou") and sp + move.card_ids:length() > sagume:getMark("luanizhou")
						and from:getPhase() == sgs.Player_NotActive and sagume:getMark("luanizhou") > 0 then
					room:notifySkillInvoked(sagume, "luanizhou")
					room:getThread():delay(1000)
					room:doAnimate(1, sagume:objectName(), room:getCurrent():objectName())
					room:getThread():delay(1000)
					room:doAnimate(1, room:getCurrent():objectName(), from:objectName())

					if isFriendQ(room, from, sagume) then
						if sagume:getMark("luanizhouX") > 0 then
							from:drawCards(sp*2)
						else
							from:drawCards(sp)
						end
					else

						if (from:getCardCount(true) < sp) or (damageC > 1 and from:getCardCount(true) < sp*2) then
							room:damage(sgs.DamageStruct("luanizhou", nil, from, damageC, sgs.DamageStruct_Normal))
						else
							local choice = room:askForChoice(from, "luanizhou2", "damage+discard")
							if choice == "damage" then
								room:damage(sgs.DamageStruct("luanizhou", nil, from, damageC, sgs.DamageStruct_Normal))
							else
								if sagume:getMark("luanizhouX") > 0 then
									room:askForDiscard(from, "luanizhou", sp*2, sp*2, false, true)
								else
									room:askForDiscard(from, "luanizhou", sp, sp, false, true)
								end
							end
						end
					end
				end
			end
			return false
		elseif event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				room:setPlayerMark(room:getCurrent(), "@luatianshii", 0)
				if room:getCurrent():getNextAlive():hasSkill("luanizhou") then
					room:setPlayerMark(room:getCurrent():getNextAlive(), "luanizhou", 0)
					room:setPlayerMark(room:getCurrent():getNextAlive(), "luanizhouX", 0)
					for i = 1,50 do
						room:setPlayerMark(room:getCurrent():getNextAlive(), "@nizhou" .. i, 0)
					end
				end
			end 
		end 
	end 
}


luanizhouCard = sgs.CreateSkillCard{
	name = "luanizhou",
	will_throw = false,
	target_fixed = true,
	handling_method = sgs.Card_MethodNone,
	on_use = function(self, room, source, targets)
		local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		dummy:addSubcards(self:getSubcards())
		room:moveCardTo(dummy, source, sgs.Player_DrawPile)
		local num = room:askForChoice(source, "luanizhou", "1+2+3+4+5+6+7+8+9+10+11+12+13")
		room:setPlayerMark(source, "luanizhou", tonumber(num))
		num = tostring(tonumber(num) + 1)
		room:setPlayerMark(source, "@nizhou" .. num, 1)
	end
}

luanizhouVS = sgs.CreateOneCardViewAsSkill{
	name = "luanizhou",
	filter_pattern = ".",
	view_as = function(self,card)
		local skillcard = luanizhouCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function()
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luanizhou"
	end
}

luanizhou = sgs.CreateTriggerSkill{
	name = "luanizhou",
	view_as_skill = luanizhouVS,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			if player:getPhase() == sgs.Player_Start and room:askForUseCard(player, "@@luanizhou", "@luanizhou") then

			end
		end
	end
}
luashehuo = sgs.CreateTriggerSkill{
	name = "luashehuo",
	events = {sgs.Damaged},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.Damaged then
			local damage = data:toDamage()
			if damage.damage == 0 then return false end 
			if player:objectName() == damage.to:objectName() and player:hasSkill("luashehuo")
				and player:getMark("luanizhou") > 0 and room:askForSkillInvoke(player, self:objectName(), data) then
				for i = 1, damage.damage, 1 do 
					local choice = room:askForChoice(player, "luashehuo", "luashehuo1+luashehuo2")
					if choice == "luashehuo1" then
						local num = room:askForChoice(player, "luanizhou", "1+2+3+4+5+6+7+8+9+10+11+12+13")
						room:setPlayerMark(player, "luanizhou", tonumber(num))
						for j = 1,50 do
							room:setPlayerMark(player, "@nizhou" .. j, 0)
						end
						num = tostring(tonumber(num) + 1)
						room:setPlayerMark(player, "@nizhou" .. num, 1)
					else
						room:setPlayerMark(player, "luanizhouX", 1)
					end 
				end
			end 
		end
	end
}
sagume:addSkill(luanizhour)
sagume:addSkill(luanizhou)
sagume:addSkill(luashehuo)

sagumeA:addSkill(luanizhou)
sagumeA:addSkill(luashehuo)

sagumeB:addSkill(luanizhou)
sagumeB:addSkill(luashehuo)

sagumeC:addSkill(luanizhou)
sagumeC:addSkill(luashehuo)

lualunhui = sgs.CreateTriggerSkill{
	name = "lualunhui" ,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Death} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.Death then
			local death = data:toDeath()
			for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				room:loseMaxHp(p)
				if not p:isAlive() then return false end
				if not p:hasSkill("luabiyue") then
					room:handleAcquireDetachSkills(p, "luabiyue")

					continue
				end
				if not p:hasSkill("kuanggu") then
					room:handleAcquireDetachSkills(p, "kuanggu")
					room:handleAcquireDetachSkills(p, "mashu")
					continue
				end
				if not p:hasSkill("luachongsheng") then
					room:handleAcquireDetachSkills(p, "luachongsheng")
					continue
				end
				if not p:hasSkill("luadanmu") then
					room:handleAcquireDetachSkills(p, "luadanmu")
					room:handleAcquireDetachSkills(p, "feiying")
					continue
				end

				local plist = sgs.SPlayerList()
				for _,p2 in sgs.qlist(room:getAllPlayers(true)) do
					if not p2:isAlive() then
						plist:append(p2)
					end
				end
				if plist:isEmpty() then return false end
				if not p:isAlive() then return false end
				local uses = {}
				for _, liege in sgs.qlist(plist) do
					table.insert(uses, liege:getGeneralName())
				end
				local name = room:askForChoice(p, "luajianglin", table.concat(uses, "+"))
				local rp
				for _,p2 in sgs.qlist(room:getAllPlayers(true)) do
					if not p2:isAlive() and name == p2:getGeneralName() then
						rp = p2
					end
				end
				for _, skill in sgs.qlist(rp:getVisibleSkillList()) do
					room:handleAcquireDetachSkills(p, skill:objectName())
				end
			end
		end
	end
}
luazhongmo = sgs.CreateTriggerSkill{
	name = "luazhongmo$" ,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Death} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.Death then
			if room:getAlivePlayers():length() < 3 then
				room:setPlayerMark(room:getLord(), "@clock_time", room:getTag("TurnLengthCount"):toInt() + 20)
			end
		end
	end
}
sarielelis:addSkill(lualunhui)
sarielelis:addSkill(luazhongmo)




luajinshuCard = sgs.CreateSkillCard{
	name = "luajinshu",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		source:loseMark("@jinshu", room:getAlivePlayers():length() - 1)
		local choice = room:askForChoice(source, "luajinshu", "savage_assault+archery_attack+god_salvation+amazing_grace")
		source:getRoom():removeTag("luachunhuiTC")
		local slash = sgs.Sanguosha:cloneCard(choice, sgs.Card_NoSuit, 0)
		room:useCard(sgs.CardUseStruct(slash, source, sgs.SPlayerList()))
	end
}
luajinshu = sgs.CreateZeroCardViewAsSkill{
	name = "luajinshu",
	view_as = function(self, cards)
		return luajinshuCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:usedTimes("#luajinshu") < 2 and player:getMark("@jinshu") >= player:getAliveSiblings():length()
	end
}

luajinshu2 = sgs.CreateTriggerSkill
{
	name = "#luajinshu",
	events = {sgs.CardUsed, sgs.CardResponded},
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardUsed then
			local card = data:toCardUse().card
			if card:isKindOf("BasicCard") and player:hasSkill("luajinshu") then
				player:gainMark("@jinshu")
			end
		elseif event == sgs.CardResponded then
			local cd = data:toCardResponse().m_card
			if cd:isKindOf("BasicCard") and player:hasSkill("luajinshu") then
				player:gainMark("@jinshu")
			end
		end
	end,
}

luajinshu3 = sgs.CreateProhibitSkill{ --我， 天秀
	name = "#luajinshu2",
	is_prohibited = function(self, from, to, card)
		return to:hasSkill("luajinshu") and card:isKindOf("EquipCard")
	end
}

luasishuxCard = sgs.CreateSkillCard{
	name = "luasishux",
	will_throw = false,
	handling_method = sgs.Card_MethodResponse,
	filter = function(self, targets, to_select)
		local card0 = sgs.Sanguosha:getCard(self:getSubcards():at(0))
		if card0:isKindOf("BasicCard") then return #targets < 1 and to_select:getHp() == 1 end
		return to_select:objectName() == sgs.Self:objectName()
	end ,
	on_effect = function(self, effect)
		local source = effect.from
		local room = source:getRoom()
		local card0 = sgs.Sanguosha:getCard(self:getSubcards():at(0))

		local move = sgs.CardsMoveStruct()
		move.from = effect.from
		move.from_place = sgs.Player_PlaceHand
		move.to = nil
		move.to_place = sgs.Player_DiscardPile
		move.card_ids = self:getSubcards()
		move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_RESPONSE, effect.from:objectName())
		room:moveCardsAtomic(move, true)

		local data = sgs.QVariant()
		local Response = sgs.CardResponseStruct()
		Response.m_card = card0
		Response.m_who = effect.from
		Response.m_isUse = false
		Response.m_isRetrial = false
		data:setValue(Response)
		room:getThread():trigger(sgs.CardResponded, room, source, data)

		if card0:isKindOf("BasicCard") then
			room:damage(sgs.DamageStruct(self:objectName(), source, effect.to))
			room:setPlayerFlag(source, "luasishuy")
		else
			local choice = room:askForChoice(source, self:objectName(), "Slash+Jink+Analeptic+Peach+Ofuda+Hui") 
			room:setPlayerFlag(source, "luasishux")

			local discard_ids = room:getDiscardPile()
			local trickcard = sgs.IntList()
			for _, id in sgs.qlist(discard_ids) do
				local card = sgs.Sanguosha:getCard(id)
				if card:isKindOf(choice) then
					trickcard:append(id)
				end
			end
			discard_ids = room:getDrawPile()
			for _, id in sgs.qlist(discard_ids) do
				local card = sgs.Sanguosha:getCard(id)
				if card:isKindOf(choice) then
					trickcard:append(id)
				end
			end
			if trickcard:length() > 0 then 
				room:fillAG(trickcard, source)
				local card_id = room:askForAG(source, trickcard, false, "luasishux")
				local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				dummy_0:addSubcard(card_id)
				room:clearAG()
				source:obtainCard(dummy_0) 
				for _, cardX in sgs.qlist(source:getHandcards()) do 
					if cardX:getId() == card_id and not cardX:isKindOf("Jink") then 
						local aaa = room:askForPlayerChosen(source, room:getAlivePlayers(), self:objectName(), "luasishux", false, true)
						if aaa and aaa:objectName() then 
							room:useCard(sgs.CardUseStruct(cardX, source, aaa))
						end 
					end 
				end 
			end
		end

	end
}
luasishux = sgs.CreateOneCardViewAsSkill{
	name = "luasishux",
	view_filter = function(self, card)
		return (card:isKindOf("EquipCard") and not sgs.Self:hasFlag("luasishux"))
			or (card:isKindOf("BasicCard") and not sgs.Self:hasFlag("luasishuy"))
	end,
	view_as = function(self,card)
		local skillcard = luasishuxCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self,player)
		return true
	end
}

lolice:addSkill(luasishux)
lolice:addSkill(luajinshu)
lolice:addSkill(luajinshu2)
lolice:addSkill(luajinshu3)

luafenghuaCard = sgs.CreateSkillCard{
	name = "luafenghua" ,
	filter = function(self, targets, to_select)
		if #targets ~= 0 then return false end
		if to_select:objectName() == sgs.Self:objectName() then return false end
		return true
	end ,
	on_effect = function(self, effect)
		local room = effect.to:getRoom()
		local xo = room:getLord():getMark("@clock_time") + 1
		room:setPlayerMark(effect.from, "luafenghua", xo)
		local _data = sgs.QVariant()
		_data:setValue(effect.to)
		local choice = room:askForChoice(effect.from, "luafenghua", "extraturn+maxhp", _data)
		if choice == "extraturn" then
			effect.to:setMark("luafenghuaA", 1)
		else
			
			local trueHp = effect.to:getHp()  
			effect.to:getRoom():setPlayerProperty(effect.to, "maxhp", sgs.QVariant(effect.to:getMaxHp() + 1))
			effect.to:getRoom():setPlayerProperty(effect.to, "hp", sgs.QVariant(trueHp)) 
			room:recover(effect.to, sgs.RecoverStruct(effect.from, nil, 1))
		end
	end,
}
luafenghua3 = sgs.CreateTriggerSkill{
	name = "#luafenghua3" ,
	events = {sgs.EventPhaseChanging} ,
	global = true,
	priority = 1,
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		if change.to ~= sgs.Player_NotActive then return false end
		local shensimayi
		for _, p in sgs.qlist(player:getRoom():getAlivePlayers()) do
			if p:getMark("luafenghuaA") > 0 then shensimayi = p end
		end
		if not shensimayi or shensimayi:getMark("luafenghuaA") == 0 then return false end
		shensimayi:setMark("luafenghuaA",0)
		local p = shensimayi
		local playerdata = sgs.QVariant()
		playerdata:setValue(p)
		player:getRoom():setTag("luafenghuaInvoke", playerdata)
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end
}
luafenghuaDo = sgs.CreateTriggerSkill{
	name = "#luafenghua-do" ,
	events = {sgs.EventPhaseStart},
	global = true,
	priority = 1 ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:getTag("luafenghuaInvoke") then
			local target = room:getTag("luafenghuaInvoke"):toPlayer()
			room:removeTag("luafenghuaInvoke")
			if target and target:isAlive() then
				room:setPlayerMark(target, "@extra_turn", 1)
				target:gainAnExtraTurn()
				room:setPlayerMark(target, "@extra_turn", 0)
			end
			return false
		end
		return false
	end,
	can_trigger = function(self, target)
		return target and (target:getPhase() == sgs.Player_NotActive)
	end
}

luafenghua = sgs.CreateViewAsSkill{
	name = "luafenghua" ,
	n = 2 ,
	view_filter = function(self, selected, to_select)
		if #selected >= 2 then return false end
		if sgs.Self:isJilei(to_select) then return false end
		if #selected ~= 0 then
			local suit = selected[1]:getSuit()
			return to_select:getSuit() == suit
		end
		return true
	end ,
	view_as = function(self, cards)
		if #cards ~= 2 then return nil end
		local card = luafenghuaCard:clone()
		card:addSubcard(cards[1])
		card:addSubcard(cards[2])
		return card
	end ,
	enabled_at_play = function(self, player)
		return (player:getCardCount(true) >= 2) and player:getMark("luafenghua") == 0
	end
}
luafenghua2 = sgs.CreateTriggerSkill{
	name = "#luafenghua" ,
	global = true,
	events = {sgs.TurnStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom() 
		for _, toziko in sgs.qlist(room:findPlayersBySkillName("luafenghua")) do 
			if not toziko then return false end
			local xo = room:getLord():getMark("@clock_time") + 1
			local xo2 = toziko:getMark("luafenghua")
			room:writeToConsole("fenghua test F2" .. xo .. " " .. xo2)
			if toziko and xo2 > 0 and xo ~= xo2 and xo > 0 then
				room:writeToConsole("fenghua test")
				room:setPlayerMark(toziko, "luafenghua", 0)
			end
		end 
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}

luaxueyue = sgs.CreateTriggerSkill{
	name = "luaxueyue",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			if player:getPhase() ~= sgs.Player_RoundStart and player:getPhase() ~= sgs.Player_Start and player:getPhase() ~= sgs.Player_NotActive
				and room:askForSkillInvoke(player, "luaxueyue", data) then 
				local huase2 = ".|"
				for _, card in sgs.qlist(player:getHandcards()) do
					if not string.find(huase2, card:getSuitString()) then
						if huase2 ~= ".|" then
							huase2 = huase2 .. "," .. card:getSuitString()
						else
							huase2 = huase2 .. card:getSuitString()
						end
					end
				end
				if huase2 == ".|" then huase2 = "." end
				local judge = sgs.JudgeStruct()
				judge.pattern = huase2
				judge.good = false
				if huase2 == "." then
					judge.good = true
				end
				judge.reason = self:objectName()
				judge.who = player
				room:judge(judge)
				local function canLoseHp()
					for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
						if hecatiaX and isFriendQ(room, player, hecatiaX) and player:objectName() ~= hecatiaX:objectName()
								and player:getHp() == hecatiaX:getHp() then
							room:notifySkillInvoked(hecatiaX, "luayiti")
							return false
						end 
					end 
					for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
						if Erin and Erin:getKingdom() == player:getKingdom() then
							room:notifySkillInvoked(Erin, "luajiance")
							return false
						end 
					end 
					return true
				end 
				if not judge:isGood() and canLoseHp() then 
					room:loseHp(player)  
				end 
			end
		end 
	end
}

luatiandu = sgs.CreateTriggerSkill{
	name = "luatiandu",
	frequency = sgs.Skill_Frequent,
	events = {sgs.FinishJudge},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local judge = data:toJudge()
		local card = judge.card
		local card_data = sgs.QVariant()
		card_data:setValue(card)
		if room:getCardPlace(card:getEffectiveId()) == sgs.Player_PlaceJudge and player:askForSkillInvoke(self:objectName(), card_data) then
			player:obtainCard(card)
		end
	end
}

satsuki:addSkill(luafenghua)
satsuki:addSkill(luafenghua2)
satsuki:addSkill(luafenghuaDo)
satsuki:addSkill(luafenghua3)
satsuki:addSkill(luaxueyue)
satsuki:addSkill(luatiandu)

zhujiaoCard = sgs.CreateSkillCard{
	name = "luazhujiao",
	target_fixed = true,
	on_use = function(self, room, player, targets)
		if player:getMark("@luazhujiao") == 0 then
			player:drawCards(2)
			room:setPlayerMark(player, "@luazhujiao", 1)
		else
			player:drawCards(1)
			room:setPlayerMark(player, "@luazhujiao", 0)
		end
		room:setPlayerMark(player, "luazhujiao2", player:getMark("luazhujiao2") + 1)
	end
}
luazhujiao = sgs.CreateViewAsSkill{
	name = "luazhujiao",
	n = 2,
	view_filter = function(self, selected, to_select)
		if sgs.Self:getMark("@luazhujiao") == 0 then
			return #selected == 0 and not sgs.Self:isJilei(to_select)
		else
			return not sgs.Self:isJilei(to_select)
		end
	end,
	view_as = function(self, cards)
		if #cards == 0 then return end
		if sgs.Self:getMark("@luazhujiao") == 1 and #cards == 1 then return end
		local skillcard = zhujiaoCard:clone()
		for _, cd in ipairs(cards) do
			skillcard:addSubcard(cd)
		end
		return skillcard
	end,
	enabled_at_play = function(self, player)
		return player:getMaxCards() > 0
	end
}
luazhujiao2 = sgs.CreateTriggerSkill{
	name = "#luazhujiao",
	global = true,
	priority = 5,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.TurnStart},
	on_trigger = function(self, event, playerQ, data)
		if playerQ:objectName() == playerQ:getRoom():getCurrent():objectName() then
			for _, p2 in sgs.qlist(playerQ:getRoom():findPlayersBySkillName("luazhujiao")) do
				playerQ:getRoom():setPlayerMark(p2, "luazhujiao2", 0)
			end
		end
	end
}
luashikongCard = sgs.CreateSkillCard{
	name = "luashikong",
	will_throw = false,
	--handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		local card = sgs.Self:getMark("shikong") - 1
		card = sgs.Sanguosha:getCard(card)
		card:setSkillName(self:objectName())
		if card and card:targetFixed() and not response then
			return false
		end
		if response then return true end
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		--if card:isKindOf("Slash") and not card:isAvailable(sgs.Self) then return false end
		return card and card:targetFilter(qtargets, to_select, sgs.Self) and not sgs.Self:isProhibited(to_select, card, qtargets)
	end,
	feasible = function(self, targets)
		local card = sgs.Self:getMark("shikong") - 1
		card = sgs.Sanguosha:getCard(card)
		card:setSkillName(self:objectName())
		if response then return true end
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		--if card:isKindOf("Slash") and not card:isAvailable(sgs.Self) then return false end
		return card and card:targetsFeasible(qtargets, sgs.Self)
	end,
	on_validate = function(self, card_use)
		local card = card_use.from:getMark("shikong") - 1
		card = sgs.Sanguosha:getCard(card)
		local use_card = sgs.Sanguosha:cloneCard(card:objectName())
		use_card:addSubcard(card)
		use_card:setSkillName(self:objectName())
		local available = true
		for _,p in sgs.qlist(card_use.to) do
			if card_use.from:isProhibited(p,use_card) then
				available = false
				break
			end
		end
		available = available and use_card:isAvailable(card_use.from)
		if not available then
			local dummy = sgs.Sanguosha:cloneCard("jink")
			dummy:addSubcard(card)
			card_use.from:getRoom():throwCard(dummy, nil, card_use.from)
			return nil
		end
		return use_card
	end,
}
luashikongVS = sgs.CreateZeroCardViewAsSkill{
	name = "luashikong",
	response_pattern = "@@luashikong",

	view_as = function(self)
		return luashikongCard:clone()
	end
}
luashikong = sgs.CreateTriggerSkill{
	name = "luashikong",
	global = true,
	view_as_skill = luashikongVS,
	frequency = sgs.Skill_Frequent,
	events = {sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data, room)
		local move = data:toMoveOneTime()
		if move.from and move.from:objectName() == player:objectName() and player:hasSkill("luashikong") and not move.card_ids:isEmpty()
				and player:getMark("luashikong2") < 2 then
			local reason = move.reason.m_reason
			local reasonx = bit32.band(reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)
			local Yes = reasonx == sgs.CardMoveReason_S_REASON_DISCARD or reasonx == sgs.CardMoveReason_S_REASON_RESPONSE
			if Yes then
				local use_list = sgs.IntList()
				local use_list2 = {}

				local i = 0
				for _,id in sgs.qlist(move.card_ids) do
					card = sgs.Sanguosha:getCard(id)
					if card and room:getCardPlace(id) == sgs.Player_DiscardPile and move.from_places:at(i) == sgs.Player_PlaceHand then
						use_list:append(id)
						table.insert(use_list2, tostring(id))
					end
					i = i + 1
				end

				if use_list:length() > 0 then
					local y = use_list:length()
					player:setTag("luashikongK", sgs.QVariant(table.concat(use_list2, "+")))
					local tp = room:askForPlayerChosen(player, room:getAllPlayers(), "luashikong", "@luashikong", true, true)
					player:removeTag("luashikongK")
					if tp then
						--while true do
							room:fillAG(use_list, tp)
							local id1 = room:askForAG(tp, use_list,  false, self:objectName()) --S_REASON_CHANGE_EQUIP
							local id2 = id1 + 1
							room:writeToConsole("shikong test" .. id1)
							use_list:removeOne(id1)
							room:clearAG()
							room:setPlayerMark(tp, "shikong", id2)
							local card = sgs.Sanguosha:getCard(id1)
							if card:targetFixed() then
								if card:isKindOf("DefensiveHorse") or card:isKindOf("OffensiveHorse") then
									local dummy_p = sgs.Sanguosha:cloneCard("jink")
									dummy_p:addSubcard(id1)
									if card:isKindOf("DefensiveHorse") then
										if tp:getDefensiveHorse() then
											local moveA = sgs.CardsMoveStruct()
											moveA.card_ids = sgs.IntList()
											moveA.from = tp
											moveA.from_place = sgs.Player_PlaceEquip
											moveA.card_ids:append(tp:getDefensiveHorse():getId())
											moveA.to = nil
											moveA.to_place = sgs.Player_DiscardPile
											moveA.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_CHANGE_EQUIP, tp:objectName(), "luashikong", "")
											room:moveCardsAtomic(moveA, true)	--我也很尽力了，只能伪实现

										end
										room:moveCardTo(dummy_p, tp, tp, sgs.Player_PlaceEquip,
												sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, tp:objectName(), self:objectName(), nil))
									else
										if tp:getOffensiveHorse() then
											local moveA = sgs.CardsMoveStruct()
											moveA.card_ids = sgs.IntList()
											moveA.from = tp
											moveA.from_place = sgs.Player_PlaceEquip
											moveA.card_ids:append(tp:getOffensiveHorse():getId())
											moveA.to = nil
											moveA.to_place = sgs.Player_DiscardPile
											moveA.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_CHANGE_EQUIP, tp:objectName(), "luashikong", "")
											room:moveCardsAtomic(moveA, true)	--我也很尽力了，只能伪实现
										end
										room:moveCardTo(dummy_p, tp, tp, sgs.Player_PlaceEquip,
												sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, tp:objectName(), self:objectName(), nil))
									end
								else
									local dummy_0 = sgs.Sanguosha:cloneCard(card:objectName(), sgs.Card_NoSuit, 0)
									dummy_0:addSubcard(id1)
									room:clearAG()
									if tp:isCardLimited(dummy_0, sgs.Card_MethodUse) or card:isKindOf("Jink") or card:isKindOf("sakura") or card:isKindOf("Nullification")
											or ((not tp:isWounded()) and card:isKindOf("Peach")) then
									else
										room:useCard(sgs.CardUseStruct(dummy_0, tp, sgs.SPlayerList()))
									end
								end
							else
								room:writeToConsole("shikongtest" .. tp:getMark("shikong"))
								if room:askForUseCard(tp, "@@luashikong", "@luashikong") then
								end
							end
							room:setPlayerMark(player, "luashikong2", player:getMark("luashikong2") + 1)
							y = y - 1
							if use_list:isEmpty() then 
								luashikongSpec(use_list2, tp, room)
								return false
							end 
							if y == 0 then
								luashikongSpec(use_list2, tp, room)
								return false
							end  
							luashikongSpec(use_list2, tp, room)
					end

				end
			end
		end
	end
}
function luashikongSpec(use_list2, tp, room)
	local counT = 0
	for _, id in ipairs(use_list2) do 
		counT = counT + sgs.Sanguosha:getCard(id):getNumber()
	end
	if counT == 10 then 
		local dummy3 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		for _, id in ipairs(use_list2) do 
			dummy3:addSubcard(id)
		end
		tp:obtainCard(dummy3)
	end 
end 
luashikong2 = sgs.CreateTriggerSkill{
	name = "#luashikong",
	global = true,
	priority = 5,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.TurnStart},
	on_trigger = function(self, event, playerQ, data)
		if playerQ:objectName() == playerQ:getRoom():getCurrent():objectName() then
			for _, p2 in sgs.qlist(playerQ:getRoom():findPlayersBySkillName("luashikong")) do
				playerQ:getRoom():setPlayerMark(p2, "luashikong2", 0)
			end
		end
	end
}
chiyuri:addSkill(luazhujiao)
chiyuri:addSkill(luazhujiao2)
chiyuri:addSkill(luashikong)
chiyuri:addSkill(luashikong2)

luaboli2 = sgs.CreateTargetModSkill{
	name = "#luaboli",
	pattern = "Slash",
	residue_func = function(self, player, card)
		if player:hasFlag("luaboli_unlimited") or card:getSkillName() == "luaboli" then
			return 1
		end
	end,
	distance_limit_func = function(self, player, card)
		if card:getSkillName() == "luaboli" then
			return 999
		else
			return 0
		end
	end
}
luaboliCard = sgs.CreateSkillCard{
	name = "luaboli",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select)
		if to_select:objectName() == sgs.Self:objectName() then return false end
		local card = sgs.Sanguosha:getCard(self:getSubcards():at(0))
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
		slash:addSubcard(card:getEffectiveId())
		slash:setSkillName("luaboli")
		slash:deleteLater()
		if #targets ~= 0 then return false end
		return not sgs.Self:isProhibited(to_select, slash)
	end,
	on_validate = function(self,carduse)
		local source = carduse.from
		local room = source:getRoom()
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		slash:addSubcard(card:getEffectiveId())
		source:drawCards(1)
		room:setPlayerFlag(source,"luaboli_unlimited")
		room:setPlayerFlag(source,"luaboli_used")

		room:writeToConsole("luashishenyCE0")
		local xx = source:getTag("luashisheny")
		if not xx then 
			xx = ""
		else
			xx = xx:toString()
		end 
		xx = analysisString(xx, "boli")	
		room:writeToConsole("luashishenyCE0 " .. xx)
		source:setTag("luashisheny", sgs.QVariant(xx)) 

		return slash
	end,
}
luaboli = sgs.CreateOneCardViewAsSkill{
	name = "luaboli",
	view_filter = function(self, card)
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
			slash:addSubcard(card:getEffectiveId())
			slash:setSkillName("luaboli")
			slash:deleteLater()
			return slash:isAvailable(sgs.Self)
		end
		return false
	end,
	view_as = function(self, originalCard)
		local Leishi_card = luaboliCard:clone()
		Leishi_card:addSubcard(originalCard:getId())
		return Leishi_card
	end,
	enabled_at_play = function(self, player)
		return not player:hasFlag("luaboli_used")
	end,
}


luaxiaohua = sgs.CreateTriggerSkill{
	name = "luaxiaohua",
	priority = 10,
	events = {sgs.EventPhaseChanging},
	--frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data, room)
		local rp = player:getNextAlive()
		local change = data:toPhaseChange()
		if change.to == sgs.Player_Start then
			room:loseMaxHp(player)
			return false
		end
	end
}

luaquxie = sgs.CreateTriggerSkill{
	name = "luaquxie",
	events = {sgs.TargetSpecified},
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		local room = player:getRoom()
		if use.card and use.card:isKindOf("Slash") and not player:hasFlag("luaquxie") then
			
			if room:getLord():hasFlag("luafanzeSlash") then return false end 
			local index = 1
			local jink_table = sgs.QList2Table(player:getTag("Jink_" .. use.card:toString()):toIntList())

			for _, p in sgs.qlist(use.to) do
				local _data = sgs.QVariant()
				_data:setValue(p)
				if room:askForSkillInvoke(player, self:objectName(), _data) then
					room:setPlayerFlag(player, "luaquxie")
					room:showAllCards(p)

                    local Carddata2 = sgs.QVariant() -- ai用
                    Carddata2:setValue(use.card)
                    room:setTag("luaquxieTC", Carddata2)
                    local choice = room:askForChoice(player, "luaquxie", "luaquxie1+luaquxie2", _data)
                    room:removeTag("luaquxieTC")
					if choice == "luaquxie2" then
						local ids = sgs.IntList()
						for _, card in sgs.list(p:getHandcards()) do
							if card:getSuit() == use.card:getSuit() then
								ids:append(card:getId())
							end
						end
						local dummy = sgs.Sanguosha:cloneCard("jink")
						dummy:addSubcards(ids)
						room:throwCard(dummy, p, player)
					else
						jink_table[index] = 0
						index = index + 1
					end
				end
			end
			local jink_data = sgs.QVariant()
			jink_data:setValue(Table2IntList(jink_table))
			player:setTag("Jink_" .. use.card:toString(), jink_data)
		end
	end
}
luaquxie2 = sgs.CreateTriggerSkill{
	name = "#luaquxie" ,
	global = true,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		
		for _, toziko in sgs.qlist(player:getRoom():findPlayersBySkillName("luaquxie")) do 
			if toziko and toziko:hasFlag("luaquxie") then
				room:setPlayerFlag(toziko, "-luaquxie")
			end
		end 
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}
jz_reimu:addSkill(luaboli2)
jz_reimu:addSkill(luaboli)
jz_reimu:addSkill(luaxiaohua)
jz_reimu:addSkill(luaquxie)
jz_reimu:addSkill(luaquxie2)

jisu_list = {}
jisu_list2 = {}
luajisu = sgs.CreateTriggerSkill{
	name = "luajisu",
	global = true,
	events = {sgs.EventPhaseChanging, sgs.BeforeCardsMove, sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive and player:objectName() == room:getCurrent():objectName() then
				if (#jisu_list == 0) then
					for _, Akyuu in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
						local room_0 = Akyuu:getRoom()
						if room_0:askForSkillInvoke(Akyuu, "luajisu") then
							local judge = sgs.JudgeStruct()
							judge.pattern = "TrickCard|.|."
							judge.good = true
							judge.reason = self:objectName()
							judge.who = Akyuu
							room:judge(judge)
							if judge:isGood() then
								room:setPlayerMark(Akyuu, "@extra_turn", 1)
								Akyuu:gainAnExtraTurn()
								room:setPlayerMark(Akyuu, "@extra_turn", 0)
							end
						end
					end
				else
					jisu_list = {}
				end
			end
		elseif event == sgs.CardsMoveOneTime then
			local move = data:toMoveOneTime()
			if move.to_place == sgs.Player_DiscardPile then
				local Akyuu = room:findPlayerBySkillName("luajisu")
				if Akyuu then
					for _, id in sgs.qlist(move.card_ids) do
						if not table.contains(jisu_list, id) and (move.from_places:contains(sgs.Player_PlaceEquip) or move.from_places:contains(sgs.Player_PlaceHand)) then
							room:writeToConsole("luajisu test 2")
							table.insert(jisu_list, id)
						end
					end
					for _, id in sgs.qlist(move.card_ids) do
						if not table.contains(jisu_list, id) and (move.from_places:contains(sgs.Player_PlaceTable)) and table.contains(jisu_list2, id) then
							room:writeToConsole("luajisu test 2")
							table.insert(jisu_list, id)
						end
					end
					if move.from_places:contains(sgs.Player_PlaceTable) then
						jisu_list2 = {}
					end
				end
			elseif move.to_place == sgs.Player_PlaceTable then
				local Akyuu = room:findPlayerBySkillName("luajisu")
				if Akyuu then
					for _, id in sgs.qlist(move.card_ids) do
						if not table.contains(jisu_list2, id) and (move.from_places:contains(sgs.Player_PlaceEquip) or move.from_places:contains(sgs.Player_PlaceHand)) then
							room:writeToConsole("luajisu test 2")
							table.insert(jisu_list2, id)
						end
					end
				end
			end
		end
	end
}


luafengxing = sgs.CreateTriggerSkill{
	name = "luafengxing" ,
	frequency = sgs.Skill_NotFrequent ,
	events = {sgs.BeforeCardsMove } ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local move = data:toMoveOneTime()
		if not move.from then return false end
		local aicard = {}
		local j = 0
		local bool = false
		local duel = sgs.Sanguosha:cloneCard("duel", sgs.Card_NoSuit, 0)
		for _,card_id in sgs.qlist(move.card_ids) do
			if room:getCardOwner(card_id) and room:getCardOwner(card_id):objectName() == move.from:objectName()
					and room:getCardOwner(card_id):objectName() == player:objectName() then
				local place = move.from_places:at(j)
				if place == sgs.Player_PlaceHand or place == sgs.Player_PlaceEquip then
					bool = true
					table.insert(aicard, card_id)
					duel:addSubcard(card_id)
				end
			end
			j = j + 1
		end
		local _movefrom
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			if move.from:objectName() == p:objectName() then
				_movefrom = p
				break
			end
		end
		local xo = room:getLord():getMark("@clock_time") + 1
		local reason = move.reason
		local basic = bit32.band(reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)
		if _movefrom and _movefrom:objectName() == player:objectName() and _movefrom:hasSkill("luafengxing") and bool
				and (basic == sgs.CardMoveReason_S_REASON_DISCARD) then
			for _, p in sgs.qlist(room:findPlayersBySkillName("luafengxing")) do
				local i = 0
				if xo ~= p:getMark("luafengxingt") and _movefrom:askForSkillInvoke("luafengxing", sgs.QVariant(table.concat(aicard, "+"))) then
					room:setPlayerMark(p, "luafengxingt", xo)
					local old_card_ids = {}
					for _,card_idX in sgs.qlist(move.card_ids) do
						table.insert(old_card_ids, card_idX)
					end
					for _, card_idY in ipairs(old_card_ids) do
						if (duel:getSubcards():contains(card_idY)) then
							move.card_ids:removeOne(card_idY)
							move.from_places:removeAt(i)
						else
							i = i + 1
						end
					end
					local idsA = sgs.IntList()
					local idsB = duel:getSubcards()

					while not idsB:isEmpty() do
						room:fillAG(idsB, player)
						local id = room:askForAG(player, idsB, false, "luafengxing")
						idsA:append(id)
						idsB:removeOne(id)
						room:clearAG()
					end


					local reasonA = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, _movefrom:objectName(), "luafengxing", "")

					local moveK = sgs.CardsMoveStruct()
					moveK.card_ids = idsA
					moveK.to = player
					moveK.to_place = sgs.Player_DrawPile
					moveK.reason = reasonA
					room:moveCardsAtomic(moveK, true)

					local plist = sgs.SPlayerList()
					for _,p2 in sgs.qlist(room:getAlivePlayers()) do
						local ok = false
						for _,card in sgs.qlist(p2:getEquips()) do
							for _,id in sgs.qlist(idsA) do
								if card:getSuit() == sgs.Sanguosha:getCard(id):getSuit() then
									plist:append(p2)
									ok = true
								end
							end
						end
						if not ok then
							for _,card in sgs.qlist(p2:getJudgingArea()) do
								for _,id in sgs.qlist(idsA) do
									if card:getSuit() == sgs.Sanguosha:getCard(id):getSuit() then
										plist:append(p2)
										ok = true
									end
								end
							end
						end
					end
					if not plist:isEmpty() then
						local from = room:askForPlayerChosen(player, plist, "luafengxing", "@luafengxing", true, true)
						if from then
							if from:hasEquip() or from:getJudgingArea():length() > 0 then
								local idsQ = sgs.IntList()
								for _,card in sgs.qlist(from:getEquips()) do
									local could = true
									for _,id in sgs.qlist(idsA) do
										if card:getSuit() == sgs.Sanguosha:getCard(id):getSuit() then
											could = false
											break
										end
									end
									if could then
										idsQ:append(card:getId())
									end
								end
								for _,card in sgs.qlist(from:getJudgingArea()) do
									local could = true
									for _,id in sgs.qlist(idsA) do
										if card:getSuit() == sgs.Sanguosha:getCard(id):getSuit() then
											could = false
											break
										end
									end
									if could then
										idsQ:append(card:getId())
									end
								end
								local card_id = room:askForCardChosen(player, from, "ej", self:objectName(), false, sgs.Card_MethodNone, idsQ)
								if not card_id then data:setValue(move); return false end
								local card = sgs.Sanguosha:getCard(card_id)
								local place = room:getCardPlace(card_id)
								local equip_index = -1
								if place == sgs.Player_PlaceEquip then
									local equip = card:getRealCard():toEquipCard()
									equip_index = equip:location()
								end
								local tos = sgs.SPlayerList()
								local list = room:getAlivePlayers()
								for _,p3 in sgs.qlist(list) do
									if equip_index ~= -1 then
										if not p3:getEquip(equip_index) then
											tos:append(p3)
										end
									else
										if not player:isProhibited(p3, card) and not p3:containsTrick(card:objectName()) then
											tos:append(p3)
										end
									end
								end
								local to = room:askForPlayerChosen(player, tos, "LuaQiaobian")
								if to then
									local reasonS = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, player:objectName(), self:objectName(), "")
									room:moveCardTo(card, from, to, place, reasonS)
								end
							end
						end
					end
				end
			end
			data:setValue(move)
		end
		return false
	end,
}

sp_aya:addSkill(luajisu)
sp_aya:addSkill(luafengxing)

luashuangyue = sgs.CreateTriggerSkill{
	name = "luashuangyue",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DrawNCards},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local count = data:toInt() - 1
		data:setValue(count)
	end
}


luashuangyue2 = sgs.CreateTriggerSkill{
	name = "#luashuangyue" ,
	events = {sgs.EventPhaseChanging} ,
	global = true,
	frequency = sgs.Skill_Compulsory ,
	priority = 1,
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		if change.to ~= sgs.Player_NotActive then return false end
		local room = player:getRoom()
		
		for _, shensimayi in sgs.qlist(player:getRoom():findPlayersBySkillName("luashuangyue")) do  
			local xo = room:getLord():getMark("@clock_time") + 1
			if (not shensimayi) or (shensimayi:getMark("luashuangyue") == xo + 1) or player:objectName() ~= shensimayi:objectName() 
				or player:objectName() ~= room:getCurrent():objectName()  then return false end
			shensimayi:setMark("luashuangyue", xo + 1)
			local p = shensimayi
			local playerdata = sgs.QVariant()
			playerdata:setValue(p)
			player:getRoom():setTag("luashuangyueInvoke", playerdata)
		end 
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end
}
luashuangyueDo = sgs.CreateTriggerSkill{
	name = "#luashuangyue-do" ,
	events = {sgs.EventPhaseStart},
	global = true,
	priority = 1 ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:getTag("luashuangyueInvoke") then
			local target = room:getTag("luashuangyueInvoke"):toPlayer()
			room:removeTag("luashuangyueInvoke")
			if target and target:isAlive() then 
				for _, p in sgs.qlist(player:getRoom():findPlayersBySkillName("luashuangyue")) do  
					if p then
						if p:isAlive() then
							room:setPlayerMark(p, "@extra_turn", 1)
							p:gainAnExtraTurn()
							room:setPlayerMark(p, "@extra_turn", 0)
						end
					end
				end 
			end
			return false
		end
		return false
	end,
	can_trigger = function(self, target)
		return target and (target:getPhase() == sgs.Player_NotActive)
	end
}
luaxiongshi = sgs.CreateFilterSkill{
	name = "luaxiongshi",
	view_filter = function(self,to_select)
		if to_select:isKindOf("BasicCard") and to_select:isRed() then return true end
	end,
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		local _card = sgs.Sanguosha:getWrappedCard(card:getId())
		_card:takeOver(slash)
		return _card
	end
}
luaxiongshi2 = sgs.CreateTriggerSkill {
	name = "#luaxiongshi",
	events = { sgs.TargetConfirmed },
	frequency = sgs.Skill_Compulsory ,
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		local room = player:getRoom()
		if event == sgs.TargetConfirmed and use.from and player:objectName() == use.from:objectName() and use.from:hasSkill(self:objectName())
				and use.to:length() >= 1 and (use.card:isKindOf("Slash") or (use.card:isKindOf("BasicCard") and use.card:isRed())) then
			if room:getLord():hasFlag("luafanzeSlash") then return false end 
			for _, t in sgs.qlist(use.to) do
				player:addMark("luaxiongshi")
			end
		end
	end
}
luaxiongshi3 = sgs.CreateTriggerSkill{
	name = "#luaxiongshi2",
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.PreCardUsed} ,
	on_trigger = function(self, event, youmu, data)
		if event == sgs.PreCardUsed then
			local use = data:toCardUse()
			local room = youmu:getRoom()
			local function YouMuCheck(card, target)
				if card:isKindOf("Hui") or card:isKindOf("Ofuda") then
					return true
				elseif card:isKindOf("FaithCollection") or card:isKindOf("Snatch") then
					return not target:isNude() 	
				elseif card:isKindOf("Banquet") then
					return not target:containsTrick("banquet")
				end
			end
			if use.from:objectName() == youmu:objectName() and use.from:getMark("luaxiongshi") > 0 
				and not use.card:isKindOf("EquipCard") and not use.card:isKindOf("SkillCard") then
				if (use.card:isNDTrick() or use.card:isKindOf("BasicCard")) then
					local y = use.from:getMark("luaxiongshi")
					if (sgs.Sanguosha:getCurrentCardUseReason() ~= sgs.CardUseStruct_CARD_USE_REASON_PLAY) then return false end
					local available_targets = sgs.SPlayerList()
					if (not use.card:isKindOf("AOE")) and (not use.card:isKindOf("GlobalEffect")) then
						for _, p in sgs.qlist(room:getAlivePlayers()) do
							if (use.to:contains(p) or room:isProhibited(youmu, p, use.card)) then continue end
							if (use.card:targetFixed()) then
								if (not use.card:isKindOf("Peach")) or (p:isWounded()) then
									available_targets:append(p)
								end
							else
								if (use.card:targetFilter(sgs.PlayerList(), p, youmu) or YouMuCheck(use.card, p)) then
									available_targets:append(p)
								end
							end
						end
					end
					local extra
					if not use.card:isKindOf("Collateral") then
						while not available_targets:isEmpty() and y > 0 do
							local Carddata2 = sgs.QVariant() -- ai用
							Carddata2:setValue(use.card)
							room:setTag("luajianjiTC", Carddata2)
							extra = room:askForPlayerChosen(youmu, available_targets, "luaxiongshi", "luaxiongshi", true, true)
							room:removeTag("luajianjiTC")
							if extra then
								use.to:append(extra)
							else
								break
							end
							available_targets:removeOne(extra)
							y = y - 1
						end
					end
					room:sortByActionOrder(use.to)
					data:setValue(use)
					use.from:loseAllMarks("luaxiongshi")
					return false
				end
				use.from:loseAllMarks("luaxiongshi")
			end
		end
		return false
	end
}

mugetsu:addSkill(luashuangyue)
mugetsu:addSkill(luashuangyue2)
mugetsu:addSkill(luashuangyueDo)
mugetsu:addSkill(luaxiongshi)
mugetsu:addSkill(luaxiongshi2)
mugetsu:addSkill(luaxiongshi3)


luashuangshen = sgs.CreateTriggerSkill{
	name = "luashuangshen", 
	frequency = sgs.Skill_Compulsory, 
	events = {sgs.CardsMoveOneTime},
	can_trigger = function(self, target)
		return target and target:isAlive()
	end,
	on_trigger = function(self, event, player, data, room) 
		local move = data:toMoveOneTime()
		if move.from and move.from:objectName() == player:objectName() and player:hasSkill("luashuangshen") and not move.card_ids:isEmpty() then
			if move.to_place == sgs.Player_DiscardPile 
				or (move.to_place == sgs.Player_PlaceTable and move.reason.m_reason ~= sgs.CardMoveReason_S_REASON_PREVIEW) then		 
				for i = 0, (move.card_ids:length()-1), 1 do
					local card_id = move.card_ids:at(i)
					local cardX = sgs.Sanguosha:getCard(card_id)
					if (move.from_places:at(i) == sgs.Player_PlaceHand or move.from_places:at(i) == sgs.Player_PlaceEquip
						or move.from_places:at(i) == sgs.Player_PlaceTable) and not cardX:isKindOf("BasicCard")
						and room:getDiscardPile():length() > 1 and room:getCardPlace(card_id) == sgs.Player_DiscardPile then
						local card0_id = room:getDiscardPile():at(1)
						local card0 = sgs.Sanguosha:getCard(card0_id)
						if card0:isKindOf("BasicCard") then  
							room:setPlayerFlag(player, "luashuangshenb") 
							room:getThread():delay(1000)
							local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
							dummy:addSubcard(card0_id)
							player:obtainCard(dummy) 
						end
					end
				end
			end
		end 
	end
}

luashushi = sgs.CreateTriggerSkill {
	name = "luashushi",
	events = {sgs.CardUsed, sgs.CardResponded, sgs.CardFinished}, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardUsed then
			local card = data:toCardUse().card
			if card:isKindOf("BasicCard") and player:hasSkill("luashushi") then
				player:gainMark("@jinshu")
			end
		elseif event == sgs.CardResponded then
			local cd = data:toCardResponse().m_card
			if cd:isKindOf("BasicCard") and player:hasSkill("luashushi") then
				player:gainMark("@jinshu")
			end
		elseif event == sgs.CardFinished then
			local use = data:toCardUse()
			if not use.card then return end
			if not use.card:isKindOf("BasicCard") then return end
			if not use.from then return end
			if use.from:objectName() ~= player:objectName() then return end
			if player:getMark("@jinshu") == 3 or player:getMark("@jinshu") == 6 or player:getMark("@jinshu") == 9 then
				local p = room:askForPlayerChosen(player, room:getAlivePlayers(), "luashushi", "luashushi", false, true)
				if p then
					local acard = sgs.Sanguosha:cloneCard("yuzhi")
					room:useCard(sgs.CardUseStruct(acard, player, p))
				end
			end
		end
	end,
}
luashushi2 = sgs.CreateTriggerSkill{
	name = "#luashushi",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseStart, sgs.TurnStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			if room:getCurrent():objectName() == room:getLord():objectName() and room:getCurrent():getPhase() == sgs.Player_Start then
				for _, p in sgs.qlist(room:findPlayersBySkillName("luashushi")) do
					p:loseAllMarks("@jinshu")
				end
			end
		else 
			for _, yukimai in sgs.qlist(room:findPlayersBySkillName("luashuangshen")) do
				room:setPlayerFlag(yukimai, "-luashuangshenb") 
			end 
		end
	end
}
yukimai:addSkill(luashuangshen)
yukimai:addSkill(luashushi)
yukimai:addSkill(luashushi2)


lualianaiCard = sgs.CreateSkillCard{
	name = "lualianai",
	filter = function(self, selected, to_select)
		if #selected == 0 then
			return true
		end
	end,
	on_effect = function(self, effect)
		effect.from:getRoom():setPlayerMark(effect.to, "@cp2", 1)
		effect.from:loseAllMarks("@lianai")
	end
}

lualianaiVS = sgs.CreateZeroCardViewAsSkill{
	name = "lualianai",
	view_as = function(self, cards)
		return lualianaiCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@lianai") > 0
	end
}

lualianai = sgs.CreateTriggerSkill{
	name = "lualianai",
	frequency = sgs.Skill_Limited,
	limit_mark = "@lianai",
	view_as_skill = lualianaiVS,
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			--room:writeToConsole("shnva test K")
			for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if not p:hasSkill("luacpa") then room:attachSkillToPlayer(p, "luacpa") end
				end
			end
		end
	end
}

luamofa = sgs.CreateTriggerSkill{
	name = "luamofa" ,
	events = {sgs.HpRecover} ,
	frequency = sgs.Skill_Compulsory ,
	global = true,
	on_trigger = function(self, event, player, data)
		if event == sgs.HpRecover then
			local room = player:getRoom()
			local recover = data:toRecover()
			if recover.who and (recover.who:hasSkill("luamofa")) then
				if player:hasSkill("luadanmu") then
					room:handleAcquireDetachSkills(player, "luashushi")
				elseif player:hasSkill("luashushi") then
					room:handleAcquireDetachSkills(player, "luadanmu")
				else
					local choice = room:askForChoice(player, "luamofa", "luashushi+luadanmu")
					if choice == "luashushi" then
						room:handleAcquireDetachSkills(player, "luashushi")
					else
						room:handleAcquireDetachSkills(player, "luadanmu")
					end
				end
			end
		end
	end
}
ellen:addSkill(lualianai)
ellen:addSkill(luamofa)


luajulianCard = sgs.CreateSkillCard{
	name = "luajulian",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		local choice = room:askForChoice(source, "luajulian", "luajulian1+luajulian2+luajulian3+luajulian4")
		choice = "@" .. choice
		--room:setPlayerMark(source, choice, 1)
		source:gainMark(choice)
	end,

}
luajulian = sgs.CreateOneCardViewAsSkill{
	name = "luajulian",
	view_filter = function(self, card)
		return not sgs.Self:isJilei(card)
	end,
	view_as = function(self, card)
		local skill_card = luajulianCard:clone()
		skill_card:setSkillName(self:objectName())
		skill_card:addSubcard(card:getId())
		return skill_card
	end
}

julian_list = {}
luajulian2 = sgs.CreateTriggerSkill{
	name = "#luajulian2",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardFinished, sgs.PreCardUsed, sgs.TargetConfirmed, sgs.TrickCardCanceling} ,
	on_trigger = function(self, event, elliy, data)
		local room = elliy:getRoom()
		if event == sgs.PreCardUsed then
			local use = data:toCardUse()
			local card = use.card
			if use.card:isKindOf("SkillCard") then return false end
			local function YouMuCheck(cardS, target)
				if cardS:isKindOf("Hui") or cardS:isKindOf("Ofuda") then
					return true
				elseif cardS:isKindOf("FaithCollection") then
					return not target:isNude()
				elseif card:isKindOf("Banquet") then
					return not target:containsTrick("banquet")
				end
			end
			if use.from:objectName() == elliy:objectName() and (use.card:isNDTrick() or use.card:isKindOf("BasicCard")) and use.from:hasSkill("luajulian")
					and use.from:getMark("@luajulian1") > 0 then
				if (sgs.Sanguosha:getCurrentCardUseReason() ~= sgs.CardUseStruct_CARD_USE_REASON_PLAY) then return false end
				local available_targets = sgs.SPlayerList()
				if (not use.card:isKindOf("AOE")) and (not use.card:isKindOf("GlobalEffect")) then
					for _, p in sgs.qlist(room:getAlivePlayers()) do
						if (use.to:contains(p) or room:isProhibited(elliy, p, use.card)) then continue end
						if (use.card:targetFixed()) then
							if (not use.card:isKindOf("Peach")) or (p:isWounded()) then
								available_targets:append(p)
							end
						else
							if (use.card:targetFilter(sgs.PlayerList(), p, elliy) or YouMuCheck(use.card, p)) then
								available_targets:append(p)
							end
						end
					end
				end
				if not use.card:isKindOf("Collateral") then
					while use.from:getMark("@luajulian1") > 0 do
						local extra = nil
						local Carddata2 = sgs.QVariant() -- ai用
						Carddata2:setValue(use.card)
						use.from:getRoom():setTag("luajianjiTC", Carddata2)
						room:setPlayerFlag(elliy, "luajianjiQ")
						extra = room:askForPlayerChosen(elliy, available_targets, "luajianjic", "luajianjic", true, true)
						room:setPlayerFlag(elliy, "-luajianjiQ")
						use.from:getRoom():removeTag("luajianjiTC")
						if extra then
							available_targets:removeOne(extra)
							use.to:append(extra)
							if available_targets:isEmpty() then break end
						else
							break
						end
						use.from:loseMark("@luajulian1")
					end
				end
				room:sortByActionOrder(use.to)
				data:setValue(use)
				return false
			end
		elseif event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			if use.from and elliy:objectName() == use.from:objectName() and use.card:isKindOf("Slash") and elliy:hasSkill("luajulian")
					and elliy:getMark("@luajulian3") > 0 then
				for _, t in sgs.qlist(use.to) do
					local jink_table = sgs.QList2Table(elliy:getTag("Jink_" .. use.card:toString()):toIntList())
					local index = 1
					for _, p in sgs.qlist(use.to) do
						jink_table[index] = 0
						index = index + 1
					end
					local jink_data = sgs.QVariant()
					jink_data:setValue(Table2IntList(jink_table))
					elliy:setTag("Jink_" .. use.card:toString(), jink_data)
				end
			end
		elseif event == sgs.TrickCardCanceling then
			local effect = data:toCardEffect()
			if effect.from and effect.from:hasSkill(self:objectName()) and effect.from:isAlive() and effect.from:getMark("@luajulian3") > 0
					and effect.to then
				return true
			end
		elseif event == sgs.CardFinished then
			local use = data:toCardUse()
			if use.card:isKindOf("SkillCard") then return false end
			elliy:loseAllMarks("@luajulian1")
			if elliy:getMark("@luajulian2") > 0 then
				if use.card:isKindOf("Slash") then
					room:addPlayerHistory(elliy, "Slash", 0)
				elseif use.card:isKindOf("yuzhi") then
					room:addPlayerHistory(elliy, "yuzhi", 0)
				elseif use.card:isKindOf("Analeptic") then
					room:addPlayerHistory(elliy, "Analeptic", 0)
				end
				elliy:loseAllMarks("@luajulian2")
			end
			elliy:loseAllMarks("@luajulian3")
			if elliy:getMark("@luajulian4") > 0 then
				local ids = sgs.IntList()
				local jink_table = {}
				for _, id in ipairs(julian_list) do
					if room:getCardPlace(id) == sgs.Player_DiscardPile
						and sgs.Sanguosha:getCard(id):getSuit() == sgs.Card_Club then
						ids:append(id)
						table.insert(jink_table, tostring(id))
					end
				end
				local dummy = sgs.Sanguosha:cloneCard("jink")
				dummy:addSubcards(ids)
				local targetX = room:askForPlayerChosen(elliy, room:getAlivePlayers(), "luajulian", "luajulian", true)
				targetX:obtainCard(dummy)
				elliy:loseAllMarks("@luajulian4")
				room:setPlayerFlag(elliy, "Global_PlayPhaseTerminated")
			end
		end
		return false
	end
}
luajulian3 = sgs.CreateTriggerSkill{
	name = "#luajulian3",
	global = true,
	events = {sgs.EventPhaseChanging, sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive and (#julian_list > 0) then
				julian_list = {}
			end
		elseif event == sgs.CardsMoveOneTime then
			local move = data:toMoveOneTime()
			if move.to_place == sgs.Player_DiscardPile then
				local elliy = room:findPlayerBySkillName("luajulian")
				if elliy and not elliy:hasFlag("luajulian") then
					for _, id in sgs.qlist(move.card_ids) do
						if not table.contains(julian_list, id) then
							table.insert(julian_list, id)
						end
					end
				end
			end
		end
	end
}
elliy:addSkill(luajulian)
elliy:addSkill(luajulian2)
elliy:addSkill(luajulian3)


luaxuezhan2 = sgs.CreateTriggerSkill{
	name = "#luaxuezhan2" ,
	events = {sgs.DamageCaused} ,
	global = true,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.DamageCaused then
			local damage = data:toDamage()
			if damage.to and damage.to:hasSkill("luaxuezhan") and damage.card and damage.card:isKindOf("Slash") then
				damage.damage = damage.damage + 1
			end
			if damage.to and damage.card and damage.card:isKindOf("Slash") and room:getCurrent():hasSkill("luaxuezhan")
				and room:getCurrent():getMark("xuezhanda") == room:getCurrent():getHp() then
				damage.damage = damage.damage + 1 
			end 
			data:setValue(damage)
		end
		return false
	end
}
luaxuezhan3 = sgs.CreateTriggerSkill{
	name = "#luaxuezhan3",
	events = {sgs.CardUsed, sgs.CardResponded},
	global = true,
    frequency = sgs.Skill_Frequent,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardUsed then
			local card = data:toCardUse().card
			if card:isKindOf("Jink") then
				for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
					room:setPlayerFlag(p, "grani")
				end 
			end 
			if card:isKindOf("Slash") then
				room:getCurrent():addMark("xuezhanda") 
			end 
        elseif event == sgs.CardResponded then
			local cd = data:toCardResponse().m_card
			if cd:isKindOf("Jink") then
				for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
					room:setPlayerFlag(p, "grani")
				end 
			end
		end
	end,
}
luaxuezhan4 = sgs.CreateTriggerSkill{
	name = "#luaxuezhan4" ,
	global = true,
	priority = 1,
	events = {sgs.TurnStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom() 
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			room:setPlayerMark(p, "xuezhanda", 0) 
		end 
		for _, toziko in sgs.qlist(room:findPlayersBySkillName("luaxuezhan")) do 
			if toziko and toziko:hasFlag("grani") then
				room:setPlayerFlag(toziko, "-grani")
			end
		end 
		return false
	end 
}
luaxuezhan = sgs.CreateTargetModSkill{
	name = "luaxuezhan",
	frequency = sgs.Skill_NotFrequent,
	pattern = "Slash",
	extra_target_func = function(self, from, card)
		if from:hasFlag("grani") then
			return 1
		else
			return 0
		end
	end
}


luatanxiCard = sgs.CreateSkillCard{
	name = "luatanxi" ,
	filter = function(self, targets, to_select)
		if #targets ~= 0 then return end 
		return true
	end ,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		room:showAllCards(effect.to)
		room:setPlayerFlag(effect.to, "slashTargetFix")
		room:setPlayerFlag(effect.to, "slashNoDistanceLimit")
		room:setPlayerFlag(effect.to, "slashTargetFixToOne")
		room:setPlayerFlag(effect.from, "SlashAssignee")
		if effect.to:canSlash(effect.from, nil, false) and room:askForUseSlashTo(effect.to, effect.from, "@luaqijinr", false) then
			room:setPlayerFlag(effect.to, "-slashTargetFix")
			room:setPlayerFlag(effect.to, "-slashNoDistanceLimit")
			room:setPlayerFlag(effect.to, "-slashTargetFixToOne")
			room:setPlayerFlag(effect.from, "-SlashAssignee")
		else
			room:showAllCards(effect.to)
			for _, card in sgs.list(effect.to:getHandcards()) do
				if card:isKindOf("Slash") then 
					room:obtainCard(effect.from, card, false)
				end 
			end 
		end 
	end 
}
luatanxiVS = sgs.CreateZeroCardViewAsSkill{
	name = "luatanxi",
	response_pattern = "@@luatanxi",
	view_as = function(self, cards)
		return luatanxiCard:clone()
	end
}
luatanxi = sgs.CreateTriggerSkill{
	name = "luatanxi" ,
	events = {sgs.EventPhaseChanging} ,
	priority = 1,
	view_as_skill = luatanxiVS, 
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		local room = player:getRoom()
		if change.to ~= sgs.Player_Start then return false end
		if room:askForUseCard(player, "@@luatanxi", "@luatanxi") then 
			
		end 
	end 
}

luajinzhongCard = sgs.CreateSkillCard{
	name = "luajinzhong",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		local choices = {"1"}
		local y = source:getMaxHp() - source:getHp()
		if y > 1 then 
			for i = 2, y do
				table.insert(choices, tostring(i)) 
			end  
		end 
		local recoverN = room:askForChoice(source, "luajinzhong", table.concat(choices, "+"))
		room:recover(source, sgs.RecoverStruct(source, nil, tonumber(recoverN)))
		
		if not source:hasSkill("luajuezhan") then room:acquireSkill(source, "luajuezhan") end  
		if not source:hasSkill("luajianshu") then room:acquireSkill(source, "luajianshu") end  
		room:setPlayerMark(source, "luajuezhanNextTurn", 1)
		room:setPlayerMark(source, "luajianshuNextTurn", 1)
		--if not source:hasSkill("luaqishu") then room:acquireSkill(source, "luaqishu") end 

	end
}
luajinzhongVS = sgs.CreateZeroCardViewAsSkill{
	name = "luajinzhong",
	view_as = function(self, cards)
		return luajinzhongCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:usedTimes("#luajinzhong") < 1 and player:isWounded()
	end
}
luajinzhong = sgs.CreateTriggerSkill{
	name = "luajinzhong",  
	view_as_skill = luajinzhongVS, 
	priority = 10,
	events = {sgs.TurnStart},
	on_trigger = function(self, event, player, data, room)
		local player = room:getCurrent()
		if player:hasSkill(self:objectName()) then 
			for _, skill in sgs.qlist(player:getVisibleSkillList()) do
				if player:getMark(skill:objectName() .. "NextTurn") > 0 then
					room:setPlayerMark(player, skill:objectName() .. "NextTurn", 0)
					room:detachSkillFromPlayer(player, skill:objectName())
				end 
			end
		end
		return false
	end
}
yumeko:addSkill(luaxuezhan)
yumeko:addSkill(luaxuezhan4)
yumeko:addSkill(luaxuezhan3)
yumeko:addSkill(luaxuezhan2)
yumeko:addSkill(luajinzhong)
yumeko:addSkill(luatanxi) 
yumeko:addRelateSkill("luajianshu")
sgs.LoadTranslationTable{
	["pay3"] = "求闻寻昔", --注意这里每次要加逗号
	["sp_aya"] = "sp射命丸文",
	["#sp_aya"]= "风神少女",
	["designer:sp_aya"] = "诺多战士",
	["luajisu"] = "急速",
	[":luajisu"] = "一名角色的回合结束时，若本回合没有任何角色的牌置入弃牌堆，你可以判定，若为锦囊牌，你执行一个额外回合。",
	["luafengxing"] = "风行",
	[":luafengxing"] = "每轮限一次，你的牌被弃置时，你可以将之全部置于牌堆顶，然后从场上选与这些牌中有相同花色的一张牌，移动之。",

	["kana"] = "卡娜·安娜贝拉尔",
	["#kana"]= "搬家少女",
	["designer:kana"] = "Paysage",
	["Lualubiao"] = "路标",
	["@lualubiao"] = "要么给卡娜这些牌中的一张，要么不能使用、打出或弃置此类型的牌，直到你的下个回合结束",
	[":Lualubiao"] = "出牌阶段限一次，你可以展示一张手牌，令一名其他角色选择一项（若你展示的是装备牌，则此时该角色获得其所有装备牌）：交给你与其同类型的一张牌，或是不能使用或打出与其同类型的牌直到其回合结束阶段。",
	["Luahuaimeng"] = "槐梦",
	[":Luahuaimeng"] = "每当你受到1点伤害后，你可以获得弃牌堆中的一张锦囊牌。",

	["mugetsu"] = "梦月&幻月",
	["#mugetsu"]= "最凶最恶的双子",
	["designer:mugetsu"] = "Paysage", --ゆっきょん
	["illustrator:mugetsu"] = "ゆっきょん",
	["luakuanggu"] = "狂骨",
	[":luakuanggu"] = "当你对一名角色造成1点伤害后，若你与其的距离于其因受到此伤害而扣减体力前不大于1，你可以选择一项：1.回复1点体力；2.摸一张牌。",

	["luashuangyue"] = "双月",
	[":luashuangyue"] = "锁定技，摸牌阶段，你少摸一张牌。每轮你的首个回合结束后，你执行一个额外的回合。",
	["luaxiongshi"] = "凶时",
	[":luaxiongshi"] = "锁定技，你的红色基本牌均视为【杀】。你使用【杀】指定目标后，你使用的下一张非装备牌可以额外选定一名角色成为此牌的目标。",

	["sarielelis"] = "萨丽艾尔&依莉斯",
	["#sarielelis"]= "最初的幻想",
	["designer:sarielelis"] = "Paysage",
	["lualunhui"] = "轮回",
	[":lualunhui"] = "锁定技，每当一名角色阵亡时，你须减少一点体力上限，并依次获得下列一项：1.获得【闭月】；2.获得【狂骨】【马术】；3.获得【重生】；4.获得【弹幕】【飞影】；5.获得一名已阵亡角色的全部技能。",
	["luazhongmo"] = "终末",
	[":luazhongmo"] = "主公技，锁定技，当前存活人数小于三时，当前轮数+20。",
	["luabiyue"] = "闭月",
	[":luabiyue"] = "结束阶段开始时，你可以摸X张牌。（若你没有手牌，X为2，否则X为1） ",


	["yumemi"] = "冈崎梦美",
	["yumemiA"] = "冈崎梦美",
	["yumemiB"] = "冈崎梦美",
	["yumemiC"] = "冈崎梦美",
	["#yumemi"] = "最强人类",
	["designer:yumemi"] = "Paysage",
	["luachaofan"] = "超凡",
	[":luachaofan"] = "准备阶段，你可以弃一张牌，之后直到你的下个准备阶段，每当你使用或打出非装备牌时：若其点数大于弃牌堆顶的牌，则你摸一张牌且此牌无距离限制；否则“超凡”无效。",
	["luashizi"] = "十字",
	[":luashizi"] = "每回合限一次，你可以将任意数量点数合计为10的牌当做点数为10的任一牌使用或打出。",

	["sagume"] = "稀神探女",
	["sagumeA"] = "稀神探女",
	["sagumeB"] = "稀神探女",
	["sagumeC"] = "稀神探女",
	["#sagume"] = "天探女",
	["designer:sagume"] = "Paysage",
	["luanizhou"] = "逆咒",
	["@luanizhou"] = "你可以发动“逆咒”",
	["~luanizhou"] = "选择一张手牌→点击确定",
	[":luanizhou"] = "准备阶段，你可以将一张牌置于牌堆顶并声明一个正整数X。直到你的下个回合开始前，每回合的第X+1张牌由非当前回合角色失去时：若是你友方角色，其摸X张牌；若是敌方角色，其选择弃X张牌或受到1点伤害。",
	["luashehuo"] = "舌祸",
	["luashehuo1"] = "修改其X值",
	["luashehuo2"] = "效果变成两倍",
	[":luashehuo"] = "每当你受到1点伤害后，若“逆咒”生效中，你可以修改其X值，或是令其效果变成两倍。",

	["lolice"] = "爱丽丝",
	["#lolice"]= "死之少女",
	["designer:lolice"] = "Paysage",
	["luajinshu"] = "禁书",
	[":luajinshu"] = "你使用或打出一张基本牌后，你获得一枚“术”标记。出牌阶段限两次，你可以弃置X-1枚“术”，视为你使用了任意一张多目标锦囊牌（X为存活角色数）。锁定技，你不能使用装备牌。",
	["luasishux"] = "死术",
	["Slash"] = "杀",
	["Jink"] = "闪",
	["Analeptic"] = "酒",
	["Peach"] = "桃",
	[":luasishux"] = "出牌阶段各限一次，你可以打出一张：①基本牌，对一名体力值为1的角色造成一点伤害；②装备牌，获得一张基本牌并对一名角色使用之（不计入限制）。",

	["satsuki"] = "冴月麟",
	["#satsuki"]= "阿卡林",
	["designer:satsuki"] = "Paysage",
	["luafenghua"] = "辞行",
	[":luafenghua"] = "每轮限一次，你可以于出牌阶段弃置两张同花色的牌，令一名其他角色于此回合结束时执行一个额外的回合，或是增加一点体力上限并回复一点体力。",
	["luaxueyue"] = "雪月",
	[":luaxueyue"] = "除准备阶段以外，你的任意阶段开始时，你可以判定，若此牌与你一张手牌花色相同，你流失一点体力。",
	["extraturn"] = "执行一个额外的回合",
	["luatiandu"] = "天妒",
	[":luatiandu"] = "在你的判定牌生效后，你可以获得此牌。",

	["chiyuri"] = "北百合千百合",
	["#chiyuri"]= "助教",
	["designer:chiyuri"] = "Paysage",
	["illustrator:chiyuri"] = "ワダンテ",
	["luazhujiao"] = "助教",
	[":luazhujiao"] = "转化技，出牌阶段，你可以：①弃置一张牌并摸两张牌；②弃置两张牌并摸一张牌。“助教”每转化一次，你手牌上限-1。",
	["luashikong"] = "时空",
	["@luashikong"] = "你可以发动“时空”",
	["~luashikong"] = "点击要使用牌的那个角色→确定",
	[":luashikong"] = "每回合限两次，你的手牌因打出或弃置进入弃牌堆后，你可以令一名角色使用其中的一张，且若这些牌点数合计为10，其获得之。",

	["jz_reimu"] = "博丽灵梦",
	["#jz_reimu"]= "乐园的巫女",
	["designer:jz_reimu"] = "Paysage",
	["luaboli"] = "博丽",
	["boli"] = "博丽",
	[":luaboli"] = "出牌阶段限一次，你可以将一张牌当作【杀】使用（无距离与次数限制），然后摸一张牌。",
	["luaquxie"] = "祛邪",
	["luaquxie1"] = "此【杀】不可被闪避",
	["luaquxie2"] = "弃置其中与此【杀】同花色的牌",
	[":luaquxie"] = "每回合限一次，你使用【杀】指定目标后，你可展示目标角色手牌，并选择一项：1.弃置其中与此【杀】同花色的牌 ；2.此【杀】不可被闪避。",
	["luaxiaohua"] = "消华",
	[":luaxiaohua"] = "锁定技，回合开始时，你减少一点体力上限。",

	["yukimai"] = "雪&舞",
	["#yukimai"]= "双生的魔法使",
	["designer:yukimai"] = "Paysage",
	["luashuangshen"] = "双生",
	[":luashuangshen"] = "锁定技，你的非基本牌置入弃牌堆后，若弃牌堆顶的第二张牌是基本牌，你获得之，且你于本回合使用牌无限制。",
	["luashushi"] = "术式",
	[":luashushi"] = "每轮你每第三次使用、打出或弃置（弃置未实装）基本牌结算后，你可以视为对一名角色使用了一张【弹幕】。",

	["ellen"] = "爱莲",
	["#ellen"]= "蓬松松魔法使",
	["designer:ellen"] = "Paysage",
	["lualianai"] = "恋爱",
	[":lualianai"] = "限定技，出牌阶段，你可以指定一名角色，之后的游戏中，你或其于自身出牌阶段限一次，可以弃置一张牌，视为对对方使用了一张【桃】。",
	["luamofa"] = "魔法",
	[":luamofa"] = "锁定技，你每当你令一名角色回复体力后，你令其获得“弹幕”或“术式”。",

	["elliy"] = "艾丽",
	["#elliy"]= "梦幻馆的门卫",
	["designer:elliy"] = "Paysage",
	["luajulian"] = "巨镰",
	[":luajulian"] = "出牌阶段，你可以弃一张牌，令你使用的下一张牌具有如下之一：1.无距离限制并可以额外指定一个目标；2.无次数限制；3.不能被响应；4.结算后，你令一名角色获得本回合置于弃牌堆的梅花牌并结束本阶段。",
	["luajulian1"] = "额外指定一个目标",
	["luajulian2"] = "无次数限制",
	["luajulian3"] = "不能被响应",
	["luajulian4"] = "获得本回合置于弃牌堆的梅花牌",

	["yumeko"] = "梦子",
	["#yumeko"] = "魔界天使",
	["designer:yumeko"] = "Paysage",
	["illustrator:yumeko"] = "JILL",
	["luaxuezhan"] = "血战",
	[":luaxuezhan"] = "锁定技，有角色使用过【闪】的回合，你使用【杀】可以额外指定一个目标。锁定技，【杀】对你造成的伤害+1，你回合内的第X张【杀】伤害+1（X为你体力值）",
	["luajinzhong"] = "尽忠",
	[":luajinzhong"] = "出牌阶段限一次，你可以回复至少一点体力，之后直到你的下回合前，你获得“决战”“剑戍”。",

}
return {extension_pay_c}