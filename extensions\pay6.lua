extension_pay_f = sgs.Package("pay6")

qinxin = sgs.General(extension_pay_f,"qinxin","qun",3,false,false,false)
kokoroA = sgs.General(extension_pay_f,"kokoroA","qun",3,false,true,true)
kokoroB = sgs.General(extension_pay_f,"kokoroB","qun",3,false,true,true)
kokoroC = sgs.General(extension_pay_f,"kokoroC","qun",3,false,true,true)
kokoroD = sgs.General(extension_pay_f,"kokoroD","qun",3,false,true,true)

nue = sgs.General(extension_pay_f,"nue","lualian",3,false, false, false)
nueA = sgs.General(extension_pay_f,"nueA","lualian",3,false, true, true)
toziko = sgs.General(extension_pay_f,"toziko","lualing",3,false,false,false)
tatara = sgs.General(extension_pay_f,"tatara","lualian",3,false,false,false)
futo = sgs.General(extension_pay_f,"futo","lualing",3,false,false,false)
futoA = sgs.General(extension_pay_f,"futoA","lualing",3,false,true,true)
futoB = sgs.General(extension_pay_f,"futoB","lualing",3,false,true,true)
futoC = sgs.General(extension_pay_f,"futoC","lualing",3,false,true,true)

syou = sgs.General(extension_pay_f,"syou","lualian",3,false,false,false)
yoshika = sgs.General(extension_pay_f,"yoshika","lualing",4,false,true,true)
sp_suwako = sgs.General(extension_pay_f,"sp_suwako$","luafeng",3,false,false,false)


kyouko = sgs.General(extension_pay_f,"kyouko","lualian",4,false,false,false)
toyosatomimi = sgs.General(extension_pay_f,"toyosatomimi$","lualing",3,false,false,false)
toyosatomimiA = sgs.General(extension_pay_f,"toyosatomimiA$","lualing",3,false,true,true)
toyosatomimiB = sgs.General(extension_pay_f,"toyosatomimiB$","lualing",3,false,true,true)
toyosatomimiC = sgs.General(extension_pay_f,"toyosatomimiC$","lualing",3,false,true,true)
toyosatomimiD = sgs.General(extension_pay_f,"toyosatomimiD$","lualing",3,false,true,true)
toyosatomimiE = sgs.General(extension_pay_f,"toyosatomimiE$","lualing",3,false,true,true)

sp_seiga = sgs.General(extension_pay_f,"sp_seiga","lualing",3,false,true,false)
seiga = sgs.General(extension_pay_f,"seiga","lualing",3,false,false,false)
rrandom = sgs.General(extension_pay_f,"rrandom","god",5,true,true,true)

seigaA = sgs.General(extension_pay_f,"seigaA","lualing",3,false,true,true)
seigaB = sgs.General(extension_pay_f,"seigaB","lualing",3,false,true,true)
seigaC = sgs.General(extension_pay_f,"seigaC","lualing",3,false,true,true)

hiziri = sgs.General(extension_pay_f,"hiziri","lualian",4,false,false,false)
hiziriA = sgs.General(extension_pay_f,"hiziriA","lualian",4,false,true,true)
hiziriB = sgs.General(extension_pay_f,"hiziriB","lualian",4,false,true,true)
hiziriC = sgs.General(extension_pay_f,"hiziriC","lualian",4,false,true,true)
hiziriD = sgs.General(extension_pay_f,"hiziriD","lualian",4,false,true,true)

ceshi = sgs.General(extension_pay_f,"ceshi","god",4,false,true,true)

local function deleteCardSafely(moveFrom, moveTo, player, room, id)
	if room:getCardPlace(id) ~= sgs.Player_PlaceUnknown then 
		local move2 = sgs.CardsMoveStruct()
		move2.from = room:getCardOwner(id)
		move2.from_place = room:getCardPlace(id)
		move2.to = moveTo
		move2.to_place = sgs.Player_PlaceTable
		move2.card_ids = sgs.IntList()
		move2.card_ids:append(id)
		move2.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_UNKNOWN, player:objectName())
		room:moveCardsAtomic(move2, true)

		local move3 = sgs.CardsMoveStruct()
		move3.from = moveTo
		move3.from_place = sgs.Player_PlaceTable
		move3.to = nil
		move3.to_place = sgs.Player_PlaceUnknown
		move3.card_ids = sgs.IntList()
		move3.card_ids:append(id)
		move3.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_UNKNOWN, player:objectName())
		room:moveCardsAtomic(move3, true)	
	end 
end 
local function damageFarthest(player, room) 
	local des = 0 
	local targets = sgs.SPlayerList()
	for _, p in sgs.qlist(room:getOtherPlayers(player)) do
		if player:inMyAttackRange(p) then
			targets:append(p)
		end
	end 
	if not targets:isEmpty() then 
		local to = room:askForPlayerChosen(player, targets, "lualouguan", "lualouguan-invoke", true, false)
		if to then
			room:damage(sgs.DamageStruct("lualouguan", player, to, 1, sgs.DamageStruct_Normal)) 
		end 
	end 
end 
lualouguan = sgs.CreateTriggerSkill{
	name = "lualouguan" ,
	events = {sgs.BeforeCardsMove, sgs.CardsMoveOneTime},
	global = true,
	priority = 9,
	on_trigger = function(self, event, player, data, room)
		local move = data:toMoveOneTime()  
		local function MoveToPlaceUnknown2DrawPile(id) --从虚空中获得
			local move = sgs.CardsMoveStruct()
			move.from = nil
			move.from_place = sgs.Player_PlaceUnknown
			move.to = nil
			move.to_place = sgs.Player_DrawPile
			move.card_ids = sgs.IntList()
			move.card_ids:append(id)
			move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_UNKNOWN, player:objectName())
			room:moveCardsAtomic(move, true)
		end 
		if event == sgs.BeforeCardsMove then
			if (move.to and move.to:objectName() == player:objectName() and move.to_place == sgs.Player_PlaceEquip) then 
				--[[
				for _, id in sgs.qlist(move.card_ids) do
					if sgs.Sanguosha:getCard(id):isKindOf("Roukanken") then
						room:writeToConsole("***** lualouguann ****** " .. sgs.Player_PlaceHand)
						if move.to then 
							room:writeToConsole("Roukanken move.to " .. move.to:objectName())
						end 
						if move.from then 
							room:writeToConsole("Roukanken move.from " .. move.from:objectName())
						end 
						if room:getCardOwner(id) then 
							room:writeToConsole("Roukanken room:getCardOwner(id) " .. room:getCardOwner(id):objectName())
						end 
						if move.to_place then 
							room:writeToConsole("Roukanken move.to_place " .. move.to_place)
						end 
						if room:getCardPlace(id) then 
							room:writeToConsole("Roukanken move.getCardPlace " .. room:getCardPlace(id))
						end 
						room:writeToConsole("***** lualouguann ****** " .. sgs.Player_PlaceEquip) 
					end
				end ]]
				local i = 0
				local old_card_ids = {}
				for _,card_idX in sgs.qlist(move.card_ids) do
					table.insert(old_card_ids, card_idX)
				end
				for _, id in ipairs(old_card_ids) do
					if sgs.Sanguosha:getCard(id):isKindOf("Roukanken") then 
						if room:getCardPlace(id) ~= sgs.Player_PlaceEquip then 
							local choice = room:askForChoice(player, "lualouguan", "weaponPay+armorPay")
							if choice == "armorPay" then 
								room:writeToConsole("Roukanken Move00")
								
								deleteCardSafely(move.from, move.to, player, room, id)
								move.card_ids:removeOne(id)
								move.from_places:removeAt(i) 

								room:writeToConsole("Roukanken Move001")
								MoveToPlaceUnknown2DrawPile(272)
								room:installEquip(player, sgs.Sanguosha:getCard(272):objectName()) 
							end 
						else 
							i = i + 1
						end
					elseif sgs.Sanguosha:getCard(id):isKindOf("Weapon") and not sgs.Sanguosha:getCard(id):isKindOf("Roukanken") then 
						if player:getWeapon() ~= nil then 
							local weaponx = player:getWeapon()
							if weaponx:isKindOf("Roukanken") then
								room:writeToConsole("Roukanken Weapon Changing")
								player:setFlags("RoukankenFlag")
							end 
						end
					elseif sgs.Sanguosha:getCard(id):isKindOf("Armor") and not sgs.Sanguosha:getCard(id):isKindOf("Roukankenx") then  
						if player:getArmor() == nil then  
							if player:hasFlag("RoukankenArFlag") then 
								deleteCardSafely(player, player, player, room, 271)
								room:writeToConsole("Roukankenx Armor Changing")
								MoveToPlaceUnknown2DrawPile(271)
								local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
								dummy:addSubcard(271)
								room:obtainCard(player, dummy, true) 
								damageFarthest(player, room)
								--player:setFlags("RoukankenXXFlag")
							end 
						end
					end 
				end	
				data:setValue(move)
				return false
			elseif (move.from and move.from:objectName() == player:objectName() and not player:hasFlag("RoukankenArFlag")) then 
				local i = 0
				local old_card_ids = {}
				for _,card_idX in sgs.qlist(move.card_ids) do
					table.insert(old_card_ids, card_idX)
				end
				for _, id in ipairs(old_card_ids) do
					if sgs.Sanguosha:getCard(id):isKindOf("Roukankenx") then 
						if move.to then 
							room:writeToConsole("Roukankenx Armor Move00 " .. move.to:getGeneralName())
							room:writeToConsole("Roukankenx Armor Move00 " .. move.to_place)
							room:writeToConsole("Roukankenx Armor Move00 " .. sgs.Player_PlaceHand)
						else
							room:writeToConsole("Roukankenx Armor Move00 ") 
						end 
						player:setFlags("RoukankenArFlag") 
						deleteCardSafely(move.from, move.from, player, room, id)

						move.card_ids:removeOne(id)
						move.from_places:removeAt(i) 
					end
				end 
				if player:hasFlag("RoukankenArFlag") then 
					if move.to_place == sgs.Player_DiscardPile or move.to_place == sgs.Player_DrawPile then 
						local moveX = sgs.CardsMoveStruct()
						moveX.from = nil
						moveX.from_place = sgs.Player_PlaceUnknown
						moveX.to = nil
						moveX.to_place = move.to_place
						moveX.card_ids = sgs.IntList()
						moveX.card_ids:append(271)
						moveX.reason = move.reason
						room:moveCardsAtomic(moveX, true)
					elseif move.to_place == sgs.Player_PlaceHand and move.to then 
						local _moveto
						for _, p in sgs.qlist(room:getAlivePlayers()) do
							if move.to:objectName() == p:objectName() then
								_moveto = p
								break
							end
						end
						MoveToPlaceUnknown2DrawPile(271)
						room:writeToConsole("Roukankenx Armor Move Success " .. move.to:getGeneralName())
						local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
						dummy:addSubcard(271)
						room:obtainCard(_moveto, dummy, true) 
					elseif move.to_place == sgs.Player_PlaceEquip and move.to then 
						MoveToPlaceUnknown2DrawPile(271)
						local _moveto
						for _, p in sgs.qlist(room:getAlivePlayers()) do
							if move.to:objectName() == p:objectName() then
								_moveto = p
								break
							end
						end
						room:writeToConsole("Roukankenx Armor Move Success " .. move.to:getGeneralName())
						room:installEquip(_moveto, sgs.Sanguosha:getCard(271):objectName())  
					end 
				end 
				data:setValue(move)
				return false
			end 		
		elseif event == sgs.CardsMoveOneTime then
			if (move.from and move.from:objectName() == player:objectName()) then 
				local i = 0
				local old_card_ids = {}
				for _,card_idX in sgs.qlist(move.card_ids) do
					table.insert(old_card_ids, card_idX)
				end
				local xiugai = false
				local duel = sgs.Sanguosha:cloneCard("duel", sgs.Card_NoSuit, 0)
				for _, id in ipairs(old_card_ids) do
					if sgs.Sanguosha:getCard(id):isKindOf("Roukanken") and move.from_places:at(i) == sgs.Player_PlaceEquip
						and player:hasFlag("RoukankenFlag") then  
							xiugai = true 
							duel:addSubcard(id) 
							player:setFlags("-RoukankenFlag")
					else
						i = i + 1
					end 
				end 
				if xiugai then 
					player:obtainCard(duel)
					damageFarthest(player, room)
				end 
				duel:deleteLater()
			end 
			return false	
		end 
	end
}
ceshi:addSkill(lualouguan)

luachunlei = sgs.CreateFilterSkill{
	name = "luachunlei",	
	view_filter = function(self, to_select)
		local room = sgs.Sanguosha:currentRoom()
		local place = room:getCardPlace(to_select:getEffectiveId())
		return (to_select:getSuit() == sgs.Card_Spade) and (place == sgs.Player_PlaceHand)
	end,	
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("peach", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		local _card = sgs.Sanguosha:getWrappedCard(card:getId())
		_card:takeOver(slash)
		_card:setModified(true)
		return _card
	end
}
ceshi:addSkill(luachunlei)

luajiutiaozhen = sgs.CreateTriggerSkill{
	name = "#luajiutiaozhen",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.ConfirmDamage, sgs.TurnStart},
	priority = 10,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()		
		--[[
		if event == sgs.ConfirmDamage then
            local damage = data:toDamage()
			if damage.from and damage.from:isAlive() and damage.card --and damage.from:objectName() == player:objectName()
				and damage.from:getMark("@damageup") > 0 and damage.from:getMark("jiutiaozhen") > 0 then
				local slash = damage.card
				if (slash and slash:isKindOf("Slash")) and not damage.chain and not damage.transfer then 
					room:writeToConsole("luajiutiaozhen0")
					if not slash:hasFlag("drank") then
						room:writeToConsole("luajiutiaozhen2")
						damage.damage = damage.damage + player:getMark("jiutiaozhen")
						data:setValue(damage)
					end 
				end 
			end 
		else]]--
		if event == sgs.TurnStart then
			local kp = room:getCurrent()
			if room:getCurrent():objectName() == player:objectName() then
				if kp:getMark("@damageup") > 0 and kp:getMark("jiutiaozhen") > 0 then
					room:writeToConsole("luajiutiaozhen0")
					room:setPlayerMark(kp, "@damageup", kp:getMark("@damageup") - kp:getMark("jiutiaozhen"))
					room:setPlayerMark(kp, "jiutiaozhen", 0)  
					room:setPlayerMark(kp, "drank", 0) 
				end 
			end 
		end 
	end 
}
luajiutiaozhen2 = sgs.CreateTriggerSkill{
	name = "#luajiutiaozhen2",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardUsed, sgs.EventPhaseChanging},
	priority = -1,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()		
		if event == sgs.CardUsed then
			local use = data:toCardUse()
			for _, t in sgs.qlist(use.to) do
				if t and t:isAlive() and use.card then 
					if use.card:isKindOf("Analeptic") and t:getMark("drank") > 0  then
						room:writeToConsole("luajiutiaozhen1")
						room:setPlayerMark(t, "@damageup", t:getMark("@damageup") + 1)
						room:setPlayerMark(t, "jiutiaozhen", t:getMark("jiutiaozhen") + 1)					
					end 
				end 
			end  		
		elseif event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if p:getMark("@damageup") > 0 and p:getMark("jiutiaozhen") > 0 then
						room:setEmotion(p, "analeptic")
						room:addPlayerMark(p, "drank")
					end 
				end  
			end 
		end 
	end 
}

luajiutiaozhen3 = sgs.CreateTriggerSkill{
	name = "#luajiutiaozhen3",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardUsed}, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()		
		if event == sgs.CardUsed then
			local use = data:toCardUse() 
			if use.from and use.from:isAlive() and use.from:objectName() == player:objectName() and use.card then  
				if use.card:isKindOf("Slash") and player:getMark("@damageup") > 0 and player:getMark("jiutiaozhen") > 0 then 
					room:writeToConsole("luajiutiaozhen3")
					room:setPlayerMark(player, "@damageup", player:getMark("@damageup") - player:getMark("jiutiaozhen"))
					room:setPlayerMark(player, "jiutiaozhen", 0)
				end 
			end  		
		end 
	end 
}
ceshi:addSkill(luajiutiaozhen)
ceshi:addSkill(luajiutiaozhen2)
ceshi:addSkill(luajiutiaozhen3)



lualuoying = sgs.CreateTriggerSkill{
	name = "lualuoying",
	frequency = sgs.Skill_Frequent,
	events = {sgs.BeforeCardsMove},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local move = data:toMoveOneTime()
		if move.from == nil or move.from:objectName() == player:objectName() then return false end
		if move.to_place == sgs.Player_DiscardPile and (bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) == sgs.CardMoveReason_S_REASON_DISCARD or move.reason.m_reason == sgs.CardMoveReason_S_REASON_JUDGEDONE) then
			local card_ids = sgs.IntList()
			local i = 0
			for _, card_id in sgs.qlist(move.card_ids) do
				if sgs.Sanguosha:getCard(card_id):getSuit() == sgs.Card_Club and ((move.reason.m_reason == sgs.CardMoveReason_S_REASON_JUDGEDONE and move.from_places:at(i) == sgs.Player_PlaceJudge and move.to_place == sgs.Player_DiscardPile) or (move.reason.m_reason ~= sgs.CardMoveReason_S_REASON_JUDGEDONE and room:getCardOwner(card_id):objectName() == move.from:objectName() and (move.from_places:at(i) == sgs.Player_PlaceHand or move.from_places:at(i) == sgs.Player_PlaceEquip))) then
					card_ids:append(card_id)
				end
				i = i + 1
			end
			if card_ids:isEmpty() then
				return false
			else
				if player:askForSkillInvoke(self:objectName(), data) then
					while not card_ids:isEmpty() do
						room:fillAG(card_ids, player)
						local id = room:askForAG(player, card_ids, true, self:objectName())
						if id == -1 then
							room:clearAG(player)
							break
						end
						card_ids:removeOne(id)
						room:clearAG(player)
					end
					if not card_ids:isEmpty() then
						move:removeCardIds(card_ids)
						data:setValue(move)
						local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
						dummy:addSubcards(card_ids)
						room:moveCardTo(dummy, player, sgs.Player_PlaceHand, move.reason, true)
					end
				end
			end
		end
		return false
	end
}
ceshi:addSkill(lualuoying)
 
luashouyejCard = sgs.CreateSkillCard{
	name = "luashouyej",
	will_throw = false,
	handling_method = sgs.Card_MethodUse,
	filter = function(self, targets, to_select)
		local ID = -1
		if sgs.Self:hasSkill("luashouyek") then 
			ID = sgs.Self:getMark("luashouyekID") - 1
		else
			for _, p in sgs.qlist(sgs.Self:getAliveSiblings()) do
				if p:hasSkill("luashouyek") then
					if p and p:isAlive() and p:getMark("luashouyekID") > 0 then
						ID = p:getMark("luashouyekID") - 1
					end 
				end
			end 
		end 
		if ID < 0 then return false end 
		local card = ID
		card = sgs.Sanguosha:getCard(card)
		card:setSkillName(self:objectName())
		card:addSubcards(self:getSubcards())
		if card and (card:isKindOf("Jink") or card:isKindOf("sakura") or card:isKindOf("Nullification")) then
			return false
		end
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		if card:targetFixed() then
			return #targets == 0 and card and card:targetFilter(qtargets, sgs.Self, sgs.Self) and not sgs.Self:isProhibited(sgs.Self, card, qtargets)
		end 
		return card and card:targetFilter(qtargets, to_select, sgs.Self) and not sgs.Self:isProhibited(to_select, card, qtargets)
	end,
	feasible = function(self, targets)
		local ID = -1
		if sgs.Self:hasSkill("luashouyek") then 
			ID = sgs.Self:getMark("luashouyekID") - 1
		else
			for _, p in sgs.qlist(sgs.Self:getAliveSiblings()) do
				if p:hasSkill("luashouyek") then
					if p and p:isAlive() and p:getMark("luashouyekID") > 0 then
						ID = p:getMark("luashouyekID") - 1
					end 
				end
			end 
		end 
		if ID < 0 then return false end 
		local card = ID
		card = sgs.Sanguosha:getCard(card)
		card:setSkillName(self:objectName()) 
		card:addSubcards(self:getSubcards())
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		if card and (card:isKindOf("Jink") or card:isKindOf("sakura") or card:isKindOf("Nullification")) then
			return false
		end 
		if card:targetFixed() then
			return #targets == 0 and card and card:targetsFeasible(qtargets, sgs.Self)
		end 
		return card and card:targetsFeasible(qtargets, sgs.Self)
	end,
	on_validate = function(self, card_use)
		local ID = -1
		local keine
		if card_use.from:hasSkill("luashouyek") then 
			ID = card_use.from:getMark("luashouyekID") - 1
			keine = card_use.from
		else		
			for _, p in sgs.qlist(card_use.from:getRoom():getAlivePlayers()) do
				if p:hasSkill("luashouyek") then
					if p and p:isAlive() and p:getMark("luashouyekID") > 0 then
						ID = p:getMark("luashouyekID") - 1
						keine = p
					end 
				end
			end 
		end 
		if ID < 0 then return false end 
		local card = ID
		 
		card = sgs.Sanguosha:getCard(card)
		local use_card = sgs.Sanguosha:cloneCard(card:objectName())
		use_card:addSubcards(self:getSubcards())
		use_card:setSkillName(self:objectName())
		local available = true
		for _,p in sgs.qlist(card_use.to) do
			if card_use.from:isProhibited(p,use_card) then
				available = false
				break
			end
		end
		available = available and use_card:isAvailable(card_use.from)
		if not available then
			local dummy = sgs.Sanguosha:cloneCard("jink") 
			dummy:addSubcards(self:getSubcards())
			card_use.from:getRoom():throwCard(dummy, nil, card_use.from)
			return nil
		end
		if available then
			card_use.from:getRoom():addPlayerHistory(card_use.from, "luashouyej") 
			card_use.from:getRoom():setPlayerMark(keine, "luatouchui", math.max(keine:getMark("luatouchui") - 1, 0))
			if (not card_use.from:hasFlag("luayuetuan")) and use_card:isKindOf("Slash") then card_use.from:getRoom():setPlayerFlag(card_use.from, "yuechongslash") end 
			keine:drawCards(1)
			return use_card
		end
	end,
}

luashouyej = sgs.CreateOneCardViewAsSkill{
	name = "luashouyej&",
	--filter_pattern = ".|.|.|hand",
	view_filter = function(self, card) 
    	return true
	end,
	view_as = function(self, originalCard)
		local caihuo_card = luashouyejCard:clone()
		caihuo_card:addSubcard(originalCard:getId())
		return caihuo_card
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#luashouyej")
	end, 
	enabled_at_response = function(self, player, pattern)
		return false
	end
}

ceshi:addSkill(luashouyej)

luayunsong2 = sgs.CreateTriggerSkill{
	name = "#luayunsong2", 
	events = {sgs.Deathed},  
	global = true,
	on_trigger = function(self, triggerEvent, player, data)
		local room = player:getRoom()
		if triggerEvent == sgs.Deathed then  
			local death = data:toDeath()
			if death.who:hasSkill("luayunsong") then 
				local lord = room:getLord()
				local uaqiuwen = lord:getTag("luamuling"):toString() 
				uaqiuwen = uaqiuwen:split("|")
				local range_list = sgs.IntList()
				for _, id in ipairs(uaqiuwen) do
					range_list:append(id)
				end 
				if range_list:length() > 0 and room:askForSkillInvoke(death.who, "luayunsong") then

					room:revivePlayer(death.who)
					death.who:setHp(death.who:getMaxHp())
					room:fillAG(range_list)
					local card_id = room:askForAG(death.who, range_list, false, "luayunsong")  
					if card_id ~= -1 then
					
						local card_ids2 = sgs.IntList()
						card_ids2:append(card_id)
						move_to = sgs.CardsMoveStruct()
						move_to.from = nil
						move_to.from_place = sgs.Player_PlaceUnknown
						move_to.to = nil
						move_to.to_place = sgs.Player_DrawPile
						move_to.card_ids = card_ids2
						move_to.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_REMOVE_FROM_PILE, player:objectName()) 
						room:moveCardsAtomic(move_to, false)
						--[[for _, Yaezaki_An in sgs.qlist(room:getAllPlayers(true)) do 
							if Yaezaki_An:getGeneralName() == "Yaezaki_An" then 						
								local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
								dummy_0:addSubcard(card_id) 
								Yaezaki_An:obtainCard(dummy_0)
								dummy_0:deleteLater()
								break
							end 
						end ]]--
						range_list:removeOne(card_id)
					end 
					room:clearAG()
					
					if range_list:length() == 0 then
						lord:removeTag("luamuling")
					else 
						anxbbb = {}
						for _, id in sgs.qlist(range_list) do
							table.insert(anxbbb, id)
						end  
						lord:setTag("luamuling", sgs.QVariant(table.concat(anxbbb, "|"))) 
					end  
					death.who:drawCards(1)
					death.who:drawCards(1)
				end 
			end  
		end
	end
}
 
luajianju2 = sgs.CreateTriggerSkill{
	name = "#luajianju2", 
	global = true, 
	events = {sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:getTag("FirstRound"):toBool() then return end
		local move = data:toMoveOneTime() 
		if move.from and move.from:hasSkill("luajianju") and move.to_place == sgs.Player_DiscardPile and player:isLord() then
			local Subcards = sgs.IntList()
			for _, id in sgs.qlist(move.card_ids) do
				Subcards:append(id)
			end 
			local HeartC = 0
			local DiamondC = 0
			local ClubC = 0
			local SpadeC = 0
			for _, id in sgs.qlist(Subcards) do
				local cardX = sgs.Sanguosha:getCard(id)
				if cardX:getSuit() == sgs.Card_Heart then
					HeartC = HeartC + 1
				end 
				if cardX:getSuit() == sgs.Card_Diamond then
					DiamondC = DiamondC + 1
				end 
				if cardX:getSuit() == sgs.Card_Club then
					ClubC = ClubC + 1
				end 
				if cardX:getSuit() == sgs.Card_Spade then
					SpadeC = SpadeC + 1
				end 
			end
			local AAA = math.floor(HeartC / 2) + math.floor(DiamondC / 2) + math.floor(ClubC / 2) + math.floor(SpadeC / 2)
			for _, Yaezaki_An in sgs.qlist(room:getAllPlayers(true)) do 
				if Yaezaki_An:getGeneralName() == "Yaezaki_An" then 
					for i = 1, AAA do
						local to = room:askForPlayerChosen(Yaezaki_An, room:getAlivePlayers(), "luajianju", "luajianju-invoke", true, true)
						local to1 = room:askForPlayerChosen(Yaezaki_An, room:getOtherPlayers(to), "luajianju", "luajianju-invokee", true, true)
						if to and not to:isNude() then
							local to_throw = room:askForCardChosen(Yaezaki_An, to, "he", self:objectName(), false, sgs.Card_MethodDiscard)
							room:throwCard(sgs.Sanguosha:getCard(to_throw), to, Yaezaki_An)
						end 
						if to1 then
							room:damage(sgs.DamageStruct(self:objectName(), Yaezaki_An, to1))
						end 
					end 
				end 
			end 
		end 
	end
}
ceshi:addSkill(luayunsong2)
ceshi:addSkill(luajianju2)


local function isFriendQ(room, target, player)
	if player:getRole() == "rebel" then
		return target:getRole() == "rebel"
	end
	if player:getRole() == "loyalist" or player:getRole() == "lord" then
		return target:getRole() == "loyalist" or target:getRole() == "lord"
	end
end

local function gettorisumi(player)
	local torisumi
	for _, p in sgs.qlist(sgs.Self:getSiblings()) do
		if p:hasSkill("luahongyi") and p:isAlive() then
			torisumi = p
		end
	end
	if not torisumi and player:hasSkill("luahongyi") then torisumi = player end
	return torisumi
end
luahongyia = sgs.CreateOneCardViewAsSkill{
	name = "luahongyia&",
	view_filter = function(self, card)
		local torisumi = gettorisumi(sgs.Self)
		if not torisumi then return false end
		if card:isEquipped() then return false end
		local usereason = sgs.Sanguosha:getCurrentCardUseReason()
		if usereason == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			if (torisumi:getMark("@hongyired") > 0) or ((torisumi:getMark("@hongyired") == 0) and (torisumi:getMark("@hongyiblack") == 0)) then return card:isRed() end
			if (torisumi:getMark("@hongyiblack") > 0) then return card:isBlack() end
		elseif (usereason == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE) or (usereason == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE) then
			local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
			if pattern == "slash" and (torisumi:getMark("@hongyired") > 0) or ((torisumi:getMark("@hongyired") == 0) and (torisumi:getMark("@hongyiblack") == 0)) then
				return card:isRed()
			elseif pattern == "jink" and (torisumi:getMark("@hongyiblack") > 0) then
				return card:isBlack()
			end
		else
			return false
		end
	end ,
	view_as = function(self, card)
		local torisumi = gettorisumi(sgs.Self)
		--if not torisumi then return nil end
		local originalCard = card
		local usereason = sgs.Sanguosha:getCurrentCardUseReason()
		if usereason == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			if (torisumi:getMark("@hongyired") > 0) or ((torisumi:getMark("@hongyired") == 0) and (torisumi:getMark("@hongyiblack") == 0)) then
				local slash = sgs.Sanguosha:cloneCard("slash", originalCard:getSuit(), originalCard:getNumber())
				slash:addSubcard(originalCard)
				slash:setSkillName(self:objectName())
				return slash
			end
		elseif (sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE) or
				(sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE) then
			if ((torisumi:getMark("@hongyired") > 0) or ((torisumi:getMark("@hongyired") == 0) and (torisumi:getMark("@hongyiblack") == 0))) then
				local slash = sgs.Sanguosha:cloneCard("slash", originalCard:getSuit(), originalCard:getNumber())
				slash:addSubcard(originalCard)
				slash:setSkillName(self:objectName())
				return slash
			elseif (torisumi:getMark("@hongyiblack") > 0) then
				local ncard = sgs.Sanguosha:cloneCard("jink", originalCard:getSuit(), originalCard:getNumber())
				ncard:addSubcard(originalCard)
				ncard:setSkillName(self:objectName()) --sgs.Self:getMark("Luameiyin")
				return ncard
			else
				return nil
			end
		end
	end ,
	enabled_at_play = function(self, target)
		local torisumi = gettorisumi(target)
		if not torisumi then return false end
		if not isFriendQ(nil, torisumi, target) then return false end
		if (sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE) or
				(sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE) then
			if (torisumi:getMark("@hongyired") > 0) or ((torisumi:getMark("@hongyired") == 0) and (torisumi:getMark("@hongyiblack") == 0)) then return sgs.Slash_IsAvailable(target) end
			return false
		end
		if (torisumi:getMark("@hongyiblack") > 0) then return false end
		return true
	end,
	enabled_at_response = function(self, target, pattern)
		local torisumi = gettorisumi(target)
		if not torisumi then return false end
		if not isFriendQ(nil, torisumi, target) then return false end
		if (pattern == "jink") and (torisumi:getMark("@hongyiblack") > 0) then return true end
		if (pattern == "slash")
				and ((torisumi:getMark("@hongyired") > 0) or ((torisumi:getMark("@hongyired") == 0) and (torisumi:getMark("@hongyiblack") == 0))) then return true end
		return false
	end,
	enabled_at_nullification = function(self, player)
		return false
	end
}
ceshi:addSkill(luahongyia)


luayingzi = sgs.CreateTriggerSkill{
	name = "luayingzi",
	frequency = sgs.Skill_Frequent,
	events = {sgs.DrawNCards},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:askForSkillInvoke(player, "luayingzi", data) then
			local count = data:toInt() + 1
			data:setValue(count)
		end
	end
}
ceshi:addSkill(luayingzi)
lualingbai3 = sgs.CreateFilterSkill{
	name = "lualingbai3",
	view_filter = function(self,to_select)
		local room = sgs.Sanguosha:currentRoom()
		local place = room:getCardPlace(to_select:getEffectiveId())
		return (place == sgs.Player_PlaceHand) and to_select:hasFlag("lingbai")
	end,
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("ex_nihilo", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		local _card = sgs.Sanguosha:getWrappedCard(card:getId())
		_card:takeOver(slash)
		_card:setModified(true)
		return _card
	end
}
ceshi:addSkill(lualingbai3)
luasuanshu3 = sgs.CreateFilterSkill{
	name = "luasuanshu3",
	view_filter = function(self,to_select)
		local room = sgs.Sanguosha:currentRoom()
		local place = room:getCardPlace(to_select:getEffectiveId())
		return (place == sgs.Player_PlaceHand)
	end,
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("nullification", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		local _card = sgs.Sanguosha:getWrappedCard(card:getId())
		_card:takeOver(slash)
		_card:setModified(true)
		return _card
	end
}
ceshi:addSkill(luasuanshu3)

luafenxing5 = sgs.CreateFilterSkill{
	name = "luafenxing5",
	view_filter = function(self,to_select)
		local room = sgs.Sanguosha:currentRoom()
		local place = room:getCardPlace(to_select:getEffectiveId())
		return (place == sgs.Player_PlaceHand)
	end,
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("peach", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		local _card = sgs.Sanguosha:getWrappedCard(card:getId())
		_card:takeOver(slash)
		_card:setModified(true)
		return _card
	end
}
ceshi:addSkill(luafenxing5)
lualiangxiao3 = sgs.CreateFilterSkill{
	name = "lualiangxiao3",
	view_filter = function(self,to_select)
		local room = sgs.Sanguosha:currentRoom()
		local place = room:getCardPlace(to_select:getEffectiveId())
		return (place == sgs.Player_PlaceHand) and to_select:hasFlag("lualiangxiao")
	end,
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("analeptic", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		local wrap = sgs.Sanguosha:getWrappedCard(card:getId())
		wrap:takeOver(slash)
		wrap:setModified(true)
		return wrap
	end
}
ceshi:addSkill(lualiangxiao3)

luajuezhan2 = sgs.CreateTriggerSkill{
	name = "#luajuezhan2" ,
	events = {sgs.Damage, sgs.TurnStart} ,
	global = true,
	on_trigger = function(self, event, player, data) 
		local room = player:getRoom()
		if event == sgs.TurnStart then 
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				p:getRoom():setPlayerFlag(p, "-luajuezhani")
			end
		else
			local damage = data:toDamage()
			if damage.from and damage.from:hasSkill("luajuezhan") then
				damage.from:getRoom():setPlayerFlag(damage.from, "luajuezhani")
			end

		end 
		return false
	end,
}
ceshi:addSkill(luajuezhan2)


luabiyue = sgs.CreateTriggerSkill{
	name = "luabiyue",
	frequency = sgs.Skill_Frequent,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		if player:getPhase() == sgs.Player_Finish then
			local room = player:getRoom()
			if room:askForSkillInvoke(player, self:objectName()) then
				if player:isKongcheng() then 
					player:drawCards(2)
				else
					player:drawCards(1) 
				end 
			end
		end
	end
}
ceshi:addSkill(luabiyue)
luacuiruo = sgs.CreateTriggerSkill{
	name = "luacuiruo" ,
	global = true,
	frequency = sgs.Skill_Compulsory ,
	events = {sgs.SlashEffected, sgs.TargetConfirming} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.TargetConfirming then
			local use = data:toCardUse()
			if use.card and use.card:isKindOf("Slash") and use.from:hasSkill("luacuiruo") then
				use.from:setMark("luacuiruo", 0)
				local dataforai = sgs.QVariant()
				dataforai:setValue(use.to:at(0))
				if not use.from:getRoom():askForCard(use.from,".Basic","@xiangle-discard", dataforai) then
					use.from:addMark("luacuiruo")
				end
			end
		else
			local effect = data:toSlashEffect()
			if effect.from:getMark("luacuiruo") > 0 then
				effect.from:removeMark("luacuiruo")
				return true
			end
		end
	end
}
luacuiruo2 = sgs.CreateTriggerSkill{
	name = "#luacuiruo" ,
	global = true,
	frequency = sgs.Skill_Compulsory ,
	events = {sgs.TargetConfirmed} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			for _, t in sgs.qlist(use.to) do
				local i = 1
				if use.card:isKindOf("Slash") and player and player:isAlive() and use.from
						and player:hasSkill("luacuiruo") and (t:objectName() == player:objectName()) then
					local jink_table = sgs.QList2Table(use.from:getTag("Jink_" .. use.card:toString()):toIntList())
					if jink_table[i] == 1 then
						jink_table[i] = 2 --只要设置出两张闪就可以了，不用两次askForCard
					end
					local jink_data = sgs.QVariant()
					jink_data:setValue(Table2IntList(jink_table))
					use.from:setTag("Jink_" .. use.card:toString(), jink_data)
				end
				i = i + 1
			end
		end
	end
}
luacuiruo3 = sgs.CreateTriggerSkill{
	name = "#luacuiruo2" ,
	global = true,
	frequency = sgs.Skill_Compulsory ,
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local change = data:toPhaseChange()
		if change.to == sgs.Player_NotActive and player:hasSkill("luacuiruo") and player:objectName() == room:getCurrent():objectName() then
			room:handleAcquireDetachSkills(player, "-luacuiruo")
		end
	end
}
ceshi:addSkill(luacuiruo)
ceshi:addSkill(luacuiruo2)
ceshi:addSkill(luacuiruo3)
luahezour = sgs.CreateFilterSkill{
	name = "luahezour" ,
	view_filter = function(self, card)
		return card:hasFlag("luahezou")
	end ,
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("yuzhi", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		local wrap = sgs.Sanguosha:getWrappedCard(card:getId())
		wrap:takeOver(slash)
		wrap:setModified(true)
		return wrap
	end
}
ceshi:addSkill(luahezour)

quanjuliuxue = sgs.CreateTriggerSkill{
	name = "#quanjuliuxue",
	global = true,
	events = {sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data, room)
		local change = data:toPhaseChange()
		if change.to ~= sgs.Player_Start then return false end
		if room:getCurrent():isLord() then
			local count = 0
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:getState() ~= "robot" then
					count = count + 1
				end
			end
			if count == 1 then
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if p:getState() ~= "robot" then
						if (string.find(p:screenName(), "百乡草")) then
							return false
						end
					end
				end
			end
			local xo = room:getLord():getMark("@clock_time") + 1
			if xo > 10 and xo ~= room:getLord():getMark("quanjuliuxue") then
				room:setPlayerMark(room:getLord(), "quanjuliuxue", xo)
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					local function canLoseHp()
						for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
							if hecatiaX and isFriendQ(room, p, hecatiaX) and p:objectName() ~= hecatiaX:objectName()
									and p:getHp() == hecatiaX:getHp() then
								room:notifySkillInvoked(hecatiaX, "luayiti")
								return false
							end 
						end 
						for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
							if Erin and Erin:getKingdom() == p:getKingdom() then
								room:notifySkillInvoked(Erin, "luajiance")
								return false
							end 
						end 
						return true
					end 
					if canLoseHp() then room:loseHp(p) end 
				end
			end
		end
	end
}

ceshi:addSkill(quanjuliuxue)

luawanbangrCard = sgs.CreateSkillCard{
	name = "luawanbangr",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		local mio = room:findPlayerBySkillName("luawanbang")
		if mio and mio:isAlive() then
			mio:drawCards(1)
		end
	end
}
luawanbangr = sgs.CreateOneCardViewAsSkill{
	name = "luawanbangr&",
	view_filter = function(self, to_select)
		return true
	end,
	view_as = function(self, card)
		local skillcard = luawanbangrCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self,player)
		local hasXL = false
		for _, p in sgs.qlist(sgs.Self:getSiblings()) do
			if p:hasLordSkill("luawanbang") and p:isAlive() then
				hasXL = true
			end
		end
		return not player:hasUsed("#luawanbangr") and hasXL
	end,
}
ceshi:addSkill(luawanbangr)

luaxianglinCard = sgs.CreateSkillCard{
	name = "luaxianglinr",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		local mio = room:findPlayerBySkillName("luaxianglin")
		if mio and mio:isAlive() and mio:isWounded() then
			room:recover(mio, sgs.RecoverStruct(source, nil, 1))
		end
	end
}
luaxianglinr = sgs.CreateOneCardViewAsSkill{
	name = "luaxianglinr&",
	view_filter = function(self, to_select)
		return to_select:isKindOf("EquipCard")
	end,
	view_as = function(self, card)
		local skillcard = luaxianglinCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self,player)
		local hasXL = false
		for _, p in sgs.qlist(sgs.Self:getSiblings()) do
			if p:hasSkill("luaxianglin") and p:isWounded() and p:isAlive() then
				hasXL = true
			end
		end
		return not player:hasUsed("#luaxianglinr") and hasXL
	end,
}
ceshi:addSkill(luaxianglinr)

luatianzhaorCard = sgs.CreateSkillCard{
	name = "luatianzhaor" ,
	target_fixed = true ,
	will_throw = false,
	on_use = function(self, room, source, targets)
		local mio = room:findPlayerBySkillName("luatianzhao")
		if mio and mio:isAlive() then
			source:addMark("luatianzhaor")
		end
	end
}
luatianzhaor = sgs.CreateZeroCardViewAsSkill{
	name = "luatianzhaor&",
	view_as = function(self, cards)
		return luatianzhaorCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("luatianzhaor") < 1 and not player:hasUsed("#luatianzhaor")
	end
}
ceshi:addSkill(luatianzhaor)

luabasicrule1 = sgs.CreateTriggerSkill{
	name = "#luabasicrule1" ,
	global = true,
	priority = 99,
	events = {sgs.BeforeCardsMove} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom() 
		if player:objectName() == room:getCurrent():objectName() then 
			local move = data:toMoveOneTime()
			for _,card_id in sgs.qlist(move.card_ids) do
				local cardX = sgs.Sanguosha:getCard(card_id)
				cardX:setFlags("basicrule1")
			end  
		end 
	end 
}

luabasicrule2 = sgs.CreateTriggerSkill{
	name = "#luabasicrule2" ,
	global = true,
	priority = -1,
	events = {sgs.CardsMoveOneTime} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:objectName() == room:getCurrent():objectName() then 
			local move = data:toMoveOneTime()
			for _,card_id in sgs.qlist(move.card_ids) do
				local cardX = sgs.Sanguosha:getCard(card_id)
				if cardX:hasFlag("basicrule1") then  
					cardX:setFlags("-basicrule1") 
				end 
			end 
		end 
	end 
}
ceshi:addSkill(luabasicrule1)
ceshi:addSkill(luabasicrule2)

fuzhengCard = sgs.CreateSkillCard{
	name = "luafuzhengr" ,
	target_fixed = true ,
	will_throw = false,
	on_use = function(self, room, source, targets)
		local mio = room:findPlayerBySkillName("luafuzheng")
		if mio and mio:isAlive() then
			source:drawCards(1)
			mio:gainMark("@luafuzheng")
			room:askForDiscard(source, self:objectName(), 1, 1, false, true)
		end
	end
}
luafuzhengr = sgs.CreateZeroCardViewAsSkill{
	name = "luafuzhengr&",
	view_as = function(self)
		return fuzhengCard:clone()
	end,
	enabled_at_play = function(self,player)
		local hasXL = false
		for _, p in sgs.qlist(sgs.Self:getSiblings()) do
			if p:hasSkill("luafuzheng") and p:isAlive() then
				hasXL = true
			end
		end
		if player:hasSkill("luafuzheng") and player:isAlive() then hasXL = true end
		return not player:hasUsed("#luafuzhengr") and hasXL
	end
}
ceshi:addSkill(luafuzhengr)
luajisiCard = sgs.CreateSkillCard{
	name = "luajisir",
	target_fixed = true,
	handling_method = sgs.Card_MethodNone,
	on_use = function(self, room, source, targets)
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			if p:hasSkill("luajisi") then
				local x = math.abs(source:getHp() - p:getHp())
				p:obtainCard(self, true)
				local card_ids = room:getNCards(x)
				local y = 0 - sgs.Sanguosha:getCard(self:getSubcards():at(0)):getNumber()
				for _, id in sgs.qlist(card_ids) do
					local _card = sgs.Sanguosha:getCard(id)
					y = y + _card:getNumber()
				end
				room:fillAG(card_ids)
				room:getThread():delay()
				room:getThread():delay()
				room:clearAG()
				local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				dummy_0:addSubcards(card_ids)
				room:clearAG()
				source:obtainCard(dummy_0)
				if y < 0 then
					room:askForUseCard(p, ".", "luajisira")
				end
			end
		end
	end
}
luajisir = sgs.CreateOneCardViewAsSkill{
	name = "luajisir&",
	view_filter = function(self, card)
		return true
	end,
	view_as = function(self,card)
		local skillcard = luajisiCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self, player)
		for _, p in sgs.qlist(player:getAliveSiblings()) do
			if p:hasSkill("luajisi") then
				return not player:hasUsed("#luajisir") and p:getHp() - player:getHp() ~= 0
			end
		end
		if (player:hasSkill("luajisi") and not player:hasUsed("#luajisir") and player:getHp() - player:getHp() ~= 0 ) then return true end
		return false
	end,
}
ceshi:addSkill(luajisir)

luapinyi = sgs.CreateTriggerSkill{
	name = "luapinyi",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.MaxHpChanged, sgs.HpChanged, sgs.CardsMoveOneTime, sgs.EventPhaseChanging, sgs.TurnStart, sgs.EventAcquireSkill, sgs.EventLoseSkill},
	on_trigger = function(self, event, player, data)
		if event == sgs.EventLoseSkill then
			if data:toString() == self:objectName() then
				room:setPlayerMark(player, "hand_invalidity", 0)
				room:setPlayerMark(player, "@skill_invalidity", 0)
				room:removePlayerCardLimitation(player, "use,response", ".|.|.|hand$0")
			end
			return false
		end
		if player:hasSkill("luapinyi") then
			local room = player:getRoom()
			if player:getHp() == 1 then
				if player:getMark("@skill_invalidity") == 0 then
					room:addPlayerMark(player, "@skill_invalidity")
				end
			else
				room:setPlayerMark(player, "@skill_invalidity", 0)
			end
			if player:getHandcardNum() == 1 then
				if player:getMark("hand_invalidity") == 0 then
					room:addPlayerMark(player, "hand_invalidity")
					room:setPlayerCardLimitation(player, "use,response", ".|.|.|hand", false)
				end
			else
				for i = 1, 49 do
					room:removePlayerCardLimitation(player, "use,response", ".|.|.|hand$0")
					room:setPlayerMark(player, "hand_invalidity", 0)
				end
			end
		end
	end
}
luachongqun2 = sgs.CreateFilterSkill{
	name = "luachongqun2",
	view_filter = function(self,to_select)
		local room = sgs.Sanguosha:currentRoom()
		local place = room:getCardPlace(to_select:getEffectiveId())
		if (place == sgs.Player_PlaceHand) then return true end
	end,
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("ex_nihilo", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		local _card = sgs.Sanguosha:getWrappedCard(card:getId())
		_card:takeOver(slash)
		return _card
	end
}
ceshi:addSkill(luapinyi)
ceshi:addSkill(luachongqun2)

yingdengCard = sgs.CreateSkillCard{
	name = "luayingdengr" ,
	target_fixed = true ,
	will_throw = false,
	on_use = function(self, room, source, targets)
		local wriggle = room:findPlayerBySkillName("luayingdeng")
		if wriggle and wriggle:isAlive() and not wriggle:isKongcheng() then
			local to_throw = room:askForCardChosen(source, wriggle, "h", self:objectName(), false, sgs.Card_MethodUse)
			to_throw = sgs.Sanguosha:getCard(to_throw)
			if to_throw and not wriggle:isProhibited(source, to_throw, source:getSiblings()) and not wriggle:isCardLimited(to_throw, sgs.Card_MethodUse) then
				room:useCard(sgs.CardUseStruct(to_throw, wriggle, source))
				room:recover(wriggle, sgs.RecoverStruct(wriggle, nil, 1))
			end
		end
	end
}
luayingdengr = sgs.CreateZeroCardViewAsSkill{
	name = "luayingdengr&",
	view_as = function(self)
		return yingdengCard:clone()
	end,
	enabled_at_play = function(self,player)
		local hasXL = false
		for _, p in sgs.qlist(sgs.Self:getSiblings()) do
			if p:hasSkill("luayingdeng") and p:isAlive() then
				hasXL = true
			end
		end
		return not player:hasUsed("#luayingdengr") and hasXL
	end
}
ceshi:addSkill(luayingdengr) 
luagongchengrCard = sgs.CreateSkillCard{
	name = "luagongchengr",
	will_throw = false,
	target_fixed = true,
	on_use = function(self, room, source, targets)
		local cards = self:getSubcards()
		local nitori = room:findPlayerBySkillName("luagongcheng")
		local nitoris = sgs.SPlayerList()
		for _, nitoriX in sgs.qlist(room:findPlayersBySkillName("luagongcheng")) do 
			nitoris:append(nitoriX)
		end 
		if (not nitori) or (not nitori:isAlive()) then return end
		nitori = room:askForPlayerChosen(source, nitoris, self:objectName(), "luagongchengrw", false, false)
		local x = cards:length()
		for _, id in sgs.qlist(cards) do
			local card = sgs.Sanguosha:getCard(id)
			nitori:obtainCard(card)
		end
		source:drawCards(x)
	end
}

luagongchengr = sgs.CreateViewAsSkill{
	name = "luagongchengr&",
	n = 1,
	view_filter = function(self, selected, to_select)
		return to_select:isKindOf("Slash")
	end,
	view_as = function(self, cards)
		if #cards == 0 then return nil end
		local luaxiexianCard = luagongchengrCard:clone()
		for _,card in ipairs(cards) do
			luaxiexianCard:addSubcard(card)
		end
		return luaxiexianCard
	end,
	enabled_at_play = function(self,player)
		if player:hasUsed("#luagongchengr") then return false end
		if not player:hasSkill("luagongcheng") then
			for _, p in sgs.qlist(player:getAliveSiblings()) do
				if p:hasSkill("luagongcheng") then return true end
			end
		end
		if player:hasSkill("luagongcheng") then return true end
		return false
	end,
}
ceshi:addSkill(luagongchengr)

luasenyurCard = sgs.CreateSkillCard{
	name = "luasenyur",
	will_throw = false,
	target_fixed = true,
	on_use = function(self, room, source, targets)
		local cards = self:getSubcards()
		local nitori = room:findPlayerBySkillName("luasenyu")
		local nitoris = sgs.SPlayerList()
		for _, nitoriX in sgs.qlist(room:findPlayersBySkillName("luasenyu")) do 
			nitoris:append(nitoriX)
		end 
		if (not nitori) or (not nitori:isAlive()) then return end
		nitori = room:askForPlayerChosen(source, nitoris, self:objectName(), "luasenyurw", false, false)
		local x = cards:length()
		for _, id in sgs.qlist(cards) do
			local card = sgs.Sanguosha:getCard(id)
			nitori:obtainCard(card)
		end
		source:drawCards(x)
	end
}

luasenyur = sgs.CreateViewAsSkill{
	name = "luasenyur&",
	n = 1,
	view_filter = function(self, selected, to_select)
		return not to_select:isKindOf("Slash") and to_select:isKindOf("BasicCard")
	end,
	view_as = function(self, cards)
		if #cards == 0 then return nil end
		local luaxiexianCard = luasenyurCard:clone()
		for _,card in ipairs(cards) do
			luaxiexianCard:addSubcard(card)
		end
		return luaxiexianCard
	end,
	enabled_at_play = function(self,player)
		if player:hasUsed("#luasenyur") then return false end
		if not player:hasSkill("luasenyu") then
			for _, p in sgs.qlist(player:getAliveSiblings()) do
				if p:hasSkill("luasenyu") then return true end
			end
		end
		if player:hasSkill("luasenyu") then return true end
		return false
	end,
}
ceshi:addSkill(luasenyur)

luahongyuan = sgs.CreateTriggerSkill{
	name = "luahongyuan",
	frequency = sgs.Skill_NotFrequent,
	events = { sgs.DrawNCards },
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:askForSkillInvoke(player, self:objectName()) then
			player:setFlags(self:objectName())
			local count = data:toInt() - 1
			data:setValue(count)
		end
	end
}
luahongyuan2 = sgs.CreateTriggerSkill {
	name = "#luahongyuan",
	frequency = sgs.Skill_Frequent,
	global = true,
	events = { sgs.AfterDrawNCards },
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:hasFlag("luahongyuan") then
			player:setFlags("-luahongyuan")
			local plist = room:getOtherPlayers(player)
			while not plist:isEmpty() do
				local to = room:askForPlayerChosen(player, plist, "luahongyuan", "luahongyuan-invoke", true, true)
				if not to then break end
				to:drawCards(1)
				plist:removeOne(to)
			end
		end
		return false
	end
}
ceshi:addSkill(luahongyuan)
ceshi:addSkill(luahongyuan2)
luajiang = sgs.CreateTriggerSkill{
	name = "luajiang" ,
	events = {sgs.TargetConfirmed},
	frequency = sgs.Skill_Frequent,
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		if use.from and ((use.from:objectName() == player:objectName()) or use.to:contains(player)) then
			if use.card:isKindOf("Duel") or (use.card:isKindOf("Slash") and use.card:isRed()) then
				if player:askForSkillInvoke(self:objectName(),data) then
					player:drawCards(1)
				end
			end
		end
	end
}
ceshi:addSkill(luajiang)
luacpaCard = sgs.CreateSkillCard{
	name = "luacpa" ,
	target_fixed = true ,
	on_use = function(self, room, source, targets)
		local dest
		if source:getMark("@cp2") > 0 then
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:hasSkill("lualianai") then dest = p end
			end
		else
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:getMark("@cp2") > 0 then dest = p end
			end
		end
		if not dest then return false end
		if not dest:isAlive() then return false end
		local peach = sgs.Sanguosha:cloneCard("peach", sgs.Card_NoSuit, 0)
		if not room:isProhibited(source, dest, peach) and dest:isWounded()
				and not source:isCardLimited(peach, sgs.Card_MethodUse) then
			room:useCard(sgs.CardUseStruct(peach, source, dest))
		end
	end

}
luacpa = sgs.CreateOneCardViewAsSkill{
	name = "luacpa&",
	filter_pattern = ".",
	view_as = function(self,card)
		local skillcard = luacpaCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self, player)
		if player:hasUsed("#luacpa") then return false end
		if player:getMark("@cp2") > 0 then
			for _, p in sgs.qlist(player:getSiblings()) do
				if p:hasSkill("lualianai") and p:isWounded() then return true end
			end
		else
			if not player:hasSkill("lualianai") then return false end
			for _, p in sgs.qlist(player:getSiblings()) do
				if p:getMark("@cp2") > 0 and p:isWounded() then return true end
			end
		end

	end
}
ceshi:addSkill(luacpa)
luahanlingCard = sgs.CreateSkillCard{
	name = "luahanling" ,
	handling_method = sgs.Card_MethodNone,
	target_fixed = true ,
	will_throw = false,
	on_use = function(self, room, source, targets)
		local kosuzu = room:findPlayerBySkillName("luahanling")
		local qizhi = kosuzu:getPile("Shu")
		local dummy = sgs.Sanguosha:cloneCard("jink")
		if not qizhi:isEmpty() then
			local dummyW = sgs.Sanguosha:cloneCard("jink")
			dummyW:addSubcards(self:getSubcards())
			kosuzu:obtainCard(dummyW)
			room:fillAG(qizhi, source)
			local card_id = room:askForAG(source, qizhi, false, "luahanling")
			local card = sgs.Sanguosha:getCard(card_id)
			if not card then return false end
			dummy:addSubcard(card)
			source:getRoom():getCurrent():obtainCard(dummy)
			qizhi:removeOne(card_id)
			room:clearAG(source)
		end
	end
}
luahanling2 = sgs.CreateOneCardViewAsSkill{
	name = "luahanling2&",
	filter_pattern = ".|.|.|hand",
	view_as = function(self,card)
		local skillcard = luahanlingCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self, player)
		local hasXL = false
		for _, p in sgs.qlist(sgs.Self:getSiblings()) do
			if p:hasSkill("luahanling") and p:isAlive() and p:getPile("Shu"):length() > 0 then
				hasXL = true
			end
		end
		return not player:hasUsed("#luahanling") and hasXL
	end
}
ceshi:addSkill(luahanling2)
local function ToDeath(ori_acq, general, player, room)
	local kill = true
	for i = 1, #ori_acq do
		if string.find(general, ori_acq[i]) then
			kill = false
		end
	end
	if kill then room:killPlayer(player) end
end
local function ChangeSkin(GeneralName, kplayer, room, modeCode)
	local NewGeneralName = GeneralName
	--modeCode 1表示正常局  2表示混沌局
				if GeneralName == "reimu" then
					local str = "reimu+reimuA+reimuB+reimuC+reimuD+reimuE+reimuX"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "yuka" then
					local str = "yuka+yukaA"
					if kplayer:getState() ~= "robot" then
						str = "yuka+yukaA+yukaB"
					end 
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room) 
				elseif GeneralName == "yumemi" then
					local str = "yumemi+yumemiA+yumemiB+yumemiC"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "rh_flandre" then
					if (string.find(kplayer:screenName(), "Kevin")) or (string.find(kplayer:screenName(), "kevin")) then
						local str = "rh_flandreH+rh_flandreA+rh_flandreB+rh_flandreC+rh_flandreD+rh_flandreE+rh_flandreF+rh_flandreG"
						local general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					else
						local str = "rh_flandre+rh_flandreA+rh_flandreB+rh_flandreC+rh_flandreD+rh_flandreE+rh_flandreF+rh_flandreG"
						local general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					end
				elseif GeneralName == "hifu" then
					local str = "hifu+hifuA+hifuB+hifuC+hifuD+hifuE+hifuF"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "aya" then
					if (string.find(kplayer:screenName(), "诺多")) then
						local general = room:askForGeneral(kplayer, "aya+ayaA+ayaB+ayaC+ayaD+sp_aya")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
					elseif kplayer:getState() == "robot" then
						general = room:askForGeneral(kplayer, "aya+ayaA+ayaB")
					else
						local str = "aya+ayaA+ayaB+ayaC+ayaD+ayaE"
						local general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					end
				elseif GeneralName == "yuyuko" then
					local str = "yuyuko+yuyukoA+yuyukoB+yuyukoC+yuyukoD"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "cirno" then
					local general
					if kplayer:getState() == "robot" then
						general = room:askForGeneral(kplayer, "cirno+cirnoA+cirnoB+cirnoC+cirnoE")
					else
						if (string.find(kplayer:screenName(), "月半")) then
							general = room:askForGeneral(kplayer, "cirno+cirnoA+cirnoB+cirnoC+cirnoD+cirnoE+accirno")
						else
							general = room:askForGeneral(kplayer, "cirno+cirnoA+cirnoB+cirnoC+cirnoE+accirno")
						end
					end
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					if general == "accirno" then
						room:notifySkillInvoked(kplayer, "luaqijian")
						local card_ids = room:getNCards(7)
						kplayer:addToPile("sword", card_ids, false)
					end
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "rumia" then
					local general
					if kplayer:getState() == "robot" then
						general = room:askForGeneral(kplayer, "rumia+rumiaA+rumiaB")
					else
						local str = "rumia+rumiaA+rumiaB+ex_rumia"
						general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					end 
				elseif GeneralName == "qinxin" then
					local str = "qinxin+kokoroA+kokoroB+kokoroC+kokoroD"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "sanae" then
					local str = "sanae+sanaeA+sanaeB+sanaeC+sanaeD"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "patchouli" then
					local str = "patchouli+patchouliA+patchouliB+patchouliC+patchouliD"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "hiziri" then
					local str = "hiziri+hiziriA+hiziriB+hiziriC+hiziriD"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "akyuu" then
					local str = "akyuu+akyuuA+akyuuB+akyuuC+sp_akyuuB"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					if general == "sp_akyuuB" then
						local x = kplayer:getMaxHp()
						room:setPlayerProperty(kplayer, "hp", sgs.QVariant(x - 7)) 
					end 
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "marisa" then
					if kplayer:getState() == "robot" then
						local str = "marisa"
						local general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					else
						local str = "marisaA+marisaB+marisaC+marisaD+marisaE+sp_marisa"
						local general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					end
				elseif GeneralName == "shinki" then
					local general = room:askForGeneral(kplayer, "shinki")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "sp_rin" then
					local str = "sp_rin+sp_rinA+sp_rinB"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "iku" then
					local str = "iku+ikuA+ikuB"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "youmu" then
					local str = "youmu+youmuA+youmuB+youmuC"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					local x = kplayer:getMaxHp()
					room:setPlayerProperty(kplayer, "hp", sgs.QVariant(x - 1))
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "seiga" then
					local str = "seiga+seigaA+seigaB+seigaC"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "remilia" then
					local str = "remilia+remiliaA+remiliaB+remiliaC"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "sp_youmu" then
					local str = "sp_youmu+sp_youmuA+sp_youmuB+sp_youmuC"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					local x = kplayer:getMaxHp()
					room:setPlayerProperty(kplayer, "hp", sgs.QVariant(x - 1))
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "satori" then
					local str = "satori+satoriA+satoriB"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "eternity" then
					local str = "eternity+eternityA+eternityB+eternityC"
					if kplayer:getState() == "robot" then
						str = "eternity+eternityA+eternityB"
					end 
					if room:getAlivePlayers():length() > 6 then
						str = "eternity+eternityA+eternityB"
					end 
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "hina" then
					local general = room:askForGeneral(kplayer, "hina+hinaA+hinaB")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "Rh_junko" then
					local general = room:askForGeneral(kplayer, "Rh_junko+Rh_junkoA+Rh_junkoB")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "kaguya" then
					local general = room:askForGeneral(kplayer, "kaguya+kaguyaA+kaguyaB+kaguyaC+kaguyaD")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "reisen" then
					local general = room:askForGeneral(kplayer, "reisen+reisenA+reisenB+reisenC+reisenD")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "toyosatomimi" then
					local general = room:askForGeneral(kplayer, "toyosatomimi+toyosatomimiA+toyosatomimiB+toyosatomimiC+toyosatomimiD+toyosatomimiE")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "parsee" then
					local general = room:askForGeneral(kplayer, "parseeA")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "nue" then
					local general = room:askForGeneral(kplayer, "nueA")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "sakuya" then
					local general = room:askForGeneral(kplayer, "sakuyaO+sakuyaA+sakuyaB+sakuyaC+sakuyaD")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "white" then
					if kplayer:getState() ~= "robot" then
						local str = "white+whiteA+whiteB+whiteC+whiteD"
						local general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					else
						local str = "white+whiteA+whiteC+whiteD"
						local general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					end
				elseif GeneralName == "sagume" then
					local general = room:askForGeneral(kplayer, "sagume+sagumeA+sagumeB+sagumeC")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "koakuma" then
					local general = room:askForGeneral(kplayer, "koakuma+koakumaA+koakumaB")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "akong" then
					local general = room:askForGeneral(kplayer, "akongO+akongA+akongB+akongC+akongD")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "daiyousei" then
					local general = room:askForGeneral(kplayer, "daiyousei+daiyouseiA+daiyouseiB+daiyouseiC+daiyouseiD")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "suika" then
					local general = room:askForGeneral(kplayer, "suika+suikaA+suikaB+suikaC+suikaD+suikaE")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "yukariex" then
					if (string.find(kplayer:screenName(), "魔理")) then
						local general = room:askForGeneral(kplayer, "yukariex+yukariexA+yukariexB+yukariexC+yukariexD")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
					else
						local general = room:askForGeneral(kplayer, "yukariex+yukariexA+yukariexB+yukariexC+yukariexD")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
					end
				elseif GeneralName == "koishi" then
					local general = room:askForGeneral(kplayer, "koishi+koishiA+koishiB+koishiC")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "clownpiece" then
					local general = room:askForGeneral(kplayer, "clownpiece+clownpieceA+clownpieceB")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "alice" then
					local general = room:askForGeneral(kplayer, "alice+aliceA+aliceB+aliceC+aliceD+aliceE")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "futo" then
					local general = room:askForGeneral(kplayer, "futo+futoA+futoB+futoC")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "sangetsusei" then
					local general = room:askForGeneral(kplayer, "sangetsusei+sangetsuseiA+sangetsuseiB+sangetsuseiC")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "prismriver" then
					local general = room:askForGeneral(kplayer, "prismriver+prismriverA+prismriverB")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "kutaka" then
					local general = room:askForGeneral(kplayer, "kutaka+kutakaA+kutakaB+kutakaC")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "keiki" then
					local general = room:askForGeneral(kplayer, "keiki+keikiA+keikiB+keikiC")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerProperty(kplayer, "maxhp", sgs.QVariant(1))
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "momiji" then
					local general = room:askForGeneral(kplayer, "momiji+momijiA+momijiB+momijiC+momijiD")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "sumireko" then
					local general = room:askForGeneral(kplayer, "sumireko+sumirekoA+sumirekoB+sumirekoC+sumirekoD")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "joon" then
					if (string.find(kplayer:screenName(), "arisa")) then
						local general = room:askForGeneral(kplayer, "joon+yorigami+shion")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
					elseif kplayer:getState() ~= "robot" then
						local general = room:askForGeneral(kplayer, "joon+yorigami+shion")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
					end
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "tewi" then
					if (string.find(kplayer:screenName(), "⑨")) then
						local general = room:askForGeneral(kplayer, "tewi+sp_tewi")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
					end
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "keine" then
					if kplayer:getState() ~= "robot" then
						local general = room:askForGeneral(kplayer, "keine+keineA+keineB")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "kitcho" then
					local general = room:askForGeneral(kplayer, "kitcho+kitchoA+kitchoB")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "toone" then
					if kplayer:getState() ~= "robot" then
						local general = room:askForGeneral(kplayer, "toone+yukariY")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
					end  
				end 
	return NewGeneralName
end 
luaHuanPifu = sgs.CreateTriggerSkill{
	name = "#luaHuanPifu",
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.TurnStart},
	priority = 9,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:getLord():getMark("hasChangedX") > 0 then return false end
		for  _, kplayer in sgs.qlist(room:getAlivePlayers()) do
			if kplayer:getMark("hasChanged") == 0 then
				ChangeSkin(kplayer:getGeneralName(), kplayer, room, 1)
			end
		end
		room:setPlayerMark(room:getLord(), "hasChangedX", 1)
	end
}
ceshi:addSkill(luaHuanPifu)
luabuxin4 = sgs.CreateFilterSkill{
	name = "luabuxin4",
	view_filter = function(self, to_select)
		local room = sgs.Sanguosha:currentRoom()
		local place = room:getCardPlace(to_select:getEffectiveId())
		if (place == sgs.Player_PlaceHand) then
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				local card = sgs.Sanguosha:getEngineCard(to_select:getEffectiveId())
				if p:getMark("luabuxin" .. card:objectName()) > 0 then
					room:writeToConsole("kana test" .. p:objectName())
					return true
				end
			end 
		end
	end,
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("hui", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		local _card = sgs.Sanguosha:getWrappedCard(card:getId())
		_card:takeOver(slash)
		return _card
	end
}
ceshi:addSkill(luabuxin4)
luahuizhen2 = sgs.CreateFilterSkill{
	name = "luahuizhen2",
	view_filter = function(self,to_select)
		local room = sgs.Sanguosha:currentRoom()
		local place = room:getCardPlace(to_select:getEffectiveId())
		if room:getCurrent():getPhase() == sgs.Player_Finish or room:getCurrent():hasSkill("luahuizhen") then
			if (place == sgs.Player_PlaceHand) then return true end
		end
	end,
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		local _card = sgs.Sanguosha:getWrappedCard(card:getId())
		_card:takeOver(slash)
		return _card
	end
}
ceshi:addSkill(luahuizhen2)
luayanmeng2 = sgs.CreateFilterSkill{
    name = "luayanmeng2" ,
    view_filter = function(self, card)
        return card:objectName() == "jink"  
    end ,
    view_as = function(self, card)
        local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
        slash:setSkillName(self:objectName())
        local wrap = sgs.Sanguosha:getWrappedCard(card:getId())
        wrap:takeOver(slash)
        return wrap
    end
}
ceshi:addSkill(luayanmeng2)
gemingCard = sgs.CreateSkillCard{
	name = "luagemingg" ,
	target_fixed = false ,
	will_throw = true ,
	filter = function(self, targets, to_select)
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
		slash:deleteLater()
		return #targets == 0 and not sgs.Self:isCardLimited(slash, sgs.Card_MethodUse) and (not sgs.Self:isProhibited(to_select, slash))
	end ,
	about_to_use = function(self, room, cardUse)
		local diaochan = cardUse.from
		local logg = sgs.LogMessage()
		logg.from = diaochan
		logg.to = cardUse.to
		logg.type = "#UseCard"
		logg.card_str = self:toString()
		room:sendLog(logg)
		local data = sgs.QVariant()
		data:setValue(cardUse)
		local thread = room:getThread()
		thread:trigger(sgs.PreCardUsed, room, diaochan, data)
		local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_THROW, diaochan:objectName(), nil, "luagemingg", nil)
		room:moveCardTo(self, diaochan, nil, sgs.Player_DiscardPile, reason, true)
		thread:trigger(sgs.CardUsed, room, diaochan, data)
		thread:trigger(sgs.CardFinished, room, diaochan, data)
	end ,
	on_use = function(self, room, source, targets)
		local to = targets[1]
		local from = source
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		if (not from:isCardLimited(slash, sgs.Card_MethodUse)) and (not from:isProhibited(to, slash)) then
			room:useCard(sgs.CardUseStruct(slash, from, to))
		end
	end
}

luagemingg = sgs.CreateOneCardViewAsSkill{
	name = "luagemingg&",
	filter_pattern = ".|.|.|hand",
	view_as = function(self,card)
		local skillcard = gemingCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#luagemingg") and player:getMark("gemingEnd") == 0
	end,
}

ceshi:addSkill(luagemingg)


luaxiedaoCard = sgs.CreateSkillCard{
	name = "luaxiedao",
	will_throw = true,
	target_fixed = true,
	on_use = function(self, room, source, targets)
		if source:hasFlag("luayanxun") and source:hasFlag("luaguiwan") then
			local choice = room:askForChoice(source, "luaxiedao", "luayanxun+luaguiwan")
			if choice == "luayanxun" then
				room:setPlayerFlag(source, "-luayanxun")
			else
				room:setPlayerFlag(source, "-luaguiwan")
			end
		elseif source:hasFlag("luayanxun") and not source:hasFlag("luaguiwan") then
			room:setPlayerFlag(source, "-luayanxun")
		elseif source:hasFlag("luaguiwan") and not source:hasFlag("luayanxun") then
			room:setPlayerFlag(source, "-luaguiwan")
		end
	end
}
luaxiedao = sgs.CreateZeroCardViewAsSkill{
	name = "luaxiedao",
	view_as = function(self, cards)
		return luaxiedaoCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:hasFlag("luaxiedao") and not player:hasUsed("#luaxiedao")
	end
}

luaguiwanCard = sgs.CreateSkillCard{
	name = "luaguiwan",
	filter = function(self, targets, to_select)
		return (#targets == 0)
	end,
	on_effect = function(self, effect)
		effect.from:drawCards(1)
		effect.from:getRoom():damage(sgs.DamageStruct("luaguiwan", effect.from, effect.to))
		effect.from:getRoom():setPlayerFlag(effect.from, "luaguiwan")
	end
}
luaguiwan = sgs.CreateOneCardViewAsSkill{
	name = "luaguiwan",
	view_filter = function(self, to_select)
		return to_select:isKindOf("EquipCard")
	end,
	view_as = function(self, card)
		local skillcard = luaguiwanCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self,player)
		return not player:hasFlag("luaguiwan")
	end,
}
ceshi:addSkill(luaxiedao)
ceshi:addSkill(luaguiwan)

Luaxinwu2 = sgs.CreateTriggerSkill{
	name = "#Luaxinwu2",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Pindian},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local pindian = data:toPindian()
		if pindian.reason == "Luaxinwu" then
			local fromNumber = pindian.from_number
			local toNumber = pindian.to_number
			room:writeToConsole(tostring(fromNumber))
			room:writeToConsole(tostring(toNumber))
			local winner
			local loser
			local loser2
			if fromNumber > toNumber then
				winner = pindian.from
				loser = pindian.to
			elseif fromNumber == toNumber then
				loser = pindian.to
				loser2 = pindian.from
			elseif fromNumber <= toNumber then
				loser = pindian.from
				winner = pindian.to
			end
			if winner and not loser:isNude() then
				for _, qinxin in sgs.qlist(room:findPlayersBySkillName("Luaxinwu")) do
					local choice = room:askForChoice(qinxin, "LuaxinwuX","Luaxinwu1+Luaxinwu2", data)
					if choice == "Luaxinwu1" then
						if not loser:isNude() then
							local card_id = room:askForCardChosen(winner, loser, "he", "Luaxinwu2")
							local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, winner:objectName())
							room:obtainCard(winner, card_id, false)
						end
					else
						local basic_card = {"analeptic", "peach"}
						if qinxin:canSlash(loser, sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0), false) then
							table.insert(basic_card,"slash")
						end
						if qinxin:canSlash(loser, sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_NoSuit, 0), false) then
							table.insert(basic_card,"fire_slash")
						end
						if qinxin:canSlash(loser, sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_NoSuit, 0), false) then
							table.insert(basic_card,"thunder_slash")
						end
						local room2 = qinxin:getRoom()
						local playerdata = sgs.QVariant() --ai用
						playerdata:setValue(loser)
						choice = room2:askForChoice(qinxin, "Luaxinwu",table.concat(basic_card,"+"), playerdata)
						local slash = sgs.Sanguosha:cloneCard(choice, sgs.Card_NoSuit, 0)
						slash:setSkillName("Luaxinwu")
						local card_use = sgs.CardUseStruct()
						card_use.from = qinxin
						card_use.to:append(loser)
						card_use.card = slash
						room2:useCard(card_use, false)
					end
				end
			elseif winner and loser:isNude() then
				for _, qinxin in sgs.qlist(room:findPlayersBySkillName("Luaxinwu")) do
					local basic_card = {"analeptic", "peach"}
					if qinxin:canSlash(loser, sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0), false) then
						table.insert(basic_card,"slash")
					end
					if qinxin:canSlash(loser, sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_NoSuit, 0), false) then
						table.insert(basic_card,"fire_slash")
					end
					if qinxin:canSlash(loser, sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_NoSuit, 0), false) then
						table.insert(basic_card,"thunder_slash")
					end
					local room2 = qinxin:getRoom()
					local playerdata = sgs.QVariant() --ai用
					playerdata:setValue(loser)
					local choice = room2:askForChoice(qinxin, "Luaxinwu",table.concat(basic_card,"+"), playerdata)
					local slash = sgs.Sanguosha:cloneCard(choice, sgs.Card_NoSuit, 0)
					slash:setSkillName("Luaxinwu")
					local card_use = sgs.CardUseStruct()
					card_use.from = qinxin
					card_use.to:append(loser)
					card_use.card = slash
					room2:useCard(card_use, false)
				end
			elseif loser and loser2 then
				for _, qinxin in sgs.qlist(room:findPlayersBySkillName("Luaxinwu")) do
					local plist = sgs.SPlayerList()
					plist:append(loser)
					plist:append(loser2)
					local olayer = room:askForPlayerChosen(qinxin, plist, self:objectName(), "LuaxinwuTarget", true, false)
					if olayer:objectName() ~= loser:objectName() then
						olayer = loser
						loser = loser2
						loser2 = olayer
					end
					local basic_card = {"analeptic", "peach"}
					if qinxin:canSlash(loser, sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0), false) then
						table.insert(basic_card,"slash")
					end
					if qinxin:canSlash(loser, sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_NoSuit, 0), false) then
						table.insert(basic_card,"fire_slash")
					end
					if qinxin:canSlash(loser, sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_NoSuit, 0), false) then
						table.insert(basic_card,"thunder_slash")
					end
					local room2 = qinxin:getRoom()
					local playerdata = sgs.QVariant() --ai用
					playerdata:setValue(loser)
					local choice = room2:askForChoice(qinxin, "Luaxinwu",table.concat(basic_card,"+"), playerdata)
					local slash = sgs.Sanguosha:cloneCard(choice, sgs.Card_NoSuit, 0)
					slash:setSkillName("Luaxinwu")
					local card_use = sgs.CardUseStruct()
					card_use.from = qinxin
					card_use.to:append(loser)
					card_use.card = slash
					room2:useCard(card_use, false)

					local basic_card2 = {"analeptic", "peach"}
					if qinxin:canSlash(loser2, sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0), false) then
						table.insert(basic_card2,"slash")
					end
					if qinxin:canSlash(loser2, sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_NoSuit, 0), false) then
						table.insert(basic_card2,"fire_slash")
					end
					if qinxin:canSlash(loser2, sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_NoSuit, 0), false) then
						table.insert(basic_card2,"thunder_slash")
					end
					playerdata:setValue(loser2)
					local choice3 = room2:askForChoice(qinxin, "Luaxinwu",table.concat(basic_card2,"+"), playerdata)
					local slash2 = sgs.Sanguosha:cloneCard(choice3, sgs.Card_NoSuit, 0)
					slash2:setSkillName("Luaxinwu")
					local card_use2 = sgs.CardUseStruct()
					card_use2.from = qinxin
					card_use2.to:append(loser2)
					card_use2.card = slash2
					room2:useCard(card_use2, false)
				end
			end
		end
		return false
	end,
	can_trigger = function(self, target)
		return target
	end,
	priority = -1
}
LuaxinwuCard = sgs.CreateSkillCard{
	name = "Luaxinwu" ,
	will_throw = true ,
	filter = function(self, targets, to_select)
		if to_select:isKongcheng() then
			return false
		end
		return #targets < 2
	end ,
	feasible = function(self, targets)
		return #targets == 2
	end ,
	on_use = function(self, room, source, targets)
		room:setPlayerFlag(targets[1],"Luaxinwu_Target")
		room:setPlayerFlag(targets[2],"Luaxinwu_Target")
		targets[1]:pindian(targets[2], "Luaxinwu", nil)
		room:setPlayerFlag(targets[1],"-Luaxinwu_Target")
		room:setPlayerFlag(targets[2],"-Luaxinwu_Target")
	end
}
Luaxinwu = sgs.CreateViewAsSkill{
	name = "Luaxinwu" ,
	n = 1 ,
	view_filter = function(self, selected, to_select)
		if sgs.Self:isJilei(to_select) then return false end
		return true
	end ,
	view_as = function(self, cards)
		if #cards == 0 then return nil end
		local xinwu_card = LuaxinwuCard:clone()
		for _, c in ipairs(cards) do
			xinwu_card:addSubcard(c)
		end
		return xinwu_card
	end ,
	enabled_at_play = function(self, player)
		return not player:isKongcheng() and not player:hasUsed("#Luaxinwu")
	end
}

--player:obtainCard(pindian.to_card)
Luanengmian = sgs.CreateTriggerSkill{
	name = "Luanengmian" ,
	events = {sgs.PindianVerifying} ,
	view_as_skill = LuanengmianVS ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local qinxin = room:findPlayerBySkillName(self:objectName())
		if not qinxin then return false end
		local pindian = data:toPindian()
		local plist = sgs.SPlayerList()
		plist:append(pindian.from)
		plist:append(pindian.to)
		local enumber = qinxin:getEquips():length()
		if enumber == 0 then return end
		room:setPlayerFlag(pindian.from, "nengmian_Target1")
		room:setPlayerMark(pindian.from, "nengmian", pindian.from_number)
		room:setPlayerFlag(pindian.to, "nengmian_Target2")
		room:setPlayerMark(pindian.to, "nengmian", pindian.to_number)
		local olayer = room:askForPlayerChosen(qinxin, plist, self:objectName(), "Luanengmiani", true, true)
		room:setPlayerFlag(pindian.from, "-nengmian_Target1")
		room:setPlayerMark(pindian.from, "nengmian", 0)
		room:setPlayerFlag(pindian.to, "-nengmian_Target2")
		room:setPlayerMark(pindian.to, "nengmian", 0)
		if olayer then
			local num = 0
			if olayer:objectName() == pindian.from:objectName() then
				num = pindian.from_number
			else
				num = pindian.to_number
			end
			room:setPlayerFlag(olayer, "nengmian_Target3")
			room:setPlayerMark(olayer, "nengmian", num)
			if olayer:objectName() == pindian.from:objectName() then
				room:setPlayerFlag(pindian.to, "nengmian_Target4")
				room:setPlayerMark(pindian.to, "nengmian", num)
			else
				room:setPlayerFlag(pindian.from, "nengmian_Target4")
				room:setPlayerMark(pindian.from, "nengmian", num)
			end
			local i = -enumber
			--room:writeToConsole(tostring(i))
			local choice1 = {}
			while (i >= -enumber) and (i <= enumber) do
				if i ~= 0 and num + i > 0 and num + i < 14 then
					table.insert(choice1,tostring(i))
					--room:writeToConsole(tostring(i))
				end
				i = i + 1
			end
			local choice = room:askForChoice(qinxin, "Luanengmian",table.concat(choice1,"+"))
			room:setPlayerFlag(olayer, "-nengmian_Target3")
			room:setPlayerMark(olayer, "nengmian", 0)

			choice = tonumber(choice)
			if olayer:objectName() == pindian.from:objectName() then
				pindian.from_number = pindian.from_number + choice
				--room:writeToConsole("asd "..tostring(pindian.from_number))
			else
				pindian.to_number = pindian.to_number + choice
				--room:writeToConsole("bef "..tostring(pindian.to_number))
			end
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if p:hasFlag("nengmian_Target4") then
					room:setPlayerFlag(olayer, "-nengmian_Target4")
					room:setPlayerMark(olayer, "nengmian", 0)
				end
			end
			data:setValue(pindian)
		end

		return
	end ,
	can_trigger = function(self, target)
		return target
	end
}
Luanengmian2 = sgs.CreateTriggerSkill{
	name = "#Luanengmian",
	events = {sgs.Pindian},
	on_trigger = function(self, event, player, data)
		local pindian = data:toPindian()
		if player:hasSkill("Luanengmian") then
			player:obtainCard(pindian.to_card)
		end
		return false
	end,
	priority = -1
}
qinxin:addSkill(Luaxinwu)
qinxin:addSkill(Luaxinwu2)
qinxin:addSkill(Luanengmian)
qinxin:addSkill(Luanengmian2)

kokoroA:addSkill(Luaxinwu)
kokoroA:addSkill(Luaxinwu2)
kokoroA:addSkill(Luanengmian)
kokoroA:addSkill(Luanengmian2)

kokoroB:addSkill(Luaxinwu)
kokoroB:addSkill(Luaxinwu2)
kokoroB:addSkill(Luanengmian)
kokoroB:addSkill(Luanengmian2)

kokoroC:addSkill(Luaxinwu)
kokoroC:addSkill(Luaxinwu2)
kokoroC:addSkill(Luanengmian)
kokoroC:addSkill(Luanengmian2)

kokoroD:addSkill(Luaxinwu)
kokoroD:addSkill(Luaxinwu2)
kokoroD:addSkill(Luanengmian)
kokoroD:addSkill(Luanengmian2)

LuaFengshen = sgs.CreateTriggerSkill{
	name = "LuaFengshen$",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Damaged, sgs.PreDamageDone},
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local room = player:getRoom()
		if (event == sgs.PreDamageDone) and damage.to then
			damage.to:setTag("InvokeLuaFengshen", sgs.QVariant(true))
		elseif (event == sgs.Damaged) and player:getTag("InvokeLuaFengshen"):toBool() and player:isAlive() then
			local suwakos = sgs.SPlayerList()
			for _, p in sgs.qlist(room:getOtherPlayers(player)) do
				if p:hasLordSkill(self:objectName()) then
					suwakos:append(p)
				end
			end
			while not suwakos:isEmpty() do
				local suwako = room:askForPlayerChosen(player, suwakos, self:objectName(), "@LuaFengshen2", true)
				if suwako then
					suwakos:removeOne(suwako)
					local judge = sgs.JudgeStruct()
					judge.pattern = "TrickCard|.|."
					judge.good = true
					judge.reason = self:objectName()
					judge.who = player
					room:judge(judge)
					if judge:isGood() then
						suwako:drawCards(1)
					end
				else
					break
				end
			end
		end
		return false
	end,
	can_trigger = function(self, target)
		return target
	end
}

luasheji2 = sgs.CreateTriggerSkill{
	name = "#luasheji2",
	global = true,
	events = {sgs.TurnStart, sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data) 
		local room = player:getRoom()
		if event == sgs.TurnStart then
			for _, p in sgs.qlist(room:findPlayersBySkillName("luasheji")) do 
				if p:getMark("@luasheji3") < 1 and p:getMark("@luasheji2") < 1 and p:getMark("@luasheji1") < 1 then 
					room:setPlayerMark(p, "@luasheji3", 1)
				end 
			end 
		else
			local move = data:toMoveOneTime() 
			for _, houjuu in sgs.qlist(room:findPlayersBySkillName("luasheji")) do
				if player:objectName() == houjuu:objectName() then 
					if (move.card_ids:length() == 1) and move.from_places:contains(sgs.Player_PlaceTable) and (move.to_place == sgs.Player_DiscardPile)
							and (move.reason.m_reason == sgs.CardMoveReason_S_REASON_USE) then
						local card = sgs.Sanguosha:getCard(move.card_ids:first())
						if player:getMark("luashejiF") - 1 == card:getEffectiveId() then  
							room:setPlayerMark(player, "luashejiF", 0)
							local targets = {}
							local target1 = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "luasheji", true, false)
							if target1 then 
								local players = sgs.SPlayerList()
								for _, p in sgs.qlist(room:getAlivePlayers()) do
									if p:objectName() ~= target1:objectName() then
										players:append(p)
									end
								end 
								local target2 = room:askForPlayerChosen(player, players, self:objectName(), "luasheji", true, false)
								if target2 then 
									table.insert(targets, target1)
									table.insert(targets, target2)
									qiangxingshiyongtttt(card, targets, houjuu, 2)
								end 
							end  
						end
					end
				end 
			end 
		end 
	end
}

luasheji = sgs.CreateTriggerSkill{
	name = "luasheji", 
	events = {sgs.TargetConfirmed},
	global = true,
	on_trigger = function(self, event, player, data) 
		local room = player:getRoom() 
		if event == sgs.TargetConfirmed then
			local use = data:toCardUse() 
			if not use.from then return end 
			if use.from:objectName() ~= player:objectName() or not use.from:hasSkill(self:objectName()) then return end 
			local room = player:getRoom()
			if not use.card then return end 
			if use.card:isKindOf("SkillCard") then return end 
			room:writeToConsole("Hello World!A")
			for _,p in sgs.qlist(use.to) do
				room:writeToConsole("Hello World!AA")
				if p:getHp() > 0 then 
					room:writeToConsole("Hello World!AAA" .. player:getGeneralName())
					for _,c in sgs.qlist(player:getHandcards()) do
						room:writeToConsole("Hello World!AAA " .. c:objectName())

					end  
					if (player:getHandcardNum() == 3 and player:getMark("@luasheji3") > 0) and not p:isNude()
						and room:askForSkillInvoke(player, self:objectName(), data) then 
						room:setPlayerMark(player, "@luasheji3", 0)
						room:setPlayerMark(player, "@luasheji2", 1)
						local id1 = room:askForCardChosen(player, p, "he", self:objectName()) 
						local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
						dummy:addSubcard(id1)
						room:throwCard(dummy, p, player)
					elseif (player:getHandcardNum() == 2 and player:getMark("@luasheji2") > 0) 
						and room:askForSkillInvoke(player, self:objectName(), data) then
						room:setPlayerMark(player, "@luasheji2", 0)
						room:setPlayerMark(player, "@luasheji1", 1)
						room:setPlayerMark(player, "luashejiF", use.card:getEffectiveId() + 1) 
					elseif (player:getHandcardNum() == 1 and player:getMark("@luasheji1") > 0) 
						and room:askForSkillInvoke(player, self:objectName(), data) then
						room:setPlayerMark(player, "@luasheji1", 0)
						room:setPlayerMark(player, "@luasheji3", 1)
						room:loseHp(player, 1)	 
						player:drawCards(2)
					end  
				end 
			end  
		end 
	end 
}

luayaoyuncard = sgs.CreateSkillCard{
	name = "luayaoyun" , 
	target_fixed = true, 
	on_use = function(self, room, source, targets) 
		for _,c in sgs.qlist(source:getHandcards()) do
			room:setCardFlag(c:getEffectiveId(), "luayaoyunF")
		end 
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		room:setPlayerMark(source, "@luayaoyunA", 1) 
		if not source:hasSkill("luamengyan") then room:acquireSkill(source, "luamengyan") end 
 
		local thread = room:getThread()
		local old_phase = source:getPhase()

		source:setPhase(sgs.Player_Play)

		room:broadcastProperty(source, "phase")
		if not thread:trigger(sgs.EventPhaseStart, room, source) then
			thread:trigger(sgs.EventPhaseProceeding, room, source)
		end

		thread:trigger(sgs.EventPhaseEnd, room, source)
		source:setPhase(old_phase)
		room:broadcastProperty(source, "phase")		
		
	end
}
luayaoyunVS = sgs.CreateZeroCardViewAsSkill{
	name = "luayaoyun",
	
	view_as = function()
		return luayaoyuncard:clone()
	end,

	enabled_at_play = function(self, player)
		return player:getMark("@luayaoyunA") == 0
	end
}
luayaoyun = sgs.CreateTriggerSkill{
	name = "luayaoyun",	
	events = {sgs.EventPhaseStart},
	view_as_skill = luayaoyunVS, 
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart and player:getPhase() == sgs.Player_Start and player:getMark("@luayaoyunA") > 0 then 
			if player:hasSkill("luamengyan") then room:detachSkillFromPlayer(player, "luamengyan") end 
			room:setPlayerMark(player, "@luayaoyunA", 0) 
			
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				for _,c in sgs.qlist(p:getHandcards()) do
					room:setCardFlag(c:getEffectiveId(), "-luayaoyunF")
				end 
			end 
		end 
	end,
}

function qiangxingshiyongtttt(stars, targets, source, leastTarget)
	local room = source:getRoom()
	local function canUse2Self(card)
		return card:isKindOf("ExNihilo") or card:isKindOf("Lightning") or card:isKindOf("GodSalvation") or card:isKindOf("AmazingGrace")
	end
	if source:isCardLimited(stars, sgs.Card_MethodUse) then return false end
	local _targets = sgs.SPlayerList()
	for _, p in ipairs(targets) do
		if not source:isProhibited(p, stars) then
			_targets:append(p)
		end
	end
	if (not _targets) or (_targets:length() == 0) then return false end
	if stars:isKindOf("Jink") or stars:isKindOf("Nullification") or stars:isKindOf("Collateral") or stars:isKindOf("sakura") then return false end  --借刀杀人我不想做了
	if stars:isKindOf("DelayedTrick") and leastTarget > 1 then return false end 
	if stars:isKindOf("TrickCard") then
		local targets2 = sgs.SPlayerList()
		for _, _player in sgs.qlist(_targets) do
			local canuse = true
			if stars:isKindOf("DelayedTrick") then
				if _player:containsTrick(stars:objectName()) then
					canuse = false
				end
			end
			if canuse then
				targets2:append(_player) 
			end
		end
		if targets2 and targets2:length() >= leastTarget then
			if stars:isKindOf("AOE") or stars:isKindOf("AmazingGrace") or stars:isKindOf("GodSalvation") then
				for _, target in sgs.qlist(targets2) do
					room:setPlayerFlag(source, "meiyinAOE")
					room:setPlayerFlag(target, "meiyinAOEs")
				end
				room:useCard(sgs.CardUseStruct(stars, source, sgs.SPlayerList()))
				for _, target in sgs.qlist(targets2) do
					room:setPlayerFlag(source, "-meiyinAOE")
					room:setPlayerFlag(target, "-meiyinAOEs")
				end
				return true
			end
			room:useCard(sgs.CardUseStruct(stars, source, _targets))
			return true
		end
	end
	if (stars:isKindOf("EquipCard") and leastTarget > 1) then return false end 
	if (stars:isKindOf("EquipCard") or stars:isKindOf("Analeptic")) then
		room:useCard(sgs.CardUseStruct(stars, source, _targets))
		return true
	end
	if stars:isKindOf("Slash") then
		local targets2 = sgs.SPlayerList()
		for _, _player in sgs.qlist(_targets) do
			if not source:isProhibited(_player, stars) then
				targets2:append(_player)
			end
		end
		if targets2 and targets2:length() > 0 then
			room:useCard(sgs.CardUseStruct(stars, source, targets2))
			room:addPlayerHistory(source,"Slash",1)
			return true
		end
		return false
	end
	if stars:isKindOf("Hui") then
		local targets2 = sgs.SPlayerList()
		for _, _player in sgs.qlist(_targets) do
				targets2:append(_player)
		end
		if targets2 and targets2:length() > 0 then
			room:useCard(sgs.CardUseStruct(stars, source, targets2))
			return true
		end
		return false
	end
	if stars:isKindOf("Peach") then
		local targets2 = sgs.SPlayerList()
		for _, _player in sgs.qlist(_targets) do
			if _player:isWounded() then
				targets2:append(_player)
			end
		end
		if targets2 and targets2:length() > 0 then
			room:useCard(sgs.CardUseStruct(stars, source, targets2))
			return true
		end
		return false
	end
	if stars:isKindOf("Ofuda") then
		local targets2 = sgs.SPlayerList()
		for _, _player in sgs.qlist(_targets) do
			targets2:append(_player)
		end
		if targets2 and targets2:length() > 0 then
			room:useCard(sgs.CardUseStruct(stars, source, targets2))
			return true
		end
		return false
	end
	return false
end

luamengyancard = sgs.CreateSkillCard{
	name = "luamengyan", 
	filter = function(self, targets, to_select)
		return to_select:hasFlag("luamengyanF")
	end,  
	on_use = function(self, room, source, targets) 
		local card = sgs.Sanguosha:getCard(room:getDrawPile():first())
		if not qiangxingshiyongtttt(card, targets, source, 1) then 
			local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, source:objectName())
			room:moveCardTo(card, source, nil, sgs.Player_DiscardPile, reason, true)
		end 
	end
}
luamengyanVS = sgs.CreateZeroCardViewAsSkill{
	name = "luamengyan", 
	view_as = function(self, cards) 
		return luamengyancard:clone()
	end, 
	enabled_at_play = function(self, player)
		return false
	end, 
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luamengyan"
	end
}
luamengyan = sgs.CreateTriggerSkill{
	name = "luamengyan",
	view_as_skill = luamengyanVS, 
	priority = -1, 
	events = {sgs.TargetConfirmed, sgs.BeforeCardsMove},
	frequency = sgs.Skill_Compulsory ,
	on_trigger = function(self, event, player, data)
		if not player:hasSkill("luamengyan") then return end
		local room = player:getRoom()
		if event == sgs.TargetConfirmed then
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				room:setPlayerFlag(p, "-luamengyanF")
	
			end 
			local use = data:toCardUse()
			if not use.from then return end
			if use.to:length() < 1 then return end
			if not use.from:hasSkill("luamengyan") then return end
			if use.card then
				room:writeToConsole("nue testQQQ" ..  use.card:getId())
				local boolU = true
				if use.card:getSubcards():length() > 0 then
					for _,id in sgs.qlist(use.card:getSubcards()) do
						if (player:getMark("meiyin" .. id) == 0) then
							boolU = false
						end
					end
				else
					boolU = false
				end
				if (player:getMark("meiyin" .. use.card:getId()) > 0) or boolU
						or (use.from:objectName() == use.to:at(0):objectName() and room:getCardPlace(use.card:getEffectiveId()) == sgs.Player_PlaceHand) then
					room:writeToConsole("nue testT" ..  use.card:getId())
					for i = 1,500 do
						room:setPlayerMark(player, "meiyin" .. i, 0)
					end
					local useto = use.to

					for _, target in sgs.qlist(useto) do
						room:setPlayerFlag(target, "luamengyanF")
					end 
					room:setPlayerFlag(player, "luamengyanF")

					local discard_ids = room:getDrawPile()
					local card_ids = sgs.IntList()
					card_ids:append(room:getDrawPile():first())
					room:fillAG(card_ids)
					room:getThread():delay(1500) 
					room:clearAG()

					if not (room:askForUseCard(player, "@@luamengyan", "@luamengyan")) then
						local card = sgs.Sanguosha:getCard(room:getDrawPile():first())
						if not qiangxingshiyongtttt(card, sgs.QList2Table(use.to), player, 1) then
							local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, player:objectName())
							room:moveCardTo(card, player, nil, sgs.Player_DiscardPile, reason, true) 
						end 
					end 
					if use.card:isKindOf("EquipCard") or not use.card:isNDTrick() then
						local reasonR = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_RULEDISCARD, player:objectName())
						room:moveCardTo(use.card, target, nil, sgs.Player_DiscardPile, reasonR, true)
					else
						use.to = sgs.SPlayerList()
						data:setValue(use)
					end					
					for _, target in sgs.qlist(useto) do
						room:setPlayerFlag(target, "-luamengyanF")
					end 
					return true
				end
				for i = 1,500 do
					room:setPlayerMark(player, "meiyin" .. i, 0)
				end
			end
		else
			local move = data:toMoveOneTime()
			if move.from and move.from:objectName() and move.from:objectName() == player:objectName() then
				local reason = move.reason.m_reason
				local reasonx = bit32.band(reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)
				local Yes = reasonx == sgs.CardMoveReason_S_REASON_USE
				if Yes and card:hasFlag("luayaoyunF") then
					local i = 0
					for _,id in sgs.qlist(move.card_ids) do
						card = sgs.Sanguosha:getCard(id)
						if move.from_places:at(i) == sgs.Player_PlaceHand then
							room:writeToConsole("nue testQ" .. id)
							player:addMark("meiyin" .. id)
						end
						i = i + 1
					end
				end
			end
		end
		return false
	end
}
luamengyan2 = sgs.CreateTriggerSkill{
	name = "#luamengyan2",
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.EventPhaseStart, sgs.PreCardUsed},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.PreCardUsed then
			local use = data:toCardUse()
			local card = use.card
			if use.from and use.from:hasSkill("luamengyan") then
				if use.from:hasFlag("meiyinAOE") then
					use.to = sgs.SPlayerList()
					for _, p in sgs.qlist(room:getAllPlayers()) do
						if p:hasFlag("meiyinAOEs") then use.to:append(p) end
					end
				end
				data:setValue(use)
				return false
			end
		else
			if event == sgs.PreCardUsed then
				if player:getPhase() == sgs.Player_Start and player:hasSkill("luayaoyun") and 
					player:getMark("@luayaoyunA") > 0 then
					room:setPlayerMark(source, "@luayaoyunA", 0) 
					room:setPlayerMark(source, "@luayaoyunB", 1)  
					if player:hasSkill("luamengyan") then room:detachSkillFromPlayer(player, "luamengyan") end 
					for _, p in sgs.qlist(room:getAllPlayers()) do
						for _,c in sgs.qlist(p:getHandcards()) do 
							room:setCardFlag(c:getEffectiveId(), "-luayaoyunF")
						end 
					end 
				end
			end 
		end
	end,
	can_trigger = function(self, target)
		return target
	end,
}

nue:addSkill(luasheji)
nue:addSkill(luasheji2)
nue:addSkill(luayaoyun)

nueA:addSkill(luasheji)
nueA:addSkill(luasheji2)
nueA:addSkill(luayaoyun)
 
ceshi:addSkill(luamengyan)
ceshi:addSkill(luamengyan2)
 

LuaLeishiCard = sgs.CreateSkillCard{
	name = "LuaLeishi",
	target_fixed = false,  --slashTargetFixToOne
	will_throw = false,
	handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		if to_select:objectName() == sgs.Self:objectName() then return false end
		if sgs.Self:hasFlag("slashTargetFixToOne") then
			local target
			for _, p in sgs.qlist(sgs.Self:getSiblings()) do
				if p:hasFlag("SlashAssignee") then target = p end
			end
			if not target then return false end
			if to_select:objectName() ~= target:objectName() then return false end
		end
		local slash = sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_SuitToBeDecided, -1)
		slash:addSubcard(self:getSubcards():first())
		slash:setSkillName("LuaLeishi")
		slash:deleteLater()
		if #targets >= 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, sgs.Self, slash) then return false end
		return sgs.Self:canSlash(to_select, slash, true)
	end,
	on_validate = function(self, carduse)
		local source = carduse.from
		local target = carduse.to:first()
		local room = source:getRoom()
		local card = sgs.Sanguosha:getCard(self:getSubcards():first()) 
		if source:canSlash(target, nil, false) then
			local slash = sgs.Sanguosha:cloneCard("thunder_slash", card:getSuit(), card:getNumber())
			slash:setSkillName(self:objectName())
			slash:addSubcard(card:getEffectiveId())
			room:setPlayerFlag(source,"LuaLeishi_used")
			return slash
		end
	end,
	on_validate_in_response = function(self, user)
		local room = user:getRoom()
		local jink = sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_SuitToBeDecided, -1)
		jink:addSubcard(self:getSubcards():first())
		room:setPlayerFlag(user,"LuaLeishi_used")
		local str = "LuaLeishi" .. tostring(self:getSubcards():first())
		room:setPlayerMark(user, str, 1)
		jink:setSkillName(self:objectName())
		return jink
	end
}
LuaLeishi = sgs.CreateOneCardViewAsSkill{
	name = "LuaLeishi",
	--filter_pattern = ".|.|.|hand",
	view_filter = function(self, card)
		if card:isEquipped() then return false end
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			local slash = sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_SuitToBeDecided, -1)
			slash:addSubcard(card:getEffectiveId())
			slash:deleteLater()
			return slash:isAvailable(sgs.Self)
		end
		return true
	end,
	view_as = function(self, originalCard)
		local Leishi_card = LuaLeishiCard:clone()
		Leishi_card:addSubcard(originalCard:getId())
		return Leishi_card
	end,
	enabled_at_play = function(self, player)
		return sgs.Slash_IsAvailable(player) and not player:hasFlag("LuaLeishi_used")
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "slash" and not player:hasFlag("LuaLeishi_used")
	end
}

LuaLeishi2 = sgs.CreateTriggerSkill{
	name = "#LuaLeishi" ,
	global = true,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local toziko = player:getRoom():findPlayerBySkillName("Luashenyi")
		if toziko and toziko:hasFlag("LuaLeishi_used") then
			room:setPlayerFlag(toziko, "-LuaLeishi_used")
			--room:writeToConsole("回合结束")
		end
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}

Luayuanxing = sgs.CreateTriggerSkill{
	name = "Luayuanxing",
	events = sgs.DamageCaused,
	global = true,
	limit_mark = "@yuanxing",
	frequency = sgs.Skill_Limited,
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local room = player:getRoom()
		for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
			if damage.to and damage.to:isAlive() and damage.from:hasSkill("Luayuanxing")
					and p:getMark("@yuanxing") > 0 and damage.nature == sgs.DamageStruct_Thunder and room:askForSkillInvoke(p, self:objectName(), data) then
				damage.damage = damage.damage + 1
				room:removePlayerMark(p,"Luayuanxing")
				data:setValue(damage)
				p:loseMark("@yuanxing")
			end
		end
	end,
	can_trigger = function(self, target)
		return target and target:isAlive()
	end
}
Luayuanlingcard = sgs.CreateSkillCard{
	name = "Luayuanling",
	filter = function(self, targets, to_select)
		local x = sgs.Self:getMark("Luayuanling")
		return (#targets <= x) and not to_select:isChained()
	end,
	on_effect = function(self,effect)
		local to  = effect.to
		local room = to:getRoom()
		if not to:isChained() then
			room:setPlayerProperty(to, "chained", sgs.QVariant(true))
			room:setEmotion(to, "chain")
		else
			room:setPlayerProperty(to, "chained", sgs.QVariant(false))
			room:setEmotion(to, "chain")
		end
	end,

}
LuayuanlingVS = sgs.CreateViewAsSkill{
	name = "Luayuanling",
	n = 0,
	view_filter = function()
		return false
	end,
	view_as = function()
		return Luayuanlingcard:clone()
	end,
	enabled_at_play = function()
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@Luayuanling"
	end,

}
Luayuanling = sgs.CreateTriggerSkill{
	name = "Luayuanling",
	frequency = sgs.Skill_Compulsory, --Frequent, NotFrequent, Compulsory, Limited, Wake
	events = {sgs.EventPhaseEnd},
	view_as_skill = LuayuanlingVS,
	on_trigger = function(self, triggerEvent, player, data)
		if triggerEvent == sgs.EventPhaseEnd then
			if player and player:isAlive() and player:hasSkill(self:objectName()) and player:getPhase() == sgs.Player_Play then
				local ccan = false
				local room = player:getRoom()
				for _,p in sgs.qlist(room:getAlivePlayers()) do
					if not p:isChained() then ccan = true end
				end
				if not ccan then return false end
				local x = player:getMark("Luayuanling")
				if x and (x >= 0) and room:askForUseCard(player, "@@Luayuanling", "@Luayuanling" , -1, sgs.Card_MethodNone) then
				end
				room:setPlayerMark(player, "Luayuanling", 0)
			end
		end
		return false
	end,
	can_trigger = function(self, target)
		return target
	end,
}
Luayuanling2 = sgs.CreateTriggerSkill{
	name = "#Luayuanling",
	frequency = sgs.Skill_Compulsory, --Frequent, NotFrequent, Compulsory, Limited, Wake
	events = {sgs.DamageDone},
	on_trigger = function(self, triggerEvent, player, data)
		if triggerEvent == sgs.DamageDone then
			local damage = data:toDamage()
			if damage.from and damage.from:getPhase() == sgs.Player_Play and damage.from:hasSkill("Luayuanling") and damage.nature == sgs.DamageStruct_Thunder then
				local room = damage.from:getRoom()
				local x = damage.from:getMark("Luayuanling")
				if (not x) or (x <= 0) then
					x = 0
				end
				room:setPlayerMark(damage.from, "Luayuanling", x+damage.damage)
			end
		end
	end,
	can_trigger = function(self, target)
		return target
	end,
}
Luashenyi2 = sgs.CreateMasochismSkill{
	name = "#Luashenyi",
	on_damaged = function(self,player, damage)
		if not player:hasSkill("Luashenyi") then return false end
		if damage.damage == 0 then return false end
		local room = player:getRoom()
		room:setPlayerFlag(player, "Luashenyi")
		-- local nextphase = change.to
		-- room:setPlayerMark(player, "qiaobianPhase", nextphase)
	end,
	can_trigger = function(self, target)
		return target and target:hasSkill("Luashenyi")
	end
}
Luashenyi = sgs.CreateTriggerSkill{
	name = "Luashenyi" ,
	events = {sgs.EventPhaseChanging} ,
	--frequency = sgs.Skill_Frequent , 这句话源代码没有，但是我感觉应该加上，毕竟连破一点副作用都没有
	priority = 1,
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		local toziko = player:getRoom():findPlayerBySkillName("Luashenyi")
		if not toziko then return false end
		if not toziko:hasFlag("Luashenyi") then return false end
		if not toziko:askForSkillInvoke("Luashenyi") then
			local room = toziko:getRoom()
			room:setPlayerFlag(toziko, "-Luashenyi")
			return false
		end
		local room = toziko:getRoom()
		local p = room:askForPlayerChosen(toziko, room:getAlivePlayers(), self:objectName(), "Luashenyito", true)
		local playerdata = sgs.QVariant()
		playerdata:setValue(p)
		player:getRoom():setTag("LuashenyiInvoke", playerdata)
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end
}
LuashenyiDo = sgs.CreateTriggerSkill{
	name = "#Luashenyi-do" ,
	events = {sgs.EventPhaseStart},
	priority = 1 ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local toziko = player:getRoom():findPlayerBySkillName("Luashenyi")
		if room:getTag("LuashenyiInvoke") then
			local target = room:getTag("LuashenyiInvoke"):toPlayer()
			room:removeTag("LuashenyiInvoke")
			if target and target:isAlive() then
				local room_0 = target:getRoom()
				local thread = room_0:getThread()
				-- local phase_0 = sgs.QVariant()
				-- phase_0:setValue(sgs.Player_Play)
				--target:play(phase_0)
				local old_phase = target:getPhase()

				target:setPhase(sgs.Player_Play)

				room_0:broadcastProperty(target, "phase")
				if not thread:trigger(sgs.EventPhaseStart, room_0, target) then
					thread:trigger(sgs.EventPhaseProceeding, room_0, target)
				end

				thread:trigger(sgs.EventPhaseEnd, room_0, target)
				target:setPhase(old_phase)
				room_0:broadcastProperty(target, "phase")


				room_0:writeToConsole(old_phase .. "  " .. sgs.Player_NotActive)
				if toziko and toziko:hasFlag("Luashenyi") then
					room:setPlayerFlag(toziko, "-Luashenyi")
				end
			end
			return false
		end
	end,
	can_trigger = function(self, target)
		return target
	end
}


toziko:addSkill(LuaLeishi)
toziko:addSkill(LuaLeishi2)
toziko:addSkill(Luayuanxing)
toziko:addSkill(Luayuanling)
toziko:addSkill(Luayuanling2)
toziko:addSkill(Luashenyi2)
toziko:addSkill(Luashenyi)
toziko:addSkill(LuashenyiDo)

ofuda_move = sgs.CreateTriggerSkill{
	name = "#ofuda_move",
	global = true,
	events = {sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data, room)
		local change = data:toPhaseChange()
		if change.to == sgs.Player_NotActive then
			for _,p in sgs.qlist(room:getAlivePlayers()) do
				if p:getMark("@ofudaa") > 0 then
					room:setPlayerMark(p, "@ofudaa", 0)
					room:removePlayerCardLimitation(p, "use,response,discard", ".|.|.|hand" .. "$1")
				end
			end
		end
	end
}
hui_move = sgs.CreateTriggerSkill{
	name = "#hui_move",
	global = true,
	events = {sgs.BeforeCardsMove, sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data, room)
		local move = data:toMoveOneTime()
		if not move.from then return false end
		if event == sgs.CardsMoveOneTime then
			if player:getMark("hui_move") > 0 then
				room:writeToConsole("hui test y2")
				local n = player:getMark("hui_move")
				room:setPlayerMark(player, "hui_move", 0)
				if not player:hasFlag("dontEatShit") then
					for i = 1, n do
						if (player:getCardCount(true) < 2) then
							room:damage(sgs.DamageStruct(self:objectName(), player, player, 1, sgs.DamageStruct_Normal))
						else
							local choice = room:askForChoice(player, "hui", "damage+discard")
							if choice == "damage" then
								local NN = player:getTag("hui_move"):toInt() 
								if NN < 0 then 
									room:damage(sgs.DamageStruct(self:objectName(), player, player, 1, sgs.DamageStruct_Normal))
								else
									room:damage(sgs.DamageStruct(sgs.Sanguosha:getCard(NN), player, player, 1, sgs.DamageStruct_Normal))
								end 
							else
								room:askForDiscard(player, "hui", 2, 2, false, true)
							end
						end
					end
				end
			else
				player:setTag("hui_move", sgs.QVariant(-1))
			end
		end
		if event == sgs.BeforeCardsMove then
			if not move.from_places:contains(sgs.Player_PlaceHand) then return end
			if player:objectName() == move.from:objectName() then
				local n = 0
				local NN = -1
				for _, id in sgs.qlist(move.card_ids) do
					if move.open and sgs.Sanguosha:getCard(id):isKindOf("Hui") then
						n = n + 1
						NN = id
					end
				end
				if n == 0 then return false end
				if NN < 0 then return false end
				if move.to_place == sgs.Player_DiscardPile then
					local source = move.reason.m_playerId
					for _, p in sgs.qlist(room:getAlivePlayers()) do
						if p:objectName() == source then source = p;break end
					end
					room:setPlayerMark(source, "hui_move", n)
					source:setTag("hui_move", sgs.QVariant(NN))
				end
			end
		end
		return false
	end
}
--
girl_choosenDo = sgs.CreateTriggerSkill{
	name = "#girl_choosen-do" ,
	events = {sgs.EventPhaseStart},
	global = true,
	priority = -1 ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:getTag("girl_choosenInvoke") then
			local target = room:getTag("girl_choosenInvoke"):toPlayer()
			room:removeTag("girl_choosenInvoke")
			if target and target:isAlive() then
				room:writeToConsole("tokiko test F2")
				room:setPlayerMark(target, "@extra_turn", 1)
				target:gainAnExtraTurn()
				room:setPlayerMark(target, "@extra_turn", 0)
			end
			return false
		end
		return false
	end,
	can_trigger = function(self, target)
		return target and (target:getPhase() == sgs.Player_NotActive)
	end
}
girl_choosen3 = sgs.CreateTriggerSkill{
	name = "#girl_choosen3" ,
	events = {sgs.EventPhaseChanging} ,
	global = true,
	--frequency = sgs.Skill_Frequent , 这句话源代码没有，但是我感觉应该加上，毕竟连破一点副作用都没有
	priority = -1,
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		if change.to ~= sgs.Player_NotActive then return false end
		local shensimayi
		for _, p in sgs.qlist(player:getRoom():getAlivePlayers()) do
			if p:getMark("girl_choosenA") > 0 then shensimayi = p end
		end
		if not shensimayi or shensimayi:getMark("girl_choosenA") == 0 then return false end
		shensimayi:setMark("girl_choosenA",0)
		player:getRoom():writeToConsole(" tokiko test F")
		local p = shensimayi
		local playerdata = sgs.QVariant()
		playerdata:setValue(p)
		player:getRoom():setTag("girl_choosenInvoke", playerdata)
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end
}
need_maribel2 = sgs.CreateTriggerSkill{
	name = "#need_maribel" ,
	frequency = sgs.Skill_Compulsory ,
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		if change.to == sgs.Player_Discard and player:hasFlag("need_maribel") then player:skip(change.to); player:getRoom():setPlayerFlag(player, "-need_maribel") end
		return false
	end
}
ceshi:addSkill(hui_move)
ceshi:addSkill(ofuda_move)
ceshi:addSkill(girl_choosen3)
ceshi:addSkill(girl_choosenDo)
ceshi:addSkill(need_maribel2)

luajingxia = sgs.CreateTriggerSkill{
	name = "luajingxia",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Damaged, sgs.Damage},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if not damage.from then return end
		local target
		if player:objectName() == damage.from:objectName() then target = damage.to end
		if player:objectName() == damage.to:objectName() then target = damage.from end
		if not target:isAlive() then return end
		if damage.damage == 0 then return end 
		if not player:hasFlag("luajingxia") and room:askForSkillInvoke(player, self:objectName(), data) then
			room:setPlayerFlag(player, "luajingxia")
			if not player:isAlive() then return end
			local ids_A = sgs.IntList()
			for _,equip in sgs.qlist(target:getEquips()) do
				if equip:isKindOf("EquipCard") then
					ids_A:append(equip:getId())
				end
			end
			for _, cardD in sgs.qlist(target:getHandcards()) do
				if cardD:isKindOf("EquipCard") then
					ids_A:append(cardD:getId())
				end
			end
			if ids_A:isEmpty() then
				target:drawCards(1) ;target:turnOver()
				if not target:getEquips() then target:drawCards(1) end
				if target:getEquips():isEmpty() then target:drawCards(1) end
				return
			end
			local choice = room:askForChoice(target, self:objectName(), "giveEquip+turnOver")
			if choice == "turnOver" then
				target:drawCards(1) ;target:turnOver()
				if not target:getEquips() then target:drawCards(1) end
				if target:getEquips():isEmpty() then target:drawCards(1) end
			else
				room:fillAG(ids_A, player)
				local _data = sgs.QVariant()
				_data:setValue(player)
				player:setTag("luajingxiaTP", _data)
				local card_id = room:askForAG(player, ids_A, false, "luajingxia")
				player:removeTag("luajingxiaTP")
				local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				dummy_0:addSubcard(card_id)
				room:clearAG()
				room:obtainCard(player, dummy_0, sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_ROB, player:objectName()))
			end

		end
		return false
	end
}

luajingxia2 = sgs.CreateTriggerSkill{
	name = "#luajingxia" ,
	global = true,
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		if event == sgs.EventPhaseChanging then
			local room = player:getRoom()
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				local toziko = player:getRoom():findPlayerBySkillName("luajingxia") 
				if toziko and toziko:hasFlag("luajingxia") then
					room:setPlayerFlag(toziko, "-luajingxia")
				end
			end
		end
	end
}


luajinglianCard = sgs.CreateSkillCard{
	name = "luajinglian" ,
	will_throw = false,
	filter = function(self, targets, to_select)
		if #targets > 0 then return false end
		return true
	end ,
	feasible = function(self, targets)
		return #targets == 1 --and (targets[1]:getHp() < targets[1]:getRoom():getLord():getMark("@clock_time"))
	end ,
	on_effect = function(self, effect)
		local room = effect.to:getRoom()
		effect.to:drawCards(1)
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
		local dummy_0 = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
		dummy_0:addSubcards(self:getSubcards())
		effect.to:obtainCard(dummy_0)
		if not effect.to:isCardLimited(slash, sgs.Card_MethodUse) and not effect.to:isCardLimited(sgs.Sanguosha:getCard(self:getSubcards():at(0)), sgs.Card_MethodUse) then
			if sgs.Sanguosha:getCard(self:getSubcards():at(0)):isAvailable(effect.to) then
				room:useCard(sgs.CardUseStruct(sgs.Sanguosha:getCard(self:getSubcards():at(0)), effect.to, effect.to))
				local cardp = room:askForCard(effect.to, ".", "@luajinglian", sgs.QVariant(), sgs.Card_MethodNone)
				if not cardp then return false end
				slash:addSubcard(cardp)
				local targets_list = sgs.SPlayerList()
				for  _, target in sgs.qlist(room:getAlivePlayers()) do
					if effect.to:canSlash(target, slash, false) then
						targets_list:append(target)
					end
				end
				if targets_list:length() > 0 then
					local Carddata2 = sgs.QVariant() -- ai用
					Carddata2:setValue(cardp)
					room:setTag("luajinglianTC", Carddata2)
					local target = room:askForPlayerChosen(effect.to, targets_list, "luajinglian", "@luajinglian2", false)
					room:removeTag("luajinglianTC")
					if target then
						slash:setSkillName(self:objectName())
						room:useCard(sgs.CardUseStruct(slash, effect.to, target))
					end
				end
			end
		end 
	end
}

luajinglian = sgs.CreateOneCardViewAsSkill{
	name = "luajinglian",
	view_filter = function(self, card)
		return card:isKindOf("EquipCard") --and (not card:isEquipped())
	end,
	view_as = function(self, originalCard)
		local Leishi_card = luajinglianCard:clone()
		Leishi_card:addSubcard(originalCard:getId())
		return Leishi_card
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#luajinglian")
	end,
}
tatara:addSkill(luajingxia2)
tatara:addSkill(luajingxia)
tatara:addSkill(luajinglian)
LuazonghuoCard = sgs.CreateSkillCard{
	name = "Luazonghuo" ,
	filter = function(self, targets, to_select)
		if #targets > 0 then return false end
		return not to_select:isNude()
	end ,
	feasible = function(self, targets)
		return #targets == 1 --and (targets[1]:getHp() < targets[1]:getRoom():getLord():getMark("@clock_time"))
	end ,
	on_effect = function(self, effect)
		local room = effect.to:getRoom()
		local id1 = room:askForCardChosen(effect.from, effect.to, "he", self:objectName(), false)
		local carde = sgs.Sanguosha:getCard(id1)
		local acard = sgs.Sanguosha:cloneCard("fire_slash", carde:getSuit(), carde:getNumber())
		acard:addSubcard(carde)
		--if effect.to:canSlash(effect.to, acard) then
			room:useCard(sgs.CardUseStruct(acard, effect.to, effect.to))
		--end
	end
}
Luazonghuo = sgs.CreateViewAsSkill{
	name = "Luazonghuo" ,
	n = 0 ,
	view_as = function(self, cards)
		if #cards > 0 then return nil end
		local first = LuazonghuoCard:clone()
		return first
	end ,
	enabled_at_play = function(self, player)
		return (not player:hasUsed("#Luazonghuo"))
	end
}

LuaFengshui = sgs.CreateTriggerSkill{
	name = "LuaFengshui",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageForseen},
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		if not damage.from then return false end
		local bool0 = ((damage.nature ~= sgs.DamageStruct_Thunder) and (damage.nature ~= sgs.DamageStruct_Fire))
		local bool1 = ((damage.from:getHp() == player:getHp()) and bool0)
		local bool2 = ((damage.from:getHp() ~= player:getHp()) and not bool0)
		return bool1 or bool2
	end,
	can_trigger = function(self, target)
		return target:hasSkill("LuaFengshui")
	end
}
futo:addSkill(Luazonghuo)
futo:addSkill(LuaFengshui)

futoA:addSkill(Luazonghuo)
futoA:addSkill(LuaFengshui)

futoB:addSkill(Luazonghuo)
futoB:addSkill(LuaFengshui)

futoC:addSkill(Luazonghuo)
futoC:addSkill(LuaFengshui)


luaweiguangCard = sgs.CreateSkillCard{
	name = "luaweiguang",
	will_throw = false,
	target_fixed = true,
	on_use = function(self, room, source, targets)
		if source:getMark("@luaweiguangA") > 0 then  --有A标对应不能出杀
			room:setPlayerMark(source, "@luaweiguangA", 0)
			room:setPlayerMark(source, "@luaweiguangB", 1)
		else
			room:setPlayerMark(source, "@luaweiguangB", 0)
			room:setPlayerMark(source, "@luaweiguangA", 1)
		end
		local ids_A = sgs.IntList()
		for _,card in sgs.qlist(source:getHandcards()) do
			ids_A:append(card:getId())
		end
		local throwIds = sgs.IntList()
		room:fillAG(ids_A, source)
		local card_id = room:askForAG(source, ids_A, false, self:objectName())
		ids_A:removeOne(card_id)
		local cardA = sgs.Sanguosha:getCard(card_id)
		local suit = cardA:getSuit()
		throwIds:append(card_id)
		for _,id in sgs.qlist(ids_A) do
			local c = sgs.Sanguosha:getCard(id)
			if c:getSuit() == suit then
				throwIds:append(id)
			end
		end
		room:broadcastInvoke("clearAG")
		room:clearAG()
		local x = throwIds:length()
		local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		dummy:addSubcards(throwIds)
		room:throwCard(dummy, sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_NATURAL_ENTER, source:objectName(), self:objectName(), ""), nil)
		room:setPlayerMark(source, "@luaweiguangC", 1)

		if x > 1 then
			local target = room:askForPlayerChosen(source, room:getAlivePlayers(), self:objectName(), "luaweiguangQ")
			--任意锦囊牌
			local type = {}
			local sttrick = {}
			local mttrick = {}
			local patterns = {"snatch", "dismantlement", "collateral", "ex_nihilo", "duel", "fire_attack", "amazing_grace",
							  "savage_assault", "archery_attack", "god_salvation", "iron_chain", "lightning"}
			if not (Set(sgs.Sanguosha:getBanPackages()))["pay9"] then
				table.insert(patterns, 2, "faith_collection")
			end
			for _, cd in ipairs(patterns) do
				local cardX = sgs.Sanguosha:cloneCard(cd, sgs.Card_NoSuit, 0)
				cardX:setSkillName("luaweiguang")
				local qtargets = sgs.PlayerList()
				if cardX and cardX:targetFilter(qtargets, target, source) and not source:isProhibited(target, cardX, qtargets) then
					qtargets:append(target)
					cardX:deleteLater()
					if cardX:isAvailable(source) and cardX:targetsFeasible(qtargets, source) then
						if (cardX:isKindOf("SingleTargetTrick") and not cardX:isKindOf("DelayedTrick")) then
							table.insert(sttrick, cd)
						else
							table.insert(mttrick, cd)
						end
					end
				end
			end
			if #sttrick ~= 0 then table.insert(type, "single_target_trick") end
			if #mttrick ~= 0 then table.insert(type, "multiple_target_trick") end
			local typechoice = ""
			if #type > 0 then
				typechoice = room:askForChoice(source, "luaweiguang", table.concat(type, "+"))
			end
			local choices = {}
			if typechoice == "single_target_trick" then
				choices = sttrick
			elseif typechoice == "multiple_target_trick" then
				choices = mttrick
			end
			local pattern_0 = room:askForChoice(source, "luaweiguang", table.concat(choices, "+"))
			if pattern_0 then
				local card_X = sgs.Sanguosha:cloneCard(pattern_0, sgs.Card_NoSuit, 0)
				card_X:setSkillName("luaweiguang")
				if card_X:isKindOf("AOE") or card_X:isKindOf("AmazingGrace") or card_X:isKindOf("GodSalvation") then
					room:setPlayerFlag(source, "qucaiAOE")
					room:setPlayerFlag(target, "qucaiAOEs")
					card_X:setSkillName("luaweiguang")
					room:useCard(sgs.CardUseStruct(card_X, source, sgs.SPlayerList()))
					room:setPlayerFlag(source, "-qucaiAOE")
					room:setPlayerFlag(target, "-qucaiAOEs")
				else
					local targets_list = sgs.SPlayerList()
					targets_list:append(target)
					room:useCard(sgs.CardUseStruct(card_X, source, targets_list))

				end
				if not target:isAlive() then return true end  --非常漂亮!
			end
		end
	end
}
luaweiguang2 = sgs.CreateTriggerSkill{
	name = "#luaweiguang",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.ConfirmDamage},
	can_trigger = function(self, player)
		return player
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.ConfirmDamage then
			local damage = data:toDamage()
			if damage.from and damage.from:getMark("@luaweiguangC") > 0 and damage.to and damage.to:isAlive()
				and damage.card and damage.card:isKindOf("Slash") then
				damage.damage = damage.damage + 1
				data:setValue(damage)
				room:broadcastSkillInvoke("luaweiguang", 1)
				room:setPlayerMark(damage.from, "@luaweiguangC", 0)
				return false
			end
		end
	end
}
luaweiguang = sgs.CreateZeroCardViewAsSkill{
	name = "luaweiguang",
	view_as = function(self, cards)
		return luaweiguangCard:clone()
	end,
	enabled_at_play = function(self, player)  --sgs.Slash_IsAvailable(source) and source:canSlash(targets[1], nil, false)
		local IfCanSlash = false
		for _, p in sgs.qlist(player:getSiblings()) do
			if player:canSlash(p, nil, false) then IfCanSlash = true end
		end
		if not sgs.Slash_IsAvailable(player) then IfCanSlash = false end
		return not player:isKongcheng() and ((IfCanSlash and player:getMark("@luaweiguangA") == 0) or (not IfCanSlash and player:getMark("@luaweiguangA") > 0))
	end,
}

luafadeng = sgs.CreateTriggerSkill{
	name = "luafadeng" ,
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseEnd} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Play and not player:hasUsed("Slash") and room:askForSkillInvoke(player, self:objectName(), data) then
			local target = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "luafadeng")
			local suit = room:askForSuit(player, "luafadeng")
			local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			local i = 0
			for _, id in sgs.qlist(room:getDrawPile()) do
				local Acard = sgs.Sanguosha:getCard(id)
				if Acard:getSuit() == suit then
					i = i + 1
					dummy:addSubcard(id)
				end
				if i == 2 then break end
			end
			room:obtainCard(target, dummy)
		end
	end
}
syou:addSkill(luaweiguang)
syou:addSkill(luaweiguang2)
syou:addSkill(luafadeng)

--[[
const Card *Room::askForUseSlashTo(ServerPlayer *slasher, QList<ServerPlayer *> victims, const QString &prompt, bool distance_limit, bool disable_extra, bool addHistory)
]]
Luaduzhua = sgs.CreateTriggerSkill{
	name = "Luaduzhua",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		if player:getPhase() == sgs.Player_Play then
			local room = player:getRoom()
			local playerlist = sgs.SPlayerList()
			for _, p in sgs.qlist(room:getOtherPlayers(player)) do
				if not p:hasSkill("Luaganran") then
					playerlist:append(p)
				end
			end
			local use_slash = room:askForUseSlashTo(player, playerlist, "LuaduzhuaA", true, true, true)
			if not use_slash then
				room:askForDiscard(player, self:objectName(), 1, 1, false, true)
			end
		end
	end
}
Luaganran = sgs.CreateTriggerSkill{
	name = "Luaganran",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Damage},
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local room = player:getRoom()
		local target = damage.to
		if damage.card and damage.card:isKindOf("Slash")
			and (target:objectName() ~= player:objectName() and (not damage.chain) and (not damage.transfer)) and (not target:hasSkill("Luaduzhua")) then
			if room:askForSkillInvoke(player, self:objectName(), data) then
				room:acquireSkill(target, "Luaduzhua")
			end
		end
		return false
	end
}

LuabaigeCard = sgs.CreateSkillCard{
	name = "Luabaige",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select)
		if to_select:objectName() == sgs.Self:objectName() then return false end
		if #targets~=0 then return false end
	return sgs.Self:inMyAttackRange(to_select)
	end,
	on_validate = function(self,carduse)
		local source = carduse.from
		local target = carduse.to:first()
		local room = source:getRoom()
		if source:canSlash(target, nil, false) then
			room:showAllCards(source)
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			slash:setSkillName(self:objectName())
			room:setPlayerFlag(source,"Luabaige_used")
			return slash
		end
	end,
}
Luabaige = sgs.CreateViewAsSkill{
	name = "Luabaige",
	n = 0,
	view_filter = function(self, card)
    	return true
	end,
	view_as = function(self, originalCard)
		return LuabaigeCard:clone()
	end,
	enabled_at_play = function(self, player)
		if player:isKongcheng() then return false end
		if player:hasFlag("Luabaige_used") then return false end
		for _, card in sgs.qlist(player:getHandcards()) do
			if card:isKindOf("Slash") then return false end
		end
		return sgs.Slash_IsAvailable(player)
	end,
	enabled_at_response = function(self, player, pattern)
		if player:isKongcheng() then return false end
		if player:hasFlag("Luabaige_used") then return false end
		for _, card in sgs.qlist(player:getHandcards()) do
			if card:isKindOf("Slash") then return false end
		end
		return pattern == "slash"
	end
}

Luabaige2 = sgs.CreateTriggerSkill{
	name = "#Luabaige2" ,
	global = true,
	events = {sgs.EventPhaseEnd} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local yoshika = player:getRoom():findPlayerBySkillName("Luabaige")
		if yoshika and yoshika:hasFlag("Luabaige_used") and player:getPhase() == sgs.Player_Play then
			room:setPlayerFlag(yoshika, "-Luabaige_used")
			room:removeTag("Luabaige")
			--room:writeToConsole("回合结束")
		end
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}
yoshika:addSkill(Luaduzhua)
yoshika:addSkill(Luaganran)
yoshika:addSkill(Luabaige)
yoshika:addSkill(Luabaige2)

LuaJubian = sgs.CreateTriggerSkill{
	name = "LuaJubian",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damage, sgs.PreDamageDone},
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local room = player:getRoom()
		if (event == sgs.PreDamageDone) and damage.from and damage.from:hasSkill(self:objectName()) and damage.from:isAlive()
				and (damage.nature == sgs.DamageStruct_Fire) then
			local weiyan = damage.from
			weiyan:setTag("invokeLuaJubian", sgs.QVariant(true))
		elseif (event == sgs.Damage) and player:hasSkill(self:objectName()) and player:isAlive() then
			local invoke = player:getTag("invokeLuaJubian"):toBool()
			player:setTag("invokeLuaJubian", sgs.QVariant(false))
			if invoke then
				player:drawCards(1)
			end
		end
		return false
	end,
	can_trigger = function(self, target)
		return target
	end
}
ceshi:addSkill(LuaJubian)
LuaGuanweiCard = sgs.CreateSkillCard{
	name = "LuaGuanwei" ,
	filter = function(self, targets, to_select)
		if self:getSubcards():length() == 0 then
			return to_select:objectName() ~= sgs.Self:objectName() and (#targets == 0)
		end
		return (#targets == 0)
	end ,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		if self:getSubcards():length() == 0 then
			effect.from:drawCards(1)
			room:damage(sgs.DamageStruct(self:objectName(), effect.from, effect.to))
			room:recover(effect.to, sgs.RecoverStruct(effect.from, nil, 1))
		else
			local choices = {"Player_Start", "Player_Judge", "Player_Draw", "Player_Play", "Player_Discard", "Player_Finish"}
			local playerdata = sgs.QVariant() -- ai用
			playerdata:setValue(effect.to)
			room:setTag("LuaGuanwei", playerdata)
			local choice = room:askForChoice(effect.from, "LuaGuanwei", table.concat(choices, "+")) 
			room:removeTag("LuaGuanwei")
			local roomx = effect.to:getRoom()
			effect.to:setTag("LuaGuanwei", sgs.QVariant(choice))

			roomx:setPlayerFlag(effect.to, "LuaGuanwei")
		end
	end
}
luaguanwei2 = sgs.CreateTriggerSkill{
	name = "#luaguanwei" ,
	global = true,
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			if p:getTag("LuaGuanwei") and p:getTag("LuaGuanwei"):toString() ~= "" then
				local roomx = p:getRoom()
				local thread = roomx:getThread()
				local choice = p:getTag("LuaGuanwei"):toString()
				p:removeTag("LuaGuanwei")
				roomx:writeToConsole("guanwei" .. choice)
				local old_phase = p:getPhase()
				if choice == "Player_Start" then
					p:setPhase(sgs.Player_Start)
				elseif choice == "Player_Judge" then
					p:setPhase(sgs.Player_Judge)
				elseif choice == "Player_Draw" then
					p:setPhase(sgs.Player_Draw)
				elseif choice == "Player_Play" then
					p:setPhase(sgs.Player_Play)
				elseif choice == "Player_Discard" then
					p:setPhase(sgs.Player_Discard)
				elseif choice == "Player_Finish" then
					p:setPhase(sgs.Player_Finish)
				end
				roomx:broadcastProperty(p, "phase")
				if not thread:trigger(sgs.EventPhaseStart, roomx, p) then
					thread:trigger(sgs.EventPhaseProceeding, roomx, p)
				end
				thread:trigger(sgs.EventPhaseEnd, roomx, p)
				p:setPhase(old_phase)
				roomx:broadcastProperty(p, "phase")
			end
		end
	end
}

LuaGuanweiVS = sgs.CreateViewAsSkill{
	name = "LuaGuanwei" ,
	n = 1,
	view_filter = function(self, selected, to_select)
		return (not sgs.Self:isJilei(to_select))
	end ,
	view_as = function(self, cards)
		local card = LuaGuanweiCard:clone()
		if #cards > 0 then
			card:addSubcard(cards[1])
		end
		return card
	end ,
	enabled_at_play = function()
		return false
	end,
	enabled_at_response = function(self, player, pattern)
        return pattern == "@@LuaGuanwei"
	end,
}

LuaGuanwei = sgs.CreateTriggerSkill{
	name = "LuaGuanwei",
	frequency = sgs.Skill_Frequent,
	events = {sgs.Damaged},
	view_as_skill = LuaGuanweiVS,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		local x = damage.damage
		for i = 0, x - 1, 1 do
			if room:askForUseCard(player, "@@LuaGuanwei", "@LuaGuanwei") then

			end
		end
	end,
}

luawangshiCard = sgs.CreateSkillCard{
	name = "luawangshi" ,
	filter = function(self, targets, to_select)
		return (#targets == 0) and (to_select:objectName() ~= sgs.Self:objectName())
	end,
	on_effect = function(self, effect)
		local room = effect.to:getRoom()

		local playerdata = sgs.QVariant() --ai用
		playerdata:setValue(effect.from)
		room:setTag("Jiantingsource", playerdata)

		local playerdataQ = sgs.QVariant() --ai用
		playerdataQ:setValue(effect.to)
		room:setTag("Jiantingsource3", playerdataQ)
		local playerlist = sgs.SPlayerList()

		for _, p in sgs.qlist(room:getAlivePlayers()) do
			if p:objectName() ~= effect.to:objectName() then playerlist:append(p) end
		end
		local target = room:askForPlayerChosen(effect.from, playerlist, "luawangshi", "luawangshiS", false, true)

		local playerdataW = sgs.QVariant() --ai用
		playerdataW:setValue(target)
		room:setTag("luawangshiz", playerdataW)

		if not target then return end
		local card = room:askForUseSlashTo(effect.to, target, "luawangshiz", false, false, false)
		if card then
			-- local reasonG = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_GIVE, effect.to:objectName(), effect.from:objectName(), self:objectName(), "")
			-- room:moveCardTo(card, effect.to, effect.from, sgs.Player_PlaceHand, reasonG, false)
			effect.to:drawCards(1)
		else
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			slash:setSkillName(self:objectName())
			local use = sgs.CardUseStruct()
			use.card = slash
			use.from = effect.from
			use.to:append(effect.to)
			room:useCard(use)
		end
		room:removeTag("Jiantingsource")
		room:removeTag("luawangshiz")
		room:removeTag("Jiantingsource3")
	end
}

luawangshi = sgs.CreateZeroCardViewAsSkill{
	name = "luawangshi" ,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#luawangshi")
	end ,
	view_as = function()
		return luawangshiCard:clone()
	end
}

LuaJianting = sgs.CreateGameStartSkill{
	name = "LuaJianting$" ,
	on_gamestart = function(self, player)
		local room = player:getRoom()
		if player:hasLordSkill(self:objectName()) then
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if (not p:hasLordSkill(self:objectName())) and not p:hasSkill("LuaJiantingAsk") then room:attachSkillToPlayer(p, "LuaJiantingAsk") end
			end
		end
	end ,
}

LuaJiantingCard = sgs.CreateSkillCard{
	name = "LuaJianting" ,
	will_throw = false ,
	handling_method = sgs.Card_MethodNone ,
	filter = function(self, selected, to_select)
		return (#selected == 0) and to_select:hasLordSkill("LuaJianting")
	end ,
	on_use = function(self, room, source, targets)
		local target = targets[1]
		room:obtainCard(target, self, false)
		local x = self:getSubcards():length()
		if x > 0 then
			local playerdata = sgs.QVariant()
			playerdata:setValue(source)
			target:getRoom():setTag("LuaJiantingsource", playerdata)
			local to_exchange = room:askForExchange(target, "LuaJianting", x, x)
			room:moveCardTo(to_exchange, source, sgs.Player_PlaceHand, false)
			target:getRoom():removeTag("LuaJiantingsource")
		end
	end
}
LuaJiantingAsk = sgs.CreateViewAsSkill{
	name = "LuaJiantingAsk&" ,
	n = 999 ,
	view_filter = function(self, selected, to_select)
		local y = 0
		for _,p in sgs.qlist(sgs.Self:getAliveSiblings()) do
			if p:isAlive() then y = y + 1 end
		end
		y = y / 3
		return #selected <= y
	end ,
	view_as = function(self, cards)
		if #cards == 0 then return nil end
		local jianting_card = LuaJiantingCard:clone()
		for _, c in ipairs(cards) do
			jianting_card:addSubcard(c)
		end
		return jianting_card
	end ,
	enabled_at_play = function(self, player)
		return not player:isKongcheng() and not player:hasUsed("#LuaJianting")
	end
}
toyosatomimi:addSkill(luawangshi)
toyosatomimi:addSkill(LuaGuanwei)
toyosatomimi:addSkill(luaguanwei2)
toyosatomimi:addSkill(LuaJianting)

toyosatomimiA:addSkill(luawangshi)
toyosatomimiA:addSkill(LuaGuanwei)
toyosatomimiA:addSkill(LuaJianting)

toyosatomimiB:addSkill(luawangshi)
toyosatomimiB:addSkill(LuaGuanwei)
toyosatomimiB:addSkill(LuaJianting)

toyosatomimiC:addSkill(luawangshi)
toyosatomimiC:addSkill(LuaGuanwei)
toyosatomimiC:addSkill(LuaJianting)

toyosatomimiD:addSkill(luawangshi)
toyosatomimiD:addSkill(LuaGuanwei)
toyosatomimiD:addSkill(LuaJianting)

toyosatomimiE:addSkill(luawangshi)
toyosatomimiE:addSkill(LuaGuanwei)
toyosatomimiE:addSkill(LuaJianting)

ceshi:addSkill(LuaJiantingAsk)


luagongming = sgs.CreateTriggerSkill{
	name = "luagongming" ,
	global = true, 
	view_as_skill = luagongmingVS,
	events = {sgs.TargetConfirmed, sgs.Damage, sgs.SlashMissed} ,
	on_trigger = function(self, event, player, data)
		if event == sgs.TargetConfirmed then 
			local use = data:toCardUse()
			if not use.from then return false end
			local room = use.from:getRoom()
			if (player:objectName() ~= use.from:objectName()) or (not use.card:isKindOf("Slash")) then return false end  
			local xo = room:getLord():getMark("@clock_time") + 1
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:getMark("@luagongmingP") > 0 and p:getMark("@luagongmingP") == xo then return false end 
			end  

			for _, kyouko in sgs.qlist(room:findPlayersBySkillName("luagongming")) do 
				local pattern = ".|.|.|."
				if use.card:isRed() then
					pattern = ".|black|.|."
				elseif use.card:isBlack() then
					pattern = ".|red|.|."
				end 
				if room:askForCard(kyouko, pattern, "@luagongming", sgs.QVariant(), sgs.Card_MethodDiscard) then
					room:setCardFlag(use.card, "luagongmingA")  
					room:setPlayerMark(kyouko, "luagongmingB", kyouko:getMark("luagongmingB") + 1)
					for _, p in sgs.qlist(room:getAlivePlayers()) do 
						room:setPlayerMark(p, "@luagongmingP", 0)
					end  
					room:setPlayerMark(room:getCurrent(), "@luagongmingP", xo)
					if kyouko then
						local gainList = kyouko:getRoom():getTag("luahuishenX")
						if gainList and gainList:toString() ~= "" then 
							local resultA = string.gsub(gainList:toString(), "huishenDummy", "") 
							kyouko:getRoom():setTag("luahuishenX", sgs.QVariant(resultA))  
							kyouko:getRoom():writeToConsole("registerToKyouko " .. resultA)
						end 
					end 
				end 
			end  
		elseif event == sgs.Damage then
			local damage = data:toDamage()
			if damage.card and damage.card:isKindOf("Slash") and damage.card:hasFlag("luagongmingA") 
				and damage.from and damage.from:isAlive() then 
				local room = damage.from:getRoom()
				for _, kyouko in sgs.qlist(room:findPlayersBySkillName("luagongming")) do 
					if kyouko:getMark("luagongmingB") > 0 then
						room:setPlayerMark(kyouko, "luagongmingB", kyouko:getMark("luagongmingB") - 1)  
						local targets_list = sgs.SPlayerList()
						targets_list:append(kyouko)
						targets_list:append(damage.from)
						local targetX = room:askForPlayerChosen(kyouko, targets_list, self:objectName(), "luagongmingX", false, true)
						if targetX then 
							targets_list:append(targetX)
							targetX:drawCards(1)
						end 
					end 
				end 
			end
		elseif event == sgs.SlashMissed then
			local room = player:getRoom()
			local effect = data:toSlashEffect() 
			if effect.slash and effect.slash:hasFlag("luagongmingA") and effect.from and effect.from:isAlive() then 
				for _, kyouko in sgs.qlist(room:findPlayersBySkillName("luagongming")) do 
					if kyouko:getMark("luagongmingB") > 0 then
						room:setPlayerMark(kyouko, "luagongmingB", kyouko:getMark("luagongmingB") - 1)   
						effect.to:drawCards(1) 
					end 
				end 				
			end 
		end 
	end
}

local function huishenUse(playerX, room, objectName)
	local gainList = room:getTag("luahuishenX")
	if not gainList or gainList:toString() == "" then return false end 
	gainList = gainList:toString()
	if string.find(gainList, "huishenDummy", 1, true) then return false end 
	room:writeToConsole("huishenUse " .. gainList)
	return string.find(gainList, objectName, 1, true)
end 

huishenCard = sgs.CreateSkillCard{
	name = "luahuishen",
	will_throw = false,
	--handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		local card = sgs.Self:getTag("luahuishen")
		local response = false
		if card then 
			card = card:toCard()
		else		
			response = true			
			card = sgs.Sanguosha:cloneCard(self:getUserString())
			card = sgs.self
		end 
		if not card then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		if self:getUserString() and self:getUserString() ~= "" then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		card:addSubcards(self:getSubcards())
		card:setSkillName(self:objectName())
		if card and card:targetFixed() and not response then
			return false
		end
		if response then return true end 
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end

		return card and card:targetFilter(qtargets, to_select, sgs.Self) and not sgs.Self:isProhibited(to_select, card, qtargets)
	end,
	feasible = function(self, targets)
		local card = sgs.Self:getTag("luahuishen")
		local response = false
		if card then 
			card = card:toCard()
		else
			response = true
			card = sgs.Sanguosha:cloneCard(self:getUserString())
		end 
		if not card then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		if self:getUserString() and self:getUserString() ~= "" then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		card:addSubcards(self:getSubcards())
		card:setSkillName(self:objectName())
		--if card:isKindOf("BasicCard") then card = kcard:clone() end 
		if response then return true end 
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		return card and card:targetsFeasible(qtargets, sgs.Self)
	end,	
	on_validate = function(self, card_use)
		local use_card = sgs.Sanguosha:cloneCard(self:getUserString())
		use_card:addSubcards(self:getSubcards())
		use_card:setSkillName(self:objectName())
		local available = true
		for _,p in sgs.qlist(card_use.to) do
			if card_use.from:isProhibited(p,use_card) then
				available = false
				break
			end
		end
		if not huishenUse(card_use.from, card_use.from:getRoom(), use_card:objectName()) then return false end 
		available = available and use_card:isAvailable(card_use.from)
		if not available then return nil end
		card_use.from:getRoom():addPlayerHistory(card_use.from, "luahuishen")
		return use_card		
	end,
	on_validate_in_response = function(self, user)
		local room = user:getRoom()
		local aocaistring = self:getUserString()
		local use_card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, -1)
		if string.find(aocaistring, "+")  then
			local uses = {}
			for _, name in pairs(aocaistring:split("+"))do
				table.insert(uses, name)
			end
			local name = room:askForChoice(user, "luahuishen", table.concat(uses, "+"))
			use_card = sgs.Sanguosha:cloneCard(name, sgs.Card_NoSuit, -1)
		end
		if not huishenUse(user, room, use_card:objectName()) then return false end 
		use_card:addSubcards(self:getSubcards())
		use_card:setSkillName("luahuishen")
		room:addPlayerHistory(user, "luahuishen")
		return use_card	
	end
}
luahuishen = sgs.CreateViewAsSkill{
	name = "luahuishen",
	n = 1,
	view_filter = function(self, selected, to_select)
		return true
	end,
	view_as = function(self, cards)
		if #cards < 1 then return nil end 
		if (sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY) then			
			local c = sgs.Self:getTag("luahuishen"):toCard()
			if c then
				local card = huishenCard:clone()
				for _, acard in ipairs(cards) do
					card:addSubcard(acard)
				end			
				card:setUserString(c:objectName())	
				return card			
			end
		else
			local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
			if pattern == "slash" then 
				pattern = "slash+thunder_slash+fire_slash"
			end
			local acard = huishenCard:clone()
			if #cards ~= 1 then return end
			for _, bcard in ipairs(cards) do
				acard:addSubcard(bcard)
			end
			if pattern == "peach+analeptic"  then
				if sgs.Self:hasFlag("Global_PreventPeach") then
					pattern = "analeptic"
				else
					pattern = "peach"
				end
			end
			acard:setUserString(pattern)
			return acard		
		end 
		return nil
	end,
	enabled_at_play = function(self, player)
		return not player:isNude() and (player:usedTimes("#luahuishen") < 1)
	end,
	enabled_at_response = function(self, player, pattern)
		if player:isNude() or string.sub(pattern, 1, 1) == "." or string.sub(pattern, 1, 1) == "@" then
			return false
		end
		return (player:usedTimes("#luahuishen") < 1)
	end,	
	enabled_at_nullification = function(self, player)		
		return not player:isNude() and (player:usedTimes("#luahuishen") < 1)
	end
}
luahuishen:setGuhuoDialog("lrd")

local function registerToKyouko(room, objectName)
	for _, kyouko in sgs.qlist(room:findPlayersBySkillName("luahuishen")) do 
		local gainList = kyouko:getRoom():getTag("luahuishenX")
		if kyouko:hasSkill("luagongming") then
			if not gainList or gainList:toString() == "" then 
				local resultA = "huishenDummy" .. "+" .. objectName
				kyouko:getRoom():setTag("luahuishenX", sgs.QVariant(resultA)) 
				room:writeToConsole("registerToKyouko " .. resultA)
			else  
				if not string.find(gainList:toString(), objectName, 1, true) then 
					local resultA = gainList:toString() .. "+" .. objectName
					kyouko:getRoom():setTag("luahuishenX", sgs.QVariant(resultA)) 
					room:writeToConsole("registerToKyouko " .. resultA)
				end 
			end 
		end 
	end 
end 
luahuishen2 = sgs.CreateTriggerSkill{
	name = "#luahuishen", 
	global = true,
	frequency = sgs.Skill_Compulsory, 
	events = {sgs.CardsMoveOneTime, sgs.TurnStart}, 
	on_trigger = function(self, event, player, data, room) 
		if event == sgs.CardsMoveOneTime then
			local move = data:toMoveOneTime()
			if move.from and move.from:objectName() == player:objectName() and not move.card_ids:isEmpty() then
				if move.to_place == sgs.Player_DiscardPile then		 
					for i = 0, (move.card_ids:length()-1), 1 do
						local card_id = move.card_ids:at(i)
						local c = sgs.Sanguosha:getCard(card_id)
						registerToKyouko(room, c:objectName())
					end
				end
			end
		elseif event == sgs.TurnStart then 
			for _, kyouko in sgs.qlist(room:findPlayersBySkillName("luahuishen")) do 
				kyouko:getRoom():removeTag("luagongming")  
			end 
		end 
	end
}

kyouko:addSkill(luagongming) 
kyouko:addSkill(luahuishen) 
kyouko:addSkill(luahuishen2) 
 



tonglingcard = sgs.CreateSkillCard{
	name = "luatongling" ,
	filter = function(self, targets, to_select)
		return #targets == 0
	end ,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		room:setPlayerFlag(effect.from, "luatongling")
		local playerdata = sgs.QVariant() -- ai用
		playerdata:setValue(effect.from)
		effect.to:getRoom():setTag("tonglingTarge", playerdata)
		if room:askForUseCard(effect.to, "slash", "@luatongling") then
			effect.to:getRoom():removeTag("tonglingTarge")
			room:setPlayerFlag(effect.from, "-luatongling")
			effect.from:gainMark("@tongling")
		else
			effect.to:getRoom():removeTag("tonglingTarge")
			room:setPlayerFlag(effect.from, "-luatongling")
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			slash:setSkillName("luatongling")
			local x = effect.from:getMark("@tongling") + 1
			local tps = sgs.SPlayerList()
			for _, p in sgs.qlist(room:getOtherPlayers(effect.to)) do
				if effect.to:canSlash(p, slash, false) then tps:append(p) end
			end
			while (x > 0) and not tps:isEmpty() do
				local target = room:askForPlayerChosen(effect.from, tps, self:objectName(), "tonglingTarget", true, false)
				if not target then break end
				room:useCard(sgs.CardUseStruct(slash, effect.to, target))
				tps:removeOne(target)
				x = x - 1
			end
			effect.from:loseAllMarks("@tongling")
			room:showAllCards(effect.to)
			for _, card in sgs.list(effect.to:getHandcards()) do
				if card:isKindOf("Slash") then
					room:obtainCard(effect.from, card, false)
				end
			end
		end
	end
}
luatongling = sgs.CreateViewAsSkill{
	name = "luatongling" ,
	n = 0 ,
	view_filter = function(self, selected, to_select)
		return true
	end ,
	view_as = function(self, cards)
		local card = tonglingcard:clone()
		return card
	end ,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#luatongling")
	end
}

luaxiexianCard = sgs.CreateSkillCard{
	name = "luaxiexian",
	will_throw = false,
	filter = function(self, targets, to_select)
		return (#targets == 0) and to_select:objectName() ~= sgs.Self:objectName()
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		local cards = self:getSubcards()
		local x = cards:length()
		for _, id in sgs.qlist(cards) do
			local card = sgs.Sanguosha:getCard(id)
			effect.to:obtainCard(card)
		end
		effect.from:drawCards(x)
	end
}

luaxiexian = sgs.CreateViewAsSkill{
	name = "luaxiexian",
	n = 99,
	view_filter = function(self, selected, to_select)
        return to_select:isKindOf("Slash")
	end,
	view_as = function(self, cards)
        if #cards == 0 then return nil end
		local luaxiexianCard = luaxiexianCard:clone()
		for _,card in ipairs(cards) do
			luaxiexianCard:addSubcard(card)
		end
		return luaxiexianCard
	end,
	enabled_at_play = function(self,player)
        return not player:hasUsed("#luaxiexian")
	end,
}

seiga:addSkill(luatongling)
seiga:addSkill(luaxiexian)

seigaA:addSkill(luatongling)
seigaA:addSkill(luaxiexian)

seigaB:addSkill(luatongling)
seigaB:addSkill(luaxiexian)

seigaC:addSkill(luatongling)
seigaC:addSkill(luaxiexian)



luachanyuan = sgs.CreateFilterSkill{
	name = "luachanyuan" ,
	view_filter = function(self, card)
		return card:objectName() == "slash" or card:objectName() == "fire_slash" or card:objectName() == "thunder_slash"
	end ,
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("quanxiang", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		local wrap = sgs.Sanguosha:getWrappedCard(card:getId())
		wrap:takeOver(slash)
		return wrap
	end
}

luajingjuan = sgs.CreateTriggerSkill{
	name = "luajingjuan" ,
	global = true,
	events = {sgs.CardsMoveOneTime} ,
	can_trigger = function(self, target)
		return target:isAlive() and target:getHandcardNum() > 1
	end ,
	on_trigger = function(self, event, player, data)
		if event == sgs.CardsMoveOneTime then
			if not player:hasSkill("luajingjuan") then return false end
			local room = player:getRoom()
			local move = data:toMoveOneTime()
			if room:getTag("FirstRound"):toBool() then return end
			if move.from and move.from:objectName() == player:objectName() then
				for i = 0, move.card_ids:length() - 1, 1 do
					local id = move.card_ids:at(i)
					if (move.from_places:at(i) == sgs.Player_PlaceHand) and player:getHandcardNum() > 1  then
						local suit1 = player:getHandcards():at(0):getSuit()
						local invoke = true
						for _, card in sgs.list(player:getHandcards()) do
							if card:getSuit() ~= suit1 then invoke = false;break end
						end

						local targets_list = sgs.SPlayerList()
						for _, target in sgs.qlist(room:getAlivePlayers()) do
							if player:canSlash(target, nil, false) then
								targets_list:append(target)
							end
						end
						if not invoke then return false end
						if targets_list:isEmpty() then return false end
						local to = room:askForPlayerChosen(player, targets_list, "luajingjuan", "luajingjuanS", true)
						if to then
							room:showAllCards(player)
							if suit1 == sgs.Card_Heart then
								room:setPlayerMark(to, "jingjuanfff", to:getMark("jingjuanfff") + 1)
								room:setPlayerCardLimitation(to, "use", ".|.|.|hand", true)
							end
							if suit1 == sgs.Card_Club then room:addPlayerMark(to, "@skill_invalidity") end
							if suit1 == sgs.Card_Spade and not to:isAllNude() then
								local id2 = room:askForCardChosen(player, to, "hej", "luajingjuan", false, sgs.Card_MethodDiscard)
								room:throwCard(sgs.Sanguosha:getCard(id2), to, player)
							end
							local slash = sgs.Sanguosha:cloneCard("slash", suit1, 0)
							slash:setSkillName("luajingjuan")
							room:useCard(sgs.CardUseStruct(slash, player, to))
						end
						break
					end
				end

			elseif move.to and move.to:objectName() == player:objectName() then
				for _,id in sgs.qlist(move.card_ids) do
					if (room:getCardPlace(id) == sgs.Player_PlaceHand) and room:getCardOwner(id) and (room:getCardOwner(id):objectName() == player:objectName())
						and player:getHandcardNum() > 1 then
						local suit1 = player:getHandcards():at(0):getSuit()
						local invoke = true
						for _, card in sgs.list(player:getHandcards()) do
							if card:getSuit() ~= suit1 then invoke = false;break end
						end

						local targets_list = sgs.SPlayerList()
						for _, target in sgs.qlist(room:getAlivePlayers()) do
							if player:canSlash(target, nil, false) then
								targets_list:append(target)
							end
						end
						if not invoke then return false end
						if targets_list:isEmpty() then return false end
						local to = room:askForPlayerChosen(player, targets_list, "luajingjuan", "luajingjuanS", true)
						if invoke and to then
							room:showAllCards(player)
							if suit1 == sgs.Card_Heart then
								room:setPlayerMark(to, "jingjuanfff", to:getMark("jingjuanfff") + 1)
								room:setPlayerCardLimitation(to, "use", ".|.|.|hand", true)
							end
							--if suit1 == sgs.Card_Diamond then player:drawCards(1) end
							if suit1 == sgs.Card_Club then room:addPlayerMark(to, "@skill_invalidity") end
							if suit1 == sgs.Card_Spade and not to:isAllNude() then
								local id2 = room:askForCardChosen(player, to, "hej", "luajingjuan", false, sgs.Card_MethodDiscard)
								room:throwCard(sgs.Sanguosha:getCard(id2), to, player)
							end
							local slash = sgs.Sanguosha:cloneCard("slash", suit1, 0)
							slash:setSkillName("luajingjuan")
							room:useCard(sgs.CardUseStruct(slash, player, to))
						end
						break
					end
				end
			end
			return false
		end
	end
}
luajingjuan2 = sgs.CreateTriggerSkill{
	name = "#luajingjuan2" ,
	global = true,
	events = {sgs.TurnStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom() 
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			local count = p:getMark("jingjuanfff")
			for i = 1, count do
				room:removePlayerCardLimitation(p, "use", ".|.|.|hand$1")
			end
			room:setPlayerMark(p, "jingjuanfff", 0)
		end
		return false
	end , 
	priority = 2
}
hiziri:addSkill(luachanyuan)
hiziri:addSkill(luajingjuan2)
hiziri:addSkill(luajingjuan)


hiziriA:addSkill(luachanyuan)
hiziriA:addSkill(luajingjuan)

hiziriB:addSkill(luachanyuan)
hiziriB:addSkill(luajingjuan)

hiziriC:addSkill(luachanyuan)
hiziriC:addSkill(luajingjuan)

hiziriD:addSkill(luachanyuan)
hiziriD:addSkill(luajingjuan)
chunhuiCard = sgs.CreateSkillCard{
	name = "luachunhui",
	target_fixed = true,
	will_throw = false,
	handling_method = sgs.Card_MethodNone,
	on_use = function(self, room, source, targets)

		local Carddata2 = sgs.QVariant() -- ai用
		Carddata2:setValue(sgs.Sanguosha:getCard(self:getSubcards():first()))
		source:getRoom():setTag("luachunhuiTC", Carddata2)

		local choice = room:askForChoice(source, "luachunhui", "savage_assault+archery_attack+god_salvation+amazing_grace+iron_chain")
		source:getRoom():removeTag("luachunhuiTC")
		if choice ~= "iron_chain" then
			local slash = sgs.Sanguosha:cloneCard(choice, sgs.Card_NoSuit, 0)
			slash:addSubcard(self:getSubcards():first())
			room:useCard(sgs.CardUseStruct(slash, source, sgs.SPlayerList()))
		else
			local slash = sgs.Sanguosha:cloneCard(choice, sgs.Card_NoSuit, 0)
			slash:addSubcard(self:getSubcards():first())
			local targets = sgs.SPlayerList()
			for _,p in sgs.qlist(room:getAlivePlayers()) do
				if not room:isProhibited(source, p, slash) and slash:targetFilter(sgs.PlayerList(), p, source) then
					targets:append(p)
				end
			end
			local to = room:askForPlayerChosen(source, targets, "luachunhui", "luachunhui-invoke", false, false)
			local targets2 = sgs.SPlayerList()
			targets2:append(to)
			local to2 = room:askForPlayerChosen(source, targets, "luachunhui", "luachunhui-invoke", false, false)
			if to2:objectName() ~= to:objectName() then
				targets2:append(to2)
			end 
			room:useCard(sgs.CardUseStruct(slash, source, targets2))
		end 

	end
}
luachunhui = sgs.CreateOneCardViewAsSkill{
	name = "luachunhui",
	filter_pattern = ".",
	view_as = function(self,card)
		local skillcard = chunhuiCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self,player)
		return not player:hasUsed("#luachunhui")
	end
}

ceshi:addSkill(luachunhui)
--[[
luapinyi2 = sgs.CreateTriggerSkill{
	name = "#luapinyi2",
	global = true,
	events = {sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventAcquireSkill then
			if data:toString() == self:objectName() then
				room:setPlayerCardLimitation(player, "use,response", ".", false)
			end

		end
	end
}
luapinyi = sgs.CreateInvaliditySkill{
	name = "luapinyi",
	skill_valid = function(self, player, skill)
		if player:getHp() == 1 and skill:objectName() ~= "luapinyi" and skill:objectName() ~= self:objectName() then
			return false
		else
			return true
		end
	end
}
ceshi:addSkill(luapinyi)]]--
--ceshi:addSkill(luapinyi2)
luaweixin = sgs.CreateTriggerSkill{
	name = "luaweixin",
	frequency = sgs.Skill_Limited,
	events = {sgs.GameStart},
	limit_mark = "@weixin",
	on_trigger = function()
	end
}
ceshi:addSkill(luaweixin)



luayuling = sgs.CreateTriggerSkill{
	name = "luayuling",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damage, sgs.PreDamageDone},
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local room = player:getRoom()
		local card = damage.card
		local bool = (card == nil) or not (card:isKindOf("AOE"))
		local sp_seiga = room:findPlayerBySkillName("luajianglin")
		if not sp_seiga then return false end
		if not sp_seiga:isAlive() then return false end
		if (event == sgs.PreDamageDone) and damage.from and damage.from:hasSkill(self:objectName()) and damage.from:isAlive() then
			room:writeToConsole("luayuling tewstA")
			local weiyan = damage.from
			weiyan:setTag("invokeluayuling", sgs.QVariant(bool))
		elseif (event == sgs.Damage) and player:hasSkill(self:objectName()) and player:isAlive() then
			room:writeToConsole("luayuling tewstB")
			local invoke = player:getTag("invokeluayuling"):toBool()
			player:setTag("invokeluayuling", sgs.QVariant(false))
			if invoke then
				if not sp_seiga:isWounded() then
					sp_seiga:drawCards(1)
				else
					local choice = room:askForChoice(player, "luayuling", "recover+draw")
					if choice == "recover" then
						local recover = sgs.RecoverStruct()
						recover.who = sp_seiga
						recover.recover = 1
						room:recover(sp_seiga, recover)
					else
						sp_seiga:drawCards(1)
					end
				end
			end
		end
		return false
	end,
	can_trigger = function(self, target)
		return target
	end
}

luajianglinCard = sgs.CreateSkillCard{
	name = "luajianglin",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		local plist = sgs.SPlayerList()
		for _,p in sgs.qlist(room:getAllPlayers(true)) do
			if not p:isAlive() and isFriendQ(room, p, source) then
				plist:append(p)
			end
		end
		if plist:isEmpty() then return false end

		local uses = {}
		for _, liege in sgs.qlist(plist) do
			table.insert(uses, liege:getGeneralName())
		end
		local name = room:askForChoice(source, "luajianglin", table.concat(uses, "+"))
 		local rp
		for _,p in sgs.qlist(room:getAllPlayers(true)) do
			if not p:isAlive() and isFriendQ(room, p, source) and name == p:getGeneralName() then
				rp = p
			end
		end

		room:setPlayerProperty(rp, "maxhp", sgs.QVariant(1))
		room:setPlayerProperty(rp, "hp", sgs.QVariant(1))
		room:revivePlayer(rp, true)
		room:handleAcquireDetachSkills(rp, "luayuling")
	end
}
luajianglin = sgs.CreateZeroCardViewAsSkill{
	name = "luajianglin",
	view_as = function(self, cards)
		return luajianglinCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:hasSkill("luajianglin") and not player:hasUsed("#luajianglin")
	end,
}


luarumo = sgs.CreateTriggerSkill{
	name = "luarumo",
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if not p:hasSkill("luarumor") and not p:hasSkill("luarumo")
                    	and isFriendQ(room, p, p2) then room:writeToConsole("jiantin test" .. p:objectName()); room:attachSkillToPlayer(p, "luarumor") end
				end
			end
		end
	end
}


luarumorCard = sgs.CreateSkillCard{
	name = "luarumor",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		local sp_seiga = room:findPlayerBySkillName("luajianglin")
		if source:getMaxHp() == 1 then return false end
		if not sp_seiga then return false end
		if not sp_seiga:isAlive() then return false end
		room:loseMaxHp(source, 1)
		source:drawCards(2)
		if sp_seiga:isWounded() then room:recover(sp_seiga, sgs.RecoverStruct(source)) end
		room:askForUseCard(source, "slash", "@askforslash", -1, sgs.Card_MethodUse, false)
	end
}
luarumor = sgs.CreateZeroCardViewAsSkill{
	name = "luarumor&",
	view_as = function(self, cards)
		return luarumorCard:clone()
	end,
	enabled_at_play = function(self, player)
		for _, p in sgs.qlist(player:getAliveSiblings()) do
			if p:hasSkill("luarumo") then
				if not p:isWounded() then return false end
			end
		end
		return player:hasSkill("luarumor") and not player:hasUsed("#luarumor") and player:getMaxHp() ~= 1
	end,
}
ceshi:addSkill(luarumor)
ceshi:addSkill(luayuling)
sp_seiga:addSkill(luajianglin)
sp_seiga:addSkill(luarumo)

luajiangsuiCard = sgs.CreateSkillCard{
	name = "luajiangsui",
	filter = function(self, targets, to_select)
		return #targets == 0
	end,
	on_effect = function(self, effect)
		local targets = {effect.to, effect.from}
		local room = effect.from:getRoom()
		local x = 0
		local y = 0
		local i = 1
		local card_ids = room:getNCards(3)
		room:fillAG(card_ids)
		local _data = sgs.QVariant()
		_data:setValue(effect.to)
		room:setTag("luajiangsuiTP", _data)
		while (not card_ids:isEmpty()) do
			local card_id = room:askForAG(targets[i], card_ids, false, self:objectName())
			card_ids:removeOne(card_id)
			local card = sgs.Sanguosha:getCard(card_id)
			if i == 1 then x = x + card:getNumber() end
			if i == 2 then y = y + card:getNumber() end
			if i == 1 then room:setTag("luajiangsui", sgs.QVariant(tostring(card:getNumber()))) end
			room:takeAG(targets[i], card_id)
			i = 3 - i
		end
		room:removeTag("luajiangsui")
		room:removeTag("luajiangsuiTP")
		if x > y then effect.from:getRoom():damage(sgs.DamageStruct("luajiangsui", effect.from, targets[1])) end
		if y > x then effect.from:getRoom():damage(sgs.DamageStruct("luajiangsui", effect.from, targets[2])) end
		room:clearAG()
	end
}
luajiangsui = sgs.CreateZeroCardViewAsSkill{
	name = "luajiangsui",
	view_as = function(self, cards)
		return luajiangsuiCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:hasSkill("luajiangsui") and not player:hasUsed("#luajiangsui")
	end,
}

luatielunCard = sgs.CreateSkillCard{
	name = "luatielun",
	will_throw = true,
	filter = function(self, targets, to_select)
		return #targets == 0 and to_select:objectName() ~= sgs.Self:objectName()
	end,
	on_effect = function(self, effect)
		local count = 0
		local room = effect.from:getRoom()
		for _,card in sgs.qlist(effect.to:getHandcards()) do
			if card:isKindOf("BasicCard") then count = count + 1 end
		end
		if count > 2 then
			local card1 = room:askForCard(effect.to, "BasicCard!", "@luatielunA", sgs.QVariant(), sgs.Card_MethodNone)
			if card1 then
				room:setPlayerMark(effect.to, "kuangxiangx", card1:getId() + 1)
				local dummy = sgs.Sanguosha:cloneCard("jink")
				room:writeToConsole("^" .. card1:toString() .. "|" .. card1:getSuitString() .. "!")
				local card2 = room:askForCard(effect.to, "^" .. card1:toString() .. "+BasicCard" .. "!", "@luatielunB", sgs.QVariant(), sgs.Card_MethodNone) --牛逼，极其牛逼
				room:setPlayerMark(effect.to, "kuangxiangx", 0)
				if (not card2) or (card1:getId() == card2:getId()) then
					room:writeToConsole("kuangxiang test failed")
					local dummy22 = sgs.Sanguosha:cloneCard("jink")
					for _, cardX in sgs.list(effect.to:getHandcards()) do
						if cardX:hasFlag("luasanaess") then
							dummy22:addSubcard(cardX)
						end
					end
					room:throwCard(dummy22, effect.to, effect.to)
					return false
				end
				dummy:addSubcard(card1)
				dummy:addSubcard(card2)
				room:throwCard(dummy, effect.to)
			else
				room:writeToConsole("kuangxiang test failed")
				local dummy22 = sgs.Sanguosha:cloneCard("jink")
				for _, cardX in sgs.list(effect.to:getHandcards()) do
					if cardX:hasFlag("luasanaess") then
						dummy22:addSubcard(cardX)
					end
				end
				room:throwCard(dummy22, effect.to, effect.to)
				return false
			end
		else
			local dummy = sgs.Sanguosha:cloneCard("jink")
			for _,card in sgs.qlist(effect.to:getHandcards()) do
				if card:isKindOf("BasicCard") then dummy:addSubcard(card) end
			end
			room:throwCard(dummy, effect.to, effect.to)
		end
	end
}

luatielun = sgs.CreateOneCardViewAsSkill{
	name = "luatielun",
	view_filter = function(self, card)
		return not card:isKindOf("BasicCard")
	end,
	view_as = function(self, card)
		local skillcard = luatielunCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self, player)
		return player:hasSkill("luatielun") and not player:hasUsed("#luatielun")
	end,
}

sp_suwako:addSkill(luajiangsui)
sp_suwako:addSkill(luatielun)
--sp_suwako:addSkill(LuaFengshen)


luajianshu = sgs.CreateOneCardViewAsSkill{  --没写完
	name = "luajianshu",
	--filter_pattern = ".|.|.|hand",
	view_filter = function(self, card)
		if card:isEquipped() then return false end
		local usereason = sgs.Sanguosha:getCurrentCardUseReason()
		if usereason == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
			slash:addSubcard(card:getEffectiveId())
			slash:setSkillName("luajianshu")
			slash:deleteLater()
			return slash:isAvailable(sgs.Self) and not sgs.Self:hasFlag("luajianshuSlash_used")
		elseif (usereason == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE) or (usereason == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE) then
			local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
			if pattern == "slash" then
				local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
				slash:addSubcard(card:getEffectiveId())
				slash:setSkillName("luajianshu")
				slash:deleteLater()
				return slash:isAvailable(sgs.Self) and not sgs.Self:hasFlag("luajianshuSlash_used")
			elseif pattern == "jink" then 
				return not sgs.Self:hasFlag("luajianshuJink_used")
			end
		else
			return false
		end 
	end,
	view_as = function(self, originalCard)
		local Leishi_card = luajianshuCard:clone()
		Leishi_card:addSubcard(originalCard:getId())
		Leishi_card:setSkillName("luajianshu") 
		local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
		if pattern and (pattern == "slash" or pattern == "jink") then 
			Leishi_card:setUserString(pattern)
		end

		return Leishi_card
	end,
	enabled_at_play = function(self, player)
		return not (player:hasFlag("luajianshuJink_used") and player:hasFlag("luajianshuSlash_used"))
	end,
	enabled_at_response = function(self, player, pattern)
		return (pattern == "slash" or pattern == "jink") and not (player:hasFlag("luajianshuJink_used") and player:hasFlag("luajianshuSlash_used"))
	end
}
luajianshuCard = sgs.CreateSkillCard{
	name = "luajianshu",
	target_fixed = false,  --slashTargetFixToOne
	will_throw = false,
	handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
		local usereason = sgs.Sanguosha:getCurrentCardUseReason()
		if usereason == sgs.CardUseStruct_CARD_USE_REASON_PLAY or 
			(usereason == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE and pattern == "slash") then
			if to_select:objectName() == sgs.Self:objectName() then return false end
			if sgs.Self:hasFlag("slashTargetFixToOne") then
				local target
				for _, p in sgs.qlist(sgs.Self:getSiblings()) do
					if p:hasFlag("SlashAssignee") then target = p end
				end
				if not target then return false end
				if to_select:objectName() ~= target:objectName() then return false end
			end
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
			slash:addSubcard(self:getSubcards():first())
			slash:setSkillName("luajianshu")
			slash:deleteLater()
			if #targets >= 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, sgs.Self, slash) then return false end
			return true
		end 
		return true
	end,
	on_validate = function(self,carduse)
		local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
		local usereason = sgs.Sanguosha:getCurrentCardUseReason()
		if usereason == sgs.CardUseStruct_CARD_USE_REASON_PLAY or 
			(usereason == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE and pattern == "slash") then
			local source = carduse.from
			local target = carduse.to:first()
			local room = source:getRoom()
			local card = sgs.Sanguosha:getCard(self:getSubcards():first())
			if source:canSlash(target, nil, false) then
				local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
				slash:setSkillName(self:objectName())
				slash:addSubcard(card:getEffectiveId())
				room:setPlayerFlag(source,"luajianshuSlash_used")
				return slash
			end
		else
			room:setPlayerFlag(source,"luajianshuJink_used")

		end 
	end,
	on_validate_in_response = function(self, user)
		local room = user:getRoom()
		
		local aocaistring = self:getUserString()
		if aocaistring then 
			if (aocaistring == "slash" or aocaistring == "Slash") then
				room:setPlayerFlag(user,"luajianshuSlash_used")
			else
				room:setPlayerFlag(user,"luajianshuJink_used") 
			end 
			local use_card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, -1) 
			use_card:addSubcard(self:getSubcards():first())
			use_card:setSkillName("luajianshu")
			return use_card	
		end 
	end
}
luajianshu2 = sgs.CreateTriggerSkill{
	name = "#luajianshu" ,
	global = true,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local toziko = player:getRoom():findPlayerBySkillName("Luashenyi")
		if toziko and toziko:hasFlag("luajianshu_used") then
			room:setPlayerFlag(toziko, "-luajianshuSlash_used")
			room:setPlayerFlag(toziko, "-luajianshuJink_used")
			--room:writeToConsole("回合结束")
		end
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}
ceshi:addSkill(luajianshu)
ceshi:addSkill(luajianshu2)



sgs.LoadTranslationTable{
	["pay6"] = "求闻口授", --注意这里每次要加逗号
	["qinxin"] = "秦心",
	["kokoroA"] = "秦心",
	["kokoroB"] = "秦心",
	["kokoroC"] = "秦心",
	["kokoroD"] = "秦心",
	["#qinxin"]= "亡失的情感",
	["designer:qinxin"] = "Paysage",
	["Luaxinwu"] = "心舞",
	[":Luaxinwu"] = "出牌阶段限一次，你可以弃置一张手牌令两名角色拼点，然后你选择一项：1.赢的角色从没赢的角色处获得一张牌2.视为你对没赢的角色使用了一张【秽】以外的基本牌",
	["Luaxinwu1"] = "赢的角色从没赢的角色处获得一张牌",
	["Luaxinwu2"] = "视为你对没赢的角色使用了一张【秽】以外的基本牌",
	["LuaxinwuTarget"] = "“心舞”<br> <b>操作提示</b>: 请选择第一张基本牌要指定的角色<br/>",
	["Luanengmian"] = "能面",
	[":Luanengmian"] = "每回合限一次，你可以令一名角色的拼点牌至多+x或-x（x为你装备区域牌数量）。锁定技，拼点时你始终获得对方的拼点牌。",
	["Luanengmiani"] = "你可以发动“能面”<br> <b>操作提示</b>: 选择拼点中的一名角色→点击确定，然后选择增加或减少的点数<br/>",

	["LuaFengshen"] = "奉神",
	["@LuaFengshen2"] = "请选择主公，你进行判定：若为锦囊牌，其摸一张牌。",
	[":LuaFengshen"] = "主公技，其他角色受到伤害时，该角色可以进行判定：若为锦囊牌，你摸一张牌。",

	["lualuoying"] = "落英",
	[":lualuoying"] = "其他角色的牌因判定或弃置而置入弃牌堆时，你可以获得其中至少一张梅花牌。 ",
	
	["sp_suwako"] = "洩矢诹访子",
	["designer:sp_suwako"] = "Paysage",
	["#sp_suwako"]= "土著神的顶点",
	["luajiangsui"] = "降祟",
	["jiangsui"] = "降祟",
	[":luajiangsui"] = "出牌阶段限一次，你可以展示牌堆顶的三张牌，选一名角色与你轮流获得其中一张。获得牌点数合计最大的角色受到你对其造成的一点伤害。",
	["luatielun"] = "铁轮",
	[":luatielun"] = "出牌阶段限一次，你可以弃置一张非基本牌，令其弃置手中的全部基本牌（至多两张）。",
	["@luatielunA"] = "请弃置两张基本牌，现在选择第一张",
	["@luatielunb"] = "请弃置两张基本牌，现在选择第二张",



	["nue"] = "封兽鵺",
	["nueA"] = "封兽鵺",
	["designer:nue"] = "Paysage",
	["#nue"]= "不明正体", 

	["luasheji"] = "蛇戟",
	[":luasheji"] = "转化技，当你使用牌指定目标时，若你手牌数为：" ..
					"③：3，弃置或标记目标的一张牌。" ..
					"②：2，此牌结算后再对其他任意两名角色使用之。" ..
					"①：1，流失一点体力并摸两张牌。",

	["luayaoyun"] = "妖云",
	[":luayaoyun"] = "转化技，①：出牌阶段，你可以标记你的所有手牌，获得“梦魇”和一个额外的出牌阶段；②：锁定技，准备阶段，你清除所有牌的标记并失去“梦魇” 。",

	["@luamengyan"] = "你须发动“梦魇”",
	["~luamengyan"] = "请选择牌要指定的角色",
	["luamengyan"] = "梦魇",
	[":luamengyan"] = "锁定技，被你标记的牌指定目标后，效果改为「展示牌堆顶的牌，对你或目标中的至少一名角色使用之（若不能则弃置）」", 

	["toziko"] = "苏我屠自古",
	["#toziko"] = "神明后裔的亡灵",
	["designer:toziko"] = "Paysage",
	["LuaLeishi"] = "雷矢",
	[":LuaLeishi"] = "每回合限一次，你可以将一张手牌当作雷【杀】使用或打出。",
	["Luayuanxing"] = "元兴",
	[":Luayuanxing"] = "限定技，你可以令你造成的一次雷属性伤害+1。 ",
	["Luayuanling"] = "怨灵",
	[":Luayuanling"] = "锁定技，出牌阶段结束时，你须横置X+1名角色（X为本阶段你造成的雷属性伤害数）。",
	["@Luayuanling"] = "你可以发动“怨灵”",
	["~Luayuanling"] = "请选择要横置的角色",
	["Luashenyi"] = "神裔",
	["Luashenyito"] = "你可以令一名角色执行一个额外的出牌阶段。",
	[":Luashenyi"] = "每当你受到伤害后，你可以于此阶段结束时令一名角色执行一个额外的出牌阶段。 ",

	["turnover2"] = "摸一张牌并翻面。",
	["syou"] = "寅丸星",
	["#syou"] = "毘沙门天的弟子",
	["designer:syou"] = "星外纸人",
	["luaweiguang"] = "杀戒",
	["luaweiguangQ"] = "请选择锦囊牌要指定的目标",
	[":luaweiguang"] = "转化技，出牌阶段，你可以于 ①.可以②.不能 使用杀的场合，展示全部手牌并弃置其中一种花色的牌，若弃置牌的数量＞0，你下一张杀造成的伤害+1；＞1，则你视为对一名角色使用一张普通锦囊牌。",
	["luafadeng"] = "聚宝",
	[":luafadeng"] = "出牌阶段结束时，若本回合你未使用过【杀】，则你可以令一名角色获得牌堆顶两张同花色的牌（花色由你声明）。",
	["@Luaweiguang"] = "你可以对一名角色使用一张【劝降】",

	["quanxiang"] = "劝降",

	["tatara"] = "多多良小伞",
	["#tatara"] = "遗忘之伞",
	["designer:tatara"] = "Paysage",
	["luajingxia"] = "惊吓",
	["giveEquip"] = "给小伞装备",
	["turnOver"] = "摸牌翻面",
	[":luajingxia"] = "每回合限一次，每当你对一名角色造成伤害，或是受到一名角色对你造成的伤害后，你可以令其选择一项：①你确认其所有牌，并从中获得一张装备牌；②其摸一张牌并翻面（若其装备区没有牌，改为摸两张）。",
	["luajinglian"] = "精炼",
	["jinglian"] = "精炼",
	["@luajinglian"] = "选择一张牌，因“精炼”的效果，其会被当作【杀】使用（现在不必选择目标）。",
	["@luajinglian2"] = "选择你要杀的目标。",
	[":luajinglian"] = "出牌阶段限一次，你可以交给一名角色一张装备牌并令其摸一张牌，然后其可以将一张牌当作【杀】使用之（无限制）。",

	["futo"] = "物部布都",
	["futoA"] = "物部布都",
	["futoB"] = "物部布都",
	["futoC"] = "物部布都",
	["#futo"] = "风水师",
	["designer:futo"] = "Paysage",
	["illustrator:futo"] = "桜庭友紀",
	["illustrator:futo_1"] = "HUG",
	["illustrator:futo_2"] = "羽羽斩",
	["illustrator:futo_3"] = "炭水化物P",
	["Luazonghuo"] = "纵火",
	["zonghuo"] = "纵火",
	[":Luazonghuo"] = "出牌阶段限一次，你可以令一名角色将其一张你选择的牌当作火【杀】对其使用。",
	["LuaFengshui"] = "风水",
	[":LuaFengshui"] = "锁定技，你防止与你体力值相等的角色对你造成的非属性伤害和与你体力值不同的角色对你造成的属性伤害。",

	["yoshika"] = "宫古芳香",
	["#yoshika"] = "忠诚的尸体",
	["designer:yoshika"] = "Paysage",
	["Luaduzhua"] = "毒爪",
	["LuaduzhuaA"] = "请对持有“感染”以外的一名角色使用一张【杀】",
	[":Luaduzhua"] = "锁定技，出牌阶段开始时，你须选择一项：对持有“感染”以外的一名角色使用一张【杀】，或是弃置一张牌。",
	["Luaganran"] = "感染",
	[":Luaganran"] = "你使用【杀】对目标角色造成伤害后，你可令其获得“毒爪”。",
	["Luabaige"] = "白歌",
	[":Luabaige"] = "每个出牌阶段限一次，当你需要使用或打出【杀】时，你可以展示你的所有手牌，若那其中没有【杀】，则视为你使用或打出了一张【杀】。",
 

	["luaweixin"] = "卫星",
	[":luaweixin"] = "这是一个卫星。",

	["LuaJubian"] = "聚变",
	[":LuaJubian"] = "锁定技，你对一名角色造成火焰伤害后，你须摸一张牌。",

	["toyosatomimi"] = "丰聪耳神子",
	["toyosatomimiA"] = "丰聪耳神子",
	["toyosatomimiB"] = "丰聪耳神子",
	["toyosatomimiC"] = "丰聪耳神子",
	["toyosatomimiD"] = "丰聪耳神子",
	["toyosatomimiE"] = "丰聪耳神子",
	["toyosatomimiN"] = "丰聪耳神子",
	["#toyosatomimi"] = "圣德太子",
	["designer:toyosatomimi"] = "Paysage",
	["LuaGuanwei"] = "冠位",
	[":LuaGuanwei"] = "每当你受到一点伤害后，你选一项：①摸一张牌，对一名其他角色造成一点伤害并令其回复一点体力；②弃一张牌，令一名角色于当前阶段结束时执行一个额外的任意阶段。",
	["@LuaGuanwei"] = "你可以发动“冠位”",
	["~LuaGuanwei"] = "若你选择①，则选择一名角色，点击确定即可；若你选择②，则你选择一张要弃的牌，再选择角色点击确定即可。",
	["luawangshi"] = "王师",
	["wangshi"] = "王师",
	["luawangshiz"] = "请对刚刚神子指定的那个对象使用一张【杀】",
	["luawangshiS"] = "请选择要让其使用【杀】的一个对象。",
	[":luawangshi"] = "出牌阶段限一次，你令一名其他角色选择一项：对你指定的一名角色使用一张【杀】并摸一张牌，或视为你对其使用了一张【杀】。",
	["LuaJianting"] = "兼听",
	[":LuaJianting"] = "主公技，其他角色的出牌阶段限一次，可以交给你至多X/3张牌，然后你交给其等量的牌。（X为存活人数）。",
	["LuaJiantingAsk"] = "兼听",
	[":LuaJiantingAsk"] = "你可以交给丰聪耳神子至多X张牌，然后其交给你等量的牌。（X为存活人数）。",

	["kyouko"] = "幽谷响子",
	["#kyouko"] = "诵经的山彦",
	["designer:kyouko"] = "Paysage",
	["luagongming"] = "共鸣",
	[":luagongming"] = "一名角色使用【杀】指定目标后，你可以弃置一张与此【杀】颜色不同的牌。然后若此【杀】造成伤害，你或其摸一张牌；"
		.. "若此【杀】被抵消，你与所有目标各摸一张牌。“共鸣”同一轮仅能在一个回合内发动。",
	["luahuishen"] = "回声",
	[":luahuishen"] = "每回合限一次，你可以将一张牌当作“共鸣”回合进入弃牌堆的一张非装备牌使用。", 


	["seiga"] = "霍青娥",
	["seigaN"] = "霍青娥",
	["seigaA"] = "霍青娥",
	["seigaB"] = "霍青娥",
	["seigaC"] = "霍青娥",
	["#seiga"] = "穿壁的邪仙",
	["designer:seiga"] = "Paysage",
	["luatongling"] = "通灵",
	["@luatongling"] = "请使用一张【杀】",
	["tonglingTarget"] = "请指定一名角色，其将成为被你“通灵”所指定玩家的【杀】的目标。",
	[":luatongling"] = "出牌阶段限一次，你可以令一名角色选择一项：1.使用一张【杀】并令你获得一个“灵”标记  2.视为对你指定的X+1名角色使用了一张【杀】并令你失去所有“灵”标记，然后你可以展示其所有手牌并获得其中的【杀】（X为你的“灵”标记数）。",
	["luaxiexian"] = "邪仙",
	[":luaxiexian"] = "出牌阶段限一次，你可以将任意数量的【杀】交给一名其他角色，然后摸等量的牌。",

	["sp_seiga"] = "霍青娥",
	["#sp_seiga"] = "穿壁的邪仙",
	["designer:seiga"] = "Paysage",
	["luajianglin"] = "降灵",
	[":luajianglin"] = "出牌阶段限一次，你可以复活一名已阵亡的友方角色，其体力上限改为1并获得“欲灵”。",

	["luayuling"] = "欲灵",
	[":luayuling"] = "锁定技，非群体锦囊以外，每当你对一名角色造成1点伤害后，你可以令霍青娥回复1点体力或是摸1张牌。",
	["luarumo"] = "入魔",
	["luarumor"] = "入魔",
	[":luarumor"] = "你可以摸两张牌并令霍青娥回复一点体力，然后其减少一点体力上限并可以使用一张【杀】（无次数限制）。",
	[":luarumo"] = "友方其他角色若体力值大于1，其可以摸两张牌并令你回复一点体力，然后其减少一点体力上限并可以使用一张【杀】（无次数限制）。“入魔”每回合最多使用一次。",


	["hiziri"] = "圣白莲",
	["hiziriA"] = "圣白莲",
	["hiziriB"] = "圣白莲",
	["hiziriC"] = "圣白莲",
	["hiziriD"] = "圣白莲",
	["#hiziri"] = "住持僧",
	["designer:hiziri"] = "Paysage",
	["illustrator:hiziri"] = "べにしゃけ",
	["illustrator:hiziriA"] = "りひと",
	["illustrator:hiziriB"] = "煮茶",
	["illustrator:hiziriC"] = "匡吉",
	["illustrator:hiziriD"] = "Pixerite",
	["luachanyuan"] = "禅缘",
	[":luachanyuan"] = "锁定技，你手牌中的【杀】均视作【劝降】。你不能成为【劝降】的目标。",
	["luajingjuan"] = "经卷",
	["luajingjuanS"] = "请选择“经卷”【杀】指定的目标。",
	[":luajingjuan"] = "你手牌数变化后，你可以展示全部手牌（至少两张），若其花色全部相同，则视为你使用了一张此花色的【杀】，若此花色为红桃，其直到回合结束阶段"
		.. "不能使用或打出手牌；梅花，其直到回合结束阶段非锁定技无效；黑桃,你弃置其一张牌。",

	[":quanxiang"] = "锦囊牌<br/>" ..
				"<b>时机：</b>出牌阶段（每回合限使用一张）<br/>"..
				"<b>目标：</b>攻击范围内的一名手牌数比你少的其他角色<br/>" ..
				"<b>效果：</b>你与其拼点，若你赢，其摸一张牌并翻面；若你没赢，你获得其拼点牌。<br/>" ,

	["luachanyuan"] = "禅缘",
	[":luachanyuan"] = "锁定技，你手牌中的【杀】均视作【劝降】。你不能成为【劝降】的目标。",
	["luajingjuan"] = "经卷",
	["luajingjuanS"] = "请选择“经卷”【杀】指定的目标。",

	["luagongchengr"] = "工程",
	["luagongchengrw"] = "请选择一名河城荷取",
	[":luagongchengr"] = "可以把你的一张【杀】交给荷取来摸一张牌。",
	
	["luasenyur"] = "森域",
	["luasenyurw"] = "请选择一名山城高岭",
	[":luasenyur"] = "可以把你的一张非【杀】基本牌交给荷取来摸一张牌。",


	["rrandom"] = "充满魅力的选择",
	["hui"] = "秽",
	["Hui"] = "秽",
	[":hui"] = "基本牌\
	<b>时机</b>：出牌阶段\
	<b>目标</b>：自己\
	<b>效果</b>：除拼点外，导致此牌进入弃牌堆的玩家二选一：弃置两张牌，或是受到一点伤害。",

	["pay9"] = "朝花夕拾",

	["ofuda"] = "符",
	["Ofuda"] = "符",
	[":ofuda"] = "基本牌\
	<b>时机</b>：出牌阶段\
	<b>目标</b>：攻击范围内的一名其他角色\
	<b>效果</b>：目标角色需交给你一张手牌，否则其不能使用、打出或弃置手牌，且非锁定技无效，直到此回合结束。\
	<b>响应</b>：响应【南蛮入侵】【决斗】【宗教战争】时，此牌可以当作【杀】使用或打出。",
	["giveCard"] = "交出一张牌",
	["ofuda1"] = "交出一张牌",
	["ofuda2"] = "手牌与技能封印直到回合结束",
	["@ofuda"] = "请选择要交出的手牌",
	["@ofudaa"] = "符",

	["need_maribel"] = "望梅止渴",
	["NeedMaribel"] = "望梅止渴",
	[":need_maribel"] = "延时类锦囊牌\
	<b>时机</b>：出牌阶段\
	<b>目标</b>：一名角色\
	<b>效果</b>：目标角色判定：若判定不为梅花，其摸一张牌并跳过本回合的弃牌阶段，否则其回复一点体力。",

	["girl_choosen"] = "自机大选",
	["GirlChoosen"] = "自机大选",
	[":girl_choosen"] = "延时类锦囊牌\
	<b>时机</b>：出牌阶段\
	<b>目标</b>：自己\
	<b>效果</b>：目标角色判定：若判定为红桃2~9，本回合结束时其执行一个额外的回合，否则将之置于下家的判定区。",

	["banquet"] = "宴会",
	["Banquet"] = "宴会",
	["banquet2"] = "请选择要将【宴会】移动至的角色",
	[":banquet"] = "延时类锦囊牌\
	<b>时机</b>：出牌阶段\
	<b>目标</b>：自己\
	<b>使用时效果</b>：视为你使用了一张【酒】\
	<b>效果</b>：目标角色判定：若判定不为黑桃，将此牌移至一名其他角色的判定区，视为目标角色使用了一张【酒】。",

	["blossom"] = "朝花夕拾",
	["Blossom"] = "朝花夕拾",
	[":blossom"] = "锦囊牌\
	<b>时机</b>：出牌阶段\
	<b>目标</b>：自己\
	<b>效果</b>：若弃牌堆顶的牌不是【朝花夕拾】，你可以获得之。",

	["faith_collection"] = "信仰收集",
	["FaithCollection"] = "信仰收集",
	[":faith_collection"] = "锦囊牌\
	<b>时机</b>：出牌阶段\
	<b>目标</b>：一名其他角色\
	<b>效果</b>：目标角色交给你其任意区域里的一张牌。\
	<b>响应</b>：响应【南蛮入侵】【决斗】【宗教战争】时，此牌可以当作【杀】使用或打出。",

	["religion_battle"] = "宗教战争",
	["ReligionBattle"] = "宗教战争",
	["@RB"] = "请使用一张牌以响应“宗教战争”",
	["@RB2"] = "请使用一张牌",
	[":religion_battle"] = "锦囊牌\
	<b>时机</b>：出牌阶段\
	<b>目标</b>：所有角色\
	<b>效果</b>：从你开始，所有玩家依次使用一张牌（无距离与次数限制），或是受到一点伤害。",

	
	["luajianshu"] = "剑戍",
	[":luajianshu"] = "每回合各限一次，你可以将一张手牌当作【杀】【闪】使用或打出（不计入限制）。",
}

return {extension_pay_f}