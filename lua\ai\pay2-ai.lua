--hahaha
local Luatannang_skill={} -- 初始化 Luatannang_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 Luatannang_skill 只是为了出于习惯
Luatannang_skill.name="Luatannang" -- 设置 name
table.insert(sgs.ai_skills, Luatannang_skill) -- 把这个表加入到 sgs.ai_skills 中

Pay = require "paysage" --加载价值模组


sgs.ai_skill_invoke.luayingzi = sgs.ai_skill_invoke.nosyingzi
sgs.pay_ai_card.Peach.luayingzi = function(self, card, use, mustusepeach)
	if self.player:hasSkill("luayingzi") and not mustusepeach then
		if self.player:getPhase() == sgs.Player_Play and (self:getOverflow() <= 0) then return 2 end
	end
end 
local function findhakreiTarget(self, cards)
	local slash
	local slash2 = sgs.Sanguosha:cloneCard("slash")
	slash2:setSkillName("luahakurei2")

	local dummy_use
	local useSlash = false
	for _, card in ipairs(cards) do
		if card:isKindOf("Slash") then useSlash = true end
	end
	if math.random() < 0.4 then useSlash = false end
	if (self.player:getHandcardNum() > 2 or self:getOverflow() > 0) and not useSlash then
		slash = sgs.Sanguosha:cloneCard("slash")
		slash:setSkillName("luahakurei2")
		dummy_use = { isDummy = true , to = sgs.SPlayerList() , distance = 99}
	else
		slash = sgs.Sanguosha:cloneCard("slash")
		slash:setSkillName("luahakurei")
		dummy_use = { isDummy = true , to = sgs.SPlayerList() }
	end
	self:useBasicCard(slash, dummy_use)
	if not dummy_use.card or dummy_use.to:isEmpty() then
		dummy_use = { isDummy = true , to = sgs.SPlayerList() , distance = 99}
		self:useBasicCard(slash2, dummy_use)
	end

	if dummy_use.card and dummy_use.card:isKindOf("Slash") and dummy_use.to and dummy_use.to:length() > 0 then
		self.room:writeToConsole("reimu test P3")
		return dummy_use.to
	end
	return
end
local function ceYan(self, card)
	local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
	slash:addSubcard(card:getEffectiveId())
	slash:setSkillName("luahakurei2")
	slash:deleteLater()
	return slash:isAvailable(self.player)
end
local function findhakreicard(self, target, cardsA)
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	local red_card
	self:sortByUseValue(cards, true)

	local x = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
	local bool_k = (self.player:getHp() <= 2 and self.player:getHandcardNum() <= 3 and x <= 1)

	local useAll = false
	if target:getHp() == 1 and not target:hasArmorEffect("eight_diagram") and self:isWeak(target)
			and getCardsNum("Jink", target, self.player) + getCardsNum("Peach", target, self.player) + getCardsNum("Analeptic", target, self.player) == 0 then
		useAll = true
	end

	local function caonima(slash)
		local boolQ = false
		if (self.player:getHandcardNum() > 2 or self:getOverflow() > 0) then
			for _, card in ipairs(cardsA) do
				if card:getEffectiveId() == slash:getEffectiveId() then boolQ = true end
			end
		end
		if not boolQ then return true end
		return false
	end
	local function Check_F(card_0)
		if (card_0:isKindOf("Peach") or card_0:isKindOf("ExNihilo")) and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
			return false
		end
		if card_0:isKindOf("Duel") and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
			return false
		end
		if card_0:isKindOf("Indulgence") and not useAll then
			for _, enemy in ipairs(self.enemies) do
				if self:getIndulgenceValue(enemy) > 3 then return false end
			end
		end
		if self:ThreeCheck(card_0) then return false end

		if (card_0:isKindOf("AOE") and self:getAoeValue(card_0) > 35) then return false end
		if card_0:isKindOf("Lightning") and not self:willUseLightning(card_0) then return true end
		local bool_2 = card_0:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(card_0:getId()) == sgs.Player_PlaceEquip))
		local bool_3 = (card_0:isKindOf("EightDiagram") or card_0:isKindOf("RenwangShield") or card_0:isKindOf("Tengu"))
				and ((self.room:getCardPlace(card_0:getId()) == sgs.Player_PlaceEquip)
				or ((self.room:getCardPlace(card_0:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
		if (bool_2 or bool_3) and not useAll then return false end
		if card_0:isKindOf("Slash") and caonima(card_0) then return false end
		if (not (card_0:isKindOf("Jink") and bool_k) and not (isCard("Peach", card_0, self.player) and self.player:isWounded())) or useAll then
			return true
		end
	end



	local disCrossbow = false
	if self:getCardsNum("Slash") < 2 or self.player:hasSkill("paoxiao") then
		disCrossbow = true
	end

	if not red_card then
		if self:needToThrowArmor() and ceYan(self, self.player:getArmor()) then
			red_card = self.player:getArmor()
		end
	end



	if not red_card then
		for _, card in ipairs(cards) do
			if card:getSuit() == sgs.Card_Diamond and ceYan(self, card) then
				if not (card:isKindOf("Jink") and bool_k) and not (isCard("Peach", card, self.player) and self.player:isWounded())
					and not (card:isKindOf("Slash") and caonima(card)) then
					red_card = card
				end
			end
		end
	end

	if not red_card then
		for _, card in ipairs(cards) do
			if  (not isCard("Crossbow", card, self.player) and not disCrossbow)
					and ceYan(self, card)
					and (Check_F(card) or self.player:hasWeapon("pundarika")) then
				red_card = card
				break
			end
		end
	end
	return red_card
end
local luahakurei_skill = {}
luahakurei_skill.name = "luahakurei"
table.insert(sgs.ai_skills, luahakurei_skill)
luahakurei_skill.getTurnUseCard = function(self, inclusive)
	if self.player:hasFlag("luahakurei_used") then return end
	return sgs.Card_Parse("#luahakurei:.:")
end

sgs.ai_skill_use_func["#luahakurei"] = function(card, use, self)
	local cardsA = self:getTurnUse(true)
	local targets = findhakreiTarget(self, cardsA)
	if (not targets) or (targets:isEmpty()) then return end
	local red_card = findhakreicard(self, targets:at(0), cardsA)
	if not red_card then return end


	local slash = sgs.Sanguosha:cloneCard("slash", red_card:getSuit(), red_card:getNumber())
	slash:setSkillName("luahakurei2")
	slash:addSubcard(red_card:getEffectiveId())
	local dummy_use = { isDummy = true , to = sgs.SPlayerList() , distance = 99}
	self:useBasicCard(slash, dummy_use)
	if dummy_use.card and not dummy_use.card:isKindOf("Slash") then
		use.card = dummy_use.card
		if use.to then
			use.to = dummy_use.to
		end
	end


	use.card = sgs.Card_Parse("#luahakurei:" .. red_card:getId() ..":")
	if use.to then
		if red_card:getSuit() == sgs.Card_Diamond and math.random() < 0.2 then self.player:speak("妖怪治退！") end
		use.to = targets
		return
	end
end

sgs.ai_use_priority.luahakurei = 4.38

sgs.ai_skill_choice.luahakurei = function(self, choices, data)
	self.room:writeToConsole("屠自古测试2")
	local target = data:toPlayer()
	local useSlash = false
	local cards = self:getTurnUse(true)
	for _, card in ipairs(cards) do
		if card:isKindOf("Slash") then useSlash = true end
	end
	if target:getHp() <= 2 and useSlash then return "luahakurei1" end
	if math.random() < 0.4 then useSlash = false end
	if (self.player:getHandcardNum() > 2 or self:getOverflow() > 0) and useSlash then
		return "luahakurei1"
	else
		return "luahakurei2"
	end
end

sgs.ai_cardneed.luahakurei = function(to, card)
	if not to:hasSkill("luahakurei") then return end
	return card:isKindOf("Weapon")
end

sgs.ai_skill_invoke.LuaXingshang = true

sgs.ai_skill_invoke.luayanxun = function(self, data)
	local damage = data:toDamage()
	if self:isFriend(damage.to) then if self.player:getMark("@luahuaxian") > 3 then return false else return true end end
	if self.player:getMark("@luahuaxian") > 3 then return true end
	if self:isWeak(damage.to) then return false end
	if self.player:getHandcardNum() < 2 then return true end
	if damage.to:hasSkill("luafenxing") then return true end
	if damage.to:getWeapon() and self.player:getMark("huaxianW") == 0 then
		return true
	end
	if damage.to:getArmor() and self.player:getMark("huaxianA") == 0 then
		return true
	end
	if damage.to:getDefensiveHorse() and self.player:getMark("huaxianD") == 0 then
		return true
	end
	if damage.to:getOffensiveHorse() and self.player:getMark("huaxianO") == 0 then
		return true
	end
	if damage.to:getTreasure() and self.player:getMark("huaxianT") == 0 then
		return true
	end
	if self:getDangerousCard(damage.to) then return true end
	if (self:getKnownNum(damage.to) >= damage.to:getHandcardNum() - 1) then
		for _, cardD in sgs.qlist(damage.to:getHandcards()) do
			if cardD:isKindOf("Peach") or cardD:isKindOf("ExNihilo") or cardD:isKindOf("Indulgence") then return true end
			if cardD:isKindOf("Weapon") and self.player:getMark("huaxianW") == 0 then return true end
			if cardD:isKindOf("Armor") and self.player:getMark("huaxianA") == 0 then return true end
			if cardD:isKindOf("DefensiveHorse") and self.player:getMark("huaxianD") == 0 then return true end
			if cardD:isKindOf("OffensiveHorse") and self.player:getMark("huaxianO") == 0 then return true end
			if cardD:isKindOf("Treasure") and self.player:getMark("huaxianT") == 0 then return true end
		end
	end
	return false
end

local luaguiwan_skill = {}
luaguiwan_skill.name = "luaguiwan"
table.insert(sgs.ai_skills, luaguiwan_skill)
luaguiwan_skill.getTurnUseCard = function(self, inclusive)
	if self.player:hasFlag("luaguiwan") then return end
	if #self.enemies == 0 then return end
	return sgs.Card_Parse("#luaguiwan:.:")
end

sgs.ai_skill_use_func["#luaguiwan"] = function(card, use, self)
	if #self.enemies == 0 then return end

	local useSlash = false
	for _, ecard in ipairs(self:getTurnUse(true)) do
		if ecard:isKindOf("Slash") then
			useSlash = true
		end
	end

	local enemies = self.enemies
	self:sort(enemies, "defense")
	local card
	if self:needToThrowArmor() then
		card = self.player:getArmor()
	end
	for _, equip in sgs.qlist(self.player:getEquips()) do
		if equip:isKindOf("DefensiveHorse") then card = equip;break end
		if equip:isKindOf("OffensiveHorse") then card = equip;break end
		if equip:isKindOf("Armor") and (self:isWeak(enemies[1]) or self.player:getHp() > 2) then card = equip;break end
		if equip:isKindOf("Weapon") and not useSlash then card = equip;break end
	end
	if not card then return end

	use.card = sgs.Card_Parse("#luaguiwan:".. card:getId() ..":")
	if use.to then
		use.to:append(enemies[1])
		return
	end
end

sgs.ai_use_priority.luaguiwan = 6.5

local luaxiedao_skill = {}
luaxiedao_skill.name = "luaxiedao"
table.insert(sgs.ai_skills, luaxiedao_skill)
luaxiedao_skill.getTurnUseCard = function(self, inclusive)
	if self.player:hasUsed("#luaxiedao") then return end
	if not self.player:hasFlag("luayanxun") and not self.player:hasFlag("luaguiwan") then return end
	if #self.enemies == 0 then return end
	return sgs.Card_Parse("#luaxiedao:.:")
end

sgs.ai_skill_use_func["#luaxiedao"] = function(card, use, self)
	use.card = sgs.Card_Parse("#luaxiedao:.:")
	if use.to then
		use.to = sgs.SPlayerList()
		return
	end
end

sgs.ai_use_priority.luaxiedao = 7
sgs.ai_skill_choice.luaxiedao = function(self, choices)
	for _, ecard in ipairs(self:getTurnUse(true)) do
		if ecard:isKindOf("AOE") or ecard:isKindOf("Duel") then return "luayanxun" end
	end
	if self.player:getEquips():isEmpty() then return "luayanxun" end
	if self.player:getEquips() and self.player:getEquips():length() > 2 then return "luaguiwan" end
	for _, ecard in ipairs(self:getTurnUse(true)) do
		if ecard:isKindOf("Slash") then return "luayanxun" end
	end
	return "luaguiwan"
end

sgs.ai_cardneed.luahuaxian = function(to, card, self)
	if not to:hasSkill("luahuaxian") then return end
	if to:getMark("@luahuaxian") < 4 then
		if card:isKindOf("Weapon") and to:getMark("huaxianW") == 0 then return card end
		if card:isKindOf("Armor") and to:getMark("huaxianA") == 0 then return card end
		if card:isKindOf("DefensiveHorse") and to:getMark("huaxianD") == 0 then return card end
		if card:isKindOf("OffensiveHorse") and to:getMark("huaxianO") == 0 then return card end
	end
	if card:isKindOf("EquipCard") then return card end
end

sgs.ai_skill_cardask["LuaGuishen"] = function(self, data)
	self.room:writeToConsole("yuggi test")
	if self.player:hasSkill("LuaBisha") then 
		if (self:getCardsNum("Slash") < 1) and self:getOverflow() < 2 then return "." end 
	end 
	return true 
end 

sgs.ai_skill_invoke.LuaBisha = sgs.ai_skill_invoke.liegong  --如何决定技能是否发动的一个实例

local LuaYaoban_skill = {}
LuaYaoban_skill.name = "LuaYaoban"
table.insert(sgs.ai_skills, LuaYaoban_skill)

function Adjust(self, cards, suit, use_value, keep_value)
	local count = 0
	local cards_0 = {}
	local value_0 = 0
	for _, card in ipairs(cards) do
		if card:getSuit() == suit then
			count = count + 1 
			table.insert(cards_0, card)
		end 
	end 
	-- self.room:writeToConsole("阿空cards是" .. #cards)
	-- self.room:writeToConsole("阿空花色是" .. suit)
	-- self.room:writeToConsole("阿空同花色牌数量是" .. count)
	
	if count < 3 then 
		value_0 = 0 
		cards_0 = {}
	else
		if use_value then 
			self:sortByKeepValue(cards)
		elseif keep_value then 
			self:sortByUseValue(cards, true)
		end 
		for _, card in ipairs(cards) do
			if use_value then 
				value_0 = value_0 + self:getUseValue(card)
			elseif keep_value then 
				value_0 = value_0 + self:getKeepValue(card)
			end 
		end 
	end 	
	return count, cards_0, value_0
end 
function find3card(self, cards, use_value, keep_value)  --只返回最低价值的那一套
	local count = 0 
	local cards_0 = {}
	local value_0 = 0
	count, cards_0, value_0 = Adjust(self, cards, sgs.Card_Heart, use_value, keep_value)
	local count_X, cards_X, value_X = Adjust(self, cards, sgs.Card_Diamond, use_value, keep_value)
	if count_X > 2 then 
		if (value_X < value_0) or (value_0 == 0) then 
			cards_0 = cards_X
			value_0 = value_X
		end 
	end 
	count_X, cards_X, value_X = Adjust(self, cards, sgs.Card_Club, use_value, keep_value)
	if count_X > 2 then 
		if (value_X < value_0) or (value_0 == 0) then 
			cards_0 = cards_X
			value_0 = value_X
		end 
	end 	
	count_X, cards_X, value_X = Adjust(self, cards, sgs.Card_Spade, use_value, keep_value)
	if count_X > 2 then 
		if (value_X < value_0) or (value_0 == 0) then 
			cards_0 = cards_X
			value_0 = value_X
		end 
	end 
	if #cards_0 >= 3 then  
		for _, card in ipairs(cards_0) do
			self.room:writeToConsole("阿空同花色牌是" .. card:getSuitString())
		end 
	end 
	return cards_0
end 
function findAkongCard(self, no_chain_enemy, extra_cards_P)
	local blank = {}
	
	local extra_cards = {}
	if extra_cards_P then extra_cards = extra_cards_P end 
	
	local cards = sgs.QList2Table(self.player:getHandcards())  --确认是否有能用的手牌
	for _, card in ipairs(extra_cards) do
		table.insert(cards, card)
	end 
	
	local function getOverflow2()
		local x = self.player:getHandcardNum() - self.player:getMaxCards()
		x = x + #extra_cards
		return x
	end 
	
	local to_card = {}
	local slash = sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_NoSuit, 0)
	local slash_v = self:getUseValue(slash) + 1
	local dismantlement = sgs.Sanguosha:cloneCard("dismantlement", sgs.Card_NoSuit, 0)
	local dismantlement_v = self:getUseValue(dismantlement) + 1
	local jink = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
	local jink_v = self:getKeepValue(jink, true, true)
	
	if getOverflow2(self, extra_cards) > 0 then --加入多余的手牌数
		
		self:sortByKeepValue(cards)
		local cards_X = find3card(self, cards, false, true) 
		if (not cards_X) or (#cards_X < 3) then return blank end 
		self:sortByKeepValue(cards_X)
		local i = 1
		for _, card in ipairs(cards_X) do
			if #to_card >= 3 then break end 
			local shouldUse = false 
			if isCard("Peach", card, self.player) and self:OverFlowPeach(card) then shouldUse = true end
			if ((not shouldUse) and not isCard("ExNihilo", card, self.player)) or self.player:containsTrick("gainb") then table.insert(to_card, card) end --默认受伤桃和无中生有是不搞的
			if (i < getOverflow2(self, extra_cards)) then i = i + 1 else break end 
			
		end 
	end 
	self:sortByUseValue(cards, true)
	local function check(cardsA, cardX)
		for _, card in ipairs(cardsA) do
			if cardX:getId() == card:getId() then return false end 
		end 
		return true 
	end 
	for _, card in ipairs(cards) do
		if #to_card >= 3 then break end 
		if ((to_card and #to_card > 0) and (card:getSuit() == to_card[1]:getSuit()) and check(to_card, card)) or (not to_card) or (#to_card == 0) or self.player:containsTrick("gainb") then
			local shouldUse = false
			if isCard("Peach", card, self.player) and self:OverFlowPeach(card) then shouldUse = true end
			if (not shouldUse) and not isCard("ExNihilo", card, self.player) then 
				local val1 = 0
				if ((self.player:hasSkill("LuaJubian") and no_chain_enemy > 1) or (no_chain_enemy > 2)) then 
					val1 = math.max(slash_v, dismantlement_v)
				else
					val1 = math.min(slash_v, dismantlement_v)
				end 
				if (self:getUseValue(card) <= val1) or self.player:containsTrick("gainb")
					or (((self.player:hasSkill("LuaJubian") and no_chain_enemy > 1) or (no_chain_enemy > 2)) and (self:getKeepValue(card) <= jink_v)) then 
					table.insert(to_card, card)
				end 
			end 
		end 
	end 

	if #to_card == 3 then
		return to_card 
	end 

	return blank
end 
function findAkongTarget(self)
	local enemies = self.enemies
	self:sort(enemies, "defense")
	local no_chain_enemy = 0
	for _, enemy in ipairs(enemies) do
		if not enemy:isChained() then no_chain_enemy = no_chain_enemy + 1 end 
	end 
	local chain_enemy = #enemies - no_chain_enemy

	local count = 0
	for _, player in sgs.qlist(self.room:getAlivePlayers()) do
		if player:isChained() then
			local damageA = self:AtomDamageCount(enemy, self.player, sgs.DamageStruct_Fire, nil)
			if not self:isFriend(player) then 
				count = count + damageA
			else
				count = count - damageA
			end 
		end 
	end 

	if count > 0 then 
		for _, enemy in ipairs(enemies) do
			local damageA = self:AtomDamageCount(enemy, self.player, sgs.DamageStruct_Fire, nil)
			if (damageA > 0) and enemy:isChained() then 
				return enemy
			end 
		end 
	else
		if no_chain_enemy >= 2 then 
			for _, enemy in ipairs(enemies) do
				local damageA = self:AtomDamageCount(enemy, self.player, sgs.DamageStruct_Fire, nil)
				if (damageA > 0) and not enemy:isChained() then 
					return enemy	
				end 
			end 
		end 
		for _, enemy in ipairs(enemies) do
			local damageA = self:AtomDamageCount(enemy, self.player, sgs.DamageStruct_Fire, nil)
			if (damageA > 1) then 
				return enemy
			end 
		end 		
		for _, enemy in ipairs(enemies) do
			local damageA = self:AtomDamageCount(enemy, self.player, sgs.DamageStruct_Fire, nil)
			if (damageA > 0) then 
				return enemy
			end 
		end 			
	end 
	return 
end 
LuaYaoban_skill.getTurnUseCard = function(self, inclusive)
	local enemies = self.enemies
	self:sort(enemies, "defense")
	
	local damage_Max = 0  --确认是否能造成实质性伤害
	local no_chain_enemy = 0
	for _, enemy in ipairs(enemies) do
		local damageA = self:AtomDamageCount(enemy, self.player, sgs.DamageStruct_Fire, nil)
		if damageA > damage_Max then damage_Max = damageA end 
		if not enemy:isChained() then no_chain_enemy = no_chain_enemy + 1 end 
	end 
	local count_P = #enemies - no_chain_enemy
	damage_Max = math.max(damage_Max, count_P) --连锁传导伤害
	if damage_Max == 0 then return false end 
	if self:needBear() and ((damage_Max == 1) and not ((self.player:getHandcardNum() == 5) and self.player:getMaxCards() > 4)) then return end
	
	local to_card = findAkongCard(self, no_chain_enemy)
	
	if to_card and (#to_card > 2) then return sgs.Card_Parse("#LuaYaoban:.:") end 
	return
end 

sgs.ai_skill_use_func["#LuaYaoban"] = function(X, use, self)
	local enemies = self.enemies
	self:sort(enemies, "defense")
	local best_target = findAkongTarget(self)
	local no_chain_enemy = 0
	
	for _, enemy in ipairs(enemies) do
		if not enemy:isChained() then no_chain_enemy = no_chain_enemy + 1 end 
	end 
	
	local to_card = findAkongCard(self, no_chain_enemy)	
	if not to_card then return end 
	local card_ids = {}
	for _, card in ipairs(to_card) do
		table.insert(card_ids, card:getId())
	end 
	if #card_ids ~= 3 then return end 
	if best_target then 
		use.card = sgs.Card_Parse("#LuaYaoban:".. table.concat(card_ids, "+")..":")
		if use.to then
			if no_chain_enemy >= 2 then
				for _, enemy in ipairs(enemies) do
					if not enemy:isChained() then use.to:append(enemy) end 
					if use.to:length() == 3 then return end
				end
				return
			else
				use.to:append(best_target)
				return
			end 
		end 
	end 
	
end 

sgs.ai_skill_invoke.LuaXinXing = function(self, data)
	if self.player:containsTrick("gaina") then return false end --你看那么多管什么用
	return true
end 
function findUsefulSuit(self, suit, card_ids, no_chain_enemy, removelist)
	local image = {}
	for _,id in sgs.qlist(card_ids) do
		local c = sgs.Sanguosha:getCard(id)
		if c:getSuit() == suit then
			table.insert(removelist, c)
		end			
	end
	image = findAkongCard(self, no_chain_enemy, removelist)
	if image and (#image == 3) and (#removelist > 0) then 
		for _, card in ipairs(image) do
			self.room:writeToConsole("阿空预热炮弹是" .. card:getId())
		end 
		return removelist[1]:getId() 
	end 
	return -99 
end 
sgs.ai_skill_askforag.LuaXinXing = function(self, card_ids)
	local best_target = findAkongTarget(self)
	local no_chain_enemy = 0
	
	local enemies = self.enemies
	self:sort(enemies, "defense")
	
	for _, enemy in ipairs(enemies) do
		if not enemy:isChained() then no_chain_enemy = no_chain_enemy + 1 end 
	end 
	
	self.room:writeToConsole("灵乌路空测试1")
	local x = self.player:getMaxCards() - self.player:getHandcardNum()
	for _, id in ipairs(card_ids) do
		local countX = 0 
		local c = sgs.Sanguosha:getCard(id)
		for _, id2 in ipairs(card_ids) do
			local c2 = sgs.Sanguosha:getCard(id2)
			if id2 ~= id and (c2:getSuit() == c:getSuit()) then 
				countX = countX + 1
			end 
		end 
		if countX > 1 then self.room:writeToConsole("灵乌路空测试4"); return id end 
		if (countX == 1) and ((self.player:hasSkill("LuaHebao") and (self.player:getMark("LuaHebao") == 0) and (self.player:getHandcardNum() == 5))) then return id end 
		if (countX == 1) and (x > 1) then self.room:writeToConsole("灵乌路空测试5"); return id end 
	end
	
	local P = findAkongCard(self, no_chain_enemy)
	if p and best_target and (#p ~= 3) then 
		local removelist = {}
		local id = findUsefulSuit(self, sgs.Card_Spade , card_ids, no_chain_enemy, removelist)
		if id >= 0 then self.room:writeToConsole("灵乌路空测试3"); return id end 
		removelist = {}		
		id = findUsefulSuit(self, sgs.Card_Club , card_ids, no_chain_enemy, removelist)
		if id >= 0 then self.room:writeToConsole("灵乌路空测试3"); return id end 
		removelist = {}		
		id = findUsefulSuit(self, sgs.Card_Diamond , card_ids, no_chain_enemy, removelist)
		if id >= 0 then self.room:writeToConsole("灵乌路空测试3"); return id end 
		removelist = {}
		id = findUsefulSuit(self, sgs.Card_Heart , card_ids, no_chain_enemy, removelist)
		if id >= 0 then self.room:writeToConsole("灵乌路空测试3"); return id end 
		removelist = {}
	end 
	

	
	self.room:writeToConsole("灵乌路空测试2")
	local Luaduhuo2 = card_ids
	local cards = {}
	for _, id in ipairs(Luaduhuo2) do
		local card = sgs.Sanguosha:getCard(id)
		table.insert(cards, card)
	end
	if (self:isWeak() or (self.player:getHandcardNum() <= 1)) then 
		self:sortByKeepValue(cards, true)
	else
		self:sortByUseValue(cards)
	end 
	return cards[1]:getId()	
end 

sgs.ai_skill_choice.LuaYaoban2 = function(self, choices)
	local enemies = self.enemies
	self:sort(enemies, "defense")
	local no_chain_enemy = 0
	for _, enemy in ipairs(enemies) do
		if not enemy:isChained() then no_chain_enemy = no_chain_enemy + 1 end 
	end 
	local chain_enemy = #enemies - no_chain_enemy
	
	if chain_enemy > 1 then return "chain" end 
	return "damage"
end 

sgs.ai_use_priority.LuaYaoban = function(self)
	local x = #self.enemies
	x = 3.8 + 3.2*x
	return x
end 

local LuaYuanzu_skill={}
LuaYuanzu_skill.name="LuaYuanzu"
table.insert(sgs.ai_skills,LuaYuanzu_skill)
LuaYuanzu_skill.getTurnUseCard = function(self)
	local cards = self.player:getCards("h")
	cards = sgs.QList2Table(cards)

	local diamond_card

	self:sortByUseValue(cards,true)



	local disCrossbow = false
	if self:getCardsNum("Slash") < 2 or self.player:hasSkill("paoxiao") then
		disCrossbow = true
	end

	
	for _,card in ipairs(cards)  do
		local useAll = false
		local slash_0 = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
		slash_0:addSubcard(card)
		slash_0:setSkillName("LuaYuanzu")		
		if self:canKillEnermyAtOnce(false, slash_0) then useAll = true end 
	
		local heavy_dmg = false 
		local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
		self:useBasicCard(slash_0, dummy_use)
		if not dummy_use.to:isEmpty() then
			for _, playerX in sgs.qlist(dummy_use.to) do
				if self:hasHeavySlashDamage(self.player, slash_0, playerX) then heavy_dmg = true end 
			end 
		end 	
		local snatch = sgs.Sanguosha:cloneCard("snatch")
		local dismantlement = sgs.Sanguosha:cloneCard("slash")
		local val1 = self:getUseValue(snatch)
		local val0 = self:getUseValue(dismantlement) + 1
		local bool_0 = (self:getUseValue(card) <= val0) or (heavy_dmg and (self:getUseValue(card) <= val1))
		
		if card:isRed() and not (card:isKindOf("Ofuda") and self.player:getJudgingArea():length() > 1)
		and (((not self:OverFlowPeach(card)) and not isCard("ExNihilo", card, self.player)) or useAll)
		and (not isCard("Crossbow", card, self.player) and not disCrossbow)
		and (bool_0 or inclusive or sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_Residue, self.player, sgs.Sanguosha:cloneCard("slash")) > 0) then
			diamond_card = card
			break
		end
	end

	if not diamond_card then return nil end
	local suit = diamond_card:getSuitString()
	local number = diamond_card:getNumberString()
	local card_id = diamond_card:getEffectiveId()
	local card_str = ("slash:LuaYuanzu[%s:%s]=%d"):format(suit, number, card_id)
	local slash = sgs.Card_Parse(card_str)
	assert(slash)

	return slash

end 
sgs.ai_view_as.LuaYuanzu = function(card, player, card_place, class_name)
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	if card_place == sgs.Player_PlaceHand and card:isRed() and not card:hasFlag("using") then
		if class_name == "Slash" then
			return ("slash:LuaYuanzu[%s:%s]=%d"):format(suit, number, card_id)
		elseif class_name == "Jink" then
			return ("jink:LuaYuanzu[%s:%s]=%d"):format(suit, number, card_id)
		end
	end
end

sgs.ai_skill_invoke.LuaShanguang = function(self, data)
	local damage = data:toDamage()
	local to = damage.to
	if not self:damageIsEffective(to) then return false end
	if self:isFriend(to) then return false end 
	local x = self.player:getJudgingArea():length()
	self.room:writeToConsole("shanguang test " .. damage.damage)
	if damage.damage > 1 and x <= 3 then return false end
	if ((x == 1) and self:isWeak()) then 
		return math.random() > 0.35
	end 
	if x == 2 then 
		if to:getHp() <= 2 then return true end 
		if self:isWeak() then 
			if not to:isNude() then return true else return math.random() > 0.5 end 
		end 
		if (damage.nature ~= sgs.DamageStruct_Normal) and to:isChained() then 
			return true
		end 
		return false 
	end 
	if x > 2 then 
		return true 
	end 
end 

sgs.ai_skill_choice.LuaShanguang = function(self, choices, data)
	local damage = data:toDamage()
	local to = damage.to
	local count = self.player:getJudgingArea():length()
	if to:getHp() == 1 then 
		if count > 2 then count = 2 end 
	end 
	return tostring(count)
end

sgs.ai_cardneed.LuaShanguang = function(to, card)
	if not to:hasSkill("LuaShanguang") then return end
	if to:getWeapon() then return end
	return card:isKindOf("Weapon")
end

sgs.ai_skill_discard.luaqijian = function(self, discard_num, optional, include_equip)
	local cards = sgs.QList2Table(self.player:getHandcards())
	local to_discard = {}
	local function fatusevalue(a)
		local v = self:getUseValue(cardPay)
		if a:isKindOf("Weapon") then return 0 end 
		if a:isKindOf("OffensiveHorse") or a:isKindOf("DefensiveHorse") then return 0 end 
		if a:isKindOf("Slash") then 
			local k = v - 1.2
			return k 
		end 
		return v 
	end 
    local compare_func = function(a, b)
        local value1 = fatusevalue(a)
        local value2 = fatusevalue(b)
		
        if value1 ~= value2 then
            return value1 > value2
        else
            return a:getNumber() > b:getNumber()
        end
    end
	table.sort(cards, compare_func)
	for _, card in ipairs(cards) do
		if #to_discard >= discard_num then break end
		table.insert(to_discard, card:getId())
	end

	return to_discard
end

local luaqijian_skill = {}
luaqijian_skill.name = "luaqijian"
table.insert(sgs.ai_skills, luaqijian_skill)


luaqijian_skill.getTurnUseCard = function(self, inclusive)
	if self.player:getPile("sword"):length() == 0 then return end 
	if self.player:hasSkill("luawushuang") and self.player:hasSkill("luajuezhan") then return end
	if not self.player:getWeapon() then return sgs.Card_Parse("#luaqijian:.:") end
	local count = 0
	local count2 = 0
	for _, acard in sgs.qlist(self.player:getHandcards()) do
		if not self.player:isCardLimited(acard, sgs.Card_MethodUse) then 
			count = count + 1 
			if acard:getSuit() == self.player:getWeapon():getSuit() then count2 = count2 + 1 end 
		end 
	end 
	if count2  >= 2 then return end
	if self.player:getHandcardNum() > 2 then
		return sgs.Card_Parse("#luaqijian:.:")
	end
end 
sgs.ai_skill_use_func["#luaqijian"] = function(card, use, self)
	if self.player:getWeapon() then
		local cards = self:getTurnUse(true)
		local bool1 = false
		local bool2 = false
		for _, acard in ipairs(cards) do
			if acard:isKindOf("Slash") then
				bool1 = true
			end
			if acard:isKindOf("Duel") then
				bool1 = true
			end
		end
		if bool1 and bool2 then return end
	end
	use.card = sgs.Card_Parse("#luaqijian:.:")
	if use.to then 
		use.to:append(self.player) 
		return 
	end 
end 
sgs.ai_skill_askforag.luaqijian = function(self, card_ids)
	local Luaduhuo2 = card_ids
	local cards = {}
	for _, id in ipairs(Luaduhuo2) do
		local card = sgs.Sanguosha:getCard(id)
		table.insert(cards, card)
	end
	local function fatusevalue(a)
		local v = self:getUseValue(a)
		if a:isKindOf("Weapon") then return 0 end 
		if a:isKindOf("OffensiveHorse") or a:isKindOf("DefensiveHorse") then return 0 end 
		if a:isKindOf("Slash") then 
			local k = v - 1.2
			return k 
		end 
		return v 
	end 
    local function compare_func(a, b)
        local value1 = fatusevalue(a)
        local value2 = fatusevalue(b)
		
        if value1 ~= value2 then
            return value1 < value2
        else
            return a:getNumber() < b:getNumber()
        end
    end
	table.sort(cards, compare_func)
	return cards[1]:getId()
end

local luazhengyi_skill = {}
luazhengyi_skill.name = "luazhengyi"
table.insert(sgs.ai_skills, luazhengyi_skill)

local function HowtoUse(self)
	local cards = self:getTurnUse(true)

	if (self.player:getMark("drank") == 0) then
		local peach = sgs.Sanguosha:cloneCard("peach", sgs.Card_NoSuit, 0)
		local dummyuseW = { isDummy = true }
		self:useBasicCard(peach, dummyuseW)
		if dummyuseW.card then
			return "peach"
		end
	end

	if not self:slashIsAvailable() then return end
	local bool1 = false
	local count = 0
	for _, acard in ipairs(cards) do
		if acard:isKindOf("Slash") then
			self.room:writeToConsole("luazhengyi turn use slash" .. acard:getEffectiveId())
			count = count + 1
			bool1 = true
		end  
	end 
	self.room:writeToConsole("luazhengyi prepare slash")
	if not bool1 then 
		local slash = {}
		slash[1] = "fire_slash"
		slash[2] = "thunder_slash"
		slash[3] = "slash"
		for i = 1, 3 do
			local basic2 = sgs.Sanguosha:cloneCard(slash[i], sgs.Card_SuitToBeDecided, -1)
			basic2:addSubcard(self:justice():getEffectiveId())
			if self:YouMu()  then 
				local dummyuse = { isDummy = true }
				self:useBasicCard(basic2, dummyuse)
				if dummyuse.card then 
					return slash[i]  
				end 
			end 
		end 
	end 
end 
luazhengyi_skill.getTurnUseCard = function(self)
	if not self.player:getWeapon() then return end
	return sgs.Card_Parse("#luazhengyi:.:")
end
sgs.ai_skill_use_func["#luazhengyi"] = function(card, use, self)
	local card0 = self:justice()
	if not card0 then return end
	local userstring = HowtoUse(self)
	if not userstring then return end
	local taoluancard = sgs.Sanguosha:cloneCard(userstring, card0:getSuit(), card0:getNumber())

	taoluancard:setSkillName("luazhengyi")
	
	taoluancard:addSubcard(card0:getId())
	
	local dummy_use = { isDummy = false , to = sgs.SPlayerList() }
	
	self:useBasicCard(taoluancard, dummy_use)
	if userstring == "jink" and userstring == "nullification" then return false end
	if not dummy_use.card then return end
	if dummy_use.to and not dummy_use.to:isEmpty() then 
		if dummy_use.to[1] then 
			self.room:writeToConsole("sanae test target" .. dummy_use.to[1]:objectName())
		end 
	end
	use.card = taoluancard
	if use.to then 
		use.to = dummy_use.to 
		return
	end 
	return
end 
sgs.ai_view_as.luazhengyi = function(card, player, card_place, class_name)
	if not player:getWeapon() then return end

	local abc = string.lower(class_name)
	local slash = sgs.Sanguosha:cloneCard(abc)
	if not slash then return end
	if (not slash:isKindOf("BasicCard")) and not slash:isKindOf("Nullification") then return end

	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	if card_place == sgs.Player_PlaceSpecial then return end
	if abc == "slash" and card:getEffectiveId() == player:getWeapon():getEffectiveId()
		and not player:getHp() == 1 then return end

	if card:getSuit() == player:getWeapon():getSuit() and not card:isKindOf(class_name) then
		return (abc .. ":luazhengyi[%s:%s]=%d"):format(suit, number, card_id) --六到无话可说
	end
end

sgs.ai_use_priority.luazhengyi = 3.2
sgs.ai_use_priority.luaqijian = 15
sgs.ai_skill_cardask["@luadanmu"] = sgs.ai_skill_cardask["@Luashenpan"]

sgs.ai_cardneed.luazhengyi = function(to, card, self)
	return card:isKindOf("Weapon") and not to:getWeapon() and to:getPile("sword"):length() == 0
end 


sgs.ai_card_intention.luadanmu = 30

sgs.ai_skill_playerchosen.luayuetuan = function(self, targets)
	if self.player:getHandcardNum() > 2 then 
		local friends = self.friends_noself
		self:sort(friends, "handcard2")
		for _, friend in ipairs(friends) do
			if friend:hasSkill("luasanaex") and not friend:hasFlag("luasanaex") then return friend end
		end
		for _, friend in ipairs(friends) do
			if friend:hasSkills(sgs.cardneed_skill) and friend:getHandcardNum() <= 3 then return friend end
		end
		for _, friend in ipairs(friends) do
			if self:isWeak(friend) and friend:getHandcardNum() <= 4 then return friend end 
		end 
		for _, friend in ipairs(friends) do
			if friend:hasSkills(sgs.cardneed_skill) then return friend end 
		end 			
	end 
	return self.player
end

sgs.ai_skill_discard["luayuetuan"] = function(self, discard_num, min_num, optional, include_equip)
	if self.player:isNude() then return "." end
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	if not (cards[1]:isKindOf("Snatch") and self:ThreeCheck(cards[1])) and not (cards[1]:isKindOf("Peach") and not self:OverFlowPeach(cards[1]))
		and not cards[1]:isKindOf("Wanbaochui") and not cards[1]:isKindOf("Hui") then
		return cards[1]:getId()
	end
end
sgs.ai_need_damaged.luayuetuan = function(self, attacker, player)
	if self.player:isNude() then return false end
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	if not (cards[1]:isKindOf("Snatch") and self:ThreeCheck(cards[1])) and not (cards[1]:isKindOf("Peach") and not self:OverFlowPeach(cards[1]))
			and not cards[1]:isKindOf("Wanbaochui") and not cards[1]:isKindOf("Hui") then
		if self.player:getHp() > 2 then return true end
	end
end
sgs.ai_skill_playerchosen.luayuechong = function(self, targets)
	local card = self.room:getTag("luayuechongTC"):toCard()
	local dummy_use = { isDummy = true , to = sgs.SPlayerList(), distance = 99 }
	if card:isKindOf("EquipCard") then
		for _, friend in ipairs(self.friends) do
			if friend:hasSkills(sgs.need_equip_skill) and not self:getSameEquip(card, friend) then
				return friend
			end
		end
		for _, friend in ipairs(self.friends) do
			if not self:getSameEquip(card, friend) then
				return friend
			end
		end
	end
	if card:isKindOf("Hui") then
		for _, enemy in ipairs(self.enemies) do
			if self:damageIsEffective(enemy, nil, enemy) then
				return enemy
			end
		end
	end
	if card:isKindOf("Peach") then
		for _, friend in ipairs(self.friends) do
			if friend:isWounded() and ((not friend:hasSkill("luasanaey")) or friend:getPhase() ~= sgs.Player_NotActive) then
				return friend
			end
		end
	end
	if card:isKindOf("Analeptic") or card:isKindOf("ExNihilo") or card:isKindOf("Banquet") then
		return self.player
	end
	if card:getTypeId() == sgs.Card_TypeBasic then
		self:useBasicCard(card, dummy_use)
	else
		self:useTrickCard(card, dummy_use)
	end
	
	if dummy_use.to and not dummy_use.to:isEmpty() then
		for _, p in sgs.qlist(dummy_use.to) do
			return p
		end
	end

end 

sgs.ai_card_intention.luayuetuan = -40

--魔理沙ai

sgs.ai_skill_playerchosen.luaaoshu = function(self, targets)
	local enemies = self.enemies
	self:sort(enemies, "defense")
	local friends = self.friends_noself
	self:sort(friends, "defense")
	for _, enemy in ipairs(enemies) do
		if not enemy:hasSkill("tuntian") and not (enemy:hasSkill("luasanaex") and not enemy:hasFlag("luasanaex")) then
			local x = enemy:getHandcardNum() + 3
			local zuiduo = true
			for _,p in sgs.qlist(self.room:getAlivePlayers()) do
				if p:getHandcardNum() > x then zuiduo = false end
			end
			if zuiduo then
				if x >= 2 and (self:getDangerousCard(enemy) or enemy:hasArmorEffect("eight_diagram")) then
					return enemy
				end
			end
		end
	end
	for _, enemy in ipairs(enemies) do
		if not enemy:hasSkill("tuntian") and not (enemy:hasSkill("luasanaex") and not enemy:hasFlag("luasanaex")) then
			local x = enemy:getHandcardNum() + 3
			local zuiduo = true
			for _,p in sgs.qlist(self.room:getAlivePlayers()) do
				if p:getHandcardNum() > x then zuiduo = false end
			end
			if zuiduo then
				if x > 3 then
					return enemy
				end
			end
		end
	end

	for _, friend in ipairs(friends) do
		local x = friend:getHandcardNum() + 3
		local zuiduo = true
		for _,p in sgs.qlist(self.room:getAlivePlayers()) do
			if p:getHandcardNum() > x then zuiduo = false end
		end
		if zuiduo then
			return friend
		end
	end

	for _, enemy in ipairs(enemies) do
		if not enemy:hasSkill("tuntian") and not (enemy:hasSkill("luasanaex") and not enemy:hasFlag("luasanaex")) then
			local x = enemy:getHandcardNum() + 3
			local zuiduo = true
			for _,p in sgs.qlist(self.room:getAlivePlayers()) do
				if p:getHandcardNum() > x then zuiduo = false end
			end
			if zuiduo then
				if x == 2 then
					return enemy
				end
			end
		end
	end

	if #friends > 0 then return friends[1] end
	return nil
end
sgs.ai_skill_cardchosen["luaaoshu"] = function(self, who, flags)
	if self:isFriend(who) then
		return self:getCardRandomly(who, "h")
	end
end
local function findDMTarget(self)
	local x = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end

	local function Check_R(card)
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return end
		end
		if card:isKindOf("Peach") or card:isKindOf("Analeptic") then return false end
		if x <= 1 and card:isKindOf("Jink") then self.room:writeToConsole("findDMTarget test") ;return false end
		if card:isKindOf("ExNihilo") or card:isKindOf("Duel") then return false end
		if card:isKindOf("Lightning") and self:willUseLightning(card) then return false end
		if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 5 then return false end
		if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
		if card:isKindOf("Indulgence") then return false end
		if card:isKindOf("EquipCard") then
			local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
			if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) and rinnosuke
					and rinnosuke:isAlive() and self:isFriend(rinnosuke) then return false end
		end
		if card:isKindOf("IronChain") and shuxin then return false end
		if (card:isKindOf("AOE") and self:getAoeValue(card) > 40) then return false end
		if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
		local bool_2 = card:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip))
		local bool_3 = (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu"))
				and ((self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
				or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
		if bool_3 or bool_2 then return false end
		return true
	end

	local function not_Keiki(target) --返回true就不能打
		local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), target:objectName())
		if target:hasSkill("luaouxiang") then
			if target:getHandcardNum() > 3 then return true end
			if target:hasLordSkill("luashenxing") then
				for _, cc in ipairs(target:getHandcards()) do
					if (cc:hasFlag("visible") or cc:hasFlag(flag)) and (cc:getSuit() == sgs.Card_Heart) then
						return true
					end
				end
			end
			for _, enemy in ipairs(self.enemies) do
				for _, cc in ipairs(enemy:getHandcards()) do
					if (cc:hasFlag("visible") or cc:hasFlag(flag)) and (isCard("Peach", cc, enemy) or isCard("Analeptic", cc, enemy)) then
						return true
					end
				end
			end
		end
		if target:hasSkill("luayuechong") and target:getHandcardNum() < 2 then return true end
		if target:hasSkill("luaqiji") and target:getHandcardNum() > 1 and target:getLostHp() > 1 and target:getHp() > 1 then return true end
		return false
	end
	for _,p in sgs.qlist(self.room:getAlivePlayers()) do
		for _, card in sgs.list(self.player:getHandcards()) do
			if self.player:distanceTo(p) == 1 and self.player:hasSkill("kuanggu") then
				if ((p:getHandcardNum() == self.player:getHandcardNum() - 1) or ((p:getHandcardNum() == self.player:getHandcardNum()) and self.player:hasWeapon("hakkero") and card:isKindOf("Slash")))
						and self:isEnemy(p) and self:damageIsEffective(p, nil, self.player) and not not_Keiki(p)
						and self.player:inMyAttackRange(p) and not (self:getDamagedEffects(p, self.player) or (self:needToLoseHp(p, self.player, false, true) and #self.enemies > 1)) then
					self.room:writeToConsole("findDMTarget test3")
					return card, p
				end
			end
		end
	end

	for _, card in sgs.list(self.player:getHandcards()) do
		if Check_R(card) then
			for _,p in sgs.qlist(self.room:getAlivePlayers()) do
				if ((p:getHandcardNum() == self.player:getHandcardNum() - 1) or ((p:getHandcardNum() == self.player:getHandcardNum()) and self.player:hasWeapon("hakkero") and card:isKindOf("Slash")))
						and self:isEnemy(p) and self:damageIsEffective(p, nil, self.player) and not not_Keiki(p)
					and self.player:inMyAttackRange(p) and not (self:getDamagedEffects(p, self.player) or (self:needToLoseHp(p, self.player, false, true) and #self.enemies > 1)) then
					self.room:writeToConsole("findDMTarget test3")
					return card, p
				end
			end
		end
	end
end
local luadanmu_skill = {}
luadanmu_skill.name = "luadanmu"
table.insert(sgs.ai_skills, luadanmu_skill)
luadanmu_skill.getTurnUseCard = function(self)
	return sgs.Card_Parse("#luadanmu:.:")
end
sgs.ai_skill_use_func["#luadanmu"] = function(X, use, self)
	local card,target = findDMTarget(self)
	if card and target then
		use.card = sgs.Card_Parse("#luadanmu:.:")
		if use.to then use.to = sgs.SPlayerList() end
		return
	end
	if #self:getTurnUse(true) == 0 and self:getOverflow() > 0 then
		use.card = sgs.Card_Parse("#luadanmu:.:")
		if use.to then use.to = sgs.SPlayerList() end
		return
	end
end
sgs.ai_use_priority.luadanmu = 7
sgs.ai_skill_discard.luadanmu = function(self, discard_num, min_num, optional, include_equip)
	local cards = self:getTurnUse(true)
	if #cards == 0 and self:getOverflow() > 0 then
		local handcards = sgs.QList2Table(self.player:getHandcards())
		self:sortByKeepValue(handcards)
		return handcards[1]:getId()
	end
	local card, p = findDMTarget(self)
	self.room:setPlayerFlag(p, "luadanmuTag")
	return card:getId()
end
sgs.ai_skill_playerchosen.luadanmu = function(self, targets)
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasFlag("luadanmuTag") then
			self.room:setPlayerFlag(p, "-luadanmuTag")
			return p
		end
	end
	local targetlist = sgs.QList2Table(targets)
	for _, target in ipairs(targetlist) do	--杀敌
		if self:isEnemy(target) then return target end
	end
end

--萃香ai
local luajiuchi_skill = {}
luajiuchi_skill.name = "luajiuchi"
table.insert(sgs.ai_skills,luajiuchi_skill)
luajiuchi_skill.getTurnUseCard=function(self)
	local cards = self.player:getCards("h")
	cards=sgs.QList2Table(cards)

	local card

	self:sortByUseValue(cards,true)

	for _,acard in ipairs(cards)  do
		if acard:getSuit() == sgs.Card_Spade then
			card = acard
			break
		end
	end

	if not card then return nil end
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	local card_str = ("analeptic:luajiuchi[spade:%s]=%d"):format(number, card_id)
	local analeptic = sgs.Card_Parse(card_str)

	if sgs.Analeptic_IsAvailable(self.player, analeptic) then
		assert(analeptic)
		return analeptic
	end
end

sgs.ai_view_as.luajiuchi = function(card, player, card_place)
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	if card_place == sgs.Player_PlaceHand then
		if card:getSuit() == sgs.Card_Spade then
			return ("analeptic:luajiuchi[%s:%s]=%d"):format(suit, number, card_id)
		end
	end
end

sgs.ai_cardneed.luajiuchi = function(to, card, self)
	if not self then return false end
	return card:getSuit() == sgs.Card_Spade and (getKnownCard(to, self.player, "club", false) + getKnownCard(to, self.player, "spade", false)) == 0
end
 
local luasuiyue_skill = {}
luasuiyue_skill.name = "luasuiyue"
table.insert(sgs.ai_skills, luasuiyue_skill)
luasuiyue_skill.getTurnUseCard = function(self)
	if self.player:containsTrick("gainb") then
		sgs.ai_use_priority.luasuiyue = 5
	else
		sgs.ai_use_priority.luasuiyue = 0
	end
	if (not self.player:hasUsed("#luasuiyue")) and (not self.player:isKongcheng())
		and (self:YouMu() or (self.player:getHandcardNum() < 3)) then
		return sgs.Card_Parse("#luasuiyue:.:")
	end
end

sgs.ai_skill_use_func["#luasuiyue"] = function(X, use, self)
	local unpreferedCards = {}
	local cards = sgs.QList2Table(self.player:getHandcards())

	if self.player:getHp() < 3 then
		local zcards = self.player:getCards("h")
		local use_slash, keep_jink, keep_analeptic, keep_weapon = false, false, false
		local keep_slash = self.player:getTag("JilveWansha"):toBool()
		for _, zcard in sgs.qlist(zcards) do
			if not isCard("Peach", zcard, self.player) and not isCard("ExNihilo", zcard, self.player) then
				local shouldUse = true
				if isCard("Slash", zcard, self.player) and not use_slash then
					local dummy_use = { isDummy = true , to = sgs.SPlayerList()}
					self:useBasicCard(zcard, dummy_use)
					if dummy_use.card then
						if keep_slash then shouldUse = false end
						if dummy_use.to then
							for _, p in sgs.qlist(dummy_use.to) do
								if p:getHp() <= 1 then
									shouldUse = false
									if self.player:distanceTo(p) > 1 then keep_weapon = self.player:getWeapon() end
									break
								end
							end
							if dummy_use.to:length() > 1 then shouldUse = false end
						end
						if not self:isWeak() then shouldUse = false end
						if not shouldUse then use_slash = true end
					end
				end
				if zcard:getTypeId() == sgs.Card_TypeTrick then
					local dummy_use = { isDummy = true }
					self:useTrickCard(zcard, dummy_use)
					if dummy_use.card then shouldUse = false end
				end
				if zcard:getTypeId() == sgs.Card_TypeEquip and not self.player:hasEquip(zcard) then
					local dummy_use = { isDummy = true }
					self:useEquipCard(zcard, dummy_use)
					if dummy_use.card then shouldUse = false end
					if keep_weapon and zcard:getEffectiveId() == keep_weapon:getEffectiveId() then shouldUse = false end
				end
				if self.player:hasEquip(zcard) and zcard:isKindOf("Armor") and not self:needToThrowArmor() then shouldUse = false end
				if self.player:hasEquip(zcard) and zcard:isKindOf("DefensiveHorse") and not self:needToThrowArmor() then shouldUse = false end
				if self.player:hasEquip(zcard) and zcard:isKindOf("WoodenOx") and self.player:getPile("wooden_ox"):length() > 0 then shouldUse = false end	--yun
				if isCard("Jink", zcard, self.player) and not keep_jink then
					keep_jink = true
					shouldUse = false
				end
				if self.player:getHp() == 1 and isCard("Analeptic", zcard, self.player) and not keep_analeptic then
					keep_analeptic = true
					shouldUse = false
				end
				if shouldUse then table.insert(unpreferedCards, zcard:getId()) end
			end
		end
	end

	if #unpreferedCards == 0 then
		local use_slash_num = 0
		self:sortByKeepValue(cards)
		for _, card in ipairs(cards) do
			if card:isKindOf("Slash") then
				local will_use = false
				if use_slash_num <= sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_Residue, self.player, card) then
					local dummy_use = { isDummy = true }
					self:useBasicCard(card, dummy_use)
					if dummy_use.card then
						will_use = true
						use_slash_num = use_slash_num + 1
					end
				end
				if not will_use then table.insert(unpreferedCards, card:getId()) end
			end
		end

		local num = self:getCardsNum("Jink") - 1
		if self.player:getArmor() then num = num + 1 end
		if num > 0 then
			for _, card in ipairs(cards) do
				if card:isKindOf("Jink") and num > 0 then
					table.insert(unpreferedCards, card:getId())
					num = num - 1
				end
			end
		end
		for _, card in ipairs(cards) do
			if (card:isKindOf("Weapon") and self.player:getHandcardNum() < 3) or card:isKindOf("OffensiveHorse")
				or self:getSameEquip(card, self.player) or card:isKindOf("AmazingGrace") then
				table.insert(unpreferedCards, card:getId())
			elseif card:getTypeId() == sgs.Card_TypeTrick then
				local dummy_use = { isDummy = true }
				self:useTrickCard(card, dummy_use)
				if not dummy_use.card then table.insert(unpreferedCards, card:getId()) end
			end
		end

		if self.player:getWeapon() and self.player:getHandcardNum() < 3 then
			table.insert(unpreferedCards, self.player:getWeapon():getId())
		end

		if self:needToThrowArmor() then
			table.insert(unpreferedCards, self.player:getArmor():getId())
		end

		if self.player:getOffensiveHorse() and self.player:getWeapon() then
			table.insert(unpreferedCards, self.player:getOffensiveHorse():getId())
		end
	end

	for index = #unpreferedCards, 1, -1 do
		if sgs.Sanguosha:getCard(unpreferedCards[index]):isKindOf("WoodenOx") and self.player:getPile("wooden_ox"):length() > 0 then	--yun
			table.removeOne(unpreferedCards, unpreferedCards[index])
		end
	end

	local use_cards = {}
	for index = #unpreferedCards, 1, -1 do
		local card = sgs.Sanguosha:getCard(unpreferedCards[index]) 	--yun
		if not self.player:isJilei(card) and not self.player:isCardLimited(card, sgs.Card_MethodDiscard) then
			table.insert(use_cards, unpreferedCards[index]) 
		end
	end
	local p = self.player:getHandcardNum()
	
	local targets
	local slash = sgs.Sanguosha:cloneCard("slash")
	local dummy_use = { isDummy = true , to = sgs.SPlayerList() }
	self:useBasicCard(slash, dummy_use)	
	if dummy_use.card and dummy_use.to and dummy_use.to:length() > 0 then 
		
		targets = dummy_use.to
	end 

	if not targets then return end 
	if targets:isEmpty() then return end 
			
	if not use.isDummy then
		local analeptic = self:searchForAnaleptic(use, targets:at(0), slash)
		if analeptic and self:shouldUseAnaleptic(targets:at(0), slash) 
			and ((not self.player:hasFlag("jianji")) or (self.player:getPhase() ~= sgs.Player_Play) or (self.player:getMark("jianji") > 1)) then
			use.card = analeptic
			if use.to then use.to = sgs.SPlayerList() end
			return
		end
	end 
	
	if (#use_cards > p - 2) or (p < 2) or self.player:containsTrick("gainb") then
		
		local givecard = sgs.QList2Table(self.player:getHandcards())
		local asdfg = {}
		for _, card in ipairs(givecard) do
			table.insert(asdfg, card:getId())
		end 
		use.card = sgs.Card_Parse("#luasuiyue:" .. table.concat(asdfg, "+").. ":") 
		if use.to then use.to = targets end
		return		
	end
end

sgs.ai_cardneed.luasuiyue = function(to, card)
	if not to:hasSkill("luasuiyue") then return end
	return card:isKindOf("Weapon")
end

local Luaxiuyue_skill = {}
Luaxiuyue_skill.name = "Luaxiuyue"
table.insert(sgs.ai_skills, Luaxiuyue_skill)
Luaxiuyue_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#Luaxiuyue") then return end 
	local cards = self:getTurnUse(true)
	local x = #cards
	local y = self.player:getHandcardNum() -self.player:getMaxCards() - x
	local useSlash
	for _, card in ipairs(cards) do
		if card:isKindOf("Slash") then useSlash = true end
	end 	
	if self.player:getMark("Luaxiuyue1") == 0 then 
		if self.player:getHp() == 5 then return sgs.Card_Parse("#Luaxiuyue:.:") end
		if self.player:getHp() == 4 and y < 1 and useSlash then return sgs.Card_Parse("#Luaxiuyue:.:") end
		if self.player:getHp() == 3 and not useSlash then return sgs.Card_Parse("#Luaxiuyue:.:") end 
		if self.player:getHp() == 2 and not self.player:isKongcheng() then return sgs.Card_Parse("#Luaxiuyue:.:") end 
	else
		if not ((self.player:getHp() == 2) and not useSlash) then
			return sgs.Card_Parse("#Luaxiuyue:.:") 
		end 		
	end 
end 

sgs.ai_skill_use_func["#Luaxiuyue"] = function(card, use, self)
	use.card = sgs.Card_Parse("#Luaxiuyue:.:") 
	if use.to then use.to:append(self.player) end
	return		
end 

sgs.ai_use_priority.Luaxiuyue = function(self)
	if self.player:getHp() == 4 and (self.player:getMark("Luaxiuyue1") == 0) then 
		return 6
	end 
	if self.player:getHp() == 2 and (self.player:getMark("Luaxiuyue1") == 1) then 
		return 6
	end 
	return 7
	
end

sgs.ai_skill_invoke.luachongsheng = function(self, data)
	if self.player:getHp() <= 2 then return true end
	return false
end

local luahonghun_skill = {}
luahonghun_skill.name = "luahonghun"
table.insert(sgs.ai_skills, luahonghun_skill)
luahonghun_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luahonghun") then return end
	return sgs.Card_Parse("#luahonghun:.:")
end

sgs.ai_skill_use_func["#luahonghun"] = function(card, use, self)
	local card_0
	local handcards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(handcards)
	self:sortByUseValue(handcards, true)
	for _, cardS in ipairs(handcards) do
		if (not cardS:isKindOf("AOE") and self:getAoeValue(cardS) > 35) and not cardS:isKindOf("FireAttack")
			and (not cardS:isKindOf("GodSalvation") and self:godSalvationValue(cardS) > 10) then
			card_0 = cardS
		end
	end

	local jinks = {}
	for _, cd in ipairs(handcards) do
		if cd:isKindOf("Jink") then
			table.insert(jinks, cd)
		end
	end
	if (not card_0) and #jinks > 0 then
		if self.player:getMark("luachongsheng") < 1 and self.player:getHp() > 2 then
			card_0 = jinks[1]
		end
		if #jinks > 1 then
			card_0 = jinks[1]
		end
	end

	local Nullification = {}
	for _, cd in ipairs(handcards) do
		if cd:isKindOf("Nullification") then
			table.insert(Nullification, cd)
		end
	end
	if (not card_0) and #Nullification > 0 then
		if #Nullification > 1 then
			card_0 = Nullification[1]
		end
	end

	if card_0 then
		local ir = sgs.Sanguosha:cloneCard("iron_chain", card_0:getSuit(), card_0:getNumber())
		ir:addSubcard(card_0)
		local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
		self:useTrickCard(ir, dummy_use)
		if dummy_use.card and dummy_use.to and (dummy_use.to:length() > 0) then
			for _, p in sgs.qlist(dummy_use.to) do
				if p:objectName() ~= self.player:objectName() then
					use.card = sgs.Card_Parse("#luahonghun:".. card_0:getId() ..":")
					if use.to then use.to:append(p) end
					return
				end
			end
		end
	end
end

sgs.ai_use_priority.luahonghun = 7.5

sgs.ai_skill_use["@@luashuangfeng"] = function(self, prompt)
	local target = self.room:getTag("luashuangfeng"):toPlayer()
	if self:isFriend(target) then return "." end
	local slash1 = sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_NoSuit, 0)
	local slash2 = sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_NoSuit, 0)
	if (not self:slashIsEffective(slash1, target, self.player) and not self:slashIsEffective(slash2, target, self.player))
			or (self:slashProhibit(slash1, target) and self:slashProhibit(slash2, target)) or (target:hasSkill("anxian") and not target:isKongcheng()) then
		return "."
	end
	self.room:writeToConsole("luashuangfeng ai test")
	if self:needToThrowArmor() then
		return "#luashuangfeng:" .. self.player:getArmor():getId().. ":"
	end
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByUseValue(cards)
	for _, card in sgs.list(self.player:getHandcards()) do
		local dummy_use = { isDummy = true }
		local type = card:getTypeId()
		self["use" .. sgs.ai_type_name[type + 1] .. "Card"](self, card, dummy_use)
		if not dummy_use.card then
			if not (card:isKindOf("Jink") and self.player:getHp() < 3) and not (card:isKindOf("Analeptic") and self.player:getHp() < 3)
				and not card:isKindOf("Peach") and not card:isKindOf("ExNihilo") and not card:isKindOf("Weapon") then
				return "#luashuangfeng:" .. card:getEffectiveId() .. ":"
			end
		end
	end
	if self.player:getDefensiveHorse() and not self:isWeak() then
		return "#luashuangfeng:" .. self.player:getDefensiveHorse():getId().. ":"
	end
	return "."
end

local luachixiao_skill = {}
luachixiao_skill.name = "luachixiao"
table.insert(sgs.ai_skills, luachixiao_skill)
luachixiao_skill.getTurnUseCard = function(self)
	if self.player:getMark("@chixiao") == 0 then return end

	return sgs.Card_Parse("#luachixiao:.:")
end

sgs.ai_skill_use_func["#luachixiao"] = function(card, use, self)
	local slash2 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
	local function anotherD(target, b)
		local players = sgs.QList2Table(self.room:getAllPlayers())
		local x = target:getSeat() - b:getSeat()
		local y = b:getSeat() - target:getSeat()
		local distance = math.min(x % #players, y % #players)
		if b:getDefensiveHorse() or b:hasSkill("feiying") then distance = distance + 1 end
		self.room:writeToConsole("chixiao distance" .. target:getGeneralName() .. " to ".. b:getGeneralName() .. " is " .. tostring(distance))
		return distance
	end

	local function slash_shouyi(target) -- 陈站在target位置后，能杀到几个人
		local shouyi2 = 0
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if anotherD(target, p) <= self.player:getAttackRange() and self:slashIsEffective(slash2, p, self.player)
				and not self:slashProhibit(slash2, p) and self:isEnemy(p) then
				shouyi2 = shouyi2 + 1
				if self:YouMu2(p, true) then
					shouyi2 = shouyi2 + 1
				end
			end
		end
		return shouyi2
	end
	if #self.enemies > 2 then
		for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
			local shouyi = 0
			if p:getSeat() > self.player:getSeat() then
				shouyi = (self:getEnemyNumBySeat(self.player, p, self.player) - self:getFriendNumBySeat(self.player, p) + 1) * 3
				if self:isEnemy(p) then shouyi = shouyi + 3 end
				if self:isFriend(p) then shouyi = shouyi - 3 end
			else
				shouyi = (self:getFriendNumBySeat(p, self.player) - self:getEnemyNumBySeat(p, self.player, self.player)) * 3
			end
			if #self.enemies == self.room:getOtherPlayers(self.player):length() then shouyi = 0 end
			self.room:writeToConsole("luachixiao ai test " .. tostring(shouyi))
			if shouyi > 5 then
				if slash_shouyi(p) >= #self.enemies - 2 and self.player:getHandcardNum() > 2 then
					use.card = sgs.Card_Parse("#luachixiao:.:")
					if use.to then use.to:append(p) end
					return
				end
			end
		end
	elseif #self.enemies > 1 and self.room:getAlivePlayers():length() < 5 then
		for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
			-- 基准点法
			local shouyi = 0
			if p:getSeat() > self.player:getSeat() then
				shouyi = (self:getEnemyNumBySeat(self.player, p, self.player) - self:getFriendNumBySeat(self.player, p) + 1) * 3
				if self:isEnemy(p) then shouyi = shouyi + 3 end
				if self:isFriend(p) then shouyi = shouyi - 3 end
			else
				shouyi = (self:getFriendNumBySeat(p, self.player) - self:getEnemyNumBySeat(p, self.player, self.player)) * 3
			end
			if #self.enemies == self.room:getOtherPlayers(self.player):length() then shouyi = 0 end
			self.room:writeToConsole("luachixiao ai test " .. tostring(shouyi))
			if shouyi > 2 or (#self.enemies == self.room:getOtherPlayers(self.player):length()) then
				if (slash_shouyi(p) >= 2 or (shouyi > 5 and slash_shouyi(p) >= 1)) and self.player:getHandcardNum() > 1 then
					use.card = sgs.Card_Parse("#luachixiao:.:")
					if use.to then use.to:append(p) end
					return
				end
			end
		end
	elseif self.room:getAlivePlayers():length() < 4 then
		if self:slashIsEffective(slash2, self.enemies[1], self.player) and self.player:inMyAttackRange(self.enemies[1])
			and not self:slashProhibit(slash2, self.enemies[1]) and not self.player:isKongcheng() then
			use.card = sgs.Card_Parse("#luachixiao:.:")
			if use.to then use.to:append(self.enemies[1]) end
			return
		end
	end
end

sgs.ai_use_priority.luachixiao = 10

sgs.ai_skill_invoke.luachixiao = sgs.ai_skill_invoke.liegong

sgs.ai_skill_playerchosen.luashaojie = function(self, targets, slashX)
	local targetlist = sgs.QList2Table(targets)
	for _, target in ipairs(targetlist) do	--杀敌
		if self:isFriend(target) and target:objectName() ~= self.player:objectName() then return target end
	end
	return self.player
end
sgs.ai_card_intention.luashaojie = -80 --pay
sgs.ai_skill_invoke.lualangshi = function(self, data)
	return self.player:getHp() <= 3 or (self.player:getHandcardNum() > 5 and #self.enemies > 1 and self.player:getHp() == 4)
end


