--悍勇
sgs.ai_skill_invoke.hanyong = function(self, data)
	local use = data:toCardUse()
	local earnings = 0
	local need
	if use.card:isKindOf("SavageAssault") then need = "Slash"
	elseif use.card:isKindOf("ArcheryAttack") then need = "Jink" end
	for _, enemy in ipairs(self.enemies) do
		if not enemy:has<PERSON>rm<PERSON><PERSON>ffect("Vine") and self:damageIsEffective(enemy, nil, self.player) and getCardsNum(need, enemy, self.player) == 0 then
			earnings = earnings + 1
			if self:isWeak(enemy) then
				earnings = earnings + 1
			end
			if self:hasEightDiagramEffect(enemy) and need == "Jink" then
				earnings = earnings - 1
			end
		end
	end
	for _, friend in ipairs(self.friends_noself) do
		if not friend:hasArmorEffect("Vine") and self:damageIsEffective(friend, nil, self.player) and getCardsNum(need, friend, self.player) == 0 then
			earnings = earnings - 1
			if self:isWeak(friend) then
				earnings = earnings - 1
			end
			if self:hasEightDiagramEffect(friend) and need == "Jink" then
				earnings = earnings + 1
			end
		else
			earnings = earnings + 1
		end
	end
	if earnings >= 0 then return true end
	return false
end
--邀名
sgs.ai_skill_playerchosen.yaoming = function(self, targets)
	self:updatePlayers()
	local target = nil
	self:sort(self.friends_noself, "handcard")
	for _, friend in ipairs(self.friends_noself) do
		if friend:getHandcardNum() < self.player:getHandcardNum()then
			if friend:hasSkills(sgs.cardneed_skill) and not self:needKongcheng(friend) and not friend:hasSkill("manjuan") then
				target = friend
				break
			end
		elseif friend:getHandcardNum() > self.player:getHandcardNum()then
			if self:doNotDiscard(friend) then
				target = friend
				break
			end
			if self:needKongcheng(friend) and friend:getHandcardNum() == 1 then
				target = friend
				break
			end
		end
	end
	if target == nil then
		self:sort(self.friends_noself, "chaofeng")
		for _, friend in ipairs(self.friends_noself) do
			if friend:getHandcardNum() < self.player:getHandcardNum()and not friend:hasSkill("manjuan") then
				target = friend
				break
			end
		end
	end
	if target == nil then
		self:sort(self.enemies, "handcard")
		local players = sgs.SPlayerList()
		for _, enemy in ipairs(self.enemies) do
			if enemy:getHandcardNum() > self.player:getHandcardNum()then
				players:append(enemy)
			end
		end
		target = self:findPlayerToDiscard("h", false, true, players, false)
	end
	return target
end
sgs.ai_playerchosen_intention.yaoming = function(self, from, to)
	if hasManjuanEffect(to) then sgs.updateIntention(from, to, 50) end
	if to:getHandcardNum() < from:getHandcardNum()then
		sgs.updateIntention(from, to, -50)
	else
		if self:doNotDiscard(to) or self:needKongcheng(to)then
			sgs.updateIntention(from, to, -20)
		else
			sgs.updateIntention(from, to, 20)
		end
	end
end
--殉志
sgs.ai_skill_invoke.ol_xunzhi = function(self)
	if self.player:getHp() == 2 and self.player:hasSkill("chanyuan") then return false end
	if not self:getCard("Peach") and self:isWeak() then return false end
	local diaochan = self.room:findPlayerBySkillName("lihun")
	if diaochan and self:isEnemy(diaochan) and self:willSkipPlayPhase()then return false end
	local num = 0
	local lose = 1
	if self.player:hasSkill("yawang") then
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if p:getHp() == self.player:getHp() then
				num = num +1
			elseif p:getHp() == self.player:getHp()-1 then
				lose = lose +1
			end
		end
		if lose < num then return false end
		return self.player:getHp() > 1
	end
	return self:needToLoseHp()
end
--倾袭
sgs.ai_skill_invoke.qingxi = function(self, data)
	local damage = data:toDamage()
	local target = damage.to
	local weapon = self.player:getWeapon()
	local X = weapon:getRealCard():toWeapon():getRange()
	if weapon:isKindOf("Crossbow") or weapon:isKindOf("Blade") then
		if not self:hasCrossbowEffect() and self:getCardsNum("Slash") > 1 then return false end
	end
	if weapon:isKindOf("Axe") and self:hasHeavySlashDamage(self.player) then return false end
	if target:getCards("he"):length() < X then return self:isEnemy(target) end
	if X == 1 then
		if self:doNotDiscard(target)or self:hasSkills(sgs.lose_equip_skill, target) or self:needToThrowArmor(target) then return self:isFriend(target) end
		if self:needKongcheng(target) and target:getHandcardNum() == 1 then return self:isFriend(target) end
	else
		if X == 2 and self:needToThrowArmor(target) then return self:isFriend(target) end
		local hasweapon = false
		for _, card in sgs.qlist(self.player:getCards("h")) do
			if card:isKindOf("Weapon") then
				hasweapon = true
			end
		end
		if target:getCards("he"):length() == X and not self:needKongcheng(target) then return self:isEnemy(target) and hasweapon end
		if X > 2 and hasweapon then return self:isEnemy(target) end
	end
	return false
end
sgs.ai_skill_discard.qingxi = function(self, discard_num, min_num, optional, include_equip)
	local user = nil
	for _,p in sgs.qlist(self.room:findPlayersBySkillName("qingxi")) do
		if not p:getWeapon() then continue end
		if p:getWeapon():getRealCard():toWeapon():getRange() == discard_num then
			user = p
			break
		end
	end
	if discard_num > 3 and not self:isWeak() then return {} end
	if user and self:needToLoseHp(self.player, user) then return {} end
	if user and (self.player:hasArmorEffect("silver_lion") and not IgnoreArmor(user, self.player)) and not self.player:isWounded() then return {} end
	local to_discard = {}
	
	local cards = sgs.QList2Table(self.player:getCards("he"))
	local index = 0
	local all_peaches = 0
	for _, card in ipairs(cards) do
		if isCard("Peach", card, self.player) then
			all_peaches = all_peaches + 1
		end
	end
	if all_peaches >= 2 and self:getOverflow() <= 0 then return {} end
	self:sortByKeepValue(cards)
	cards = sgs.reverse(cards)

	for i = #cards, 1, -1 do
		local card = cards[i]
		if not isCard("Peach", card, self.player) and not self.player:isJilei(card) then
			table.insert(to_discard, card:getEffectiveId())
			table.remove(cards, i)
			index = index + 1
			if index == discard_num then break end
		end
	end
	if #to_discard < discard_num then return {}
	else
		return to_discard
	end
end
--明鉴
local ol_mingjian_skill = {}
ol_mingjian_skill.name = "ol_mingjian"
table.insert(sgs.ai_skills, ol_mingjian_skill)
ol_mingjian_skill.getTurnUseCard = function(self)
	if not self.player:isKongcheng() and not self.player:hasUsed("#ol_mingjian") then
        return sgs.Card_Parse("#ol_mingjian:.:")
    end
end

sgs.ai_skill_use_func["#ol_mingjian"] = function(card,use,self)
	self:sort(self.friends_noself, "handcard")

	for _, friend in ipairs(self.friends_noself) do
		if not friend:hasSkill("manjuan") and not self:needKongcheng(friend) then
			if friend:hasSkills(sgs.cardneed_skill) then
				use.card = sgs.Card_Parse("#ol_mingjian:.:")
				if use.to then use.to:append(friend) end
				return
			end
			if getCardsNum("Slash", friend, self.player)+self:getCardsNum("Slash")>1 then
				use.card = sgs.Card_Parse("#ol_mingjian:.:")
				if use.to then use.to:append(friend) end
				return
			end
		end
	end
end

sgs.ai_use_priority["ol_mingjian"] = 0.6
sgs.ai_use_value["ol_mingjian"] = 2.45
sgs.ai_card_intention.ol_mingjian = -80
--避乱
getKingdoms = function(player)
	local kingdoms = {}
	local room = player:getRoom()
	for _, p in sgs.qlist(room:getAlivePlayers()) do
		local flag = true
		for _, k in ipairs(kingdoms) do
			if p:getKingdom() == k then
				flag = false
				break
			end
		end
		if flag then table.insert(kingdoms, p:getKingdom()) end
	end
	return #kingdoms
end
sgs.ai_skill_invoke.ol_biluan = function(self)
	local others = sgs.SPlayerList()
	local n = self.room:getOtherPlayers(self.player):length()
	for i=1,n do 
		others:append(self.player:getNextAlive(i))
	end
	local range_list = sgs.IntList()
	for _, p in sgs.qlist(others) do
		local distance = p:distanceTo(self.player)-p:getAttackRange()+getKingdoms(self.player)
		if distance < 0 then distance = 0 end
		range_list:append(distance)
	end
	local invoke = 0
	local earnings = 0
	for i=0,n-1 do 
		local to = others:at(i)
		if self:isEnemy(to) and range_list:at(i)-invoke > 0 and getCardsNum("Slash", to) > 0 then
			earnings = earnings + 1
		end
		if range_list:at(i)-invoke > 0 then
			if self.player:hasSkill("ol_lixia") then
				invoke = invoke + 1
				earnings = earnings +1
			end
		end
	end
	earnings = earnings - self:ImitateResult_DrawNCards(self.player, self.player:getVisibleSkillList(true))
	if earnings >= 0 then return true end
	return false
end
--礼下
sgs.ai_skill_choice["ol_lixia"] = function(self, choices, data)
	local current = self.room:getCurrent()
	local items = choices:split("+")
    if #items == 1 then
        return items[1]
	else
		if self:isFriend(current) and (current:hasSkills(sgs.cardneed_skill) or self:isWeak(current)) then 
			return "lixia2"
		end
    end
    return "lixia1"
end
--仁德
local function OlRendeArrange(self, cards, friends, enemies, unknowns, arrange)
    if #enemies > 0 then
        self:sort(enemies, "hp")
        for _,card in ipairs(cards) do
            if card:isKindOf("Shit") or card:isKindOf("Hui") then
                return enemies[1], card, "enemy"
            end
        end
    end
    if #friends > 0 then
        self:sort(friends, "defense")
        for _,friend in ipairs(friends) do
            local arranged = arrange[friend:objectName()] or {}
            if self:isWeak(friend) and friend:getHandcardNum() + #arranged < 3 then
                for _,card in ipairs(cards) do
                    if card:isKindOf("Shit") or card:isKindOf("Hui") then
                    elseif isCard("Peach", card, friend) or isCard("Analeptic", card, friend) then
                        return friend, card, "friend"
                    elseif isCard("Jink", card, friend) and self:getEnemyNumBySeat(self.player, friend) > 0 then
                        return friend, card, "friend"
                    end
                end
            end
        end
        for _,friend in ipairs(friends) do
            local arranged = arrange[friend:objectName()] or {}
            if friend:getHp() <= 2 and friend:faceUp() then
                for _,card in ipairs(cards) do
                    if card:isKindOf("Armor") then
                        if not friend:getArmor() and not self:hasSkills("yizhong|bazhen|bossmanjia", friend) then
                            local given = false
                            for _,c in ipairs(arranged) do
                                if c:isKindOf("Armor") then
                                    given = true
                                    break
                                end
                            end
                            if not given then
                                return friend, card, "friend"
                            end
                        end
                    elseif card:isKindOf("DefensiveHorse") then
                        if not friend:getDefensiveHorse() then
                            local given = false
                            for _,c in ipairs(arranged) do
                                if c:isKindOf("DefensiveHorse") then
                                    given = true
                                    break
                                end
                            end
                            if not given then
                                return friend, card, "friend"
                            end
                        end
                    end
                end
            end
        end
        for _,friend in ipairs(friends) do
            local arranged = arrange[friend:objectName()] or {}
            if friend:getHandcardNum() + #arranged < 4 then
                if friend:hasSkill("jijiu") then
                    for _,card in ipairs(cards) do
                        if card:isRed() then
                            return friend, card, "friend"
                        end
                    end
                end
                if friend:hasSkill("jieyin") then
                    return friend, cards[1], "friend"
                elseif friend:hasSkill("nosrenxin") and friend:isKongcheng() then
                    return friend, cards[1], "friend"
                end
            end
        end
        for _,friend in ipairs(friends) do
            if self:hasSkills("wusheng|longdan|wushen|keji|chixin", friend) then
                local arranged = arrange[friend:objectName()] or {}
                if friend:getHandcardNum() + #arranged >= 2 and not self:hasCrossbowEffect(friend) then
                    for _,card in ipairs(cards) do
                        if card:isKindOf("Crossbow") then
                            local given = false
                            for _,c in ipairs(arranged) do
                                if c:isKindOf("Crossbow") then
                                    given = true
                                    break
                                end
                            end
                            if not given then
                                return friend, card, "friend"
                            end
                        end
                    end
                end
            end
        end
        for _,friend in ipairs(friends) do
            local arranged = arrange[friend:objectName()] or {}
            local has_crossbow = self:hasCrossbowEffect(friend)
            if not has_crossbow then
                for _,c in ipairs(arranged) do
                    if c:isKindOf("Crossbow") then
                        has_crossbow = true
                        break
                    end
                end
            end
            if has_crossbow or getKnownCard(friend, self.player, "Crossbow") > 0 then
                for _, p in ipairs(self.enemies) do
                    if sgs.isGoodTarget(p, self.enemies, self) and friend:distanceTo(p) <= 1 then
                        for _,card in ipairs(cards) do
                            if isCard("Slash", card, friend) then
                                return friend, card, "friend"
                            end
                        end
                    end
                end
            end
        end
        local compareByAction = function(a, b)
            return self.room:getFront(a, b):objectName() == a:objectName()
        end
        table.sort(friends, compareByAction)
        for _,friend in ipairs(friends) do
            local flag = string.format("weapon_done_%s_%s", self.player:objectName(), friend:objectName())
            if friend:faceUp() and not friend:hasFlag(flag) then
                local can_slash = false
                local others = self.room:getOtherPlayers(friend)
                for _,p in sgs.qlist(others) do
                    if self:isEnemy(p) and sgs.isGoodTarget(p, self.enemies, self) then
                        if friend:distanceTo(p) <= friend:getAttackRange() then
                            can_slash = true
                            break
                        end
                    end
                end
                if not can_slash then
                    for _,p in sgs.qlist(others) do
                        if self:isEnemy(p) and sgs.isGoodTarget(p, self.enemies, self) then
                            local distance = friend:distanceTo(p)
                            local range = friend:getAttackRange()
                            if distance > range then
                                for _,card in ipairs(cards) do
                                    if card:isKindOf("Weapon") then
                                        if not friend:getWeapon() then
                                            if distance <= range + (sgs.weapon_range[card:getClassName()] or 0) then
                                                self.room:setPlayerFlag(friend, flag)
                                                return friend, card, "friend"
                                            end
                                        end
                                    elseif card:isKindOf("OffensiveHorse") then
                                        if not friend:getOffensiveHorse() then
                                            if distance <= range + 1 then
                                                self.room:setPlayerFlag(friend, flag)
                                                return friend, card, "friend"
                                            end
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
            end
        end
        local compareByNumber = function(a, b)
            return a:getNumber() > b:getNumber()
        end
        table.sort(cards, compareByNumber)
        for _,friend in ipairs(friends) do
            if friend:faceUp() then
                local skills = friend:getVisibleSkillList(true)
                for _,skill in sgs.qlist(skills) do
                    local callback = sgs.ai_cardneed[skill:objectName()]
                    if type(callback) == "function" then
                        for _,card in ipairs(cards) do
                            if callback(friend, card, self) then
                                return friend, card, "friend"
                            end
                        end
                    end
                end
            end
        end
        for _,card in ipairs(cards) do
            if card:isKindOf("Shit") or card:isKindOf("Hui") then
                for _,friend in ipairs(friends) do
                    if self:isWeak(friend) then
                    elseif friend:hasSkill("jueqing") or card:getSuit() == sgs.Card_Spade then
                        if friend:hasSkill("zhaxiang") then
                            return friend, card, "friend"
                        end
                    elseif self:hasSkills("guixin|jieming|yiji|nosyiji|chengxiang|noschengxiang|jianxiong", friend) then
                        return friend, card, "friend"
                    end
                end
            end
        end
        if self.role == "lord" and self.player:hasLordSkill("jijiang") then
            for _,friend in ipairs(friends) do
                local arranged = arrange[friend:objectName()] or {}
                if friend:getKingdom() == "shu" and friend:getHandcardNum() + #arranged < 3 then
                    for _,card in ipairs(cards) do
                        if isCard("Slash", card, friend) then
                            return friend, card, "friend"
                        end
                    end
                end
            end
        end
    end
    if #enemies > 0 then
        self:sort(enemies, "defense")
        for _,enemy in ipairs(enemies) do
            if enemy:hasSkill("kongcheng") and enemy:isKongcheng() then
                if not enemy:hasSkill("manjuan") then
                    for _,card in ipairs(cards) do
                        if isCard("Jink", card, enemy) then
                        elseif card:isKindOf("Disaster") or card:isKindOf("Shit") or card:isKindOf("Hui") then
                            return enemy, card, "enemy"
                        elseif card:isKindOf("Collateral") or card:isKindOf("AmazingGrace") then
                            return enemy, card, "enemy"
                        elseif card:isKindOf("OffensiveHorse") or card:isKindOf("Weapon") then
                            return enemy, card, "enemy"
                        end
                    end
                end
            end
        end
    end
    local overflow = self:getOverflow()
    if #friends > 0 then
        for _,friend in ipairs(friends) do
            local arranged = arrange[friend:objectName()] or {}
            if self:willSkipPlayPhase(friend) then
            elseif self:hasSkills(sgs.priority_skill, friend) and friend:getHandcardNum() + #arranged <= 3 then
                if overflow - #arranged > 0 or self.player:getHandcardNum() - #arranged > 3 then
                    return friend, cards[1], "friend"
                end
            end
        end
    end
    if overflow > 0 and #friends > 0 then
        for _,card in ipairs(cards) do
            local dummy_use = {
                isDummy = true,
            }
            if card:isKindOf("BasicCard") then
                self:useBasicCard(card, dummy_use)
            elseif card:isKindOf("EquipCard") then
                self:useEquipCard(card, dummy_use)
            elseif card:isKindOf("TrickCard") then
                self:useTrickCard(card, dummy_use)
            end
            if not dummy_use.card then
                self:sort(friends, "defense")
                return friends[1], card, "friend"
            end
        end
    end
    if arrange["count"] < 2 and self.player:getLostHp() > 0 and self.player:getHandcardNum() >= 2 and self:isWeak() then
        if #friends > 0 then
            return friends[1], cards[1], "friend"
        elseif #unknowns > 0 then
            self:sortByKeepValue(cards)
            for _,p in ipairs(unknowns) do
                if p:hasSkill("manjuan") then
                    return p, cards[1], "unknown"
                end
            end
            self:sort(unknowns, "threat")
            return unknowns[#unknowns], cards[1], "unknown"
        elseif #enemies > 0 then
            for _,enemy in ipairs(enemies) do
                if enemy:hasSkill("manjuan") then
                    return enemy, cards[1], "enemy"
                end
            end
        end
    end
end
local function resetPlayers(players, except)
    local result = {}
    for _,p in ipairs(players) do
        if not p:objectName() == except:objectName() then
            table.insert(result, p)
        end
    end
    return result
end
local rende_skill = {
    name = "ol_rende",
    getTurnUseCard = function(self, inclusive)
		if not self.player:isKongcheng() then
            return sgs.Card_Parse("#ol_rende:.:")
        end
    end,
}
table.insert(sgs.ai_skills, rende_skill)
sgs.ai_skill_use_func["#ol_rende"] = function(card, use, self)
    local others = self.room:getOtherPlayers(self.player)
    local friends, enemies, unknowns = {}, {}, {}
    local arrange = {}
    arrange["count"] = 0
    for _,p in sgs.qlist(others) do
        if p:getMark("ol_rende") == 0 then
            arrange[p:objectName()] = {}
            if self:isFriend(p) then
                table.insert(friends, p)
            elseif self:isEnemy(p) then
                table.insert(enemies, p)
            else
                table.insert(unknowns, p)
            end
        end
    end
    local new_friends = {}
    for _,friend in ipairs(friends) do
        local exclude = false
        if self:needKongcheng(friend, true) or self:willSkipPlayPhase(friend) then
            exclude = true
            if self:hasSkills("keji|jiewei|shensu", friend) then
                exclude = false
            elseif friend:getHp() - friend:getHandcardNum() >= 3 then
                exclude = false
            elseif friend:isLord() and self:isWeak(friend) and self:getEnemyNumBySeat(self.player, friend) >= 1 then
                exclude = false
            end
        end
        if not exclude and not hasManjuanEffect(friend) and self:objectiveLevel(friend) <= -2 then
            table.insert(new_friends, friend)
        end
    end
    friends = new_friends
    local overflow = self:getOverflow()
    if overflow <= 0 and #friends == 0 then
        return 
    end
    local handcards = self.player:getHandcards()
    handcards = sgs.QList2Table(handcards)
    self:sortByUseValue(handcards)
    while true do
        if #handcards == 0 then
            break
        end
        local target, to_give, group = OlRendeArrange(self, handcards, friends, enemies, unknowns, arrange)
        if target and to_give and group then
            table.insert(arrange[target:objectName()], to_give)
            arrange["count"] = arrange["count"] + 1
            handcards = self:resetCards(handcards, to_give)
        else
            break
        end
    end
    local max_count, max_name = 0, nil
    for name, cards in pairs(arrange) do
        if type(cards) == "table" then
            local count = #cards
            if count > max_count then
                max_count = count
                max_name = name
            end
        end
    end
    if max_count == 0 or not max_name then
        return 
    end
    local max_target = nil
    for _,p in sgs.qlist(others) do
        if p:objectName() == max_name then
            max_target = p
            break
        end
    end
    if max_target and type(arrange[max_name]) == "table" and #arrange[max_name] > 0 then
        local to_use = {}
        for _,c in ipairs(arrange[max_name]) do
            table.insert(to_use, c:getEffectiveId())
        end
        local card_str = string.format("#ol_rende:%s:", table.concat(to_use, "+"))
        local acard = sgs.Card_Parse(card_str)
        assert(acard)
        use.card = acard
        if use.to then
            use.to:append(max_target)
        end
    end
end
sgs.ai_skill_choice["ol_rende"] = function(self, choices)
	local items = choices:split("+")
    if #items == 1 then
        return items[1]
	else
		if self:isWeak() and self.player:isWounded() and table.contains(items, "peach")then return "peach" end
		local slash = sgs.Sanguosha:cloneCard("slash")
		if self:getCardsNum("Slash") > 1 and not slash:isAvailable(self.player) and table.contains(items, "analeptic") then
			for _, enemy in ipairs(self.enemies) do
				if ((enemy:getHp() < 3 and enemy:getHandcardNum() < 3) or (enemy:getHandcardNum() < 2)) and self.player:canSlash(enemy) and not self:slashProhibit(slash, enemy, self.player)
					and self:slashIsEffective(slash, enemy, self.player) and sgs.isGoodTarget(enemy, self.enemies, self, true) then
					return "analeptic"
				end
			end
		end
		for _, enemy in ipairs(self.enemies) do
			if self.player:canSlash(enemy) and sgs.isGoodTarget(enemy, self.enemies, self, true) then
				local thunder_slash = sgs.Sanguosha:cloneCard("thunder_slash")
				local fire_slash = sgs.Sanguosha:cloneCard("fire_slash")
				if table.contains(items, "fire_slash")and not self:slashProhibit(fire_slash, enemy, self.player)and self:slashIsEffective(fire_slash, enemy, self.player)then
					return "fire_slash"
				end
				if table.contains(items, "thunder_slash")and not self:slashProhibit(thunder_slash, enemy, self.player)and self:slashIsEffective(thunder_slash, enemy, self.player)then
					return "thunder_slash"
				end
				if table.contains(items, "slash")and not self:slashProhibit(slash, enemy, self.player)and self:slashIsEffective(slash, enemy, self.player)then
					return "slash"
				end
			end
		end
		if self.player:isWounded() and table.contains(items, "peach")then return "peach" end
    end
    return "cancel"
end
sgs.ai_skill_use["@@ol_rende"]=function(self,prompt)
	self:updatePlayers()
	self:sort(self.enemies,"defense")
	local class_name = self.player:property("ol_rende"):toString()
	local use_card = sgs.Sanguosha:cloneCard(class_name, sgs.Card_NoSuit, 0)
	use_card:setSkillName("ol_rende")
	if (use_card:targetFixed()) then
		return use_card:toString()
	else
		if string.find(class_name, "slash")then
			for _,enemy in ipairs(self.enemies) do
				if self.player:canSlash(enemy, use_card, false) and not self:slashProhibit(nil, enemy) and self.player:inMyAttackRange(enemy)
				and sgs.getDefenseSlash(enemy, self)< 6 and self:slashIsEffective(use_card, enemy) and sgs.isGoodTarget(enemy, self.enemies, self)then
					return use_card:toString() .. "->" .. enemy:objectName()
				end
			end
		end
	end
end
sgs.ai_use_value["ol_rende"] = sgs.ai_use_value.RendeCard
sgs.ai_use_priority["ol_rende"] = sgs.ai_use_priority.RendeCard
sgs.ai_card_intention["ol_rende"] = sgs.ai_card_intention.RendeCard
sgs.dynamic_value.benefit["ol_rende"] = true
--凤魄
sgs.ai_skill_invoke.ol_fengpo = function(self, data)
	local target = data:toPlayer()
	self.room:setTag("ol_fengpo",sgs.QVariant(target:objectName()))
	return true
end
sgs.ai_skill_choice["ol_fengpo"] = function(self, choices)
	local items = choices:split("+")
    local name = self.room:getTag("ol_fengpo"):toString()
	
	if name == "" then return items[1] end
	local target = nil
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:objectName() == name then
			target = p
			break
		end
	end
	if target == nil then return items[1] end
	if #items == 1 then
        return items[1]
	else
		if self:isEnemy(target) and getCardsNum("Jink", target, self.player) == 0 then
			return "fengpo2"
		else
			return "fengpo1"
		end
    end
    return items[1]
end

--缮甲
sgs.ai_skill_invoke.shanjia = function(self, data)
	return true
end
sgs.ai_skill_use["@@shanjia!"] = function(self, prompt)
	self:updatePlayers()
	self:sort(self.enemies, "defenseSlash")
	
	local selfDef = sgs.getDefense(self.player)
	local cards = self.player:getCards("he")
	local to_discard = {}
	cards = sgs.QList2Table(cards)
	self:sortByKeepValue(cards)
	
	local best_target, target
	for _, enemy in ipairs(self.enemies) do
		local slash = sgs.Sanguosha:cloneCard("slash")
		if self.player:canSlash(enemy, slash, false) and not self:slashProhibit(nil, enemy) and self.player:inMyAttackRange(enemy)
		and self:slashIsEffective(slash, enemy) and sgs.isGoodTarget(enemy, self.enemies, self)then
			if enemy:getHp() == 1 and getCardsNum("Jink", enemy) == 0 then best_target = enemy break end
			if sgs.getDefense(enemy) < 6 then best_target = enemy break end
		end
	end
	for _, enemy in ipairs(self.enemies) do
			local slash = sgs.Sanguosha:cloneCard("slash")
		if self.player:canSlash(enemy, slash, false) and not self:slashProhibit(nil, enemy) and self.player:inMyAttackRange(enemy)
		and self:slashIsEffective(slash, enemy) and sgs.isGoodTarget(enemy, self.enemies, self)then
				target = enemy
		end
	end
	local eCard = nil
	if self:needToThrowArmor() and not self.player:isCardLimited(self.player:getArmor(), method) then
		eCard = self.player:getArmor()
	end
	if best_target or target then
		if not eCard then
			for _, card in sgs.qlist(self.player:getCards("e")) do
				if card:isKindOf("EquipCard") then
					if card:isKindOf("Weapon") then
						if card:isKindOf("Crossbow") or card:isKindOf("Blade") then
							if not self:hasCrossbowEffect() and self:getCardsNum("Slash") > 1 then continue end
						end
						if card:isKindOf("Axe") and self:hasHeavySlashDamage(self.player) then continue end
						if card:isKindOf("GudingBlade") then continue end
						if best_target and self.player:distanceTo(best_target) > 1 then continue end
						if target and self.player:distanceTo(target) > 1 then continue end
					elseif card:isKindOf("Armor") then
						if not self:hasSkills("bazhen|yizhong|linglong") and self:isWeak(self.player) then
							if not (self:needToThrowArmor() and self.player:hasEquip(card)) then continue end
						end
					elseif card:isKindOf("DefensiveHorse") then
						if self:isWeak(self.player) then continue end
					elseif card:isKindOf("OffensiveHorse") then
						continue
					end
					if not eCard then eCard = card break end
				end
			end
		end
	end
	if eCard then
		local needcard_num = math.min(self.player:getMark("@shanjia"), 7) - 1
		for _, card in ipairs(cards) do
			if not card:isKindOf("Peach") and card:getEffectiveId() ~= eCard:getEffectiveId() then
				if #to_discard == needcard_num then break end
				table.insert(to_discard, card:getEffectiveId())
			end
		end
		if #to_discard == needcard_num then
			table.insert(to_discard, eCard:getEffectiveId())
			if best_target then
				return "#shanjia:"..table.concat(to_discard, "+")..":->"..best_target:objectName()
			end
			if target then
				return "#shanjia:"..table.concat(to_discard, "+")..":->"..target:objectName()
			end
			return "#shanjia:"..table.concat(to_discard, "+")..":"
		end
	else
		local needcard_num = math.min(self.player:getMark("@shanjia"), 7)
		for _, card in ipairs(cards) do
			if not card:isKindOf("Peach") then
				if #to_discard == needcard_num then break end
				table.insert(to_discard, card:getEffectiveId())
			end
		end
		for _, card in ipairs(cards) do
			if table.contains(to_discard, card:getEffectiveId())then continue end
			if #to_discard == needcard_num then break end
			table.insert(to_discard, card:getEffectiveId())
		end
		return "#shanjia:"..table.concat(to_discard, "+")..":"
	end
end
--奇制
sgs.ai_skill_playerchosen.qizhi = function(self, targets)
	self:updatePlayers()
	targets = sgs.QList2Table(targets)
	for _, target in ipairs(targets) do
		if self:isEnemy(target) and target:hasSkill("manjuan") and not target:isNude() then
			return target
		end
	end
	for _, target in ipairs(targets) do
		if self:isFriend(target) and not target:hasSkill("manjuan") 
		and (target:hasSkills(sgs.lose_equip_skill) or self:doNotDiscard(target) or self:needToThrowArmor(target)) then
			return target
		end
	end
	for _, target in ipairs(targets) do
		if self:isEnemy(target) and target:hasEquip()
		and not target:hasSkills(sgs.lose_equip_skill) and not self:doNotDiscard(target) and not target:hasSkills(sgs.cardneed_skill) then
			return target
		end
	end
	if self:PayContains(targets, self.player) or #targets == 0 then return self.player end
	return "."
end
sgs.ai_skill_cardchosen["qizhi"] = function(self, who, flags)
	local card
	local cards = sgs.QList2Table(who:getEquips())
	local handcards = sgs.QList2Table(who:getHandcards())
	if self:isFriend(who) then
		if self:needToThrowArmor(who) then
			return who:getArmor()
		end
		if who:hasSkills(sgs.lose_equip_skill) or self:doNotDiscard(who) then
			for _, card in ipairs(cards) do
				if card:isKindOf("Weapon") then
					if card:isKindOf("Crossbow") or card:isKindOf("Blade") then continue end
					if card:isKindOf("Axe") or card:isKindOf("GudingBlade") then continue end
				elseif card:isKindOf("Armor") then
					if not self:hasSkills("bazhen|yizhong|linglong") and self:isWeak(who) then
						continue
					end
				elseif card:isKindOf("DefensiveHorse") then
					if self:isWeak(who) then continue end
				end
				return card
			end
		end
		if not who:isKongcheng() then return handcards[1] end
		return cards[1]
	else
		if #handcards==1 and handcards[1]:hasFlag("visible") then table.insert(cards,handcards[1]) end
		if not who:hasSkills(sgs.lose_equip_skill) then
			for _, card in ipairs(cards) do
				if card:isKindOf("Weapon") then
					if card:isKindOf("Crossbow") or card:isKindOf("Blade") then return card end
					if card:isKindOf("Axe") or card:isKindOf("GudingBlade") then return card end
				elseif card:isKindOf("Armor") then
					if not self:hasSkills("bazhen|yizhong|linglong") and self:isWeak(who) then
						return card
					end
				elseif card:isKindOf("DefensiveHorse") then
					if self:isWeak(who) then return card end
				end
			end
			if #cards>0 then return cards[1] end
		end
	end
	return nil
end
--进趋
sgs.ai_skill_invoke.jinqu = function(self)
	if self.player:getMark("@qizhi-Clear") >= self.player:getHandcardNum()then return true end
	return false
end
--极奢
local jishe_skill = {}
jishe_skill.name = "jishe"
table.insert(sgs.ai_skills, jishe_skill)
jishe_skill.getTurnUseCard = function(self)
	if self.player:getMaxCards() < 1 then return end
	return sgs.Card_Parse("#jishe:.:")
end

sgs.ai_skill_use_func["#jishe"] = function(card, use, self)
	if self.player:getHandcardNum() < self.player:getMaxCards()then use.card=card end
	if self.player:getHandcardNum() == 1 and self.player:getMaxCards() == 1 then
		self:updatePlayers()
		self:sort(self.enemies,"defense")
		local num = 0
		for _, enemy in ipairs(self.enemies) do
			if self:damageIsEffective(enemy, sgs.DamageStruct_Thunder) and not enemy:isChained() then
				num = num + 1
			end
		end
		if num > 1 and self.player:getHp() > 1 then use.card=card end
	end
end
sgs.ai_skill_use["@@jishe"]=function(self,prompt)
	self:updatePlayers()
	self:sort(self.enemies,"defense")
	local targets = {}
	if self.player:hasSkill("lianhuo") then
		if self.player:getHp() > 1 and not self.player:isChained() then table.insert(targets, self.player:objectName()) end
	end
	for _, enemy in ipairs(self.enemies) do
		if #targets < self.player:getHp() then
			if self:damageIsEffective(enemy, sgs.DamageStruct_Thunder) and not enemy:isChained() then
				table.insert(targets, enemy:objectName())
			end
		else break end	
	end
	return "#jishe_chained:.:->"..table.concat(targets, "+")
end
sgs.ai_use_priority["jishe"] = 1
sgs.ai_use_value["jishe"] = 1
--寝情
sgs.ai_skill_playerchosen.qinqing = function(self, targets)
	self:updatePlayers()
	targets = sgs.QList2Table(targets)
	for _, target in ipairs(targets) do
		if self:isEnemy(target) and target:hasSkill("manjuan") and not target:isNude() then
			return target
		end
	end
	for _, target in ipairs(targets) do
		if self:isFriend(target) and not target:hasSkill("manjuan") 
		and (target:hasSkills(sgs.lose_equip_skill) or self:doNotDiscard(target) or self:needToThrowArmor(target)) then
			return target
		end
	end
	for _, target in ipairs(targets) do
		if self:isEnemy(target) and target:hasEquip()
		and not target:hasSkills(sgs.lose_equip_skill) and not self:doNotDiscard(target) and not target:hasSkills(sgs.cardneed_skill) then
			return target
		end
	end
	local lord = self.room:getLord()
	for _, target in ipairs(targets) do
		if target:getHandcardNum() >= lord:getHandcardNum() then
			if (self:isFriend(target) and not target:hasSkill("manjuan")) 
			or (not self:isFriend(target) and not target:hasSkills(sgs.lose_equip_skill) and not self:doNotDiscard(target) and not target:hasSkills(sgs.cardneed_skill)) then
				return target
			end
		end
	end
	if self:PayContains(targets, self.player) then return self.player end
	return "."
end
--贿生
sgs.ai_skill_discard["huisheng"] = function(self, discard_num, min_num, optional, include_equip)
	local cards = sgs.QList2Table(self.player:getCards("he"))
	local to_discard = {}
	local compare_func = function(a, b)
		return self:getKeepValue(a) < self:getKeepValue(b)
	end
	table.sort(cards, compare_func)
	
	local damage = self.room:getTag("CurrentDamageStruct"):toDamage()
	if self:isFriend(damage.from) then
		for _, card in ipairs(cards) do
			if #to_discard >= discard_num then break end
			table.insert(to_discard, card:getEffectiveId())
		end
		return to_discard
	else
		if damage.damage > 1 then
			for _, card in ipairs(cards) do
				if not card:isKindOf("Peach") then
					if #to_discard >= damage.from:getCards("he"):length() then break end
					table.insert(to_discard, card:getEffectiveId())
				end
			end
			return to_discard
		else
			if #cards > damage.from:getCards("he"):length() or (damage.from:getCards("he"):length()>#cards and #cards > 1) then
				for _, card in ipairs(cards) do
					if not card:isKindOf("Peach") then
						if #to_discard >= damage.from:getCards("he"):length() then break end
						table.insert(to_discard, card:getEffectiveId())
					end
				end
				return to_discard
			else
				for _, card in ipairs(cards) do
					if not card:isKindOf("Peach") then
						table.insert(to_discard, card:getEffectiveId())
					end
				end
			end
		end
	end
	local m = #to_discard
	self.room:setTag("huisheng",sgs.QVariant(m))
	if #to_discard > 1 or self:isWeak() then return to_discard end
	return to_discard
end
sgs.ai_skill_askforag["huisheng"] = function(self, card_ids)
	local to_dis = {}
	local to_obtain = {}
	for card_id in ipairs(card_ids) do
		if self.room:getCardOwner(card_id) == self.player then
			table.insert(to_dis, sgs.Sanguosha:getCard(card_id))
		else
			table.insert(to_obtain, sgs.Sanguosha:getCard(card_id))
		end
	end
	local compare_func = function(a, b)
		return self:getKeepValue(a) < self:getKeepValue(b)
	end
	table.sort(to_dis, compare_func)
	local m = self.room:getTag("huisheng"):toInt()
	local damage = self.room:getTag("CurrentDamageStruct"):toDamage()
	if #to_obtain == 0 then return to_dis[1]:getEffectiveId() end
	self:sortByCardNeed(to_obtain, true)
	if m > #to_dis then return to_obtain[1]:getEffectiveId() end
	if 2*damage.damage >= m+1 then
		if sgs.ai_keep_value[to_obtain[1]:getClassName()] > sgs.ai_keep_value[to_dis[m]:getClassName()] and damage.damage < damage.to:getHp() then return to_obtain[1]:getEffectiveId() end
		return to_dis[1]:getEffectiveId()
	end
	return to_obtain[1]:getEffectiveId()
end
--瑰藻
sgs.ai_skill_choice.guizao = function(self, choices)
	local items = choices:split("+")
    if #items == 1 then
        return items[1]
	else
		if table.contains(items, "guizao2") and self.player:isWounded() and self:isWeak() then 
			return "guizao2"
		end
		if table.contains(items, "guizao1") and self:needBear() then 
			return "guizao1"
		end
		if table.contains(items, "guizao2") and self.player:isWounded() then 
			return "guizao2"
		end
		if table.contains(items, "guizao1") then 
			return "guizao1"
		end
    end
    return items[1]
end
--讥谀
local jiyu_skill = {}
jiyu_skill.name = "jiyu"
table.insert(sgs.ai_skills, jiyu_skill)
jiyu_skill.getTurnUseCard = function(self)
	local invoke = false
	for _, card in sgs.qlist(self.player:getHandcards()) do
		if not self.player:isCardLimited(card, sgs.Card_MethodUse) then
			invoke = true
			break
		end
	end
	if invoke then
		return sgs.Card_Parse("#jiyu:.:")
	end
end
sgs.ai_skill_use_func["#jiyu"] = function(card,use,self)
	self:sort(self.friends_noself, "handcard")
	self:sort(self.enemies, "chaofeng")
	local enemys = {}
	for _, enemy in ipairs(self.enemies) do
		if enemy:getMark("jiyu_Play") == 0 then
			if enemy:isKongcheng() then continue end
			if self:doNotDiscard(enemy) then continue end
			if self:needKongcheng(enemy) and enemy:getHandcardNum() == 1 then continue end
			if self:needToLoseHp(enemy, self.player) and not (self:isWeak(enemy) or enemy:getHp() == 1)then continue end
			table.insert(enemys, enemy)
		end	
	end
	if self.player:getHp() >= 2 and not self.player:faceUp() then 
		local hasspade = false
		for _, c in sgs.qlist(self.player:getHandcards()) do
			if c:getSuit() == sgs.Card_Spade then
				hasspade = true
				break
			end
		end
		if hasspade then 
			use.card = sgs.Card_Parse("#jiyu:.:")
			if use.to then use.to:append(self.player) return end
		end
	end
	
	if #enemys > 0 then
		use.card = sgs.Card_Parse("#jiyu:.:")
		if use.to then use.to:append(enemys[1]) end
	else
		local targets = {}
		local target = nil
		for _, friend in ipairs(self.friends) do
			if friend:isKongcheng() then continue end
			if (not self:needToLoseHp(friend, self.player) and friend:objectName()~=self.player:objectName()) or self:isWeak(friend)or friend:getHp() == 1then continue end
			if friend:getMark("jiyu_Play") == 0 then
				local handcards = sgs.QList2Table(friend:getHandcards())
				local hasspade = false
				local hasother = false
				for _, c in sgs.qlist(cards) do
					local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), friend:objectName())
					if c:hasFlag("visible") or c:hasFlag(flag) then
						if c:getSuit() == sgs.Card_Spade then
							hasspade = true
						else
							hasother = true
						end
						if hasspade and hasother then break end
					end
				end
				if self.player:faceUp() and hasspade then
				elseif not self.player:faceUp() and hasother then
					if (self:needKongcheng(friend) and #handcards==1)or(self:doNotDiscard(friend))then
					else continue end
				else continue end
				table.insert(targets, friend)
			end
			if #targets > 0 then
				use.card = sgs.Card_Parse("#jiyu:.:")
				if use.to then use.to:append(targets[1]) end
			end
		end
	end
end
sgs.ai_skill_cardask["@jiyu"] = function(self, data, pattern, target)
	local target = data:toPlayer()
	local cards = sgs.QList2Table(self.player:getCards("h"))
	self:sortByKeepValue(cards)
	if self:isFriend(target) then
		for _, card in ipairs(cards) do
			if target:faceUp() and self.player:getHp() > 1 then
				if card:getSuit() == sgs.Card_Spade then
					return "$" .. card:getEffectiveId()
				else continue end
			elseif self.player:isCardLimited(card, sgs.Card_MethodUse) then
					return "$" .. card:getEffectiveId()
			else continue end
		end
		for _, card in ipairs(cards) do
			if self.player:isCardLimited(card, sgs.Card_MethodUse) then
				return "$" .. card:getEffectiveId()
			else continue end
			return "$" .. card:getEffectiveId()
		end
		for _, card in ipairs(cards) do
			return "$" .. card:getEffectiveId()
		end
	else
		for _, card in ipairs(cards) do
			if target:faceUp()then
				if card:getSuit() == sgs.Card_Spade then continue end
				if self.player:isCardLimited(card, sgs.Card_MethodUse)then continue end
				return "$" .. card:getEffectiveId()
			else
				if self.player:isCardLimited(card, sgs.Card_MethodUse)then continue end
				return "$" .. card:getEffectiveId()
			end
		end
		for _, card in ipairs(cards) do
			return "$" .. card:getEffectiveId()
		end
	end
	return "$" .. cards[1]:getEffectiveId()
end

sgs.ai_use_value["jiyu"] = 1
sgs.ai_use_priority["jiyu"] = 2

--滔乱
function Set(list)
	local set = {}
	for _, l in ipairs(list) do set[l] = true end
	return set
end
local patterns = {}
for i = 0, 10000 do
	local card = sgs.Sanguosha:getEngineCard(i)
	if card == nil then break end
	if not (Set(sgs.Sanguosha:getBanPackages()))[card:getPackage()] and (card:isKindOf("BasicCard") or card:isNDTrick()) and not table.contains(patterns, card:objectName())  then
		table.insert(patterns, card:objectName())
	end
end
local taoluan_skill = {
	name = "taoluan", 
	getTurnUseCard = function(self)
		self:sort(self.friends_noself, "handcard")
		if #self.friends_noself == 0 and self:isWeak(self.player) then return false end
		if self.player:isNude() or self.player:getMark("taoluan-Clear") ~= 0 then return end
		local cards = self.player:getCards("he")
		cards = sgs.QList2Table(cards)
		local suit = cards[1]:getSuit()
		local number = cards[1]:getNumber()
		local card_id = cards[1]:getEffectiveId()
		local choices = {}
		for _,name in ipairs(patterns) do
			local poi = sgs.Sanguosha:cloneCard(name, suit, number)
			poi:addSubcard(card_id)
			if poi:isAvailable(self.player) and self.player:getMark("taoluan"..name) == 0 then
				table.insert(choices, name)
			end
		end
		if next(choices) then --如果可以使用
			local to_use = {}
			local types = {"BasicCard", "TrickCard", "EquipCard"}
			local dorn ={"slash","fire_slash","thunder_slash","peach","analeptic","amazing_grace","god_salvation","savage_assault","archery_attack","duel"}
			for _,name in ipairs(choices) do
				local can_use = false
				local poi = sgs.Sanguosha:cloneCard(name, sgs.Card_NoSuit, -1)
				local _type = types[poi:getTypeId()]
				for _, friend in ipairs(self.friends_noself) do
					if getCardsNum(_type, friend, self.player) > 0 then
						can_use = true
						break
					end
				end
				if table.contains(dorn,name) or can_use or not self:isWeak() then
					table.insert(to_use,name)
				end
			end
			if next(to_use) then
				local no_have = true
				for _,c in sgs.qlist(self.player:getCards("he")) do
					if c:objectName()==to_use[1] then
						no_have = false
						break
					end
				end
				if not no_have then return end
				local parsed_card = sgs.Card_Parse("#taoluanCard:"..card_id.. ":" .. to_use[1])
				return parsed_card
			end
		end
	end,
}
table.insert(sgs.ai_skills, taoluan_skill) --加入AI可用技能表
sgs.ai_skill_use_func["#taoluanCard"] = function(card, use, self)
	self:sort(self.friends_noself, "handcard")
	local userstring = card:toString()
	userstring = (userstring:split(":"))[4]
	local taoluancard = sgs.Sanguosha:cloneCard(userstring, card:getSuit(), card:getNumber())
	taoluancard:setSkillName("taoluan")
	if taoluancard:getTypeId() == sgs.Card_TypeBasic then
		self:useBasicCard(taoluancard, use)
	else
		assert(taoluancard)
		self:useTrickCard(taoluancard, use)
	end
	if #self.friends_noself == 0 and userstring == "jink" and userstring == "nullification" then return false end
	if not use.card then return end
	use.card = card
end

sgs.ai_cardsview["taoluan"] = function(self, class_name, player)
	if sgs.Sanguosha:getCurrentCardUseReason() ~= sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE then return end
	local classname2objectname = {
		["Slash"] = "slash", ["Jink"] = "jink",
		["Peach"] = "peach", ["Analeptic"] = "analeptic",
		["Nullification"] = "nullification",
		["FireSlash"] = "fire_slash", ["ThunderSlash"] = "thunder_slash"
	}
	local name = classname2objectname[class_name]
	if not name then return end
	local no_have = true
	local cards = player:getCards("he")
	for _,c in sgs.qlist(cards) do
		if c:isKindOf(class_name) then
			no_have = false
			break
		end
	end
	if not no_have or player:getMark("taoluan-Clear") ~= 0 then return end
	if class_name == "Peach" and player:getMark("Global_PreventPeach") > 0 then return end
	cards = sgs.QList2Table(cards)
	local suit = cards[1]:getSuitString()
	local number = cards[1]:getNumberString()
	local card_id = cards[1]:getEffectiveId()
	if player:hasSkill("taoluan") and player:getMark("taoluan"..name) == 0 then
		return (name..":taoluan[%s:%s]=%d"):format(suit, number, card_id)
	end
end
sgs.ai_skill_playerchosen.taoluan = function(self, targets)
	self:updatePlayers()
	self:sort(self.friends_noself, "handcard")
	local target = nil
	local taoluan_type = self.room:getTag("TaoluanType"):toString()
	for _, friend in ipairs(self.friends_noself) do
		if getCardsNum(taoluan_type, friend, self.player) > 0 then
			target = friend
			break
		end
	end
	local current = self.room:getCurrent()
	if not target then
		if #self.friends_noself > 0 then
			for i = #self.friends_noself, 1, -1 do
				if self.friends_noself[i]:isKongcheng() then break end
				if current:hasUsed("#taoluan"..self.friends_noself[i]:objectName().."no"..taoluan_type) then continue end
				target = self.friends_noself[i]
				break
			end
		end
	end
	if not target then
		targets = sgs.QList2Table(targets)
		self:sort(targets, "handcard")
		for _, p in ipairs(targets) do
			if self:isEnemy(p) then continue end
			if getCardsNum(taoluan_type, p, self.player) > 0 then
				target = p
				break
			end
		end
		if not target then
			for i = #targets, 1, -1 do
				if self:isEnemy(targets[i]) then continue end
				target = targets[i]
				break
			end
		end
	end
	return target
end
sgs.ai_skill_cardask["@taoluan-give"] = function(self, data, pattern, target)
	if target and self:isEnemy(target) then return "." end
	local types = pattern:split("|")[1]:split(",")
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByUseValue(cards)
	for _, card in ipairs(cards) do
		if not self:isValuableCard(card) then
			for _, classname in ipairs(types) do
				if card:isKindOf(classname) then return "$" .. card:getEffectiveId() end
			end
		end
	end
	local current = self.room:getCurrent()
	self.room:addPlayerHistory(current, "#taoluan"..self.player:objectName().."no"..types[1])
	return "."
end

--征南
sgs.ai_skill_invoke.zhengnan = function(self, data)
	return true
end
sgs.ai_skill_choice.zhengnan = function(self, choices)
	local items = choices:split("+")
    if #items == 1 then
        return items[1]
	else
		if table.contains(items, "dangxian") then 
			return "dangxian"
		end
		if table.contains(items, "zhiman") then 
			return "zhiman"
		end
		if table.contains(items, "wusheng") then 
			return "wusheng"
		end
    end
    return items[1]
end
--乱战
sgs.ai_skill_use["@@luanzhan"]=function(self,prompt)
	self:updatePlayers()
	self:sort(self.enemies,"defense")
	local targets = {}
	for _, enemy in ipairs(self.enemies) do
		if #targets < self.player:getMark("@luanz") and enemy:getMark("luanzhan") == 0 then
			--if self:hasTrickEffective(card, enemy, self.player) then--无card参数
			table.insert(targets, enemy:objectName())
		else break end	
	end
	return "#luanzhan:.:->"..table.concat(targets, "+")
end

--英魂
sgs.ai_skill_choice["yinghun_po"] = function(self, choices)
	return self.yinghun_pochoice
end
sgs.ai_skill_playerchosen.yinghun_po = function(self, targets)
	if self.player:hasFlag("AI_doNotInvoke_yinghun") then
		self.player:setFlags("-AI_doNotInvoke_yinghun")
		return
	end
	local x = self.player:getLostHp()
	if self.player:getCards("e"):length() >= self.player:getHp() then x = self.player:getMaxHp()end
	local n = x - 1
	self:updatePlayers()
	if x == 1 and #self.friends == 1 then
		for _, enemy in ipairs(self.enemies) do
			if enemy:hasSkill("manjuan") then
				return enemy
			end
		end
		return nil
	end

	self.yinghun_po = nil
	local player = self:AssistTarget()

	if x == 1 then
		self:sort(self.friends_noself, "handcard")
		self.friends_noself = sgs.reverse(self.friends_noself)
		for _, friend in ipairs(self.friends_noself) do
			if self:hasSkills(sgs.lose_equip_skill, friend) and friend:getCards("e"):length() > 0
			  and not friend:hasSkill("manjuan") then
				self.yinghun_po = friend
				break
			end
		end
		if not self.yinghun_po then
			for _, friend in ipairs(self.friends_noself) do
				if friend:hasSkills("tuntian+zaoxian") and not friend:hasSkill("manjuan") then
					self.yinghun_po = friend
					break
				end
			end
		end
		if not self.yinghun_po then
			for _, friend in ipairs(self.friends_noself) do
				if self:needToThrowArmor(friend) and not friend:hasSkill("manjuan") then
					self.yinghun_po = friend
					break
				end
			end
		end
		if not self.yinghun_po then
			for _, enemy in ipairs(self.enemies) do
				if enemy:hasSkill("manjuan") then
					return enemy
				end
			end
		end

		if not self.yinghun_po and player and not player:hasSkill("manjuan") and player:getCardCount(true) > 0 and not self:needKongcheng(player, true) then
			self.yinghun_po = player
		end

		if not self.yinghun_po then
			for _, friend in ipairs(self.friends_noself) do
				if friend:getCards("he"):length() > 0 and not friend:hasSkill("manjuan") then
					self.yinghun_po = friend
					break
				end
			end
		end

		if not self.yinghun_po then
			for _, friend in ipairs(self.friends_noself) do
				if not friend:hasSkill("manjuan") then
					self.yinghun_po = friend
					break
				end
			end
		end
	elseif #self.friends > 1 then
		self:sort(self.friends_noself, "chaofeng")
		for _, friend in ipairs(self.friends_noself) do
			if self:hasSkills(sgs.lose_equip_skill, friend) and friend:getCards("e"):length() > 0
			  and not friend:hasSkill("manjuan") then
				self.yinghun_po = friend
				break
			end
		end
		if not self.yinghun_po then
			for _, friend in ipairs(self.friends_noself) do
				if friend:hasSkills("tuntian+zaoxian") and not friend:hasSkill("manjuan") then
					self.yinghun_po = friend
					break
				end
			end
		end
		if not self.yinghun_po then
			for _, friend in ipairs(self.friends_noself) do
				if self:needToThrowArmor(friend) and not friend:hasSkill("manjuan") then
					self.yinghun_po = friend
					break
				end
			end
		end
		if not self.yinghun_po and #self.enemies > 0 then
			local wf
			if self.player:isLord() then
				if self:isWeak() and (self.player:getHp() < 2 and self:getCardsNum("Peach") < 1) then
					wf = true
				end
			end
			if not wf then
				for _, friend in ipairs(self.friends_noself) do
					if self:isWeak(friend) then
						wf = true
						break
					end
				end
			end

			if not wf then
				self:sort(self.enemies, "chaofeng")
				for _, enemy in ipairs(self.enemies) do
					if enemy:getCards("he"):length() == n
						and not self:doNotDiscard(enemy, "nil", true, n) then
						self.yinghun_pochoice = "yinghun1"
						return enemy
					end
				end
				for _, enemy in ipairs(self.enemies) do
					if enemy:getCards("he"):length() >= n
						and not self:doNotDiscard(enemy, "nil", true, n)
						and self:hasSkills(sgs.cardneed_skill, enemy) then
						self.yinghun_pochoice = "yinghun1"
						return enemy
					end
				end
			end
		end

		if not self.yinghun_po and player and not player:hasSkill("manjuan") and not self:needKongcheng(player, true) then
			self.yinghun_po = player
		end

		if not self.yinghun_po then
			self.yinghun_po = self:findPlayerToDraw(false, n)
		end
		if not self.yinghun_po then
			for _, friend in ipairs(self.friends_noself) do
				if not friend:hasSkill("manjuan") then
					self.yinghun_po = friend
					break
				end
			end
		end
		if self.yinghun_po then self.yinghun_pochoice = "yinghun2" end
	end
	if not self.yinghun_po and x > 1 and #self.enemies > 0 then
		self:sort(self.enemies, "handcard")
		for _, enemy in ipairs(self.enemies) do
			if enemy:getCards("he"):length() >= n
				and not self:doNotDiscard(enemy, "nil", true, n) then
				self.yinghun_pochoice = "yinghun1"
				return enemy
			end
		end
		self.enemies = sgs.reverse(self.enemies)
		for _, enemy in ipairs(self.enemies) do
			if not enemy:isNude()
				and not (self:hasSkills(sgs.lose_equip_skill, enemy) and enemy:getCards("e"):length() > 0)
				and not self:needToThrowArmor(enemy)
				and not enemy:hasSkills("tuntian+zaoxian") then
				self.yinghun_pochoice = "yinghun1"
				return enemy
			end
		end
		for _, enemy in ipairs(self.enemies) do
			if not enemy:isNude()
				and not (self:hasSkills(sgs.lose_equip_skill, enemy) and enemy:getCards("e"):length() > 0)
				and not self:needToThrowArmor(enemy)
				and not (enemy:hasSkills("tuntian+zaoxian") and x < 3 and enemy:getCards("he"):length() < 2) then
				self.yinghun_pochoice = "yinghun1"
				return enemy
			end
		end
	end

	return self.yinghun_po
end

sgs.ai_skill_choice["yinghun_po"] = function(self, choices)
	return self.yinghun_pochoice
end

--族荫
sgs.ai_skill_invoke.yicheng = function(self, data)
	local player = data:toPlayer()
	return self:isFriend(player)
end
--神速
sgs.ai_skill_use["@@ol_shensu1"]=sgs.ai_skill_use["@@shensu1"]
sgs.ai_skill_use["@@ol_shensu2"]=sgs.ai_skill_use["@@shensu2"]
sgs.ai_skill_use["@@ol_shensu3"]=function(self,prompt)
	self:updatePlayers()
	self:sort(self.enemies,"defense")
	
	local selfSub = self.player:getHandcardNum() - self.player:getMaxCards()
	local selfDef = sgs.getDefense(self.player)

	for _,enemy in ipairs(self.enemies) do
		local def = sgs.getDefenseSlash(enemy, self)
		local slash = sgs.Sanguosha:cloneCard("slash")
		local eff = self:slashIsEffective(slash, enemy) and sgs.isGoodTarget(enemy, self.enemies, self)

		if not self.player:canSlash(enemy, slash, false) then
		elseif self:slashProhibit(nil, enemy) then
		elseif self.player:faceUp() then return "@ShensuCard=.->"..enemy:objectName()
		elseif def < 6 and eff then return "@ShensuCard=.->"..enemy:objectName()
		elseif selfSub >= 2 then return "@ShensuCard=.->"..enemy:objectName()
		elseif selfDef < 6 then return "." 
		else
			for _, friend in ipairs(self.friends) do
			if friend:hasSkills("fangzhu|jilve") and not self:isWeak(friend) then return "@ShensuCard=.->"..enemy:objectName() end
				if friend:hasSkill("junxing") and friend:faceUp() and not self:willSkipPlayPhase(friend)
					and not (friend:isKongcheng() and self:willSkipDrawPhase(friend)) then
					return "@ShensuCard=.->"..enemy:objectName()
				end
			end
		end
	end

	for _,enemy in ipairs(self.enemies) do
		local def=sgs.getDefense(enemy)
		local slash = sgs.Sanguosha:cloneCard("slash")
		local eff = self:slashIsEffective(slash, enemy) and sgs.isGoodTarget(enemy, self.enemies, self)

		if not self.player:canSlash(enemy, slash, false) then
		elseif self:slashProhibit(nil, enemy) then
		elseif eff and def < 8 then return "@ShensuCard=.->"..enemy:objectName()
		elseif self.player:faceUp() then return "@ShensuCard=.->"..enemy:objectName()
		else
			for _, friend in ipairs(self.friends) do
			if friend:hasSkills("fangzhu|jilve") and not self:isWeak(friend) then return "@ShensuCard=.->"..enemy:objectName() end
				if friend:hasSkill("junxing") and friend:faceUp() and not self:willSkipPlayPhase(friend)
					and not (friend:isKongcheng() and self:willSkipDrawPhase(friend)) then
					return "@ShensuCard=.->"..enemy:objectName()
				end
			end
		return "." end
	end
	return "."
end
sgs.ai_card_intention["ol_shensu"] = sgs.ai_card_intention.Slash
--烈弓
sgs.ai_skill_invoke.liegong_po = sgs.ai_skill_invoke.liegong
--狂骨
sgs.ai_skill_choice.ol_kuanggu = function(self, choices)
	local items = choices:split("+")
    if #items == 1 then
        return items[1]
	else
		if table.contains(items, "kuanggu2") and self.player:isWounded() and self:isWeak() then 
			return "kuanggu2"
		end
		if table.contains(items, "kuanggu1") and self:needBear() then 
			return "kuanggu1"
		end
		if table.contains(items, "kuanggu2") and self.player:isWounded() then 
			return "kuanggu2"
		end
		if table.contains(items, "kuanggu1") then 
			return "kuanggu1"
		end
    end
    return items[1]
end
--奇谋
local qimou_skill = {}
qimou_skill.name = "qimou"
table.insert(sgs.ai_skills, qimou_skill)
qimou_skill.getTurnUseCard = function(self)
	if self.player:getMark("@qimou") == 0 or self.player:getHp() < 2 then return end
	return sgs.Card_Parse("#qimouCard:.:")
end
sgs.ai_skill_use_func["#qimouCard"] = function(card, use, self)
	self:updatePlayers()
	self:sort(self.enemies,"defense")
	local acard = sgs.Card_Parse("#qimouCard:.:") --根据卡牌构成字符串产生实际将使用的卡牌
	assert(acard)
	local defense = 6
	local selfSub = self.player:getHandcardNum() - self.player:getHp()
	for _, enemy in ipairs(self.enemies) do
		local def = sgs.getDefense(enemy)
		local slash = sgs.Sanguosha:cloneCard("slash")
		local eff = self:slashIsEffective(slash, enemy) and sgs.isGoodTarget(enemy, self.enemies, self)
	
		if not self.player:canSlash(enemy, slash, false) then
		elseif throw_weapon and enemy:hasArmorEffect("vine") and not self.player:hasSkill("zonghuo") then
		elseif self:slashProhibit(nil, enemy) then
		elseif eff then
			if enemy:getHp() == 1 and getCardsNum("Jink", enemy) == 0 then best_target = enemy break end
			if def < defense then
				best_target = enemy
				defense = def
			end
			target = enemy
		end
		if selfSub < 0 then return "." end
	end
	if best_target then
		if self:getCardsNum("Slash") > 1 and self.player:getHp() > 1 then
			use.card=acard
		end
	end
	if target then
		if self:getCardsNum("Slash") > 1 and self.player:getHp() > 2 then
			use.card=acard
		end
	end
	for _, c in sgs.qlist(self.player:getHandcards()) do
        local x = nil
        if isCard("ArcheryAttack", c, self.player) then
            x = sgs.Sanguosha:cloneCard("ArcheryAttack")
        elseif isCard("SavageAssault", c, self.player) then
            x = sgs.Sanguosha:cloneCard("SavageAssault")
        else continue end

        local du = { isDummy = true }
        self:useTrickCard(x, du)
        if (du.card) and self.player:getHp() > 1 then use.card=acard end
    end
end
sgs.ai_skill_choice.qimou = function(self, choices)
	local items = choices:split("+")
    if #items == 1 then
        return items[1]
	else
		for _, c in sgs.qlist(self.player:getHandcards()) do
			local x = nil
			if isCard("ArcheryAttack", c, self.player) then
				x = sgs.Sanguosha:cloneCard("ArcheryAttack")
			elseif isCard("SavageAssault", c, self.player) then
				x = sgs.Sanguosha:cloneCard("SavageAssault")
			else continue end
			
			local du = { isDummy = true }
			self:useTrickCard(x, du)
			if (du.card) then return tostring(self.player:getHp()-1) end
		end
		return tostring(math.min(self.player:getHp()-1, self:getCardsNum("Slash")))
    end
    return items[1]
end
sgs.ai_use_value["qimouCard"] = 2 --卡牌使用价值
sgs.ai_use_priority["qimouCard"] = 3 --卡牌使用优先级
--止戈
local zhige_skill = {}
zhige_skill.name = "zhige"
table.insert(sgs.ai_skills, zhige_skill)
zhige_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#zhige") or self.player:getHandcardNum() <= self.player:getHp() then return end
	return sgs.Card_Parse("#zhige:.:")
end
sgs.ai_skill_use_func["#zhige"] = function(card,use,self)
	local targets = {}
	local best_enemy = nil
	for _, friend in ipairs(self.friends_noself) do
		local best_target, target
		if getCardsNum("Slash", friend, self.player)>0 then
			for _, enemy in ipairs(self.enemies) do
				local slash = sgs.Sanguosha:cloneCard("slash")
				if friend:canSlash(enemy) and not self:slashProhibit(nil, friend, enemy) and friend:inMyAttackRange(enemy)
				and self:slashIsEffective(slash, friend, enemy) and sgs.isGoodTarget(enemy, self.enemies, friend)then
					if enemy:getHp() == 1 and getCardsNum("Jink", enemy) == 0 then best_target = enemy break end
					if sgs.getDefense(enemy) < 6 then best_target = enemy break end
				end
			end
		end
		if getCardsNum("Slash", friend, self.player)>1 then
			for _, enemy in ipairs(self.enemies) do
				local slash = sgs.Sanguosha:cloneCard("slash")
				if friend:canSlash(enemy) and not self:slashProhibit(nil, friend, enemy) and friend:inMyAttackRange(enemy)
				and self:slashIsEffective(slash, friend, enemy) and sgs.isGoodTarget(enemy, self.enemies, friend)then
					target = enemy
					break
				end
			end
		end
		
		if best_target or target or self:needToThrowArmor(friend) 
		or (friend:hasSkills(sgs.lose_equip_skill) and not friend:getCards("e"):isEmpty()) then 
			table.insert(targets, friend)
		end
	end
	for _, enemy in ipairs(self.enemies) do
		if enemy:getCards("e"):isEmpty() then continue end
		local add_enemy = false
		for _,friend in ipairs(self.friends) do
			local slash = sgs.Sanguosha:cloneCard("slash")
			if enemy:canSlash(friend) and not self:slashProhibit(nil, enemy, friend) and enemy:inMyAttackRange(friend)
			and self:slashIsEffective(slash, enemy, friend) and sgs.isGoodTarget(friend) then
				if getCardsNum("Slash", enemy, self.player) > 1 then break end
				add_enemy = true
			end
		end
		if add_enemy then table.insert(targets, enemy) end
	end
	if #targets == 0 then return end
	for _, target in ipairs(targets) do
		if self:isFriend(target) then continue end
		if getCardsNum("Slash", target, self.player) == 0 then
			if target:getDefensiveHorse() and not self.player:getDefensiveHorse() then
			elseif target:getOffensiveHorse() and not self.player:getOffensiveHorse() then
			elseif target:getArmor() then
				if self.player:getArmor() and self:evaluateArmor(target:getArmor()) >= self:evaluateArmor(self.player:getArmor()) then
				elseif not self.player:getArmor() then
					if self.player:hasSkills("bazhen|yizhong") then return end
				else return end
			elseif target:getWeapon() then
				if self.player:getWeapon() and self:evaluateArmor(target:getWeapon()) >= self:evaluateArmor(self.player:getWeapon()) then
				elseif not self.player:getWeapon() then
				else return end
			elseif target:getTreasure() then
			else continue end
			best_enemy = target
			break
		end
	end
	use.card = card
	if use.to then
		if best_enemy then
			use.to:append(best_enemy)
		else
			self:sort(targets, "defenseSlash")
			use.to:append(targets[1])
		end
	end
end
sgs.ai_skill_use["@zhige"] = function(self, prompt)
	return sgs.ai_skill_use.slash(self, prompt)
end

sgs.ai_skill_cardask["@zhige_give"] = function(self, data, pattern, target)
	if self:needToThrowArmor() then
		return "$" .. self.player:getArmor():getEffectiveId()
	end
	local cards = sgs.QList2Table(self.player:getCards("e"))
	self:sortByKeepValue(cards)
	for _, card in ipairs(cards) do
		return "$" .. card:getEffectiveId()
	end
	return "$" .. cards[1]:getEffectiveId()
end

sgs.ai_use_value["zhige"] = 2 --卡牌使用价值
sgs.ai_use_priority["zhige"] = 3 --卡牌使用优先级

--匡弼
local kuangbi_skill = {
	name = "kuangbi", 
	getTurnUseCard = function(self, inclusive)
		if self.player:hasUsed("#kuangbi") then return end
		return sgs.Card_Parse("#kuangbi:.:")
	end,
}
table.insert(sgs.ai_skills, kuangbi_skill) --加入AI可用技能表
sgs.ai_skill_use_func["#kuangbi"] = function(card, use, self)
	self:updatePlayers()
	self:sort(self.friends_noself, "handcard")
	local acard = sgs.Card_Parse("#kuangbi:.:") --根据卡牌构成字符串产生实际将使用的卡牌
	assert(acard)
		--self:sort(self.friends_noself, "handcard")
	for _, friend in ipairs(self.friends_noself) do
		if friend:isNude() then continue end
		if not friend:hasSkill("manjuan") and (friend:hasSkills(sgs.lose_equip_skill) or self:doNotDiscard(friend) or self:needToThrowArmor(friend)) then
			use.card = acard
			if use.to then
				use.to:append(friend) --填充卡牌使用结构体（to部分）
				return
			end
		end
	end
	for _, friend in ipairs(self.friends_noself) do
		if friend:isNude() then continue end
		if not friend:hasSkill("manjuan") 
		and (friend:hasSkills(sgs.lose_equip_skill) or self:doNotDiscard(friend) or self:needToThrowArmor(friend) or friend:hasSkill("lianying")) then
			use.card = acard
			if use.to then
				use.to:append(friend) --填充卡牌使用结构体（to部分）
			end
			return
		end
	end
	for _, enemy in ipairs(self.enemies) do
		if enemy:isNude() then continue end
		if enemy:hasSkill("manjuan") then
			use.card = acard
			if use.to then
				use.to:append(enemy) --填充卡牌使用结构体（to部分）
			end
			return
		end
	end
	for _, friend in ipairs(self.friends_noself) do
		if friend:isNude() then continue end
		if not friend:hasSkill("manjuan") and not self:isWeak(friend) then
			if (friend:hasSkills(sgs.notActive_cardneed_skill) and not friend:hasSkills(sgs.Active_cardneed_skill))or self:needKongcheng(friend) then
				use.card = acard
				if use.to then
					use.to:append(friend) --填充卡牌使用结构体（to部分）
				end
				return 
			end
		end
	end
	for _, enemy in ipairs(self.enemies) do
		if enemy:isNude() then continue end
		if not enemy:hasSkills(sgs.lose_equip_skill) and not self:doNotDiscard(enemy) and not self:needToThrowArmor(enemy) then
			if enemy:hasSkills(sgs.Active_cardneed_skill) or self:isWeak(enemy) then
				use.card = acard
				if use.to then
					use.to:append(enemy) --填充卡牌使用结构体（to部分）
				end
				return 
			end
		end
	end
end
sgs.ai_skill_discard["kuangbi"] = function(self, discard_num, min_num, optional, include_equip)
	local current = self.room:getCurrent()
	local cards = sgs.QList2Table(self.player:getHandcards())
	local to_discard = {}
	local compare_func = function(a, b)
		return self:getKeepValue(a) < self:getKeepValue(b)
	end
	table.sort(cards, compare_func)
	if self:needToThrowArmor()then
		table.insert(to_discard, self.player:getArmor():getEffectiveId())
	end
	if self:isFriend(current) or self.player:hasSkills(sgs.lose_equip_skill) then
		for _, card in sgs.qlist(self.player:getCards("e")) do
			local equip = card:getRealCard():toEquipCard()
			local equip_index = equip:location()
			if not self.player:hasSkills(sgs.lose_equip_skill) and current:getEquip(equip_index) ~= nil then continue end
			if card:isKindOf("Weapon") then
				if card:isKindOf("Crossbow") or card:isKindOf("Blade") then
					if not self:hasCrossbowEffect() and self:getCardsNum("Slash") > 1 then continue end
				end
				if card:isKindOf("Axe") and self:hasHeavySlashDamage(self.player) then continue end
				if card:isKindOf("GudingBlade") then continue end
			elseif card:isKindOf("Armor") then
				if not self:hasSkills("bazhen|yizhong|linglong") and self:isWeak() then
					if not self:needToThrowArmor()then continue end
				end
			elseif card:isKindOf("DefensiveHorse") then
				if self:isWeak() then continue end
			end
			if not table.contains(to_discard, card:getEffectiveId()) then
				table.insert(to_discard, card:getEffectiveId())
			end
			if #to_discard >= discard_num then break end
		end
	end
	if self:isEnemy(current) or (self.player:getHandcardNum() <= self.player:getMaxCards() and not self.player:hasSkill("lianying") and not self:needKongcheng()) then
		for _, card in ipairs(cards) do
			if #to_discard >= min_num then break end
			table.insert(to_discard, card:getId())
		end
	else
		local Max = math.min(discard_num,self.player:getHandcardNum()-self.player:getMaxCards())
		if Max > #to_discard then
			for _, card in ipairs(cards) do
				if #to_discard >= Max then break end
				table.insert(to_discard, card:getId())
			end
		end
	end
	return to_discard
end
--督粮
local duliang_skill = {}
duliang_skill.name = "duliang"
table.insert(sgs.ai_skills, duliang_skill)
duliang_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#duliang") then return end
	return sgs.Card_Parse("#duliang:.:")
end
sgs.ai_skill_use_func["#duliang"] = function(card, use, self)
	self:updatePlayers()
	self:sort(self.friends_noself, "handcard")
	local acard = sgs.Card_Parse("#duliang:.:") --根据卡牌构成字符串产生实际将使用的卡牌
	assert(acard)
	for _, friend in ipairs(self.friends_noself) do
		if friend:isKongcheng() then continue end
		if self:doNotDiscard(friend) or (self:isWeak(friend) and not friend:hasSkill("manjuan")) or self:needKongcheng(friend) or friend:hasSkills(sgs.cardneed_skill)then
			use.card = acard
			if use.to then
				use.to:append(friend) --填充卡牌使用结构体（to部分）
				return
			end
		end
	end
	for _, friend in ipairs(self.friends_noself) do
		if friend:isKongcheng() then continue end
		use.card = acard
		if use.to then
			use.to:append(friend) --填充卡牌使用结构体（to部分）
			self.room:setTag("duliang",sgs.QVariant(friend:objectName()))
			return
		end
	end
end
sgs.ai_skill_choice["duliang"] = function(self, choices)
	local items = choices:split("+")
	local name = self.room:getTag("duliang"):toString()
	if name == "" then return items[1] end
	local target = nil
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:objectName() == name then
			target = p
			break
		end
	end
	if target:hasSkill("manjuan") or self:needKongcheng(target, true) then return "duliang2" end
	if self:isWeak() or self:isWeak(target)then return "duliang1" end
	if self:needKongcheng(target) or target:hasSkills(sgs.notActive_cardneed_skill) then return "duliang2" end
    return "duliang1"
end
--矫诏
local jiaozhao_skill = {
	name = "jiaozhao", 
	getTurnUseCard = function(self, inclusive)
		local writing = nil
		local acard = nil
		for _, mark in sgs.list(self.player:getMarkNames()) do
			if string.find(mark, "jiaozhao") and self.player:getMark(mark) > 0 then
				writing = string.sub(mark, 9, string.len(mark) - 6)
				for _, card in sgs.qlist(self.player:getCards("h")) do
					if card:hasFlag("jiaozhao"..writing) then
						acard = card
						break
					end
				end
				if acard then break end
			end
		end
		if acard == nil and not self.player:hasUsed("#jiaozhao") and not self.player:isKongcheng() then
			return sgs.Card_Parse("#jiaozhao:.:")
		elseif acard then
			local suit = acard:getSuitString()
			local number = acard:getNumberString()
			local card_id = acard:getEffectiveId()
			local use_card = sgs.Sanguosha:cloneCard(writing, acard:getSuit(), acard:getNumber())
			use_card:setSkillName("jiaozhao")
			local card_str = (writing..":jiaozhao[%s:%s]=%d"):format(suit, number, card_id)
			local skillcard = sgs.Card_Parse(card_str)
			assert(skillcard)
			if use_card:isKindOf("Analeptic") or use_card:isKindOf("ExNihilo") or use_card:isKindOf("Peach") then--禁止桃酒无中
				local dummyuse = { isDummy = true }
				if use_card:getTypeId() == sgs.Card_TypeBasic then
					self:useBasicCard(use_card, dummyuse)
				else
					self:useTrickCard(use_card, dummyuse)
				end
				if dummyuse.skillcard then return skillcard end
			else
				return skillcard
			end
		end
	end,
}
table.insert(sgs.ai_skills, jiaozhao_skill) --加入AI可用技能表
sgs.ai_skill_use_func["#jiaozhao"] = function(card, use, self)
	local cards = self.player:getCards("h")
	cards = sgs.QList2Table(cards)
	self:sortByUseValue(cards, true)
	if #cards == 0 then return end
	local target = nil
	if self.player:getMark("danxin") == 2 then target = self.player end
	if not target then
		local players = self.room:getOtherPlayers(self.player)
		local distance_list = sgs.IntList()
		local nearest = 1000
		for _,p in sgs.qlist(players) do
			local distance = self.player:distanceTo(p)
			distance_list:append(distance)
			nearest = math.min(nearest, distance)
		end
		local danxin_targets = sgs.SPlayerList()
		for i = 0, distance_list:length() - 1, 1 do
			if distance_list:at(i) == nearest then
				danxin_targets:append(players:at(i))
			end
		end
		for _, p in sgs.qlist(danxin_targets) do
			if self:isFriend(p) then target = p break end
		end
		if not target and self.role == "renegade" then
			for _, p in sgs.qlist(danxin_targets) do
				if self:isEnemy(p) then target = p break end
			end
		end
	end
	if not target then return end
	local card_str = string.format("#jiaozhao:%s:", cards[1]:getEffectiveId())
	local acard = sgs.Card_Parse(card_str) --根据卡牌构成字符串产生实际将使用的卡牌
	assert(acard)
	use.card = acard --填充卡牌使用结构体（card部分）
	if use.to then
		use.to:append(target)
	end
end

sgs.ai_view_as["jiaozhao"] = function(card, player, card_place)--该死的禁止桃
	local writing = nil
	for _, mark in sgs.list(player:getMarkNames()) do
		if string.find(mark, "jiaozhao") and player:getMark(mark) > 0 then
			writing = string.sub(mark, 9, string.len(mark) - 6)
			break
		end
	end
	if writing ~= "peach" then return end
	local ask = player:getRoom():getCurrentDyingPlayer()
	if not ask then return end
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	if card_place == sgs.Player_PlaceHand and card:hasFlag("jiaozhao"..writing)
	and ask:objectName()~=player:objectName() and player:getMark("Global_PreventPeach") == 0 then
		return ("peach:jiaozhao[%s:%s]=%d"):format(suit, number, card_id)
	end
end

sgs.ai_skill_askforag["jiaozhao"] = function(self, card_ids)
	--card_ids[i]杀,闪,桃,酒,雷杀,火杀
	--card_ids[i]杀,闪,桃,五谷,桃园,南蛮,万剑,决斗,无中,顺,拆,借刀,无懈,酒,雷杀,铁索,火攻,火杀,水淹
	--230~248
	--230,231,232,243,244,247
	local current = self.room:getCurrent()
	
	local min_jiaozhao_cards = 0
	for i = 0, 10000 do
		local card = sgs.Sanguosha:getEngineCard(i)
		if card:getPackage() == "jiaozhao" then
			min_jiaozhao_cards = i  --最小的矫诏卡牌号，默认为230（在没开屎包且没加其他卡牌扩展时）
			break
		end
	end
	if self:isEnemy(current) then
		--return 243--酒
		return min_jiaozhao_cards + 13
	end
	if current:getMark("danxin") == 0 then
		if sgs.Slash_IsAvailable(current) and getCardsNum("Slash", current, self.player) < 3 then
			for _, enemy in ipairs(self.enemies) do
				if current:canSlash(enemy) and sgs.isGoodTarget(enemy, self.enemies, current, true) then
					local thunder_slash = sgs.Sanguosha:cloneCard("thunder_slash")
					local fire_slash = sgs.Sanguosha:cloneCard("fire_slash")
					if not self:slashProhibit(fire_slash, enemy, current)and self:slashIsEffective(fire_slash, enemy, current)then
						--return 247--火杀
						return min_jiaozhao_cards + 17
					end
					if not self:slashProhibit(thunder_slash, enemy, self.player)and self:slashIsEffective(thunder_slash, enemy, self.player)then
						--return 244--雷杀
						return min_jiaozhao_cards + 14
					end
					if not self:slashProhibit(slash, enemy, self.player)and self:slashIsEffective(slash, enemy, self.player)then
						--return 230--杀
						return min_jiaozhao_cards + 0
					end
				end
			end
		end
		--return 232--桃
		return min_jiaozhao_cards + 2
	end
	local aoename = "savage_assault|archery_attack"
	local aoenames = aoename:split("|")
	local aoe
	local i
	local good, bad = 0, 0
	local qicetrick = "savage_assault|archery_attack|god_salvation"
	local qicetricks = qicetrick:split("|")
	local aoe_available, ge_available = true, true
	for i = 1, #qicetricks do
		local forbiden = qicetricks[i]
		forbid = sgs.Sanguosha:cloneCard(forbiden)
		if current:isCardLimited(forbid, sgs.Card_MethodUse, true) or not forbid:isAvailable(current) then
			if forbid:isKindOf("AOE") then aoe_available = false end
			if forbid:isKindOf("GlobalEffect") then ge_available = false end
		end
	end
	for _,p in sgs.qlist(self.room:getOtherPlayers(current)) do
		if self:isFriend(p) then
			if p:isWounded() then
				good = good + 10 / p:getHp()
				if p:isLord() then good = good + 10 / p:getHp() end
			end
		else
			if p:isWounded() then
				bad = bad + 10 / p:getHp()
				if p:isLord() then
					bad = bad + 10 / p:getHp()
				end
			end
		end
	end
	local godsalvation = sgs.Sanguosha:cloneCard("god_salvation")
	if aoe_available then
		for i = 1, #aoenames do
			local newqice = aoenames[i]
			aoe = sgs.Sanguosha:cloneCard(newqice)
			local earnings = 0
			local need
			if aoe:isKindOf("SavageAssault") then need = "Slash"
			elseif aoe:isKindOf("ArcheryAttack") then need = "Jink" end
			for _,p in sgs.qlist(self.room:getOtherPlayers(current)) do
				if self:isFriend(p) then
					if not p:hasArmorEffect("Vine") and self:damageIsEffective(p, nil, self.player) and getCardsNum(need, p, self.player) == 0 then
						earnings = earnings - 1
						if self:isWeak(p) then
							earnings = earnings - 1
						end
						if self:hasEightDiagramEffect(p) and need == "Jink" then
							earnings = earnings + 1
						end
					else
						earnings = earnings + 1
					end
				else
					if not p:hasArmorEffect("Vine") and self:damageIsEffective(p, nil, self.player) and getCardsNum(need, p, self.player) == 0 then
						earnings = earnings + 1
						if self:isWeak(p) then
							earnings = earnings + 1
						end
						if self:hasEightDiagramEffect(p) and need == "Jink" then
							earnings = earnings - 1
						end
					end
				end
				if earnings > 0 then--'getAoeValue' (a nil value)
					if newqice == "savage_assault" then
						--return 235--南蛮
						return min_jiaozhao_cards + 5
					elseif newqice == "archery_attack" then
						--return 236--万剑
						return min_jiaozhao_cards + 6
					end
				end
			end
		end
	end
	if ge_available and good > bad then-- 'willUseGodSalvation' (a nil value)
		--return 234--桃园
		return min_jiaozhao_cards + 4
	end
	for _,p in sgs.qlist(self.room:getOtherPlayers(current)) do
		local card = sgs.Sanguosha:cloneCard("snatch")
		if current:isCardLimited(card, sgs.Card_MethodUse, true)then break end
		if self.room:isProhibited(current, p, card) or current:distanceTo(p)>1 then continue end
		if self:isFriend(p) and (p:containsTrick("indulgence") or p:containsTrick("supply_shortage")) and not p:containsTrick("YanxiaoCard")then
		elseif self:isEnemy(p) and not p:isNude()then
		else continue end
		--return 239--顺
		return min_jiaozhao_cards + 9
	end
	for _, enemy in ipairs(self.enemies) do
		local card = sgs.Sanguosha:cloneCard("duel")
		if current:isCardLimited(card, sgs.Card_MethodUse, true)then break end
		if self.room:isProhibited(current, enemy, card) then continue end
		if getCardsNum("Slash", current, self.player) >= getCardsNum("Slash", enemy, self.player) then
			--return 237--决斗
			return min_jiaozhao_cards + 7
		end
	end
	local a,b
	for _,p in sgs.qlist(self.room:getOtherPlayers(current)) do
		local card = sgs.Sanguosha:cloneCard("iron_chain")
		if current:isCardLimited(card, sgs.Card_MethodUse, true)then break end
		if self.room:isProhibited(current, p, card) then continue end
		if p:isChained() and self:isFriend(p) then
		elseif not p:isChained() and self:isEnemy(p) then
		else continue
		end
		if not a then
			a=p
		else
			if not b then
				b=p
			else break
			end
		end
	end
	if a and b then
		--return 245--铁索
		return min_jiaozhao_cards + 15
	end
	for _,p in sgs.qlist(self.room:getOtherPlayers(current)) do
		local card = sgs.Sanguosha:cloneCard("collateral")
		if current:isCardLimited(card, sgs.Card_MethodUse, true) then break end
		if not p:getWeapon() then continue end
		if self:isFriend(p) and getCardsNum("Slash", p, self.player) > 2 then
		elseif self:isEnemy(p) and getCardsNum("Slash", p, self.player)==0 then
		else continue
		end
		--return 241--借刀
		return min_jiaozhao_cards + 11
	end
	if sgs.Slash_IsAvailable(current) and getCardsNum("Slash", current, self.player) == 0 then
		for _, enemy in ipairs(self.enemies) do
			if current:canSlash(enemy) and sgs.isGoodTarget(enemy, self.enemies, current, true) then
				local thunder_slash = sgs.Sanguosha:cloneCard("thunder_slash")
				local fire_slash = sgs.Sanguosha:cloneCard("fire_slash")
				if not self:slashProhibit(fire_slash, enemy, current)and self:slashIsEffective(fire_slash, enemy, current)then
					--return 247--火杀
					return min_jiaozhao_cards + 17
				end
				if not self:slashProhibit(thunder_slash, enemy, self.player)and self:slashIsEffective(thunder_slash, enemy, self.player)then
					--return 244--雷杀
					return min_jiaozhao_cards + 14
				end
				if not self:slashProhibit(slash, enemy, self.player)and self:slashIsEffective(slash, enemy, self.player)then
					--return 230--雷杀
					return min_jiaozhao_cards + 0
				end
			end
		end
	end
	if getCardsNum("TrickCard", current, self.player) > 0 and getCardsNum("Nullification", current, self.player) == 0 then
		--return 242--无懈
		return min_jiaozhao_cards + 12
	else
		--return 240--拆
		return min_jiaozhao_cards + 10
	end
end
sgs.ai_use_priority["jiaozhao"] = 2 --卡牌使用优先级
--殚心
sgs.ai_skill_choice.danxin = function(self, choices)
	local items = choices:split("+")
	if #items == 1 then
        return items[1]
	else
		if self:isWeak() and self.player:getHp() < 2 then return "danxin1" end
		if table.contains(items, "danxin2") then return "danxin2" end
		if table.contains(items, "danxin3") then return "danxin3" end
	end
    return items[1]
end
--生息
sgs.ai_skill_invoke.ol_shengxi = function(self, data)
	return not self:needKongcheng(self.player, true)
end
--恂恂
sgs.ai_skill_invoke.ol_xunxun = function(self, data)
	return true
end
--决死
local juesi_skill = {
	name = "juesi", 
	getTurnUseCard = function(self, inclusive)
		if self:getCardsNum("Slash") == 0 then return end
		local can_use = false
		for _,p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
			if p:isNude() then continue end
			if self.player:inMyAttackRange(p) then 
				can_use = true
				break
			end
		end
		if can_use then
			return sgs.Card_Parse("#juesi:.:")
		end
	end,
}
table.insert(sgs.ai_skills, juesi_skill) --加入AI可用技能表
sgs.ai_skill_use_func["#juesi"] = function(card, use, self)
	local handcards = self.player:getHandcards()
	local slashs = {}
	for _,c in sgs.qlist(handcards) do
		if c:isKindOf("Slash") then
			if self.player:canDiscard(self.player, c:getEffectiveId()) then
				table.insert(slashs, c)
			end
		end
	end
	if #slashs == 0 then return end
	self:sort(self.enemies, "chaofeng")
	local enemys = {}
	for _, enemy in ipairs(self.enemies) do
		if not self.player:inMyAttackRange(enemy) then continue end
		if self:doNotDiscard(enemy) and not (self:isWeak(enemy) and self.player:getHp() <= enemy:getHp()) then continue end
		if self:hasSkills(sgs.lose_equip_skill, enemy) and not (self:isWeak(enemy) and self.player:getHp() <= enemy:getHp()) then continue end
		if self:needKongcheng(enemy) and enemy:getHandcardNum() == 1 then continue end
		if self:getCardsNum("Slash") - 1 < getCardsNum("Slash", enemy) and self.player:getHp() <= enemy:getHp() then continue end
		table.insert(enemys, enemy)
	end
	if #enemys > 0 then
		local card_str = string.format("#juesi:%s:", slashs[1]:getEffectiveId())
		local acard = sgs.Card_Parse(card_str)
		use.card = acard
		if use.to then use.to:append(enemys[1]) end
	else
		if self:getOverflow() <= 0 then return end
		local friends = {}
		for _, friend in ipairs(self.friends_noself) do
			if self.player:getHp() <= friend:getHp() then continue end
			if not self.player:inMyAttackRange(friend) then continue end
			if self:needKongcheng(friend) and friend:getHandcardNum() == 1 then
			elseif self:needToThrowArmor(friend) then
			elseif self:hasSkills(sgs.lose_equip_skill, friend) then
			else continue end
			table.insert(friends, friend)
		end
		if #friends > 0 then
			local card_str = string.format("#juesi:%s:", slashs[1]:getEffectiveId())
			local acard = sgs.Card_Parse(card_str)
			use.card = acard
			if use.to then use.to:append(friends[1]) end 
		end
	end
end
sgs.ai_skill_cardask["@juesi"] = function(self, data, pattern, target)
	local current = self.room:getCurrent()
	local card = nil
	if self:needToThrowArmor() then card = self.player:getArmor() end
	local handcards = self.player:getHandcards()
	if not card and self:needKongcheng() and self.player:getHandcardNum() == 1 then
		card = sgs.QList2Table(handcards)[1]
	end
	if not card and self:hasSkills(sgs.lose_equip_skill) then
		local cards = sgs.QList2Table(self.player:getCards("e"))
		self:sortByKeepValue(cards)
		card = cards[1]
	end
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	if not card then
		card = cards[1]
	end
	local slashs = {}
	for _,c in sgs.qlist(handcards) do
		if c:isKindOf("Slash") then
			if self.player:canDiscard(self.player, c:getEffectiveId()) then
				table.insert(slashs, c)
			end
		end
	end
	if self.player:getHp() > current:getHp() then
		if self:isEnemy(current) and self:getCardsNum("Slash") > getCardsNum("Slash", current) then
			if card:isKindOf("Slash") then
				for _, c in ipairs(cards) do
					if not c:isKindOf("Slash") then
						card = c
						break
					end
				end
			end
			return "$" .. card:getEffectiveId()
		end
		if #slashs > 0 then
			return "$" .. slashs[1]:getEffectiveId()
		else
			return "$" .. card:getEffectiveId()
		end
	else
		return "$" .. card:getEffectiveId()
	end
end
--间书
local jianshu_skill = {
	name = "jianshu", 
	getTurnUseCard = function(self, inclusive)
		if self.player:getMark("@book") == 0 then return end
		local can_use = false
		self:sort(self.enemies, "chaofeng")
		for _, enemy_a in ipairs(self.enemies) do
			for _, enemy_b in ipairs(self.enemies) do
				if enemy_b:isKongcheng() then continue end
				if enemy_a:objectName() == enemy_b:objectName() then continue end
				if not enemy_b:inMyAttackRange(enemy_a) then continue end
				can_use = true
				break
			end
			if can_use == true then break end
		end
		if can_use then
			return sgs.Card_Parse("#jianshu:.:")
		end
	end,
}
table.insert(sgs.ai_skills, jianshu_skill) --加入AI可用技能表
sgs.ai_skill_use_func["#jianshu"] = function(card, use, self)
	local handcards = self.player:getHandcards()
	local blacks = {}
	for _,c in sgs.qlist(handcards) do
		if c:isBlack() then
			table.insert(blacks, c) --加入可用集合
		end
	end
	if #blacks == 0 then return end
	self:sortByUseValue(blacks, true) --对可用手牌按使用价值从小到大排序
	local togive = nil
	local max_card, max_point = nil, 0
	local min_card, min_point = nil, 14
	for _, c in ipairs(blacks) do
		local point = c:getNumber()
		if point > max_point then
			max_point = point
			max_card = c
		elseif point < min_point then
			min_point = point
			min_card = c
		end
	end
	self:sort(self.enemies, "chaofeng")
	local weak_enemy = {}
	local kong_enemy = {}
	local kong_weak = {}
	for _, enemy in ipairs(self.enemies) do
		if self:isWeak(enemy) and not enemy:isKongcheng() then
			table.insert(weak_enemy, enemy)
		elseif not self:isWeak(enemy) and enemy:isKongcheng() then
			if enemy:hasSkill("manjuan") or enemy:hasSkill("qingjian") then continue end
			table.insert(kong_enemy, enemy)
		elseif self:isWeak(enemy) and enemy:isKongcheng() then
			table.insert(weak_enemy, enemy)
			if enemy:hasSkill("manjuan") or enemy:hasSkill("qingjian") then continue end
			table.insert(kong_enemy, enemy)
			table.insert(kong_weak, enemy)
		end
	end
	if #weak_enemy == 0 then return end
	local lost_enemy = nil
	local dis_enemy = nil
	if #kong_weak > 0 then
		for _, enemy_a in ipairs(kong_weak) do
			for _, enemy_b in ipairs(self.enemies) do
				if enemy_b:isKongcheng() then continue end
				if enemy_a:objectName() == enemy_b:objectName() then continue end
				if not enemy_b:inMyAttackRange(enemy_a) then continue end
				local min_card = self:getMinCard(enemy_b)
				local min_num = 10
				if min_card then
					min_num = min_card:getNumber()
				end
				if min_num >= min_point then
					dis_enemy = enemy_b
					break
				end
			end
			if dis_enemy then
				lost_enemy = enemy_a
				break
			end
		end
	end
	if not lost_enemy then
		if #weak_enemy > 1 then
			for _, enemy_a in ipairs(weak_enemy) do
				for _, enemy_b in ipairs(self.enemies) do
					if enemy_b:isKongcheng() then continue end
					if enemy_a:objectName() == enemy_b:objectName() then continue end
					if not enemy_b:inMyAttackRange(enemy_a) then continue end
					if not self:isWeak(enemy_b) then continue end
					togive = blacks[1]
					if enemy_a:getCardCount(true) > enemy_b:getCardCount(true) then
						if enemy_b:getCardCount(true) > 1 then
							lost_enemy = enemy_a
							dis_enemy = enemy_b
						else
							lost_enemy = enemy_b
							dis_enemy = enemy_a
						end
					else
						if enemy_a:getCardCount(true) > 1 then
							lost_enemy = enemy_b
							dis_enemy = enemy_a
						else
							lost_enemy = enemy_a
							dis_enemy = enemy_b
						end
					end
					break
				end
				if togive then break end
			end
		end
		if not lost_enemy then
			for _, enemy_a in ipairs(weak_enemy) do
				for _, enemy_b in ipairs(self.enemies) do
					if enemy_b:isKongcheng() then continue end
					if enemy_a:objectName() == enemy_b:objectName() then continue end
					if not enemy_b:inMyAttackRange(enemy_a) then continue end
					local min_card = self:getMinCard(enemy_b)
					local min_num = 10
					if min_card then
						min_num = min_card:getNumber()
					end
					local max_card = self:getMaxCard(enemy_a)
					if max_card then
						max_num = math.max(min_point,max_card:getNumber())
					else
						max_num = math.max(min_point,4)
					end
					if min_num >= max_num then
						dis_enemy = enemy_b
						break
					end
				end
				if not dis_enemy and #kong_enemy > 0 then
					for _, enemy_b in ipairs(kong_enemy) do
						if enemy_a:objectName() == enemy_b:objectName() then continue end
						if not enemy_b:inMyAttackRange(enemy_a) then continue end
						local max_card = self:getMaxCard(enemy_a)
						local max_num = 4
						if max_card then 
							max_num = max_card:getNumber()
						end
						if max_point > max_num then
							dis_enemy = enemy_b
							break
						end
					end
				end
				if dis_enemy then
					lost_enemy = enemy_a
					break
				end
			end
		end
	end
	if lost_enemy and dis_enemy then
		if dis_enemy:isKongcheng() then
			togive = max_card
		elseif not togive then
			togive = min_card
		end 
		local card_str = string.format("#jianshu:%s:", togive:getEffectiveId())
		local acard = sgs.Card_Parse(card_str)
		use.card = acard
		if use.to then 
			if dis_enemy:isKongcheng() then
				use.to:append(dis_enemy)
				use.to:append(lost_enemy)
			else
				use.to:append(lost_enemy)
				use.to:append(dis_enemy)
			end
		end
	end
end

--拥嫡
sgs.ai_skill_playerchosen.yongdi = function(self, targets)
	self:updatePlayers()
	local lords = {}
	local targets = {}
	local good_targets = {}
	local best_target = nil
	self:sort(self.friends_noself, "chaofeng")
	for _, friend in ipairs(self.friends_noself) do
		if not friend:isMale() then continue end
		if self:hasSkills(sgs.need_maxhp_skill, friend) then
			table.insert(good_targets, friend)
		end
		for _,skill in sgs.qlist(friend:getGeneral():getVisibleSkillList()) do
			if skill:isLordSkill() and not friend:hasSkill(skill:objectName()) and not friend:isLord() then
				table.insert(lords, friend)
				break
			end
		end
		if self:isWeak(friend) then continue end
		table.insert(targets, friend)
	end
	for _, lord in ipairs(lords) do
		if self:hasSkills(sgs.need_maxhp_skill, lord) then
			best_target = lord
			break
		end
	end
	if best_target then return best_target end
	if #good_targets > 0 then return good_targets[1] end
	if #lords > 0 then return lords[1] end
	if self:isWeak() then return targets[1] end
	return nil
end

--乱击
local luanji_po_skill = {}
luanji_po_skill.name = "luanji_po"
table.insert(sgs.ai_skills, luanji_po_skill)
luanji_po_skill.getTurnUseCard = function(self)
	local archery = sgs.Sanguosha:cloneCard("archery_attack")
	local first_found, second_found = false, false
	local first_card, second_card
	if self.player:getHandcardNum() >= 2 then
		local cards = self.player:getHandcards()
		local same_suit = false
		cards = sgs.QList2Table(cards)
		self:sortByKeepValue(cards)
		local useAll = false
		for _, enemy in ipairs(self.enemies) do
			if enemy:getHp() == 1 and not enemy:hasArmorEffect("Vine") and not self:hasEightDiagramEffect(enemy) and self:damageIsEffective(enemy, nil, self.player)
				and self:isWeak(enemy) and getCardsNum("Jink", enemy, self.player) + getCardsNum("Peach", enemy, self.player) + getCardsNum("Analeptic", enemy, self.player) == 0 then
				useAll = true
			end
		end
		for _, fcard in ipairs(cards) do
			local fvalueCard = (isCard("Peach", fcard, self.player) or isCard("ExNihilo", fcard, self.player) or isCard("ArcheryAttack", fcard, self.player))
			if useAll then fvalueCard = isCard("ArcheryAttack", fcard, self.player) end
			if not fvalueCard and not self.player:hasFlag(fcard:getSuitString()) then
				first_card = fcard
				first_found = true
				for _, scard in ipairs(cards) do
					local svalueCard = (isCard("Peach", scard, self.player) or isCard("ExNihilo", scard, self.player) or isCard("ArcheryAttack", scard, self.player))
					if useAll then svalueCard = (isCard("ArcheryAttack", scard, self.player)) end
					if first_card ~= scard and not self.player:hasFlag(scard:getSuitString())
						and not svalueCard then

						local card_str = ("archery_attack:luanji_po[%s:%s]=%d+%d"):format("to_be_decided", 0, first_card:getId(), scard:getId())
						local archeryattack = sgs.Card_Parse(card_str)

						assert(archeryattack)

						local dummy_use = { isDummy = true }
						self:useTrickCard(archeryattack, dummy_use)
						if dummy_use.card then
							second_card = scard
							second_found = true
							break
						end
					end
				end
				if second_card then break end
			end
		end
	end

	if first_found and second_found then
		local first_id = first_card:getId()
		local second_id = second_card:getId()
		local card_str = ("archery_attack:luanji_po[%s:%s]=%d+%d"):format("to_be_decided", 0, first_id, second_id)
		local archeryattack = sgs.Card_Parse(card_str)
		assert(archeryattack)
		return archeryattack
	end
end
--绝地
sgs.ai_skill_playerchosen.ol_juedi  = function(self, targets)
	targets = sgs.QList2Table(targets)
	self:sort(targets, "defense")
	for _,p in ipairs(targets) do
		if self:isFriend(p) and (self:isWeak(p) or 2*self.player:getPile("hat"):length() >= (self.player:getMaxHp()- self.player:getHandcardNum())) and not self:needKongcheng(p,true) then 
			return p 
		end
	end
	return nil
end
sgs.ai_playerchosen_intention.ol_juedi = -80
--司敌
sgs.ai_skill_cardask["@ol_sidi"] = function(self, data)
	local current = data:toPlayer()
	if not self:isEnemy(current)  then return "." end
	local red,black,reds,blacks = 0,0,0,0
	for _,c in sgs.qlist(current:getCards("h")) do
		if c:isAvailable(current) then
			if c:isRed() then
				red = red + 1 
				if c:isKindOf("Slash") then
					reds = reds + 1
				end
			else
				black = black + 1
				if c:isKindOf("Slash") then
					blacks = blacks + 1
				end
			end
		end
	end
	if (red == 0 or black == 0) and not self:isWeak(current) then return "." end
	local color
	if red ~= black then
		color = (red > black and "red") or "black"
	else
		color = (reds > blacks and "red") or "black"
	end
	local cards={}
	for _,c in sgs.qlist(self.player:getCards("e")) do
		if color == "red" then
			if self.player:canDiscard(self.player,c:getId()) and c:isRed() then
				table.insert(cards,c)
			end
		else
			if self.player:canDiscard(self.player,c:getId()) and c:isBlack() then
				table.insert(cards,c)
			end			
		end
	end
	if #cards==0 and self:isWeak(current) then
		for _,c in sgs.qlist(self.player:getCards("e")) do
			if self.player:canDiscard(self.player,c:getId()) then
				table.insert(cards,c)
			end
		end
	end
	if #cards==0 then return "." end
	self:sortByKeepValue(cards)
	return "$" .. cards[1]:getId()
end
sgs.ai_choicemade_filter.cardResponded["@ol_sidi"] = function(self, player, promptlist)
	if promptlist[#promptlist] ~= "_nil_" then
		local current = self.player:getTag("sidi_target"):toPlayer()
        if current then
            sgs.updateIntention(player, current, 80)
        end
	end
end
sgs.ai_skill_choice.bianshen = function(self, choices, data)
	return "shenlvbu2"
end
sgs.ai_skill_invoke.jianchu = function(self,data)
	local target = data:toPlayer()
	if not self:isFriend(target) then return true end
	return false
end
sgs.ai_skill_invoke.ol_liegong = function(self,data)
	local target = data:toPlayer()
	if not self:isFriend(target) then return true end
	return false
end
sgs.ai_skill_invoke.ol_guixin = sgs.ai_skill_invoke.guixin
sgs.ai_skill_choice["ol_guixin"] = function(self, choices, data)
	local target = data:toPlayer()
	if self:isFriend(target) then
		if not target:getCards("j"):isEmpty() then return "j" end
		if not target:isKongcheng() and self:needKongcheng(target) then return "h" end 
		if not target:getEquips():isEmpty() then return "e" end
		return "h"
	else
		if not target:getEquips():isEmpty() then return "e" end
		if not target:isKongcheng() and not self:needKongcheng(target) then return "h" end
		if not target:getCards("j"):isEmpty() then return "j" end 
	end
end
sgs.ai_skill_invoke.kuangcai = function(self,data)
	return true
end

sgs.ai_skill_playerchosen.shejian = function(self, targets)
	targets = sgs.QList2Table(targets)
	self:sort(targets,"defense")
	for _, enemy in ipairs(self.enemies) do
		if (not self:doNotDiscard(enemy) or self:getDangerousCard(enemy) or self:getValuableCard(enemy)) and not enemy:isNude() and
		not (enemy:hasSkill("guzheng") and self.room:getCurrent():getPhase() == sgs.Player_Discard) then
			return enemy
		end
	end
	for _, friend in ipairs(self.friends) do
		if(self:hasSkills(sgs.lose_equip_skill, friend) and not friend:getEquips():isEmpty())
		or (self:needToThrowArmor(friend) and friend:getArmor()) or self:doNotDiscard(friend) then
			return friend
		end
	end
end

sgs.ai_skill_invoke.yixiang = function(self,data)
	return true
end

sgs.ai_skill_playerchosen.yirang = function(self, targets)
	self:updatePlayers()
	targets = sgs.QList2Table(targets)
	local players = sgs.SPlayerList()
	local maxhp = self.player:getMaxHp()
	for _, target in ipairs(targets) do
		if target:getMaxHp() > maxhp then
			maxhp = target:getMaxHp()
		end
	end
	local maxhp = self.player:getMaxHp()
	for _, target in ipairs(targets) do
		if target:getMaxHp() == maxhp then
			if  self:isFriend(target) and not target:needKongcheng() then
				return target
			else
				if target:hasSkill("manjuan") and not self:isEnemy(target) and not target:needKongcheng() then
					return target
				end
			end
		end
	end
		if self:isEnemy(target) and target:hasSkill("manjuan") and not target:isNude() then
			return target
		end
	for _, target in ipairs(targets) do
		if self:isFriend(target) and not target:hasSkill("manjuan") 
		and (target:hasSkills(sgs.lose_equip_skill) or self:doNotDiscard(target) or self:needToThrowArmor(target)) then
			return target
		end
	end
	for _, target in ipairs(targets) do
		if self:isEnemy(target) and target:hasEquip()
		and not target:hasSkills(sgs.lose_equip_skill) and not self:doNotDiscard(target) and not target:hasSkills(sgs.cardneed_skill) then
			return target
		end
	end
	if self:PayContains(targets, self.player) then return self.player end
	return "."
end

sgs.ai_skill_cardchosen.shejian = function(self, who, flags)
	local cards = sgs.QList2Table(who:getEquips())
	local handcards = sgs.QList2Table(who:getHandcards())
	if #handcards < 3 or handcards[1]:hasFlag("visible") then table.insert(cards,handcards[1]) end

	for i=1,#cards,1 do
			return cards[i]
	end
	return nil
end

sgs.ai_choicemade_filter.cardChosen.shejian = sgs.ai_choicemade_filter.cardChosen.dismantlement

sgs.ai_skill_use["@@yingjian"] = function(self, prompt)
	local card_str = sgs.ai_skill_use["@@shensu1"](self, "@shensu1")
	return string.gsub(card_str, "ShensuCard", "QingyiCard")
end

ol_duanliang_skill={}
ol_duanliang_skill.name = "ol_duanliang"
table.insert(sgs.ai_skills,ol_duanliang_skill)
ol_duanliang_skill.getTurnUseCard=function(self)
	local cards = self.player:getCards("he")
	cards=sgs.QList2Table(cards)

	local card

	self:sortByUseValue(cards,true)

	for _,acard in ipairs(cards)  do
		if (acard:isBlack()) and (acard:isKindOf("BasicCard") or acard:isKindOf("EquipCard")) and (self:getDynamicUsePriority(acard)<sgs.ai_use_value.SupplyShortage)then
			card = acard
			break
		end
	end

	if not card then return nil end
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	local card_str = ("supply_shortage:ol_duanliang[%s:%s]=%d"):format(suit, number, card_id)
	local skillcard = sgs.Card_Parse(card_str)

	assert(skillcard)

	return skillcard
end

sgs.ai_cardneed.ol_duanliang = function(to, card, self)
	return card:isBlack() and card:getTypeId() ~= sgs.Card_TypeTrick and getKnownCard(to, self.player, "black", false) < 2
end

sgs.ol_duanliang_suit_value = {
	spade = 3.9,
	club = 3.9
}
--OL曹仁
function sgs.ai_skill_invoke.ol_jushou(self, data)
	if not self.player:faceUp() then return true end
	for _, friend in ipairs(self.friends) do
		if friend:hasSkills("fangzhu|jilve") and not self:isWeak(friend) then return true end
		if friend:hasSkill("junxing") and friend:faceUp() and not self:willSkipPlayPhase(friend)
			and not (friend:isKongcheng() and self:willSkipDrawPhase(friend)) then
			return true
		end
	end
	for _, card in sgs.qlist(self.player:getHandcards()) do
		if card:getTypeId() == sgs.Card_TypeTrick and not card:isKindOf("Nullification") then
			local dummy_use = { isDummy = true }
			self:useTrickCard(card, dummy_use)
			if dummy_use.card then return true end
		elseif card:getTypeId() == sgs.Card_TypeEquip then
			local dummy_use = { isDummy = true }
			self:useEquipCard(card, dummy_use)
			if dummy_use.card then return true end
		end
	end
	local Rate = math.random() + self.player:getCardCount()/10 + self.player:getHp()/10
	if Rate > 1.1 then return true end
	return false
end
sgs.ai_skill_cardask["@jushou"] = function(self, data)
	local has_analeptic, has_slash, has_jink
	for _, acard in sgs.qlist(self.player:getHandcards()) do
		if acard:isKindOf("DefensiveHorse") and not self.player:getDefensiveHorse() then return "$" .. acard:getEffectiveId() end
	end
	for _, acard in sgs.qlist(self.player:getHandcards()) do
		if acard:isKindOf("Armor") and not self.player:getArmor() then return "$" .. acard:getEffectiveId() end
	end
	for _, acard in sgs.qlist(self.player:getHandcards()) do
		if acard:isKindOf("Weapon") and not self.player:getWeapon() then return "$" .. acard:getEffectiveId() end
	end
	for _, acard in sgs.qlist(self.player:getHandcards()) do
		if acard:isKindOf("EquipCard") then return "$" .. acard:getEffectiveId() end
	end
	local to_discard = self:askForDiscard("jushou", 1, 1, false, true)
	if #to_discard > 0 then return "$" .. to_discard[1] else return "." end
end

sgs.ai_view_as.ol_jiewei = function(card, player, card_place)
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	if card_place == sgs.Player_PlaceEquip then
		return ("nullification:ol_jiewei[%s:%s]=%d"):format(suit, number, card_id)
	end
end
local function card_for_jiewei(self, who, return_prompt)
	local card, target
	if self:isFriend(who) then
		local judges = who:getJudgingArea()
		if not judges:isEmpty() then
			for _, judge in sgs.qlist(judges) do
				card = sgs.Sanguosha:getCard(judge:getEffectiveId())
				if not judge:isKindOf("YanxiaoCard") then
					for _, enemy in ipairs(self.enemies) do
						if not enemy:containsTrick(judge:objectName()) and not enemy:containsTrick("YanxiaoCard")
							and not self.room:isProhibited(self.player, enemy, judge) and not (enemy:hasSkill("hongyan") or judge:isKindOf("Lightning")) then
							target = enemy
							break
						end
					end
					if target then break end
				end
			end
		end

		local equips = who:getCards("e")
		local weak
		if not target and not equips:isEmpty() and self:hasSkills(sgs.lose_equip_skill, who) then
			for _, equip in sgs.qlist(equips) do
				if equip:isKindOf("OffensiveHorse") then card = equip break
				elseif equip:isKindOf("Weapon") then card = equip break
				elseif equip:isKindOf("DefensiveHorse") and not self:isWeak(who) then
					card = equip
					break
				elseif equip:isKindOf("Armor") and (not self:isWeak(who) or self:needToThrowArmor(who)) then
					card = equip
					break
				end
			end

			if card then
				if card:isKindOf("Armor") or card:isKindOf("DefensiveHorse") then
					self:sort(self.friends, "defense")
				else
					self:sort(self.friends, "handcard")
					self.friends = sgs.reverse(self.friends)
				end

				for _, friend in ipairs(self.friends) do
					if not self:getSameEquip(card, friend) and friend:objectName() ~= who:objectName()
						and self:hasSkills(sgs.need_equip_skill .. "|" .. sgs.lose_equip_skill, friend) then
							target = friend
							break
					end
				end
				for _, friend in ipairs(self.friends) do
					if not self:getSameEquip(card, friend) and friend:objectName() ~= who:objectName() then
						target = friend
						break
					end
				end
			end
		end
	else
		local judges = who:getJudgingArea()
		if who:containsTrick("YanxiaoCard") then
			for _, judge in sgs.qlist(judges) do
				if judge:isKindOf("YanxiaoCard") then
					card = sgs.Sanguosha:getCard(judge:getEffectiveId())
					for _, friend in ipairs(self.friends) do
						if not friend:containsTrick(judge:objectName()) and not self.room:isProhibited(self.player, friend, judge)
							and not friend:getJudgingArea():isEmpty() then
							target = friend
							break
						end
					end
					if target then break end
					for _, friend in ipairs(self.friends) do
						if not friend:containsTrick(judge:objectName()) and not self.room:isProhibited(self.player, friend, judge) then
							target = friend
							break
						end
					end
					if target then break end
				end
			end
		end
		if card==nil or target==nil then
			if not who:hasEquip() or self:hasSkills(sgs.lose_equip_skill, who) then return nil end
			local card_id = self:askForCardChosen(who, "e", "snatch")
			if card_id >= 0 and who:hasEquip(sgs.Sanguosha:getCard(card_id)) then card = sgs.Sanguosha:getCard(card_id) end

			if card then
				if card:isKindOf("Armor") or card:isKindOf("DefensiveHorse") then
					self:sort(self.friends, "defense")
				else
					self:sort(self.friends, "handcard")
					self.friends = sgs.reverse(self.friends)
				end

				for _, friend in ipairs(self.friends) do
					if not self:getSameEquip(card, friend) and friend:objectName() ~= who:objectName() and self:hasSkills(sgs.lose_equip_skill .. "|shensu" , friend) then
						target = friend
						break
					end
				end
				for _, friend in ipairs(self.friends) do
					if not self:getSameEquip(card, friend) and friend:objectName() ~= who:objectName() then
						target = friend
						break
					end
				end
			end
		end
	end

	if return_prompt == "card" then return card
	elseif return_prompt == "target" then return target
	else
		return (card and target)
	end
end

sgs.ai_skill_discard.jiewei = function(self, discard_num, min_num, optional, include_equip)
	local to_discard = {}
	local cards = self.player:getHandcards()
	cards = sgs.QList2Table(cards)
	self:sortByUseValue(cards, true)
	local stealer
	for _, ap in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if ap:hasSkills("tuxi|nostuxi") and self:isEnemy(ap) then stealer = ap end
	end
	local card
	for i=1, #cards, 1 do
		local isPeach = cards[i]:isKindOf("Peach")
		if isPeach then
			if stealer and self:isEnemy(stealer) and self.player:getHandcardNum()<=2 and self.player:getHp() > 2
			and (not stealer:containsTrick("supply_shortage") or stealer:containsTrick("YanxiaoCard")) then
				card = cards[i]
				break
			end
			local to_discard_peach = true
			for _,fd in ipairs(self.friends) do
				if fd:getHp()<=2 and not fd:hasSkill("niepan") then
					to_discard_peach = false
				end
			end
			if to_discard_peach then
				card = cards[i]
				break
			end
		else
			card = cards[i]
			break
		end
	end
	if card == nil then return {} end
	table.insert(to_discard, card:getEffectiveId())
	current_phase = self.player:getMark("jieweiPhase")
	if current_phase == sgs.Player_Judge and not self.player:isSkipped(sgs.Player_Judge) then
		if not self.player:containsTrick("YanxiaoCard") then
			if (self.player:containsTrick("lightning") and not self:hasWizard(self.friends) and self:hasWizard(self.enemies))
				or (self.player:containsTrick("lightning") and #self.friends > #self.enemies) then
				return to_discard
			elseif self.player:containsTrick("supply_shortage") then
				if self.player:getHp() > self.player:getHandcardNum() then return to_discard end
				local cardstr = sgs.ai_skill_use["@@nostuxi"](self, "@nostuxi")
				if cardstr:match("->") then
					local targetstr = cardstr:split("->")[2]
					local targets = targetstr:split("+")
					if #targets == 2 then
						return to_discard
					end
				end
			elseif self.player:containsTrick("indulgence") then
				if self.player:getHandcardNum() > 3 or self.player:getHandcardNum() > self.player:getHp() - 1 then return to_discard end
				for _, friend in ipairs(self.friends_noself) do
					if not friend:containsTrick("YanxiaoCard") and (friend:containsTrick("indulgence") or friend:containsTrick("supply_shortage")) then
						return to_discard
					end
				end
			end
		end
	elseif current_phase == sgs.Player_Draw and not self.player:isSkipped(sgs.Player_Draw) and not self.player:hasSkills("tuxi|nostuxi") then
		local cardstr = sgs.ai_skill_use["@@nostuxi"](self, "@nostuxi")
		if cardstr:match("->") then
			local targetstr = cardstr:split("->")[2]
			local targets = targetstr:split("+")
			if #targets == 2 then
				return to_discard
			end
		end
		return {}
	elseif current_phase == sgs.Player_Play and not self.player:isSkipped(sgs.Player_Play) then
		self:sortByKeepValue(cards)
		table.remove(to_discard)
		table.insert(to_discard, cards[1]:getEffectiveId())

		self:sort(self.enemies, "defense")
		self:sort(self.friends, "defense")
		self:sort(self.friends_noself, "defense")

		for _, friend in ipairs(self.friends) do
			if not friend:getCards("j"):isEmpty() and not friend:containsTrick("YanxiaoCard") and card_for_jiewei(self, friend, ".") then
				return to_discard
			end
		end

		for _, enemy in ipairs(self.enemies) do
			if not enemy:getCards("j"):isEmpty() and enemy:containsTrick("YanxiaoCard") and card_for_jiewei(self, enemy, ".") then
				return to_discard
			end
		end

		for _, friend in ipairs(self.friends_noself) do
			if not friend:getCards("e"):isEmpty() and self:hasSkills(sgs.lose_equip_skill, friend) and card_for_jiewei(self, friend, ".") then
				return to_discard
			end
		end

		local top_value = 0
		for _, hcard in ipairs(cards) do
			if not hcard:isKindOf("Jink") then
				if self:getUseValue(hcard) > top_value then top_value = self:getUseValue(hcard) end
			end
		end
		if top_value >= 3.7 and #(self:getTurnUse())>0 then return {} end

		local targets = {}
		for _, enemy in ipairs(self.enemies) do
			if not self:hasSkills(sgs.lose_equip_skill, enemy) and card_for_jiewei(self, enemy, ".") then
				table.insert(targets, enemy)
			end
		end

		if #targets > 0 then
			self:sort(targets, "defense")
			return to_discard
		end
	elseif current_phase == sgs.Player_Discard and not self.player:isSkipped(sgs.Player_Discard) then
		self:sortByKeepValue(cards)
		if self:needBear() then return {} end
		if self.player:getHandcardNum() > self:getOverflow(self.player, true) then
			return { cards[1]:getEffectiveId() }
		end
	end

	return {}
end

sgs.ai_skill_cardchosen.jiewei = function(self, who, flags)
	if flags == "ej" then
		return card_for_jiewei(self, who, "card")
	end
end

sgs.ai_skill_playerchosen.jiewei = function(self, targets)
	local who = self.room:getTag("jieweiTarget"):toPlayer()
	if who then
		if not card_for_jiewei(self, who, "target") then self.room:writeToConsole("NULL") end
		return card_for_jiewei(self, who, "target")
	end
end

sgs.ai_skill_use["@@jiewei"] = function(self, prompt)
	self:updatePlayers()
	local cards = self.player:getHandcards()
	cards = sgs.QList2Table(cards)
	self:sortByUseValue(cards, true)
	local card = cards[1]

		-- if self.player:getHandcardNum()-2 > self.player:getHp() then return "." end

		self:sort(self.enemies, "defense")
		for _, friend in ipairs(self.friends) do
			if not friend:getCards("j"):isEmpty() and not friend:containsTrick("YanxiaoCard") and card_for_jiewei(self, friend, ".") then
				-- return "@jieweiCard=" .. card:getEffectiveId() .."->".. friend:objectName()
				return "@jieweiCard=.->".. friend:objectName()
			end
		end

		for _, enemy in ipairs(self.enemies) do
			if not enemy:getCards("j"):isEmpty() and enemy:containsTrick("YanxiaoCard") and card_for_jiewei(self, enemy, ".") then
				-- return "@jieweiCard=" .. card:getEffectiveId() .."->".. friend:objectName()
				return "@jieweiCard=.->".. enemy:objectName()
			end
		end

		for _, friend in ipairs(self.friends_noself) do
			if not friend:getCards("e"):isEmpty() and self:hasSkills(sgs.lose_equip_skill, friend) and card_for_jiewei(self, friend, ".") then
				return "@jieweiCard=.->".. friend:objectName()
			end
		end

		local top_value = 0
		for _, hcard in ipairs(cards) do
			if not hcard:isKindOf("Jink") then
				if self:getUseValue(hcard) > top_value then top_value = self:getUseValue(hcard) end
			end
		end
		if top_value >= 3.7 and #(self:getTurnUse())>0 then return "." end

		local targets = {}
		for _, enemy in ipairs(self.enemies) do
			if card_for_jiewei(self, enemy, ".") then
				table.insert(targets, enemy:objectName())
			end
		end

		if #targets > 0 then
			self:sort(targets, "defense")
			-- return "@jieweiCard=" .. card:getEffectiveId() .."->".. targets[#targets]:objectName()
			return ("#ol_jiewei:%d:->%s"):format(card:getEffectiveId(), table.concat(targets,"+"))
		end

	return "."
end

function sgs.ai_cardneed.jiewei(to, card)
	return to:getCards("he"):length() <= 2
end

sgs.ai_skill_use["@ol_jiewei"]=function(self, prompt)
    self:updatePlayers()
		local selectset = {}
		for _,friend in ipairs(self.friends) do
			if #selectset == 0 and friend:getJudgingArea():length() > 0 then
				for _,enemy in ipairs(self.enemies) do
					if #selectset == 0 and enemy:getJudgingArea():isEmpty() then
						table.insert(selectset, friend:objectName())
					end
				end
			end
		end
		for _,enemy in ipairs(self.enemies) do
			if #selectset == 0 and not enemy:getEquips():isEmpty() then
				for _,friend in ipairs(self.friends) do
					if #selectset == 0 and friend:getEquips():isEmpty() then
						table.insert(selectset, enemy:objectName())
					end
				end
			end
		end
		if #selectset > 0 then
			local cards = self.player:getCards("he")
			cards = sgs.QList2Table(cards)
			self:sortByUseValue(cards, true)
		    return ("#ol_jiewei:%d:->%s"):format(cards[1]:getEffectiveId(), table.concat(selectset,"+"))
		else
		    return "."
		end
end
--OL SP 曹仁

 
local weikui_skill = {}
weikui_skill.name = "weikui"
table.insert(sgs.ai_skills, weikui_skill)
weikui_skill.getTurnUseCard = function(self)
    if self.player:hasUsed("#weikui") then return end
    local card_str = "#weikui:.:"
    local weikui = sgs.Card_Parse(card_str)
    assert(weikui)
    return weikui 
end
 
sgs.ai_skill_use_func["#weikui"] = function(card, use, self)
    if self.player:isWounded() and not self:needToLoseHp(self.player, nil, false) then return end
 
    local target
    self:sort(self.enemies, "defense")
    local slash = sgs.Sanguosha:cloneCard("slash")
    for _, enemy in ipairs(self.enemies) do
        if not self.player:canSlash(enemy, slash, false) then
        elseif self:slashProhibit(nil, enemy) then
        elseif enemy:isKongcheng() then
        else 
            target = enemy break
        end
    end 
 
    if not target then return end
 
    if use.to then
        use.to:append(target)
    end
    use.card = card
end
 
sgs.ai_use_priority.weikui = 2.7

sgs.ai_skill_use["@@lizhan"] = function(self, prompt)
    self:updatePlayers(true)
    local targets = {}
    for _, friend in ipairs(self.friends) do
        if friend:isWounded() and not self:needKongcheng(friend, true) then
            table.insert(targets, friend:objectName())
        end 
    end
    return "#lizhan:.:->"..table.concat(targets, "+")
end
--贺齐

 
local shanxi_skill = {
    name = "shanxi", 
    getTurnUseCard = function(self, inclusive)
        if self.player:hasUsed("#shanxi") then return end
        local can_use = false
        for _,p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
            if p:isNude() then continue end
            if self.player:inMyAttackRange(p) and not self:doNotDiscard(p) then 
                can_use = true
                break
            end
        end
        if can_use then
            return sgs.Card_Parse("#shanxi:.:")
        end
    end,
}
table.insert(sgs.ai_skills, shanxi_skill)
sgs.ai_skill_use_func["#shanxi"] = function(card, use, self)
    local handcards = self.player:getHandcards()
    local slashs = {}
    for _,c in sgs.qlist(handcards) do
        if c:isKindOf("BasicCard") and c:isRed() and not c:isKindOf("Peach") then
            if self.player:canDiscard(self.player, c:getEffectiveId()) then
                table.insert(slashs, c)
            end
        end
    end
    if #slashs == 0 then return end
    self:sortByUseValue(slashs, true)
     
    local targets, friends, enemies = {}, {}, {}
    for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
        if self.player:canSlash(player) then 
            table.insert(targets, player)           
            if self:isFriend(player) then
                table.insert(friends, player)
            elseif self:isEnemy(player) and not self:doNotDiscard(player) then
                table.insert(enemies, player)
            end
        end
    end
     
    if #targets == 0 then return end    
    local target
     
    self:sort(enemies, "defense")
    for _, enemy in ipairs(enemies) do
        if self:getDangerousCard(enemy) then
            target = enemy
            break
        end
    end
 
    if not target then
        for _, friend in ipairs(friends) do
            if self:needToThrowArmor(friend) then
                target = friend
                break
            end
        end
    end
    if not target then
        for _, friend in ipairs(friends) do
            if friend:hasSkill("kongcheng") and friend:getHandcardNum() == 1 and self:getEnemyNumBySeat(self.player, friend) > 0
              and friend:getHp() <= 2 then
                target = friend
                break
            end
        end
    end
     
    if not target and #enemies == 0 then return end
    if not target then
        for _, enemy in ipairs(enemies) do
            if self:getValuableCard(enemy) and not self:doNotDiscard(enemy, "e") then
                target = enemy
                break
            end
        end
    end
    if not target then
        for _, enemy in ipairs(enemies) do
            local cards = sgs.QList2Table(enemy:getHandcards())
            local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
            if #cards <= 2 and not self:doNotDiscard(enemy, "h") then
                for _, cc in ipairs(cards) do
                    if (cc:hasFlag("visible") or cc:hasFlag(flag)) and (cc:isKindOf("Peach") or cc:isKindOf("Analeptic")) then
                        target = enemy
                        break
                    end
                end
            end
        end
    end
    if not target then
        for _, enemy in ipairs(enemies) do
            if self:hasSkills(sgs.concentrated_skill,enemy) and not self:doNotDiscard(enemy, "e") then
                local equips = { enemy:getDefensiveHorse(), enemy:getArmor(), enemy:getOffensiveHorse(), enemy:getWeapon() }
                if enemy:getDefensiveHorse() then
                    target = enemy
                    break
                end
                if not target then
                    if enemy:getArmor() and not self:needToThrowArmor(enemy) then
                        target = enemy
                        break   
                    end
                end
                if not target then
                    for _ , equip in ipairs(equips) do
                        if equip and equip:isRed() and enemy:hasSkill("jijiu") then 
                            target = enemy
                            break
                        end
                    end
                end
            end
        end
    end
     
    if not target and self:getOverflow() > 0 then
        for _, enemy in ipairs(enemies) do
            if not self:doNotDiscard(enemy, "e") then
                target = enemy
                break
            end
        end 
        if not target then
            for _, enemy in ipairs(enemies) do
                if not enemy:isKongcheng() and not self:doNotDiscard(enemy, "h") then
                    target = enemy
                    break
                end
            end
        end
        if not target then
            for _, enemy in ipairs(enemies) do
                if not self:doNotDiscard(enemy) then
                    target = enemy
                    break
                end
            end
        end
    end
 
    if not target then return end
 
    local card_str = string.format("#shanxi:%s:", slashs[1]:getEffectiveId())
    local acard = sgs.Card_Parse(card_str)
    use.card = acard
    if use.to then use.to:append(target) end
end
--孙乾
sgs.ai_skill_playerchosen.shuimeng = function(self, targets)
	if target == nil then
		self:sort(self.enemies, "handcard")
		local players = sgs.SPlayerList()
		for _, enemy in ipairs(self.enemies) do
			if not enemy:isKongcheng() then
				players:append(enemy)
			end
		end
		target = self:findPlayerToDiscard("h", false, true, players, false)
	end
	return target
end
sgs.ai_skill_askforyiji.qianya = function(self, card_ids)
	return sgs.ai_skill_askforyiji.nosyiji(self, card_ids)
end
--王朗
local gushe_skill={}
gushe_skill.name="gushe"
table.insert(sgs.ai_skills,gushe_skill)
gushe_skill.getTurnUseCard=function(self,inclusive)
	self:sort(self.enemies, "handcard")
	if self.player:getHandcardNum() < 2 or self.player:hasUsed("#gushe") then return false end
	for _, enemy in ipairs(self.enemies) do
	    if not enemy:isKongcheng() then
			return sgs.Card_Parse("#gushe:.:")
		end
	end
end

sgs.ai_skill_use_func["#gushe"]=function(card, use, self)
    local cards = sgs.QList2Table(self.player:getCards("h"))
	self:sortByUseValue(cards,true)
	self:sort(self.enemies, "handcard")
	for _, enemy in ipairs(self.enemies) do
	    if not enemy:isKongcheng() then
			if use.to and use.to:length() < 3 then use.to:append(enemy) end
		end
	end
	for _, card in ipairs(cards) do
		if not card:isKindOf("Peach") and not card:isKindOf("ExNihilo") and not card:isKindOf("Jink") then
			use.card = sgs.Card_Parse("#gushe:"..card:getId()..":")
		end
	end
	if use.to and use.to:length() > 0 then return end
end

sgs.ai_use_value["gushe"] = sgs.ai_use_value.ExNihilo - 0.1
sgs.ai_use_priority["gushe"] = sgs.ai_use_priority.ExNihilo - 0.1

sgs.ai_skill_invoke.jici = function(self,data)
	return true
end

sgs.ai_skill_invoke.fuhuo = function(self,data)
	return true
end
--戏志才
sgs.ai_skill_playerchosen.xianfu = function(self, targets)
	if self.player:getRole() == "loyalist" then
		return self.room:getLord()
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("shibei") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("jijiu") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("huituo") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("nosrende") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("super_rende") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("rende") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("jieyin") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("yuce") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("yongsi") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("lijian") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("haoshi") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("shibei") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("jijiu") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("huituo") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("nosrende") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("super_rende") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("rende") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("jieyin") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("yuce") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("yongsi") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("lijian") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("haoshi") then return p end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if self.player:getRole() == "lord" and p:getRole() == "loyalist" then
			return p
		end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if self.player:getRole() == p:getRole() then
			return p
		end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		return p
	end
end

sgs.ai_skill_playerchosen.chouce = function(self, targets)
	if self.room:getTag("chouce"):toBool() then
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if p:getMark("@fu") > 0 and self:isFriend(p) then
				return p
			end
		end
		if self:isWeak(self.player) then return self.player end
		self:sort(self.friends_noself, "handcard")
		for _, friend in ipairs(self.friends_noself) do
			if friend:hasSkills(sgs.cardneed_skill) and not self:needKongcheng(friend) and not friend:hasSkill("manjuan") then
				return friend
			end
		end
		return self.player
	else
		for _, enemy in ipairs(self.enemies) do
			if (not self:doNotDiscard(enemy) or self:getDangerousCard(enemy) or self:getValuableCard(enemy)) and not enemy:isNude() then
				return enemy
			end
		end
		for _, friend in ipairs(self.friends) do
			if(self:hasSkills(sgs.lose_equip_skill, friend) and not friend:getEquips():isEmpty())
			or (self:needToThrowArmor(friend) and friend:getArmor()) or self:doNotDiscard(friend) then
				return friend
			end
		end
		return self.player
	end
end
--皇甫嵩
local fenyue_skill={}
fenyue_skill.name="fenyue"
table.insert(sgs.ai_skills,fenyue_skill)
fenyue_skill.getTurnUseCard=function(self,inclusive)
	if self.player:getHandcardNum() < 2 or self.player:hasUsed("#fenyue") then return false end
	return sgs.Card_Parse("#fenyue:.:")
end

sgs.ai_skill_use_func["#fenyue"]=function(card, use, self)
    local cards = sgs.QList2Table(self.player:getCards("h"))
	self:sortByUseValue(cards,true)
	self:sort(self.enemies, "handcard")
	for _, enemy in ipairs(self.enemies) do
	    if not enemy:isKongcheng() then
			for _, card in ipairs(cards) do
				if not card:isKindOf("Peach") and not card:isKindOf("ExNihilo") and not card:isKindOf("Jink") then
					use.card = sgs.Card_Parse("#fenyue:"..card:getId()..":")
					if use.to then use.to:append(enemy) end
					return
				end
			end
		end
	end
end
sgs.ai_skill_choice["fenyue"] = function(self, choices, data)
	local items = choices:split("+")
    if #items == 1 then
        return items[1]
	else
		return "fenyue2"
    end
    return "fenyue1"
end

sgs.ai_use_value["fenyue"] = sgs.ai_use_value.Slash + 1
sgs.ai_use_priority["fenyue"] = sgs.ai_use_priority.Slash + 1
--唐咨
sgs.ai_skill_invoke.xingzhao = function(self,data)
	local current = self.room:getCurrent()
	if data:toCardUse() then
		current = data:toCardUse().from
		if ((self:isFriend(current) and not self:needKongcheng(current)) or (self:isEnemy(current) and self:needKongcheng(current))) then return true end
		return false
	end
	return self:isFriend(current)
end
--OL于禁
sgs.ai_skill_playerchosen.zhenjun = function(self, targets) --yun
    self:updatePlayers()
    targets = sgs.QList2Table(targets)
     
    if #targets == 0 then return end
    self:sort(targets, "defense")
    targets = sgs.reverse(targets)
    sgs.zhenjunChoice = true
    for _, target in ipairs(targets) do
        if self:isFriend(target) and self:needToThrowCard(target) then return target end
    end
    targets = sgs.reverse(targets)
    for _, target in ipairs(targets) do
        if self:isEnemy(target) and not self:doNotDiscard(target, "e") then 
            sgs.zhenjunChoice = false 
            if sgs.zhenjun and sgs.zhenjun > self.player:getCardCount(true) then sgs.zhenjunChoice = true end
            return target 
        end
    end
 
    for _, target in ipairs(targets) do
        if self:isEnemy(target) and math.max(target:getHp(), 0) >= self:getLeastHandcardNum(target) then return target end
    end
    if self:PayContains(targets, self.player) then return self.player end
end
 
sgs.ai_skill_invoke.zhenjun = sgs.zhenjunChoice
 
sgs.ai_skill_discard.zhenjun = function(self, discard_num, min_num, optional, include_equip)
    if sgs.zhenjun == 0 then return {} end
    if sgs.zhenjun and sgs.zhenjun > self.player:getCardCount(true) then return {} end
    return self:askForDiscard("dummyreason", sgs.zhenjun, sgs.zhenjun, false, true)
end
 --OL关银屏
sgs.ai_cardneed.ol_xueji = function(to, card)
    return to:getHandcardNum() < 3 and card:isRed()
end
 
local function can_be_selected_as_target_olxueji(self, who)
    if not self:damageIsEffective(who, sgs.DamageStruct_Fire) then return false end
    if self:isEnemy(who) and not self:cantbeHurt(who) and not self:getDamagedEffects(who) and not self:needToLoseHp(who) then
        return true
    elseif self:isFriend(who) then
        if who:hasSkills("yiji|nosyiji") and not self.player:hasSkill("jueqing") then
            local huatuo = self.room:findPlayerBySkillName("jijiu")
            if (huatuo and self:isFriend(huatuo) and huatuo:getHandcardNum() >= 3 and huatuo ~= self.player)
                or who:getHp() >= 3 then
                return true
            end
        end
        if who:hasSkill("hunzi") and who:getMark("hunzi") == 0
          and who:objectName() == self.player:getNextAlive():objectName() and who:getHp() == 2 then
            return true
        end
        if self:getDamagedEffects(who) or self:needToLoseHp(who) then return true end
        return false
    end
    return false
end
 
local ol_xueji_skill = {
    name = "ol_xueji", 
    getTurnUseCard = function(self, inclusive)
        if self.player:hasUsed("#ol_xueji") then return end
        return sgs.Card_Parse("#ol_xueji:.:")
    end,
}
table.insert(sgs.ai_skills, ol_xueji_skill)
sgs.ai_skill_use_func["#ol_xueji"] = function(card, use, self)
    local cards = self.player:getCards("he")
    local slashs = {}
    for _,c in sgs.qlist(cards) do
        if c:isRed() and not c:isKindOf("Peach") then
            if self.player:canDiscard(self.player, c:getEffectiveId()) then
                table.insert(slashs, c)
            end
        end
    end
    if #slashs == 0 then return end
    local to_use = false
    for _, enemy in ipairs(self.enemies) do
        if can_be_selected_as_target_olxueji(self, enemy) then
            to_use = true
            break
        end
    end
    if not to_use then
        for _, friend in ipairs(self.friends) do
            if can_be_selected_as_target_olxueji(self, friend) then
                to_use = true
                break
            end
        end
    end
    if not to_use then return end
     
    self:sortByUseValue(slashs, true)
    local n = math.max(self.player:getLostHp(), 1)
     
    self:sort(self.enemies, "defense")
     
    use.card = sgs.Card_Parse("#ol_xueji:"..slashs[1]:getId()..":")
 
    for _, enemy in ipairs(self.enemies) do
        if can_be_selected_as_target_olxueji(self, enemy) then
            if use.to and use.to:length() < n then use.to:append(enemy) end
        end
    end
    for _, friend in ipairs(self.friends) do
         if can_be_selected_as_target_olxueji(self, friend) then
            if use.to and use.to:length() < n then use.to:append(friend) end
        end
    end
end
 
sgs.ai_card_intention.ol_xueji = function(self, card, from, tos)
    for _,to in ipairs(tos) do
        local intention = 60
        if self:getDamagedEffects(to) or self:needToLoseHp(to) then
            intention = 0
        end
        if to:hasSkill("hunzi") and to:getMark("hunzi") == 0 then
            if to:objectName() == from:getNextAlive():objectName() and to:getHp() == 2 then
                intention = -20
            end
        end
        sgs.updateIntention(from, to, intention)
    end
end
 
sgs.ai_use_value.ol_xuejiCard = 3
sgs.ai_use_priority.ol_xuejiCard = 2.35

--推锋
sgs.ai_skill_discard.tuifeng = function(self)
	local to_discard = {}
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	self:sortByKeepValue(cards)

	table.insert(to_discard, cards[1]:getEffectiveId())

	return to_discard
end
sgs.ai_skill_discard.xiaolian = function(self)
	local to_discard = {}
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	self:sortByKeepValue(cards)

	table.insert(to_discard, cards[1]:getEffectiveId())

	return to_discard
end

sgs.ai_skill_discard["lucky"] = function(self, discard_num, min_num, optional, include_equip)
	return self:askForDiscard("gamerule", discard_num, min_num, optional, include_equip)
end

 --弘德
sgs.ai_skill_playerchosen.hongde = function(self, targets)
    return self:findPlayerToDraw(false, 1)
end
 
sgs.ai_playerchosen_intention.hongde = function(self, from, to)
    if self:needKongcheng(to, true) then sgs.updateIntention(from, to, 0) end
    sgs.updateIntention(from, to, -50)
end
 
--定叛
local dingpan_skill = {}
dingpan_skill.name = "dingpan"
table.insert(sgs.ai_skills, dingpan_skill)
dingpan_skill.getTurnUseCard = function(self)
    if self.player:usedTimes("#dingpan") >= self.player:getMark("dingpan") then return end
    local card_str = "#dingpan:.:"
    local dingpan = sgs.Card_Parse(card_str)
    assert(dingpan)
    return dingpan 
end
 
sgs.ai_skill_use_func["#dingpan"] = function(card, use, self)
    local targets, friends, enemies = {}, {}, {}
    for _, player in sgs.qlist(self.room:getPlayers()) do
        if not player:getEquips():isEmpty() then 
            table.insert(targets, player)           
            if self:isFriend(player) then
                table.insert(friends, player)
            elseif self:isEnemy(player) and not self:doNotDiscard(player, "e") 
                and not player:hasArmorEffect("silverLion") and not player:hasSkill("yqijiang") 
                and not self:getDamagedEffects(player) and not self:needToLoseHp(player) then
                table.insert(enemies, player)
            end
        end
    end
     
    if #targets == 0 then return end    
    local target
    if self.player:hasArmorEffect("silverLion") or (self.player:hasSkill("yqijiang") and self.player:hasEquip()) then target = self.player end
     
    if not target then
        for _, friend in ipairs(friends) do
            if self:needToThrowArmor(friend) or friend:hasArmorEffect("silverLion") or friend:hasSkill("yqijiang") then
                target = friend
                break
            end
        end
    end
     
    if not target then
        for _, enemy in ipairs(enemies) do
            if enemy:containsTrick("indulgence") then
                target = enemy
                break
            end
        end
    end
     
    if not target then
        for _, enemy in ipairs(enemies) do
            if self:getDangerousCard(enemy) then
                target = enemy
                break
            end
        end
    end
 
    if not target then
        for _, enemy in ipairs(enemies) do
            if self:getValuableCard(enemy) then
                target = enemy
                break
            end
        end
    end
     
    if not target then
        for _, enemy in ipairs(enemies) do
            if self:hasSkills(sgs.concentrated_skill,enemy) and not self:doNotDiscard(enemy, "e") then
                if enemy:getDefensiveHorse() then
                    target = enemy
                    break
                end
                if not target then
                    if enemy:getArmor() and not self:needToThrowArmor(enemy) then
                        target = enemy
                        break   
                    end
                end
            end
        end
    end
     
    if not target then
        for _, friend in ipairs(friends) do
            if self:isWeak(friend) and (friend:getWeapon() or friend:getOffensiveHorse()) then
                target = friend
                break
            end
        end
    end
 
    if not target then return end
 
    if use.to then
        use.to:append(target)
    end
     
    use.card = card
end
 
sgs.ai_skill_choice.dingpan = function(self, choices) 
    if self.player:hasArmorEffect("silverLion") or self.player:hasSkill("yqijiang") 
        or self:getDamagedEffects() or self:needToLoseHp() then 
        return "dingpan2" 
    end
    return "dingpan1"
end

 --卞夫人
sgs.ai_skill_invoke.yuejian = function(self)
    local current = self.room:getCurrent()
    if current then
        if not self:isFriend(current) then return false end
        if self:isFriend(current) and self:needBear(current) then return true end
    end
    return false
end
 --马忠
local fuman_skill = {
    name = "fuman", 
    getTurnUseCard = function(self, inclusive)
        return sgs.Card_Parse("#fuman:.:")
    end,
}
table.insert(sgs.ai_skills, fuman_skill)
sgs.ai_skill_use_func["#fuman"] = function(card, use, self)
    local handcards = self.player:getHandcards()
    local slashs = {}
    for _,c in sgs.qlist(handcards) do
        if c:isKindOf("Slash") then
            if self.player:canDiscard(self.player, c:getEffectiveId()) then
                table.insert(slashs, c)
            end
        end
    end
    if #slashs == 0 then return end
    self:sortByUseValue(slashs, true)
     
    local targets = {}
    for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
        if self:isFriend(player) and player:getMark("fuman_Play") == 0 and not self:needKongcheng(player, true)
            and not self:willSkipPlayPhase(player) and not player:hasSkill("manjuan") then 
            table.insert(targets, player)           
        end
    end
     
    if #targets == 0 then return end    
    self:sort(targets, "handcard")
    local card_str = string.format("#fuman:%s:", slashs[1]:getEffectiveId())
    local acard = sgs.Card_Parse(card_str)
    use.card = acard
    if use.to then use.to:append(targets[1]) end
end
 
sgs.ai_card_intention.fuman = function(self,card, from, tos)
    local to = tos[1]
    local intention = -70
    if hasManjuanEffect(to) then
        intention = 0
    elseif self:needKongcheng(to, true) then
        intention = 0
    end
    sgs.updateIntention(from, to, intention)
end
 --OL周仓
sgs.ai_skill_playerchosen.ol_zhongyong = function(self, targets)
    self:updatePlayers()
    targets = sgs.QList2Table(targets)
    self:sort(targets, "defense")
    for _, p in ipairs(targets) do
        if self:isFriend(p) and not hasManjuanEffect(p) then return p end
    end
end
 
sgs.ai_skill_askforag.ol_zhongyong = function(self, card_ids)
    if #self.friends_noself < 1 then return -1 end
    if #card_ids == 1 then return card_ids[1] end
    local cards = {}
    for _, card_id in ipairs(card_ids) do
        table.insert(cards, sgs.Sanguosha:getCard(card_id))
    end
    for _, card in ipairs(cards) do
        if card:isKindOf("Slash") and card:isRed() then return card:getEffectiveId() end
    end
    for _, card in ipairs(cards) do
        if card:isKindOf("Jink") then return card:getEffectiveId() end
    end
    if #card_ids > 0 then return self:askForAG(card_ids, false, "dummyreason") end
    return -1
end
 --OL 孙策
sgs.ai_skill_choice.ol_hunshang = function(self, choices)   --hunshang
    return self.hunshang
end
 
sgs.ai_skill_playerchosen.ol_hunshang = function(self, targets)
    local x = self.player:getLostHp()
    local n = x - 1
    self:updatePlayers()
    if x == 1 and #self.friends == 1 then
        for _, enemy in ipairs(self.enemies) do
            if enemy:hasSkill("manjuan") then
                return enemy
            end
        end
        return nil
    end
 
    self.hunshangTarget = nil
 
    if x == 1 then
        self:sort(self.friends_noself, "handcard")
        self.friends_noself = sgs.reverse(self.friends_noself)
        for _, friend in ipairs(self.friends_noself) do
            if (self:needToThrowCard(friend, "e") or friend:hasSkill("tuntian")) and not friend:hasSkill("manjuan") then
                return friend
            end
        end
     
        if not self.hunshangTarget then
            for _, enemy in ipairs(self.enemies) do
                if enemy:hasSkill("manjuan") and not self:doNotDiscard(enemy, "he", true) then
                    return enemy
                end
            end
        end
 
        if not self.hunshangTarget then
            for _, friend in ipairs(self.friends_noself) do
                if friend:getCards("he"):length() > 0 and not friend:hasSkill("manjuan") then
                    return friend
                end
            end
        end
 
        if not self.hunshangTarget then
            for _, friend in ipairs(self.friends_noself) do
                if not friend:hasSkill("manjuan") then
                    return friend
                end
            end
        end
        return nil
    elseif #self.friends > 1 then
        self:sort(self.friends_noself, "defense")
        for _, friend in ipairs(self.friends_noself) do
            if (self:needToThrowCard(friend, "e") or friend:hasSkill("tuntian")) and not friend:hasSkill("manjuan") then
                self.hunshangTarget = friend
                break
            end
        end
         
        if not self.hunshangTarget and #self.enemies > 0 then
            local wf
            if self.player:isLord() then
                if self:isWeak() and (self.player:getHp() < 2 and self:getCardsNum("Peach") < 1) then
                    wf = true
                end
            end
            if not wf then
                for _, friend in ipairs(self.friends_noself) do
                    if self:isWeak(friend) then
                        wf = true
                        break
                    end
                end
            end
 
            if not wf then
                self:sort(self.enemies, "defense")
                for _, enemy in ipairs(self.enemies) do
                    if enemy:getCards("he"):length() == n
                        and not self:doNotDiscard(enemy, "he", true, n) then
                        self.hunshang = "yinghun1"
                        return enemy
                    end
                end
                for _, enemy in ipairs(self.enemies) do
                    if enemy:getCards("he"):length() >= n
                        and not self:doNotDiscard(enemy, "he", true, n)
                        and self:hasSkills(sgs.cardneed_skill, enemy) then
                        self.hunshang = "yinghun1"
                        return enemy
                    end
                end
            end
        end
 
        if not self.hunshangTarget then
            self.hunshangTarget = self:findPlayerToDraw(false, n)
        end
 
        if self.hunshangTarget then self.hunshang = "yinghun2" end
    end
    if not self.hunshangTarget and x > 1 and #self.enemies > 0 then
        self:sort(self.enemies, "handcard")
        for _, enemy in ipairs(self.enemies) do
            if enemy:getCards("he"):length() >= n
                and not self:doNotDiscard(enemy, "nil", true, n) then
                self.hunshang = "yinghun1"
                return enemy
            end
        end
        self.enemies = sgs.reverse(self.enemies)
        for _, enemy in ipairs(self.enemies) do
            if not enemy:isNude()
                and not (self:hasSkills(sgs.lose_equip_skill, enemy) and enemy:getCards("e"):length() > 0)
                and not self:needToThrowArmor(enemy)
                and not enemy:hasSkills("tuntian+zaoxian") then
                self.hunshang = "yinghun1"
                return enemy
            end
        end
        for _, enemy in ipairs(self.enemies) do
            if not enemy:isNude()
                and not (self:hasSkills(sgs.lose_equip_skill, enemy) and enemy:getCards("e"):length() > 0)
                and not self:needToThrowArmor(enemy)
                and not (enemy:hasSkills("tuntian+zaoxian") and x < 3 and enemy:getCards("he"):length() < 2) then
                self.hunshang = "yinghun1"
                return enemy
            end
        end
    end
 
    return self.hunshangTarget
end
--刘琦
sgs.ai_skill_invoke.wenji = function(self)
    local current = self.room:getCurrent()
    if current then
        if self:isEnemy(current) then return false end
    end
    return true
end
 
sgs.ai_skill_cardask["@wenji"] = function(self, data) 
    local liuqi = self.room:findPlayerBySkillName("wenji")
    if not self:isFriend(liuqi) then return "." end
    if self:isWeak() and not self:needToThrowCard(self.player, "h") then return "." end
    if liuqi:getHandcardNum() >= self.player:getHandcardNum() and not self:needToThrowCard(self.player, "h") then return "." end
    local to_discard = self:askForDiscard("dummyreason", 1, 1, false, true)
    if #to_discard > 0 then return "$" .. to_discard[1] else return "." end
end
 
sgs.ai_skill_playerchosen.tunjiang = function(self, targets)
    return self:findPlayerToDraw(true, 2)
end
--糜竺
local ziyuan_skill = {
    name = "ziyuan", 
    getTurnUseCard = function(self, inclusive)
        return sgs.Card_Parse("#ziyuan:.:")
    end,
}
table.insert(sgs.ai_skills, ziyuan_skill)
sgs.ai_skill_use_func["#ziyuan"] = function(card, use, self)
    if self.player:hasUsed("#ziyuan") then return end
    if self.player:isKongcheng() then return end
    local targets = {}
    for _, friend in ipairs(self.friends_noself) do
        if not friend:hasSkill("manjuan") and not self:needKongcheng(friend, true) then
            table.insert(targets, friend)
        end
    end
    if #targets < 1 then return end
    local compare_func = function(a, b)
        if a:isWounded() ~= b:isWounded() then
            return a:isWounded()
        elseif a:isWounded() then
            return a:getHp() < b:getHp()
        else
            return a:getHandcardNum() < b:getHandcardNum()
        end
    end
    table.sort(targets, compare_func)
     
    local cards = self.player:getHandcards()
    cards = sgs.QList2Table(cards)
    self:sortByKeepValue(cards)
     
    for _,card1 in ipairs(cards) do
        if card1:getNumber() == 13 then 
            local card_str = string.format("#ziyuan:%s:", card1:getEffectiveId())
            local acard = sgs.Card_Parse(card_str)
            use.card = acard
            if use.to then use.to:append(targets[1]) end
            return 
        end
         
        for __,card2 in ipairs(cards) do
            if card1:getId() == card2:getId() then
            elseif card1:getNumber() + card2:getNumber() > 13 then
            elseif card1:getNumber() + card2:getNumber() == 13 then
                local card_str = string.format("#ziyuan:%s:", card1:getEffectiveId().. "+" .. card2:getEffectiveId())
                local acard = sgs.Card_Parse(card_str)
                use.card = acard
                if use.to then use.to:append(targets[1]) end
                return
            else
                for __,card3 in ipairs(cards) do
                    if card1:getId() == card3:getId() or card2:getId() == card3:getId() then
                    elseif card1:getNumber() + card2:getNumber() + card3:getNumber() == 13 then
                        local card_str = string.format("#ziyuan:%s:", card1:getEffectiveId().. "+" .. card2:getEffectiveId().. "+" .. card3:getEffectiveId())
                        local acard = sgs.Card_Parse(card_str)
                        use.card = acard
                        if use.to then use.to:append(targets[1]) end
                        return
                    end
                end
            end
        end
    end
end
--孙亮
sgs.ai_skill_invoke.wenji = function(self)
	if self:isEnemy(data:toPlayer()) then return false end
    return true
end