module("extensions.pay31",package.seeall)
extension = sgs.Package("pay31")
math.randomseed(tostring(os.time()):reverse():sub(1, 7))
 
seoi = sgs.General(extension,"seoi","luacai",4,false,false,false)
tenkai_tsurubami = sgs.General(extension,"tenkai_tsurubami$","qun",3,true,false,false)
Hypothia = sgs.General(extension,"Hypothia","qun",3,false,false,false)
ex_rumia = sgs.General(extension,"ex_rumia","luaxing",2,false,false,false,5)
torisumi = sgs.General(extension,"torisumi","luacai",4,false,true,false)
Yaezaki_An = sgs.General(extension,"Yaezaki_An","luaxing",1,false,false,false)
magahara = sgs.General(extension,"magahara","god",4,false,false,false) 
fujix = sgs.General(extension,"fujix","qun",4,false,false,false)

lualvzhiu = sgs.CreateTriggerSkill{
	name = "lualvzhiu" ,
	events = {sgs.DamageCaused} ,
	frequency = sgs.Skill_Compulsory,
	global = true,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.DamageCaused then
			local damage = data:toDamage() 
			if damage.chain then return false end
			if damage.to and damage.to:hasSkill("lualvzhiu") and damage.card and damage.card:isKindOf("Slash") then
				damage.damage = 0
				room:setPlayerProperty(damage.to, "maxhp", sgs.QVariant(damage.to:getMaxHp() - 1))
				if damage.to:getHp() <= 0 or damage.to:getMaxHp() <= 0 then room:killPlayer(damage.to) end
				data:setValue(damage)
			end
		end
		return false
	end
}


lualvzhiu2 = sgs.CreateTriggerSkill
{
	name = "#lualvzhiu",
	events = {sgs.CardUsed, sgs.CardResponded},
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardUsed then
			local card = data:toCardUse().card
			if card:isKindOf("BasicCard") and player:hasSkill("lualvzhiu") and player:isWounded() then
				room:recover(player, sgs.RecoverStruct(player, nil, 1))

			end
		elseif event == sgs.CardResponded then
			local cd = data:toCardResponse().m_card
			if cd:isKindOf("BasicCard") and player:hasSkill("lualvzhiu") and player:isWounded() then
				room:recover(player, sgs.RecoverStruct(player, nil, 1))
			end
		end
	end,
}

local function isFriendQ(room, target, player)
	if player:getRole() == "rebel" then
		return target:getRole() == "rebel"
	end
	if player:getRole() == "loyalist" or player:getRole() == "lord" then
		return target:getRole() == "loyalist" or target:getRole() == "lord"
	end
end


luajinjingcard = sgs.CreateSkillCard{
	name = "luajinjing",
	filter = function(self, targets, to_select)
		return to_select:getHp() < sgs.Self:getHp() and #targets == 0
	end,
    on_effect = function(self, effect)
		local room = effect.from:getRoom() 
        local x = effect.from:getHp() - effect.to:getHp() 
        room:loseHp(effect.from, x)
		local playerdata = sgs.QVariant() --ai用
		playerdata:setValue(effect.from)
        local card = room:askForCard(effect.to, ".|.|.|hand", "@luajinjingGive", playerdata, sgs.Card_MethodNone)	
        if card then
            room:obtainCard(effect.from, card, false)
        else
            local choices = {}
            for _, skill in sgs.qlist(effect.to:getVisibleSkillList()) do 
                if not effect.from:hasSkill(skill:objectName()) and string.lower(skill:objectName()) ~= "choosejiang"
					and string.lower(skill:objectName()) ~= "ChooseJiangEight" and string.lower(skill:objectName()) ~= "ChooseJiangChaos" then 
                    table.insert(choices, skill:objectName())
                end 
            end
            if #choices > 0 then
                local choice = room:askForChoice(effect.to, "luajinjing", table.concat(choices, "+"))
                if choice then 
				    if not effect.from:hasSkill(choice) then room:acquireSkill(effect.from, choice) end 
                end 
            end 
        end 
	end 
}

luajinjingVS = sgs.CreateZeroCardViewAsSkill{
	name = "luajinjing",
	view_as = function()
		return luajinjingcard:clone()
	end,
	enabled_at_play = function(self, player)
		return false
	end, 
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luajinjing"
	end
}

luajinjing = sgs.CreateTriggerSkill{
	name = "luajinjing", 
	global = true, 
	view_as_skill = luajinjingVS, 
	events = {sgs.TurnStart},
	on_trigger = function(self, event, player, data, room)
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			if p:hasSkill(self:objectName()) and not p:isWounded() then
				room:askForUseCard(p, "@@luajinjing", "@luajinjing")

			end 
		end  --没做同将模式 2024年9月28日16:55:16
	end
}

luazhongyan = sgs.CreateTriggerSkill{
	name = "luazhongyan", 
	global = true, 
	events = {sgs.TurnStart},
	on_trigger = function(self, event, player, data, room)
		local kp = room:getCurrent()
        local function canLoseHp(playerXX)
            for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
                if hecatiaX and isFriendQ(room, playerXX, hecatiaX) and playerXX:objectName() ~= hecatiaX:objectName()
                        and playerXX:getHp() == hecatiaX:getHp() then
                    room:notifySkillInvoked(hecatiaX, "luayiti")
                    return false
                end 
            end 
            for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
                if Erin and Erin:getKingdom() == playerXX:getKingdom() then
                    room:notifySkillInvoked(Erin, "luajiance")
                    return false
                end 
            end 
            return true
        end 
		if kp:isLord() then 
			for _, seoi in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do 
                local zuiduo = true
                for _, ap in sgs.qlist(room:getOtherPlayers(seoi)) do
                    if seoi:getHp() < ap:getHp() then zuiduo = false end
                end
                if zuiduo and seoi:getHp() < room:getLord():getMark("@clock_time") then
                    room:writeToConsole("Hello World! luazhongyan2")
                    for _, p in sgs.qlist(room:getOtherPlayers(seoi)) do 
                        if canLoseHp(p) then room:loseHp(p) end 
                    end

                    local result = room:askForChoice(seoi, "benghuai", "hp+maxhp")
                    if result == "hp" then
                        if canLoseHp(seoi) then room:loseHp(seoi) end 
                    else
                        room:loseMaxHp(seoi)
                    end
                end 
            end 
		end
		return false
	end
}

seoi:addSkill(lualvzhiu)
seoi:addSkill(lualvzhiu2)
seoi:addSkill(luajinjing)
--seoi:addSkill(luazhongyan)

luaxiangyaoCard = sgs.CreateSkillCard{
	name = "luaxiangyao", 
	will_throw = false,
	filter = function(self, targets, to_select)
		return #targets <= self:getSubcards():length() and to_select:objectName() ~= sgs.Self:objectName()
	end,
	on_use = function(self, room, source, targets) 
		local function canSSSlash(targetX, slash)
			if source:canSlash(targetX, slash, false) and slash:isAvailable(source) then
				return true
			end 
			return false 
		end 
		local function jiaoshui(targetX, Ctargets)
			for _, p in ipairs(Ctargets) do 
				if p:objectName() == targetX:objectName() then
					return true
				end
			end 
			return false 
		end 
		local Atargets = {}
		local Btargets = {}
		local pattern = ".|"
		local huase = "."
		for _,id in sgs.qlist(self:getSubcards()) do
			local c = sgs.Sanguosha:getCard(id)
			if c:getSuit() == sgs.Card_Heart then
				huase = "heart"
			elseif c:getSuit() == sgs.Card_Diamond then
				huase = "diamond"
			elseif c:getSuit() == sgs.Card_Club then
				huase = "club"
			elseif c:getSuit() == sgs.Card_Spade then
				huase = "spade"
			end
			room:setCardFlag(c, "luaxiangyaoUsing")
			if not string.match(pattern, huase) then pattern = pattern .. huase .. "," end 
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
			slash:addSubcard(id)
			slash:setSkillName("luaxiangyao") 
			local trueTargets = sgs.SPlayerList() 
			for _, p in ipairs(targets) do 
				if canSSSlash(p, slash) then
					trueTargets:append(p)
				end 
			end
			if not trueTargets:isEmpty() then
				local target = room:askForPlayerChosen(source, trueTargets, "luaxiangyao", "luaxiangyaoInvoke", true, false)
				if target then 
					table.insert(Atargets, target)
					room:doAnimate(1, source:objectName(), target:objectName())
					room:setPlayerMark(target, "luaxiangyaoBeiSha", target:getMark("luaxiangyaoBeiSha") + 1)
					room:getThread():delay()
				else
					table.insert(Atargets, 0)
				end 
			else
				table.insert(Atargets, 0) 
			end 
			slash:deleteLater()
		end
		pattern = pattern:gsub(",$", "")
		pattern = pattern .. "|.|hand" 
		for _, p in ipairs(targets) do 
			if p:isAlive() then 
				local _data = sgs.QVariant()
				_data:setValue(source)
				p:getRoom():setTag("luaxiangyaoTP", _data)
				local card = room:askForCard(p, pattern, "@luaxiangyaoDiscard", sgs.QVariant(pattern), sgs.Card_MethodDiscard)
				p:getRoom():removeTag("luaxiangyaoTP")
				if card then 
					table.insert(Btargets, p) 
				end  
			end 
		end  
		for _, p in sgs.qlist(room:getAlivePlayers()) do

		end 
		local ii = 1
		for _,id in sgs.qlist(self:getSubcards()) do
			local c = sgs.Sanguosha:getCard(id) 
			local targetU = Atargets[ii]
			if c:hasFlag("luaxiangyaoUsing") then
				if targetU ~= 0 and not jiaoshui(targetU, Btargets) then 
					if targetU:isAlive() then 
						local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
						slash:addSubcard(id)
						slash:setSkillName("luaxiangyao") 
						room:useCard(sgs.CardUseStruct(slash, source, targetU), true)
					end 
				else 
					local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, "luaxiangyao", "luaxiangyao", "")
					room:moveCardTo(c, nil, sgs.Player_DiscardPile, reason, true)
				end 
			end 
			ii = ii + 1
		end 

	end 
}

luaxiangyao = sgs.CreateViewAsSkill{
	name = "luaxiangyao",
	n = 99,
	view_filter = function(self, selected, to_select)
		if #selected == 0 then 
			return to_select:isKindOf("Slash") and to_select:isAvailable(sgs.Self)
		else 
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
			slash:addSubcard(to_select:getEffectiveId())
			slash:setSkillName("luaxiangyao")
			slash:deleteLater()
			return slash:isAvailable(sgs.Self) and #selected < sgs.Self:getHp() 
		end 
	end,
	view_as = function(self, cards)
		if #cards < sgs.Self:getHp() then return end
		if #cards > sgs.Self:getHp() then return end
		local card = luaxiangyaoCard:clone()
		for _, cd in ipairs(cards) do
			card:addSubcard(cd)
		end
		return card
	end ,
	enabled_at_play = function()
		return true
	end , 
	enabled_at_response = function(self, player, pattern)
		return string.startsWith(pattern, "@@luaxiangyao")
	end 
}

luaduanjieCard = sgs.CreateSkillCard{
	name = "luaduanjie" , 
	filter = function(self, targets, to_select)
		return true
	end,
	on_use = function(self, room, source, targets) 
		for _, p in ipairs(targets) do 
			p:drawCards(1)
		end 
		if #targets > 2 then
			if source:hasSkill("luaxiangyao") then 
				room:handleAcquireDetachSkills(source, "-luaxiangyao")
				local function canLoseHp(player)
					for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
						if hecatiaX and isFriendQ(room, player, hecatiaX) and player:objectName() ~= hecatiaX:objectName()
								and player:getHp() == hecatiaX:getHp() then
							room:notifySkillInvoked(hecatiaX, "luayiti")
							return false
						end 
					end 
					for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
						if Erin and Erin:getKingdom() == player:getKingdom() then
							room:notifySkillInvoked(Erin, "luajiance")
							return false
						end 
					end 
					return true
				end 
				for _, p in ipairs(targets) do 
					if canLoseHp(p) then room:loseHp(p) end 
				end 
			end 
		end 
	end
}
luaduanjieVS = sgs.CreateZeroCardViewAsSkill{
	name = "luaduanjie",
	response_pattern = "@@luaduanjie",
	view_as = function(self, cards)
		return luaduanjieCard:clone()
	end
} 

luaduanjie = sgs.CreateTriggerSkill{
	name = "luaduanjie" ,
	global = true ,
	view_as_skill = luaduanjieVS, 
	frequency = sgs.Skill_Compulsory, 
	events = {sgs.Damaged, sgs.Damage, sgs.EventPhaseEnd} ,
	on_trigger = function(self, event, tenkai, data)
		local room = tenkai:getRoom()
		if event == sgs.EventPhaseEnd then
			for _, tenkaiX in sgs.qlist(room:findPlayersBySkillName("luaduanjie")) do 
				if tenkaiX:hasFlag("luaduanjie") then 
					tenkaiX:setFlags("-luaduanjie")
					if room:askForUseCard(tenkaiX, "@@luaduanjie", "@luaduanjie") then
					else
						tenkaiX:drawCards(1) 
					end 
				end 
			end 
		else
			local damage = data:toDamage()
			if damage.damage ~= 1 then return false end 
			if event == sgs.Damaged and (not damage.to or damage.to:objectName() ~= tenkai:objectName()) then return false end 
			if event == sgs.Damage and (not damage.from or damage.from:objectName() ~= tenkai:objectName()) then return false end 
			if tenkai:hasSkill("luaduanjie") then 
				if not tenkai:hasFlag("luaduanjie") then  
					tenkai:setFlags("luaduanjie")
				end
			end 
		end
	end
}

lualianliCard = sgs.CreateSkillCard{
	name = "lualianli", 
	target_fixed = true,
	filter = function(self, targets, to_select)
		return #targets == 0 
	end,
	on_use = function(self, room, source, targets) 
		local choices = {}
		for _, skill in sgs.qlist(source:getVisibleSkillList()) do
			if not skill:isLordSkill() then
				table.insert(choices, skill:objectName())
			end 
		end
		if #choices > 1 then
			local choice = source:getRoom():askForChoice(source, self:objectName(), table.concat(choices, "+"))
			if choice then
				room:detachSkillFromPlayer(source, choice)

				local loseSkill = source:getTag("SkillTemproryDetached")
				if loseSkill:toString() and loseSkill:toString() ~= "" then 
					source:setTag("SkillTemproryDetached", sgs.QVariant(loseSkill:toString() .. "|" .. choice)) 
				else
					source:setTag("SkillTemproryDetached", sgs.QVariant(choice))  
				end  
 
				local choices2 = {}
				if source:hasSkill("luaduanjie") then table.insert(choices2, "luaduanjie") end 
				if source:hasSkill("luaxiangyao") then table.insert(choices2, "luaxiangyao") end 
				local choice2 = source:getRoom():askForChoice(source, self:objectName(), table.concat(choices2, "+"))
				if choice2 then
					local used = room:askForUseCard(source, "@@" .. choice2, "@" .. choice2) 
				end 
			end 
		end 
	end
}

lualianli = sgs.CreateZeroCardViewAsSkill{
	name = "lualianli$", 
	view_as = function(self, cards)
		local card = lualianliCard:clone()
		card:setSkillName(self:objectName())
		return card
	end, 
	enabled_at_play = function(self, player) 
		return true
	end
}

lualianlix = sgs.CreateTriggerSkill {
	name = "#lualianli2",
	global = true, 
	priority = 2,
	events = { sgs.EventPhaseChanging },
	on_trigger = function(self, event, player, data, room)
		local room = player:getRoom()
		local change = data:toPhaseChange()
		if change.to == sgs.Player_NotActive then
			if player:objectName() == room:getCurrent():objectName() then
				local loseSkill = player:getTag("SkillTemproryDetached")
				if loseSkill and loseSkill:toString() and loseSkill:toString() ~= "" then
					loseSkill = loseSkill:toString()
					loseSkill = loseSkill:split("|") 
					for _, name in ipairs(loseSkill) do
						room:acquireSkill(player, name) 
					end 
				end  
				player:removeTag("SkillTemproryDetached")
			end 
		end 
	end 
}

tenkai_tsurubami:addSkill(luaxiangyao)
tenkai_tsurubami:addSkill(luaduanjie)
tenkai_tsurubami:addSkill(lualianli)
tenkai_tsurubami:addSkill(lualianlix)



luaqisheCard = sgs.CreateSkillCard{
	name = "luaqishe", 
	will_throw = false,
	filter = function(self, targets, to_select)
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		local dummy = sgs.Sanguosha:cloneCard(card:objectName(), sgs.Card_NoSuit, 0)  
		dummy:deleteLater()
		if not ((dummy:isKindOf("FireAttack") or dummy:isKindOf("quanxiang")) and to_select:isKongcheng())
			and not ((dummy:isKindOf("Snatch") or dummy:isKindOf("Dismantlement")) and to_select:isAllNude())
			and not (dummy:isKindOf("Peach") and not to_select:isWounded()) then 
				return #targets < sgs.Self:getHandcardNum()  
		end 
	end,	
	feasible = function(self, targets)
		return #targets >= sgs.Self:getHandcardNum() 
	end, 
	on_use = function(self, room, source, targets) 
		if #targets ~= source:getHandcardNum() then return false end 
		
		local card = sgs.Sanguosha:getCard(self:getSubcards():first()) 
		local subcardsX = source:getHandcards()
		local function removeX(targetsX, target_Y)
			local _targetsP = {}
			for _, p in ipairs(targetsX) do
				if p:objectName() ~= target_Y:objectName() then
					table.insert(_targetsP, p)
				end 
			end 
			return _targetsP
		end 
		if card  then --and card:isAvailable(source)  暂时不能加上这个
			for _, cardX in sgs.list(subcardsX) do 
				local _targets = sgs.SPlayerList()
				for _, p in ipairs(targets) do
					local dummy = sgs.Sanguosha:cloneCard(card:objectName(), sgs.Card_SuitToBeDecided, -1) 
					dummy:addSubcard(cardX)
					if not source:isProhibited(p, dummy)
						and not ((dummy:isKindOf("FireAttack") or dummy:isKindOf("quanxiang")) and p:isKongcheng())
						and not ((dummy:isKindOf("Snatch") or dummy:isKindOf("Dismantlement")) and p:isAllNude())
						and not (dummy:isKindOf("Peach") and not p:isWounded()) then 
						_targets:append(p)
					end 	
					dummy:deleteLater()			
				end   
				if not _targets:isEmpty() then 
					local targetX = room:askForPlayerChosen(source, _targets, "luaqishe", "luaqisheTarget", false, false)
					if targetX then  
						room:doAnimate(1, source:objectName(), targetX:objectName()) 
						room:getThread():delay()  
						targets = removeX(targets, targetX)
						local dummy = sgs.Sanguosha:cloneCard(card:objectName(), sgs.Card_SuitToBeDecided, -1) 
						dummy:addSubcard(cardX)
						dummy:setSkillName("luaqishe")
						if card:isKindOf("AOE") or card:isKindOf("AmazingGrace") or card:isKindOf("GodSalvation") then
							room:setPlayerFlag(source, "qucaiAOE")
							room:setPlayerFlag(targetX, "qucaiAOEs")
							room:useCard(sgs.CardUseStruct(dummy, source, sgs.SPlayerList()), true)
							room:setPlayerFlag(source, "-qucaiAOE")
							room:setPlayerFlag(targetX, "-qucaiAOEs")		 
						else
							room:useCard(sgs.CardUseStruct(dummy, source, targetX), true) 
						end 
					else
						local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, "luaqishe", "luaqishe", "")
						room:moveCardTo(cardX, nil, sgs.Player_DiscardPile, reason, true)
					end 
				else
					local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, "luaqishe", "luaqishe", "")
					room:moveCardTo(cardX, nil, sgs.Player_DiscardPile, reason, true) 
				end 
			end 
		end 
	end,
} 
luaqishe = sgs.CreateOneCardViewAsSkill{
	name = "luaqishe",
	view_filter = function(self, card) 
		return (not (card:isKindOf("Jink") or card:isKindOf("sakura") or card:isKindOf("Nullification") or card:isKindOf("Collateral")))
			and (card:isAvailable(sgs.Self) or card:isKindOf("Peach")) and not card:isKindOf("EquipCard") and not card:isKindOf("banquet")
	end,
	view_as = function(self, card)
		local qnc = luaqisheCard:clone()
		qnc:addSubcard(card)
		qnc:setSkillName(self:objectName())
		return qnc
	end,
	enabled_at_play = function(self, player)
		for _, card in sgs.list(player:getHandcards()) do
			if player:isJilei(card) then return false end 
		end 
		return not player:isKongcheng()
	end,
}


luayantuoCard = sgs.CreateSkillCard{
	name = "luayantuo", 
	will_throw = false,
	filter = function(self, targets, to_select)
		return to_select:isWounded()
	end,
	on_effect = function(self, effect) 
		local room = effect.from:getRoom()
		room:recover(effect.to, sgs.RecoverStruct(effect.from, nil, 1))
		room:setPlayerMark(effect.from, "luayantuo", effect.from:getMark("luayantuo") + 1)
		room:setPlayerMark(effect.to, "@luayantuoDown", effect.to:getMark("@luayantuoDown") + 1)

	end
}

luayantuoVS = sgs.CreateZeroCardViewAsSkill{
	name = "luayantuo",
	view_as = function(self, cards)
		return luayantuoCard:clone()
	end,
	enabled_at_play = function(self, player) 
		return player:getMark("luayantuo") < 1
	end,
}
luayantuo = sgs.CreateTriggerSkill{
	name = "luayantuo",
	global = true, 
	priority = -1,
	view_as_skill = luayantuoVS,  
	events = {sgs.PreHpRecover, sgs.Damaged, sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.PreHpRecover then
			local rec = data:toRecover() 
			if player:getMark("@luayantuoDown") > 0 then
				rec.recover = rec.recover - 1
				data:setValue(rec)
			end
		elseif event == sgs.Damaged then 
			local damage = data:toDamage()
			local function canLoseHp(playerXX)
				for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
					if hecatiaX and isFriendQ(room, playerXX, hecatiaX) and playerXX:objectName() ~= hecatiaX:objectName()
							and playerXX:getHp() == hecatiaX:getHp() then
						room:notifySkillInvoked(hecatiaX, "luayiti")
						return false
					end 
				end 
				for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
					if Erin and Erin:getKingdom() == playerXX:getKingdom() then
						room:notifySkillInvoked(Erin, "luajiance")
						return false
					end 
				end 
				return true
			end 
			if damage.to and damage.to:getMark("@luayantuoDown") > 0 and (not damage.to:isKongcheng() or damage.to:hasSkill("luayantuo")) and damage.damage > 0 then
				local Hypothias = room:findPlayersBySkillName("luayantuo")
				if not Hypothias:isEmpty() then 
					local card = room:askForCard(damage.to, ".|.|.|.", "@luayantuoGive", sgs.QVariant(), sgs.Card_MethodNone) 
					if card then
						local Hypothia = room:askForPlayerChosen(damage.to, Hypothias, "luayantuo", "luayantuo", false, false)
						if Hypothia then
							room:obtainCard(Hypothia, card, false)  
							if canLoseHp(damage.to) and damage.to:objectName() ~= Hypothia:objectName() then room:loseHp(damage.to) end 
							room:setPlayerMark(damage.to, "@luayantuoDown", 0) 
						end 
					end 
				end 
			end   
		elseif event == sgs.EventPhaseEnd then 
			for _, Hypothia in sgs.qlist(room:findPlayersBySkillName("luayantuo")) do 
				room:setPlayerMark(Hypothia, "luayantuo", 0)  
			end  
		end
	end,
}

luajinmie = sgs.CreateTriggerSkill{
	name = "luajinmie" ,  
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:getCurrent():getPhase() == sgs.Player_Finish and player:getMark("@luajinmie") >= 1 then
			local targets = sgs.SPlayerList()
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:getHp() == 1 then targets:append(p) end 
			end
			if targets:length() > 0 then
				room:sendCompulsoryTriggerLog(player, "luajinmie")
				room:notifySkillInvoked(player, self:objectName())
				player:loseMark("@luajinmie")
				for _, p in sgs.qlist(targets) do
					room:doAnimate(1, player:objectName(), p:objectName()) 
					room:getThread():delay()
					room:damage(sgs.DamageStruct(self:objectName(), player, p)) 
				end 
			end 
		end
	end
}

luajinmie2 = sgs.CreateTriggerSkill{
	name = "#luajinmie" ,  
	frequency = sgs.Skill_Compulsory,
	events = {sgs.TurnStart} ,
	global = true,
	priority = 8,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom() 
		for _,p in sgs.qlist(room:getAlivePlayers()) do
			if p:getMark("luajinmieu") == 0 then 
				room:setPlayerMark(p, "luajinmieu", 1)
				if p:hasSkill("luajinmie") then 
					room:setPlayerMark(p, "@luajinmie", 1)
				end 
			end
			if p:getMark("rumiaMax") == 0 and p:getGeneralName() == "ex_rumia" then 
				room:setPlayerMark(p, "rumiaMax", 1)
				if p:isLord() then
					room:setPlayerProperty(p, "hp", sgs.QVariant(6))   
				else
					room:setPlayerProperty(p, "hp", sgs.QVariant(5)) 
				end 
				local y = p:getHp() - p:getMaxHp()
				if y >= 0 then 
					room:setPlayerMark(p, "@canji", y) 
				else
					if p:getMark("@canji") > 0 then 
						room:setPlayerMark(p, "@canji", 0)
					end 
				end
			end 
		end
	end
}

Hypothia:addSkill(luaqishe)
Hypothia:addSkill(luayantuo)
Hypothia:addSkill(luajinmie)
Hypothia:addSkill(luajinmie2)
 

--条件有哪些
local damageSkills = {
	reisen = function(self, player, damage, data)
		local room = player:getRoom() 
		local x = damage.damage
		for i = 0, x - 1, 1 do
			if not player:isAlive() then return end
			room:setPlayerFlag(player, "luayuelong")
			room:askForUseCard(player, "@@Luayuelong", "@Luayuelong")
			room:setPlayerFlag(player, "-luayuelong")
		end
	end,

	tatara = function(self, player, damage, data)
		local room = player:getRoom() 
		if not damage.from then return end
		local target
		if player:objectName() == damage.from:objectName() then target = damage.to end
		if player:objectName() == damage.to:objectName() then target = damage.from end
		if not target:isAlive() then return end
		if not player:hasFlag("luajingxia") and room:askForSkillInvoke(player, self:objectName(), data) then
			room:setPlayerFlag(player, "luajingxia")
			if not player:isAlive() then return end
			local ids_A = sgs.IntList()
			for _,equip in sgs.qlist(target:getEquips()) do
				if equip:isKindOf("EquipCard") then
					ids_A:append(equip:getId())
				end
			end
			for _, cardD in sgs.qlist(target:getHandcards()) do
				if cardD:isKindOf("EquipCard") then
					ids_A:append(cardD:getId())
				end
			end
			if ids_A:isEmpty() then
				target:drawCards(1) ;target:turnOver()
				if not target:getEquips() then target:drawCards(1) end
				if target:getEquips():isEmpty() then target:drawCards(1) end
				return
			end
			local choice = room:askForChoice(target, self:objectName(), "giveEquip+turnOver")
			if choice == "turnOver" then
				target:drawCards(1) ;target:turnOver()
				if not target:getEquips() then target:drawCards(1) end
				if target:getEquips():isEmpty() then target:drawCards(1) end
			else
				room:fillAG(ids_A, player)
				local _data = sgs.QVariant()
				_data:setValue(player)
				player:setTag("luajingxiaTP", _data)
				local card_id = room:askForAG(player, ids_A, false, "luajingxia")
				player:removeTag("luajingxiaTP")
				local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				dummy_0:addSubcard(card_id)
				room:clearAG()
				room:obtainCard(player, dummy_0, sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_ROB, player:objectName()))
			end
		end
	end,

	komachi = function(self, player, damage, data)
		local room = player:getRoom() 
		if (not damage.card or (not damage.card:isKindOf("AOE"))) and damage.by_user and not damage.transfer and damage.to:isAlive() then
			if room:askForSkillInvoke(player, self:objectName(), data) then
				local judge = sgs.JudgeStruct()
				judge.pattern = ".|heart"
				judge.good = true
				judge.reason = self:objectName()
				judge.who = damage.to
				room:judge(judge)
				if judge:isGood() then
					if damage.to and not damage.to:isNude() then
						local card_id = room:askForCardChosen(player, damage.to, "he", self:objectName())
						local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, player:objectName())
						room:obtainCard(player, sgs.Sanguosha:getCard(card_id), reason, room:getCardPlace(card_id) ~= sgs.Player_PlaceHand)
					end
				else
                    damage.to:gainMark("@luasilian")
				end
			end
		end
	end, 

	ringoseiran = function(self, player, damage, data)
		local room = player:getRoom() 
		if damage.damage == 0 then return false end
		if ((damage.card and (not damage.card:isKindOf("AOE"))) or not damage.card) and damage.by_user and not player:hasFlag("luayuetuan") then
			room:setPlayerFlag(player, "luayuetuan")
			local x = damage.damage
			for i = 0, x - 1, 1 do
				if room:askForDiscard(player, self:objectName(), 1, 1, true, true) then
					local p = room:askForPlayerChosen(player, room:getAlivePlayers(), "luayuetuan", "luayuetuan", false, true)
					p:drawCards(1)
				end
			end
			room:setPlayerFlag(player, "-luayuetuan")
		end
	end,

	mai_satono = function(self, player, damage, data)
		local room = player:getRoom()
		if player:objectName() ~= damage.from:objectName() then
			return false
		end 
		if damage.card and damage.card:isKindOf("AOE") then
			return false
		end
		local olayer = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "luazhuwu", true, true)
		if olayer then
			local room2 = olayer:getRoom()
			local xd = olayer:getHp() + 1
			room:setPlayerProperty(olayer, "hp", sgs.QVariant(xd))
			local thread = olayer:getRoom():getThread()
			thread:trigger(sgs.HpChanged, olayer:getRoom(), olayer)
			room:setPlayerMark(olayer, "@zhuwu", 1)
			local playerdata = sgs.QVariant() -- ai用
			playerdata:setValue(player)
			room2:setTag("maisatono", playerdata)

		end
	end, 

	weiyan = function(self, player, damage, data)
		local room = player:getRoom() 
		local invoke = player:getTag("invokeLuaKuanggu"):toBool()
		player:setTag("invokeLuaKuanggu", sgs.QVariant(false))
		if invoke and player:isWounded() then
			local recover = sgs.RecoverStruct()
			recover.who = player
			recover.recover = damage.damage
			room:recover(player, recover)
		end
	end, 

	panfeng = function(self, player, damage, data) 
		local target = damage.to
		if damage.card and damage.card:isKindOf("Slash") and target:hasEquip() and (not damage.chain) and (not damage.transfer) then
			local equiplist = {}
			for i = 0, 3, 1 do
				if not target:getEquip(i) then continue end
				if player:canDiscard(target, target:getEquip(i):getEffectiveId()) or (player:getEquip(i) == nil) then
					table.insert(equiplist,tostring(i))
				end
			end
			if #equiplist == nil then return false end
			if not player:askForSkillInvoke(self:objectName(), data) then return false end
			local _data = sgs.QVariant()
			_data:setValue(target)
			local room = player:getRoom()
			local equip_index = tonumber(room:askForChoice(player, "LuaKuangfu_equip", table.concat(equiplist, "+"), _data))
			local card = target:getEquip(equip_index)
			local card_id = card:getEffectiveId()
			local choicelist = {}
			if player:canDiscard(target, card_id) then
				table.insert(choicelist, "throw")
			end
			if (equip_index > -1) and (player:getEquip(equip_index) == nil) then
				table.insert(choicelist, "move")
			end
			local choice = room:askForChoice(player, "LuaKuangfu", table.concat(choicelist, "+"))
			if choice == "move" then
				room:moveCardTo(card, player, sgs.Player_PlaceEquip)
			else
				room:throwCard(card, target, player)
			end
		end	
	end, 

	xusheng = function(self, player, damage, data) 
		if damage.card and damage.card:isKindOf("Slash") and (not damage.chain) and (not damage.transfer)
				and damage.to:isAlive() then
			if player:getRoom():askForSkillInvoke(player, self:objectName(), data) then
				local x = math.min(5, damage.to:getHp())
				damage.to:drawCards(x)
				damage.to:turnOver()
			end
		end
	end, 

	zhurong = function(self, player, damage, data)
		local room = player:getRoom() 
		local target = damage.to
		if damage.card and damage.card:isKindOf("Slash") and (not player:isKongcheng()) and (not target:isKongcheng()) and (not target:hasFlag("Global_DebutFlag")) and (not damage.chain) and (not damage.transfer) then
			if room:askForSkillInvoke(player, self:objectName(), data) then
				local success = player:pindian(target, "LuaLieren", nil)
				if not success then return false end
				if not target:isNude() then
					local card_id = room:askForCardChosen(player, target, "he", self:objectName())
					local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, player:objectName())
					room:obtainCard(player, sgs.Sanguosha:getCard(card_id), reason, room:getCardPlace(card_id) ~= Player_PlaceHand)
				end
			end
		end
	end, 

	lidian = function(self, player, damage, data)
		local room = player:getRoom()  
		if not damage.to or damage.to:objectName() == damage.to:objectName() then return false end
		local players = sgs.SPlayerList()
			players:append(player)
			players:append(target)
			room:sortByActionOrder(players)
		for i = 1, damage.damage, 1 do
			if not target:isAlive() or not player:isAlive() then return false end
			local value = sgs.QVariant()
				value:setValue(target)
			if room:askForSkillInvoke(player,self:objectName(),value) then
				room:drawCards(players,1,self:objectName())
			end
		end
	end, 

}

luazhanhun = sgs.CreateTriggerSkill{
	name = "luazhanhun",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damage, sgs.PreDamageDone},
	on_trigger = function(self, event, player, data, room)
		local damage = data:toDamage() 
		if event == sgs.Damage then 
			local target = damage.to
			local Ava = {}
			local invoke = player:getTag("invokeLuaKuanggu"):toBool()
			player:setTag("invokeLuaKuanggu", sgs.QVariant(false))
			if ((damage.card and (not damage.card:isKindOf("AOE"))) or not damage.card) and damage.by_user and not damage.chain 
				and not damage.transfer and not player:hasFlag("luayuelong") then
					table.insert(Ava, "reisen")
			end 
			if not player:hasFlag("luajingxia") and damage.to and damage.to:isAlive() then 
				table.insert(Ava, "tatara")
			end 
			if (not damage.card or (not damage.card:isKindOf("AOE"))) and damage.by_user and not damage.transfer and damage.to:isAlive() then
				table.insert(Ava, "komachi")
				table.insert(Ava, "mai_satono")
			end 
			if ((damage.card and (not damage.card:isKindOf("AOE"))) or not damage.card) and damage.by_user and not player:hasFlag("luayuetuan") then
				table.insert(Ava, "ringoseiran")
			end 
			if invoke and player:isWounded() then
				table.insert(Ava, "weiyan")
			end 
			if damage.card and damage.card:isKindOf("Slash") and target:hasEquip() and (not damage.chain) and (not damage.transfer)
				and damage.to and damage.to:isAlive() then
				table.insert(Ava, "panfeng") 
			end 
			if damage.card and damage.card:isKindOf("Slash") and (not damage.chain) and (not damage.transfer) and damage.to:isAlive() then
				table.insert(Ava, "xusheng") 
			end 
			if damage.card and damage.card:isKindOf("Slash") and (not player:isKongcheng()) and (not target:isKongcheng()) 
				and (not target:hasFlag("Global_DebutFlag")) and (not damage.chain) and (not damage.transfer) and damage.to and damage.to:isAlive() then
				table.insert(Ava, "zhurong") 
			end  
			if damage.to and damage.to:objectName() ~= damage.to:objectName() and damage.to and damage.to:isAlive() then 
				table.insert(Ava, "lidian") 
			end
			if #Ava > 2 then
				local acquired = {}
				repeat
					local rand = math.random(1,#Ava) 
					if not table.contains(acquired,Ava[rand]) then
						table.insert(acquired,(Ava[rand])) 
					end
				until #acquired == 3
				local choice = room:askForGeneral(player, table.concat(acquired, "+")) 
				damageSkills[choice](self, player, damage, data)
				player:gainMark("@wrath", 1)
			end 
		elseif (event == sgs.PreDamageDone) and damage.from and damage.from:hasSkill(self:objectName()) and damage.from:isAlive() then
				local weiyan = damage.from
				weiyan:setTag("invokeLuaKuanggu", sgs.QVariant((weiyan:distanceTo(damage.to) <= 1)))
		end 
	end 
}

luaduopoCard = sgs.CreateSkillCard{
	name = "luaduopo",
	will_throw = true, 
	filter = function(self, targets, to_select)
		local length = self:getSubcards():length() - 1 
		local targets_list = sgs.PlayerList()  
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		slash:setSkillName("luaduopo")
		slash:deleteLater()
		return slash:targetFilter(targets_list, to_select, sgs.Self) and #targets < self:getSubcards():length() - 1
	end,
	on_effect = function(self, effect)
		local source = effect.from
		local room = source:getRoom() 
		  
		if source:canSlash(effect.to, nil, false) then
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			slash:setSkillName("luaduopo")
			room:useCard(sgs.CardUseStruct(slash, source, effect.to))

		end 
	end 

}
luaduopoVS = sgs.CreateViewAsSkill{
	name = "luaduopo",
	n = 99,
	view_filter = function(self, selected, to_select)
		local n = to_select:getNumber()
		if n > 10 then return false end 
		for _, card in ipairs(selected) do
			n = n + card:getNumber()
			if n > 10 then return false end 
		end 
		return true
	end,
	view_as = function(self, cards)
		if #cards <= 1 then return nil end 
		local n = 0
		for _, card in ipairs(cards) do
			n = n + card:getNumber()
		end 
		if n < 10 then return nil end 
		local acard = luaduopoCard:clone()
		for _, bcard in ipairs(cards) do
			acard:addSubcard(bcard)
		end			
		return acard		 
	end
}
luaduopo = sgs.CreateTriggerSkill{
	name = "luaduopo" ,
	view_as_skill = luaduopoVS,
	events = {sgs.HpChanged, sgs.MaxHpChanged} ,
	on_trigger = function(self, event, rumia, data)
		local room = rumia:getRoom() 
		if rumia:isWounded() then 
			room:setPlayerMark(rumia, "luaduopo", 1)  
			room:loseMaxHp(rumia, 1)
			local choice = room:askForChoice(rumia, "luaduopo", "shenfen+luajinmie+luakuangwang") 
			if choice and not rumia:hasSkill(choice) then room:acquireSkill(rumia, choice) end 
		end 
	end,
	can_trigger = function(self, target)
		return (target and target:isAlive() and target:hasSkill(self:objectName()))
				and (target:getMark("luaduopo") == 0)
	end
}

luaduopo2 = sgs.CreateTriggerSkill{
	name = "#luaduopo", 
	global = true, 
	events = {sgs.CardFinished}, 
	on_trigger = function(self, event, player, data, room)
		local use = data:toCardUse()
		if use.card and use.card:isKindOf("Slash") and use.card:getSkillName() == "luaduopo" and use.to
			and use.from and use.from:objectName() == player:objectName() then
			for _, t in sgs.qlist(use.to) do
				if t:isWounded() and t:isAlive() then
					room:loseMaxHp(t, 1)
					local trueHp = player:getHp()
					room:setPlayerProperty(player, "maxhp", sgs.QVariant(player:getMaxHp() + 1)) 
					room:setPlayerProperty(player, "hp", sgs.QVariant(trueHp)) 
				end 
			end 
		end
	end
}

ex_rumia:addSkill(luazhanhun)
ex_rumia:addSkill(luaduopo)
ex_rumia:addSkill(luaduopo2)

local function isFriendQ(room, target, player)
	if player:getRole() == "rebel" then
		return target:getRole() == "rebel"
	end
	if player:getRole() == "loyalist" or player:getRole() == "lord" then
		return target:getRole() == "loyalist" or target:getRole() == "lord"
	end
end
luahongyi = sgs.CreateTriggerSkill{
	name = "luahongyi",
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if not p:hasSkill("luahongyia") and isFriendQ(room, p, p2)
						then room:writeToConsole("jiantin test" .. p:objectName()); room:attachSkillToPlayer(p, "luahongyia") end
				end
			end
		end
	end
}

luahongyiused = sgs.CreateTriggerSkill{
	name = "#luahongyiused" ,
	global = true,
	frequency = sgs.Skill_Compulsory ,
	events = {sgs.CardResponded, sgs.CardUsed} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local torisumis = sgs.SPlayerList() 
		for _, p in sgs.qlist(room:findPlayersBySkillName("luahongyi")) do
			torisumis:append(p)
		end 
		if event == sgs.CardResponded then
			local resp = data:toCardResponse()
			local aplayerx = resp.m_who
			if (resp.m_card:getSkillName() == "luahongyia" and aplayerx and aplayerx:isAlive() and not torisumis:isEmpty()) then 
				local torisumi = room:askForPlayerChosen(aplayerx, torisumis, self:objectName(), "luahongyi", false, false)
				if (torisumi:getMark("@hongyired") > 0) or ((torisumi:getMark("@hongyired") == 0) and (torisumi:getMark("@hongyiblack") == 0)) then
					room:setPlayerMark(torisumi, "@hongyired", 0)
					room:setPlayerMark(torisumi, "@hongyiblack", 1)
					torisumi:gainMark("@hongyyi")
				elseif (torisumi:getMark("@hongyiblack") > 0) then
					room:setPlayerMark(torisumi, "@hongyired", 1)
					room:setPlayerMark(torisumi, "@hongyiblack", 0)
					torisumi:gainMark("@hongyyi")
				end
			end
		else
			local use = data:toCardUse()
			if (use.from:objectName() == player:objectName()) and (use.card:getSkillName() == "luahongyia") and not torisumis:isEmpty() then
				local torisumi = room:askForPlayerChosen(use.from, torisumis, self:objectName(), "luahongyi", false, false)
				if (torisumi:getMark("@hongyired") > 0) or ((torisumi:getMark("@hongyired") == 0) and (torisumi:getMark("@hongyiblack") == 0)) then
					room:setPlayerMark(torisumi, "@hongyired", 0)
					room:setPlayerMark(torisumi, "@hongyiblack", 1)
					torisumi:gainMark("@hongyyi")
				elseif (torisumi:getMark("@hongyiblack") > 0) then
					room:setPlayerMark(torisumi, "@hongyired", 1)
					room:setPlayerMark(torisumi, "@hongyiblack", 0)
					torisumi:gainMark("@hongyyi")
				end
			end
		end
		return false
	end
}
luafushenCard = sgs.CreateSkillCard{
	name = "luafushen",
	will_throw = false,
	target_fixed = true,
	handling_method = sgs.Card_MethodResponse,
	on_use = function(self, room, source, targets)
		if room:askForUseCard(source, "jink", "@luafushen", -1, sgs.Card_MethodResponse)
			and source:getMark("@hongyyi") >= 3 then
			local choices = {"DoNothing", "luajinlun"}
			if source:getMark("@hongyyi") >= 6 then
				table.insert(choices, "luasushen")
			end
			local choice = room:askForChoice(source, "luafushen", table.concat(choices,"+"))
			if choice == "luajinlun" then
				source:loseMark("@hongyyi", 3)
				local p = room:askForPlayerChosen(source, room:getAlivePlayers(), "luajinlun", "luajinlun", true, true)
				if p then
					local jink = sgs.Sanguosha:cloneCard("jink")
					jink:addSubcards(p:getHandcards())
					room:throwCard(jink, p, p)
					if p:isAlive() then
						room:setPlayerMark(p, "@extra_turn", 1)
						p:gainAnExtraTurn()
						room:setPlayerMark(p, "@extra_turn", 0)
					end
				end
			elseif choice == "luasushen" then
				source:loseMark("@hongyyi", 6)
				local p = room:askForPlayerChosen(source, room:getAlivePlayers(), "luasushen", "luasushen", true, true)
				if p then
					local x = 4 - p:getHp()
					x = math.min(p:getLostHp(), x)
					source:getRoom():recover(p, sgs.RecoverStruct(source, nil, x))
					p:drawCards(x)
				end
			end
		end
	end
}
luafushen = sgs.CreateZeroCardViewAsSkill{
	name = "luafushen",
	view_as = function(self, card)
		local skill_card = luafushenCard:clone()
		skill_card:setSkillName(self:objectName())
		return skill_card
	end,
	enabled_at_play = function(self, player)
		return player:hasSkill("luafushen")
	end,
}
torisumi:addSkill(luahongyi)
torisumi:addSkill(luahongyiused)
torisumi:addSkill(luafushen)

function addToanxbbb(ids, player, room, name)
	local lord = room:getLord()
	anxbbb = {}
 
	local uaqiuwen = lord:getTag("luamuling"):toString() 
	uaqiuwen = uaqiuwen:split("|") 
	for _, id in ipairs(uaqiuwen) do
		table.insert(anxbbb, id)
	end 
	for _, id in sgs.qlist(ids) do
		table.insert(anxbbb, id)
	end  
	
	lord:setTag("luamuling", sgs.QVariant(table.concat(anxbbb, "|"))) 
	
	local dummy2 = sgs.Sanguosha:cloneCard("yuzhi", sgs.Card_NoSuit, 0)
	dummy2:addSubcards(ids) 
	local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, player:objectName(), name, nil)
	room:moveCardTo(dummy2, player, nil, sgs.Player_PlaceTable, reason, true)
	dummy2:deleteLater()
	
	local dummy3 = sgs.Sanguosha:cloneCard("yuzhi", sgs.Card_NoSuit, 0)
	dummy3:addSubcards(ids)
	local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, nil, name, nil)
	room:moveCardTo(dummy3, nil, nil, sgs.Player_PlaceUnknown, reason, true)
	dummy3:deleteLater()
end 

luamulingCard = sgs.CreateSkillCard{
	name = "luamuling",
	will_throw = false,
	target_fixed = true,
	handling_method = sgs.Card_MethodNone,
	on_use = function(self, room, source, targets)   
		local lord = room:getLord()
		local uaqiuwen = lord:getTag("luamuling"):toString() --甚至提供及时查看功能，太nice了！
		uaqiuwen = uaqiuwen:split("|")
		local range_list = sgs.IntList()
		for _, id in ipairs(uaqiuwen) do
			range_list:append(id)
		end 
		room:fillAG(range_list)
        room:getThread():delay(1500)
		room:clearAG()
	end
}
luamulingVS = sgs.CreateZeroCardViewAsSkill{
	name = "luamuling",
	view_as = function(self, cards)
		local card = luamulingCard:clone()
		card:setSkillName(self:objectName())
		return card
	end,
	enabled_at_play = function(self, player)
		return true
	end,
}
luamuling = sgs.CreateTriggerSkill{
	name = "luamuling",
	--frequency = sgs.Skill_Compulsory, 
	events = {sgs.AfterDrawInitialCards}, 
	view_as_skill = luamulingVS,
	on_trigger = function(self, triggerEvent, Yaezaki_An, data)
		local room = Yaezaki_An:getRoom()
		if triggerEvent == sgs.AfterDrawInitialCards then 
			room:notifySkillInvoked(Yaezaki_An, "luamuling")
			local ids = room:askForExchange(Yaezaki_An, self:objectName(), 99, 0, false, "mingren_put", true):getSubcards()
			addToanxbbb(ids, Yaezaki_An, room, self:objectName())
		end
	end
} 

luayunsong = sgs.CreateTriggerSkill{
	name = "luayunsong", 
	events = {sgs.AfterDrawInitialCards},  
	on_trigger = function(self, triggerEvent, Yaezaki_An, data)
		local room = Yaezaki_An:getRoom()
		if triggerEvent == sgs.AfterDrawInitialCards then 
		end
	end
} 


luabaiyin = sgs.CreateTriggerSkill{
	name = "luabaiyin", 
	global = true, 
	events = {sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:getTag("FirstRound"):toBool() then return end
		local move = data:toMoveOneTime() 
		if move.to and move.to:objectName() ~= player:objectName() then
			return
		end
		if move.to_place ~= sgs.Player_PlaceHand and move.to_place ~= sgs.Player_PlaceEquip then return false end  
		if move.card_ids:length() <= 1 then return false end  
		if player:hasSkill(self:objectName()) then return false end 
		local Yaezaki_Ans = sgs.SPlayerList()
		for _, p in sgs.qlist(room:getAllPlayers()) do
			if p:hasSkill("luabaiyin") then
				Yaezaki_Ans:append(p)							
			end
		end	
		if Yaezaki_Ans:length() > 0 and room:askForSkillInvoke(player, self:objectName()) then 
			local Subcards = sgs.IntList()
			for _, card in sgs.list(player:getHandcards()) do
				for _, id in sgs.qlist(move.card_ids) do
					if card:getId() == id then Subcards:append(id) end 
				end 
			end 
			
			if player:getEquips() then 
				for _,card in sgs.qlist(player:getEquips()) do 
					for _, id in sgs.qlist(move.card_ids) do
						if card:getId() == id then Subcards:append(id) end 
					end 
				end 
			end  
			
			if Subcards:length() > 1 then 
				local to = room:askForPlayerChosen(player, Yaezaki_Ans, "luabaiyin", "luabaiyin", true, true)		
				if to then				
					local giveCard = sgs.IntList()
					
					room:fillAG(Subcards)
					local card_id = room:askForAG(player, Subcards, false, "luayunsong") 
					if card_id ~= -1 then
						Subcards:removeOne(card_id) 
						giveCard:append(card_id)
						addToanxbbb(giveCard, player, room, "luamuling")   
					end 
					room:clearAG()
					
					if card_id ~= -1 then
						for _,id in sgs.qlist(Subcards) do
							local cardX = sgs.Sanguosha:getCard(id)
							room:askForUseCard(player, cardX:toString(), "@luabaiyin")
						end
					end 
				end 
			end 
		end 	
	end
}
 
luajianju = sgs.CreateTriggerSkill{
	name = "luajianju", 
	events = {sgs.AfterDrawInitialCards},  
	on_trigger = function(self, triggerEvent, Yaezaki_An, data)
		local room = Yaezaki_An:getRoom()
		if triggerEvent == sgs.AfterDrawInitialCards then 
		end
	end
} 

Yaezaki_An:addSkill(luamuling)
Yaezaki_An:addSkill(luayunsong)
Yaezaki_An:addSkill(luabaiyin)
Yaezaki_An:addSkill(luajianju)

lualeigong4 = sgs.CreateProhibitSkill{
    name = "#lualeigong4",
    is_prohibited = function(self, from, to, card)
        return card:isKindOf("Weapon") and to:hasSkill("lualeigong") 
    end
}
lualeigong3 = sgs.CreateAttackRangeSkill{
	name = "#lualeigong3",
	extra_func = function(self,player)
		if player:hasSkill("lualeigong") then 
			return 3
		end 
	end,
}

lualeigong2 = sgs.CreateTriggerSkill{
	name = "#lualeigong", 
	global = true,
	priority = 50,
	events = {sgs.TurnStart, sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data, room) 
		local Magahara = room:findPlayerBySkillName("lualeigong")
		if event == sgs.TurnStart then 
			if Magahara and Magahara:isAlive() and Magahara:getMark("lualeigongA") == 0 then
				local discard_ids = room:getDrawPile()
				local ThunderSlashes = sgs.IntList()
				for _, id in sgs.qlist(discard_ids) do
					local card = sgs.Sanguosha:getCard(id)
					if card:isKindOf("ThunderSlash") then
						ThunderSlashes:append(id)
					end
				end
				Magahara:addToPile("lualeigong", ThunderSlashes)
				room:setPlayerCardLimitation(Magahara, "use", "Weapon|.|.|.", true) 
				room:setPlayerMark(Magahara, "lualeigongA", 1)
				Magahara:setTag("lualeigongX", sgs.QVariant(-1)) 
			end 
		else
			if Magahara and Magahara:isAlive() and Magahara:getTag("lualeigongX") and Magahara:getTag("lualeigongX"):toInt() >= 0 then
				local n = Magahara:getTag("lualeigongX"):toInt()
				local invoke = false
				local move = data:toMoveOneTime()
				for _, id in sgs.qlist(move.card_ids) do
					if id == n then
						invoke = true
					end 
				end 
				if invoke then
					local fields = Magahara:getPile("lualeigong")
					local count = fields:length()
					local idD
					if count == 0 then
						return
					elseif count == 1 then
						idD = fields:first()
					else
						room:fillAG(fields, Magahara)
						idD = room:askForAG(Magahara, fields, false, self:objectName())
						room:clearAG()
						if idD == -1 then
							return
						end
					end
					local dummy = sgs.Sanguosha:cloneCard("jink")
					dummy:addSubcard(idD)
					room:obtainCard(Magahara, dummy, false) 
					Magahara:setTag("lualeigongX", sgs.QVariant(-1)) 
				end 
			end 
		end 
	end
}
lualeigong = sgs.CreateTriggerSkill{
	name = "lualeigong",   
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Damage},
	on_trigger = function(self, event, player, data, room) 
		local room = player:getRoom()
		local damage = data:toDamage()
		if damage.damage == 0 then return false end	 
		if damage.card and damage.from and damage.by_user and not damage.chain and not damage.transfer then 
			player:setTag("lualeigongX", sgs.QVariant(damage.card:getId())) 
		end
	end
}

luadianli2 = sgs.CreateTriggerSkill{
	name = "#luadianli",   
	global = true, 
	events = {sgs.Damage, sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data, room) 
		if event == sgs.Damage then 
			local room = player:getRoom()
			local damage = data:toDamage()
			if damage.damage == 0 then return false end	
			local Magahara = room:findPlayerBySkillName("luadianli")
			if Magahara and Magahara:isAlive() then
				if damage.from and player:objectName() == damage.from:objectName() and damage.from:getMark("@luadianli") > 0 then
					damage.from:addMark("luadianliYY")
				end
			end 
		else
			local room = player:getRoom()
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive and player:getMark("@luadianli") > 0 and player:objectName() == room:getCurrent():objectName() then
				room:setPlayerMark(player, "@luadianli", 0)
				--if player:getMark("luadianliYY") > 0 then
					player:drawCards(player:getMark("luadianliYY") + 1)
				--end 
				room:setPlayerMark(player, "luadianliYY", 0)
			end 
		end 
	end
}
luadianliCard = sgs.CreateSkillCard{
	name = "luadianli",
	filter = function(self, targets, to_select)
		return (#targets == 0)
	end,
	on_effect = function(self, effect)
		local room = effect.to:getRoom()
		room:setPlayerMark(effect.to, "@luadianli", 1)
	end
}
luadianli = sgs.CreateOneCardViewAsSkill{
	name = "luadianli",
	filter_pattern = "Slash",
	view_as = function(self,card)
		local skillcard = luadianliCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self,player) 
		return not player:isKongcheng() and not player:hasUsed("#luadianli")
	end
}
magahara:addSkill(lualeigong)
magahara:addSkill(lualeigong2)
magahara:addSkill(lualeigong3)
magahara:addSkill(lualeigong4)
magahara:addSkill(luadianli) 
magahara:addSkill(luadianli2) 

luabainian = sgs.CreateTriggerSkill{
	name = "luabainian",
	global = true,
	events = {sgs.Damage},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.Damage then
			local damage = data:toDamage()
			if damage.from and damage.to:hasSkill("luabainian") and damage.from:objectName() == player:objectName() and not player:hasFlag("luabainian")
				and damage.to:isAlive() and room:askForSkillInvoke(player, "luabainian", data) then
				room:setPlayerFlag(player, "luabainian")
				player:drawCards(1)
				damage.to:drawCards(1)
				room:recover(damage.to, sgs.RecoverStruct(damage.to, nil, 1))
			end
		end
	end
}

luabainian3 = sgs.CreateTriggerSkill{
	name = "#luabainian2" ,
	global = true,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Start and player:objectName() == room:getCurrent():objectName() then
			local toziko = player:getRoom():findPlayerBySkillName("luabainian")
			if toziko then
				if toziko:hasFlag("luabainian") then room:setPlayerFlag(toziko, "-luabainian") end
			end
		end
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}

gongxiCard = sgs.CreateSkillCard{
	name = "luagongxi",
	filter = function(self, targets, to_select)
		return #targets == 0
	end,
	on_use = function(self, room, source, targets)
		room:handleAcquireDetachSkills(targets[1], "luachunhui")
	end
}
luachunhui2 = sgs.CreateTriggerSkill{
	name = "#luachunhui",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageInflicted},
	on_trigger = function(self, event, player, data, room)
		local damage = data:toDamage()
		if event == sgs.DamageInflicted and damage.to and damage.to:hasSkill("luachunhui")
			and damage.to:objectName() == player:objectName() then
			if player:hasSkill("luachunhui") then
				room:handleAcquireDetachSkills(player, "-luachunhui")
			end
		end
	end
}
luagongxi = sgs.CreateOneCardViewAsSkill{
	name = "luagongxi",
	filter_pattern = ".",
	view_as = function(self,card)
		local skillcard = gongxiCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self,player)
		for _, p in sgs.qlist(sgs.Self:getSiblings()) do
			if p:hasSkill("luachunhui") then return false end
		end
		if player:hasSkill("luachunhui") then return false end
		return true
	end
}

fujix:addSkill(luabainian)
fujix:addSkill(luabainian3)
fujix:addSkill(luagongxi)
fujix:addSkill(luachunhui2)

sgs.LoadTranslationTable {
	["pay31"] = "万千世界", --注意这里每次要加逗号
	["seoi"] = "濑笈叶", --注意这里每次要加逗号
	["#seoi"] = "花店员工",	

	["luajinjing"] = "精进", 
	["@luajinjingGive"] = "你需要交给 濑笈叶 一张手牌，或者令她习得你的一个技能。", 
	[":luajinjing"] = "任意角色的回合开始时，若你未受伤，你可以流失体力至与一名角色相同，然后其交给你一张手牌，或令你获得其一个技能。",
	["~luajinjing"] = "选择一名角色→点击确定",
	["@luajinjing"] = "你可以发动“精进”",

	["luazhongyan"] = "终焉", 
	[":luazhongyan"] = "每轮开始时，若你体力值为全场最多且少于轮数，所有其他角色流失一点体力，你触发“崩坏”。",
     
	["lualvzhiu"] = "绿荫",
	[":lualvzhiu"] = "锁定技，你受到【杀】对你造成的伤害时，你须减少一点体力上限并防止此伤害。你使用或打出一张基本牌后，你回复一点体力。", 
 
	["tenkai_tsurubami"] = "旧神社组",  
	["#tenkai_tsurubami"] = "凤声鹤唳",	

	["luaxiangyao"] = "降妖",  
	[":luaxiangyao"] = "你可以将包含【杀】的X张牌每张各当作【杀】，分配对至多等量角色依次使用（X为你体力值）。"..
 						"（目标角色可以弃置与这X张牌花色相同的一张手牌来取消成为此技能目标）",
	["luaxiangyaoInvoke"] = "请为你的【杀】分配一个目标", 
	["@luaxiangyaoDiscard"] = "最好从亮起的牌中弃置一张，否则会被【杀】爆", 	

	["luaduanjie"] = "断界", 
	[":luaduanjie"] = "锁定技，你造成或受到1点伤害的阶段结束时，你令至少一名角色摸一张牌。选择的角色数若超过2，则你失去“降妖”，然后那些角色失去1点体力。", 
	["~luaduanjie"] = "选择任意名角色，点击确定",
	["@luaduanjie"] = "你可以发动“断界”",

	["lualianli"] = "连理",
	[":lualianli"] = "主公技，你可以于本回合暂时失去一个技能，然后触发另外一个技能（主公技除外）。", 

	["Hypothia"] = "希帕西娅",  
	["#Hypothia"] = "微分天下",
	["luaqishe"] = "齐射",

	[":luaqishe"] = "你可以将所有手牌每张各当你手中已有的一张非装备牌，对等量角色分别使用之。",
	["@luaqishe"] = "请选择要当作什么牌",
	["luaqisheTarget"] = "请选择此牌的目标",

	["luayantuo"] = "延拓",
	[":luayantuo"] = "出牌阶段限一次，你可以令一名已受伤的角色回复一点体力。此后，其体力回复量-1。其受到伤害后，可交给你一张牌（若其不是你，须再流失一点体力）移除此效果。",
	["@luayantuoGive"] = "你可以交出一张牌移除你「体力回复量-1」的负面效果",
	["luajinmie"] = "烬灭",
	[":luajinmie"] = "限定技，锁定技，结束阶段，你对所有体力值为1的角色造成1点伤害。",
 
	["ex_rumia"] = "EX露米娅",  
	["#ex_rumia"] = "阿修罗",	
	["luazhanhun"] = "斩魂",
	[":luazhanhun"] = "你造成伤害后，你可以从三个于此时机发动的技能中选一个发动，然后获得一枚“暴怒”标记。",
	["luaduopo"] = "夺魄",
	[":luaduopo"] = "你可以弃置X张点数和为10的牌，视为对X-1名角色使用了一张【杀】，此【杀】结算后，若目标已受伤，你夺取其一点体力上限。"
	 .. "觉醒技，若你已受伤，你减一点体力上限，从“烬灭”“台风”“神愤”中选一项技能获得。",

	["torisumi"] = "鸟澄珠乌",
	["#torisumi"]= "极东的朱鹮",
	["luahongyi"] = "虹翼",
	["luahongyia"] = "虹翼",
	[":luahongyia"] = "转化技，①：将一张红色手牌当作【杀】使用或打出；②：将一张黑色手牌当作【闪】使用或打出。",
	[":luahongyi"] = "转化技，友方角色可以①：将一张红色手牌当作【杀】使用或打出；②：将一张黑色手牌当作【闪】使用或打出。“虹翼”每转化一次，你获得一枚“彩”标记。",
	["luafushen"] = "复生",
	["@luafushen"] = "请打出一张【闪】",
	[":luafushen"] = "出牌阶段，你可以打出一张【闪】，然后①：弃置三枚“彩”发动“鹮羽”；②：弃置七枚“彩”发动“苏生”。",

	["Yaezaki_An"] = "八重咲杏",
	["#Yaezaki_An"] = "木灵",	
	["luamuling"] = "木灵", 
	[":luamuling"] = "分发起始手牌时,你将其中任意数量的牌移出游戏,记为“杏”。",
	["luayunsong"] = "云松", 
	[":luayunsong"] = "你死亡结算后,你可以获得一张“杏”,复活并摸一张牌,你阵亡不触发奖惩。", 
	["luabaiyin"] = "白樱", 
	[":luabaiyin"] = "一名其他角色获得一张以上的手牌时,其可以将一张置于“杏”中，并使用余下的牌。",  
	["luajianju"] = "剑菊", 
	[":luajianju"] = "你的牌置于弃牌堆后，其中每有两张同花色的牌，你可以弃置一名角色的一张牌，对另一名角色造成一点伤害。", 
	["luajianju-invoke"] = "你可以发动“剑菊”<br/> <b>操作提示</b>: 选择一名角色来弃牌→点击确定<br/>",
	["luajianju-invokee"] = "你可以发动“剑菊”<br/> <b>操作提示</b>: 选择另外一名角色受到伤害→点击确定<br/>",
	["@luabaiyin"] = "你可以发动“白樱”(来自 八重咲杏 )",
	["~luabaiyin"] = "操作提示：点选你要给出的那张牌，再依次点选你要使用的牌，然后点击确定。",
	
	["anx"] = "杏",

	["fujix"] = "藤原",
	["#fujix"] = "恭贺新禧",
	["designer:fujix"] = "Paysage",
	["luabainian"] = "拜年",
	[":luabainian"] = "每回合限一次，一名角色对你造成伤害后，其可以与你各摸一张牌，若如此做，你回复一点体力。",
	["luagongxi"] = "恭禧",
	[":luagongxi"] = "出牌阶段，你可以弃置一张牌，令一名角色获得“春辉”。场上有“春辉”角色存活时，你不能发动“恭禧”。",
	["luachunhui"] = "春辉",
	[":luachunhui"] = "出牌阶段限一次，你可以将一张手牌当作任一多目标锦囊使用。锁定技，你受到伤害后，失去本技能。", 
	["luachunhui-invoke"] = "请选择【铁索连环】要指定的目标  <br/> <b>操作提示</b>: 只需选择一名角色→点击确定<br/>",

	["magahara"] = "祸原命庙",
	["illustrator:magahara"] = "明ノ宮 飛鳥",
    ["#magahara"]= "天满御灵",
	["designer:magahara"] = "Paysage",
    ["lualeigong"] = "雷弓",
    [":lualeigong"] = "锁定技，你不能使用武器牌，你的攻击范围+3。分发起始手牌后，你将所有雷【杀】置于武将牌上，每当你造成伤害的牌置于弃牌堆时，你从放置的牌中获得一张。",
    ["luadianli"] = "电力",
    [":luadianli"] = "出牌阶段限一次，你可以弃置一张【杀】，指定一名角色，其于下个回合结束时摸X+1张牌（X为此期间其造成的伤害数）。",	
}