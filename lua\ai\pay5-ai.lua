--hahaha

math.randomseed(tostring(os.time()):reverse():sub(1, 7)) --------设置时间种子


-- player:hasFlag("hasUsedSlash") 是否使用了杀
Pay = require "paysage" --加载价值模组 

sgs.ai_skill_invoke.Luashenqiang = sgs.ai_skill_invoke.liegong  --如何决定技能是否发动的一个实例

function sgs.ai_skill_pindian.LuaYouqu(minusecard, self, requestor, maxcard, mincard)
	
	local cards = sgs.QList2Table(self.player:getHandcards())
	local function compare_func1(a, b)
		return a:getNumber() > b:getNumber()
	end
	table.sort(cards, compare_func1)
	local table2 = {}
	if self.player:objectName() == requestor:objectName() then
		--self.room:writeToConsole("UUZ对队友测试")
		local table3 = {}
		local req 
		for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
			if p:hasFlag("LuaYouqu_Target") then
				req = p
				break
			end
		end
		if not req then self.room:writeToConsole("UUZ出严重bug") end 
		if req and self:isFriend(req) then 
			for _, card in ipairs(cards) do
				if card:getNumber() < 8 then table.insert(table3, card) end 
			end 	
			self:sortByKeepValue(table3)
			if #table3 > 0 then return table3[1] end 
			return self:getMinCard()
		end 
	end 
	if requestor and self:isFriend(requestor) then
		for _, card in ipairs(cards) do
			if card:getNumber() > 8 then table.insert(table2, card) end 
		end 
		self:sortByKeepValue(table2)
		if #table2 > 0 then return table2[1] end 
	elseif requestor then 
		for _, card in ipairs(cards) do
			if card:getNumber() > 10 then table.insert(table2, card) end 
		end 
		self:sortByKeepValue(table2)
		if #table2 > 0 then return table2[1] end 		
	end 
	return self:getMaxCard()
end

sgs.ai_skill_invoke.LuaYouqu = function(self, data)
	local target = self.room:getCurrentDyingPlayer()
	if not target then return false end 
	if target:isKongcheng() then return false end 
	local cards = sgs.QList2Table(self.player:getHandcards())
	local function compare_func1(a, b)
		return a:getNumber() > b:getNumber()
	end
	local table3 = {}
	table.sort(cards, compare_func1)
	if self:isFriend(target) then
		for _, friend in ipairs(self.friends) do
			if getCardsNum("Peach", friend) >= 1 then return false end
		end
		for _, card in ipairs(cards) do
			if card:getNumber() < 8 then table.insert(table3, card) end
		end
		self:sortByKeepValue(table3)
		local to_card
		if #table3 > 0 then to_card = table3[1] end
		if not to_card then to_card = self:getMinCard() end
		if self:getMinCard(target) and to_card:getNumber() <= self:getMinCard(target):getNumber() then return true end
		if self:getMaxCard(target) and to_card:getNumber() > self:getMaxCard(target):getNumber()
				and (self:getKnownNum(target) == target:getHandcardNum()) then return false end
		if to_card:isKindOf("Peach") then return false end
		if to_card:getNumber() > 10 then return false end
		if to_card:getNumber() > 8 then return (math.random() > 0.5) end
		if to_card:getNumber() > 6 then return (math.random() > 0.2) end
		if to_card:getNumber() <= 6 then return true end
	else
		if target:getMaxHp() == 1 then return false end
		local to_card = self:getMaxCard()
		local x = target:getHandcardNum()
		x = (x * x)/30

		local k = math.random()
		if self:getMinCard(target) and to_card:getNumber() <= self:getMinCard(target):getNumber() then return false end
		if self:getMaxCard(target) and to_card:getNumber() > self:getMaxCard(target):getNumber()
				and (self:getKnownNum(target) == target:getHandcardNum()) then return true end
		if to_card:getNumber() > 10 and (k > (0.1 + x)) then return true end
		if to_card:getNumber() > 8 and (k > (0.25 + x)) then return true end
		if to_card:getNumber() > 6 and (k > (0.6 + x)) then return true end
		if to_card:getNumber() <= 6 then return false end
	end 
end
local function decide_yuyuko(self) 
	local function Clear()
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			self.room:setPlayerFlag(p, "-luayouqu_source")
			self.room:setPlayerFlag(p, "-luayouqu_target")
			p:removeTag("luayouqu")
		end

	end
	local function GetAna(target)
		local ana
		for _, acard in sgs.qlist(target:getHandcards()) do
			if isCard("Analeptic", acard, target) and not target:isCardLimited(acard, sgs.Card_MethodUse) then ana = acard;break end
		end
		return ana
	end
	local function GetOfd(target)
		local ofd
		for _, acard in sgs.qlist(target:getHandcards()) do
			if isCard("Ofuda", acard, target) and not target:isCardLimited(acard, sgs.Card_MethodUse) then ofd = acard;break end
		end
		return ofd
	end
	local cards = self:getTurnUse(true)
	for _,card in ipairs(cards) do
		local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
		if card:isKindOf("BasicCard") then
			if card:isKindOf("Slash") then
				self:useBasicCard(card, dummy_use)
				if not dummy_use.to:isEmpty() then
					for _, playerX in sgs.qlist(dummy_use.to) do
						if GetAna(self.player) and self:shouldUseAnaleptic(playerX, card) then
							Clear()
							self.room:setPlayerFlag(self.player, "luayouqu_source")
							self.room:setPlayerFlag(self.player, "luayouqu_target")
							self.player:setTag("luayouqu", sgs.QVariant("Analeptic"))
							return self.player
						end
					end
					for _, playerX in sgs.qlist(dummy_use.to) do
						if GetOfd(self.player) and self:canBeOfudaTarget(playerX, GetOfd(self.player)) then
							Clear()
							self.room:setPlayerFlag(self.player, "luayouqu_source")
							self.room:setPlayerFlag(self.player, "luayouqu_target")
							self.player:setTag("luayouqu", sgs.QVariant("Ofuda"))
							return self.player
						end
					end
					Clear()
					self.room:setPlayerFlag(self.player, "luayouqu_source")
					self.room:setPlayerFlag(dummy_use.to:at(0), "luayouqu_target")
					if self:AtomDamageCount2(dummy_use.to:at(0), self.player, nil, sgs.Sanguosha:cloneCard("fire_slash")) > 0
							and (self:isGoodChainTarget(dummy_use.to:at(0), self.player, sgs.DamageStruct_Fire, 1, nil)) then
						self.player:setTag("luayouqu", sgs.QVariant("fire_slash"))
					else
						self.player:setTag("luayouqu", sgs.QVariant("Slash"))
					end
					return self.player
				end
			end
			if card:isKindOf("Peach") then
				Clear()
				self.room:setPlayerFlag(self.player, "luayouqu_source")
				self.room:setPlayerFlag(self.player, "luayouqu_target")
				self.player:setTag("luayouqu", sgs.QVariant("Peach"))
				return self.player
			end
		end
	end
	local iku = self.room:findPlayerBySkillName("Luayuyi")
	if iku and iku:isAlive() and self:isFriend(iku) and not iku:isNude() then
		return iku
	end
	local friend = self:Kitcho(nil, true, true, sgs.QList2Table(self.room:getOtherPlayers(self.player)))
	if friend and not friend:isNude() then
		Clear()
		self.room:setPlayerFlag(friend, "luayouqu_source")
		self.room:setPlayerFlag(friend, "luayouqu_target")
		self.player:setTag("luayouqu", sgs.QVariant("Slash"))
		return friend
	end
	for _, friendS in ipairs(self.friends_noself) do
		if friendS:getMark("@ofudaa") == 0 then
			if friendS:isWounded() then
				for _, cardQ in sgs.qlist(friendS:getHandcards()) do
					if not friendS:isCardLimited(cardQ, sgs.Card_MethodUse) then
						local flag = string.format("%s_%s_%s", "visible", friendS:objectName(), self.player:objectName())
						local yukari = false
						if friendS:hasSkill("luajinjie3") and cardQ:hasFlag("luajinjie") then
							local a,b,c = YUKARIN(cardQ)
							if "peach" == c then yukari = true end
						end
						if ((cardQ:hasFlag("visible") or cardQ:hasFlag(flag)) and cardQ:isKindOf("Peach")) or yukari then
							self.room:setPlayerFlag(friend, "luayouqu_source")
							self.room:setPlayerFlag(friend, "luayouqu_target")
							self.player:setTag("luayouqu", sgs.QVariant("Peach"))
							return friend
						end
					end
				end
			end
			if self:Skadi(sgs.Sanguosha:cloneCard("slash"), nil, friend, true) then
				for _, cardQ in sgs.qlist(friendS:getHandcards()) do
					if not friendS:isCardLimited(cardQ, sgs.Card_MethodUse) then
						local flag = string.format("%s_%s_%s", "visible", friendS:objectName(), self.player:objectName())
						local yukari = false
						if friendS:hasSkill("luajinjie3") and cardQ:hasFlag("luajinjie") then
							local a,b,c = YUKARIN(cardQ)
							if "ofuda" == c then yukari = true end
						end
						if ((cardQ:hasFlag("visible") or cardQ:hasFlag(flag)) and cardQ:isKindOf("Ofuda")) or yukari then
							self.room:setPlayerFlag(friend, "luayouqu_source")
							self.room:setPlayerFlag(friend, "luayouqu_target")
							self.player:setTag("luayouqu", sgs.QVariant("Ofuda"))
							return friend
						end
					end
				end
			end
			if self:Skadi(sgs.Sanguosha:cloneCard("slash"), nil, friend, true) then
				for _, cardQ in sgs.qlist(friendS:getHandcards()) do
					if not friendS:isCardLimited(cardQ, sgs.Card_MethodUse) then
						local flag = string.format("%s_%s_%s", "visible", friendS:objectName(), self.player:objectName())
						local yukari = false
						if friendS:hasSkill("luajinjie3") and cardQ:hasFlag("luajinjie") then
							local a,b,c = YUKARIN(cardQ)
							if "analeptic" == c then yukari = true end
						end
						if ((cardQ:hasFlag("visible") or cardQ:hasFlag(flag)) and cardQ:isKindOf("Analeptic")) or yukari then
							self.room:setPlayerFlag(friend, "luayouqu_source")
							self.room:setPlayerFlag(friend, "luayouqu_target")
							self.player:setTag("luayouqu", sgs.QVariant("Analeptic"))
							self.room:writeToConsole("yuyuko test Analeptic")
							return friend
						end
					end
				end
			end
		end
	end
	if self.player:isWounded() then return self.player end
	self:sort(self.friends, "defense")
	for _, friendW in ipairs(self.friends) do
		if not friendW:isNude() then
			return friendW
		end
	end
end
local luayouqu_skill = {} -- 初始化 LuaShengong_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 LuaShengong_skill 只是为了出于习惯
luayouqu_skill.name = "luayouqu" -- 设置 name
table.insert(sgs.ai_skills, luayouqu_skill) -- 把这个表加入到 sgs.ai_skills 中   self:HasGou(false, player)
luayouqu_skill.getTurnUseCard = function(self)
	if not self.player:hasUsed("#luayouqu") and self.player:getHandcardNum() < 7 then
		return sgs.Card_Parse("#luayouqu:.:")
	end
end
sgs.ai_skill_use_func["#luayouqu"] = function(cardD, use, self)
	local target = decide_yuyuko(self)
	if target and not target:isNude() then
		use.card = sgs.Card_Parse("#luayouqu:.:")
		if use.to then
			use.to:append(target)
			return
		end
	end
end
sgs.ai_skill_choice.luayouqu = function(self, choice)
	local canSlash = false
	if choice:match("slash") then canSlash = true end
	self.room:writeToConsole("yuyuko test AI" .. self.player:objectName())
	local str = self.player:getTag("luayouqu")
	if str then str = str:toString() end
	if str and str ~= "" and choice:match(string.lower(str)) then self.room:writeToConsole("yuyuko test AI Y" .. str);return string.lower(str) end
	for _, ecard in ipairs(self:getTurnUse(true)) do
		if ecard:isKindOf("BasicCard") then
			if ecard:isKindOf("Slash") and canSlash then
				self.room:writeToConsole("yuyuko test AI X" .. ecard:objectName())
				return ecard:objectName()
			end
			if not ecard:isKindOf("Slash") then
				self.room:writeToConsole("yuyuko test AI X" .. ecard:objectName())
				return ecard:objectName()
			end
		end
	end
	local target = self:Skadi(sgs.Sanguosha:cloneCard("fire_slash"), nil, self.player, true)
	local target2 = self:Skadi(sgs.Sanguosha:cloneCard("slash"), nil, self.player, true)
	self.room:writeToConsole("yuyuko test AI2" .. self.player:objectName())
	if target and (self:YouMu2(target, true) or self.player:getMark("drank") > 0)
			and self.player:getCards("he"):length() > 1 then
		for _, card in sgs.qlist(self.player:getHandcards()) do
			if card:isKindOf("Analeptic") and canSlash then
				return "analeptic"
			end
		end
	end
	if target and self.player:getCards("he"):length() > 1 then
		for _, card in sgs.qlist(self.player:getHandcards()) do
			if card:isKindOf("Ofuda") and canSlash then
				return "ofuda"
			end
		end
	end
	if target and self:YouMu2(target, true) and canSlash then return "fire_slash" end
	if target2 and self:YouMu2(target2, true) and canSlash then return "slash" end
	if target and (self:YouMu2(target, true) or self.player:getMark("drank") > 0) and canSlash then
		return "fire_slash"
	end
	if target2 and (self:YouMu2(target2, true) or self.player:getMark("drank") > 0) and canSlash then
		return "slash"
	end
	if self:isWeak() then return "peach" end
	if target and canSlash then return "fire_slash" end
	if target2 and canSlash then return "slash" end
	if self.player:isWounded() then return "peach" end
	return "hui"
end

sgs.ai_skill_cardask["@luayouqu"] = function(self, data, pattern, target)
	local str = data:toString()
	if str == "hui" then return "." end
	if self.player:isNude() then return "." end
	for _, card in sgs.qlist(self.player:getHandcards()) do
		if card:isKindOf("Slash") and (str == "fire_slash" or str == "thunder_slash" or str == "slash") then return "$" .. card:getEffectiveId() end
	end
	for _, card in sgs.qlist(self.player:getHandcards()) do
		if card:isKindOf("Analeptic") and (str == "analeptic") then return "$" .. card:getEffectiveId() end
	end
	for _, card in sgs.qlist(self.player:getHandcards()) do
		if card:isKindOf("Peach") and (str == "peach") then return "$" .. card:getEffectiveId() end
	end
	for _, card in sgs.qlist(self.player:getHandcards()) do
		if card:isKindOf("Ofuda") and (str == "ofuda") then return "$" .. card:getEffectiveId() end
	end
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	self:sortByKeepValue(cards)
	local skills = self.player:getVisibleSkillList(true)
	for _,skill in sgs.qlist(skills) do
		local callback = sgs.ai_cardneed[skill:objectName()]
		if type(callback) == "function" then
			if callback(self.player, cards[1], self) then
				return "."
			end
		end
	end
	if cards[1]:isKindOf("Duel") or cards[1]:isKindOf("ExNihilo") or cards[1]:isKindOf("Indulgence")
		or cards[1]:isKindOf("Indulgence") or cards[1]:isKindOf("Wanbaochui") then
		return "."
	end
	return "$" .. cards[1]:getEffectiveId()
end
sgs.ai_skill_playerchosen.luayouqu = function(self, targets)
	local targetlist = sgs.QList2Table(targets)
	local Acard = self.player:getTag("luayouquTC"):toCard()
	if #targetlist == 1 then return targetlist[1] end
	for _, target in ipairs(targetlist) do	--杀敌
		if target:hasFlag("luayouqu_target") then return target end
	end
	for _, target in ipairs(targetlist) do	--杀敌
		if (Acard:isKindOf("Slash") or Acard:isKindOf("Ofuda")) and self:Skadi2(Acard, target, self.player, true)
			and self:isEnemy(target) then return target end
	end
	return
end
sgs.ai_skill_choice.luayouqu2 = function(self, targets)
	local acards = self:getTurnUse(true)
	if #acards > 0 then return "luayouqu" end
	self:sort(self.friends_noself, "handcard")
	for _, friend in ipairs(self.friends_noself) do
		if friend:getHandcardNum() > 2 and friend:getMark("@ofudaa") == 0 then
			return "luayouqu"
		end
		if friend:getMark("drank") > 0 then
			return "luayouqu"
		end
	end
	for _, enemy in ipairs(self.enemies) do
		if enemy:getMark("@ofudaa") > 0 then
			return "luayouqu"
		end
	end
	return "draw"
end
sgs.ai_use_priority.luayouqu = 3.9
sgs.ai_card_intention.luayouqu = -40
sgs.ai_skill_invoke.luafanhun = function(self, data)
	local dying = data:toDying()
	local _player = dying.who
	if self:isFriend(_player) then
		local x = math.max(_player:getMark("@luafanhun") - 2, 0)
		if x * x / 10 > 1 - math.random() then return true end
		if self:HasGou(true, _player) then return false end
		if self.player:getMark("@luafanhun") <= 1 and self.player:getHp() < 3 then return false end
		if x + _player:getHandcardNum() < 8 - self.player:getHp() then return false end
	else
		local x = _player:getMark("@luafanhun") - 2
		if x < 2 then return true end
		if x + _player:getHandcardNum() < 4 then return true end
		if self.player:getMark("@luafanhun") <= 1 and self.player:getHp() < 3 then return false end
	end
	return true
end



function if_can_use(self, card_0, target, card_X2)
	local all_trick = {}
	if not card_0 then return all_trick end 
	local delay_trick = {}
	local sttrick = {}
	local mttrick = {}
	local patterns = {"snatch", "dismantlement", "collateral", "ex_nihilo", "duel", "fire_attack", "faith_collection", "banquet",
	"amazing_grace", "savage_assault", "archery_attack", "god_salvation", "iron_chain", "supply_shortage", "lightning", "indulgence"}
	local function YouMuCheck(card, target2)
		if card:isKindOf("Hui") or card:isKindOf("Ofuda") then
			return true
		elseif card:isKindOf("FaithCollection") then
			return not target2:isNude()
		elseif card:isKindOf("Banquet") then
			return not target2:containsTrick("banquet")
		end
	end
			
	for _, cd in ipairs(patterns) do
		local card = sgs.Sanguosha:cloneCard(cd, card_0:getSuit(), card_0:getNumber())
		card:addSubcard(card_0)
		card:setSkillName("Lualianji")				
		local qtargets = sgs.PlayerList()
		if card and (card:targetFilter(qtargets, target, self.player) or YouMuCheck(card, target)) and not self.player:isProhibited(target, card, qtargets) then	
			qtargets:append(target)
			card:deleteLater()
			if card:isAvailable(self.player) and (cd ~= card_X2:objectName()) and card:targetsFeasible(qtargets, self.player) then
				if card:isKindOf("DelayedTrick") then
					table.insert(delay_trick, cd)
				elseif (card:isKindOf("SingleTargetTrick") and not card:isKindOf("DelayedTrick")) then
					table.insert(sttrick, cd)
				else
					table.insert(mttrick, cd)
				end
			end
		end
	end
	for _, cd in ipairs(delay_trick) do
		table.insert(all_trick, cd)
	end 
	for _, cd in ipairs(sttrick) do
		table.insert(all_trick, cd)
	end 
	for _, cd in ipairs(mttrick) do
		table.insert(all_trick, cd)
	end 
	--local x = #delay_trick + #sttrick + #mttrick
	return all_trick
end 
function check_to_enermy(table_A, target)
	return table.contains(table_A, "duel") or (table.contains(table_A, "snatch") and not target:isNude()) or (table.contains(table_A, "faith_collection") and not target:isNude())
		or table.contains(table_A, "supply_shortage") or table.contains(table_A, "indulgence")
end 
function check_to_friend(table_A, target)
	return table.contains(table_A, "ex_nihilo") or table.contains(table_A, "banquet")
		or (table.contains(table_A, "snatch") and (target:containsTrick("gaina") or target:containsTrick("indulgence") or target:containsTrick("supply_shortage")))
		or (table.contains(table_A, "dismantlement") and (target:containsTrick("gaina") or target:containsTrick("indulgence") or target:containsTrick("supply_shortage")))
		or (table.contains(table_A, "faith_collection") and (target:containsTrick("gaina") or target:containsTrick("indulgence") or target:containsTrick("supply_shortage")))
end 
function Find_Subcard(self, target, card_X2)  --用于找出帕秋莉技能子卡

	local cardid = card_X2:getId()
	local allcards = {}
	local handcards = self.player:getHandcards()
	local x_num = self:getCardsNum("Jink")
	local y_num = self:getCardsNum("Peach")
	local a_num = self:getCardsNum("Analeptic")
	local z_num = y_num + self.player:getHp()
	
	local function check_R(card)
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return false end
		end
		if (z_num <= self.player:getMaxHp()) and card:isKindOf("Peach") then return false end 
		if (z_num == 1) and card:isKindOf("Analeptic") and (a_num == 1) then return false end 
		if card:isKindOf("ExNihilo") and card:isAvailable(self.player) then return false end
		if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
		if card:isKindOf("Indulgence") then return false end
		if card:isKindOf("Wanbaochui") then return false end
		return true 
	end 

	local allcards_A = {}
	local allcards_B = {}  --牌里的非锦囊牌
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		local table_X = if_can_use(self, c, target, card_X2)
		if (c:getSuitString() == card_X2:getSuitString()) and (#table_X > 0) and (c:getId() ~= cardid)
			and check_R(c) then table.insert(allcards, c) end 
	end
	local equips = self.player:getEquips()
	for _,c in sgs.qlist(equips) do
		local table_X = if_can_use(self, c, target, card_X2)
		if (c:getSuitString() == card_X2:getSuitString()) and (#table_X > 0) and (c:getId() ~= cardid)
			and check_R(c) then table.insert(allcards, c) end 
	end	--得到所有牌数量		
	if #allcards == 0 then return false end 
	for _, c in ipairs(allcards) do
		if c:isKindOf("TrickCard") then 
			table.insert(allcards_A, c)
		else
			table.insert(allcards_B, c)
		end 
	end 
	if #allcards_B ~= 0 then 
		self:sortByKeepValue(allcards_B)	
		local to_card = allcards_B[1]
		local to_card2 
		if #allcards_B > 1 then to_card2 = allcards_B[2] end 
		local table_X = if_can_use(self, to_card, target, card_X2)
		local table_X2 = if_can_use(self, to_card2, target, card_X2)  --我就简化运算两次了
		if self:isFriend(target) and check_to_friend(table_X, target) then return to_card end 
		if self:isFriend(target) and check_to_friend(table_X2, target) then return to_card2 end 
		if not self:isFriend(target) and check_to_enermy(table_X, target) then return to_card end 
		if not self:isFriend(target) and check_to_enermy(table_X2, target) then return to_card2 end 	
	else
		if #allcards_A ~= 0 then 
			self:sortByUseValue(allcards_A, true) -- 按使用价值从小到大排列卡牌
			if not allcards_A[1]:isKindOf("ExNihilo") and not allcards_A[1]:isKindOf("Indulgence") and not allcards_A[1]:isKindOf("FaithCollection") and not allcards_A[1]:isKindOf("Banquet")
				and not allcards_A[1]:isKindOf("SupplyShortage") and not allcards_A[1]:isKindOf("Duel") then 
				local table_X = if_can_use(self, allcards_A[1], target, card_X2)
				if self:isFriend(target) and check_to_friend(table_X, target) then return allcards_A[1] end 
				if not self:isFriend(target) and check_to_enermy(table_X, target) then return allcards_A[1] end 
			end 
		end 
	end 
end 

sgs.ai_skill_cardask["@Lualianji"] = function(self, data)  --有关于铁索连环的重要性，这里偷懒没写 2019年1月25日18:24:37
	local card_type = data:toString()
	local target = self.room:getTag("LualianjiTarget")
	if (not target) or (not target:toPlayer()) or (not target:toPlayer():objectName()) then return "." end  
	target = target:toPlayer()
	local card_X2 = self.room:getTag("LualianjiTargetC2"):toCard()
	local card = Find_Subcard(self, target, card_X2) 
	if card then return "$" .. card:getEffectiveId() end 
	return "."
end
function to_select_choice(self)  --@todo 我没有考虑闪电
	local target = self.room:getTag("LualianjiTarget")
	local card_0 = self.room:getTag("LualianjiTargetC"):toCard()
	local card_X2 = self.room:getTag("LualianjiTargetC2"):toCard()
	target = target:toPlayer()
	local table_X = if_can_use(self, card_0, target, card_X2)
	if self:isFriend(target) then 
		if table.contains(table_X, "banquet") and self:getOverflow() > 0 and getCardsNum("Slash", target, self.player) >= 1 and #self.friends > 1 then return "banquet" end 
		if table.contains(table_X, "ex_nihilo") then return "ex_nihilo" end 
		if (table.contains(table_X, "snatch") and (target:containsTrick("gaina") or target:containsTrick("indulgence") or target:containsTrick("supply_shortage"))) then return "snatch" end 
		if (table.contains(table_X, "faith_collection") and (target:containsTrick("gaina") or target:containsTrick("indulgence") or target:containsTrick("supply_shortage"))) then return "dismantlement" end 
		if (table.contains(table_X, "dismantlement") and (target:containsTrick("gaina") or target:containsTrick("indulgence") or target:containsTrick("supply_shortage"))) then return "dismantlement" end 
	else		
		if table.contains(table_X, "duel") and self:use_duel(target) then return "duel" end 
		if table.contains(table_X, "snatch") then return "snatch" end 	
		if table.contains(table_X, "indulgence") and self:getOverflow(target) > 2 then return "indulgence" end 	 
		if table.contains(table_X, "dismantlement") and target:getEquips():length() > 0 then return "dismantlement" end 			
		if table.contains(table_X, "faith_collection") then return "faith_collection" end 	 
		if table.contains(table_X, "dismantlement") then return "dismantlement" end 	
		if table.contains(table_X, "supply_shortage") and self:isWeak(target) then return "supply_shortage" end 	
		if table.contains(table_X, "indulgence") then return "indulgence" end 		
		if table.contains(table_X, "supply_shortage") then return "supply_shortage" end 	
		if table.contains(table_X, "iron_chain") and not target:isChained() then return "iron_chain" end 			
	end 
end 
sgs.ai_skill_choice.Lualianji = function(self, choices)
	local pattern = to_select_choice(self)
	local card = sgs.Sanguosha:cloneCard(pattern, sgs.Card_NoSuit, 0)
	if card:isKindOf("DelayedTrick") then
		return "delay_trick"
	elseif (card:isKindOf("SingleTargetTrick") and not card:isKindOf("DelayedTrick")) then
		return "single_target_trick"
	else
		return "multiple_target_trick"
	end
end 

sgs.ai_skill_choice.LualianjiX = function(self, choices)
	local choice = to_select_choice(self)
	return choice
end 

local Luaxianzhe_skill = {}
Luaxianzhe_skill.name = "Luaxianzhe"
table.insert(sgs.ai_skills, Luaxianzhe_skill)
function subsubcard(self, card, basiccardX)
	self.room:writeToConsole("patchouli long" .. #basiccardX)
	for _, c in ipairs(basiccardX) do
		if (c:getSuit() == card:getSuit()) then return true end 
	end 
	self.room:writeToConsole("patchouli suit ")
	return false 
end 
function Patchouli(self)
	local allcards = sgs.QList2Table(self.player:getHandcards())
	local allcards_2 = {}
	local trickcards = {}
	local pcards = {}
	local alive_p_c = self.room:getAlivePlayers():length() - 1
	for _, card in ipairs(allcards) do		
		if card:isKindOf("TrickCard") then
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			self:useTrickCard(card, dummy_use)
			if dummy_use.card then 
				table.insert(trickcards, card) 
			end
			if dummy_use.to and dummy_use.to:length() > 0 then 
				self.room:writeToConsole("patchouli gonna target" .. dummy_use.to:at(0):objectName())
			end 
		end	
	end 
	local enermy = self.enemies  --这里注意一下
	self:sort(enermy, "defense")
	--if #enrmy = 0 then 
	for _, card in ipairs(trickcards) do		
		local pcard
		if card:isKindOf("ExNihilo") or card:isKindOf("Lightning") then 
			pcard = Find_Subcard(self, self.player, card) 
		elseif (card:isKindOf("AOE") and (self:getAoeValue(card) > 0) and (alive_p_c == 1))
			or (card:isKindOf("IronChain")) 
			or ((card:isKindOf("SingleTargetTrick") and #enermy > 0)) then 
			pcard = Find_Subcard(self, enermy[1], card) 
		end 		 
		if pcard then table.insert(pcards, pcard) end 
	end      --先将手牌两两配对
	local discard_ids = self.room:getDiscardPile()
	local friends = self.friends_noself
	local trickcard = sgs.IntList()
	local basiccard = sgs.IntList()
	for _, id in sgs.qlist(discard_ids) do 
		local card = sgs.Sanguosha:getCard(id)
		if card:isKindOf("TrickCard") then 
			trickcard:append(id)
		elseif (card:isKindOf("BasicCard") and not card:isKindOf("Peach")) then 
			basiccard:append(id)
		end 
	end 

	for _, friend in ipairs(friends) do
		if friend:hasSkill("Luasishu") and friend:faceUp() and (trickcard:length() > 0) then return trickcard[1] end 
	end 
	if (trickcard:length() == 0) or (basiccard:length() == 0) then return false end 
	local trickcardX = {}
	local basiccardX = {}
	for _, id in sgs.qlist(trickcard) do 
		local card = sgs.Sanguosha:getCard(id)
		table.insert(trickcardX, card)
	end 
	for _, id in sgs.qlist(basiccard) do 
		local card = sgs.Sanguosha:getCard(id)
		table.insert(basiccardX, card)
	end 
	self:sortByUseValue(trickcardX)
	self:sortByUseValue(basiccardX)
		-- for _, askill in ipairs(("manjuan|nostuxi|tuxi|dimeng|haoshi|guanxing|zhiheng|qiaobian|qice|noslijian|lijian|neofanjian|shuijian|shelie|xunxun|luoshen|" ..
		-- "yongsi|shude|biyue|yingzi|qingnang|caizhaoji_hujia|dujin|yyinyang"):split("|")) do	--yun
			-- if str:matchOne(askill) then return askill end
		-- end

	for _, card in ipairs(trickcardX) do
		if alive_p_c == 1 then 
			if card:isKindOf("SavageAssault") and card:isAvailable(self.player) and subsubcard(self, card, basiccardX) then return card end 
			if card:isKindOf("ArcheryAttack") and card:isAvailable(self.player) and subsubcard(self, card, basiccardX) then return card end 
		end 
	end 
	for _, card in ipairs(trickcardX) do
		if card:isKindOf("Duel") and self:ceshi(card, false, true) and subsubcard(self, card, basiccardX) then return card end 
	end 
	for _, card in ipairs(trickcardX) do
		if card:isKindOf("Snatch") and self:ceshi(card, true) and subsubcard(self, card, basiccardX) then return card end 
	end 
	for _, card in ipairs(trickcardX) do
		if card:isKindOf("Banquet") and subsubcard(self, card, basiccardX) and #self.friends > 1 then return card end 
	end 
	for _, card in ipairs(trickcardX) do
		if card:isKindOf("FaithCollection") and self:ceshi(card, true) and subsubcard(self, card, basiccardX) then return card end 
	end 
	for _, card in ipairs(trickcardX) do
		if card:isKindOf("Dismantlement") and self:ceshi(card, true) and subsubcard(self, card, basiccardX)  then return card end 
	end 
	for _, card in ipairs(trickcardX) do
		if card:isKindOf("Snatch") and self:ceshi(card) and subsubcard(self, card, basiccardX) then return card end 
	end 
	for _, card in ipairs(trickcardX) do
		if card:isKindOf("Duel") and self:ceshi(card) and subsubcard(self, card, basiccardX) then return card end 
	end 
	for _, card in ipairs(trickcardX) do
		if card:isKindOf("Indulgence") and self:ceshi(card, false, false, true) and subsubcard(self, card, basiccardX) then return card end 
	end 
	for _, card in ipairs(trickcardX) do
		if card:isKindOf("SupplyShortage") and self:ceshi(card) and subsubcard(self, card, basiccardX) then return card end 
	end 
	for _, card in ipairs(trickcardX) do
		if card:isKindOf("Indulgence") and self:ceshi(card) and subsubcard(self, card, basiccardX) then return card end 
	end 
	for _, card in ipairs(trickcardX) do		
		if card:isKindOf("ExNihilo") and card:isAvailable(self.player) then return card end 
		if alive_p_c == 1 then 
			if card:isKindOf("SavageAssault") and card:isAvailable(self.player) then return card end 
			if card:isKindOf("ArcheryAttack") and card:isAvailable(self.player) then return card end 
		end 		
		if card:isKindOf("SavageAssault") and card:isAvailable(self.player) and (self:getAoeValue(card) > 99) then return card end 
		if card:isKindOf("ArcheryAttack") and card:isAvailable(self.player) and (self:getAoeValue(card) > 99) then return card end 
		if card:isKindOf("Dismantlement") and self:ceshi(card) and subsubcard(self, card, basiccardX) then return card end 
		local cards = {card}
		local cardX, friend = self:getCardNeedPlayer(cards)
		if cardX and friend and (self:getCardsNum("Jink") < 1) then return true end  -- 这里我没处理基本牌，太累了	
	end
	return false 	
end
local function Check(self)
	local discard_ids = self.room:getDiscardPile()
	local trickcard = sgs.IntList()
	local basiccard = sgs.IntList()
	for _, id in sgs.qlist(discard_ids) do
		local card = sgs.Sanguosha:getCard(id)
		if card:isKindOf("TrickCard") then
			trickcard:append(id)
		elseif (card:isKindOf("BasicCard") and not card:isKindOf("Peach")) then
			basiccard:append(id)
		end
	end
	if not basiccard:isEmpty() then
		for _, id in sgs.qlist(discard_ids) do
			local card = sgs.Sanguosha:getCard(id)
			if card:isKindOf("Indulgence") or card:isKindOf("ExNihilo")
					or card:isKindOf("SupplyShortage") or card:isKindOf("Snatch") then
				return true
			end
		end
	end
	return false
end
Luaxianzhe_skill.getTurnUseCard = function(self)	
	if self.player:hasUsed("#Luaxianzhe") then return end 
	if not self.player:faceUp() then return sgs.Card_Parse("#Luaxianzhe:.:") end 
	local card = Patchouli(self)
	if not Check(self) then return end
	if card and (#self.enemies > 0) and ((type(card) == "boolean") or (not card:isKindOf("ExNihilo"))) or (self.player:getHandcardNum() < 3) then 
		return sgs.Card_Parse("#Luaxianzhe:.:") 
	end 
	return 
end 
sgs.ai_skill_use_func["#Luaxianzhe"] = function(card, use, self)
	use.card = sgs.Card_Parse("#Luaxianzhe:.:") 
	if use.to then use.to:append(self.player) end
	return	
end 
sgs.ai_skill_askforag.LuaxianzheT = function(self, card_ids)
	local LuaxianzheT = card_ids
	local cards = {}
	for _, id in ipairs(LuaxianzheT) do
		local card = sgs.Sanguosha:getCard(id)
		table.insert(cards, card)
	end
	self:sortByUseValue(cards)
	local card = Patchouli(self)
	if not card then 
		return cards[1]:getId() 
	else
		if type(card) == "boolean" then
			local booldata = sgs.QVariant() -- ai用
			if card then 
				booldata:setValue(1)
			else
				booldata:setValue(0)
			end 
			self.room:setTag("LuaxianzheT", booldata)			
		end 
	end  
	if type(card) ~= "boolean" then
		return card:getId()
	end 
	if math.random() < 0.09 then self.player:speak("我都玩了⑨年了，技术不比你强？") end 
	return cards[1]:getId()
end

sgs.ai_skill_askforag.LuaxianzheB = function(self, card_ids)   --return nil, -1
	local LuaxianzheB = card_ids
	local cardX = self.room:getTag("LuaxianzheTC"):toCard()
	local target = self.room:getTag("LuaxianzheT")
	if target and (target:toInt() == 1) then 
		target = true
	else
		target = false 
	end 
	local cards = {}
	for _, id in ipairs(LuaxianzheB) do
		local card = sgs.Sanguosha:getCard(id)
		table.insert(cards, card)
	end
	self:sortByUseValue(cards)
	if not target then 
		for _, card in ipairs(cards) do
			if card:getSuit() == cardX:getSuit() and card:isAvailable(self.player) then return card:getId() end 
		end 
	end 
	return cards[1]:getId()
end

sgs.ai_skill_askforyiji.Luaxianzhe = function(self, card_ids)   --Luayuyi
	local target = self.room:getTag("LuaxianzheT")
	if target and (target:toInt() == 1) then 
		target = true 
	else
		target = false 
	end 
	if target then
		 return sgs.ai_skill_askforyiji.nosyiji(self, card_ids)
	else
		return nil, -1
	end 
end
sgs.ai_cardneed.Luaxianzhe = function(to, card)
	return card:isKindOf("SingleTargetTrick")
end
sgs.ai_skill_invoke.Luayuyi = true
sgs.ai_skill_discard.Lualeidian = function(self, discard_num, min_num, optional, include_equip)
	local invoke = false
	for _, to in ipairs(self.enemies) do
		if self:damageIsEffective(to, sgs.DamageStruct_Thunder, self.player) then
			invoke = true
		end
	end
	if not invoke then return {} end
	if self.player:getHandcardNum() == (self.player:getMaxCards() + 1) then
		local to_discard = {} -- 初始化 to_discard 为空表
		-- 这一句不可省略，否则 table.insert(to_discard, ...) 会报错
		local cards = sgs.QList2Table(self.player:getHandcards())	
		self:sortByKeepValue(cards)
		table.insert(to_discard, cards[1]:getId())
		for _, to in ipairs(self.enemies) do
			if self:damageIsEffective(to, sgs.DamageStruct_Thunder, self.player) then
				return to_discard
			end
		end
	else
		return {} 
	end 
end 
sgs.ai_skill_playerchosen.Lualeidian = function(self, targets)
	self:sort(self.enemies, "defense")
	for _, to in ipairs(self.enemies) do
		if targets:contains(to) and self:damageIsEffective(to, sgs.DamageStruct_Thunder, self.player) then
			return to
		end
	end
end 

sgs.ai_slash_prohibit.Lualeidian = function(self, from, to, card) --求求你们别狂杀满牌19了，我被电傻了
	if self:isFriend(to, from) then return false end
	if self:getOverflow(from) > 0 and to:getHandcardNum() < 2 then return false end
	if from:hasSkill("tieji") or self:canLiegong(to, from) then
		return false
	end
	if (to:getHandcardNum() == to:getMaxCards()) and (math.random() > 0.2) then return true end
	return false
end

sgs.ai_playerchosen_intention.Lualeidian = 40
sgs.ai_use_priority.Luaxianzhe = 12


local lualongxi_skill = {}
lualongxi_skill.name = "lualongxi"
table.insert(sgs.ai_skills, lualongxi_skill)

lualongxi_skill.getTurnUseCard = function(self)
	if not self.player:containsTrick("gaina") then
		if self.player:hasSkill("Lualeidian") then return sgs.Card_Parse("#lualongxi:.:") end
		local yuyuko = self.room:findPlayerBySkillName("LuaWangwu")
		if yuyuko and self.player:getHp() then return end
		local x = self.player:getHp() - self.player:getHandcardNum()
		if x >= 2 then return sgs.Card_Parse("#lualongxi:.:") end
	end 
end 

sgs.ai_skill_use_func["#lualongxi"] = function(card, use, self)
	use.card = sgs.Card_Parse("#lualongxi:.:") 
	if use.to then use.to:append(self.player) end
	return	
end
sgs.ai_cardneed.lualongxi = function(to, card)
	return isCard("Peach", card, to)
end
sgs.pay_ai_card.Peach.lualongyan = function(self, card, use)
	if self.player:hasSkill("lualongxi") then	--yun
		if (self.player:getHandcardNum() <= self.player:getHp()) or self:hasCrossbowEffect() then 
			return 1
		end
		local has_weak_f = false
		for _, friend in ipairs(self.friends_noself) do
			if self:isWeak(friend) then has_weak_f = true end
		end
		if not has_weak_f then 
			return 1
		end
	end
end 

sgs.ai_skill_invoke.LuaTaiji = function(self, data)
	local x = self.player:getHandcardNum()
	local cards = sgs.QList2Table(self.player:getHandcards())
	local sakuya = self.room:findPlayerBySkillName("luashiji")
	if x == 0 then return true end
	if sakuya and sakuya:isAlive() and self:isFriend(sakuya) then return true end
	if self.player:containsTrick("gainb") then return true end
	if x > self.player:getHp() then
		for _, card in ipairs(cards) do
			if card:isKindOf("Peach") then return false end
			if card:isKindOf("GodSalvation") then return false end
			if card:isKindOf("ExNihilo") then return false end
		end
		if #cards >= 3 then return false end
		if self.player:isWounded() then return true end
	elseif x == self.player:getHp() then
		for _, card in ipairs(cards) do
			if card:isKindOf("ExNihilo") then return false end
			if card:isKindOf("Peach") then return false end
			if card:isKindOf("GodSalvation") and self.player:isWounded() then return false end
		end
		if #cards >= 2 then return false end
		if (#cards >= 1) and self.player:getHp() <= 2 then return false end
		return true
	else
		for _, card in ipairs(cards) do
			if card:isKindOf("Peach") and card:isAvailable(self.player) and self.player:isWounded() then return false end
			if card:isKindOf("Analeptic") and card:isAvailable(self.player) and self.player:getHp() == 1 then return false end
			if card:isKindOf("Armor") and (not self.player:getArmor()) and self.player:getHp() == 1 then return false end
		end
		local target
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		for _, enemy in ipairs(self.enemies) do
			if self.player:canSlash(enemy, slash, true) and self:slashIsEffective(slash, enemy, self.player)
					and not self:slashProhibit(slash, enemy) and not (enemy:hasSkill("anxian") and not enemy:isKongcheng()) then target = enemy;break
			end
		end
		if not target then return false end
		if x < (self.player:getHp() - 1) then return true end
		return false
	end
	return false
end 
sgs.ai_skill_playerchosen.LuaTaiji = function(self, targets)
	self:sort(self.enemies, "defenseSlash")
	local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
	for _, to in ipairs(self.enemies) do
		if targets:contains(to) and self.player:canSlash(to, slash, true) and self:slashIsEffective(slash, to, self.player)
				and not self:slashProhibit(slash, to) and not (to:hasSkill("anxian") and not to:isKongcheng()) then
			return to
		end
	end
end
function sgs.ai_cardneed.LuaTaiji(to, card, self)
	return isCard("Crossbow", card, to)
end
 



sgs.ai_skill_use["@@LuaFeixiang"] = function(self, prompt) -- 我有一个绝妙而大胆的想法
	if #self.enemies == 0 then return end 
	local nude = true
	for _, to in ipairs(self.enemies) do
		if not to:isNude() then nude = false end 
	end 
	if nude then return end 
	self:sort(self.enemies, "defense")
	local trick = sgs.Sanguosha:cloneCard("LuaYao", sgs.Card_NoSuit, -1)
	local dummyuse = { isDummy = true, to = sgs.SPlayerList() }
	self:useCardSnatchOrDismantlement(trick, dummyuse)
	if not dummyuse.to:isEmpty() then
		return "#LuaFeixiang:.:->" .. dummyuse.to:at(0):objectName()
	else
		for _, to in ipairs(self.enemies) do
			if not to:isNude() then return "#LuaFeixiang:.:->" .. to:objectName() end 
		end 
	end 
end

sgs.ai_choicemade_filter.cardChosen.LuaFeixiang = sgs.ai_choicemade_filter.cardChosen.snatch

local LuaJiaosi_skill = {}
LuaJiaosi_skill.name = "LuaJiaosi"
table.insert(sgs.ai_skills,LuaJiaosi_skill)

function JiaosiCard(self)
	local cards = self.player:getCards("h")  --先查手卡
	cards = sgs.QList2Table(cards)
	local jink_card
	self:sortByUseValue(cards,true)

    local function TSSuit(card)
        for _,id in sgs.qlist(self.player:getPile("qizhi2")) do
            if sgs.Sanguosha:getCard(id):getSuit() == card:getSuit() then return true end
        end
        return false
    end

	local cardP = self:getTurnUse(true)
	local function YiJiSi(card)
		local duel = sgs.Sanguosha:cloneCard("duel", card:getSuit(), card:getNumber())
		for _, to in ipairs(self.enemies) do
			if not self.player:isProhibited(to, duel) and to:getHp() <= 1 and not self:HasGou(true, to) then
				return true
			end
		end

		local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
		local any = self:Skadi(slash)
		local xpp = self:AtomDamageCount2(any, self.player, false, slash)
		if any and any:getHp() <= xpp and not self:HasGou(true, any) then return true end
		return false 
	end 
	for _,card in ipairs(cards) do
		local jinks = self:getCards("Jink")
		if (not (card:isKindOf("Jink") and (#jinks == 1))) and (not card:isKindOf("ExNihilo")) and (not card:isKindOf("Wanbaochui"))
			and (not (card:isKindOf("Peach") and (not self:OverFlowPeach(card)))) and TSSuit(card) then
			
			jink_card = card
			break
		end
	end	
	if not jink_card then 
		for _,card in ipairs(cards) do
			if (not card:isKindOf("ExNihilo") and (not card:isKindOf("Wanbaochui"))) and YiJiSi(card) and TSSuit(card) then
				jink_card = card
				break
			end
		end		
	end 
	if not jink_card then 
		local cards_0 = self.player:getCards("he")  --再查装备
		cards_0 = sgs.QList2Table(cards_0)
		self:sortByKeepValue(cards_0)
		for _,card in ipairs(cards_0) do
			local jinks = self:getCards("Jink")
			if (not (card:isKindOf("Jink") and (#jinks == 1))) and (not card:isKindOf("ExNihilo")) and (not card:isKindOf("Wanbaochui"))
				and (not (card:isKindOf("Peach") and (not self:OverFlowPeach(card)))) and TSSuit(card) then
				jink_card = card
				break
			end
		end	
		if not jink_card then 
			for _,card in ipairs(cards_0) do
				local jinks = self:getCards("Jink")
				if (not card:isKindOf("ExNihilo") and (not card:isKindOf("Wanbaochui"))) and YiJiSi(card) and TSSuit(card) and (#jinks > 1 or self.player:getHp() > 2) then
					jink_card = card
					break
				end
			end		
		end 
	end 
	if jink_card and self.room:getCardPlace(jink_card:getId()) == sgs.Player_PlaceHand and jink_card:isKindOf("EquipCard") then
		for _,card in ipairs(cardP) do
			if card:getId() == jink_card:getId() then self.room:writeToConsole("天子需要先穿装备") ; return end 
		end
	end 
	return jink_card
end 
function JiaosiTarget(self, card)
	local targets

	local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
	if not targets then
		targets = sgs.SPlayerList()
		local any = self:Skadi(slash)
		if any then
			targets:append(any)
			return targets
		end
	end
	if (self.player:getMark("LuaJiaosiD") < 1) then
		local duel = sgs.Sanguosha:cloneCard("duel", card:getSuit(), card:getNumber())
		for _, to in ipairs(self.enemies) do
			if not self.player:isProhibited(to, duel) then
				targets:append(to)
				return targets
			end
		end
	end
	
	return targets
end 
LuaJiaosi_skill.getTurnUseCard = function(self)
	if (not self.player:getPile("qizhi2")) or (self.player:getPile("qizhi2"):isEmpty()) then return end 
	if (self.player:getMark("LuaJiaosiD") >= 1) and (self.player:getMark("LuaJiaosiS") >= 1) then return end
	if self.player:getMark("LuaJiaosiAI") > 4 then return end
	local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
	local duel = sgs.Sanguosha:cloneCard("duel", sgs.Card_SuitToBeDecided, -1)
	if (not slash:isAvailable(self.player)) and not duel:isAvailable(self.player) then return end
	return sgs.Card_Parse("#LuaJiaosi:.:")
end
sgs.ai_skill_use_func["#LuaJiaosi"] = function(cardF, use, self)
	local card = JiaosiCard(self)
	if not card then return end
	self.room:setPlayerMark(self.player, "LuaJiaosiAI", self.player:getMark("LuaJiaosiAI") + 1)
	local targets = JiaosiTarget(self, card)
	if card and targets then
		use.card = sgs.Card_Parse("#LuaJiaosi:" .. card:getEffectiveId()..":")	
		if use.to then
			use.to = targets
			return
		end 
	end 
end

sgs.ai_skill_askforag["LuaFeixiang"] = function(self, card_ids)
	local cards = {}
	for i, card_id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(card_id)
		table.insert(cards, card)
	end
	if self.player:getPhase() == sgs.Player_NotActive then
		self:sortByKeepValue(cards, true)
	else
		self:sortByUseValue(cards)
	end
	return cards[1]:getId()
end
sgs.ai_view_as.LuaJiaosi = function(card, player, card_place)
	if (not player:getPile("qizhi2")) or (player:getPile("qizhi2"):isEmpty()) then return end
	if (player:getMark("LuaJiaosiS") >= 1) then return end
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	for _,id in sgs.qlist(player:getPile("qizhi2")) do
		if sgs.Sanguosha:getCard(id):getSuit() == card:getSuit() then
			if card_place ~= sgs.Player_PlaceSpecial and not card:isKindOf("Peach") and not card:hasFlag("using") then
				return ("slash:LuaJiaosi[%s:%s]=%d"):format(suit, number, card_id)
			end
		end
	end
end
sgs.ai_skill_choice.LuaJiaosi = function(self, choices)
	local target = self.room:getTag("LuaJiaosi_user"):toPlayer()
	local card = self.room:getTag("LuaJiaosi_card"):toCard()
	
	local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
	slash:setSkillName("LuaJiaosi")
	slash:addSubcard(card:getEffectiveId())	
	
	local duel = sgs.Sanguosha:cloneCard("duel", card:getSuit(), card:getNumber())
	duel:setSkillName("LuaJiaosi")
	duel:addSubcard(card:getEffectiveId())		
	
	local b = self:AtomDamageCount2(target, self.player, false, duel)
	local a = self:AtomDamageCount2(target, self.player, false, slash)
	
	if a > b then return "slash" end 
	return "duel"
end 


sgs.ai_use_priority.LuaJiaosi = function(self)
	local duel = sgs.Sanguosha:cloneCard("duel")
	local x = self:getUseValue(duel)
	return x
end 

function findqizoutarget(self)
	if not self.player:faceUp() then 
		if prismRiver(self) then 
			return self.player 
		end 
	end 
	for _, friend in ipairs(self.friends_noself) do
		if not friend:faceUp() then
			return friend
		end	
	end 
	self:sort(self.enemies)
	for _, enemy in ipairs(self.enemies) do
		if self:toTurnOver(enemy, 0, "luaqizou") then
			return enemy
		end
	end	
	return 
end 
function findqizoucard(self)
	local function valuableE(acard)
		if #self.enemies == 1 then 
			if acard:isKindOf("Vine") and self:evaluateArmor(acard, player) > 0 then return true end 
		end 
		for _, card in ipairs(self:getTurnUse(true)) do
			if card:getEffectiveId() == acard:getEffectiveId() then return true end 
		end 
		if acard:isKindOf("Crossbow") then 
			local count = 0 
			for _, bcard in sgs.qlist(self.player:getHandcards()) do
				if isCard("Slash", bcard, self.player) and not self.player:isCardLimited(bcard, sgs.Card_MethodUse) then count = count + 1 end
			end 
			for _, id in sgs.qlist(self.player:getPile("wooden_ox")) do
				local c = sgs.Sanguosha:getCard(id)
				if isCard("Slash", c, self.player) and not self.player:isCardLimited(c, sgs.Card_MethodUse) then count = count + 1 end 
			end 			
			if count > 2 then return true end 
		end 
		return false 
	end 
	local function valuableT(ccard)
		if isCard("ExNihilo", ccard, self.player) then 
			local dummy_use = { isDummy = true}
			self:useCardExNihilo(ccard, dummy_use)
			if dummy_use.card then return true end 
		end 	
		if isCard("Snatch", ccard, self.player) or isCard("Dismantlement", ccard, self.player) then 
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			self:useTrickCard(ccard, dummy_use)
			if dummy_use.card and dummy_use.to and (not dummy_use.to:isEmpty()) and self:isFriend(dummy_use.to:at(0)) then return true end 		
			if dummy_use.card and dummy_use.to and (not dummy_use.to:isEmpty()) and self:isEnemy(dummy_use.to:at(0)) then 
				local enemy5 = dummy_use.to:at(0)
				if isCard("Snatch", ccard, self.player) then 				
					local cards = sgs.QList2Table(enemy5:getHandcards())
					local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy5:objectName())
					if #cards <= 2 and not enemy5:isKongcheng() and not self:doNotDiscard(enemy5, "h", true) and (self:hasTrickEffective(ccard, enemy5)) 
						and not enemy5:containsTrick("gainb") then
						for _, cc in ipairs(cards) do
							if (cc:hasFlag("visible") or cc:hasFlag(flag)) and (cc:isKindOf("Peach") or cc:isKindOf("Analeptic")) then
								return true 
							end
						end
					end
				end 
				if enemy5:hasArmorEffect("eight_diagram") and enemy5:getArmor() and not self:needToThrowArmor(enemy5) and not enemy5:containsTrick("gainb") then 
					return true 
				end 
			end 	
		end 		
		if isCard("Indulgence", ccard, self.player) then 
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			self:useTrickCard(ccard, dummy_use)		
			if dummy_use.card and dummy_use.to and (not dummy_use.to:isEmpty()) then 
				for _, p in sgs.qlist(dummy_use.to) do
					local value = p:getHandcardNum() - p:getMaxCards()
					if p:hasSkills("noslijian|lijian|fanjian|neofanjian|dimeng|jijiu|jieyin|anxu|yongsi|zhiheng|manjuan|nosrende|rende|qixi|jixi"..
						"mingce|yjieliang|yquanshi|kuangcai") then return true end	--yun		
					if value > 2 then return true end 
				end 
			end 
		end 
		if ccard:isKindOf("FireAttack") then 
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			self:useTrickCard(ccard, dummy_use)		
			if dummy_use.card and dummy_use.to and (not dummy_use.to:isEmpty()) then 
				for _, p in sgs.qlist(dummy_use.to) do
					if self:AtomDamageCount2(p, self.player, sgs.DamageStruct_Fire, ccard) > 1 then return true end 
				end 
			end 
		end 
		if ccard:isKindOf("Duel") then 
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			self:useTrickCard(ccard, dummy_use)		
			if dummy_use.card and dummy_use.to and (not dummy_use.to:isEmpty()) then 		
				for _, p in sgs.qlist(dummy_use.to) do
					if self:isWeak(p) then return true end 
				end 
			end 
		end 
		return false 
	end 
	local function valuableB(ccard)
		if ccard:isKindOf("Slash") and (#self:getCards("Slash") == 1) then 
			local count_Y = 0 
			for _, dcard in sgs.qlist(self.player:getHandcards()) do
				if dcard:isKindOf("BasicCard") and ((not isCard("Peach", dcard, self.player)) or self:OverFlowPeach(dcard)) then count_Y = count_Y + 1 end 
			end 
			if count_Y > 1 then 
				return true
			end 
		end 	
		return false 
	end 
	
	local cards = sgs.QList2Table(self.player:getCards("he"))
	local tocard = {}
	local tocard2 = {}
	for _, bcard in ipairs(cards) do
		if not self.player:isJilei(bcard) then 
			if bcard:isKindOf("EquipCard") and not valuableE(bcard) then 
				table.insert(tocard, bcard)
			end 
			if bcard:isKindOf("BasicCard") then 
				if (not isCard("Peach", bcard, self.player)) or self:OverFlowPeach(bcard) then -- and not self:needtoKeepAJink(self.player, true)
					if not valuableB(bcard) then table.insert(tocard, bcard) end 
				end 
			end 
			if bcard:isKindOf("TrickCard") and not valuableT(bcard) then 
				--self.room:writeToConsole("骚灵乐团测试3.6")
				table.insert(tocard, bcard)
			end 
		end 
	end
	self:sortByKeepValue(tocard)
	for _, bcard in ipairs(tocard) do
		if bcard:isKindOf("EquipCard") then 
			table.insert(tocard2, bcard)
			--self.room:writeToConsole("骚灵乐团测试3.7")
			break
		end 
	end 
	for _, bcard in ipairs(tocard)  do
		if bcard:isKindOf("BasicCard") then 
			table.insert(tocard2, bcard)
			--self.room:writeToConsole("骚灵乐团测试3.8")
			break
		end 
	end 		
	for _, bcard in ipairs(tocard)  do
		if bcard:isKindOf("TrickCard") then 
			table.insert(tocard2, bcard)
			--self.room:writeToConsole("骚灵乐团测试3.9")
			break
		end 
	end 	
	for _, bcard in ipairs(tocard2) do --先用武器等距离加成把杀给打出去		
		if bcard:isKindOf("Weapon") or bcard:isKindOf("OffensiveHorse")then 
			local cardPlace = self.room:getCardPlace(bcard:getEffectiveId())
			if cardPlace == sgs.Player_PlaceEquip then 
				for _, ecard in ipairs(self:getTurnUse(true)) do
					if ecard:isKindOf("Slash") then 
						local boolG = false 
						for _, fcard in ipairs(tocard2) do
							if ecard:getEffectiveId() == fcard:getEffectiveId() then boolG = true end 
						end 
						if not boolG then self.room:writeToConsole("骚灵乐团先出杀") return false end 
					end 					
				end 
			end 
		end 
	end 
	if #tocard2 == 3 then return tocard2 end 
	return false 
end 

function prismRiver(self, boolX) --boolX表示不是当下立即需要作出选择
	local needwine = true
	for _, ecard in ipairs(self:getTurnUse(true)) do
		if ecard:isKindOf("Analeptic") then 
			needwine = false 
		end 
	end 
	if self.player:getMark("drank") > 0 then needwine = false end 
	if self.player:getPhase() ~= sgs.Player_Play then needwine = false end 
	--场合1，需要【酒】
	if not boolX then boolX = false end 	
	local wine = sgs.Sanguosha:cloneCard("analeptic", sgs.Card_NoSuit, 0)
	if not self.player:isProhibited(self.player, wine) and not self.player:isCardLimited(wine, sgs.Card_MethodUse) and needwine
		and self.player:getMark("drank") == 0 then 
		self.room:writeToConsole("骚灵乐团测试【酒】")
		if self.player:faceUp() and findqizoutarget(self) and (findqizoucard(self) and findqizoutarget(self):objectName() == self.player:objectName()) and boolX then 
			self.room:writeToConsole("骚灵乐团测试【酒】P")
			local slashX = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			if not self.player:isCardLimited(slashX, sgs.Card_MethodUse) then 
				local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				slash:setSkillName("luayinlv")	
				local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
				self:useBasicCard(slash, dummy_use)
				if dummy_use.card and dummy_use.to and (dummy_use.to:length() > 0) then 
					return "analeptic"
				end 
			end 
		end 
		local tp = self:YouMu()
		local boolS = false
		for _, slash in ipairs(self:getCards("Slash")) do
			local dummy_use = { isDummy = true}
			self:useBasicCard(slash, dummy_use)
			if dummy_use.card then 
				boolS = true
				break 
			end 		
		end 
		if (not boolS) and findqizoutarget(self) and (findqizoucard(self) and findqizoutarget(self):objectName() == self.player:objectName()) then boolS = true end 
		if tp and boolS then return "analeptic" end 
	end 
	--场合2，需要【桃】 我在想peach
	local peach = sgs.Sanguosha:cloneCard("peach", sgs.Card_NoSuit, 0)
	if not self.player:isProhibited(self.player, peach) and not self.player:isCardLimited(peach, sgs.Card_MethodUse) then 
		self.room:writeToConsole("骚灵乐团测试【桃】")
		if self.player:getLostHp() > 1 then 
			local bool = false 
			for _, acard in sgs.qlist(self.player:getHandcards()) do
				if isCard("Peach", acard, self.player) and not self.player:isCardLimited(acard, sgs.Card_MethodUse) then bool = true end 
			end 
			if not bool then return "peach" end 
		end 
	end 
	
	--场合1，需要【酒】
	if not self.player:isProhibited(self.player, wine) and not self.player:isCardLimited(wine, sgs.Card_MethodUse) and needwine
		and self.player:getMark("drank") == 0 then 
		self.room:writeToConsole("骚灵乐团测试【酒2】")
		local boolS = false
		slashpp = {}
		for _, slash in ipairs(self:getCards("Slash")) do
			local dummy_use = { isDummy = true}
			self:useBasicCard(slash, dummy_use)
			if dummy_use.card then 
				boolS = true 
				table.insert(slashpp, slash)
			end 		
		end 
		local qizoucard = findqizoucard(self)
		if (not boolS) and qizoucard then boolS = true end 
		if qizoucard and #slashpp > 0 then 
			local boolyu = false
			for _, carda in ipairs(slashpp) do
				for _, cardz in ipairs(qizoucard) do
					if cardz:getEffectiveId() == carda:getEffectiveId() then boolyu = true end 
				end 
				if not boolyu then return "analeptic" end 
			end 
		end 
		if ((self.player:getHp() > 2) or (self:getOverflow() > 1)) and boolS then return "analeptic" end 
	end 
	
	--场合3，需要【杀】
	local slashX = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
	if not self.player:isCardLimited(slashX, sgs.Card_MethodUse) then 
		self.room:writeToConsole("骚灵乐团测试【杀】")
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		slash:setSkillName("luayinlv")	
		local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
		self:useBasicCard(slash, dummy_use)
		local ss = {}
		table.insert(ss, slash)
		if dummy_use.card and dummy_use.to and (dummy_use.to:length() > 0) 
			and (self:hasHeavySlashDamage(self.player, slash, dummy_use.to:at(0)) or self:canKillEnermyAtOnce(ss)) then 
			return "slash"
		end 
	end 
	
	return
end 
sgs.ai_card_intention.luaqizou = sgs.ai_card_intention.FuluanCard

local luayinlv_skill = {}
luayinlv_skill.name = "luayinlv"
table.insert(sgs.ai_skills,luayinlv_skill)
luayinlv_skill.getTurnUseCard = function(self)
	if not self.player:faceUp() then return end 
	local rprprpr = prismRiver(self, true)
	if rprprpr then 
		self.room:setTag("luayinlv", sgs.QVariant(rprprpr))
		self.room:writeToConsole("骚灵乐团测试X  " .. rprprpr)
		return sgs.Card_Parse("#luayinlv:.:")
	end 	
	return
end 
sgs.ai_use_priority.luayinlv = function(self)
	local aaabc = prismRiver(self, true)
	if not aaabc then return 0 end 
	local duel = sgs.Sanguosha:cloneCard(aaabc)
	local x = self:getUseValue(duel) + 0.5
	return x
end 
sgs.ai_skill_use_func["#luayinlv"] = function(card, use, self)
	
	use.card = sgs.Card_Parse("#luayinlv:.:")
	if use.to then 
		self.room:writeToConsole("骚灵乐团测试3")
		use.to:append(self.player) 
		return
	end
	return
end 


local luaqizou_skill = {}
luaqizou_skill.name = "luaqizou"
table.insert(sgs.ai_skills,luaqizou_skill)
luaqizou_skill.getTurnUseCard = function(self)
	self.room:writeToConsole("骚灵乐团测试3.5")
	if not findqizoucard(self) then return end 
	self.room:writeToConsole("骚灵乐团测试4")
	if findqizoutarget(self) then return sgs.Card_Parse("#luaqizou:.:") end 
end 
sgs.ai_skill_use_func["#luaqizou"] = function(cardQ, use, self)
	local cards = findqizoucard(self)
	local target = findqizoutarget(self)
	if not cards then return end 
	if not target then return end 
	local card_ids = {}
	for _, card in ipairs(cards) do
		table.insert(card_ids, card:getId())
	end 	
	use.card = sgs.Card_Parse("#luaqizou:".. table.concat(card_ids, "+") ..":")	
	if use.to then 
		use.to:append(target)
		return
	end
	return	
end 
sgs.ai_use_priority.luaqizou = function(self)
	local aaabc = prismRiver(self)
	if not aaabc then 
		if self.player:faceUp() then return 0 end 
		return 7
	end 
	local duel = sgs.Sanguosha:cloneCard(aaabc)
	local x = self:getUseValue(duel) - 0.3
	return x
	--return 15
end 
sgs.ai_skill_invoke.luayinlv = function(self, data)
	return true
end 
sgs.ai_skill_choice.luayinlv3 = function(self, choices)
	if self.room:getTag("luayinlv") and self.room:getTag("luayinlv"):toString() ~= "" then 
		local prprp = self.room:getTag("luayinlv"):toString() 
		self.room:writeToConsole("骚灵乐团测试X2  " .. prprp)
		self.room:removeTag("luayinlv")
		return prprp
	end 
	if prismRiver(self) then 
		return prismRiver(self)
	end 
	self.room:writeToConsole("骚灵乐团出bug了")
	local items = choices:split("+")
	if table.contains(items, "slash") then return "slash" end 
	return
end 

sgs.ai_skill_playerchosen.luayinlv2 = function(self, targets)
	local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
	slash:setSkillName("luayinlv")	
	local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
	self:useBasicCard(slash, dummy_use)
	if dummy_use.card and dummy_use.to and (dummy_use.to:length() > 0) then 
		return dummy_use.to:at(0)
	end 
	self.room:writeToConsole("骚灵乐团出bug了2")
	return
end 


local function sortBybingpuValue(self, cards, inverse, kept)
    local compare_func = function(a, b)
        local v1 = self:getKeepValue(a)
        local v2 = self:getKeepValue(b)
		if a:isBlack() then v1 = v1 + 0.5 end 
		if b:isBlack() then v2 = v2 + 0.5 end 
        if v1 ~= v2 then
            if inverse then return v1 > v2 end
            return v1 < v2
        else
            if not inverse then return a:getNumber() > b:getNumber() end
            return a:getNumber() < b:getNumber()
        end
    end

    table.sort(cards, compare_func)
	return cards
end
sgs.ai_skill_cardask["@luabingpua"] = function(self, data, pattern, target) 

	local cards = sgs.QList2Table(self.player:getCards("he"))
	cards = sortBybingpuValue(self, cards)	
	
	return "$" .. cards[1]:getId()
end

sgs.ai_skill_cardask["@luabingpub"] = function(self, data, pattern, target) 
	local card_0 = self.room:getTag("luabingpuC2")
	if card_0 and card_0:toCard() and (self.player:getMark("@baka") == 0) then 
		
		card_0 = card_0:toCard()
		local cards = sgs.QList2Table(self.player:getCards("h"))
		self:sortByKeepValue(cards)
		for _, card in ipairs(cards) do
			if (card_0:isRed() and card:isBlack()) or (card_0:isBlack() and card:isRed()) then 
				return "$" .. card:getId()
			end 
		end 
	end 
	return "."
end

sgs.ai_card_intention.luajuezhan = 40

local function findjuezhanTarget(self, needhandcard)
	local to
	if #self.enemies > 0 then
		local enermy = self.enemies
		self:sort(enermy, "defense")
		if needhandcard then
			for _, target in ipairs(enermy) do
				if not target:isNude() and not target:hasSkills("Luayuyi|luayuechong")
					and self:damageIsEffective(target, nil, self.player) then to = target end
			end
		else
			for _, target in ipairs(enermy) do
				if self:damageIsEffective(target, nil, self.player) and not target:hasSkills("Luayuyi|luayuechong") then to = target end
			end
		end
	end
	if not to then
		local players = sgs.QList2Table(self.room:getAllPlayers())
		self:sort(players, "defense")
		for _, target in ipairs(players) do
			if not self:isFriend(target) and ((not needhandcard) or not target:isNude()) and not target:hasSkills("Luayuyi|luayuechong")
				and self:damageIsEffective(target, nil, self.player) then to = target end
		end
	end
	if needhandcard and self:getCardsNum("Slash") < 1 and to and to:isNude() then return end
	if needhandcard and self.player:isCardLimited(sgs.Sanguosha:cloneCard("jink"), sgs.Card_MethodDiscard) then return end
	return to
end
local luajuezhan_skill = {}
luajuezhan_skill.name = "luajuezhan"
table.insert(sgs.ai_skills, luajuezhan_skill)
luajuezhan_skill.getTurnUseCard = function(self)
	if self.player:hasFlag("luajuezhani") then return end
	if self.player:usedTimes("#luajuezhan") > 1 then return end
	if #self.friends == self.room:getAllPlayers():length() then return end
	return sgs.Card_Parse("#luajuezhan:.:")
end 

sgs.ai_skill_use_func["#luajuezhan"] = function(cardX, use, self)
	for _, slash in ipairs(self:getCards("Slash")) do --是否把手牌中的杀用于决斗比较值
		local canuse = true
		local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
		self:useBasicCard(slash, dummy_use)
		if dummy_use.card and dummy_use.to and (dummy_use.to:length() > 0) then
			if self:hasHeavySlashDamage(self.player, slash, dummy_use.to:at(0)) then canuse = false end
			if (slash:isKindOf("ThunderSlash") or slash:isKindOf("FireSlash")) and self:isGoodChainTarget(dummy_use.to:at(0)) then canuse = false end
		end
		if canuse then
			local target = findjuezhanTarget(self)
			if target then
				use.card = sgs.Card_Parse("#luajuezhan:.:")
				if use.to then use.to:append(target) end
				return
			end
		end
	end

	if self.player:getHp() >= 4 then
		if not self.player:isKongcheng() then
			local target = findjuezhanTarget(self, true)
			if target then
				use.card = sgs.Card_Parse("#luajuezhan:.:")
				if use.to then use.to:append(target) end
				return
			end
		end
	end
	if self.player:getHp() >= 2 then
		if (self.player:getMark("@baka") == 0) then
			local black = {}
			local red = {}
			local allcards = sgs.QList2Table(self.player:getCards("he"))
			for _, card in ipairs(allcards) do
				if card:isBlack() then table.insert(black, card) end
			end
			for _, card in ipairs(allcards) do
				if card:isRed() then table.insert(red, card) end
			end
			if not ((#red == 1) and (red[1]:isKindOf("ExNihilo") or (red[1]:isKindOf("Peach") and self.player:isWounded()))) then
				local target = findjuezhanTarget(self, true)
				if target then
					use.card = sgs.Card_Parse("#luajuezhan:.:")
					if use.to then use.to:append(target) end
					return
				end
			end
		else
			local daiyousei = self.room:findPlayerBySkillName("Luamengyan")
			if daiyousei and self:isFriend(daiyousei) and self.player:getMark("@LuaYizuo") > 0 then
				local target = findjuezhanTarget(self, true)
				if target then
					use.card = sgs.Card_Parse("#luajuezhan:.:")
					if use.to then use.to:append(target) end
					return
				end
			end
			local callback = sgs.ai_skill_cardask["duel-slash"]
			if callback and callback(self, sgs.QVariant(), "slash", self.enemies[1]) ~= "." then
				local target = findjuezhanTarget(self)
				if target then
					use.card = sgs.Card_Parse("#luajuezhan:.:")
					if use.to then use.to:append(target) end
					return
				end
			end
		end
		if math.random() < 0.2 then
			local target = findjuezhanTarget(self, true)
			if target then
				use.card = sgs.Card_Parse("#luajuezhan:.:")
				if use.to then use.to:append(target) end
				return
			end
		end
	end
end 

sgs.ai_use_priority.luajuezhan = function(self)
	if self.player:hasSkill("luajianta") then return 0 end
	local duel = sgs.Sanguosha:cloneCard("duel", sgs.Card_NoSuit, 0)
	local x = self:getUseValue(duel) - 0.15
	return x
end 

sgs.ai_skill_invoke.luabingpu = function(self, data)
	local target = self.room:getTag("luabingpuTarget")
	if (not target) or (not target:toPlayer()) or (not target:toPlayer():objectName()) then return false end  	
	target = target:toPlayer()
	if target:objectName() ~= self.player:objectName() and self:isFriend(target) then return false end
	if target:hasSkill("luayuechong") then return false end
	if target:objectName() == self.player:objectName() then 
		local duel = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
		local value_0 = self:getKeepValue(duel, true, true) - 1.2
		local allcards = {}
		local dcards = sgs.QList2Table(self.player:getCards("h"))
		for _, c in ipairs(allcards) do
			local table_0 = {}
			table.insert(table_0, c)
			if (self:getKeepValue(duel, true, true) < value_0) and not c:isKindOf("EightDiagram")
				and not self:canKillEnermyAtOnce(table_0) then 
				table.insert(allcards, c)
			end 
		end
		for _, c in ipairs(allcards) do
			if c:isRed() then 
				for _,g in sgs.qlist(allcards) do 
					if g:isBlack() then return true end 
				end 
			end 
		end 	
	end 
	if not self:isFriend(target) then return true end 
	return false 
end 

sgs.ai_skill_invoke.luayexiao = function(self, data)
	local qargets = sgs.SPlayerList()
	for _, target in sgs.qlist(self.room:getAlivePlayers()) do
		if (not target:isWounded()) or (target:getHandcardNum() <= 1) then 
			qargets:append(target)
		end 
	end 		
	for _, aplayer in sgs.qlist(qargets) do
		if self:isEnemy(aplayer) then return true end 
	end 
	
	local boolo = false
	for _, enemy in ipairs(self.enemies) do
		if enemy:getLostHp() <= 1 then boolo = true end 
	end 
	if boolo == false then 
		for _, enemy in ipairs(self.enemies) do
			if (enemy:getHandcardNum() <= 2) then return true end 
		end 
	end 
	return false
end 

sgs.ai_skill_playerchosen.luayexiao = function(self, targets)
	targets = sgs.QList2Table(targets)
	self:sort(targets, "defense")
	for _, target in ipairs(targets) do
		if self:isEnemy(target) and self:damageIsEffective(target, nil, self.player) then return target end
	end
	for _, target in ipairs(targets) do
		if self:isEnemy(target) then return target end
	end
	return nil
end 

local luayexiao_skill = {}
luayexiao_skill.name = "luayexiao"
table.insert(sgs.ai_skills, luayexiao_skill)
luayexiao_skill.getTurnUseCard = function(self)
	if self.player:getMark("rumia1") == 0 then return end 
	for _, enemy in ipairs(self.enemies) do
		if enemy:isKongcheng() and self:damageIsEffective(enemy) then 
			
			return sgs.Card_Parse("#luayexiao:.:") 
		end 
	end 	
end 

sgs.ai_skill_use_func["#luayexiao"] = function(card, use, self)	
	local enermy = self.enemies
	self:sort(enermy, "defense")	
	
	if self:isWeak() then 
		for _, enemy in ipairs(self.enemies) do
			if enemy:isKongcheng() and enemy:getHp() == 1 and self:damageIsEffective(enemy, nil, self.player) then
				use.card = sgs.Card_Parse("#luayexiao:.:") 
				if use.to then use.to:append(enermy[1]) end
				return			
			end 
		end 	
	end 
	for _, enemy in ipairs(self.enemies) do
		if enemy:isKongcheng() and self:damageIsEffective(enemy, nil, self.player) then
			
			use.card = sgs.Card_Parse("#luayexiao:.:") 
			if use.to then use.to:append(enemy) end
			return			
		end 
	end 
end 

sgs.ai_use_priority.luayexiao = function(self)
	if self.player:isWounded() then 
		for _, enemy in ipairs(self.enemies) do
			if enemy:getHp() == 1 and self:damageIsEffective(enemy) then 
				return 10
			end 
		end 
	end 
	return 1.5
end 
function yueshicard(self)
	local toUse = {}
	local cards = self:getTurnUse(true)
	for _, card in ipairs(cards) do
		if self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand and card:isKindOf("EquipCard") then 
			if self:getSameEquip(card, self.player) then 
				table.insert(toUse, card)
			end 
			if card:getNumber() > 10 then table.insert(toUse, card) end 
		end 
	end 
	local fcaerds = sgs.QList2Table(self.player:getCards("h"))
	local jink = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
	local jink_v = self:getKeepValue(jink, true, true) - 0.5
	for _, card in ipairs(fcaerds) do
		if (not table.contains(cards, card)) and self:getKeepValue(card) <= jink_v and not table.contains(toUse, card) then 
			
			table.insert(toUse, card)
		end 
	end 
	
	local jinks = self:getCardsNum("Jink")
	local jcount = 0
	for _, card in ipairs(fcaerds) do
		if (jcount == jinks - 1) or (jinks == 0) then break end 
		if card:isKindOf("Jink") and not table.contains(toUse, card) then 
			table.insert(toUse, card)
			jcount = jcount + 1
		end
	end 
	
	local peachs = self:getCardsNum("Peach")
	for _, card in ipairs(fcaerds) do
		if card:isKindOf("Peach") and not table.contains(toUse, card) and self:OverFlowPeach(card) then 
			table.insert(toUse, card)
		end
	end 	
	if #toUse == 0 then return end 
	self:sortByKeepValue(toUse)
	return toUse[1]
end 

function useyueshicard(self)
	local cards = sgs.QList2Table(self.player:getCards("h"))
	self:sortByKeepValue(cards)
	local max_card = self:getMaxCard(self.player)
	if not max_card then return end 
	local jink = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
	local jink_v = self:getKeepValue(jink, true, true) - 1
	local bool = ((max_card:getNumber() > 9) or ((max_card:getNumber() > 7) and self:getKeepValue(max_card) <= jink_v)) 
	
	local enermy = self.enemies
	self:sort(enermy, "defense")
	if self.player:getMark("rumia1") > 0 then 	
		for _, enemy in ipairs(enermy) do
			if enemy:isKongcheng() and self:damageIsEffective(enemy) then return end 
		end 
		for _, enemy in ipairs(enermy) do
			if enemy:getHandcardNum() == 1 and self:damageIsEffective(enemy) then return cards[1] end 
		end
		for _, enemy in ipairs(enermy) do
			if enemy:getHandcardNum() == 2 and ((enemy:objectName() == enermy[1]:objectName()) or (enemy:objectName() == enermy[2]:objectName() and #self.enemies > 2))
				and self:damageIsEffective(enemy) and bool then 				
				return max_card
			end 
		end 		
	end 
	if self.player:getMark("rumia2") > 0 then 	
		for _, enemy in ipairs(enermy) do
			if not enemy:isWounded() then return end 
		end 
		for _, enemy in ipairs(enermy) do
			if enemy:getLostHp() == 1 and ((enemy:objectName() == enermy[1]:objectName()) or (enemy:objectName() == enermy[2]:objectName() and #self.enemies > 2))
				and self:damageIsEffective(enemy) and bool then 				
				return max_card				 
			end 
		end 
	end 
	if yueshicard(self) then return yueshicard(self) end 
end



sgs.ai_skill_invoke.luaxiaoan = sgs.ai_skill_invoke.liegong

sgs.ai_skill_cardask["@luaxiaoan"] = function(self, data, pattern, target)

	local cards = sgs.QList2Table(self.player:getCards("h"))
	for _, acard in ipairs(cards) do
		if acard:isBlack() and not (self.player:getLostHp() == 1 and self.player:hasSkill("luashenjun") and self.player:getMaxHp() > 3) then
			return  "$" .. cards[1]:getId()
		end
	end

	return "."
end

local luayueshi_skill = {}
luayueshi_skill.name = "luayueshi"
table.insert(sgs.ai_skills, luayueshi_skill)
luayueshi_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luayueshi") then return end 	
	if #self.enemies == 0 then return end 
	if useyueshicard(self) then return sgs.Card_Parse("#luayueshi:.:") end 
end

sgs.ai_skill_use_func["#luayueshi"] = function(card, use, self)	
	local enermy = self.enemies
	self:sort(enermy, "defense")
	local cardf = useyueshicard(self)
	for _, enemy in ipairs(enermy) do
		if enemy:getMark("@lualongyan") > 0 and not enemy:isKongcheng() then
			use.card = sgs.Card_Parse("#luayueshi:".. cardf:getId() ..":" )
			if use.to then use.to:append(enemy) end
			return
		end
	end
	if self.player:getMark("rumia1") > 0 then 
		for _, enemy in ipairs(enermy) do
			if enemy:getHandcardNum() == 1 and self:damageIsEffective(enemy) then 		
				use.card = sgs.Card_Parse("#luayueshi:".. cardf:getId() ..":" ) 
				if use.to then use.to:append(enemy) end
				return
			end 
		end
		for _, enemy in ipairs(enermy) do
			if enemy:getHandcardNum() == 2 then 		
				use.card = sgs.Card_Parse("#luayueshi:".. cardf:getId() ..":" ) 
				if use.to then use.to:append(enemy) end
				return
			end 
		end 
	end
	for _, enemy in ipairs(enermy) do
		if not enemy:isKongcheng() then
			use.card = sgs.Card_Parse("#luayueshi:".. cardf:getId() ..":" )
			if use.to then use.to:append(enemy) end
			return
		end
	end
end 

sgs.ai_skill_playerchosen.luayueshi1 = function(self, targets)
	local enermy = self.enemies
	self:sort(enermy, "defense")

	local bool = false
	for _, enemy in ipairs(enermy) do
		if not enemy:isWounded() then bool = true end 
	end 
	if (not bool) and self.player:getMark("rumia2") > 0 then 
		for _, enemy in ipairs(enermy) do
			if enemy:getLostHp() == 1 then 
				return enemy
			end 
		end 
	end 
	return nil

end 

sgs.ai_skill_playerchosen.luayueshi2 = function(self, targets)
	local enermy = self.enemies
	self:sort(enermy, "defense")
	
	for _, to in ipairs(enermy) do
		if targets:contains(to) and to:getHandcardNum() == 1 then
			return to
		end
	end	

	for _, to in ipairs(enermy) do
		if targets:contains(to) and self:getValuableCard(to, true) then
			return to
		end
	end	
	
	
	for _, to in ipairs(self.friends) do
		if targets:contains(to) and to:hasSkills(sgs.lose_equip_skill)  then 
			return to
		end		
	end 	
	for _, to in ipairs(self.friends) do
		if targets:contains(to) and self:needToThrowArmor(to) 
			and self.player:canDiscard(to, to:getArmor():getEffectiveId()) then 
			return to
		end		
	end 	
	
	return enermy[1]
end 

sgs.ai_skill_cardchosen.luayueshi = function(self, who, flags)
	if who:getHandcardNum() == 1 then
		return self:getCardRandomly(who, "h")
	end 
end

sgs.ai_use_priority.luayueshi = sgs.ai_use_value.Dismantlement + 2

sgs.ai_cardneed.luajianji = function(to, card)
	if not to:getWeapon() then
		if card:isKindOf("Weapon") then return true end 
	end 
	if to:getOffensiveHorse() then
		if card:isKindOf("OffensiveHorse") then return true end 
	end 
end


sgs.ai_skill_invoke.luajianji = function(self, data)
	local cards = self:getTurnUse(true)
	self:sortByDynamicUsePriority(cards)
	local x = self.player:getHandcardNum() - self.player:getMaxCards()
	local y = #self.enemies
	if #cards <= 1 then return true end
	if self:willSkipPlayPhase(self.player) then return true end
	if #cards >= 4 then
		if cards[1]:isKindOf("Slash") and cards[2]:isKindOf("Slash") then
			local dummy_use = { isDummy = true , extra_target = 1, to = sgs.SPlayerList() }
			self:useBasicCard(cards[1], dummy_use)
			if not dummy_use.card then self.room:writeToConsole("sp_youmu test Failed!") end
			if dummy_use.to:length() > 1 then return true end
			for _, p in sgs.qlist(dummy_use.to) do
				if self:YouMu2(p) and p:getHp() <= 2 then return true end
			end
		end
		for _, card in ipairs(cards) do
			if card:isKindOf("Slash") then
				local dummy_use = { isDummy = true , extra_target = 1, to = sgs.SPlayerList() }
				self:useBasicCard(cards[1], dummy_use)
				if not dummy_use.card then self.room:writeToConsole("sp_youmu test Failed!") end
				for _, p in sgs.qlist(dummy_use.to) do
					local analeptic = self:searchForAnaleptic(dummy_use, p, dummy_use.card)
					if analeptic and self:shouldUseAnaleptic(p, dummy_use.card) and analeptic:getEffectiveId() ~= dummy_use.card:getEffectiveId() then
						return true
					end
				end
			end
			if card:isKindOf("ExNihilo") and #self.friends_noself > 0 then
				return true
			end
		end
		if (#cards == 2) or self.player:isWounded() then
			for _, card in ipairs(cards) do
				if card:isKindOf("Peach") then
					for _, friend in ipairs(self.friends_noself) do
						if friend:isWounded() and (not (friend:hasSkill("luaminghe")) or friend:getLostHp() > 1) then
							return true
						end
					end
				end
				if card:isKindOf("ExNihilo") and #self.friends_noself > 0 then return true end
			end
		end
	end
	if #cards == 2 then
		if not self.player:isWounded() then
			for _, card in ipairs(cards) do
				if card:isKindOf("ExNihilo") and #self.friends_noself > 0 then return true end
			end
			if x >= 1 and y == 0 then return false end
			if x >= 2 then return false end
		else
			for _, card in ipairs(cards) do
				if (card:isNDTrick() or (card:isKindOf("BasicCard") and (not card:isKindOf("Analeptic")) and (not card:isKindOf("sakura")))) then
					return true
				end
			end
			if x > 0 then return true end
		end
		return false
	end
	if #cards >= 3 then
		local count = 0
		for _, card in ipairs(cards) do
			if (card:isNDTrick() or (card:isKindOf("BasicCard") and (not card:isKindOf("Analeptic")) and (not card:isKindOf("sakura"))))
					and y > 1 then
				count = count + 1
			end
			if card:isKindOf("ExNihilo") and #self.friends_noself > 0 then
				return true
			end
		end
		if count <= 1 then return false end
		if x == 5 and not self.player:isWounded() then return false end
		if x > 5 then return false end
		return true
	end
end 

sgs.ai_skill_choice.luajianji = function(self, choices, data)
	local cards = self:getTurnUse(true)
	self:sortByDynamicUsePriority(cards)
	local x = self.player:getHandcardNum() - self.player:getMaxCards()
	local y = #self.enemies	
	if #cards == 0 then 
		if (x <= -4) or ((x <= -3) and self.player:isWounded()) then return "0" end 		
		return "1"
	end
	if self:willSkipPlayPhase(self.player) then return "0" end
	local kanako = self.room:findPlayerBySkillName("luaxinyang")
	if kanako and kanako:isAlive() and self:isFriend(kanako) then
		local x2 = self.player:getMaxCards()
		x2 = math.max(x2 - self.player:getHandcardNum(), 0)
		x2 = 4 - x2
		return tostring(x2)
	end
	local function pandin1(cardsP)
		if #cardsP > 1 and cardsP[1]:isKindOf("Slash") and cardsP[2]:isKindOf("Slash") then
			local dummy_use = { isDummy = true , extra_target = 1, to = sgs.SPlayerList() }
			self:useBasicCard(cardsP[1], dummy_use)
			if dummy_use.to:length() > 1 then return true end 
			for _, p in sgs.qlist(dummy_use.to) do
				if self:YouMu2(p) and p:getHp() <= 2 then return true end 
			end 
		end 
		for _, card in ipairs(cardsP) do
			if card:isKindOf("Slash") then 
				local dummy_use = { isDummy = true , extra_target = 1, to = sgs.SPlayerList() }
				self:useBasicCard(cardsP[1], dummy_use)
				for _, p in sgs.qlist(dummy_use.to) do
					local analeptic = self:searchForAnaleptic(dummy_use, p, dummy_use.card)
					if analeptic and self:shouldUseAnaleptic(p, dummy_use.card) and analeptic:getEffectiveId() ~= dummy_use.card:getEffectiveId() then
						return true 
					end 
				end 
			end 
		end 
	end 
	if #cards == 1 then 
		local tableX = {}
		table.insert(tableX, cards[1])
		if self:canKillEnermyAtOnce(tableX, false, false, 1) then return "1" end
		if pandin1(cards) then return "2" end
		if self.player:isWounded() then 
			local xy = x + 4
			if xy <= 1 then return "0" end 
			xy = x + 2
			if xy <= 2 then return "1" end 
			return "2"
		else
			local xy = x + 4
			if xy <= 0 then return "0" end 
			xy = x + 2
			if xy <= 1 then return "1" end 
			return "2"		
		end 	
	elseif #cards == 2 then 
		local count = 0
		for _, card in ipairs(cards) do
			if (card:isNDTrick() or (card:isKindOf("BasicCard") and (not card:isKindOf("Analeptic"))))
				and y > 1 then 
				count = count + 1
			end 
		end 		
		if count == 2 then return "2" end
		if pandin1(cards) then return "2" end 
		if self.player:isWounded() then 			
			for _, card in ipairs(cards) do
				if card:isKindOf("Peach") then 
					for _, friend in ipairs(self.friends_noself) do
						if friend:isWounded() and (not (friend:hasSkill("luaminghe")) or friend:getLostHp() > 1) then 
							local xy = x + 3 - 1
							if xy <= 2 then return "1" end 
							return "2" 
						end 
					end 
				end 
				if card:isKindOf("ExNihilo") and #self.friends_noself > 0 then 
					local xy = x + 3 - 1
					if xy <= -1 then return "1" end 
					return "2" 
				end 
			end 
			local xy = x + 3 - 1
			if xy <= 1 then return "1" end 
			return "2"			
		else
			for _, card in ipairs(cards) do
				if card:isKindOf("ExNihilo") and #self.friends_noself > 0 then 
					local xy = x + 3 - 1
					if xy <= -2 then return "1" end 
					return "2" 
				end 			
			end 
			local xy = x + 3 - 1
			if xy <= 0 then return "1" end 
			return "2"					
		end 	
	else
		local count = 0
		local count_X = 0
		for _, card in ipairs(cards) do
			if (card:isNDTrick() or (card:isKindOf("BasicCard") and (not card:isKindOf("Analeptic")) and (not card:isKindOf("sakura"))))
				and y > 1 then 
				count = count + 1
			end 
		end 
		for _, card in ipairs(cards) do
			if card:isKindOf("Slash") or card:isKindOf("Duel") or card:isKindOf("ExNihilo") then 
				count_X = count_X + 1 
			end 
		end 
		if count_X > 3 then return "4" end 
		if count == 2 and self.player:isWounded() then
			return "2"
		end 
		return "3"
	end 
end 


function lightningJudge(self, card, judge_str, card0)
	if card:isKindOf("Lightning") then 
		if (card0:getSuitString() ~= "spade") or (card0:getNumber() < 2) or (card0:getNumber() > 9) then return true end
	end 
	if judge_str ~= card0:getSuitString() then 
		return true 
	end
	return false 
end

sgs.ai_skill_playerchosen.luajianjic = function(self, targets)
	targets = sgs.QList2Table(targets)
	local cardX = self.room:getTag("luajianjiTC"):toCard()
	local dummy_use_a = { isDummy = true , extra_target = 1, to = sgs.SPlayerList() }

	self.room:writeToConsole("spyoumu Trick Card Test0 " .. cardX:objectName())
	local function ABF(Rplayer)
		for _, friend in ipairs(targets) do
			if friend:objectName() == Rplayer:objectName() then return true end
		end
		return false
	end
	if cardX:isKindOf("Hui") then
		self:sort(self.enemies, "defense")
		for _, enemy in ipairs(self.enemies) do
			if self:damageIsEffective(enemy, nil,enemy) and ABF(enemy) then
				return enemy
			end 
		end 
	end 
	if cardX:isKindOf("Peach") then
		self:sort(self.friends_noself, "defense")
		for _, friend in ipairs(self.friends_noself) do
			if friend:isWounded() and (not (friend:hasSkill("luaminghe")) or friend:getLostHp() > 1)
					and ABF(friend) then
				return friend
			end
		end
	elseif cardX:isKindOf("ExNihilo") then
		self.room:writeToConsole("spyoumu Trick Card Test2 " .. cardX:objectName())
		self:sort(self.friends_noself, "defense")
		for _, friend in ipairs(self.friends_noself) do
			if ABF(friend) then
				return friend
			end
		end
	elseif cardX:isKindOf("Slash") then
		local callback = sgs.ai_skill_playerchosen.slash_extra_targets
		return callback(self, targets, cardX)
	elseif cardX:isKindOf("AOE") then
		for _, enemy in ipairs(self.enemies) do
			if ABF(enemy) and self:hasTrickEffective(cardX, enemy) then return enemy end
		end
	end
	if cardX:isKindOf("BasicCard") then
		self:useBasicCard(cardX, dummy_use_a)
		if not dummy_use_a.to:isEmpty() then
			for _, p in sgs.qlist(dummy_use_a.to) do
				if ABF(p) then return p end
			end
		end
	elseif cardX:isKindOf("TrickCard") then
		if (cardX:isKindOf("Snatch") or cardX:isKindOf("Dismantlement") or cardX:isKindOf("FaithCollection")) and self.player:hasSkill("luaxiongshi") then
			for _, enemy in ipairs(self.enemies) do
				if self:hasTrickEffective(cardX, enemy) and not enemy:isNude() and ABF(enemy) then
					return enemy
				end
			end
		else
			self:useTrickCard(cardX, dummy_use_a)
			if not dummy_use_a.to:isEmpty() then
				self.room:writeToConsole("spyoumu Trick Card Test " .. cardX:objectName() .. " length" .. dummy_use_a.to:length())
				for _, p in sgs.qlist(dummy_use_a.to) do
					if ABF(p) then self.room:writeToConsole("spyoumu Trick Card Test " .. p:objectName()) ; return p end
				end
			end
		end

	end
	self.room:writeToConsole("spyoumu Trick Card Test! " .. cardX:objectName())
	return nil
end 
sgs.pay_ai_card.Peach.luajifeng = function(self, card, use)
	if self.player:hasSkill("luajifeng") and self.player:hasSkill("luaqucai") and self:getOverflow() < 0
			and self.player:getHp() > 1 and self.player:getPile("fong"):isEmpty() then	--yun
		return 2 
	end
end 
sgs.ai_skill_invoke.luajifeng = function(self, data)
	local aicards
	local xxx = sgs.QList2Table(self.player:getCards("he"))
	xxx = #xxx
	if data:toString() ~= "" then
		aicards = data:toString():split("+")
		local xtable = {}
		for _, id in ipairs(aicards) do
			table.insert(xtable, sgs.Sanguosha:getCard(id))
		end 
		aicards = xtable
	end 

	local judge = self.player:getCards("j")
	local judge_str
	if judge and judge:length() > 0 then 
		judge = sgs.QList2Table(judge)
		judge = sgs.reverse(judge)
		judge_str = sgs.ai_judgestring[judge[1]:objectName()] or sgs.ai_judgestring[judge[1]:getSuitString()]
	end 
	if self.player:containsTrick("gainb") then return false end
	if self.player:getPhase() == sgs.Player_Play or self.player:getPhase() == sgs.Player_Judge then
		if self.player:getPhase() == sgs.Player_Start and judge_str then 
			local card0 = self.room:getDiscardPile():at(0)
			card0 = sgs.Sanguosha:getCard(card0)
			if not lightningJudge(self, judge[1], judge_str, card0) then return false end 
		end 
		if self:needToThrowArmor() then return true end 
		if self:isWeak() then 
			local pc = self:getCardsNum("Peach") + self:getCardsNum("Analeptic")
			if (pc > 0) and self.player:getPhase() ~= sgs.Player_Play then return false end 
		end 
		if self.player:getEquips() then 
			if self.player:hasSkill("zhongzhuang") and self.player:getEquips():length() > 1 then return false end 
			if self.player:getEquips():length() > 2 then return true end
		end
		local dis = self:findPlayerToDiscard("ej", true)
		if not dis then return false end
		return true 
	else

		if self:needToThrowArmor() then return true end
		local dis = self:findPlayerToDiscard("ej", true)
		if not dis then return false end
		if self.player:getPhase() == sgs.Player_Play or self.player:getPhase() == sgs.Player_Draw then return true end
		if self.player:getPhase() == sgs.Player_NotActive or self.player:getPhase() == sgs.Player_Discard then
			local abenemy = self:getEnemyNumBySeat(self.room:getCurrent(), self.player, self.player, true, true)
			if abenemy > 0 then return false end
			return true
		end
		if self.player:getPhase() == sgs.Player_Start or self.player:getPhase() == sgs.Player_Judge or self.player:getPhase() == sgs.Player_Draw then
			if #aicards > 0 then
				if aicards[1]:isKindOf("Peach") or aicards[1]:isKindOf("ExNihilo") or aicards[1]:isKindOf("Duel") or aicards[1]:isKindOf("Snatch") then
					if xxx < 5 and self.player:getHandcardNum() < 4 and math.random() < 0.7 then
						return true
					end
				end
				for _, card in sgs.list(self.player:getHandcards()) do
					if (card:isKindOf("Indulgence") or card:isKindOf("ExNihilo")) and (self:getOverflow() >= -1) then return false end
					if card:isKindOf("Snatch") and (self:getOverflow() >= -1) then
						local use = { isDummy = true }
						self:useCardSnatchOrDismantlement(card, use)
						if use.card then return false end
					end
				end
				if (xxx < 5 and self.player:getHandcardNum() < 4) or (math.random() < 0.5) then
					return true
				end
			end
			return false
		end
		if self.player:getPhase() ~= sgs.Player_NotActive then
			local abenemy = 0
			for _, enemy in ipairs(self.enemies) do
				if enemy:inMyAttackRange(self.player) then abenemy = abenemy + 1 end
			end
			if abenemy > self.player:getHp() - 2 then return false end
			return true
		end
	end
end 

function findbesttarget(self, card)
	local dummy = sgs.Sanguosha:cloneCard(card:objectName(), sgs.Card_NoSuit, 0)
	local players = sgs.SPlayerList()
	local to_p = {}
	
	local basicnum = 0
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)	
	for _, acard in ipairs(cards) do
		if acard:getTypeId() == sgs.Card_TypeBasic and not acard:isKindOf("Peach") then basicnum = basicnum + 1 end
	end
	
	local canliuli = false
	for _, p in sgs.qlist(self.room:getAllPlayers()) do					
		if not self.player:isProhibited(p, dummy) then 
			players:append(p)
		end 									
	end
	--self:sort(players, "defense")
	if dummy:isKindOf("Peach") or dummy:isKindOf("GodSalvation") then		
		for _, p in sgs.qlist(players) do
			if p:isWounded() and self:isFriend(p) and not (p:hasSkill("luaminghe") and p:getLostHp() == 1) then 
				table.insert(to_p, p)
			end 
		end 
		if #to_p > 0 then 
			self:sort(to_p, "defense")
			local ru = {}
			table.insert(ru, to_p[1])
			return ru
		end 
		return
	elseif dummy:isKindOf("ExNihilo") then
		for _, p in sgs.qlist(players) do
			if self:isFriend(p) then
				table.insert(to_p, p)
			end
		end
		if #to_p > 0 then
			self:sort(to_p, "defense")
			local ru = {}
			for _, friend in ipairs(to_p) do
				if friend:hasSkills(sgs.cardneed_skill) then
					if friend:getHandcardNum() < 4 then
						table.insert(ru, friend)
						return ru
					end
				end
			end
			table.insert(ru, to_p[1])
			return ru
		end
		return
	elseif dummy:isKindOf("Duel") then
		local dummy_use = {isDummy = true, to = sgs.SPlayerList()}
		self:useTrickCard(dummy, dummy_use)
		local targets = {}
		for _, p in sgs.qlist(dummy_use.to) do
			table.insert(targets, p:objectName())
		end
		if #targets > 0 then
			return targets
		end
		return
	elseif dummy:isKindOf("AOE") then
		for _, p in sgs.qlist(players) do
			if self:isEnemy(p) then
				if self:aoeIsEffective(dummy, self.player) then
					local ru = {}
					table.insert(ru, p)
					return ru
				end
			end
		end
	elseif dummy:isKindOf("Dismantlement") or dummy:isKindOf("Snatch") then
		self.room:writeToConsole("aya test")
		local dummy2 = sgs.Sanguosha:cloneCard("dismantlement", sgs.Card_NoSuit, 0)
		local dummy_use = {isDummy = true, to = sgs.SPlayerList()}
		self:useTrickCard(dummy2, dummy_use)
		local targets = {}
		for _, p in sgs.qlist(dummy_use.to) do
			self.room:writeToConsole("aya test" .. p:getGeneralName())
			table.insert(targets, p:objectName())
		end
		if #targets > 0 then
			return targets
		end
		return
	elseif dummy:isKindOf("Slash") then 
		for _, p in sgs.qlist(players) do
			if not self:isFriend(p) and not self:slashProhibit(dummy, p) and sgs.isGoodTarget(p, self.enemies, self, true)
				and not self:getDamagedEffects(p, self.player, true) and self:slashIsEffective(dummy, p, self.player, false)
				and not ((p:hasSkill("xiangle") or self.player:hasSkill("luacuiruo")) and basicnum < 2)  then
				table.insert(to_p, p)
			end 
		end 
		if #to_p > 0 then 
			self:sort(to_p, "defense")
			local ru = {}
			table.insert(ru, to_p[1])
			return ru
		end 
		return
	elseif dummy:isKindOf("AmazingGrace") then
		for _, p in sgs.qlist(players) do
			if self:isFriend(p) then
				local ru = {}
				table.insert(ru, p)
				return ru
			end
		end
	end 
end 
sgs.ai_skill_cardask["@luaqucai"] = function(self, data)

	local judge = self.player:getCards("j")
	local judge_str
	self.room:writeToConsole("aya ai test 1")
	local retrial_c = {}
	if judge and judge:length() > 0 then 
		judge = sgs.QList2Table(judge)
		judge = sgs.reverse(judge)
		judge_str = sgs.ai_judgestring[judge[1]:objectName()] or sgs.ai_judgestring[judge[1]:getSuitString()]
	end 
	
	local cards = sgs.QList2Table(self.player:getCards("he"))
	local cardsP = {}
	for _, card in ipairs(cards) do
		if not card:isKindOf("Jink") and not card:isKindOf("Nullification") and not card:isKindOf("sakura") and not card:isKindOf("Collateral") 
			and not card:isKindOf("EquipCard") and not card:isKindOf("DelayedTrick") then 		
			table.insert(cardsP, card)
		end 
	end
	self:sortByUseValue(cardsP)
	if judge_str then 
		local card0 = self.room:getDiscardPile():at(0)
		card0 = sgs.Sanguosha:getCard(card0)
		if lightningJudge(self, judge[1], judge_str, card0) then 
			for _, card in ipairs(cardsP) do
				if isCard("Peach", card, self.player) and not ((self:getOverflow() > 2) and judge[1]:isKindOf("Indulgence")) then 
					continue
				end 
				if judge_str == card0:getSuitString() then 
					table.insert(retrial_c, card)
				end 
			end 
			if #retrial_c > 0 then 
				self:sortByUseValue(retrial_c)
				return "$" .. retrial_c[1]:getEffectiveId() 	
			end 
		else
			return "."
		end 
	else

		self.room:writeToConsole("aya ai test 2")
		for _, card in ipairs(cardsP) do
			if findbesttarget(self, card) then 
				return "$" .. card:getEffectiveId() 		
			end 
		end 
	end 
end 

sgs.ai_skill_invoke.luaqucai = function(self, data)
	local fa = sgs.ai_skill_cardask["@luaqucai"]
	if fa(self, data) and fa(self, data) ~= "." then
		return true 
	end 
end 

sgs.ai_skill_playerchosen.luaqucai = function(self, targets)
	local cardX = self.room:getTag("luaqucaiTC"):toCard()
	local pl = findbesttarget(self, cardX)
	return pl[1]
end 

local luajifeng_skill = {}
luajifeng_skill.name = "luajifeng"
table.insert(sgs.ai_skills, luajifeng_skill)
luajifeng_skill.getTurnUseCard = function(self)
	if self.player:isKongcheng() and self.player:getPile("fong"):length() > 0 then 
		local dis = self:findPlayerToDiscard("ej", true) 
		if not dis then return end
		if self.player:getPile("fong"):length() > 2 and self.player:containsTrick("gaina") then return end
		return sgs.Card_Parse("#luajifeng:.:")
	end 
end

sgs.ai_skill_use_func["#luajifeng"] = function(card, use, self)	
	local dis = self:findPlayerToDiscard("ej", true) 
	use.card = sgs.Card_Parse("#luajifeng:.:" ) 
	if use.to then use.to:append(dis) end
	return
end

sgs.ai_skill_use["@@luajifeng"] = function(self, prompt, method)
	if self.player:getPile("fong"):length() > 0 then
		local dis = self:findPlayerToDiscard("ej", true)
		if not dis then return "." end
		return "#luajifeng:.:->" .. dis:objectName()
	end

end
sgs.ai_choicemade_filter.cardChosen.luajifeng = sgs.ai_choicemade_filter.cardChosen.snatch

sgs.ai_skill_cardask["luapanjue"] = function(self, data)
	local judge = data:toJudge()
	local all_cards = {}
	local all_cards2 = sgs.QList2Table(self.player:getCards("he"))
	for _, card in ipairs(all_cards2) do
		if not card:isKindOf("Wanbaochui") then table.insert(all_cards, card) end
	end
	local cards_P = self.player:getPile("&zui"):first()
	cards_P = sgs.Sanguosha:getCard(cards_P)
	table.insert(all_cards,cards_P)
	if #all_cards == 0 then return "." end

	local card_id = self:getRetrialCardId(all_cards, judge)
	
	local carda = judge.card
	local function will_damage_f(cardx)
		return self:isFriend(judge.who) and (cardx:isBlack() ~= carda:isBlack()) and self:damageIsEffective(judge.who, sgs.DamageStruct_Thunder, self.player)
			and not self:isGoodChainTarget(judge.who, self.player, sgs.DamageStruct_Thunder, 1) and judge.reason ~= "lightning"
	end
	self.room:writeToConsole("shikieiki test" .. #all_cards)
	self:sortByKeepValue(all_cards)
	if self:isEnemy(judge.who) and self:damageIsEffective(judge.who, sgs.DamageStruct_Thunder, self.player) and self:isGoodChainTarget(judge.who, self.player, sgs.DamageStruct_Thunder, 1) then 
		for _, card in ipairs(all_cards) do
			if (not (card:isKindOf("ExNihilo")))
				and (not (card:isKindOf("Peach") and (not self:OverFlowPeach(card))) or self:isWeak(judge.who))
				and (not judge:isGood(card)) and (card:isBlack() ~= carda:isBlack()) then
				return "$" .. card_id
			end 
		end 
	end 
	
	if card_id == -1 then
		self.room:writeToConsole("shikieiki test2" .. #all_cards)
		if self:needRetrial(judge) and judge.reason ~= "beige" then
			if self:needToThrowArmor() and not will_damage_f(self.player:getArmor()) then return "$" .. self.player:getArmor():getEffectiveId() end
			self:sortByUseValue(all_cards, true)
			if self:getUseValue(judge.card) > self:getUseValue(all_cards[1]) and not will_damage_f(all_cards[1]) then
				return "$" .. all_cards[1]:getId()
			end
		end
	elseif self:needRetrial(judge) or (self:getUseValue(judge.card) > self:getUseValue(sgs.Sanguosha:getCard(card_id))) then
		self.room:writeToConsole("shikieiki test3" .. #all_cards)
		local card = sgs.Sanguosha:getCard(card_id)
		if not will_damage_f(card) then
			return "$" .. card_id
		end 
	end

	return "."
end



local luazhongshen_skill = {}
luazhongshen_skill.name = "luazhongshen"
table.insert(sgs.ai_skills, luazhongshen_skill)
luazhongshen_skill.getTurnUseCard = function(self)
	if self.player:getMark("@zhongshen") == 0 then return end 
	if #self.enemies == 0 then return end

	return sgs.Card_Parse("#luazhongshen:.:")
end
sgs.ai_skill_use_func["#luazhongshen"] = function(card, use, self)
	self:sort(self.enemies, "defense")
	if self:slashIsAvailable() then
		if self.player:getPile("&zui") and self.player:getPile("&zui"):length() > 0
				and sgs.Sanguosha:getCard(self.player:getPile("&zui"):at(0)):isKindOf("Jink") then
			use.card = sgs.Card_Parse("#luazhongshen:.:" )
			if use.to then use.to:append(self.enemies[1]) end
			return
		end
	end
	if #self.enemies + #self.friends >= self.room:getAlivePlayers():length() - 1 then
		if self:isWeak() or self.enemies[1]:getHp() < 3 then
			use.card = sgs.Card_Parse("#luazhongshen:.:" )
			if use.to then use.to:append(self.enemies[1]) end
			return
		end
	end
end
sgs.ai_use_priority.luazhongshen = 2
sgs.ai_card_intention.luazhongshen = 80
sgs.ai_skill_invoke.luazhongshenb = true

sgs.ai_skill_cardask["@luaxingyun"] = function(self, data)	--yun
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	if #cards > 0 then 
		return cards[1]:toString() 
	end 
end 

sgs.ai_skill_cardask["@luaxingyun2"] = function(self, data)	--yun
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	if #cards > 0 then 
		return cards[1]:toString() 
	end 
end 


local luaxingyun_skill = {}
luaxingyun_skill.name = "luaxingyun"
table.insert(sgs.ai_skills, luaxingyun_skill)
luaxingyun_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luaxingyun") then return end 
	return sgs.Card_Parse("#luaxingyun:.:") 
end 

sgs.ai_skill_use_func["#luaxingyun"] = function(card, use, self)	
	local enermy = self.enemies
	if #enermy > 0 then 
	
		self:sort(enermy, "defense")
		for _, enemy in ipairs(enermy) do
			if not enemy:isNude() then 
				use.card = sgs.Card_Parse("#luaxingyun:.:" ) 
				if use.to then use.to:append(enemy) end
				return	
			end 
		end 
	else
		for _, player in sgs.qlist(self.room:getAllPlayers()) do
			if (not self:isFriend(player)) and math.random() < 0.4
				and not player:isNude() then 
				use.card = sgs.Card_Parse("#luaxingyun:.:" ) 
				if use.to then use.to:append(player) end
				return							
			end 
		end 
	end 
end 

sgs.ai_skill_playerchosen.luaxingyun = function(self, targets)
	targets = sgs.QList2Table(targets)
	self.room:writeToConsole("tewi test ")
	local target
	local slash = sgs.Sanguosha:cloneCard("slash")
	local acard = self.room:getTag("luaxingyun"):toCard()
	local jink = sgs.Sanguosha:cloneCard("jink")
	local jink_v = self:getKeepValue(jink, true, true) - 0.75
	local need = self:getKeepValue(acard) > jink_v
	local targetP 
	for _, player in sgs.qlist(self.room:getAllPlayers()) do
		if player:hasFlag("xingyun") then targetP = player;break end 		
	end

	local function ABF(Rplayer)
		for _, friend in ipairs(targets) do
			if friend:objectName() == Rplayer:objectName() then return true end
		end
		return false
	end

	if need then 
		local friends = self.friends		
		for _, player in sgs.qlist(self.room:getAllPlayers()) do
			if (not self:isEnemy(player)) and (not self:isFriend(player)) then table.insert(friends, player)  end 		
		end 
		self:sort(friends, "defenseSlash2")
		
		local enemies = self.enemies
		self:sort(enemies, "defenseSlash")
		for _, friend in ipairs(friends) do
			if ((not self:slashIsEffective(slash, friend, targetP, false))
				or self:getDamagedEffects(friend, targetP, true) or (not targetP:canSlash(friend, slash, true)))
					and ABF(friend) then
				target = friend
				break
			end 
		end 

		if not target then 
			for _, friend in ipairs(friends) do
				if (getCardsNum("Jink", friend) >= 1) and ABF(friend) then
					target = friend
					break
				end 
			end 		
		end 
		
		if not target then 

			for _, enemy in ipairs(enemies) do
				if self:slashIsEffective(slash, enemy, targetP, false) and ABF(enemy)
					and self:getDamagedEffects(enemy, targetP, true) and (not targetP:canSlash(enemy, slash, true))
						and getCardsNum("Jink", enemy) >= 1  then
					target = enemy
					break
				end 
			end 		
		end 
		
		if not target then 
			for _, enemy in ipairs(enemies) do
				if self:slashIsEffective(slash, enemy, targetP, false) and ABF(enemy)
					and self:getDamagedEffects(enemy, targetP, true) and (not targetP:canSlash(enemy, slash, true)) then 
					target = enemy
					break
				end 
			end 		
		end 
		
		if not target and (#enemies > 0) then 
			for _, enemy in ipairs(enemies) do
				if ABF(enemy) then
					target = enemy
					break				
				end 
			end 
		end 
		if not target then 
			self.room:writeToConsole("tewi test 6")
			for _, player in sgs.qlist(self.room:getAllPlayers()) do
				if (not self:isEnemy(player)) and (not self:isFriend(player)) and ABF(player) then return player end
			end 		
		end 
		if target then return target end 
	else
		local friends = self.friends		
		for _, player in sgs.qlist(self.room:getAllPlayers()) do
			if (not self:isEnemy(player)) and (not self:isFriend(player)) then table.insert(friends, player)  end 		
		end 
		self:sort(friends, "defenseSlash2")
		
		local enemies = self.enemies
		self:sort(enemies, "defenseSlash")

		if not target then 
			for _, enemy in ipairs(enemies) do
				if self:slashIsEffective(slash, enemy, targetP, false) and ABF(enemy)
					and self:getDamagedEffects(enemy, targetP, true) and (not targetP:canSlash(enemy, slash, true))
						and getCardsNum("Jink", enemy) > 0  then 
					target = enemy
					break
				end 
			end 		
		end 
		
		if not target then 
			for _, enemy in ipairs(enemies) do
				if self:slashIsEffective(slash, enemy, targetP, false) and ABF(enemy)
					and self:getDamagedEffects(enemy, targetP, true) and (not targetP:canSlash(enemy, slash, true)) then 
					target = enemy
					break
				end 
			end 		
		end 

		if not target and (#enemies > 0) then 
			for _, enemy in ipairs(enemies) do
				if ABF(enemy) then
					target = enemy
					break				
				end 
			end 
		end 
		
		if not target then 
			for _, friend in ipairs(friends) do
				if ((not self:slashIsEffective(slash, friend, targetP, false)) or self:getDamagedEffects(friend, targetP, true) or (not targetP:canSlash(friend, slash, true)))
					and ABF(friend) then
					target = friend
					break
				end 
			end 
		end 
		
		if not target then 
			for _, friend in ipairs(friends) do
				if (getCardsNum("Jink", friend) > 0) and ABF(friend) then
					target = friend
					break
				end 
			end 		
		end 


		if not target then
			self.room:writeToConsole("tewi test 7")
			for _, player in sgs.qlist(self.room:getAllPlayers()) do
				if (not self:isEnemy(player)) and (not self:isFriend(player)) and ABF(player) then return player end
			end 		
		end 
		if target then return target end
	end
end 
sgs.ai_use_priority.luaxingyun = 3.92
sgs.ai_card_intention.luaxingyun = 30


local Luahonghuan_skill = {}
Luahonghuan_skill.name = "Luahonghuan"
table.insert(sgs.ai_skills, Luahonghuan_skill)
Luahonghuan_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#Luahonghuan") then return end
	if self.player:hasUsed("#luahonghuan") then return end
	--[[
	local quanzhong = 0
	local table_X = self:getTurnUse(true)
	-- local cards = sgs.QList2Table(self.player:getCards("he"))
	-- for _, card in ipairs(table_X) do
		-- if card:isKindOf("Analeptic") 
	-- end 
	if self:isWeak() then quanzhong = quanzhong + 15 end
	if #self.friends_noself == 0 then return end
	for _, card in ipairs(table_X) do
		if card:isKindOf("Slash") and self.player:isWounded() then 
			if card:isRed() then quanzhong = quanzhong + 20 end 			
			if self:getCardsNum("Analeptic") > 0 then quanzhong = quanzhong + 7 end 
			quanzhong = quanzhong + 15 
			if self.player:getLostHp() > 1 then quanzhong = quanzhong + 10 end 
		end 
	end 
	for _, friend in ipairs(self.friends_noself) do
		if friend:faceUp() then 
			if self:isWeak(friend) then quanzhong = quanzhong + 18 end 
			if getCardsNum("Slash", friend, self.player) > 0 then 
				quanzhong = quanzhong + 8
				if friend:isWounded() then quanzhong = quanzhong + 4 end 
				if friend:hasSkills("luajianji|shinue|Luafengren|luahakurei|LuaBisha|Luashenqiang|Luayuelong|luashaojie|luajingdan") then 
					quanzhong = quanzhong + 8
				end 
			end 
			if friend:isWounded() then quanzhong = quanzhong + 4 end 
			if friend:hasSkill("Luaxianzhe") then quanzhong = quanzhong + 10 end 
		else
			quanzhong = quanzhong - 10
		end 
	end 
	if quanzhong > 40 then
	]]--
		return sgs.Card_Parse("#Luahonghuan:.:") 
	--end
end 

sgs.ai_skill_use_func["#Luahonghuan"] = function(X, use, self)
	--[[
	local friends_noself = self.friends_noself
	self:sort(friends_noself, "handcard", true)
	
	local y = 0
	for _,p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:getRole() == "loyalist" and p:isAlive() then y = y + 1 end 
	end

    if y == 0 and #self.friends_noself == 0 then return end
	use.card = sgs.Card_Parse("#Luahonghuan:.:" ) 
	if use.to then 
		if y == 0 then 
			use.to:append(self.player)
			return	
		else
			y = math.min(y, #friends_noself)
			for i = 1, y do
				use.to:append(friends_noself[i])
			end 
			return	
		end 
	end
	]]
	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end

	local function Check_R(card)
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return false end
		end
		if card:isKindOf("TrickCard") then
			if card:isKindOf("IronChain") and not shuxin then return true end
			if card:isKindOf("Lightning") and not self:willUseLightning(card) then return true end
			if card:isKindOf("AOE") and self:getAoeValue(card) > 35 then return false end
			if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
			if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
			if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
			local dummy_use = {isDummy = true}
			self:useTrickCard(card, dummy_use)
			if not dummy_use.card then return true end
			return false
		end
		if card:isKindOf("EquipCard") then --or card:isKindOf("DefensiveHorse")
			local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
			if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) and rinnosuke
					and rinnosuke:isAlive() and self:isFriend(rinnosuke) then return false end
			if card:isKindOf("OffensiveHorse") then
				if self.player:getOffensiveHorse() then return true end
			end
			if card:isKindOf("DefensiveHorse") then
				if self.player:getDefensiveHorse() then return true end
			end
			if card:isKindOf("Armor") and self:needToThrowArmor() then return true end
			if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
				local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
						or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
				if not bool_3 then return true end
			end
			if card:isKindOf("Weapon") then
				local dummy_use = {isDummy = true}
				self:useEquipCard(card, dummy_use)
				if not dummy_use.card then return true end
				if dummy_use.card and self.player:getWeapon() then return true end
			end
		end
		if card:isKindOf("Ofuda") and self:getCardsNum("Jink") <= 0 then return false end
		if card:isKindOf("Analeptic") then return false end
		if card:isKindOf("Peach") then return false end
		local x0 = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
		if card:isKindOf("Jink") and x0 <= 1 then return false end
		return true
	end
	local y = math.max(0, #self.enemies - 2)
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(cards)
	if not self.player:isWounded() and self:getCardsNum("Jink") <= y then
		for _, cardX in sgs.list(cards) do
			if cardX:isBlack() and Check_R(cardX) then
				use.card = sgs.Card_Parse("#Luahonghuan:" .. cardX:getEffectiveId()..":")
				if use.to then
					use.to = sgs.SPlayerList()
					return
				end
			end
		end
	end
	local hasRSlash = false
	for _, cardX in sgs.list(cards) do
		if cardX:isKindOf("Slash") and cardX:isRed() then hasRSlash = true end
	end
	local compare_func = function(a, b)
		return a:getNumber() > b:getNumber()
	end
	table.sort(cards, compare_func)
	if not hasRSlash and self:slashIsAvailable() then
		for _, cardX in sgs.list(cards) do
			if cardX:isRed() and cardX:getNumber() > 8 then
				use.card = sgs.Card_Parse("#Luahonghuan:" .. cardX:getEffectiveId()..":")
				if use.to then
					use.to = sgs.SPlayerList()
					return
				end
			end
		end
	end
	self:sortByKeepValue(cards)
	for _, cardX in sgs.list(cards) do
		if cardX:isBlack() and Check_R(cardX) then
			use.card = sgs.Card_Parse("#Luahonghuan:" .. cardX:getEffectiveId()..":")
			if use.to then
				use.to = sgs.SPlayerList()
				return
			end
		end
	end
	table.sort(cards, compare_func)
	for _, cardX in sgs.list(cards) do
		if cardX:isRed() and Check_R(cardX)  then
			use.card = sgs.Card_Parse("#Luahonghuan:" .. cardX:getEffectiveId()..":")
			if use.to then
				use.to = sgs.SPlayerList()
				return
			end
		end
	end
end

sgs.ai_use_priority.Luahonghuan = 15

local function checkJ(self)
	for _, friend in ipairs(self.friends) do
		if friend:containsTrick("indulgence") or friend:containsTrick("supply_shortage") or friend:containsTrick("xiehuib")
			or (self:getFinalRetrial(friend) == 2 and friend:containsTrick("lightning")) then
			return friend
		end
	end
	for _, enemy in ipairs(self.enemies) do
		if enemy:containsTrick("xiehuia") or enemy:containsTrick("YanxiaoCard") then return enemy end
		if self:getKnownNum(enemy) == enemy:getHandcardNum() then
			local brea = true
			for _, cardD in sgs.qlist(enemy:getHandcards()) do
				if not cardD:isKindOf("TrickCard") then brea = false end
			end
			if brea then return enemy end
		end
	end
end
local function checkE(self)
	for _, friend in ipairs(self.friends) do
		if friend:hasArmorEffect("gale_shell") then return friend end
	end
	for _, enemy in ipairs(self.enemies) do
		if (enemy:getEquips():length() > 0) and not ((enemy:getEquips():length() == 1) and enemy:hasArmorEffect("gale_shell")) then
			return enemy
		end
		if self:getKnownNum(enemy) == enemy:getHandcardNum() then
			local brea = true
			for _, cardD in sgs.qlist(enemy:getHandcards()) do
				if not cardD:isKindOf("EquipCard") then brea = false end
			end
			if brea then return enemy end
		end
	end
end

sgs.ai_skill_choice.lualindong = function(self, choices)
	local cards = self:getTurnUse(true)
	self.room:writeToConsole("凛冬测试Q")
	if self:getOverflow() > 1 then return "notAct" end
	local b = false
	local e = false
	local j = false
	for _, card in ipairs(cards) do
		if card:isKindOf("BasicCard") then b = true end
		if card:isKindOf("EquipCard") then e = true end
		if card:isKindOf("TrickCard") then j = true end
	end
	if (e or b) and checkJ(self) and checkE(self) then return "DrawB" end
	if b and j and checkE(self) then return "DrawB" end

	local dismantlement = sgs.Sanguosha:cloneCard("dismantlement")
	dismantlement:setSkillName("lualindong")
	local dummy_use = { isDummy = true , to = sgs.SPlayerList()}
	self:useTrickCard(dismantlement, dummy_use)
	if dummy_use.to:length() > 0 then
		if (b or e or j) then return "DrawB" end
	end
	if #cards == 0 and self:getOverflow() < -1 then return "DrawA" end
	if self:getOverflow() > 0 then return "notAct" end
	if b and e and checkJ(self) then return "DrawB" end
	if #cards == 1 and self:getOverflow() < -1 then return "DrawA" end
	return "notAct"
end

sgs.ai_skill_playerchosen.lualindong = function(self, targets)
	self.room:writeToConsole("凛冬测试")
	local card1 = self.player:getTag("lualindongTC"):toCard()
	local card2 = self.player:getTag("lualindongTC2"):toCard()
	if (card1:isKindOf("TrickCard") and card2:isKindOf("EquipCard")) or (card1:isKindOf("EquipCard") and card2:isKindOf("TrickCard")) then
		for _, enemy in ipairs(self.enemies) do
			if not enemy:isKongcheng() and self:getKnownNum(enemy) == enemy:getHandcardNum() then
				local brea = true
				for _, cardD in sgs.qlist(enemy:getHandcards()) do
					if not cardD:isKindOf("BasicCard") then brea = false end
				end
				if brea then return enemy end
			else
				if not enemy:isKongcheng() then return enemy end
			end
		end
	elseif (card1:isKindOf("BasicCard") and card2:isKindOf("TrickCard")) or (card1:isKindOf("TrickCard") and card2:isKindOf("BasicCard")) then
		return checkE(self)
	elseif (card1:isKindOf("BasicCard") and card2:isKindOf("EquipCard")) or (card1:isKindOf("EquipCard") and card2:isKindOf("BasicCard")) then
		return checkJ(self)
	end

	local dismantlement = sgs.Sanguosha:cloneCard("dismantlement")
	local dummy_use = { isDummy = true , to = sgs.SPlayerList()}
	self:useTrickCard(dismantlement, dummy_use)
	if dummy_use.to:length() > 0 then
		return dummy_use.to:at(0)
	end
end
sgs.ai_skill_playerchosen.lualindong2 = function(self, targets)
	if #self.enemies > 0 then
		self:sort(self.enemies, "defense")
		return self.enemies[1]
	end
end
sgs.ai_skill_cardchosen.lualindong = function(self, who, flags)
	self.room:writeToConsole("凛冬测试2")
	local card1 = self.player:getTag("lualindongTC"):toCard()
	local card2 = self.player:getTag("lualindongTC2"):toCard()
	if (card1:isKindOf("TrickCard") and card2:isKindOf("EquipCard")) or (card1:isKindOf("EquipCard") and card2:isKindOf("TrickCard")) then
		if self:askForCardChosen(who, "h", "None") and self:askForCardChosen(who, "h", "None") > 0 then return self:askForCardChosen(who, "h", "None") end
	elseif (card1:isKindOf("BasicCard") and card2:isKindOf("TrickCard")) or (card1:isKindOf("TrickCard") and card2:isKindOf("BasicCard")) then
		if self:askForCardChosen(who, "e", "None") and self:askForCardChosen(who, "e", "None") > 0 then return self:askForCardChosen(who, "e", "None") end
	elseif (card1:isKindOf("BasicCard") and card2:isKindOf("EquipCard")) or (card1:isKindOf("EquipCard") and card2:isKindOf("BasicCard")) then
		if self:askForCardChosen(who, "j", "None") and self:askForCardChosen(who, "j", "None") > 0 then return self:askForCardChosen(who, "j", "None") end
	end
end
sgs.ai_skill_use["TrickCard+^Nullification,BasicCard+^Jink+^Analeptic,EquipCard|.|.|hand"] = function(self, prompt, method)
	return sgs.ai_skill_use["TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand"](self, prompt, method)
end
sgs.ai_skill_use["TrickCard+^Nullification,BasicCard+^Jink+^Peach+^Analeptic,EquipCard|.|.|hand"] = function(self, prompt, method)
	return sgs.ai_skill_use["TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand"](self, prompt, method)
end
sgs.ai_skill_use["TrickCard+^Nullification,BasicCard+^Jink+^Peach,EquipCard|.|.|hand"] = function(self, prompt, method)
	return sgs.ai_skill_use["TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand"](self, prompt, method)
end
sgs.ai_skill_use["TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand"] = function(self, prompt, method)
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByUseValue(cards)
	local eikaInvoke = false
	for _, ap in sgs.qlist(self.room:getAlivePlayers()) do
		if ap:hasSkill("lualeiluan") and ap:hasFlag("lualeiluan") then
			eikaInvoke = true
		end
	end
	if self.player:hasFlag("xianji") or eikaInvoke then
		self.room:writeToConsole("----eikaInvokeResult----")
		if self.player:getMark("drank") == 0 then
			for _, card in ipairs(cards) do
				if card:isKindOf("Analeptic") then return card:toString() end
			end
		end
		for _, card in ipairs(cards) do
			if card:isKindOf("Ofuda") then
				for _, card2 in ipairs(cards) do
					if card2:isKindOf("Slash") then
						local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
						self:useBasicCard(card2, dummy_use)
						if dummy_use.card then
							for _, playerX in sgs.qlist(dummy_use.to) do
								if self:canBeOfudaTarget(playerX, card) then
									return card:toString() .. "->" .. playerX:objectName()
								end
							end
						end
					end
				end
			end
		end
	end
	if self.player:hasFlag("luajieyouABC") then
		local miyoi = self.room:findPlayerBySkillName("luajieyou")
		if miyoi and miyoi:isAlive() and self:isFriend(miyoi) then
			local xo = self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt() + 1
			if self.player:getMark("lualiangxiaoA") ~= xo then
				for _, card in ipairs(cards) do
					if card:isKindOf("Analeptic") then
						return card:toString()
					end
				end
			end
			if miyoi:isWounded() then
				for _, card in ipairs(cards) do
					if card:getTypeId() == sgs.Card_TypeTrick and card:isKindOf("AOE") then
						return card:toString()
					end
				end
				for _, card in ipairs(cards) do
					if card:getTypeId() == sgs.Card_TypeTrick and not card:isKindOf("Nullification")
							and (card:isKindOf("Duel") or card:isKindOf("yuzhi") or card:isKindOf("FireAttack")) then
						local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
						self:useTrickCard(card, dummy_use)
						if dummy_use.card then
							if dummy_use.to:isEmpty() and not card:isKindOf("IronChain") then	--yun
								return dummy_use.card:toString()
							else
								local target_objectname = {}
								for _, p in sgs.qlist(dummy_use.to) do
									table.insert(target_objectname, p:objectName())
								end
								return dummy_use.card:toString() .. "->" .. table.concat(target_objectname, "+")
							end
						end
					end
				end
				for _, card in ipairs(cards) do
					if card:getTypeId() == sgs.Card_TypeBasic and not card:isKindOf("Jink")
							and (card:isKindOf("Slash")) then
						local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
						self:useBasicCard(card, dummy_use)
						if dummy_use.card then
							if dummy_use.to:isEmpty() then	--yun
								return dummy_use.card:toString()
							else
								local target_objectname = {}
								for _, p in sgs.qlist(dummy_use.to) do
									table.insert(target_objectname, p:objectName())
								end
								return dummy_use.card:toString() .. "->" .. table.concat(target_objectname, "+")
							end
						end
					end
				end
			end
		end
	end
	if self.player:hasFlag("RB") then
		self.room:setPlayerFlag(self.player, "-RB")
		for _, card in ipairs(cards) do
			if card:getTypeId() == sgs.Card_TypeTrick and not card:isKindOf("Nullification")
				and (card:isKindOf("Duel") or card:isKindOf("AOE") or card:isKindOf("yuzhi") or card:isKindOf("FireAttack")) then
				local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
				self:useTrickCard(card, dummy_use)
				if dummy_use.card then
					if dummy_use.to:isEmpty() and not card:isKindOf("IronChain") then	--yun
						return dummy_use.card:toString()
					else
						local target_objectname = {}
						for _, p in sgs.qlist(dummy_use.to) do
							table.insert(target_objectname, p:objectName())
						end
						return dummy_use.card:toString() .. "->" .. table.concat(target_objectname, "+")
					end
				end
			end
		end
		for _, card in ipairs(cards) do
			if card:getTypeId() == sgs.Card_TypeBasic and not card:isKindOf("Jink")
					and (card:isKindOf("Slash")) then
				local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
				self:useBasicCard(card, dummy_use)
				if dummy_use.card then
					if dummy_use.to:isEmpty() then	--yun
						return dummy_use.card:toString()
					else
						local target_objectname = {}
						for _, p in sgs.qlist(dummy_use.to) do
							table.insert(target_objectname, p:objectName())
						end
						return dummy_use.card:toString() .. "->" .. table.concat(target_objectname, "+")
					end
				end
			end
		end
		for _, card in ipairs(cards) do
			if card:getTypeId() == sgs.Card_TypeTrick and not card:isKindOf("Nullification") then
				local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
				self:useTrickCard(card, dummy_use)
				if dummy_use.card then
					if dummy_use.to:isEmpty() and not card:isKindOf("IronChain") then	--yun
						return dummy_use.card:toString()
					else
						local target_objectname = {}
						for _, p in sgs.qlist(dummy_use.to) do
							table.insert(target_objectname, p:objectName())
						end
						return dummy_use.card:toString() .. "->" .. table.concat(target_objectname, "+")
					end
				end
			end
		end
		for _, card in ipairs(cards) do
			if card:getTypeId() == sgs.Card_TypeBasic and not card:isKindOf("Jink") then
				local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
				self:useBasicCard(card, dummy_use)
				if dummy_use.card then
					if dummy_use.to:isEmpty() then	--yun
						return dummy_use.card:toString()
					else
						local target_objectname = {}
						for _, p in sgs.qlist(dummy_use.to) do
							table.insert(target_objectname, p:objectName())
						end
						return dummy_use.card:toString() .. "->" .. table.concat(target_objectname, "+")
					end
				end
			end
		end
		for _, card in ipairs(cards) do
			if card:getTypeId() == sgs.Card_TypeEquip then
				local dummy_use = { isDummy = true }
				self:useEquipCard(card, dummy_use)
				if dummy_use.card then
					self.jiewei_type = sgs.Card_TypeEquip
					return dummy_use.card:toString()
				end
			end
		end
	end
	if self.room:getTag("lualindongA") and self.player:hasSkill("lualindong") then
		local card1 = self.player:getTag("lualindongTC"):toCard()
		for _, card in ipairs(cards) do
			if card:getTypeId() == sgs.Card_TypeTrick and not card:isKindOf("Nullification") and card:getTypeId() ~= card1:getTypeId() and checkE(self) then
				local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
				self:useTrickCard(card, dummy_use)
				if dummy_use.card then
					if dummy_use.to:isEmpty() and not card:isKindOf("IronChain") then	--yun
						return dummy_use.card:toString()
					else
						local target_objectname = {}
						for _, p in sgs.qlist(dummy_use.to) do
							table.insert(target_objectname, p:objectName())
						end
						return dummy_use.card:toString() .. "->" .. table.concat(target_objectname, "+")
					end
				end
			end
		end
		for _, card in ipairs(cards) do
			if card:getTypeId() == sgs.Card_TypeEquip and card:getTypeId() ~= card1:getTypeId() and checkJ(self) then
				local dummy_use = { isDummy = true }
				self:useEquipCard(card, dummy_use)
				if dummy_use.card then
					self.jiewei_type = sgs.Card_TypeEquip
					return dummy_use.card:toString()
				end
			end
		end
		for _, card in ipairs(cards) do
			if card:getTypeId() == sgs.Card_TypeBasic and not card:isKindOf("Jink") and card:getTypeId() ~= card1:getTypeId() then
				local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
				self:useBasicCard(card, dummy_use)
				if dummy_use.card then
					if dummy_use.to:isEmpty() then	--yun
						return dummy_use.card:toString()
					else
						local target_objectname = {}
						for _, p in sgs.qlist(dummy_use.to) do
							table.insert(target_objectname, p:objectName())
						end
						return dummy_use.card:toString() .. "->" .. table.concat(target_objectname, "+")
					end
				end
			end
		end
	end
	for _, card in ipairs(cards) do
		if card:getTypeId() == sgs.Card_TypeTrick and not card:isKindOf("Nullification") then
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			self:useTrickCard(card, dummy_use)
			if dummy_use.card then
				if dummy_use.to:isEmpty() and not card:isKindOf("IronChain") then	--yun
					return dummy_use.card:toString()
				else
					local target_objectname = {}
					for _, p in sgs.qlist(dummy_use.to) do
						table.insert(target_objectname, p:objectName())
					end
					return dummy_use.card:toString() .. "->" .. table.concat(target_objectname, "+")
				end
			end
		elseif card:getTypeId() == sgs.Card_TypeEquip then
			local dummy_use = { isDummy = true }
			self:useEquipCard(card, dummy_use)
			if dummy_use.card then
				self.jiewei_type = sgs.Card_TypeEquip
				return dummy_use.card:toString()
			end
		elseif card:getTypeId() == sgs.Card_TypeBasic and not card:isKindOf("Jink") then
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			self:useBasicCard(card, dummy_use)
			if dummy_use.card then
				if dummy_use.to:isEmpty() then	--yun
					return dummy_use.card:toString()
				else
					local target_objectname = {}
					for _, p in sgs.qlist(dummy_use.to) do
						table.insert(target_objectname, p:objectName())
					end
					return dummy_use.card:toString() .. "->" .. table.concat(target_objectname, "+")
				end
			end
		end
	end
end


local function findjingjietarget(self)
	for _, player in ipairs(self.friends) do
		if player:hasSkill("Luashenqiang") and getCardsNum("Slash", player, self.player) <= 0
			and not player:isKongcheng() then
			local cards = sgs.QList2Table(player:getHandcards())
			self:sortByKeepValue(cards)
			for _, acard in ipairs(cards) do
				if not acard:isKindOf("Duel") and not acard:isKindOf("Snatch") and not acard:isKindOf("Analeptic") and not acard:isKindOf("Banquet")
						and not acard:isKindOf("ExNihilo") and acard:isRed() and not acard:isKindOf("Peach") then
					return player, acard, "heart", "13", "slash"
				end
			end
		end
	end
	for _, player in ipairs(self.friends) do
		if player:hasSkills("luahakurei|luasuiyue") and getCardsNum("Analeptic", player, self.player) <= 0
				and player:getHandcardNum() > 2 then
			local cards = sgs.QList2Table(player:getHandcards())
			self:sortByKeepValue(cards)
			for _, acard in ipairs(cards) do
				if not acard:isKindOf("Duel") and not acard:isKindOf("Snatch") and not acard:isKindOf("Analeptic")
						and not acard:isKindOf("ExNihilo") and not acard:isKindOf("Peach") then
					return player, acard, "heart", "13", "analeptic"
				end
			end
		end
	end
	for _, player in ipairs(self.friends) do
		if player:hasSkill("luamaoyou") and getCardsNum("Slash", player, self.player) <= 0
				and not player:isKongcheng() then
			local cards = sgs.QList2Table(player:getHandcards())
			self:sortByKeepValue(cards)
			for _, acard in ipairs(cards) do
				if not acard:isKindOf("Duel") and not acard:isKindOf("Snatch") and not acard:isKindOf("Analeptic") and not acard:isKindOf("Banquet")
						and not acard:isKindOf("ExNihilo") and not acard:isKindOf("Peach") then
					return player, acard, "heart", "13", "slash"
				end
			end
		end
	end
	for _, player in ipairs(self.friends) do
		if player:hasSkill("luahuapu") then
			local cards = sgs.QList2Table(player:getHandcards())
			self:sortByKeepValue(cards)
			for _, acard in ipairs(cards) do
				if acard:getSuit() ~= sgs.Card_Club then
					return player, acard, "club", "13", "peach"
				end
			end
		end
	end
	for _, enemy in ipairs(self.enemies) do
		if not enemy:isKongcheng() and not enemy:hasSkills("luaxiongshi|luashenjun|LuaYizuo") then
			local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
			for _, acard in sgs.qlist(enemy:getHandcards()) do
				if (acard:hasFlag("visible") or acard:hasFlag(flag)) and not (acard:isKindOf("Hui"))
						and not (acard:isRed() and enemy:hasSkill("luaxiongshi")) and not (acard:getSuit() == sgs.Card_Heart and enemy:hasSkill("Luabaochun")) then
					for _, skill in sgs.qlist(enemy:getVisibleSkillList(true)) do
						local callback = sgs.ai_cardneed[skill:objectName()]
						if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](enemy, acard, self) then
							return enemy, acard, "club", "1", "hui"
						end
					end
				end
			end
			for _, acard in sgs.qlist(enemy:getHandcards()) do
				if (acard:hasFlag("visible") or acard:hasFlag(flag)) and not (acard:isKindOf("Hui"))
					and not (acard:isRed() and enemy:hasSkill("luaxiongshi")) and not (acard:getSuit() == sgs.Card_Heart and enemy:hasSkill("Luabaochun"))
					and (acard:isKindOf("Duel") or acard:isKindOf("Snatch") or acard:isKindOf("Analeptic") or acard:isKindOf("ExNihilo") or acard:isKindOf("Peach")) then
					return enemy, acard, "club", "1", "hui"
				end
			end
		end
	end

	local peach_num = 0
	for _, player in ipairs(self.friends) do
		for _, acard in sgs.qlist(player:getHandcards()) do
			if isCard("Peach", acard, player) then
				peach_num = peach_num + 1
			end
		end
	end
	for _, player in ipairs(self.friends) do
		if self:isWeak(player) and ((peach_num <= 1 and math.random() > 0.5) or peach_num <= 0) then
			if not player:isKongcheng() then
				local cards = sgs.QList2Table(player:getHandcards())
				self:sortByKeepValue(cards)
				for _, acard in ipairs(cards) do
					return player, acard, "heart", "13", "peach"
				end
			end
		end
	end

	for _, player in ipairs(self.friends) do
		if player:hasSkill("luatianhu") and getCardsNum("Hui", player, self.player) <= 0
				and player:getHandcardNum() > 2 and getCardsNum("Jink", player, self.player) >= 1 then
			local cards = sgs.QList2Table(player:getHandcards())
			self:sortByKeepValue(cards)
			for _, acard in ipairs(cards) do
				if not acard:isKindOf("Duel") and not acard:isKindOf("Snatch") and not acard:isKindOf("Analeptic") and not acard:isKindOf("Banquet")
						and not acard:isKindOf("ExNihilo") and not acard:isKindOf("Peach") then
					return player, acard, "heart", "13", "hui"
				end
			end
		end
	end

	for _, player in ipairs(self.friends) do
		if player:hasSkill("luayouqu") and getCardsNum("Peach", player, self.player) <= 0
			and not player:isKongcheng() and player:isWounded() then
			local cards = sgs.QList2Table(player:getHandcards())
			self:sortByKeepValue(cards)
			for _, acard in ipairs(cards) do
				if not acard:isKindOf("Duel") and not acard:isKindOf("Snatch") and not acard:isKindOf("Analeptic") and not acard:isKindOf("Banquet")
						and not acard:isKindOf("ExNihilo") and not acard:isKindOf("Peach") then
					return player, acard, "heart", "13", "peach"
				end
			end
		end
	end

	for _, enemy in ipairs(self.enemies) do
		if not enemy:isKongcheng() and not enemy:hasSkills("luaxiongshi|luashenjun|LuaYizuo") then
			for _, acard in sgs.qlist(enemy:getHandcards()) do
				if not (acard:isKindOf("Hui")) and not (acard:isRed() and enemy:hasSkill("luaxiongshi"))
						and not (acard:getSuit() == sgs.Card_Heart and enemy:hasSkill("Luabaochun")) then
					return enemy, acard, "club", "1", "hui"
				end
			end 
		end
	end
end
local luajingjie_skill = {}
luajingjie_skill.name = "luajingjie"
table.insert(sgs.ai_skills, luajingjie_skill)
luajingjie_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luajingjie") then return end
	self.room:removeTag("luajingjie")
	self.room:removeTag("jingjieTC")
	return sgs.Card_Parse("#luajingjie:.:")
end
sgs.ai_skill_use_func["#luajingjie"] = function(X, use, self)
	local cardS = self:findjingjieCard()
	if not cardS then return end
	if self.player:hasFlag("luajingjieF") then
		use.card = sgs.Card_Parse("#luajingjie:" .. cardS:getEffectiveId() .. ":")
		self.room:setTag("luajingjie" ,sgs.QVariant("heart|13|analeptic"))
		if use.to then
			use.to:append(self.player)
			return
		end
	else
		local target, cardX, a, b, c = findjingjietarget(self)
		if target and cardX and a and b and c then
			use.card = sgs.Card_Parse("#luajingjie:" .. cardS:getEffectiveId() .. ":")
			self.room:setTag("luajingjie" ,sgs.QVariant(a .. "|" .. b  .. "|" .. c ))
			local Carddata2 = sgs.QVariant() -- ai用
			Carddata2:setValue(cardX)
			self.room:setTag("jingjieTC", Carddata2)
			if use.to then
				use.to:append(target)
				return
			end
		end
	end
end
sgs.ai_use_priority.luajingjie = 3
sgs.ai_skill_choice.luajingjie1 = function(self, choices, data)
	self.room:writeToConsole("yukari test1")
	local str = self.room:getTag("luajingjie"):toString()
	str = str:split("|")
	return str[1]
end
sgs.ai_skill_choice.luajingjie2 = function(self, choices, data)
	self.room:writeToConsole("yukari test2")
	local str = self.room:getTag("luajingjie"):toString()
	str = str:split("|")
	return str[2]
end
sgs.ai_skill_choice.luajingjie3 = function(self, choices, data)
	self.room:writeToConsole("yukari test3")
	local str = self.room:getTag("luajingjie"):toString()
	str = str:split("|")
	return str[3]
end
sgs.ai_skill_cardchosen.luajingjie = function(self, who, flags)
	self.room:writeToConsole("yukari test4")
	local slash = self.room:getTag("jingjieTC"):toCard()
	if slash then
		for _, card in sgs.qlist(who:getHandcards()) do
			if card:getEffectiveId() == slash:getEffectiveId() then return card:getEffectiveId() end
		end
	end
	self.room:writeToConsole("yukari bug")
	if self:isFriend(who) then
		local cards = who:getHandcards()
		cards = sgs.QList2Table(cards)
		self:sortByUseValue(cards)
		return cards[#cards]:getEffectiveId()
	else
		local cards = who:getHandcards()
		cards = sgs.QList2Table(cards)
		self:sortByUseValue(cards)
		return cards[1]:getEffectiveId()
	end
end
local function yucePingdian(self, target, to_card)
	if not to_card then to_card = self:getMinCard() end
	if self:getMinCard(target) and to_card:getNumber() <= self:getMinCard(target):getNumber() then return true end
	if self:getMaxCard(target) and to_card:getNumber() > self:getMaxCard(target):getNumber()
			and (self:getKnownNum(target) == target:getHandcardNum()) then return false end
	if to_card:isKindOf("Peach") then return false end
	if to_card:getNumber() > 10 then return false end
	if to_card:getNumber() > 8 then return (math.random() > 0.5) end
	if to_card:getNumber() > 6 then return (math.random() > 0.2) end
	if to_card:getNumber() <= 6 then return true end
end
local function yucePingdian2(self, target)
	local to_card = self:getMaxCard()
	local x = target:getHandcardNum()
	x = (x * x)/30

	local k = math.random()
	if self:getMinCard(target) and to_card:getNumber() <= self:getMinCard(target):getNumber() then return false end
	if self:getMaxCard(target) and to_card:getNumber() > self:getMaxCard(target):getNumber()
			and (self:getKnownNum(target) == target:getHandcardNum()) then return true end
	if to_card:getNumber() > 10 and (k > (0.1 + x)) then return true end
	if to_card:getNumber() > 8 and (k > (0.25 + x)) then return true end
	if to_card:getNumber() > 6 and (k > (0.6 + x)) then return true end
	if to_card:getNumber() <= 6 then return false end
end
local function findluapaoyingtarget(self)
	for _, friend in ipairs(self.friends_noself) do
		if friend:hasSkills("luachaogan|luashishen|luahakurei|luasuiyue|luatongxin|shenpan|luawangshi|luatongling"
				.. "|luaxinyan|luaboli|luachunhui|Luaxianzhe|luajiangsui") and yucePingdian(self, friend) then
			return friend
		end
	end
	--
	local count = 0
	for _, cardD in sgs.qlist(self.player:getHandcards()) do
		if (cardD:getSuit() == sgs.Card_Heart or cardD:getSuit() == sgs.Card_Spade)
			and not cardD:isKindOf("Duel") and not cardD:isKindOf("Snatch") and not cardD:isKindOf("ExNihilo") and not cardD:isKindOf("Peach") then
			count = count + 1
		end
	end

	local fcaerds = sgs.QList2Table(self.player:getCards("h"))
	self:sortByKeepValue(fcaerds)
	local function Check_R(card)
		if card:getSuit() == sgs.Card_Heart or card:getSuit() == sgs.Card_Spade then return false end
		if card:isKindOf("TrickCard") then
			if card:isKindOf("Nullification") then return true end
			if card:isKindOf("Dismantlement") or card:isKindOf("NeedMaribel") then return true end
			if card:isKindOf("Lightning") and not self:willUseLightning(card) then return true end
			if card:isKindOf("AOE") and self:getAoeValue(card) > 35 then return false end
			if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
			if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
			if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
			local dummy_use = {isDummy = true}
			self:useTrickCard(card, dummy_use)
			if not dummy_use.card then return true end
			return false
		end
		if card:isKindOf("EquipCard") then --or card:isKindOf("DefensiveHorse")
			if card:isKindOf("EquipCard") then
				local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
				if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) and rinnosuke
						and rinnosuke:isAlive() and self:isFriend(rinnosuke) then return false end
			end
			if card:isKindOf("Wanbaochui") then return false end
			if card:isKindOf("OffensiveHorse") and self.player:getOffensiveHorse() then
				return true
			end
			if card:isKindOf("DefensiveHorse") and self.player:getDefensiveHorse() then
				return true
			end
			if card:isKindOf("Armor") and self:needToThrowArmor() then return false end
			if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
				local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
						or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
				if bool_3 then return false end
			end
			if card:isKindOf("Weapon") then
				local dummy_use = {isDummy = true}
				self:useEquipCard(card, dummy_use)
				if not dummy_use.card then return true end
				if dummy_use.card and self.player:getWeapon() then return self.player:getWeapon() end
			end
		end
		if card:isKindOf("Slash") or card:isKindOf("Jink") or card:isKindOf("Ofuda") then return true end
		if card:isKindOf("Peach") and self:OverFlowPeach(card) then return true end
		if card:isKindOf("Analeptic") and self:getCardsNum("Analeptic") > 0 then return true end
	end

	for _, enemy in ipairs(self.enemies) do
		local bool = false
		for _, skill in sgs.qlist(enemy:getVisibleSkillList()) do
			if string.find(skill:getDescription(), sgs.Sanguosha:translate("luapaoying2")) then
				bool = true
			end
		end
		if not bool then
			for _, card in sgs.list(self.player:getHandcards()) do
				if Check_R(card) then return enemy end
			end
		end
		if yucePingdian2(self, enemy) and (count > 1 or (count > 0 and math.random() > 0.5)) then
			local cards = sgs.QList2Table(self.player:getHandcards())
			for _, acard in ipairs(cards) do
				if not acard:isKindOf("Duel") and not acard:isKindOf("Snatch") and not acard:isKindOf("Analeptic") and not acard:isKindOf("Banquet")
						and not acard:isKindOf("ExNihilo") and not acard:isKindOf("Peach") then
					return enemy
				end
			end
		end
	end
	for _, enemy in ipairs(self.enemies) do
		if yucePingdian2(self, enemy) and (count > 1 or (count > 0 and math.random() > 0.5)) then
			local cards = sgs.QList2Table(self.player:getHandcards())
			for _, acard in ipairs(cards) do
				if not acard:isKindOf("Duel") and not acard:isKindOf("Snatch") and not acard:isKindOf("Analeptic") and not acard:isKindOf("Banquet")
						and not acard:isKindOf("ExNihilo") and not acard:isKindOf("Peach") then
					return enemy
				end
			end
		end
	end

	for _, friend in ipairs(self.friends_noself) do
		for _, skill in sgs.qlist(friend:getVisibleSkillList()) do
			if string.find(skill:getDescription(), sgs.Sanguosha:translate("luapaoying2")) then
				for _, card in sgs.list(self.player:getHandcards()) do
					if Check_R(card) and yucePingdian(self, friend, card) then return friend end
				end

				for _, card in sgs.list(self.player:getHandcards()) do
					if (count > 1 or (count > 0 and math.random() > 0.5)) then
						return friend
					end
				end
			end
		end
	end
end
function sgs.ai_skill_pindian.luapaoying(minusecard, self, requestor, maxcard, mincard)
	local target = self.room:getTag("luapaoyingTP"):toPlayer()
	if target:hasSkills("luachaogan|luashishen|luahakurei|luasuiyue|luatongxin|shenpan|luawangshi|luatongling"
			.. "|luaxinyan|luaboli|luachunhui|Luaxianzhe|luajiangsui") and target:getHandcardNum() > 0 and self:isFriend(target) then
		if not mincard then self.room:writeToConsole(debug.traceback()) return end
		return mincard
	end

	local function Check_R(card)
		if card:getSuit() == sgs.Card_Heart or card:getSuit() == sgs.Card_Spade then return false end
		if card:isKindOf("TrickCard") then
			if card:isKindOf("Nullification") then return true end
			if card:isKindOf("Dismantlement") or card:isKindOf("NeedMaribel") then return true end
			if card:isKindOf("Lightning") and not self:willUseLightning(card) then return true end
			if card:isKindOf("AOE") and self:getAoeValue(card) > 35 then return false end
			if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
			if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
			if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
			local dummy_use = {isDummy = true}
			self:useTrickCard(card, dummy_use)
			if not dummy_use.card then return true end
			return false
		end
		if card:isKindOf("EquipCard") then --or card:isKindOf("DefensiveHorse")
			if card:isKindOf("EquipCard") then
				local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
				if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) and rinnosuke
						and rinnosuke:isAlive() and self:isFriend(rinnosuke) then return false end
			end
			if card:isKindOf("Wanbaochui") then return false end
			if card:isKindOf("OffensiveHorse") and self.player:getOffensiveHorse() then
				return true
			end
			if card:isKindOf("DefensiveHorse") and self.player:getDefensiveHorse() then
				return true
			end
			if card:isKindOf("Armor") and self:needToThrowArmor() then return false end
			if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
				local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
						or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
				if bool_3 then return false end
			end
			if card:isKindOf("Weapon") then
				local dummy_use = {isDummy = true}
				self:useEquipCard(card, dummy_use)
				if not dummy_use.card then return true end
				if dummy_use.card and self.player:getWeapon() then return self.player:getWeapon() end
			end
		end
		if card:isKindOf("Slash") or card:isKindOf("Jink") or card:isKindOf("Ofuda") then return true end
		if card:isKindOf("Peach") and self:OverFlowPeach(card) then return true end
		if card:isKindOf("Analeptic") and self:getCardsNum("Analeptic") > 0 then return true end
	end

	local bool = false
	for _, skill in sgs.qlist(target:getVisibleSkillList()) do
		if string.find(skill:getDescription(), sgs.Sanguosha:translate("luapaoying2")) then
			bool = true
		end
	end
	if not bool then
		for _, card in sgs.list(self.player:getHandcards()) do
			if Check_R(card) then return card end
		end
	end
	if self:isFriend(target) then return mincard else return maxcard end
end
sgs.ai_skill_playerchosen.luapaoying = function(self, targets, slashX)
	return findluapaoyingtarget(self)
end


sgs.ai_skill_cardask["@yemu"] = function(self, data, pattern, target)
	if self.player:isKongcheng() then return "." end
	local hcards = sgs.QList2Table(self.player:getHandcards())
	self:sortByUseValue(hcards, true)
	return "$" .. hcards[1]:getEffectiveId()
end


local luayewang_skill = {}
luayewang_skill.name = "luayewang"
table.insert(sgs.ai_skills, luayewang_skill)
luayewang_skill.getTurnUseCard = function(self)
	local black_count = 0
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:isBlack() then black_count = black_count + 1 end
	end
	if black_count == 0 and self.player:getHp() < 3 and (self:getCardsNum("Peach") > 0 or self:getCardsNum("Jink") > 0) then return end
	if self:getCardsNum("Peach") > 1 then return end
	if self:slashIsAvailable() then
		return sgs.Card_Parse("#luayewang:.:")
	end
end
sgs.ai_skill_use_func["#luayewang"] = function(X, use, self)
	use.card = sgs.Card_Parse("#luayewang:.:")
	if use.to then
		use.to = sgs.SPlayerList()
		return
	end
end
sgs.ai_use_priority.luayewang = 2

sgs.ai_skill_cardask["@Luashenqiang2"] = function(self, data, pattern, target)
	local slash = data:toCard()
	if self.player:isKongcheng() then return "." end
	local hcards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(hcards)
	if self:isWeak() and self:getCardsNum("Jink") > 0 then
		for _, card in sgs.list(self.player:getHandcards()) do
			if card:getNumber() >= slash:getNumber() and not card:isKindOf("Jink") and not card:isKindOf("Peach") then return "$" .. card:getEffectiveId() end
		end
	end
	local rcount = 0
	for _, slashX in ipairs(self:getCards("Slash")) do
		if slashX:isRed() then rcount = rcount + 1 end
	end
	if rcount == 0 then
		for _, card in sgs.list(self.player:getHandcards()) do
			if card:isRed() then return "$" .. card:getEffectiveId() end
		end
	end
	return "$" .. hcards[1]:getEffectiveId()
end
sgs.ai_skill_invoke.Luashenqiang2 = sgs.ai_skill_invoke.liegong  --如何决定技能是否发动的一个实例

sgs.ai_not_respond.Luahongzhuan = function(self, card, bcard, source) --pay
	if bcard and (bcard:isKindOf("Duel") or bcard:isKindOf("SavageAssault")) then
		if card:isRed() and not self:isWeak() then self.room:writeToConsole("ai_not_respond test"); return true end
	end
end

sgs.ai_skill_invoke.luaxuqu = function(self, data)
	local target = data:toPlayer()
	local x = 0
	local count = 0
	for _, slashX in ipairs(self:getCards("Slash")) do
		for _, ap in sgs.qlist(self.room:getAlivePlayers()) do
			if self:Skadi2(slashX, ap, self.player, true) and (self:isWeak(ap) or ap:objectName() == target:objectName()) and self:isEnemy(ap) then
				x = x + 1
				if self:YouMu2(target, true) then x = x + 1 end
			end
		end
	end
	for _, c in sgs.qlist(self.player:getHandcards()) do
		if c:isKindOf("yuzhi") then
			count = count + 1
			for _, ap in sgs.qlist(self.room:getAlivePlayers()) do
				if not self.room:isProhibited(self.player, ap, c) and self:isEnemy(ap) and self.player:inMyAttackRange(target) then
					x = x + 1
					if (self:isWeak(ap) or ap:objectName() == target:objectName()) then
						x = x + 1
					end
				end
			end
		end
	end
	if self:getOverflow() > 1 and self.player:containsTrick("indulgence") then
		x = x + 1
	end
	if self.player:getTreasure() and self.player:getTreasure():isKindOf("Wanbaochui") then x = x + 1 end
	local cards = self:getTurnUse(true)
	if #cards >= 2 then x = x + 1 end
	return x > 1
end

local luahezou_skill = {}
luahezou_skill.name = "luahezou"
table.insert(sgs.ai_skills, luahezou_skill)
luahezou_skill.getTurnUseCard = function(self)
	local count = 0
	for _, c in sgs.qlist(self.player:getHandcards()) do
		if c:isKindOf("yuzhi") then
			count = count + 1
		end
	end
	if count > 1 and self:getOverflow() > 0 then return end
	if count > 0 and self:getOverflow() > 0 and self.player:hasUsed("yuzhi") then return end
	return sgs.Card_Parse("#luahezou:.:")
end
sgs.ai_skill_use_func["#luahezou"] = function(cardD, use, self)
	local x_num = self:getCardsNum("Jink")
	local y_num = self:getCardsNum("Peach")
	local a_num = self:getCardsNum("Analeptic")
	local z_num = y_num + self.player:getHp()

	local function check_R(card, card2)
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return false end
		end
		if (z_num <= self.player:getMaxHp()) and card:isKindOf("Peach") then return false end
		if (z_num == 1) and card:isKindOf("Analeptic") and (a_num == 1) then return false end
		if card:isKindOf("ExNihilo") and card:isAvailable(self.player) then return false end
		if (card:isKindOf("AOE") and self:getAoeValue(card) > 50) then return false end
		if card:isKindOf("Peach") then return false end
		if card:isKindOf("Indulgence") or card:isKindOf("Wanbaochui") then return false end
		if card2 and card:isKindOf("Jink") and card2:isKindOf("Jink") and x_num <= 2 then return false end
		if card:isKindOf("OffensiveHorse") and not self.player:getOffensiveHorse() then return false end
		if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
		if card:isKindOf("yuzhi") then return false end
		return true
	end
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByUseValue(cards, true)
	for _, cardA in ipairs(cards) do
		if cardA:isRed() and check_R(cardA) then
			for _, cardB in ipairs(cards) do
				if check_R(cardB, cardA) and cardB:getEffectiveId() ~= cardA:getEffectiveId() then
					for _, cardC in ipairs(cards) do
						if check_R(cardC, cardA) and cardC:getEffectiveId() ~= cardB:getEffectiveId() and cardC:getEffectiveId() ~= cardA:getEffectiveId() then
							local toUse = {}
							table.insert(toUse, cardA:getId())
							table.insert(toUse, cardB:getId())
							table.insert(toUse, cardC:getId())
							use.card = sgs.Card_Parse("#luahezou:" .. table.concat(toUse, "+") ..":")
							if use.to then use.to = sgs.SPlayerList(); return end
						end
					end
				end
			end
		end
	end
	for _, cardA in ipairs(cards) do
		if check_R(cardA) then
			for _, cardB in ipairs(cards) do
				if check_R(cardB, cardA) and cardB:getEffectiveId() ~= cardA:getEffectiveId() then
					for _, cardC in ipairs(cards) do
						if check_R(cardC, cardA) and cardC:getEffectiveId() ~= cardB:getEffectiveId() and cardC:getEffectiveId() ~= cardA:getEffectiveId() then
							local toUse = {}
							table.insert(toUse, cardA:getId())
							table.insert(toUse, cardB:getId())
							table.insert(toUse, cardC:getId())
							use.card = sgs.Card_Parse("#luahezou:" .. table.concat(toUse, "+") ..":")
							if use.to then use.to = sgs.SPlayerList(); return end
						end
					end
				end
			end
		end
	end
end
sgs.ai_use_priority.luahezou = 4

sgs.ai_skill_choice.luaduzou = function(self, choices, data)
	local target = data:toPlayer()
	if self:isFriend(target) then return "Draw" end
	return "Discard"
end














