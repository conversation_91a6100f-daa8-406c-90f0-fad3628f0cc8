sgs.ai_skill_use["@@ziliang"] = function(self, data)
	local damage = self.room:getTag("CurrentDamageStruct"):toDamage()
	if hasManjuanEffect(damage.to) then return "." end
	if not self:isFriend(damage.to) then
		if damage.to:getPhase() == sgs.Player_NotActive and self:needKong<PERSON>(damage.to, true) then
			local ids = sgs.QList2Table(self.player:getPile("field"))
			for _, id in ipairs(ids) do
				local card = sgs.Sanguosha:getCard(id)
				if card:isKindOf("Disaster") or card:isKindOf("GodSalvation") or card:isKindOf("AmazingGrace") or card:isKindOf("FireAttack") then
					return "@ZiliangCard="..id
				end
			end
		else
			return "."
		end
	else
		if not (damage.to:getPhase() == sgs.Player_NotActive and self:needKongcheng(damage.to, true)) then
			local ids = sgs.QList2Table(self.player:getPile("field"))
			local cards = {}
			for _, id in ipairs(ids) do table.insert(cards, sgs.Sanguosha:getCard(id)) end
			for _, card in ipairs(cards) do
				if card:isKindOf("Peach") then return "@ZiliangCard="..card:getEffectiveId() end
			end
			for _, card in ipairs(cards) do
				if card:isKindOf("Jink") then return "@ZiliangCard="..card:getEffectiveId() end
			end
			self:sortByKeepValue(cards, true)
			return "@ZiliangCard="..cards[1]:getEffectiveId()
		else
			return "."
		end
	end
end

sgs.ai_card_intention.ZiliangCard = function(self, card, from, to)
		local intention = -40
		if to:getPhase() == sgs.Player_NotActive and self:needKongcheng(to, true) then intention = 10 end
		sgs.updateIntention(from, to, intention)
end

local function huyuan_validate(self, equip_type, is_handcard)
	local targets
	if is_handcard then targets = self.friends else targets = self.friends_noself end
	if equip_type == "SilverLion" then
		for _, enemy in ipairs(self.enemies) do
			if enemy:hasSkills("yizhong|bazhen|linglong") then table.insert(targets, enemy) end
		end
	end
	for _, friend in ipairs(targets) do
		local has_equip = false
		for _, equip in sgs.qlist(friend:getEquips()) do
			if equip:isKindOf(equip_type) then
				has_equip = true
				break
			end
		end
		if not has_equip and not ((equip_type == "Armor" or equip_type == "SilverLion") and friend:hasSkills("yizhong|bazhen")) then
			self:sort(self.enemies, "defense")
			for _, enemy in ipairs(self.enemies) do
				if friend:distanceTo(enemy) == 1 and self.player:canDiscard(enemy, "he") then
					enemy:setFlags("AI_HuyuanToChoose")
					return friend
				end
			end
		end
	end
	return nil
end

sgs.ai_skill_use["@@huyuan"] = function(self, prompt)
	local cards = self.player:getHandcards()
	cards = sgs.QList2Table(cards)
	self:sortByKeepValue(cards)
	if self.player:hasArmorEffect("silver_lion") then	--yun
		local player = huyuan_validate(self, "SilverLion", false)
		if player then return "@HuyuanCard=" .. self.player:getArmor():getEffectiveId() .. "->" .. player:objectName() end
	end
	if self.player:getOffensiveHorse() then
		local player = huyuan_validate(self, "OffensiveHorse", false)
		if player then return "@HuyuanCard=" .. self.player:getOffensiveHorse():getEffectiveId() .. "->" .. player:objectName() end
	end
	if self.player:getWeapon() then
		local player = huyuan_validate(self, "Weapon", false)
		if player then return "@HuyuanCard=" .. self.player:getWeapon():getEffectiveId() .. "->" .. player:objectName() end
	end
	if self.player:getArmor() and self.player:getLostHp() <= 1 and self.player:getHandcardNum() >= 3 then
		local player = huyuan_validate(self, "Armor", false)
		if player then return "@HuyuanCard=" .. self.player:getArmor():getEffectiveId() .. "->" .. player:objectName() end
	end
	for _, card in ipairs(cards) do
		if card:isKindOf("DefensiveHorse") then
			local player = huyuan_validate(self, "DefensiveHorse", true)
			if player then return "@HuyuanCard=" .. card:getEffectiveId() .. "->" .. player:objectName() end
		end
	end
	for _, card in ipairs(cards) do
		if card:isKindOf("OffensiveHorse") then
			local player = huyuan_validate(self, "OffensiveHorse", true)
			if player then return "@HuyuanCard=" .. card:getEffectiveId() .. "->" .. player:objectName() end
		end
	end
	for _, card in ipairs(cards) do
		if card:isKindOf("Weapon") then
			local player = huyuan_validate(self, "Weapon", true)
			if player then return "@HuyuanCard=" .. card:getEffectiveId() .. "->" .. player:objectName() end
		end
	end
	for _, card in ipairs(cards) do
		if card:isKindOf("SilverLion") then
			local player = huyuan_validate(self, "SilverLion", true)
			if player then return "@HuyuanCard=" .. card:getEffectiveId() .. "->" .. player:objectName() end
		end
		if card:isKindOf("Armor") and huyuan_validate(self, "Armor", true) then
			local player = huyuan_validate(self, "Armor", true)
			if player then return "@HuyuanCard=" .. card:getEffectiveId() .. "->" .. player:objectName() end
		end
	end
end

sgs.ai_skill_playerchosen.huyuan = function(self, targets)
	targets = sgs.QList2Table(targets)
	for _, p in ipairs(targets) do
		if p:hasFlag("AI_HuyuanToChoose") then
			p:setFlags("-AI_HuyuanToChoose")
			return p
		end
	end
	return targets[1]
end

sgs.ai_card_intention.HuyuanCard = function(self, card, from, to)
	if to[1]:hasSkills("bazhen|yizhong") then
		if sgs.Sanguosha:getCard(card:getEffectiveId()):isKindOf("SilverLion") then
			sgs.updateIntention(from, to[1], 10)
			return
		end
	end
	sgs.updateIntention(from, to[1], -50)
end

sgs.ai_cardneed.huyuan = sgs.ai_cardneed.equip

sgs.huyuan_keep_value = {
	Peach = 6,
	Jink = 5.1,
	EquipCard = 4.8
}

sgs.ai_skill_use["@@heyi"] = function(self, prompt)
	local players = sgs.QList2Table(self.room:getOtherPlayers(self.player))
	local first, last = self.player, self.player
	for i = 1, #players do
		if self:isFriend(players[i]) then last = players[i] else break end
	end
	for i = #players, 1, -1 do
		if self:isFriend(players[i]) then first = players[i] else break end
	end
	if first:objectName() == self.player:objectName() and last:objectName() == self.player:objectName() then return "." end
	return ("@HeyiCard=.->%s+%s"):format(first:objectName(), last:objectName())
end

sgs.ai_card_intention.HeyiCard = function(self, card, from, tos)
	local players = sgs.QList2Table(self.room:getOtherPlayers(from))
	local first, last = tos[1], tos[2]
	if first:objectName() == from:objectName() then
		for i = 1, #players do
			if players[i]:objectName() ~= last:objectName() then sgs.updateIntention(from, players[i], -60) else break end
		end
		sgs.updateIntention(from, last, -60)
	elseif last:objectName() == from:objectName() then
		for i = #players, 1, -1 do
			if players[i]:objectName() ~= first:objectName() then sgs.updateIntention(from, players[i], -60) else break end
		end
		sgs.updateIntention(from, first, -60)
	else
		if table.indexOf(players, first) < table.indexOf(players, last) then
			first = tos[2]
			last = tos[1]
		end
		for i = 1, #players do
			if players[i]:objectName() ~= last:objectName() then sgs.updateIntention(from, players[i], -60) else break end
		end
		for i = #players, 1, -1 do
			if players[i]:objectName() ~= first:objectName() then sgs.updateIntention(from, players[i], -60) else break end
		end
		sgs.updateIntention(from, last, -60)
		sgs.updateIntention(from, first, -60)
	end
end

sgs.ai_skill_invoke.tianfu = function(self, data)
	local jiangwei = data:toPlayer()
	return jiangwei and self:isFriend(jiangwei)
end

sgs.ai_skill_invoke.shoucheng = function(self, data)
	local move = data:toMoveOneTime()
	local from
	if move.from then from = findPlayerByObjectName(self.room, move.from:objectName()) end
	return from and self:isFriend(from)
			and not (from:getPhase() == sgs.Player_NotActive and (from:hasSkill("manjuan") or self:needKongcheng(from, true)))
end

sgs.ai_skill_choice.shoucheng = function(self, choices)
	return (self.player:getPhase() == sgs.Player_NotActive and self:needKongcheng(self.player, true)) and "reject" or "accept"
end

local shangyi_skill = {}
shangyi_skill.name = "shangyi"
table.insert(sgs.ai_skills, shangyi_skill)
shangyi_skill.getTurnUseCard = function(self)
	local card_str = ("@ShangyiCard=.")
	local shangyi_card = sgs.Card_Parse(card_str)
	assert(shangyi_card)
	return shangyi_card
end

sgs.ai_skill_use_func.ShangyiCard = function(card, use, self)
	if self.player:hasUsed("ShangyiCard") then return end
	self:sort(self.enemies, "handcard")

	for index = #self.enemies, 1, -1 do
		if not self.enemies[index]:isKongcheng() and self:objectiveLevel(self.enemies[index]) > 0 
			and not self:doNotDiscard(self.enemies[index], "h") then	--yun
				use.card = card
				if use.to then
					use.to:append(self.enemies[index])
				end
				return
		end
	end
end

sgs.ai_skill_choice.shangyi = function(self, choices)
	return "handcards"
end

sgs.ai_use_value.ShangyiCard = 4
sgs.ai_use_priority.ShangyiCard = 9
sgs.ai_card_intention.ShangyiCard = 50

sgs.ai_skill_invoke.niaoxiang = function(self, data)
	local p = data:toPlayer()
	if not self:isEnemy(p) then return false end
	if p:hasSkills("leiji|nosleiji|olleiji") and getCardsNum("Jink", p, self.player) >= 1 then return false end
	return true
end

sgs.ai_skill_invoke.yicheng = function(self, data)
	local player = data:toPlayer()
	if hasManjuanEffect(player) then
		if player:canDiscard(player, "he") then return self:isEnemy(player) else return false end
	else
		return self:isFriend(player)
	end
end

sgs.ai_skill_discard.yicheng = function(self, discard_num, min_num, optional, include_equip)
	local unpreferedCards = {}
	local cards = sgs.QList2Table(self.player:getHandcards())

	if self:getCardsNum("Slash") > 1 then
		self:sortByKeepValue(cards)
		for _, card in ipairs(cards) do
			if card:isKindOf("Slash") then table.insert(unpreferedCards, card:getId()) end
		end
		table.remove(unpreferedCards, 1)
	end

	local num = self:getCardsNum("Jink") - 1
	if self.player:getArmor() then num = num + 1 end
	if num > 0 then
		for _, card in ipairs(cards) do
			if card:isKindOf("Jink") and num > 0 then
				table.insert(unpreferedCards, card:getId())
				num = num - 1
			end
		end
	end
	for _, card in ipairs(cards) do
		if (card:isKindOf("Weapon") and self.player:getHandcardNum() < 3) or card:isKindOf("OffensiveHorse")
			or self:getSameEquip(card, self.player) or card:isKindOf("AmazingGrace") or card:isKindOf("Lightning") then
			table.insert(unpreferedCards, card:getId())
		end
	end

	if self.player:getWeapon() and self.player:getHandcardNum() < 3 then
		table.insert(unpreferedCards, self.player:getWeapon():getId())
	end

	if self:needToThrowArmor() then
		table.insert(unpreferedCards, self.player:getArmor():getId())
	end

	if self.player:getOffensiveHorse() and self.player:getWeapon() then
		table.insert(unpreferedCards, self.player:getOffensiveHorse():getId())
	end

	for index = #unpreferedCards, 1, -1 do
		local card = sgs.Sanguosha:getCard(unpreferedCards[index])	--yun
		if not self.player:isJilei(card) and not self.player:isCardLimited(card, sgs.Card_MethodDiscard) then return { unpreferedCards[index] } end
	end

	return self:askForDiscard("dummyreason", 1, 1, false, true)
end

sgs.ai_skill_invoke.qianhuan = function(self, data)
	local use = data:toCardUse()
	local card = use.card	--yun 
	if not card then	
		local yuji = self.room:findPlayerBySkillName("qianhuan")
		return yuji and self:isFriend(yuji) and yuji:getPile("sorcery"):length() < 4
	else
		local to = use.to:first()
		local from = use.from
		if self:isFriend(to, self.player) then
			if card:isKindOf("Peach") or card:isKindOf("Analeptic") or card:isKindOf("ExNihilo") or card:isKindOf("YanxiaoCard") or card:isKindOf("Lightning") then return false end
			if from then
				if from:objectName() == to:objectName() then return false end
				if card:isKindOf("Slash") then
					if not self:slashIsEffective(card, to, from) then return false end
					if self:isPriorFriendOfSlash(to, card, use.from) then return false end
					if self:getDamagedEffects(to, from, true) then return false end
					if self:needToLoseHp(to, from, true) then return false end
				end
				if card:isKindOf("Duel") or card:isKindOf("FireAttack") then
					if not self:hasTrickEffective(card, to, from) then return false end
					if self:getDamagedEffects(to, from) then return false end
					if self:needToLoseHp(to, from, false) then return false end
				end
				if card:isKindOf("Snatch") or card:isKindOf("Dismantlement") then
					if not self:hasTrickEffective(card, to, from) then return false end
					if self:doNotDiscard(to) then return false end 
					if self:isFriend(to, from) then
						if self:needKongcheng(to) then return false end
						if self:needToThrowArmor(to) then return false end
						if to:containsTrick("indulgence") or to:containsTrick("supply_shortage") or to:containsTrick("lightning") then return false end
					end
				end
				if card:isKindOf("Lightning") then
					if self:isFriend(from, self.player) then return false end
				end
				return true
			end
			return true
		else
			return self:isFriend(to, self.player) and to:objectName() ~= self.player:objectName()
		end
	end
end

sgs.ai_skill_choice.qianhuan = function(self, choices, data)
	local use = data:toCardUse()
	local card = use.card	--yun
	local from = use.from
	local to = self.player
	if card:isKindOf("Peach") or card:isKindOf("Analeptic") or card:isKindOf("ExNihilo") or card:isKindOf("YanxiaoCard") then return "reject" end
		if from then
			if from:objectName() == to:objectName() then return "reject" end
			if card:isKindOf("Slash") then
				if not self:slashIsEffective(card, to, from) then return "reject" end
				if self:isPriorFriendOfSlash(to, card, use.from) then return "reject" end
				if self:getDamagedEffects(to, from, true) then return "reject" end
				if self:needToLoseHp(to, from, true) then return "reject" end
			end
			if card:isKindOf("Duel") or card:isKindOf("FireAttack") then
				if not self:hasTrickEffective(card, to, from) then return "reject" end
				if self:getDamagedEffects(to, from) then return "reject" end
				if self:needToLoseHp(to, from, false) then return "reject" end
			end
			if card:isKindOf("Snatch") or card:isKindOf("Dismantlement") then
				if not self:hasTrickEffective(card, to, from) then return "reject" end
				if self:doNotDiscard(to) then return "reject" end
				if self:isFriend(to, from) then
					if self:needKongcheng(to) then return "reject" end
					if self:needToThrowArmor(to) then return "reject" end
					if to:containsTrick("indulgence") or to:containsTrick("supply_shortage") then return "reject" end
				end
			end
			if card:isKindOf("Lightning") then
				if self:isFriend(from, self.player) then return "reject" end
			end
			return "accept"
		end
	return "accept"
end

local function will_discard_zhendu(self)
	local current = self.room:getCurrent()
	local need_damage = self:getDamagedEffects(current, self.player) or self:needToLoseHp(current, self.player, false, true)	--yun
	if self:isFriend(current) then
		if current:getMark("drank") > 0 and not need_damage then return -1 end
		--[[
		if (getKnownCard(current, self.player, "Slash") > 0 or (getCardsNum("Slash", current, self.player) >= 1 and current:getHandcardNum() >= 2))
			and (not self:damageIsEffective(current, nil, self.player) or current:getHp() > 2 or (getCardsNum("Peach", current, self.player) > 1 
			and not self:isWeak(current))) then
			local slash = sgs.Sanguosha:cloneCard("slash")
			local trend = 3
			if current:hasWeapon("axe") then trend = trend - 1
			elseif current:hasSkills("liegong|kofliegong|tieji|wushuang|niaoxiang") then trend = trend - 0.4 end
			for _, enemy in ipairs(self.enemies) do
				if current:canSlash(enemy) and not self:slashProhibit(slash, enemy, current)
					and self:slashIsEffective(slash, enemy, current) and sgs.isGoodTarget(enemy, self.enemies, self, true) then
					return trend
				end
			end
		end
		]]	--yun
		if need_damage then return 3 end
	elseif self:isEnemy(current) then
		if not self:damageIsEffective(current, nil, self.player) then return -5 end
		if current:getHp() == 1 and not current:hasSkill("buqu") and not current:hasSkill("nosbuqu") then return 5 end	--yun
		if need_damage or current:getHandcardNum() >= 4 then return -1 end
		if self:isWeak(current) then return 2 end
		if getKnownCard(current, self.player, "Slash") == 0 and getCardsNum("Slash", current, self.player) < 1 then return 3.5 end
	end
	return -1
end

sgs.ai_skill_cardask["@zhendu-discard"] = function(self, data)
	local discard_trend = will_discard_zhendu(self)
	if discard_trend <= 0 then return "." end
	
	--if self.player:getHandcardNum() + math.random(1, 100) / 100 >= discard_trend then	--yun
	if discard_trend then
		local cards = sgs.QList2Table(self.player:getHandcards())
		self:sortByKeepValue(cards)
		for _, card in ipairs(cards) do
			--if not self:isValuableCard(card, self.player) then return "$" .. card:getEffectiveId() end	--yun
			return "$" .. card:getEffectiveId()
		end
	end
	return "."
end
