---
--- Generated by <PERSON><PERSON>ua(https://github.com/<PERSON>Lua)
--- Created by 2020.
--- DateTime: 2021/2/17 14:16
---
sgs.ai_skill_discard.wanbaochui = function(self, discard_num, min_num, optional, include_equip)
    self.room:writeToConsole("wanbaochui AI test")
    local cards = sgs.QList2Table(self.player:getCards("h"))
    self:sortByKeepValue(cards)
    for _,card in ipairs(cards) do
        local insert = true
        for _, askill in sgs.qlist(self.player:getVisibleSkillList(true)) do
            local callback = sgs.ai_cardneed[askill:objectName()]
            if type(callback)=="function" and callback(self.player, card, self) then
                insert = false
            end
        end
        if card:isKindOf("EquipCard") then
            local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
            if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) and rinnosuke
                    and rinnosuke:isAlive() and self:isFriend(rinnosuke) then insert = false end
        end
        if insert then return card:getEffectiveId() end
    end
end

sgs.ai_skill_invoke.shanghai = function(self, data)
    self.room:writeToConsole("shanghai test")
    local card = data:toCardUse().card
    if self:isFriend(data:toCardUse().from) then return false end
    local target = data:toCardUse().from
    if card:isKindOf("Slash") then
        self.room:writeToConsole("shanghai test")
        if self:AtomDamageCount2(self.player, data:toCardUse().from, false, card) > 1 then return true end
        if sgs.ai_skill_cardask["slash-jink"](self, card:getEffectiveId(), "jink", data:toCardUse().from) ~= "." then return false end
        if target:hasSkills("luasilian|Luayuelong|luajiejie|luazhuwu") then return true end
        if self.player:getHp() > 2 then return false end
        return true
    end
    if self:needToLoseHp(self.player) and (card:isKindOf("Duel") or card:isKindOf("ArcheryAttack") or card:isKindOf("SavageAssault")
        or card:isKindOf("Slash") or card:isKindOf("yuzhi") or card:isKindOf("Hui")) then return false end
    local xo = self.room:getLord():getMark("@clock_time") + 1
    if self.player:hasSkill("luajunzhen") and self.player:getMark("luajunzhenk") == xo and not self:isWeak() then
        return false
    end
    --if card:isKindOf("Slash") and sgs.ai_skill_cardask["slash-jink"](self, card, ".", self.room:getCurrent()) == "." then return false end
    if (card:isKindOf("Duel") or card:isKindOf("ArcheryAttack") or card:isKindOf("SavageAssault")
        or card:isKindOf("Snatch") or card:isKindOf("Dismantlement") or card:isKindOf("Slash")
            or card:isKindOf("SupplyShortage") or card:isKindOf("Indulgence") or card:isKindOf("yuzhi") or card:isKindOf("Hui")) then return true end
end

function sgs.ai_weapon_value.pundarika(self, enemy, player)
    if self:shouldEPundarika(player) then
        return 5
    end
end
function sgs.ai_slash_weaponfilter.pundarika(self, to, player)
    return true
end
sgs.ai_skill_invoke.luapundarika = function(self, data)
    return true
end