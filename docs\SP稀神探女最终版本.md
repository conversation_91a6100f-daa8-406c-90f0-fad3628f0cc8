# SP稀神探女最终版本

## 📋 实现说明

我已经重写了SP稀神探女的诳言技能，采用了最简单直接的方法，确保技能能够正常工作。

## 🔧 技术方案

### 1. 回到原始方案
- **不使用** Pindian 事件处理
- **使用** PindianVerifying 事件保存拼点牌信息
- **在技能卡的 on_use 中直接处理** 拼点成功后的效果

### 2. 技能架构
```
诳言技能完整架构：
├── kuangyan_card     # 技能卡：执行拼点 + 处理拼点成功效果
├── kuangyan         # 视为技能：响应@@kuangyan
└── kuangyan_trigger # 触发技能：监听出牌阶段开始 + 保存拼点牌信息
```

### 3. 核心逻辑

#### 技能卡实现
```lua
kuangyan_card = sgs.CreateSkillCard{
    on_use = function(self, room, source, targets)
        room:notifySkillInvoked(source, "kuangyan")
        local success = source:pindian(targets[1], "kuangyan", nil)
        if success then
            -- 拼点成功，立即处理效果
            -- 1. 选择目标
            -- 2. 获取拼点牌
            -- 3. 依次使用拼点牌
            -- 4. 剩余牌当乐不思蜀
        end
    end
}
```

#### 拼点牌信息保存
```lua
kuangyan_trigger = sgs.CreateTriggerSkill{
    events = {sgs.EventPhaseStart, sgs.PindianVerifying},
    on_trigger = function(self, event, player, data, room)
        if event == sgs.PindianVerifying then
            local pindian = data:toPindian()
            if pindian.reason == "kuangyan" then
                -- 保存拼点牌信息到Tag
                pindian.from:setTag("KuangyanPindianCard", sgs.QVariant(pindian.from_card:getId()))
                pindian.to:setTag("KuangyanPindianCard", sgs.QVariant(pindian.to_card:getId()))
            end
        end
    end
}
```

## 🎮 技能执行流程

```
1. 其他角色出牌阶段开始
   ↓ (kuangyan_trigger 监听 EventPhaseStart)
2. 询问稀神探女是否发动诳言
   ↓ (用户选择目标)
3. 执行拼点
   ↓ (kuangyan_card 调用 source:pindian())
4. PindianVerifying 事件触发
   ↓ (kuangyan_trigger 保存拼点牌信息)
5. 拼点结算完成，返回结果
   ↓ (kuangyan_card 检查 success)
6. 如果拼点成功：
   ├── 选择拼点牌的使用目标
   ├── 获取保存的拼点牌信息
   ├── 依次使用能使用的拼点牌
   └── 剩余牌当乐不思蜀对拼点对象使用
```

## ✅ 关键改进

### 1. 简化架构
- **移除了** 复杂的 Pindian 事件处理技能
- **保留了** 原始的 PindianVerifying 保存机制
- **在技能卡中直接处理** 拼点成功后的效果

### 2. 可靠的信息获取
```lua
-- 在 PindianVerifying 事件中保存
pindian.from:setTag("KuangyanPindianCard", sgs.QVariant(pindian.from_card:getId()))
pindian.to:setTag("KuangyanPindianCard", sgs.QVariant(pindian.to_card:getId()))

-- 在技能卡中获取
local source_card_tag = source:getTag("KuangyanPindianCard")
local target_card_tag = targets[1]:getTag("KuangyanPindianCard")
```

### 3. 完整的卡牌处理
```lua
-- 依次使用拼点牌
for _, card in ipairs(cards_to_use) do
    if card and not card:isVirtualCard() then
        local can_use = false
        local use = sgs.CardUseStruct()
        use.from = targets[1]  -- 拼点对象使用
        use.card = card
        
        -- 根据卡牌类型设置目标和使用方式
        if card:isKindOf("Slash") then
            -- 杀的处理
        elseif card:isKindOf("Peach") or card:isKindOf("Analeptic") then
            -- 桃和酒的处理
        elseif card:isKindOf("EquipCard") then
            -- 装备牌的处理
        elseif card:isKindOf("TrickCard") then
            -- 锦囊牌的处理
        end
        
        if not can_use then
            table.insert(remaining_cards, card)
        end
    end
end

-- 剩余牌当乐不思蜀使用
for _, remaining_card in ipairs(remaining_cards) do
    local indulgence = sgs.Sanguosha:cloneCard("indulgence", ...)
    -- 对拼点对象自身使用
end
```

## 🎯 技能特点

### 1. 触发时机
- **其他角色的出牌阶段开始时**，稀神探女可以选择发动诳言

### 2. 拼点机制
- 稀神探女与目标角色进行拼点
- 使用双方手牌中的牌进行比较

### 3. 拼点成功效果
1. **选择目标**：稀神探女选择一名角色作为拼点牌的使用目标
2. **使用拼点牌**：拼点对象依次使用双方的拼点牌对选择的目标
3. **剩余牌处理**：不能使用的拼点牌当【乐不思蜀】对拼点对象自身使用

### 4. 卡牌使用规则
- **杀**：只能对其他角色使用
- **桃/酒**：可以对任意角色使用
- **装备牌**：装备到目标角色身上
- **锦囊牌**：根据类型设置目标
  - AOE锦囊：无需目标
  - 单体锦囊：对选择的目标使用
  - 延时锦囊：检查目标有效性

## 📊 文件信息

- **文件路径**：`extensions/spspsp.lua`
- **总行数**：178行
- **技能数量**：2个（kuangyan, kuangyan_trigger）
- **翻译项**：完整的中文本地化

## 🚀 使用方法

1. **加载扩展包**：在游戏中启用 `spspsp` 扩展包
2. **选择武将**：选择 SP稀神探女
3. **发动技能**：在其他角色出牌阶段开始时选择发动诳言
4. **拼点操作**：选择拼点目标，进行拼点
5. **效果执行**：拼点成功后选择拼点牌的使用目标

## ✅ 测试要点

1. **基础功能**：
   - [ ] 诳言技能能否正常发动
   - [ ] 拼点机制是否正确
   - [ ] 拼点成功后是否有后续效果

2. **目标选择**：
   - [ ] 能否正确选择拼点牌的使用目标
   - [ ] 选择界面是否正常显示

3. **卡牌使用**：
   - [ ] 不同类型的拼点牌是否正确使用
   - [ ] 不能使用的牌是否正确转换为乐不思蜀

4. **边界情况**：
   - [ ] 拼点失败时是否正确结束
   - [ ] 没有可用目标时的处理
   - [ ] 拼点牌为特殊牌时的处理

这个版本采用了最直接的实现方式，应该能够正常工作。如果还有问题，请告诉我具体的错误信息！
