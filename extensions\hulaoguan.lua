extension = sgs.Package("hulaoguan", sgs.Package_GeneralPack)
first = true
invoke = false
chongzhuCard = sgs.CreateSkillCard{
	name = "chongzhu",
	will_throw = false,
	target_fixed = true,
	on_use = function(self, room, source, targets)
		room:moveCardTo(sgs.Sanguosha:getCard(self:getSubcards():first()), source, nil, sgs.Player_DiscardPile, sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_RECAST, source:objectName(), self:objectName(), ""))
		room:broadcastSkillInvoke("@recast")
		local log = sgs.LogMessage()
		log.type = "#UseCard_Recast"
		log.from = source
		log.card_str = ""..sgs.Sanguosha:getCard(self:getSubcards():first()):toString()
		room:sendLog(log)
		source:drawCards(1, "recast")
	end
}
chongzhu = sgs.CreateOneCardViewAsSkill{
	name = "chongzhu&",
	view_filter = function(self, card)
		return card:isKindOf("Weapon") and not sgs.Self:isJilei(card) and not card:isEquipped()
	end, 
	view_as = function(self, card) 
		local skill_card = chongzhuCard:clone()
		skill_card:setSkillName(self:objectName())
		skill_card:addSubcard(card:getId())
		return skill_card
	end
}
tiger_kill = sgs.CreateTriggerSkill{
	name = "tiger_kill",
	events = {sgs.TurnStart, sgs.EventPhaseStart, sgs.HpChanged, sgs.CardsMoveOneTime},
	global = true,
	priority = 1,
	on_trigger = function(self, event, player, data, room)
		local xianfeng
		for _,p in sgs.qlist(room:getAllPlayers()) do
			if p:getMark("@hulaoguan") > 0 then
				invoke = true
			end
		end
		for i = 4, 2, -1 do
			for _,p in sgs.qlist(room:getAllPlayers()) do
				if p:getSeat() == i then
					xianfeng = p
				end
			end
		end
		if not invoke then return false end
		if event == sgs.TurnStart then
			if first and player:getMark("@hulaoguan") > 0 then 
				first = false 
				return true 
			end
			if player:getMark("hulaoguan") > 0 and player:getMark("@hulaoguan") > 0 and player:getGeneralName() == "shenlvbu1"  then return true end
			if player:getMark("time_stop") > 0 then
				room:setPlayerMark(player, "time_stop", 0)
				return true
			end
			if player:objectName() == xianfeng:objectName() then
				for _,p in sgs.qlist(room:getAllPlayers(true)) do
					if p:isDead() then
						room:addPlayerMark(p, "hulaoguan"..p:getGeneralName())
						if p:getMark("hulaoguan"..p:getGeneralName()) == 4 then
							room:setPlayerProperty(p, "hp", sgs.QVariant(3))
							room:revivePlayer(p)
							p:drawCards(3)
						end
					end
				end
			end
		elseif event == sgs.EventPhaseStart then
			if player:getPhase() == sgs.Player_NotActive then
				if not player:isLord() and room:getLord():getGeneralName() == "shenlvbu1" then 
					room:setPlayerMark(room:getLord(), "hulaoguan", 0)
					room:getLord():gainAnExtraTurn()
					room:addPlayerMark(room:getLord(), "hulaoguan")
				end
			end
		elseif (event == sgs.HpChanged or event == sgs.CardsMoveOneTime) and (room:getLord():getHp() <= 4 or room:getTag("SwapPile"):toInt() >= 1) and room:getLord():getGeneralName() == "shenlvbu1" then
			if room:getLord():isWounded() then room:loseMaxHp(room:getLord(), room:getLord():getMaxHp() - math.min(player:getLostHp(), 4)) end
			room:setPlayerProperty(room:getLord(), "hp", sgs.QVariant(4))
			local choice = room:askForChoice(room:getLord(), "bianshen", "shenlvbu2+shenlvbu_gui")
			room:changeHero(room:getLord(), choice, false)
			for _,p in sgs.qlist(room:getAllPlayers()) do
				room:addPlayerMark(p, "time_stop")
				if p:isLord() then break end
			end
			if room:getLord():isChained() then
				room:setPlayerProperty(room:getLord(), "chained", sgs.QVariant(false))
			end
			if not room:getLord():faceUp() then
				room:getLord():turnOver()
			end
			local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			for _,card in sgs.qlist(room:getLord():getCards("j")) do
				dummy:addSubcard(card)
				end
			if room:getLord():getWeapon() then
				dummy:addSubcard(room:getLord():getWeapon())
			end
			room:throwCard(dummy, room:getLord())
			room:getLord():gainAnExtraTurn()
			room:throwEvent(sgs.TurnBroken)
		end
	end
}
local skills = sgs.SkillList()
if not sgs.Sanguosha:getSkill("tiger_kill") then skills:append(tiger_kill) end
if not sgs.Sanguosha:getSkill("chongzhu") then skills:append(chongzhu) end
sgs.Sanguosha:addSkills(skills)
sgs.LoadTranslationTable{ 
	["hulaoguan"]="虎牢关",
	["@hulaoguan"]="虎牢关",
	["chongzhu"]="重铸",
}
return {extension}