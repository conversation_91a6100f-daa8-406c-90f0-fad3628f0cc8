
extension_pay_a =sgs.Package("pay")
shinki = sgs.General(extension_pay_a,"shinki$","god",3,false,false,false)
shinkiA = sgs.General(extension_pay_a,"shinkiA","god",3,false,true,true)

sanae = sgs.General(extension_pay_a,"sanae","luafeng",3,false,false,false)
sanaeA = sgs.General(extension_pay_a,"sanaeA","luafeng",3,false,true,true)
sanaeB = sgs.General(extension_pay_a,"sanaeB","luafeng",3,false,true,true)
sanaeC = sgs.General(extension_pay_a,"sanaeC","luafeng",3,false,true,true)
sanaeD = sgs.General(extension_pay_a,"sanaeD","god",3,false,true,true)

yuka = sgs.General(extension_pay_a,"yuka","luacai",4,false,false,false)
yukaA = sgs.General(extension_pay_a,"yukaA","luacai",4,false,true,true)
yukaB = sgs.General(extension_pay_a,"yukaB$","luaxi",8,false,true,true)

reisen = sgs.General(extension_pay_a,"reisen","luayue",4,false,false,false)
reisenA = sgs.General(extension_pay_a,"reisenA","luayue",4,false,true,true)
reisenB = sgs.General(extension_pay_a,"reisenB","luayue",3,false,true,true)
reisenC = sgs.General(extension_pay_a,"reisenC","luayue",4,false,true,true)
reisenD = sgs.General(extension_pay_a,"reisenD","luayue",4,false,true,true)

toyohime = sgs.General(extension_pay_a,"toyohime","luayue",4,false,false,false)
parsee = sgs.General(extension_pay_a,"parsee","luadi",4,false,false,false)
parseeA = sgs.General(extension_pay_a,"parseeA","luadi",4,false,true,true)
nyasama = sgs.General(extension_pay_a,"nyasama$","qun",4,false,false,false)
joon = sgs.General(extension_pay_a,"joon","god",3,false,false,false)

youmu = sgs.General(extension_pay_a,"youmu","luayao",4,false,true,true, 3) 

medicine = sgs.General(extension_pay_a,"medicine","luaxing",3,false,false,false)
hatate = sgs.General(extension_pay_a,"hatate","luafeng",4,false,false,false)
alice = sgs.General(extension_pay_a,"alice","luacai",3,false,false,false)
aliceA = sgs.General(extension_pay_a,"aliceA","luacai",3,false,true,true)
aliceB = sgs.General(extension_pay_a,"aliceB","luacai",3,false,true,true)
aliceC = sgs.General(extension_pay_a,"aliceC","luacai",3,false,true,true)
aliceD = sgs.General(extension_pay_a,"aliceD","luacai",3,false,true,true)
aliceE = sgs.General(extension_pay_a,"aliceE","luacai",3,false,true,true)
payceshi = sgs.General(extension_pay_a,"payceshi","god",4,false,true,true)



luasanaexCard = sgs.CreateSkillCard{
	name = "luasanaex",
	will_throw = false,
	--handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		local card = sgs.Self:getMark("sanaex") - 1
		card = sgs.Sanguosha:getCard(card)
		card:setSkillName(self:objectName())
		for _, cardX in sgs.list(sgs.Self:getHandcards()) do
			if cardX:hasFlag("luasanaess") then
				card:addSubcard(cardX)
			end
		end
		if card and card:targetFixed() and not response then
			return false
		end
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end

		return card and card:targetFilter(qtargets, to_select, sgs.Self) and not sgs.Self:isProhibited(to_select, card, qtargets)
	end,
	feasible = function(self, targets)
		local card = sgs.Self:getMark("sanaex") - 1
		card = sgs.Sanguosha:getCard(card)
		card:setSkillName(self:objectName())

		for _, cardX in sgs.list(sgs.Self:getHandcards()) do
			if cardX:hasFlag("luasanaess") then
				card:addSubcard(cardX)
			end
		end
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		return card and card:targetsFeasible(qtargets, sgs.Self)
	end,
	on_validate = function(self, card_use)
		local card = card_use.from:getMark("sanaex") - 1
		card = sgs.Sanguosha:getCard(card)
		local use_card = sgs.Sanguosha:cloneCard(card:objectName())
		for _, cardX in sgs.list(card_use.from:getHandcards()) do
			if cardX:hasFlag("luasanaess") then
				use_card:addSubcard(cardX)
			end
		end
		use_card:setSkillName(self:objectName())
		local available = true
		for _,p in sgs.qlist(card_use.to) do
			if card_use.from:isProhibited(p,use_card) then
				available = false
				break
			end
		end
		available = available and use_card:isAvailable(card_use.from)
		if not available then
			local dummy = sgs.Sanguosha:cloneCard("jink")
			for _, cardX in sgs.list(card_use.from:getHandcards()) do
				if cardX:hasFlag("luasanaess") then
					dummy:addSubcard(cardX)
				end
			end
			card_use.from:getRoom():throwCard(dummy, nil, card_use.from)
			return nil
		end
		if available then
			if (not card_use.from:hasFlag("luayuetuan")) and use_card:isKindOf("Slash") then card_use.from:getRoom():setPlayerFlag(card_use.from, "yuechongslash") end
			return use_card
		end
	end,
}
luasanaexVS = sgs.CreateZeroCardViewAsSkill{
	name = "luasanaex",
	response_pattern = "@@luasanaex",
	view_as = function(self, cards)
		return luasanaexCard:clone()
	end
} 
luasanaex = sgs.CreateTriggerSkill {
	name = "luasanaex",
	global = true,
	view_as_skill = luasanaexVS,
	events = { sgs.EventPhaseChanging },
	on_trigger = function(self, event, player, data, room)
		local room = player:getRoom()
		local change = data:toPhaseChange()
		if change.to == sgs.Player_NotActive then
			for _, sanaeX in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do 
				local pattern = ""
				for _, cardA in sgs.list(sanaeX:getHandcards()) do
					if cardA:hasFlag("luasanaeSS") then 
						if pattern == "" then
							pattern = cardA:toString()
						else
							pattern = pattern .. "," .. cardA:toString()
						end 
					end 
					room:setCardFlag(cardA, "-luasanaeSS")
				end 
				room:writeToConsole("luasanaex test X " .. pattern)
				if pattern and pattern ~= "" then 
					local card2 = room:askForCard(sanaeX, pattern, "@luasanaexA", sgs.QVariant(), sgs.Card_MethodNone)
					if card2 then 
						room:setCardFlag(card2, "luasanaess")
						local choice1 = room:askForChoice(sanaeX, "luasanaex", "BasicCard+TrickCard")
						if sanaeX:getState() == "robot" then
							local patterns = { "snatch", "dismantlement", "collateral", "ex_nihilo", "duel", "fire_attack", "amazing_grace","slash", "jink", "analeptic", "peach", "ofuda", "hui",
											"savage_assault", "archery_attack", "god_salvation", "iron_chain", "supply_shortage", "lightning", "indulgence" }
							choice1 = room:askForChoice(sanaeX, "luasanaexxxx", table.concat(patterns, "+"))
							for i = 0, 1000 do
								local card = sgs.Sanguosha:getEngineCard(i)
								if card and card:objectName() == choice1 then
									room:writeToConsole("sanae AItest" .. choice1)
									room:setPlayerMark(sanaeX, "sanaex", i + 1)
									break
								end
							end
							room:askForUseCard(sanaeX, "@@luasanaex", "@luasanaex")
							return false
						end
						if choice1 == "BasicCard" then
							local pattern2 = {}
							local patterns = { "slash", "jink", "analeptic", "peach", "ofuda", "hui" }
							for _, cd in ipairs(patterns) do
								local card = sgs.Sanguosha:cloneCard(cd, sgs.Card_SuitToBeDecided, -1)
								card:addSubcard(card2)
								card:setSkillName("luasanaex")
								if card and not sanaeX:isCardLimited(card, sgs.Card_MethodUse) then
									table.insert(pattern2, cd)
								end
							end
							local choice2 = room:askForChoice(sanaeX, "luasanaex", table.concat(pattern2, "+"))
							for i = 0, 1000 do
								local card = sgs.Sanguosha:getEngineCard(i)
								if card and card:objectName() == choice2 then
									room:setPlayerMark(sanaeX, "sanaex", i + 1)
									break
								end
							end
						else
							local type = {}
							local delay_trick = {}
							local sttrick = {}
							local mttrick = {}
							local patterns = { "snatch", "dismantlement", "collateral", "ex_nihilo", "duel", "fire_attack", "amazing_grace", "savage_assault", "archery_attack", "god_salvation", "iron_chain", "supply_shortage", "lightning", "indulgence" }
							if not (Set(sgs.Sanguosha:getBanPackages()))["pay9"] then
								table.insert(patterns, 2, "banquet")
							end
							for _, cd in ipairs(patterns) do
								local card = sgs.Sanguosha:cloneCard(cd, sgs.Card_SuitToBeDecided, -1)
								card:addSubcard(card2)
								card:setSkillName("luasanaex")
								if card and not sanaeX:isCardLimited(card, sgs.Card_MethodUse) then
									if card:isKindOf("DelayedTrick") then
										table.insert(delay_trick, cd)
									elseif (card:isKindOf("SingleTargetTrick") and not card:isKindOf("DelayedTrick")) then
										table.insert(sttrick, cd)
									elseif not card:isKindOf("DelayedTrick") then
										table.insert(mttrick, cd)
									end
								end
							end
							if #delay_trick ~= 0 then
								table.insert(type, "delay_trick")
							end
							if #sttrick ~= 0 then
								table.insert(type, "single_target_trick")
							end
							if #mttrick ~= 0 then
								table.insert(type, "multiple_target_trick")
							end
							local typechoice = ""
							if #type > 0 then
								typechoice = room:askForChoice(sanaeX, "luasanaex", table.concat(type, "+"))
							end
							local choices = {}
							if typechoice == "delay_trick" then
								choices = delay_trick
							elseif typechoice == "single_target_trick" then
								choices = sttrick
							elseif typechoice == "multiple_target_trick" then
								choices = mttrick
							end

							local pattern_0
							if sanaeX:getState() ~= "robot" then
								pattern_0 = room:askForChoice(sanaeX, "luasanaex", table.concat(choices, "+"))
							else
								pattern_0 = room:askForChoice(sanaeX, "luasanaex", table.concat(patterns, "+"))
							end
							for i = 0, 1000 do
								local card = sgs.Sanguosha:getEngineCard(i)
								if card and card:objectName() == pattern_0 then
									room:setPlayerMark(sanaeX, "sanaex", i + 1)
									break
								end
							end
						end
						room:askForUseCard(sanaeX, "@@luasanaex", "@luasanaex")
					end
					room:setPlayerMark(sanaeX, "sanaex", 0) 
				end 
				for _, cardA in sgs.list(sanaeX:getHandcards()) do 
					room:setCardFlag(cardA, "-luasanaeSS")
				end 
			end
		end
	end
}
luasanaex2 = sgs.CreateTriggerSkill{
	name = "#luasanaex2" ,
	global = true,
	events = {sgs.CardsMoveOneTime } ,
	on_trigger = function(self, event, player, data) 
		local room = player:getRoom()
		local move = data:toMoveOneTime()
		if move.to and move.to:objectName() == player:objectName() and player:hasSkill("luasanaex") and not move.card_ids:isEmpty() then
			if move.to_place == sgs.Player_PlaceHand then		  
				for _, id in sgs.qlist(move.card_ids) do
					local card = sgs.Sanguosha:getCard(id)
					if room:getCardOwner(id):objectName() == player:objectName() then
						room:setCardFlag(card, "luasanaeSS") 
					end
				end				
			end 
		end 
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}
luasanaey = sgs.CreateTriggerSkill{
	name = "luasanaey",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damaged},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.Damaged then
			local damage = data:toDamage()
			if damage.damage == 0 then return false end
			for i = 1, damage.damage, 1 do
				player:gainMark("@luasanaey")
			end
		end
	end
}
luasanaey2 = sgs.CreateTriggerSkill{
	name = "#luasanaey",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.PreHpRecover},
	on_trigger = function(self, event, player, data)
		if event == sgs.PreHpRecover then
			local rec = data:toRecover()
			player:getRoom():writeToConsole(rec.who:getGeneralName())
			if (player:getPhase() == sgs.Player_NotActive) then
				rec.recover = rec.recover - 1
				data:setValue(rec)
			end
		end
	end,
}
luasanaez = sgs.CreateTriggerSkill{
	name = "luasanaez" ,
	global = true,
	events = {sgs.EventPhaseEnd} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:getCurrent():getPhase() ~= sgs.Player_NotActive then
			for _, sanaeD in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				if sanaeD:getMark("@luasanaey") > 0 and room:askForSkillInvoke(sanaeD, "luasanaez") then
					sanaeD:drawCards(1)
					sanaeD:loseMark("@luasanaey")
				end
			end
		end
	end
}
sanaeD:addSkill(luasanaex)
sanaeD:addSkill(luasanaex2)
sanaeD:addSkill(luasanaey)
sanaeD:addSkill(luasanaey2)
sanaeD:addSkill(luasanaez)

local function getKPlayers(room, A)
	local plist = sgs.SPlayerList()
	for _,p in sgs.qlist(room:getAlivePlayers()) do
		if  p:getMark("luatianzhaor") == 0 and p:objectName() ~= A:objectName() then
			plist:append(p)
		end
	end
	return plist
end
luatianzhaoCard = sgs.CreateSkillCard{
	name = "luatianzhao" ,
	filter = function(self, targets, to_select)
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		local plist = sgs.PlayerList()
		for i = 1, #targets, 1 do
			plist:append(targets[i])
		end
		return (slash:targetFilter(plist, to_select, sgs.Self) and not sgs.Self:hasFlag("Global_luatianzhaoSlashFailed")) and slash:isAvailable(sgs.Self)
				or (to_select:objectName() == sgs.Self:objectName() and (not sgs.Self:hasFlag("Global_luatianzhaoAnaFailed")) and (not sgs.Self:hasFlag("Global_luatianzhaoPeachFailed")))
	end ,
	on_validate = function(self, cardUse) --这是0610新加的哦~~~~
		cardUse.m_isOwnerUse = false
		local shinki = cardUse.from
		local targets = cardUse.to
		if targets:isEmpty() then targets:append(shinki) end
		shinki:getRoom():setPlayerMark(shinki, "tianzhaoUse", shinki:getMark("tianzhaoUse") + 1)
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		if targets:at(0):objectName() ~= shinki:objectName() then
			if slash:isAvailable(shinki) then
				room = shinki:getRoom()
				local lieges = getKPlayers(room, shinki)
				for _, target in sgs.qlist(targets) do
					target:setFlags("luatianzhaoTarget")
				end
				for _, liege in sgs.qlist(lieges) do
					slash = room:askForCard(liege, "slash", "@tianzhao-slash:" .. shinki:objectName(), sgs.QVariant(), sgs.Card_MethodResponse, shinki) --未处理胆守
					if slash then
						for _, target in sgs.qlist(targets) do
							target:setFlags("-luatianzhaoTarget")
						end
						liege:drawCards(1)
						return slash
					end
				end
				for _, target in sgs.qlist(targets) do
					target:setFlags("-luatianzhaoTarget")
				end
			end
			room:setPlayerFlag(shinki, "Global_luatianzhaoSlashFailed")
			return nil
		else
			local choices = {}
			if ((not shinki:hasFlag("Global_luatianzhaoAnaFailed")) and sgs.Analeptic_IsAvailable(shinki)) then table.insert(choices, "analeptic") end
			if (not shinki:hasFlag("Global_luatianzhaoPeachFailed")) and shinki:isWounded() then table.insert(choices, "peach") end
			if #choices == 0 then return end
			room = shinki:getRoom()
			local choice = room:askForChoice(shinki, self:objectName(), table.concat(choices, "+"))
			local analeptic
			local lieges = getKPlayers(room, shinki)
			for _, liege in sgs.qlist(lieges) do
				if choice == "analeptic" then
					analeptic = room:askForCard(liege, "Analeptic", "@tianzhao-ana:" .. shinki:objectName(), sgs.QVariant(), sgs.Card_MethodResponse, shinki)
				else
					analeptic = room:askForCard(liege, choice, "@tianzhao-peach:" .. shinki:objectName(), sgs.QVariant(), sgs.Card_MethodResponse, shinki)
				end
				if analeptic then
					liege:drawCards(1)
					return analeptic
				end
			end
			if choice == "analeptic" then
				room:setPlayerFlag(shinki, "Global_luatianzhaoAnaFailed")
			else
				room:setPlayerFlag(shinki, "Global_luatianzhaoPeachFailed")
			end
			return nil
		end
	end,
	on_validate_in_response = function(self, user)
		local room = user:getRoom()
		room:writeToConsole("shinki test X")
		user:getRoom():setPlayerMark(user, "tianzhaoUse", user:getMark("tianzhaoUse") + 1)
		if user:getMark("Global_PreventPeach") > 0 and not user:getRoom():getCurrentDyingPlayer() then

			for _ , liege in sgs.qlist(room:getAlivePlayers()) do
				local tohelp = sgs.QVariant()
				tohelp:setValue(user)
				local prompt = string.format("@tianzhao-ana:%s", user:objectName())
				local card = room:askForCard(liege, "analeptic", prompt, tohelp, sgs.Card_MethodNone)
				if card then
					local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, liege:objectName(), "luatianzhao", "")
					room:moveCardTo(card, nil, sgs.Player_DiscardPile, reason, true)
					liege:drawCards(1)
					local ana = sgs.Sanguosha:cloneCard("analeptic", card:getSuit(), card:getNumber())
					ana:setSkillName("luatianzhao")
					ana:addSubcard(card)
					return ana
				end
			end
		else

			local choices = {}
			local kpp = user:getRoom():getCurrentDyingPlayer()
			if ((not user:hasFlag("Global_luatianzhaoAnaFailed")) and sgs.Analeptic_IsAvailable(user))
				and ((not kpp) or kpp:objectName() == user:objectName()) then table.insert(choices, "analeptic") end
			if (not user:hasFlag("Global_luatianzhaoPeachFailed")) then table.insert(choices, "peach") end
			if user:hasFlag("Global_luatianzhaoAnaFailed") then room:writeToConsole("shinki test X7 " .. #choices) end
			if user:hasFlag("Global_luatianzhaoPeachFailed") then room:writeToConsole("shinki test X8 " .. #choices) end

			if #choices == 0 then return end
			local choice = room:askForChoice(user, self:objectName(), table.concat(choices, "+"))

			local analeptic
			local lieges = getKPlayers(room, user)
			for _, liege in sgs.qlist(lieges) do
				if choice == "analeptic" then
					analeptic = room:askForCard(liege, "Analeptic", "@tianzhao-ana:" .. user:objectName(), sgs.QVariant(), sgs.Card_MethodResponse, user)
				else
					analeptic = room:askForCard(liege, "peach", "@tianzhao-peach:" .. user:objectName(), sgs.QVariant(), sgs.Card_MethodResponse, user)
				end
				if analeptic then
					liege:drawCards(1)
					return analeptic
				end
			end
			room:writeToConsole("shinki test X3 " .. choice .. user:getGeneralName())
			if choice == "analeptic" then
				room:setPlayerFlag(user, "Global_luatianzhaoAnaFailed")
				room:writeToConsole("shinki test X4 " .. choice .. user:getGeneralName())
			else
				room:setPlayerFlag(user, "Global_luatianzhaoPeachFailed")
				room:writeToConsole("shinki test X9 " .. choice .. user:getGeneralName())
			end
			return
		end
		room:setPlayerFlag(user, "Global_luatianzhaoPeachFailed")
		room:setPlayerFlag(user, "Global_luatianzhaoAnaFailed")
		return nil
	end
}
luatianzhaoVS = sgs.CreateViewAsSkill{
	name = "luatianzhao" ,
	n = 0 ,
	view_as = function()
		return luatianzhaoCard:clone()
	end ,
	enabled_at_play = function(self, player)
		return player:hasSkill("luatianzhao") and player:getMark("tianzhaoUse") < 4
				and (((not player:hasFlag("Global_luatianzhaoSlashFailed")) and sgs.Slash_IsAvailable(player))
					or ((not player:hasFlag("Global_luatianzhaoAnaFailed")) and sgs.Analeptic_IsAvailable(player))
					or ((not player:hasFlag("Global_luatianzhaoPeachFailed"))))
	end ,
	enabled_at_response = function(self, player, pattern)
		return player:hasSkill("luatianzhao") and player:usedTimes("#luatianzhao") < 4 and player:getMark("tianzhaoUse") < 4
				and ((((pattern == "slash") or (pattern == "@tianzhao")) and (not player:hasFlag("Global_luatianzhaoSlashFailed")))
						or ((pattern == "peach+analeptic") or (pattern == "@tianzhao") or (pattern == "analeptic") or (pattern == "peach")) and ((not player:hasFlag("Global_luatianzhaoAnaFailed")) or (not player:hasFlag("Global_luatianzhaoPeachFailed"))))
				and (sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE)
	end
}
luatianzhao = sgs.CreateTriggerSkill{
	name = "luatianzhao" ,
	events = {sgs.CardAsked} ,
	view_as_skill = luatianzhaoVS ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local pattern = data:toStringList()[1]
		local prompt = data:toStringList()[2]
		if (pattern == "slash") and not string.find(prompt, "@tianzhao-slash")
			and not string.find(prompt, "@jijiang-slash") then

			local lieges = getKPlayers(room, player)
			if lieges:isEmpty() then return false end
			if not room:askForSkillInvoke(player, "luatianzhaoA", data) then return false end
			for _, liege in sgs.qlist(lieges) do
				local slash = room:askForCard(liege, "slash", "@tianzhao-slash:" .. player:objectName(), sgs.QVariant(), sgs.Card_MethodResponse, player)
				if slash then
					liege:drawCards(1)
					room:provide(slash)
					return true
				end
			end
		elseif (pattern == "jink") and not string.find(prompt, "@tianzhao-jink") and not string.find(prompt, "@hujia-jink") then
			local lieges = getKPlayers(room, player)
			if lieges:isEmpty() then return false end
			if not room:askForSkillInvoke(player, "luatianzhaoB", data) then return false end
			for _, liege in sgs.qlist(lieges) do
				local jink = room:askForCard(liege, "jink", "@tianzhao-jink:" .. player:objectName(), sgs.QVariant(), sgs.Card_MethodResponse, player)
				if jink then
					liege:drawCards(1)
					room:provide(jink)
					return true
				end
			end
		elseif (pattern == "peach" or pattern == "analeptic" or pattern == "peach+analeptic" or pattern == "Analeptic") and not string.find(prompt, "@tianzhao-ana")
				and not string.find(prompt, "@tianzhao-peach") then
			local choice
			if pattern == "peach+analeptic" then
				choice = room:askForChoice(player, self:objectName(), pattern)
				pattern = choice
			end
			local lieges = getKPlayers(room, player)
			if lieges:isEmpty() then return false end
			if pattern == "analeptic" or pattern == "Analeptic" then
				if not room:askForSkillInvoke(player, "luatianzhaoC", data) then return false end
			else
				if not room:askForSkillInvoke(player, "luatianzhaoD", data) then return false end
			end
			for _, liege in sgs.qlist(lieges) do
				local jink
				if pattern == "analeptic" or pattern == "Analeptic" then
					jink = room:askForCard(liege, "Analeptic", "@tianzhao-ana:" .. player:objectName(), sgs.QVariant(), sgs.Card_MethodResponse, player)
				else
					jink = room:askForCard(liege, "peach", "@tianzhao-peach:" .. player:objectName(), sgs.QVariant(), sgs.Card_MethodResponse, player)
				end
				if jink then
					liege:drawCards(1)
					room:provide(jink)
					return true
				end
			end
		end
		return false
	end ,
	can_trigger = function(self, target)
		return target and target:hasSkill("luatianzhao") and target:getMark("tianzhaoUse") < 4
	end
}

luatianzhao2 = sgs.CreateTriggerSkill{
	name = "#luatianzhao" ,
	global = true,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Start then 
			for _, sanaeD in sgs.qlist(room:findPlayersBySkillName("luatianzhao")) do 
				if sanaeD then
					room:setPlayerMark(sanaeD, "tianzhaoUse", 0)
				end
			end 
		end
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}

shenpancard = sgs.CreateSkillCard{
	name = "shenpan",
	target_fixed = true,
	on_use = function(self, room, source, targetS)
		room:setPlayerFlag(source, "shenpaning")
		local slash = room:askForCard(source, "slash", "shenpanW", sgs.QVariant(), sgs.Card_MethodResponse, player)
		local jink = room:askForCard(source, "jink", "shenpanX", sgs.QVariant(), sgs.Card_MethodResponse, player)
		local analeptic = room:askForCard(source, "Analeptic", "shenpanY", sgs.QVariant(), sgs.Card_MethodResponse, player)
		if slash and jink and analeptic then room:setPlayerFlag(source, "shenpaningtao") end
		local peach = room:askForCard(source, "peach", "shenpanZ", sgs.QVariant(), sgs.Card_MethodResponse, player)
		room:setPlayerFlag(source, "-shenpaning")
		room:setPlayerFlag(source, "-shenpaningtao")
		if slash and jink and analeptic and peach then
			local targets = room:getAlivePlayers()
			local target = room:askForPlayerChosen(source, targets, "shenpan", "shenpan", true)
			targets:removeOne(target)
			while target and not targets:isEmpty() do
				room:damage(sgs.DamageStruct(self:objectName(), source, target))
				targets:removeOne(target)
				if targets:isEmpty() then break end
				target = room:askForPlayerChosen(source, targets, "shenpan", "shenpan", true)
			end
		else
			local shen = sgs.IntList()
			if slash then
				if slash:getSubcards() then
					for _, id in sgs.qlist(slash:getSubcards()) do
						if room:getCardPlace(id) == sgs.Player_DiscardPile then shen:append(id) end
					end
				else
					if slash:getId() >= 0 and room:getCardPlace(slash:getId()) == sgs.Player_DiscardPile then shen:append(id) end
				end
			end
			if jink then
				if jink:getSubcards() then
					for _, id in sgs.qlist(jink:getSubcards()) do
						if room:getCardPlace(id) == sgs.Player_DiscardPile then shen:append(id) end
					end
				else
					if jink:getId() >= 0 and room:getCardPlace(jink:getId()) == sgs.Player_DiscardPile then shen:append(id) end
				end
			end
			if analeptic then
				if analeptic:getSubcards() then
					for _, id in sgs.qlist(analeptic:getSubcards()) do
						if room:getCardPlace(id) == sgs.Player_DiscardPile then shen:append(id) end
					end
				else
					if analeptic:getId() >= 0 and room:getCardPlace(analeptic:getId()) == sgs.Player_DiscardPile then shen:append(id) end
				end
			end
			if peach then
				if peach:getSubcards() then
					for _, id in sgs.qlist(peach:getSubcards()) do
						if room:getCardPlace(id) == sgs.Player_DiscardPile then shen:append(id) end
					end
				else
					if peach:getId() >= 0 and room:getCardPlace(peach:getId()) == sgs.Player_DiscardPile then shen:append(id) end
				end
			end
			if shen:isEmpty() then return end
			local move = sgs.CardsMoveStruct()
			move.from = nil
			move.from_place = sgs.Player_DiscardPile
			move.to = source
			move.to_place = sgs.Player_PlaceHand
			move.card_ids = shen
			move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_RECYCLE, source:objectName())
			room:moveCardsAtomic(move, true)
		end
	end 
}
shenpan = sgs.CreateZeroCardViewAsSkill{
	name = "shenpan",
	view_as = function()
		return shenpancard:clone()
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#shenpan")
	end
}
luatianzhao3 = sgs.CreateTriggerSkill{
	name = "#luatianzhao4",
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if not p:hasSkill("luatianzhaor") then room:attachSkillToPlayer(p, "luatianzhaor") end
				end
			end
		end
	end
}

luashengdian = sgs.CreateDistanceSkill{
	name = "luashengdian$",
	correct_func = function(self, from, to)
		if to:hasLordSkill("luashengdian") and to:isLord() then
			local aaa = 1
			for _, p in sgs.qlist(to:getAliveSiblings()) do
				aaa = aaa + 1
			end 
			return math.floor(aaa / 3)
		end
	end,
}
shinki:addSkill(luatianzhao2)
shinki:addSkill(luatianzhao3)
shinki:addSkill(luatianzhao)
shinki:addSkill(shenpan)
shinki:addSkill(luashengdian)

shinkiA:addSkill(luatianzhao2)
shinkiA:addSkill(luatianzhao)
shinkiA:addSkill(luatianzhao3)
shinkiA:addSkill(shenpan)
shinkiA:addSkill(luashengdian)


qiji9 = sgs.CreateTriggerSkill{
	name = "#qiji9", --必须 
	frequency = sgs.Skill_Frequent,
	events = {sgs.EventPhaseStart}, --技能触发时机,必须
	on_trigger = function(self, event, player, data) --必须
		local room = player:getRoom()
			if player:getPhase() == sgs.Player_Start and room:askForSkillInvoke(player, "luaqiji", data) then
				local choices = {"heart","diamond","club","spade"}
				local choice = room:askForChoice(player, "luaqiji2", table.concat(choices, "+"), data)
				room:setTag("luaqiji2", sgs.QVariant(choice))
				room:writeToConsole("sanae choose" .. choice)
				local judge = sgs.JudgeStruct()
				judge.pattern = ".|" .. choice
				-- sgs.Sanguosha:getCard(id):getNumber()
				judge.good = true
				judge.reason = "luaqiji"
				judge.who = player
				judge.time_consuming = true
				room:judge(judge)
				if judge:isGood() then					
					room:setPlayerFlag(player, "qiji9_success") --使用标志的一个好处是，回合结束后，标志会自动被清除掉，所以不用担心设置完标志后以后都不能使用这个技能了。
				end 
			end 
	end,
}


qijiCard = sgs.CreateSkillCard{
	name = "luaqiji",
	will_throw = false,
	--handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		local card = sgs.Self:getTag("luaqiji")
		local response = false
		if card then 
			card = card:toCard()
		else		
			response = true			
			card = sgs.Sanguosha:cloneCard(self:getUserString())
			card = sgs.self
		end 
		if not card then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		if self:getUserString() and self:getUserString() ~= "" then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		card:addSubcards(self:getSubcards())
		card:setSkillName(self:objectName())
		if card and card:targetFixed() and not response then
			return false
		end
		if response then return true end 
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end

		return card and card:targetFilter(qtargets, to_select, sgs.Self) and not sgs.Self:isProhibited(to_select, card, qtargets)
	end,
	feasible = function(self, targets)
		local card = sgs.Self:getTag("luaqiji")
		local response = false
		if card then 
			card = card:toCard()
		else
			response = true
			card = sgs.Sanguosha:cloneCard(self:getUserString())
		end 
		if not card then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		if self:getUserString() and self:getUserString() ~= "" then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		card:addSubcards(self:getSubcards())
		card:setSkillName(self:objectName())
		--if card:isKindOf("BasicCard") then card = kcard:clone() end 
		if response then return true end 
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		return card and card:targetsFeasible(qtargets, sgs.Self)
	end,	
	on_validate = function(self, card_use)
		local use_card = sgs.Sanguosha:cloneCard(self:getUserString())
		use_card:addSubcards(self:getSubcards())
		use_card:setSkillName(self:objectName())
		local available = true
		for _,p in sgs.qlist(card_use.to) do
			if card_use.from:isProhibited(p,use_card)then
				available = false
				break
			end
		end
		available = available and use_card:isAvailable(card_use.from)
		if not available then return nil end
		card_use.from:getRoom():addPlayerHistory(card_use.from, "luaqiji")
		return use_card		
	end,
	on_validate_in_response = function(self, user)
		local room = user:getRoom()
		local aocaistring = self:getUserString()
		local use_card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, -1)
		if string.find(aocaistring, "+")  then
			local uses = {}
			for _, name in pairs(aocaistring:split("+"))do
				table.insert(uses, name)
			end
			local name = room:askForChoice(user, "luaqiji", table.concat(uses, "+"))
			use_card = sgs.Sanguosha:cloneCard(name, sgs.Card_NoSuit, -1)
		end
		use_card:addSubcards(self:getSubcards())
		use_card:setSkillName("luaqiji")
		room:addPlayerHistory(user, "luaqiji")
		return use_card	
	end
}
luaqiji = sgs.CreateViewAsSkill{
	name = "luaqiji",
	n = 1,
	view_filter = function(self, selected, to_select)
		return not to_select:isEquipped() 
	end,
	view_as = function(self, cards)
		if #cards < 1 then return nil end 
		if (sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY) then			
			local c = sgs.Self:getTag("luaqiji"):toCard()
			if c then
				local card = qijiCard:clone()
				for _, acard in ipairs(cards) do
					card:addSubcard(acard)
				end			
				card:setUserString(c:objectName())	
				return card			
			end
		else
			local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
			if pattern == "slash" then 
				pattern = "slash+thunder_slash+fire_slash"
			end
			local acard = qijiCard:clone()
			if #cards ~= 1 then return end
			for _, bcard in ipairs(cards) do
				acard:addSubcard(bcard)
			end
			if pattern == "peach+analeptic"  then
				if sgs.Self:hasFlag("Global_PreventPeach") then
					pattern = "analeptic"
				else
					pattern = "peach"
				end
			end
			acard:setUserString(pattern)
			return acard		
		end 
		return nil
	end,
	enabled_at_play = function(self, player)
		return not player:isKongcheng() and player:hasFlag("qiji9_success") and (player:usedTimes("#luaqiji") < player:getLostHp())
	end,
	enabled_at_response = function(self, player, pattern)
		if player:isKongcheng() or string.sub(pattern, 1, 1) == "." or string.sub(pattern, 1, 1) == "@" then
			return false
		end
		return player:hasFlag("qiji9_success") and (player:usedTimes("#luaqiji") < player:getLostHp())
	end,	
	enabled_at_nullification = function(self, player)		
		return not player:isKongcheng() and player:hasFlag("qiji9_success") and (player:usedTimes("#luaqiji") < player:getLostHp())
	end
}
luaqiji:setGuhuoDialog("lrd")

mishu9 = sgs.CreateTriggerSkill{
	name = "mishu9" ,
	events = {sgs.AskForRetrial},
	on_trigger = function(self, event, player, data)
		if event == sgs.AskForRetrial then 
			local room = player:getRoom()
			local judge = data:toJudge()
			if judge.who:objectName() ~= player:objectName() then return false end
			--local card = room:askForCard(player,  ".|.|.|hand", "@mishu9", data, sgs.Card_MethodDiscard, player, true)
			if room:askForSkillInvoke(player, "mishu9", data) then			
				local players = sgs.SPlayerList()
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if not p:isKongcheng() then
						players:append(p)
					end
				end			
				--room:throwCard(card:getEffectiveId(), player, player)
				local toplayer = room:askForPlayerChosen(player, players, self:objectName()) --选一名自己以外的角色
				
				local id2 = room:askForCardChosen(player, toplayer, "h", self:objectName())
				local carde = sgs.Sanguosha:getCard(id2)  --根据id获得卡牌
				room:retrial(carde, player, judge, self:objectName())				
			end
		end		
	end
}
shenfeng9 = sgs.CreateMaxCardsSkill{
	name = "shenfeng9", --必须
	extra_func = function(self, target) --必须
		if target:hasSkill("shenfeng9") then 
			local x = 3 - target:getHp()
			if x < 0 then x = 0 end 
			return x
		end 
	end
}

sanae:addSkill(luaqiji)
sanae:addSkill(qiji9)
sanae:addSkill(mishu9)
sanae:addSkill(shenfeng9)

sanaeA:addSkill(luaqiji)
sanaeA:addSkill(qiji9)
sanaeA:addSkill(mishu9)
sanaeA:addSkill(shenfeng9)

sanaeB:addSkill(luaqiji)
sanaeB:addSkill(qiji9)
sanaeB:addSkill(mishu9)
sanaeB:addSkill(shenfeng9)

sanaeC:addSkill(luaqiji)
sanaeC:addSkill(qiji9)
sanaeC:addSkill(mishu9)
sanaeC:addSkill(shenfeng9)
--sanae:addSkill(mishu9Judge)

luahuapuCard = sgs.CreateSkillCard{
	name = "luahuapu",
	will_throw = false,
	target_fixed = true,
	handling_method = sgs.Card_MethodNone,
	on_use = function(self, room, source, targets)
		room:fillAG(self:getSubcards())
		room:getThread():delay(1500)
		room:clearAG()
		for _, id in sgs.qlist(self:getSubcards()) do
			local card = sgs.Sanguosha:getCard(id)
			room:setCardFlag(card, "visible")
		end
		if self:getSubcards():length() == 1 then
			source:drawCards(1)
		elseif self:getSubcards():length() == 2 then
			if source:isWounded() then
				room:recover(source, sgs.RecoverStruct(source))
			end
		elseif self:getSubcards():length() == 3 then
			local p = room:askForPlayerChosen(source, room:getAlivePlayers(), "luahuapu", "luahuapua", false, true)
			if p then
				source:getRoom():damage(sgs.DamageStruct("luahuapu", source, p))
			end
		elseif self:getSubcards():length() >= 4 then
			source:drawCards(1)
			if source:isWounded() then
				room:recover(source, sgs.RecoverStruct(source))
			end
			local p = room:askForPlayerChosen(source, room:getAlivePlayers(), "luahuapu", "luahuapua", false, true)
			if p then
				source:getRoom():damage(sgs.DamageStruct("luahuapu", source, p))
			end
			if room:askForDiscard(source, self:objectName(), 2, 2, true, true, "luahuapub") then  --sgs.EventPhaseChanging
				local playerdata = sgs.QVariant() -- ai用
				playerdata:setValue(source)
				room:setTag("luajunying", playerdata)
				room:removeTag("luajunying")
				local thread = room:getThread()
				room:setPlayerFlag(source, "luajunying")
				
				local old_phase = source:getPhase() 
				local thread = room:getThread()
				local change = sgs.PhaseChangeStruct()
				change.from = sgs.Player_Play
				change.to = sgs.Player_Play
				local _data = sgs.QVariant()
				_data:setValue(change)
				room:broadcastProperty(source, "phase")
				if not thread:trigger(sgs.EventPhaseChanging, room, source, _data) then
					if not thread:trigger(sgs.EventPhaseStart, room, source) then
						thread:trigger(sgs.EventPhaseProceeding, room, source)
					end
					source:setPhase(sgs.Player_Play)
					thread:trigger(sgs.EventPhaseEnd, room, source)
				end
				 
				source:setPhase(old_phase)
				room:broadcastProperty(source, "phase")
			end
		end
	end
}

luahuapuVS = sgs.CreateViewAsSkill{
	name = "luahuapu",
	n = 4,
	view_filter = function(self, selected, to_select)
		return to_select:getSuit() == sgs.Card_Club
	end,
	view_as = function(self, cards)
		if #cards < 1 then return end
		local card = luahuapuCard:clone()
		for _, cd in ipairs(cards) do
			card:addSubcard(cd)
		end
		return card
	end ,
	enabled_at_play = function()
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luahuapu"
	end,

}
luahuapu = sgs.CreateTriggerSkill{
	name = "luahuapu" ,
	view_as_skill = luahuapuVS,
	events = {sgs.EventPhaseEnd} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Play then
			if room:askForUseCard(player, "@@luahuapu", "@luahuapu") then

			end
		end
	end
}
yuka:addSkill(luahuapu)
yukaA:addSkill(luahuapu)

luachenmian2 = sgs.CreateTriggerSkill{
	name = "#luachenmian2", 
	global = true,
	priority = 1,
	events = {sgs.TurnStart},
	on_trigger = function(self, event, player, data, room) 
		if event == sgs.TurnStart then 
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:getMark("luachenmianA") == 0 then 
					room:setPlayerMark(p, "luachenmianA", 1)
				end 
				if p:getMark("luachangmeiA") == 0 then 
					room:setPlayerMark(p, "luachangmeiA", 1)
				end 
			end 
		end
	end 
} 
luachenmian = sgs.CreateTriggerSkill{
	name = "luachenmian", 
	global = true,
	priority = 8,
	events = {sgs.TurnStart},
	on_trigger = function(self, event, player, data, room)   
		for _, Yuuka in sgs.qlist(room:findPlayersBySkillName("luachenmian")) do
			if event == sgs.TurnStart then 
				local invoke = true
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if p:getMark("luachenmianA") > 0 then invoke = false end 
				end   
				if Yuuka and Yuuka:isAlive() and invoke then 
					-- for _,card in sgs.qlist(Yuuka:getHandcards()) do
						-- local range_list = sgs.IntList()
						-- range_list:append(card:getId()) 
						-- room:askForGuanxing(Yuuka, range_list, sgs.Room_GuanxingDownOnly)
					-- end
					Yuuka:throwAllHandCards()
					Yuuka:turnOver()
					local discard_ids = room:getDrawPile()
					local i = 0
					for _, id in sgs.qlist(discard_ids) do
						local card = sgs.Sanguosha:getCard(id)
						if card:getSuit() == sgs.Card_Club then
							local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
							dummy:addSubcard(card)
							Yuuka:obtainCard(dummy)
							i = 1 + i
						end
						if i > 0 then break end
					end
					room:acquireSkill(Yuuka, "lualuoying")
				end 
			end 
		end 
	end
}

luayoumengcard = sgs.CreateSkillCard{
    name = "luayoumeng",
    will_throw = false,
	handling_method = sgs.Card_MethodNone,
    target_fixed = true,
    on_use = function(self, room, source, targets)
		room:setPlayerMark(source, "luayoumeng", 1)
        local card_ids2 = {}
        room:fillAG(self:getSubcards())
        room:getThread():delay(1500)		
		room:clearAG()
		room:loseMaxHp(source, math.floor(source:getMaxHp() / 2))
		room:acquireSkill(source, "luahuapu")
	end
} 
luayoumengVS = sgs.CreateViewAsSkill{
	name = "luayoumeng", 
	n = 4,  
	view_filter = function(self, selected, to_select)
		return #selected <= 4 and to_select:getSuit() == sgs.Card_Club 
	end, 
	view_as = function(self, cards)
		local y = 4
		if #cards == y then 
			local card = luayoumengcard:clone()
			for _, cd in ipairs(cards) do
				card:addSubcard(cd)
			end	
			return card		
		end 
	end, 
	enabled_at_play = function(self, player)
		return false
	end, 
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luayoumeng"
	end
}
luayoumeng = sgs.CreateTriggerSkill{
	name = "luayoumeng" ,
	frequency = sgs.Skill_Wake ,
	view_as_skill = luayoumengVS,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, Yuuka, data)
		local room = Yuuka:getRoom()
		if Yuuka:getPhase() == sgs.Player_Start then
			if room:askForUseCard(Yuuka, "@@luayoumeng", "@luayoumeng") then
			
			end 
		end 
	end,
	can_trigger = function(self, target)
		return (target and target:isAlive() and target:hasSkill(self:objectName()))
				and (target:getMark("luayoumeng") == 0)
	end
}
luachangmei = sgs.CreateTriggerSkill{
	name = "luachangmei$" ,
	global = true,
	events = {sgs.TurnStart},
	on_trigger = function(self, event, player, data, room) 
		for _, Yuuka in sgs.qlist(room:findPlayersBySkillName("luachenmian")) do
			if event == sgs.TurnStart then 
				local invoke = true
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if p:getMark("luachenmianA") > 0 then invoke = false end 
				end  
				if Yuuka and Yuuka:isAlive() and invoke and player:objectName() == Yuuka:objectName() and Yuuka:hasLordSkill("luachenmian") then
					room:setPlayerProperty(Yuuka, "maxhp", sgs.QVariant(Yuuka:getMaxHp() + 1))
					room:recover(Yuuka, sgs.RecoverStruct(Yuuka))
				end
			end  
		end 
	end
}
	
yukaB:addSkill(luachenmian)
yukaB:addSkill(luachenmian2)
yukaB:addSkill(luayoumeng)
yukaB:addSkill(luachangmei)

yuelongCard = sgs.CreateSkillCard{
	name = "Luayuelong",
	will_throw = false,
	filter = function(self, targets, to_select)
		local card = self:getSubcards():at(0)
		card = sgs.Sanguosha:getCard(card)
		if not card then return end 
		card:setSkillName(self:objectName())
		if card and card:targetFixed() then
			return false
		end
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end

		return card and card:targetFilter(qtargets, to_select, sgs.Self) and not sgs.Self:isProhibited(to_select, card, qtargets)
	end,
	feasible = function(self, targets)
		local card = self:getSubcards():at(0)
		card = sgs.Sanguosha:getCard(card)
		if not card then return end 
		card:setSkillName(self:objectName())
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		return card and card:targetsFeasible(qtargets, sgs.Self)
	end,	
	on_validate = function(self, card_use)
		local use_card = self:getSubcards():at(0)
		use_card = sgs.Sanguosha:getCard(use_card)
		use_card:setSkillName(self:objectName())
		local available = true
		for _,p in sgs.qlist(card_use.to) do
			if card_use.from:isProhibited(p,use_card)then
				available = false
				break
			end
		end
		available = available and use_card:isAvailable(card_use.from)
		if not available then return nil end
		return use_card		
	end
}
LuayuelongVS = sgs.CreateOneCardViewAsSkill{
	name = "Luayuelong",
	filter_pattern = "TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand",
	view_as = function(self,card)
		local skillcard = yuelongCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self,player)
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@Luayuelong"
	end	
}

Luayuelong = sgs.CreateTriggerSkill{
	name = "Luayuelong",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.TargetConfirmed, sgs.Damaged, sgs.Damage},
	view_as_skill = LuayuelongVS,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			if (not use.from) or (use.from:objectName() ~= player:objectName()) then return end 
			if not use.from:hasSkill("Luayuelong") or not use.card then return false end
			if use.card:getSkillName() == "Luayuelong" then
				player:drawCards(1)
				if use.to:length() ~= 1 then return false end
				if use.card:isBlack() and room:askForSkillInvoke(player, self:objectName(), data) then 
					local num = use.card:getNumber()
					
					local target = use.to:at(0)
					local airoom = target:getRoom()
					airoom:setTag("Luayuelong", sgs.QVariant(num))  --ai使用
					local handcards = target:getHandcards()
					local allcards = {}
					for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
						table.insert(allcards, c)
					end
					local equips = target:getEquips()
					for _,c in sgs.qlist(equips) do
						table.insert(allcards, c)
					end	
					local i = 0
					for _,card in ipairs(allcards) do
						if not target:isJilei(card) then 
							i = i + card:getNumber()
						end 
					end
						local choice = room:askForChoice(target, "Luayuelong","Luayuelong1+Luayuelong2")	
						if i > num and choice == "Luayuelong1" then 
							--room:broadcastSkillInvoke("Luayuelong",1) 
							local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
							local card_ids = sgs.IntList()
							local original_places = sgs.IntList()
							local sum = 0 
							while sum <= num do
								if not target:canDiscard(target, "he") then break end
								local id = room:askForCardChosen(target, target, "he", self:objectName(), false, sgs.Card_MethodDiscard) 
								rc = sgs.Sanguosha:getCard(id)
								sum = sum + rc:getNumber()
								room:throwCard(rc, target, target)
							end
						else
							--room:broadcastSkillInvoke("Luayuelong",2) 
							target:gainMark("@yueni", 1)
						end
					airoom:removeTag("Luayuelong")
				end

			end 
		else
			local damage = data:toDamage()
			if damage.damage == 0 then return false end
			if ((damage.card and (not damage.card:isKindOf("AOE"))) or not damage.card) and damage.by_user and not damage.chain and not damage.transfer
				and not player:hasFlag("luayuelong") then
				local x = damage.damage
				for i = 0, x - 1, 1 do
					if not player:isAlive() then return end
					room:setPlayerFlag(player, "luayuelong")
					room:askForUseCard(player, "@@Luayuelong", "@Luayuelong")
					room:setPlayerFlag(player, "-luayuelong")
				end
			end 
		end 
		return false
	end
}

Luayuelong1 = sgs.CreateMaxCardsSkill{
	name = "#Luayuelong1", --必须
	extra_func = function(self, target) --必须
		if target:getMark("@yueni") > 0 then 
			local x = 0 - target:getMark("@yueni")
			return x
		end 
	end
}

reisen:addSkill(Luayuelong)
reisen:addSkill(Luayuelong1)

reisenA:addSkill(Luayuelong)
reisenA:addSkill(Luayuelong1)

reisenC:addSkill(Luayuelong)
reisenC:addSkill(Luayuelong1)

reisenD:addSkill(Luayuelong)
reisenD:addSkill(Luayuelong1)

yueniCard = sgs.CreateSkillCard{
	name = "luayueni",
	will_throw = false,
	filter = function(self, targets, to_select)
		local card = self:getSubcards():at(0)
		card = sgs.Sanguosha:getCard(card)
		if not card then return end
		card:setSkillName(self:objectName())
		if card and card:targetFixed() then
			return false
		end
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end

		return card and card:targetFilter(qtargets, to_select, sgs.Self) and not sgs.Self:isProhibited(to_select, card, qtargets)
	end,
	feasible = function(self, targets)
		local card = self:getSubcards():at(0)
		card = sgs.Sanguosha:getCard(card)
		if not card then return end
		card:setSkillName(self:objectName())
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		return card and card:targetsFeasible(qtargets, sgs.Self)
	end,
	on_validate = function(self, card_use)
		local use_card = self:getSubcards():at(0)
		use_card = sgs.Sanguosha:getCard(use_card)
		use_card:setSkillName(self:objectName())
		local available = true
		for _,p in sgs.qlist(card_use.to) do
			if card_use.from:isProhibited(p,use_card)then
				available = false
				break
			end
		end
		available = available and use_card:isAvailable(card_use.from)
		if not available then return nil end
		return use_card
	end
}
luayueniVS = sgs.CreateOneCardViewAsSkill{
	name = "luayueni",
	filter_pattern = ".|.|.|hand",
	view_as = function(self,card)
		local skillcard = yueniCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self,player)
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luayueni"
	end
}

luayueni = sgs.CreateTriggerSkill{
	name = "luayueni",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.TargetConfirmed, sgs.Damage},
	view_as_skill = luayueniVS,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			if (not use.from) or (use.from:objectName() ~= player:objectName()) then return end
			if not use.from:hasSkill("luayueni") or not use.card then return false end
			if use.card:getSkillName() == "luayueni" then
				player:drawCards(1)
				if use.to:length() ~= 1 then return false end
				if  room:askForSkillInvoke(player, self:objectName(), data) then
					local id = room:askForCardChosen(player, use.to:at(0), "he", self:objectName(), false, sgs.Card_MethodDiscard)
					room:throwCard(id, use.to:at(0), player)
				end
			end
		else
			local damage = data:toDamage()
			if damage.damage == 0 then return false end
			if (not damage.card or (damage.card and (not damage.card:isKindOf("AOE")))) and damage.by_user and not damage.chain and not damage.transfer
					and not player:hasFlag("luayueni") then
				local x = damage.damage
				for i = 0, x - 1, 1 do
					if not player:isAlive() then return end
					room:setPlayerFlag(player, "luayueni")
					room:askForUseCard(player, "@@luayueni", "@luayueni")
					room:setPlayerFlag(player, "-luayueni")
				end
			end
		end
		return false
	end
}

luaguoshiCard = sgs.CreateSkillCard{
	name = "luaguoshi",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		if source:getMark("@luaguoshia") == 2 then
			source:gainMark("@luaguoshib")
			source:loseAllMarks("@luaguoshia")
		elseif source:getMark("@luaguoshib") == 1 then
			source:loseAllMarks("@luaguoshib")
			room:loseMaxHp(source, 1)
			room:askForDiscard(source, self:objectName(), 1, 1, false, true)
			return false
		else
			source:gainMark("@luaguoshia")
		end
		room:recover(source, sgs.RecoverStruct(source))
		local tp = room:askForPlayerChosen(source, room:getOtherPlayers(source), "luaguoshi", "@luaguoshi", true, true)
		if tp then
			room:damage(sgs.DamageStruct(self:objectName(), source, tp))
		end
	end
}
luaguoshi = sgs.CreateZeroCardViewAsSkill{
	name = "luaguoshi",
	view_as = function(self, cards)
		return luaguoshiCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:isWounded()
	end,
}

reisenB:addSkill(luayueni)
reisenB:addSkill(luaguoshi)

Luashanhai = sgs.CreateTriggerSkill{
	name = "Luashanhai",
	frequency = sgs.Skill_Frequent,
	events = {sgs.Damaged},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		local x = damage.damage
		for i = 0, x - 1, 1 do
			if not player:isAlive() then return end
			if room:askForSkillInvoke(player, self:objectName()) then		
				local to_givelist = sgs.SPlayerList()
				for _,p in sgs.qlist(room:getAlivePlayers()) do
					if not p:hasSkill("feiying") or not p:hasSkill("mashu") then
						to_givelist:append(p)
					end
				end
				if not to_givelist:isEmpty() then
					local target = room:askForPlayerChosen(player, to_givelist, self:objectName(), "Luashanhaia", true, true)
					if not target then return false end
					if target:hasSkill("feiying") then 
						room:acquireSkill(target, "mashu", true) 
					elseif target:hasSkill("mashu") then 
						room:acquireSkill(target, "feiying", true) 
					else
						local choice = room:askForChoice(player, "Luashanhai","feiying+mashu")
						if choice == "feiying" then room:acquireSkill(target, "feiying", true) end 
						if choice == "mashu" then room:acquireSkill(target, "mashu", true); room:addPlayerMark(player, "mashuX") end
					end 					
				end				
			end 
		end 
	end 
}
LuaweiwoCard1 = sgs.CreateSkillCard{
	name = "Luaweiwo" ,
	filter = function(self, targets, to_select)
		local snatch = sgs.Sanguosha:cloneCard("snatch", sgs.Card_NoSuit, 0)
		snatch:deleteLater()
		if to_select:isProhibited(to_select, snatch) then
			return false
		end
		return #targets < 1 and not to_select:inMyAttackRange(sgs.Self) and to_select:objectName() ~= sgs.Self:objectName()
	end ,
	feasible = function(self, targets)
		return #targets == 1
	end ,
	about_to_use = function(self, room, card_use)
		local use = card_use
		local data = sgs.QVariant()
		data:setValue(card_use)
		local thread = room:getThread()
		thread:trigger(sgs.PreCardUsed, room, card_use.from, data)
		use = data:toCardUse()
		local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_THROW, card_use.from:objectName(), "", "Luaweiwo", "")
		room:moveCardTo(self, card_use.from, nil, sgs.Player_DiscardPile, reason, true)
		thread:trigger(sgs.CardUsed, room, card_use.from, data)
		thread:trigger(sgs.CardFinished, room, card_use.from, data)
	end ,
	on_use = function(self, room, source, targets)
		local to = targets[1]
		local from = source
		local snatch = sgs.Sanguosha:cloneCard("snatch", sgs.Card_NoSuit, 0)
		room:writeToConsole("丰姬测试P1")
		snatch:toTrick():setCancelable(true)-- 这里true改为false 就是旧版技能
		snatch:setSkillName(self:objectName())
		if not from:isCardLimited(snatch, sgs.Card_MethodUse) and not from:isProhibited(to, snatch) then
			room:useCard(sgs.CardUseStruct(snatch, from, to))
		else
			snatch:deleteLater()
		end
	end
}
LuaweiwoCard2 = sgs.CreateSkillCard{
	name = "Luaweiwo2" ,
	filter = function(self, targets, to_select)
		return #targets < 1 and sgs.Self:inMyAttackRange(to_select) and not to_select:isKongcheng()
			and sgs.Self:getHandcardNum() > to_select:getHandcardNum()
	end ,
	feasible = function(self, targets)
		return #targets == 1
	end ,
	about_to_use = function(self, room, card_use)
		local use = card_use
		local data = sgs.QVariant()
		data:setValue(card_use)
		local thread = room:getThread()
		thread:trigger(sgs.PreCardUsed, room, card_use.from, data)
		use = data:toCardUse()
		local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_THROW, card_use.from:objectName(), "", "Luaweiwo", "")
		room:moveCardTo(self, card_use.from, nil, sgs.Player_DiscardPile, reason, true)
		thread:trigger(sgs.CardUsed, room, card_use.from, data)
		thread:trigger(sgs.CardFinished, room, card_use.from, data)
	end ,
	on_use = function(self, room, source, targets)
		local to = targets[1]
		local from = source
		room:writeToConsole("丰姬测试P2")
		local quanxiang = sgs.Sanguosha:cloneCard("quanxiang", sgs.Card_NoSuit, 0)
		quanxiang:toTrick():setCancelable(true)-- 这里true改为false 就是旧版技能
		quanxiang:setSkillName(self:objectName())
		if not from:isCardLimited(quanxiang, sgs.Card_MethodUse) and not from:isProhibited(to, quanxiang) then
			room:useCard(sgs.CardUseStruct(quanxiang, from, to))
		else
			quanxiang:deleteLater()
		end				
	end
}

Luaweiwo = sgs.CreateViewAsSkill{
	name = "Luaweiwo" ,
	n = 1 ,
	view_filter = function(self, selected, to_select)
		if #selected ~= 0 then return false end
		return (not to_select:isEquipped()) and (to_select:getSuit() == sgs.Card_Heart or to_select:getSuit() == sgs.Card_Spade) and (not sgs.Self:isJilei(to_select))
	end ,
	view_as = function(self, cards)
		if #cards ~= 1 then return nil end
		if cards[1]:getSuit() == sgs.Card_Heart then 
			local LuaweiwoCard1 = LuaweiwoCard1:clone()
			LuaweiwoCard1:addSubcard(cards[1])
			return LuaweiwoCard1
		else
			local LuaweiwoCard2 = LuaweiwoCard2:clone()
			LuaweiwoCard2:addSubcard(cards[1])
			return LuaweiwoCard2
		end 
	end ,
	enabled_at_play = function()
		return true
	end ,
}

toyohime:addSkill(Luashanhai)
toyohime:addSkill(Luaweiwo)

caihuoCard = sgs.CreateSkillCard{
	name = "luacaihuo",
	target_fixed = function(self)		
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then return false end 
		return true
	end,
	will_throw = false,
	filter = function(self, targets, to_select)
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			if to_select:objectName() == sgs.Self:objectName() then return false end
			local snatch = sgs.Sanguosha:cloneCard("snatch", sgs.Card_NoSuit, 0)
			if sgs.Self:isProhibited(to_select, snatch, to_select:getSiblings()) then return false end
			if sgs.Self:isCardLimited(snatch, sgs.Card_MethodUse) then return false end
			return #targets == 0
		end 
		return #targets == 0
	end,
	on_validate = function(self, carduse)
		local source = carduse.from
		local target = carduse.to:first()
		local room = source:getRoom()
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			
			local slash = sgs.Sanguosha:cloneCard("snatch", card:getSuit(), card:getNumber())
			slash:setSkillName(self:objectName())
			slash:addSubcard(card:getEffectiveId())
			room:setPlayerFlag(source,"luacaihuo_used")
			local str = "luacaihuo" .. tostring(card:getEffectiveId()) 
			room:setPlayerMark(source, str, 1)
			local xyz = source:getMark("@joon") + 1
			room:setPlayerMark(source, "@joon", xyz)
			return slash
		
		end
	end, 
	on_validate_in_response = function(self, user)
		local room = user:getRoom()
		local jink = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, -1)
		jink:addSubcard(self:getSubcards():first())
		room:setPlayerFlag(user,"luacaihuo_used")
		local xyz = user:getMark("@joon") + 1
		room:setPlayerMark(user, "@joon", xyz)
		local str = "luacaihuo" .. tostring(self:getSubcards():first()) 
		room:setPlayerMark(user, str, 1)
		jink:setSkillName(self:objectName())
		return jink		
	end,
}
luacaihuo = sgs.CreateOneCardViewAsSkill{
	name = "luacaihuo",
	--filter_pattern = ".|.|.|hand",
	view_filter = function(self, card)
		--if card:isEquipped() then return false end
		for i = 0, 500 do
			local str = "luacaihuo" .. tostring(i) 
			if sgs.Self:getMark(str) > 0 then 
				if card:getId() == i then return false end 
				local dummy = sgs.Sanguosha:getCard(i)
				if dummy then 
					if dummy:objectName() == card:objectName() then return false end 
				end 
			end 
		end 
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
    		local slash = sgs.Sanguosha:cloneCard("snatch", sgs.Card_SuitToBeDecided, -1)
        	slash:addSubcard(card:getEffectiveId())
        	slash:deleteLater()
        	return slash:isAvailable(sgs.Self)		
    	end
    	return true
	end,
	view_as = function(self, originalCard)
		local caihuo_card = caihuoCard:clone()
		caihuo_card:addSubcard(originalCard:getId())
		return caihuo_card
	end,
	enabled_at_play = function(self, player)
		return not player:hasFlag("luacaihuo_used")
	end, 
	enabled_at_response = function(self, player, pattern)
		return pattern == "jink" and not player:hasFlag("luacaihuo_used")
	end
}

luacaihuo2 = sgs.CreateTargetModSkill{
	name = "#luacaihuo" ,
	pattern = "Snatch" ,
	distance_limit_func = function(self, from, card)
		if from:hasSkill("luacaihuo") then
			return 1000
		else
			return 0
		end
	end
}
luacaihuo3 = sgs.CreateTriggerSkill{
	name = "#luacaihuo2" ,
	global = true,
	events = {sgs.EventPhaseStart, sgs.TargetConfirmed, sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		if event == sgs.TargetConfirmed then
			local room = player:getRoom()
			local use = data:toCardUse()
			if use.from and use.from:hasSkill("luacaihuo") and use.card and use.card:isKindOf("Snatch") then
				for _,p in sgs.qlist(use.to) do
					if not p:hasSkill("luapinyi") then
						room:handleAcquireDetachSkills(p, "luapinyi")
					end
				end
			end
		elseif event == sgs.EventPhaseStart then
			local room = player:getRoom() 
			for _, Yuuka in sgs.qlist(room:findPlayersBySkillName("luacaihuo")) do 
				if Yuuka and Yuuka:hasFlag("luacaihuo_used") then
					room:setPlayerFlag(Yuuka, "-luacaihuo_used")
				end
			end 
			return false
		elseif event == sgs.EventPhaseChanging then
			local room = player:getRoom()
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive and player:objectName() == room:getCurrent():objectName() then
				if player:hasSkill("luapinyi") then
					room:handleAcquireDetachSkills(player, "-luapinyi")
					room:setPlayerMark(player, "hand_invalidity", 0)
					room:setPlayerMark(player, "@skill_invalidity", 0)
					for i = 1, 49 do
						room:removePlayerCardLimitation(player, "use,response", ".|.|.|hand$0")
						room:writeToConsole("pingyi test")
					end

				end
			end
		end

	end ,
	can_trigger = function(self, target)
		return target 
	end ,
	priority = 1
}


luazaieCard = sgs.CreateSkillCard{
	name = "luazaie", 
	filter = function(self, targets, to_select)		
		return (#targets == 0)  
	end,
	on_effect = function(self, effect)
		effect.from:getRoom():damage(sgs.DamageStruct("luazaie", effect.from, effect.to, 2))
	end
}
luazaie = sgs.CreateZeroCardViewAsSkill{
	name = "luazaie",
	view_as = function(self, cards)
		return luazaieCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:hasSkill("luazaie") and (player:getMark("@joon") == 13) and not player:hasUsed("#luazaie")
	end,
}


joon:addSkill(luacaihuo)
joon:addSkill(luacaihuo2)
joon:addSkill(luacaihuo3)
joon:addSkill(luazaie)





duhuoCard = sgs.CreateSkillCard{
	name = "luaduhuo",
	filter = function(self, targets, to_select)
		if (#targets > 1 and sgs.Self:getMark("luajiyuan") == 0) then return false end
		if (#targets > 2 and sgs.Self:getMark("luajiyuan") > 0) then return false end
		return true
	end ,
	on_use = function(self, room, source, targets)
		local parsee = source
		local room = parsee:getRoom()
		local suit = room:askForSuit(parsee, "luaduhuo")
		for _,target in ipairs(targets) do 
			if parsee:isKongcheng() then return false end 
			local card_id = room:askForCardChosen(target, parsee, "h", "luaduhuo")
			if parsee:objectName() == target:objectName() then 
				room:setPlayerFlag(parsee, "gluaduhuo")
			end 
			local card = sgs.Sanguosha:getCard(card_id)
			room:getThread():delay()
			room:throwCard(card, parsee, target)
			room:showCard(target, card_id)
			if card:getSuit() == suit then
				room:addPlayerMark(parsee, "@luaduhuo")
				room:damage(sgs.DamageStruct("luaduhuo", parsee, target, 1, sgs.DamageStruct_Fire))			
			end
			room:setPlayerFlag(parsee, "-gluaduhuo")
		end 
	end
}
luaduhuo = sgs.CreateZeroCardViewAsSkill{
	name = "luaduhuo",
	
	view_as = function()
		return duhuoCard:clone()
	end,

	enabled_at_play = function(self, player)
		return (not player:isKongcheng()) and (not player:hasUsed("#luaduhuo"))
	end
}
luajiyuan2 = sgs.CreateTriggerSkill{
	name = "luajiyuan2",
	frequency = sgs.Skill_Limited,
	events = {sgs.GameStart},
	limit_mark = "@weixin",
	on_trigger = function()
	end
}

luajiyuan = sgs.CreateTriggerSkill{
	name = "luajiyuan" ,
	frequency = sgs.Skill_Wake , 
	events = {sgs.MarkChanged},
	on_trigger = function(self, event, player, data, room) 
		local mark = data:toMark()
		if (player:getMark("@luaduhuo") > 2) and player:getMark("luajiyuan") == 0 and mark.name == "@luaduhuo" then  
			if room:changeMaxHpForAwakenSkill(player) then
				room:addPlayerMark(player, "luajiyuan")
				if not player:hasSkill("hongyan") then room:acquireSkill(player, "hongyan") end 
				if not player:hasSkill("luaxinyan") then room:acquireSkill(player, "luaxinyan") end 
			end
		end 
	end  
}

xinyanCard = sgs.CreateSkillCard{
	name = "luaxinyan",
	filter = function(self, targets, to_select)
		if (#targets ~= 0) then return false end
		return true
	end ,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		local pa = effect.to:getHandcardNum()
		local pb = effect.to:getEquips():length()
		local pc = effect.to:getHp()	
		local x = 5
		for _, p in sgs.qlist(room:getOtherPlayers(effect.to)) do
			if p:getHandcardNum() < pa then x = x - 1 ; break end 
		end 
		for _, p in sgs.qlist(room:getOtherPlayers(effect.to)) do
			if p:getEquips():length() < pb then x = x - 1 ; break end 
		end 
		for _, p in sgs.qlist(room:getOtherPlayers(effect.to)) do
			if p:getHp() < pc then x = x - 1 ; break end 
		end 		
		
		local cards = room:getNCards(x)
		local dummy3 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		dummy3:addSubcards(cards)
		effect.to:obtainCard(dummy3)
		local hearts = sgs.IntList()
		local non_hearts = sgs.IntList()
		for _, card_id in sgs.qlist(cards) do
			local card = sgs.Sanguosha:getCard(card_id)
			if card:getSuit() == sgs.Card_Heart or card:getSuit() == sgs.Card_Spade then
				hearts:append(card_id)
			else
				non_hearts:append(card_id)
			end
		end
		if not non_hearts:isEmpty() then
			local dummy2 = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
			dummy2:addSubcards(non_hearts)
			room:throwCard(dummy2, effect.to, effect.to)
		end 
	end 
}

luaxinyan = sgs.CreateZeroCardViewAsSkill{
	name = "luaxinyan",
	
	view_as = function()
		return xinyanCard:clone()
	end,

	enabled_at_play = function(self, player)
		return (not player:hasUsed("#luaxinyan"))
	end
}

parsee:addSkill(luaduhuo)
parsee:addSkill(luajiyuan2)

parseeA:addSkill(luaduhuo)
parseeA:addSkill(luajiyuan)
payceshi:addSkill(luaxinyan)
luashuangren = sgs.CreateTriggerSkill{
	name = "luashuangren",
	events = {sgs.PreCardUsed} ,
	on_trigger = function(self, event, youmu, data)
		if event == sgs.PreCardUsed then
			local room = youmu:getRoom()
			local function YouMuCheck(card, target)
				if card:isKindOf("Hui") or card:isKindOf("Ofuda") then
					return true
				elseif card:isKindOf("FaithCollection") then
					return not target:isNude()
				elseif card:isKindOf("Banquet") then
					return not target:containsTrick("banquet")
				end
			end
			local controlCase = 0  --2是非基本
			if room:getDiscardPile() and room:getDiscardPile():length() > 2 then
				local id_0 = room:getDiscardPile():at(0)
				local id_1 = room:getDiscardPile():at(1)
				local id_2 = room:getDiscardPile():at(2)
				if sgs.Sanguosha:getCard(id_0):isKindOf("BasicCard") and sgs.Sanguosha:getCard(id_1):isKindOf("BasicCard")
						and sgs.Sanguosha:getCard(id_2):isKindOf("BasicCard") then
					controlCase = 1
				end
				if not sgs.Sanguosha:getCard(id_0):isKindOf("BasicCard") and not sgs.Sanguosha:getCard(id_1):isKindOf("BasicCard")
						and not sgs.Sanguosha:getCard(id_2):isKindOf("BasicCard") then
					controlCase = 2
				end
			else
				controlCase = 0
			end
			local use = data:toCardUse()
			local card = use.card
			if use.from:objectName() == youmu:objectName() and use.from:hasSkill("luashuangren")
					and ((use.card:isNDTrick() and controlCase == 1) or (use.card:isKindOf("BasicCard") and controlCase == 2)) then
				if (sgs.Sanguosha:getCurrentCardUseReason() ~= sgs.CardUseStruct_CARD_USE_REASON_PLAY) then return false end
				local available_targets = sgs.SPlayerList()
				if (not use.card:isKindOf("AOE")) and (not use.card:isKindOf("GlobalEffect")) then
					for _, p in sgs.qlist(room:getAlivePlayers()) do
						if (use.to:contains(p) or room:isProhibited(youmu, p, use.card)) then continue end
						if (use.card:targetFixed()) then
							if (not use.card:isKindOf("Peach")) or (p:isWounded()) then
								available_targets:append(p)
							end
						else
							if (use.card:targetFilter(sgs.PlayerList(), p, youmu)  or YouMuCheck(use.card, p)) then
								available_targets:append(p)
							end
						end
					end
				end
				local extra
				if not use.card:isKindOf("Collateral") then
					local Carddata2 = sgs.QVariant() -- ai用
					Carddata2:setValue(use.card)
					room:setTag("luajianjiTC", Carddata2)
					extra = room:askForPlayerChosen(youmu, available_targets, "luajianjic", "luajianjic", true, true)
					room:removeTag("luajianjiTC")
					if extra then
						use.to:append(extra)
					end
				end
				room:sortByActionOrder(use.to)
				data:setValue(use)
				return false
			end
		end
		return false
	end
}

luatingshiCard = sgs.CreateSkillCard{
	name = "luatingshi" ,
	target_fixed = true,
	on_use = function(self, room, source, targets)

		local discard_ids = room:getDrawPile()
		if discard_ids:length() == 0 then return false end
		local cardid = discard_ids:at(0)
		local card = sgs.Sanguosha:getCard(cardid)
		local card_ids = sgs.IntList()
		card_ids:append(cardid)
		room:fillAG(card_ids)
		room:getThread():delay()
		room:getThread():delay()
		room:clearAG()
		local str
		if card:isKindOf("BasicCard") then
			str = "TrickCard,EquipCard"
		elseif card:isKindOf("TrickCard") then
			str = "BasicCard,EquipCard"
		elseif card:isKindOf("EquipCard") then
			str = "BasicCard,TrickCard"
		end
		local Carddata2 = sgs.QVariant() -- ai用
		Carddata2:setValue(card)
		local card1 = room:askForCard(source, str, "@luatingshiA", Carddata2, sgs.Card_MethodNone)
		if card1 then
			room:setPlayerMark(source, "luatingshix", card1:getId() + 1)
			local dummy = sgs.Sanguosha:cloneCard("jink")
			room:writeToConsole("^" .. card1:toString() .. "!")
			local str2 = "^" .. card1:toString() .. "+" .. str .. "+^" .. card1:toString()
			local Carddata3 = sgs.QVariant() -- ai用
			Carddata3:setValue(card1)
			local card2 = room:askForCard(source, str2, "@luatingshiB", Carddata3, sgs.Card_MethodNone) --牛逼，极其牛逼
			room:setPlayerMark(source, "luatingshix", 0)
			if (not card2) or (card1:getId() == card2:getId()) then
				room:writeToConsole("luatingshi test failed")
				room:setPlayerFlag(source, "luatingshi")
				return false
			end
			dummy:addSubcard(card1)
			dummy:addSubcard(card2)
			room:throwCard(dummy, source)
			local dummy2 = sgs.Sanguosha:cloneCard("Slash")
			dummy2:addSubcard(cardid)
			room:obtainCard(source, dummy2)
		else
			room:setPlayerFlag(source, "luatingshi")
		end
	end
}
luatingshi = sgs.CreateZeroCardViewAsSkill{
	name = "luatingshi",
	view_as = function(self, cards)
		return luatingshiCard:clone()
	end,
	enabled_at_play = function(self, player)
		return not player:hasFlag("luatingshi")
	end,
}
youmu:addSkill(luashuangren)
youmu:addSkill(luatingshi) 
lianmeng_move = sgs.CreateTriggerSkill{
	name = "#lianmeng_move",
	global = true,
	events = {sgs.BeforeCardsMove, sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data, room)
		local move = data:toMoveOneTime()
		if not move.from then return false end
		if move.from:getMark("lianmeng_move2") > 0 then return end
		local nyasama  
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			if move.from:getMark("lianmeng" .. p:objectName()) > 0 then nyasama = p end 
		end 
		if not nyasama or not nyasama:isAlive() then return end
		if event == sgs.CardsMoveOneTime then
			if player:objectName() == move.from:objectName() then
				if player:getMark("lianmeng_move") > 0 then
					room:setPlayerMark(player, "lianmeng_move", 0)
					--for i = 1, n do
						if (not player:isWounded()) then
							local _data = sgs.QVariant()
							_data:setValue(player)
							local choice = room:askForChoice(nyasama, "lualianmeng", "draw+nonono+notever", _data)
							if choice == "draw" then
								player:drawCards(2)
							end
						else
							local _data = sgs.QVariant()
							_data:setValue(player)
							local choice = room:askForChoice(nyasama, "lualianmeng", "draw+recover+nonono+notever", _data)
							if choice == "recover" then
								room:recover(player, sgs.RecoverStruct(nyasama))
							elseif choice == "draw" then
								player:drawCards(2)
							elseif choice == "notever" then
								room:setPlayerMark(player, "lianmeng_move2", 1)
							end
						end
					--end

				end
			end
		else
			if not move.from_places:contains(sgs.Player_PlaceHand) then return end
			local suit
			if nyasama:getMark("@lianmengheart") > 0 then
				suit = sgs.Card_Heart
			elseif nyasama:getMark("@lianmengdiamond") > 0 then
				suit = sgs.Card_Diamond
			elseif nyasama:getMark("@lianmengclub") > 0 then
				suit = sgs.Card_Club
			elseif nyasama:getMark("@lianmengspade") > 0 then
				suit = sgs.Card_Spade
			end
			if not suit then return end
			if player:objectName() == move.from:objectName() then
				local n = 0
				for _, id in sgs.qlist(move.card_ids) do
					if move.open and sgs.Sanguosha:getCard(id):getSuit() == suit then
						n = n + 1
					end
				end
				if n == 0 then return false end
				if move.to_place == sgs.Player_DiscardPile then
                    local reason = move.reason.m_reason
                    local reasonx = bit32.band(reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)
                    local Yes = reasonx == sgs.CardMoveReason_S_REASON_DISCARD
                    if Yes then
                        room:setPlayerMark(player, "lianmeng_move", n)

                    end
				end
			end
			return false
		end
	end
}
lualianmengCard = sgs.CreateSkillCard{
    name = "lualianmeng",
	filter = function(self, targets, to_select)
		if #targets ~= 0 then return false end
		return true
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		local _data = sgs.QVariant()
		_data:setValue(effect.to)
		local suit = room:askForChoice(effect.from, "lualianmeng2", "heart+diamond+club+spade", _data)
		room:detachSkillFromPlayer(effect.to, "lualianmeng3") --
		room:setPlayerMark(effect.from, "@lianmeng" .. suit, 1) 
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			room:setPlayerMark(p, "lianmeng" .. effect.from:objectName(), 1)
		end 
		for _,card in sgs.qlist(effect.to:getHandcards()) do
            room:setCardFlag(card, "lualianmeng")
            room:setCardFlag(card, "lualianmeng" .. suit)
        end
        room:acquireSkill(effect.to, "lualianmeng3", false)
		effect.from:loseAllMarks("@lianmeng")
    end
}

lualianmengVS = sgs.CreateZeroCardViewAsSkill{
	name = "lualianmeng",
	view_as = function(self, cards)
		return lualianmengCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@lianmeng") >= 1
	end
}
lualianmeng = sgs.CreateTriggerSkill{
	name = "lualianmeng",
	frequency = sgs.Skill_Limited,
	events = {sgs.GameStart, sgs.EventPhaseStart},
	limit_mark = "@lianmeng",
	view_as_skill = lualianmengVS,
	on_trigger = function()
	end
}

luawanbang = sgs.CreateTriggerSkill{
	name = "luawanbang$",
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				if p2:hasLordSkill("luawanbang") then
					for _, p in sgs.qlist(room:getAllPlayers()) do
						if not p:hasSkill("luawanbangr") and p:objectName() ~= p2:objectName() then room:writeToConsole("jiantin test" .. p:objectName()); room:attachSkillToPlayer(p, "luawanbangr") end
					end
				end
			end
		end
	end
}

nyasama:addSkill(lualianmeng)
nyasama:addSkill(luawanbang)
nyasama:addSkill(lianmeng_move)


luajunzhenCard = sgs.CreateSkillCard{
	name = "luajunzhen",
	target_fixed = false,
	filter = function(self, targets, to_select)
		local x = 0
		if sgs.Self:getMark("@luageji2") > 0 then x = x + 1 end
		local fire_slash = sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_NoSuit, 0)
		local thunder_slash = sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_NoSuit, 0)
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		local analeptic = sgs.Sanguosha:cloneCard("analeptic", sgs.Card_NoSuit, 0)
		local peach = sgs.Sanguosha:cloneCard("peach", sgs.Card_NoSuit, 0)
		local ofuda = sgs.Sanguosha:cloneCard("ofuda", sgs.Card_NoSuit, 0)
		local ava = false
		if not sgs.Self:isProhibited(to_select, fire_slash) and fire_slash:isAvailable(sgs.Self) and sgs.Self:inMyAttackRange(to_select)
			and not sgs.Self:isCardLimited(fire_slash, sgs.Card_MethodUse) then ava = true end
		if not sgs.Self:isProhibited(to_select, thunder_slash) and thunder_slash:isAvailable(sgs.Self) and sgs.Self:inMyAttackRange(to_select)
			and not sgs.Self:isCardLimited(thunder_slash, sgs.Card_MethodUse) then ava = true end
		if not sgs.Self:isProhibited(to_select, slash) and slash:isAvailable(sgs.Self) and sgs.Self:inMyAttackRange(to_select)
			and not sgs.Self:isCardLimited(slash, sgs.Card_MethodUse) then ava = true end
		if not sgs.Self:isProhibited(to_select, analeptic) and sgs.Analeptic_IsAvailable(sgs.Self)
			and not sgs.Self:isCardLimited(analeptic, sgs.Card_MethodUse) then ava = true end
		if not sgs.Self:isProhibited(to_select, peach) and to_select:isWounded()  
			and not sgs.Self:isCardLimited(peach, sgs.Card_MethodUse) then ava = true end
		if not sgs.Self:isProhibited(to_select, ofuda) and not to_select:isAllNude() and sgs.Self:inMyAttackRange(to_select)
			and not sgs.Self:isCardLimited(ofuda, sgs.Card_MethodUse) then ava = true end
		return #targets <= x and ava
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		if not effect.to then effect.to = effect.from end
		local choices = {}
		local fire_slash = sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_NoSuit, 0)
		if not room:isProhibited(effect.from, effect.to, fire_slash) and fire_slash:isAvailable(effect.from) and effect.from:inMyAttackRange(effect.to)
				and not effect.from:isCardLimited(fire_slash, sgs.Card_MethodUse) then table.insert(choices, "fire_slash") end

		local thunder_slash = sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_NoSuit, 0)
		if not room:isProhibited(effect.from, effect.to, thunder_slash) and thunder_slash:isAvailable(effect.from) and effect.from:inMyAttackRange(effect.to)
				and not effect.from:isCardLimited(thunder_slash, sgs.Card_MethodUse) then table.insert(choices, "thunder_slash") end

		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		if not room:isProhibited(effect.from, effect.to, slash) and slash:isAvailable(effect.from) and effect.from:inMyAttackRange(effect.to)
				and not effect.from:isCardLimited(slash, sgs.Card_MethodUse) then table.insert(choices, "slash") end

		local analeptic = sgs.Sanguosha:cloneCard("analeptic", sgs.Card_NoSuit, 0)
		if not room:isProhibited(effect.from, effect.to, analeptic) and sgs.Analeptic_IsAvailable(effect.from)
				and not effect.from:isCardLimited(analeptic, sgs.Card_MethodUse) then table.insert(choices, "analeptic") end

		local peach = sgs.Sanguosha:cloneCard("peach", sgs.Card_NoSuit, 0)
		if not room:isProhibited(effect.from, effect.to, peach) and effect.to:isWounded()
				and not effect.from:isCardLimited(peach, sgs.Card_MethodUse) then table.insert(choices, "peach") end

		local ofuda = sgs.Sanguosha:cloneCard("ofuda", sgs.Card_NoSuit, 0)
		if not room:isProhibited(effect.from, effect.to, ofuda) and not effect.to:isAllNude() and effect.from:inMyAttackRange(effect.to)
				and not effect.from:isCardLimited(ofuda, sgs.Card_MethodUse) then table.insert(choices, "ofuda") end
		local playerdata = sgs.QVariant() --ai用
		playerdata:setValue(effect.to)
		if #choices > 0 then 
			local choice = room:askForChoice(effect.from, "luajunzhen", table.concat(choices, "+"), playerdata) 
			local card = sgs.Sanguosha:cloneCard(choice, sgs.Card_NoSuit, 0)
			room:useCard(sgs.CardUseStruct(card, effect.from, effect.to), true)
		end 
	end
}
luajunzhen = sgs.CreateOneCardViewAsSkill{
	name = "luajunzhen",
	filter_pattern = "EquipCard",
	view_as = function(self,card)
		local skillcard = luajunzhenCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self,player)
		return true
	end,
}
luajunzhen2 = sgs.CreateTriggerSkill{
	name = "#luajunzhen2",
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.DrawInitialCards, sgs.BeforeCardsMove, sgs.EventPhaseEnd},
	on_trigger = function(self, triggerEvent, alice, data)
		local room = alice:getRoom()
		if triggerEvent == sgs.DrawInitialCards then
			if alice:hasSkill("luajunzhen") then
				room:notifySkillInvoked(alice, "luajunzhen")
				data:setValue(data:toInt() - 2)
				for _, Acard in sgs.list(alice:getHandcards()) do
					if Acard:objectName() == "hongrai" or Acard:objectName() == "shanghai" then
						alice:drawCards(1)
					end
				end
				for _, id in sgs.qlist(room:getDrawPile()) do
					local Acard = sgs.Sanguosha:getCard(id)
					if Acard:objectName() == "hongrai" or Acard:objectName() == "shanghai" then
						local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
						dummy:addSubcard(id)
						room:obtainCard(alice, dummy)
					end
				end
			end
		elseif triggerEvent == sgs.BeforeCardsMove then
			local move = data:toMoveOneTime()
			if move.from and move.from:objectName() == alice:objectName() and alice:hasSkill("luajunzhen") and not move.card_ids:isEmpty()
					and move.from_places:contains(sgs.Player_PlaceEquip) and not alice:hasFlag("luajunzheni") then
				local use_list2 = sgs.IntList()

				local i = 0
				for _,id in sgs.qlist(move.card_ids) do
					card = sgs.Sanguosha:getCard(id)
					if (room:getCardPlace(id) == sgs.Player_PlaceEquip and room:getCardOwner(id):getSeat() == alice:getSeat()
						and not card:isKindOf("Roukankenx")) then
						use_list2:append(id)
						alice:gainMark("@luajunzhen")
					end
					i = i + 1
				end
				local xo = room:getLord():getMark("@clock_time") + 1
				room:writeToConsole("alice test" .. xo)
				if alice:getMark("luajunzhenk") ~= xo then
					room:setPlayerMark(alice, "luajunzhenk", xo)
					local duel = sgs.Sanguosha:cloneCard("duel", sgs.Card_NoSuit, 0)
					room:setPlayerFlag(alice, "luajunzheni")
					duel:addSubcards(use_list2)
					alice:obtainCard(duel)
					duel:deleteLater()
					room:setPlayerFlag(alice, "-luajunzheni")

					i = 0
					local old_card_ids = {}
					for _,card_idX in sgs.qlist(move.card_ids) do
						table.insert(old_card_ids, card_idX)
					end
					for _, card_idY in ipairs(old_card_ids) do
						if (use_list2:contains(card_idY)) then
							move.card_ids:removeOne(card_idY)
							move.from_places:removeAt(i)
						else
							i = i + 1
						end
					end
					data:setValue(move)
					return false
				end
			end
		elseif triggerEvent == sgs.EventPhaseEnd then
			if alice:getPhase() == sgs.Player_Finish then
				for _, p2 in sgs.qlist(room:findPlayersBySkillName("luajunzhen")) do
					if p2:hasSkill("luaguixu") and p2:getMark("@luajunzhen") > 1 and p2:getMark("@guixu") > 0 then
						local p = room:askForPlayerChosen(p2, room:getAlivePlayers(), "luaguixu", "luaguixu9", true, true)
						if p then
							room:damage(sgs.DamageStruct(self:objectName(), p2, p, p2:getMark("@luajunzhen") - 2, sgs.DamageStruct_Normal))
							p2:loseAllMarks("@guixu")
						end
					end
					room:setPlayerMark(p2, "@luajunzhen", 0)
				end
			end
		end
	end,
}
luajunzhen4 = sgs.CreateTriggerSkill{
	name = "#luajunzhen4" ,
	global = true,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Start then
			for _, toziko in sgs.qlist(room:findPlayersBySkillName("luajunzhen")) do
				if toziko then
					room:setPlayerMark(toziko, "@luajunzhen", 0)
				end
			end 
		end
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}
luaguixu = sgs.CreateTriggerSkill{
	name = "luaguixu",
	frequency = sgs.Skill_Limited,
	events = {sgs.GameStart},
	limit_mark = "@guixu",
	on_trigger = function()
	end
}
alice:addSkill(luajunzhen)
alice:addSkill(luajunzhen2)
alice:addSkill(luajunzhen4)
alice:addSkill(luaguixu)

aliceA:addSkill(luajunzhen)
aliceA:addSkill(luajunzhen2)
aliceA:addSkill(luaguixu)

aliceB:addSkill(luajunzhen)
aliceB:addSkill(luajunzhen2)
aliceB:addSkill(luaguixu)

aliceC:addSkill(luajunzhen)
aliceC:addSkill(luajunzhen2)
aliceC:addSkill(luaguixu)

aliceD:addSkill(luajunzhen)
aliceD:addSkill(luajunzhen2)
aliceD:addSkill(luaguixu)

aliceE:addSkill(luajunzhen)
aliceE:addSkill(luajunzhen2)
aliceE:addSkill(luaguixu)

shenjuCard = sgs.CreateSkillCard{
	name = "luashenju", 
	will_throw = false,
	target_fixed = true, 
	handling_method = sgs.Card_MethodNone, 
	on_use = function(self, room, source, targets)
		--room:setPlayerCardLimitation(source, "discard", ".|.|.|hand", true)
		room:setPlayerCardLimitation(source, "use", "Slash|.|.|hand", true)
		local supply = sgs.Sanguosha:cloneCard("supply_shortage", sgs.Card_SuitToBeDecided, -1)
		local indulgence = sgs.Sanguosha:cloneCard("indulgence", sgs.Card_SuitToBeDecided, -1)
		supply:addSubcard(self:getSubcards():first())
		indulgence:addSubcard(self:getSubcards():first())
		local choices = {}
		if not source:isCardLimited(indulgence, sgs.Card_MethodUse) and not source:containsTrick("indulgence")
			and not source:isProhibited(source, indulgence, source:getSiblings()) then table.insert(choices, "indulgence") end 
		if not source:isCardLimited(supply, sgs.Card_MethodUse) and not source:containsTrick("supply_shortage")
			and not source:isProhibited(source, supply, source:getSiblings()) then table.insert(choices, "supply_shortage") end 			
		if #choices == 0 then return end 
		local choice = room:askForChoice(source, "luashenju", table.concat(choices, "+"))
		if choice == "indulgence" then 
			room:useCard(sgs.CardUseStruct(indulgence, source, source))	
		else
			room:useCard(sgs.CardUseStruct(supply, source, source))	
		end 
			local choices2 = {"draw"}
			table.insert(choices2, "recover")
			local choice2 = room:askForChoice(source, "luashenjuq", table.concat(choices2, "+"))
			if choice2 == "recover" then
				room:setPlayerProperty(source, "maxhp", sgs.QVariant(source:getMaxHp() + 1))
				room:recover(source, sgs.RecoverStruct(source))
			else
				source:drawCards(1)
				local function getSameEquip(card, player)
					if not card then return end
					if card:isKindOf("Weapon") then return player:getWeapon()
					elseif card:isKindOf("Armor") then return player:getArmor()
					elseif card:isKindOf("DefensiveHorse") then return player:getDefensiveHorse()
					elseif card:isKindOf("OffensiveHorse") then return player:getOffensiveHorse()
					elseif card:isKindOf("Treasure") then return player:getTreasure()
					end
				end		
				local discard_ids = room:getDiscardPile()
				local trickcard = sgs.IntList()
				for _, id in sgs.qlist(discard_ids) do 
					local card = sgs.Sanguosha:getCard(id)
					if card:isKindOf("EquipCard") and not card:isKindOf("Weapon") and not getSameEquip(card, source) then
						trickcard:append(id)
					end 
				end 	
				discard_ids = room:getDrawPile()
				for _, id in sgs.qlist(discard_ids) do 
					local card = sgs.Sanguosha:getCard(id)
					if card:isKindOf("EquipCard") and not card:isKindOf("Weapon") and not getSameEquip(card, source) then
						trickcard:append(id)
					end 
				end
				if trickcard:length() > 0 then
					room:fillAG(trickcard, source)
					local card_id = room:askForAG(source, trickcard, false, "luashenju")
					local card_r = sgs.Sanguosha:getCard(card_id)
					local card_po = card_r:objectName()
					if card_r:isKindOf("DefensiveHorse") or card_r:isKindOf("OffensiveHorse") then 
						local dummy_p = sgs.Sanguosha:cloneCard("jink")						
						dummy_p:addSubcard(card_id)	
						room:moveCardTo(dummy_p, source, source, sgs.Player_PlaceEquip,
							sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, source:objectName(), self:objectName(), nil))
					else
						local dummy_0 = sgs.Sanguosha:cloneCard(card_po, sgs.Card_NoSuit, 0)
						dummy_0:addSubcard(card_id)
						room:useCard(sgs.CardUseStruct(dummy_0, source, sgs.SPlayerList()))						
					end
					room:clearAG()
				end 						
			end 
	end 
}
luashenju = sgs.CreateOneCardViewAsSkill{
	name = "luashenju",
	filter_pattern = ".",
	view_as = function(self,card)
		local skillcard = shenjuCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self,player)
		return not (player:containsTrick("indulgence") and player:containsTrick("supply_shortage"))
			and (not player:hasFlag("forbidshenju"))
	end,
}
luashenjucf = sgs.CreateTriggerSkill{
	name = "#luashenjucf" ,
	events = {sgs.CardUsed} ,
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		if use.card:isKindOf("Slash") and (player:getPhase() == sgs.Player_Play) and (not player:hasFlag("forbidshenju")) then
			player:getRoom():setPlayerFlag(player, "forbidshenju")
		end
		return false
	end
}

lualianshecard = sgs.CreateSkillCard{
	name = "lualianshe",	
	filter = function(self, targets, to_select)
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
        slash:deleteLater()
		if sgs.Self:isCardLimited(slash, sgs.Card_MethodUse) then return end 
		if sgs.Self:isProhibited(to_select, slash, to_select:getSiblings()) then return false end
		return #targets == 0
	end,	
	on_effect = function(self,effect)
		local room = effect.from:getRoom()
		local pa = effect.from:getHandcardNum()
		local pb = effect.from:getEquips():length()
		local pc = effect.from:getHp()
		local x = 4
		for _, p in sgs.qlist(room:getOtherPlayers(effect.from)) do
			if p:getHandcardNum() >= pa then x = x - 1 ; break end 
		end 
		for _, p in sgs.qlist(room:getOtherPlayers(effect.from)) do
			if p:getEquips():length() >= pb then x = x - 1 ; break end 
		end 
		for _, p in sgs.qlist(room:getOtherPlayers(effect.from)) do
			if p:getHp() >= pc then x = x - 1 ; break end 
		end 		
		for i = 1,x do
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			if (not effect.from:isCardLimited(slash, sgs.Card_MethodUse)) and effect.to:isAlive() 
				and (not effect.from:isProhibited(effect.to, slash, effect.to:getSiblings())) then 
				room:useCard(sgs.CardUseStruct(slash, effect.from, effect.to))
			end 
		end 
		local card = room:askForCard(effect.from, ".|.|.|.", "@lualianshe", sgs.QVariant(), sgs.Card_MethodNone)
		local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		for _, car in sgs.list(effect.from:getHandcards()) do
			if not card or car:getId() ~= card:getId() then dummy:addSubcard(car) end
		end
		local qiguang = false
		for _, car in sgs.list(effect.from:getEquips()) do
			if not card or car:getId() ~= card:getId() then
				dummy:addSubcard(car)
				if car:isKindOf("Wanbaochui") then
					qiguang = true
				end
			end
		end

		room:setPlayerFlag(effect.from, "DontDiscard")
		room:throwCard(dummy, effect.from, effect.from)
		room:setPlayerFlag(effect.from, "-DontDiscard")
		if not effect.from:isNude() and qiguang then
			room:askForDiscard(effect.from, self:objectName(), 1, 1, false, true)
		end
		room:setPlayerProperty(effect.from, "maxhp", sgs.QVariant(effect.from:getMaxHp() - 1))
	end,

}
lualiansheVS = sgs.CreateViewAsSkill{
	name = "lualianshe",	
	n = 0,	
	view_filter = function()
		return false
	end,	
	view_as = function()
		return lualianshecard:clone()
	end,	
	enabled_at_play = function()
		return false
	end,
	enabled_at_response = function(self, player, pattern)
        return pattern == "@@lualianshe"
	end,

}
lualianshe = sgs.CreateTriggerSkill{
	name = "lualianshe",
	events = {sgs.EventPhaseStart},
	view_as_skill = lualiansheVS,
	on_trigger = function(self,event,player,data)
		local phase = player:getPhase()
		local room = player:getRoom()
		if phase == sgs.Player_Start then			
			if room:askForUseCard(player, "@@lualianshe", "@lualianshe" , -1, sgs.Card_MethodNone) then 			
			end 
		end 
	end 
}
hatate:addSkill(luashenju)
hatate:addSkill(luashenjucf)
hatate:addSkill(lualianshe)

lualinglanCard = sgs.CreateSkillCard{
	name = "lualinglan" ,
	filter = function(self, targets, to_select)
		return not to_select:isNude() and to_select:isWounded() and #targets == 0
	end ,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
        if not effect.to:isNude() then
            local to_throw = room:askForCardChosen(effect.from, effect.to, "he", self:objectName(), false)
            room:obtainCard(effect.from, sgs.Sanguosha:getCard(to_throw), false)
            room:recover(effect.to, sgs.RecoverStruct(effect.from, nil, 1))
        end
	end
}
lualinglan = sgs.CreateViewAsSkill{
	name = "lualinglan" ,
	n = 1 ,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#lualinglan")
	end ,
	view_filter = function(self, selected, to_select)
		return not sgs.Self:isJilei(to_select) and #selected == 0 and to_select:getSuit() == sgs.Card_Club
	end ,
	view_as = function(self, cards)
		if #cards == 1 then
			local linglan = lualinglanCard:clone()
			for _, c in ipairs(cards) do
				linglan:addSubcard(c)
			end
			return linglan
		end
	end ,
}

luadaidu = sgs.CreateTriggerSkill{
	name = "luadaidu",
	global = true,
	events = {sgs.DamageInflicted},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.DamageInflicted then
			local damage = data:toDamage() 
			if (not damage.card or (not damage.card:isKindOf("AOE"))) and damage.from and damage.from:hasSkill("luadaidu")
				and damage.from:objectName() ~= damage.to:objectName() then
				local bool1 = damage.to:getHandcardNum() >= damage.to:getMaxCards()
				local function canLoseHp()  --luajiance
					for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
						if hecatiaX and isFriendQ(room, damage.to, hecatiaX) and damage.to:objectName() ~= hecatiaX:objectName()
								and damage.to:getHp() == hecatiaX:getHp() then
							room:notifySkillInvoked(hecatiaX, "luayiti")
							return false
						end 
					end 
					for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
						if Erin and Erin:getKingdom() == damage.to:getKingdom() then
							room:notifySkillInvoked(Erin, "luajiance")
							return false
						end 
					end 
					return true
				end 
				if damage.to:getHp() >= damage.to:getMaxHp() and canLoseHp() then 
					room:loseHp(damage.to, 1)
				end
				if bool1 then
					room:askForDiscard(damage.to, self:objectName(), 1, 1, false, true)
				end
			end
		end
		return false
	end
}

luajunying = sgs.CreateTriggerSkill{
	name = "luajunying",
	events = {sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data)
		local phase = player:getPhase()
		local room = player:getRoom()
		if event == sgs.EventPhaseEnd then
			if phase == sgs.Player_Finish then
				player:drawCards(1)
				local playerdata = sgs.QVariant() -- ai用
				playerdata:setValue(player)
				room:setTag("luajunying", playerdata)
				room:removeTag("luajunying")
				local thread = room:getThread()
				room:setPlayerFlag(player, "luajunying")
				local old_phase = player:getPhase()
				player:setPhase(sgs.Player_Play)
				room:broadcastProperty(player, "phase")
				if not thread:trigger(sgs.EventPhaseStart, room, player) then
					thread:trigger(sgs.EventPhaseProceeding, room, player)
				end
				thread:trigger(sgs.EventPhaseEnd, room, player)
				player:setPhase(old_phase)
				room:broadcastProperty(player, "phase")
			end
		end
	end 	
}
medicine:addSkill(lualinglan)
medicine:addSkill(luajunying)
medicine:addSkill(luadaidu)
sgs.LoadTranslationTable{
	["pay"] = "风景 标准", --注意这里每次要加逗号  room:getOtherPlayers(player)
	["shinki"] = "神绮",
	["shinkiA"] = "神绮",
	["&shinki"] = "神绮",
	["#shinki"] = "六翼魔神王",
	["designer:shinki"] = "Paysage",  --
	["illustrator:shinki"] = "音無空太",
	["luatianzhao"] = "天诏",
	["luatianzhaor"] = "天诏",
	[":luatianzhaor"] = "点击这个按钮，永远不响应“天诏”",
	[":luatianzhao"] = "每回合限四次，当你需要使用或打出一张基本牌时，你可以令其他角色替你使用或打出之，然后其摸一张牌。",
	["$shenhui1"] = "嘛，那就交给你吧。",
	["$shenhui2"] = "日月天属，列星我陈。阴阳三合，一以统同。",
	["shenpan"] = "神判",
	["luatianzhaoA"] = "你可以发动“天诏”，让一名其他角色帮你打出一张【杀】。",
	["luatianzhaoB"] = "你可以发动“天诏”，让一名其他角色帮你打出一张【闪】。",
	["luatianzhaoC"] = "你可以发动“天诏”，让一名其他角色帮你打出一张【酒】。",
	["luatianzhaoD"] = "你可以发动“天诏”，让一名其他角色帮你打出一张【桃】。",
	["shenpanW"] = "请为“神判”打出一张【杀】。",
	["shenpanX"] = "请为“神判”打出一张【闪】。",
	["shenpanY"] = "请为“神判”打出一张【酒】。",
	["shenpanZ"] = "请为“神判”打出一张【桃】。",
	["@tianzhao-slash"] = "请打出一张【杀】响应 %src “天诏”",
	["@tianzhao-jink"] = "请打出一张【闪】响应 %src “天诏”",
	["@tianzhao-ana"] = "请打出一张【酒】响应 %src “天诏”",
	["@tianzhao-peach"] = "请打出一张【桃】响应 %src “天诏”",
	[":shenpan"] = "出牌阶段限一次，你可以依次打出任意张牌名不同的基本牌，若以此法打出的牌数不少于4张，你可以对任意名角色造成一点伤害，否则你收回这些牌。",
	["luashengdian"] = "神殿",
	[":luashengdian"] = "主公技，锁定技，其他角色与你计算距离+X（X为存活人数除以3，向下取整）。",
	["shenen1"] = "神恩",
	["shenen2"] = "神恩",

	["sanae"] = "东风谷早苗",
	["sanaeA"] = "东风谷早苗",
	["sanaeB"] = "东风谷早苗",
	["sanaeC"] = "东风谷早苗",
	["sanaeD"] = "东风谷早苗",
	["designer:sanae"] = "Paysage、岳逍遥",
	["illustrator:sanae"] = "amber",
	["#sanae"]= "现代人的现人神",
	["luasanaex"] = "神迹",
	[":luasanaex"] = "一名角色的回合结束时，你可以将你于这回合获得的一张牌当作任意牌使用。",
	["@luasanaex"] = "你可以发动 “神迹”。",
	["@luasanaexA"] = "你可以用亮起牌中的一张来发动 “神迹”",
	["~luasanaex"] = "选择目标→确定（如果是【桃】【无中生有】等固定目标的牌，直接点击确定即可）",
	["luasanaey"] = "秘术",
	[":luasanaey"] = "锁定技，你每受到1点伤害，你获得一个“秘术”标记，你每有一个“秘术”标记，你的手牌上限+1。你的回合外，你的回复量-1。",
	["luasanaez"] = "客星",
	[":luasanaez"] = "一名角色任意阶段开始时，你可以弃置一个“秘术”标记并摸一张牌。",

	["luaqiji"] = "奇迹",
	["luaqiji2"] = "请选择奇迹的颜色",
	[":luaqiji"] = "回合开始阶段，你可以宣言一个花色并进行一次判定。若其花色与所宣言的相同，这回合最多Ｘ次，你可以将一张手牌当作任意牌使用或打出（Ｘ为你已损失体力值）。",
	["$luaqiji"] = "相信的心是创造奇迹的力量！",
	["mishu9"] = "秘术",
	[":mishu9"] = "你的一张判定牌生效前，你可以用一名角色的一张手牌替换之。",
	["@mishu9"] = "你可以点击 “秘仪” 并选择一张手牌来发动 “秘仪”（此时不必指定目标）。",
	["$mishu9"] = "希望的群星指引未来的方向！",
	["shenfeng9"] = "神风",
	[":shenfeng9"] = "锁定技，你的手牌上限至少为3",
	["@@LuaNosguhuo"] = "此时你可以将一张手牌当作你之前声明的牌使用",

	["yuka"] = "风见幽香",
	["yukaA"] = "风见幽香",
	["illustrator:yuka"] = "赤蜻蛉",
	["#yuka"] = "鲜花之主",
	["luahuapu"] = "花圃",
	[":luahuapu"] = "出牌阶段结束时，你可以展示手牌中任意张草花牌，若以此法展示了：1张牌，你摸一张牌；2张牌，你回复一点体力；3张牌，你可以对一名角色造成一" ..
		"点伤害；4张牌，你执行前面的全部效果，然后你可以弃两张牌，获得一个额外的出牌阶段。",
	["~luahuapu"] = "你可以发动“花圃”",
	["@luahuapu"] = "选择要展示的那些牌",
	["luahuapua"] = "请选择要造成伤害的角色",
	["luahuapub"] = "你可以弃两张牌，获得一个额外的出牌阶段。",
	
	["yukaB"] = "风见幽香",
	["illustrator:yukaB"] = "白山ゆーき",
    ["designer:yukaB"] = "Paysage",
	["luachenmian"] = "沉眠",
	[":luachenmian"] = "游戏开始时，你将所有手牌至于牌堆底并翻面。然后获得一张梅花牌与“落英”。",
	["luayoumeng"] = "幽梦",
	[":luayoumeng"] = "觉醒技，准备阶段，你可以展示四张梅花手牌，减少一半体力上限并获得“花圃”。",
	["~luayoumeng"] = "你可以发动“幽梦”",
	["@luayoumeng"] = "选择要展示的那些牌",	
	["luachangmei"] = "长寐",
	[":luachangmei"] = "主公技，游戏开始时你额外增加一点体力上限并回复一点体力。",
	
	
	["reisen"] = "优昙华院",
	["reisenA"] = "优昙华院",
	["reisenC"] = "优昙华院",
	["reisenD"] = "优昙华院",
	["#reisen"]= "狂气之瞳",
	["illustrator:reisen"] = "まさる.jp",
	["illustrator:reisenA"] = "Ekita玄",
	["illustrator:reisenC"] = "ひそな",
	["illustrator:reisenD"] = "まあち",
	["Luayuelong"] = "月胧",
	[":Luayuelong"] = "每当你因群体锦囊以外受到或造成伤害后，你可以使用一张牌并摸一张牌。" ..
		"若以此法使用了黑色牌且指定唯一其他角色为目标，则你可以令其选择一项：弃置牌至合计点数大于使用牌，或手牌上限-1。你于\"月胧\"期间不能再发动“月胧”。",
	["~Luayuelong"] = "你可以发动“月胧”",	
	["@Luayuelong"] = "选择要使用的那张牌",
	["Luayuelong-invoke"] = "请选择一名角色作为「月胧」的对象",
	["Luayuelong1"] = "弃置点数合计比此牌大的牌",
	["Luayuelong2"] = "手牌上限永久减一",


	["reisenB"] = "优昙华院",
	["#reisenB"]= "国士无双",
	["luayueni"] = "月睨",
	[":luayueni"] = "每当你因群体锦囊以外造成伤害后，你可以使用一张牌并摸一张牌。.此法使用的牌指定目标后，你可以弃置其一张牌。你于“月睨”期间不能再发动“月睨”。",
	["~luayueni"] = "你可以发动“月睨”",
	["@luayueni"] = "选择要使用的那张牌",
	["luayueni-invoke"] = "请选择一名角色作为「月胧」的对象",
	["luaguoshi"] = "国士",
	[":luaguoshi"] = "出牌阶段，你可以回复一点体力然后对一名其他角色造成一点伤害。你每第4次发动“国士”时，改为“减少一点体力上限并弃一张牌。”",

	["parsee"] = "水桥帕露西",	
	["parseeA"] = "水桥帕露西",
	["designer:parsee"] = "Paysage",
	["illustrator:parsee"] = "ryosios",
	["#parsee"]= "孑然妒火",	
	["luaduhuo"] = "妒火",		
	["duhuo"] = "妒火",		
	[":luaduhuo"] = "出牌阶段限一次，你可以声明一个花色，令至多两名角色弃置你一张手牌。若所弃置牌是你声明的花色，你对其造成1点火焰伤害，并获得一枚“妒”标记。",	
	["luajiyuan"] = "积怨",
	["luajiyuan2"] = "对一名角色造成一点火焰伤害",
	[":luajiyuan"] = "觉醒技，当你“妒”标记数大于2时，你须失去1点体力上限并获得“心焰”“红颜”。此后，“妒火”可选目标数+1。",
	["luajiyuan2"] = "积怨",
	[":luajiyuan2"] = "觉醒技，当你“妒”标记数大于2时，你须失去1点体力上限并获得“心焰”“红颜”。此后，“妒火”可选目标数+1。\
						[<i><b>心焰</b>：出牌阶段限一次，你可以选择一名角色，展示并获得牌堆顶的X+2张牌，弃置其中的非红桃牌，并将余下的牌交给该角色。（X为其满足下列项数：体力值/手牌数/装备数为全场最少之一）</i>]",
	["luaxinyan"] = "心焰",		
	[":luaxinyan"] = "出牌阶段限一次，你可以选择一名角色，展示并获得牌堆顶的X+2张牌，弃置其中的非红桃牌，并将余下的牌交给该角色。（X为其满足下列项数：体力值/手牌数/装备数为全场最少之一）",
	
	["toyohime"] = "绵月丰姬",	
	["#toyohime"]= "山与海的公主",
	["Luashanhai"] = "山海",	
	["Luashanhaia"] = "山海",	
	[":Luashanhai"] = "每当你受到1点伤害后，你可以令场上一名角色获得“飞影”或者“马术”。",	
	["Luashanhai-invoke"] = "请选择一名角色作为「山海」的对象",
	["Luaweiwo"] = "帷幄",	
	[":Luaweiwo"] = "出牌阶段，你可以弃置一张红桃手牌，视为对一名攻击范围内没有你的角色使用了一张【顺手牵羊】；或者弃置一张黑桃手牌，视为对你攻击范围内的一名角色使用了一张【劝降】。",	
	-- ["yorihime"] = "绵月依姬",		
	-- ["#yorihime"]= "山与海的公主",	
	-- ["Luaweifeng"] = "威风",	
	-- [":Luaweifeng"] = "每当你受到或者造成1点伤害后，你可以将一张手牌置于你的武将牌上，称为“神灵”。",	
	-- ["Luapingyi"] = "凭依",	
	-- [":Luapingyi"] = "回合开始阶段，你可以弃置武将牌上两张同花色的“神灵”牌，此回合，你声明并获得一名蜀势力武将的一个技能（主公技、觉醒技、限定技除外），或者获得“天照”“月读”。（天照：出牌阶段限一次，你可以弃置X张红色牌，对一名体力值不小于你的角色造成一点火焰伤害（X为其体力值）（月读：出牌阶段限一次，你可以弃置X+1张黑色牌，将一名体力值不大于你的角色武将牌翻面（X为其已损失体力值）））",	
	["joon"] = "依神姐妹",	
	["#joon"]= "最凶最恶的双子",	
	["luacaihuo"] = "财祸",	
	[":luacaihuo"] = "每回合限一次，你可以将一张牌当作【顺手牵羊】或【闪】使用或打出（每牌名限一次）。锁定技，你使用【顺手牵羊】无距离限制，且目标获得“贫疫”直到其下个回合结束。",
	["luazaie"] = "灾厄",	
	[":luazaie"] = "出牌阶段限一次，若你于本局游戏发动“财祸”正好13次，你可以对一名角色造成2点伤害。",
	["luapinyi"] = "贫疫",
	[":luapinyi"] = "锁定技，若你的体力值为1，你的非锁定技无效；若你的手牌数为1，你不能使用或打出手牌。",

	["youmu"] = "魂魄妖梦",	 
	["#youmu"] = "苍天的庭师",
	["luashuangren"] = "双刃",
	["designer:youmu"] = "Kevin",  --乃絵のえる
	["illustrator:youmu"] = "乃絵のえる",
	["@chixiao"] = "一闪",
	["~luashuangren"] = "选择一张手牌→点击确定",
	["@luashuangren"] = "你可以发动“双锋”",
	[":luashuangren"] = "弃牌堆顶的三张牌均为基本牌/非基本牌的场合，若合法，你使用锦囊牌/基本牌指定目标时，可以为此牌增加一个目标。",
	["luatingshi"] = "庭师",
	[":luatingshi"] = "出牌阶段，你可以展示牌堆顶的牌，然后你须弃置两张与之种类不同的牌并获得之，否则本回合“庭师”失效。",
	["@luatingshiA"] = "请弃置两张与之种类不同的牌，现在选择第一张",
	["@luatingshiB"] = "请弃置两张与之种类不同的牌，现在选择第一张",

	["medicine"] = "梅蒂欣",	
	["#medicine"] = "甜蜜毒药",		
	["designer:medicine"] = "Paysage",  --亜音
	["illustrator:medicine"] = "亜音",
	["lualinglan"] = "铃兰",		
	["linglan"] = "铃兰",		
	[":lualinglan"] = "出牌阶段限一次，你可以弃置一张♣牌，获得一名已受伤角色的一张牌并令其回复1点体力。",
	["luadaidu"] = "甙毒",
	[":luadaidu"] = "锁定技，群体锦囊以外你对其他角色造成伤害时，若其体力值不小于体力上限，其流失1点体力；若其手牌数不小于手牌上限，其弃置1张牌。",
	["luajunying"] = "君影",	
	["Player_Start"] = "回合开始阶段",	
	["Player_Judge"] = "判定阶段",	
	["Player_Draw"] = "摸牌阶段",	
	["Player_Play"] = "出牌阶段",	--
	["Player_Discard"] = "弃牌阶段",	
	["@luajunying"] = "你可以发动“君影”",	
	["luajunying1"] = "交换牌",
	["luajunying2"] = "执行一个额外的任意阶段",
	[":luajunying"] = "锁定技，回合结束阶段，你须摸一张牌并执行一个额外的出牌阶段。",
	
	["nyasama"] = "喵玉娘",	
	["#nyasama"] = "喵玉大萌主",		
	["designer:nyasama"] = "Paysage",
	["lualianmeng"] = "联萌",
	["lualianmeng2"] = "联萌",
	["lualianmeng3"] = "联萌",
	["nonono"] = "这次不发动",
	["notever"] = "永不对该角色发动",
	["lianmeng"] = "萌",
	[":lualianmeng"] = "限定技，你可以展示一名角色的手牌，声明一个花色，将其手牌全部改为此花色。此后，一名角色与此花色相同的牌因弃置进入弃牌堆时，你可以令其回复一点体力或摸两张牌。",
	["luawanbang"] = "万邦",
	["luawanbangr"] = "万邦",
	[":luawanbangr"] = "你可以弃一张牌，令喵玉娘摸一张牌。",
	[":luawanbang"] = "主公技，所有其他角色于其出牌阶段限一次，其可以弃一张牌，令你摸一张牌。",
	["@LuaWanbang"] = "你可以发动“万邦”",
	["~LuaWanbang"] = "选择要摸牌的玩家→点击确定",	
	
	["alice"] = "爱丽丝",	
	["aliceA"] = "爱丽丝",
	["aliceB"] = "爱丽丝",
	["aliceC"] = "爱丽丝",
	["aliceD"] = "爱丽丝",
	["aliceE"] = "爱丽丝",
	["#alice"] = "七色的人偶师",
	["designer:alice"] = "Paysage",
	["illustrator:alice"] = "こぞう",
	["illustrator:alice_1"] = "さとうきび",
	["illustrator:alice_2"] = "きさらぎゆり",
	["illustrator:alice_3"] = "こぞう",
	["illustrator:alice_4"] = "夢中界",
	["illustrator:alice_5"] = "プーアカちゃん",
	["luajunzhen"] = "军阵",
	[":luajunzhen"] = "出牌阶段，你可以弃置一张装备牌，视为你对一名角色使用了一张任意基本牌（计入限制，不包括【秽】）。锁定技，分发起始手牌时，你将其中两张替换为【上海人形】【蓬莱人形】。每轮你首次失去装备区的牌时，你获得之。",
	["luaguixu"] = "归墟",
	["luaguixu9"] = "对一名角色造成X-2点伤害（X为该回合你失去装备区牌的张数）",
	[":luaguixu"] = "限定技，任意角色回合结束时，你可以对一名角色造成X-2点伤害（X为该回合你失去装备区牌的张数）。",

	["hatate"] = "姬海棠果",	
	["#hatate"] = "当代的念写记者",		
	["designer:hatate"] = "Paysage",
	["illustrator:hatate"] = "朱シオ",
	["lualianshe"] = "连摄",			
	["~lualianshe"] = "你可以发动“连摄”",	
	["@lualianshe"] = "选择你要保留的牌→点击确定",
	[":lualianshe"] = "准备阶段，你可以视为对一名角色使用了X+1张【杀】，然后弃置牌至只剩1张并减少1点体力上限（X为你满足下列项数：体力值/手牌数/装备数为全场最多且唯一）。",
	["luashenju"] = "深居",		
	["luashenjuq"] = "选择一项",		
	[":luashenju"] = "出牌阶段，你可以将一张牌当【兵粮寸断】或【乐不思蜀】对自己使用。然后你可以摸一张牌并使用一张摸牌堆中的非武器装备，或增加1点体力上限并回复1点体力。发动“深居”的回合你不能使用手牌中的【杀】。",

}
return {extension_pay_a}