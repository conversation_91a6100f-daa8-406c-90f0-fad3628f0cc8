# SP稀神探女 实现代码

![稀神探女](./sp_稀神探女.assets/9527f3f6362421e38483f61dbb4d5032-1752132057916-2.png)

## 武将设计概述

| 属性 | 值 |
|------|-----|
| **武将名称** | SP稀神探女 |
| **势力** | 神 |
| **体力值** | 3 |
| **性别** | 女 |
| **技能** | 诳言、天矢 |

## 技能描述

**诳言**：其他角色的出牌阶段开始时，你可以与其拼点。若你赢，其对你指定的一名角色依此使用拼点牌，将余下的拼点牌当【乐不思蜀】对自身使用。

**天矢**：拼点结算后，将弃牌堆顶的牌置于牌堆底。限定技：没有使用过牌的角色回合结束时，你可以令其依次对自身使用牌堆底的牌（无限制），直到不能使用为止。

## 完整实现代码

### 1. 扩展包文件 (extensions/sp_package.lua)

```lua
-- SP稀神探女扩展包
local sp_package = sgs.Package("sp_package")

-- 创建SP稀神探女武将
sp_uranomiya = sgs.General(sp_package, "sp_uranomiya", "god", 3, false)

-- 诳言技能卡
local kuangyan_card = sgs.CreateSkillCard{
    name = "kuangyan_card",
    target_fixed = false,
    will_throw = false,
    filter = function(self, targets, to_select)
        return #targets == 0 and to_select:objectName() ~= sgs.Self:objectName()
    end,
    on_use = function(self, room, source, targets)
        room:notifySkillInvoked(source, "kuangyan")
        local success = source:pindian(targets[1], "kuangyan", nil)
        if success then
            local target_card = targets[1]:getTag("KuangyanPindianCard"):toCard()
            if target_card and not target_card:isVirtualCard() then
                local target = room:askForPlayerChosen(source, room:getAlivePlayers(), "kuangyan", 
                    "@kuangyan-target:" .. targets[1]:objectName(), true)
                if target then
                    -- 使用拼点牌
                    local use = sgs.CardUseStruct()
                    use.from = targets[1]
                    use.to:append(target)
                    use.card = target_card
                    room:useCard(use)
                    
                    -- 将余下的拼点牌当乐不思蜀使用
                    local indulgence = sgs.Sanguosha:cloneCard("indulgence", target_card:getSuit(), target_card:getNumber())
                    indulgence:addSubcard(target_card:getId())
                    indulgence:setSkillName("kuangyan")
                    
                    use.to.clear()
                    use.to:append(targets[1])
                    use.card = indulgence
                    room:useCard(use)
                end
            end
        end
    end
}

-- 诳言技能
kuangyan = sgs.CreateZeroCardViewAsSkill{
    name = "kuangyan",
    view_as = function()
        return kuangyan_card:clone()
    end,
    enabled_at_play = function(self, player)
        return false
    end,
    enabled_at_response = function(self, player, pattern)
        return pattern == "@@kuangyan"
    end
}

-- 诳言触发技
kuangyan_trigger = sgs.CreateTriggerSkill{
    name = "#kuangyan_trigger",
    events = {sgs.EventPhaseStart},
    view_as_skill = kuangyan,
    can_trigger = function(self, target)
        return target and target:isAlive() and target:getPhase() == sgs.Player_Play
    end,
    on_trigger = function(self, event, player, data, room)
        for _, p in sgs.qlist(room:getOtherPlayers(player)) do
            if p:hasSkill("kuangyan") and p:canPindian(player) then
                room:askForUseCard(p, "@@kuangyan", "@kuangyan-pindian:" .. player:objectName())
            end
        end
        return false
    end,
    global = true
}

-- 天矢技能
tianshi = sgs.CreateTriggerSkill{
    name = "tianshi",
    events = {sgs.Pindian, sgs.EventPhaseEnd},
    frequency = sgs.Skill_Limited,
    limit_mark = "@tianshi",
    can_trigger = function(self, target)
        return target ~= nil
    end,
    on_trigger = function(self, event, player, data, room)
        if event == sgs.Pindian then
            -- 拼点结算后，将弃牌堆顶的牌置于牌堆底
            local pindian = data:toPindian()
            if pindian.from:hasSkill(self:objectName()) or pindian.to:hasSkill(self:objectName()) then
                if not room:getDiscardPile():isEmpty() then
                    local card_id = room:getDiscardPile():first()
                    room:moveCardTo(sgs.Sanguosha:getCard(card_id), nil, sgs.Player_DrawPile, false)
                end
            end
        elseif event == sgs.EventPhaseEnd and player:getPhase() == sgs.Player_Play then
            -- 限定技：没有使用过牌的角色回合结束时
            if player:getMark("damage_record_phase") == 0 and player:getMark("card_used_phase") == 0 then
                for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
                    if p:getMark("@tianshi") > 0 and room:askForSkillInvoke(p, self:objectName(), data) then
                        room:removePlayerMark(p, "@tianshi")
                        room:notifySkillInvoked(p, self:objectName())
                        
                        -- 令其依次对自身使用牌堆底的牌，直到不能使用为止
                        local can_use = true
                        while can_use and not room:getDrawPile():isEmpty() do
                            local card_id = room:getDrawPile():last()
                            local card = sgs.Sanguosha:getCard(card_id)
                            
                            -- 判断是否能使用
                            if not player:isCardLimited(card, card:getHandlingMethod()) then
                                local use = sgs.CardUseStruct()
                                use.from = player
                                use.to:append(player)
                                use.card = card
                                
                                if card:isKindOf("BasicCard") or card:isNDTrick() then
                                    if card:isAvailable(player) then
                                        room:useCard(use)
                                    else
                                        can_use = false
                                    end
                                else
                                    can_use = false
                                end
                            else
                                can_use = false
                            end
                        end
                    end
                end
            end
        end
        return false
    end,
    global = true
}

-- 添加技能到武将
sp_uranomiya:addSkill(kuangyan)
sp_uranomiya:addSkill(kuangyan_trigger)
sp_uranomiya:addSkill(tianshi)

-- 添加武将到扩展包
sp_package:addGeneral(sp_uranomiya)

-- 返回扩展包
return sp_package
```

### 2. 翻译文件 (lang/zh_CN/Package/SPPackage.lua)

```lua
-- SP稀神探女翻译
return {
    -- 武将翻译
    ["sp_package"] = "SP包",
    ["sp_uranomiya"] = "SP稀神探女",
    ["#sp_uranomiya"] = "天矢之巫女",
    ["designer:sp_uranomiya"] = "QSanguosha-v2开发组",
    ["illustrator:sp_uranomiya"] = "东方Project",
    ["cv:sp_uranomiya"] = "无",
    
    -- 技能翻译
    ["kuangyan"] = "诳言",
    [":kuangyan"] = "其他角色的出牌阶段开始时，你可以与其拼点。若你赢，其对你指定的一名角色依此使用拼点牌，将余下的拼点牌当【乐不思蜀】对自身使用。",
    ["@kuangyan-pindian"] = "你可以发动"诳言"与 %src 拼点",
    ["@kuangyan-target"] = "请选择 %src 拼点牌的目标",
    ["~kuangyan"] = "选择一名角色→点击确定",
    ["$kuangyan1"] = "哼哼，你上当了~",
    ["$kuangyan2"] = "我说的话，可不要全信哦~",
    
    ["tianshi"] = "天矢",
    [":tianshi"] = "拼点结算后，将弃牌堆顶的牌置于牌堆底。限定技：没有使用过牌的角色回合结束时，你可以令其依次对自身使用牌堆底的牌（无限制），直到不能使用为止。",
    ["@tianshi"] = "天矢",
    ["$tianshi1"] = "天之矢，射穿一切！",
    ["$tianshi2"] = "接招吧，这是命运的箭矢！",
    
    -- 死亡台词
    ["~sp_uranomiya"] = "我...我的箭矢...怎么会...",
}
```

### 3. AI文件 (lua/ai/sp_package-ai.lua)

```lua
-- SP稀神探女AI

-- 诳言AI
sgs.ai_skill_use["@@kuangyan"] = function(self, prompt, method)
    local target = self.player:getTag("KuangyanTarget"):toPlayer()
    if not target then return "." end
    
    -- 判断是否值得拼点
    if self:isFriend(target) then return "." end
    
    -- 如果手牌数量太少，不拼点
    if self.player:getHandcardNum() <= 2 then return "." end
    
    -- 如果对方手牌数量为0，必定赢，直接拼点
    if target:getHandcardNum() == 0 then
        return "@kuangyan_card"
    end
    
    -- 如果有大牌，考虑拼点
    local cards = self.player:getHandcards()
    local max_card = self:getMaxCard()
    if max_card and max_card:getNumber() >= 10 then
        return "@kuangyan_card"
    end
    
    return "."
end

-- 诳言目标选择AI
sgs.ai_skill_playerchosen.kuangyan = function(self, targets)
    local target = self.player:getTag("KuangyanTarget"):toPlayer()
    if not target then return nil end
    
    -- 选择最适合的目标让对方使用拼点牌
    local enemies = {}
    local friends = {}
    
    for _, p in sgs.qlist(targets) do
        if self:isEnemy(p) then
            table.insert(enemies, p)
        elseif self:isFriend(p) then
            table.insert(friends, p)
        end
    end
    
    -- 优先选择敌人
    if #enemies > 0 then
        self:sort(enemies, "threat")
        return enemies[1]
    end
    
    -- 没有敌人则选择自己
    return self.player
end

-- 天矢AI
sgs.ai_skill_invoke.tianshi = function(self, data)
    local target = data:toPlayer()
    
    -- 如果目标是敌人，直接发动
    if self:isEnemy(target) then
        return true
    end
    
    -- 如果目标是友方，考虑牌堆底的牌
    if self:isFriend(target) then
        -- 如果友方血量很低，不发动
        if target:getHp() == 1 then
            return false
        end
        
        -- 如果友方手牌充足，可以发动
        if target:getHandcardNum() >= 3 then
            return true
        end
    end
    
    -- 默认发动
    return true
end

-- 拼点相关AI
sgs.ai_skill_pindian.kuangyan = function(minusecard, self, requestor, maxcard)
    local cards = sgs.QList2Table(self.player:getHandcards())
    self:sortByKeepValue(cards)
    
    if self:isFriend(requestor) then
        return cards[1] -- 给出最小的牌
    else
        return self:getMaxCard() -- 给出最大的牌
    end
end

-- AI价值评估
sgs.ai_chaofeng.sp_uranomiya = 3 -- 中等仇恨值

-- 技能使用价值
sgs.ai_use_value.kuangyan = 4.5 -- 较高使用价值
sgs.ai_use_priority.kuangyan = 4.8 -- 较高使用优先级

-- 天矢标记保留
sgs.ai_keep_value.tianshi = 5 -- 高保留价值
```

## 使用说明

1. 将上述代码分别保存到对应文件中：
   - `extensions/sp_package.lua`
   - `lang/zh_CN/Package/SPPackage.lua`
   - `lua/ai/sp_package-ai.lua`

2. 在`lua/config.lua`中添加扩展包：
   ```lua
   package_names = {
       "StandardCard",
       "StandardGame",
       "Maneuvering",
       -- 其他扩展包...
       "sp_package", -- 添加SP包
   }
   ```

3. 准备武将图片资源：
   - `image/generals/card/sp_uranomiya.png` - 卡牌图
   - `image/generals/small/sp_uranomiya.png` - 小图
   - `image/generals/big/sp_uranomiya.png` - 大图
   - `image/generals/avatar/sp_uranomiya.png` - 头像

4. 重启游戏，即可使用SP稀神探女。

## 技能机制详解

### 诳言技能

1. **触发时机**：其他角色的出牌阶段开始时
2. **拼点机制**：与目标角色进行拼点
3. **胜利效果**：
   - 目标角色对你指定的一名角色使用拼点牌
   - 目标角色对自己使用拼点牌当【乐不思蜀】

### 天矢技能

1. **被动效果**：拼点结算后，将弃牌堆顶的牌置于牌堆底
2. **限定技效果**：
   - 触发条件：没有使用过牌的角色回合结束时
   - 效果：令其依次对自身使用牌堆底的牌，直到不能使用为止

## 战术建议

1. **诳言技能**：
   - 尽量在手牌有高点数牌时使用
   - 可以干扰敌方关键角色的出牌阶段
   - 选择合适的目标让敌方使用拼点牌，造成更大伤害

2. **天矢技能**：
   - 针对没有出过牌的敌方角色使用
   - 可以强制敌方使用对自己不利的牌
   - 配合其他控制技能，限制敌方行动

## 配合推荐

- **甄姬**：可以利用天矢技能触发甄姬的洛神
- **贾诩**：诳言可以配合贾诩的乱武，增加混乱效果
- **司马懿**：反馈技能可以配合诳言，增加控制效果
