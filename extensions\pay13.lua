module("extensions.pay13",package.seeall)

extension = sgs.Package("pay13")
paysage = sgs.General(extension,"paysage","lualing", 4,false, false,false)

sp_rin = sgs.General(extension,"sp_rin","luadi",3,false,false,false)
sp_rinA = sgs.General(extension,"sp_rinA","luadi",3,false,true,true)
sp_rinB = sgs.General(extension,"sp_rinB","luadi",3,false,true,true)
 
yamame = sgs.General(extension,"yamame","luadi",4,false,false,false)
cesdsdsdsd = sgs.General(extension,"cesdsdsdsd","luadi",3,false,true,true)
kanako = sgs.General(extension,"kanako$","luafeng",3,false,false,false)
minoriko = sgs.General(extension,"minoriko","luafeng",3,false,false,false)
sp_yuyuko = sgs.General(extension,"sp_yuyuko","luayao",1,false,true,true)

sumireko = sgs.General(extension,"sumireko","qun",3,false,false,false)
sumirekoA = sgs.General(extension,"sumirekoA","qun",3,false,true,true)
sumirekoB = sgs.General(extension,"sumirekoB","qun",3,false,true,true)
sumirekoC = sgs.General(extension,"sumirekoC","qun",3,false,true,true)
sumirekoD = sgs.General(extension,"sumirekoD","qun",3,false,true,true)

ran = sgs.General(extension,"ran","luayao",3,false,false,false)

keiki = sgs.General(extension,"keiki$","god",1,false,false,false)
keikiA = sgs.General(extension,"keikiA$","god",1,false,true,true)
keikiB = sgs.General(extension,"keikiB$","god",1,false,true,true)
keikiC = sgs.General(extension,"keikiC$","god",1,false,true,true)

toone = sgs.General(extension,"toone","luacai",3,false,false,false)
hecatia = sgs.General(extension,"hecatia$","god",3,false,true,false)
saki = sgs.General(extension,"saki","luadi",4,false,false,false)
luahongtu2 = sgs.CreateTargetModSkill{
	name = "#luahongtu",
	pattern = ".",
	distance_limit_func = function(self, player, card)
		if player:hasSkill("luahongtu") then
			if player:getMark("@luahongtu") == 1 then
				return 1000
			end
		else
			return 0
		end
	end
}
luahongtu3 = sgs.CreateTriggerSkill{
	name = "#luahongtu2",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.PreCardUsed} ,
	on_trigger = function(self, event, youmu, data)
		if event == sgs.PreCardUsed then
			local use = data:toCardUse()
			local card = use.card
			local room = youmu:getRoom()
			local function YouMuCheck(cardS, target)
				if cardS:isKindOf("Hui") or cardS:isKindOf("Ofuda") then
					return true
				elseif cardS:isKindOf("FaithCollection") then
					return not target:isNude() 		
				elseif card:isKindOf("Banquet") then
					return not target:containsTrick("banquet")
				end
			end
			if use.from:objectName() == youmu:objectName() and (use.card:isNDTrick() or use.card:isKindOf("BasicCard")) and use.from:hasSkill("luahongtu")
					and use.from:getMark("@luahongtu") == use.from:getHp() - 1 and use.from:getPhase() == sgs.Player_Play then
				if (sgs.Sanguosha:getCurrentCardUseReason() ~= sgs.CardUseStruct_CARD_USE_REASON_PLAY) then return false end
				local available_targets = sgs.SPlayerList()
				if (not use.card:isKindOf("AOE")) and (not use.card:isKindOf("GlobalEffect")) then
					for _, p in sgs.qlist(room:getAlivePlayers()) do
						if (use.to:contains(p) or room:isProhibited(youmu, p, use.card)) then continue end
						if (use.card:targetFixed()) then
							if (not use.card:isKindOf("Peach")) or (p:isWounded()) then
								available_targets:append(p)
							end
						else
							if (use.card:targetFilter(sgs.PlayerList(), p, youmu) or YouMuCheck(use.card, p)) then
								available_targets:append(p)
							end
						end
					end
				end
				local extra = nil
				if not use.card:isKindOf("Collateral") then
					local Carddata2 = sgs.QVariant() -- ai用
					Carddata2:setValue(use.card)
					room:setTag("luajianjiTC", Carddata2)

					extra = room:askForPlayerChosen(youmu, available_targets, "luahongtu", "luahongtu", true, true)
					room:removeTag("luajianjiTC")
					if extra then
						use.to:append(extra)
					end
				end
				room:sortByActionOrder(use.to)
				data:setValue(use)
				return false
			end
		end
		return false
	end
}
luahongtu = sgs.CreateTriggerSkill{
	name = "luahongtu",
	events = {sgs.TargetConfirmed, sgs.TrickCardCanceling, sgs.EventPhaseChanging, sgs.CardUsed},
	global = true,
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardUsed then 
			local use = data:toCardUse()
			local card = data:toCardUse().card
			if use.from:objectName() == player:objectName() and use.from:hasSkill("luahongtu") then
				if card:isKindOf("SkillCard") then return false end
				player:gainMark("@luahongtu")
				if player:getMark("@luahongtu") == player:getHp() + 1 then
					player:drawCards(2)
				end
			end 
		elseif event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			if use.from and player:objectName() == use.from:objectName() and use.card:isKindOf("Slash") and player:hasSkill("luahongtu")
				and player:getMark("@luahongtu") == 1 then
				for _, t in sgs.qlist(use.to) do
					local jink_table = sgs.QList2Table(player:getTag("Jink_" .. use.card:toString()):toIntList())
					local index = 1
					for _, p in sgs.qlist(use.to) do
						jink_table[index] = 0
						index = index + 1
					end
					local jink_data = sgs.QVariant()
					jink_data:setValue(Table2IntList(jink_table))
					player:setTag("Jink_" .. use.card:toString(), jink_data)
				end
			end
		elseif event == sgs.TrickCardCanceling then
			local effect = data:toCardEffect()
			if effect.from and effect.from:hasSkill(self:objectName()) and effect.from:isAlive() and effect.from:getMark("@luahongtu") == 1
					and effect.to then
				return true
			end
		elseif event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive and player:objectName() == room:getCurrent():objectName() and player:hasSkill("luahongtu") then
				player:loseAllMarks("@luahongtu")
			elseif change.to == sgs.Player_Start and player:objectName() == room:getCurrent():objectName() and player:hasSkill("luahongtu") then
				player:loseAllMarks("@luahongtu")
			end
		end
	end,
	can_trigger = function(self, target)
		return target and target:isAlive()
	end
}
paysage:addSkill(luahongtu3)
paysage:addSkill(luahongtu2)
paysage:addSkill(luahongtu)
luamaobu = sgs.CreateTriggerSkill{
	name = "luamaobu",
	frequency = sgs.Skill_Compulsory, 
	events = sgs.DamageInflicted,
	
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()		
		if player:hasSkill("luamaobu") and damage.from and damage.from:isAlive() and not damage.from:isChained() 
			and damage.to and damage.to:objectName() == player:objectName() then
			if not damage.from:isChained() then 
				room:setPlayerProperty(damage.from, "chained", sgs.QVariant(true))
				room:setEmotion(damage.from, "chain")
			else
				room:setPlayerProperty(damage.from, "chained", sgs.QVariant(false))
				room:setEmotion(damage.from, "chain")
			end			

			if not damage.to:isChained() then 
				room:setPlayerProperty(damage.to, "chained", sgs.QVariant(true))
				room:setEmotion(damage.to, "chain")
			else
				room:setPlayerProperty(damage.to, "chained", sgs.QVariant(false))
				room:setEmotion(damage.to, "chain")
			end			
			
			return true
		end
		return false
	end,
}

luaguihuo = sgs.CreateTriggerSkill{
	name = "luaguihuo",
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if not p:hasSkill("luaguihuor") then room:writeToConsole("jiantin test" .. p:objectName()); room:attachSkillToPlayer(p, "luaguihuor") end
				end
			end
		end
	end
}



luaguihuoCard = sgs.CreateSkillCard{
	name = "luaguihuor", 
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select)
		if to_select:objectName() == sgs.Self:objectName() then return false end
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
		slash:addSubcard(self:getSubcards():first())
		slash:setSkillName("luaguihuor")
		slash:deleteLater()
		if #targets >= 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, sgs.Self, slash) then return false end
		return sgs.Self:inMyAttackRange(to_select)
	end,
	on_use = function(self, room, source, targets)
		local targetsX = targets
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		
		local plist = sgs.SPlayerList()
		
		plist:append(source)
		
		local sp_rin = room:findPlayerBySkillName("luaguihuo")
		local sp_rins = sgs.SPlayerList()
		for _, sp_rinX in sgs.qlist(room:findPlayersBySkillName("luaguihuo")) do 
			sp_rins:append(sp_rinX)
		end 
		if (not sp_rin) or (not sp_rin:isAlive()) then return end
		sp_rin = room:askForPlayerChosen(source, sp_rins, self:objectName(), "luaguihuorw", false, false)
		plist:append(sp_rin)
		local to = room:askForPlayerChosen(sp_rin, plist, "luaguihuor", "luaguihuor-invoke", false, true)
		if to then 
			to:drawCards(1)
		end
		local slash = sgs.Sanguosha:cloneCard("fire_slash", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		slash:addSubcard(card:getEffectiveId())
		local targetY = sgs.SPlayerList()
		for _, target in ipairs(targetsX) do
			if source:canSlash(target, slash, true) then
				targetY:append(target)
			end
		end
		if not targetY:isEmpty() then
			room:useCard(sgs.CardUseStruct(slash, source, targetY))
		end
	end,
}


luaguihuor = sgs.CreateOneCardViewAsSkill{
	name = "luaguihuor&",
	view_filter = function(self, card)
		-- if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
    		-- local slash = sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_SuitToBeDecided, -1)
        	-- slash:addSubcard(card:getEffectiveId())
        	-- slash:deleteLater()
        	-- return slash:isAvailable(sgs.Self) and 
    	-- end
    	return card:getSuit() == sgs.Card_Spade 
	end,
	view_as = function(self,card)
		local skillcard = luaguihuoCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self, player)
		for _, p in sgs.qlist(player:getAliveSiblings()) do
			if p:hasSkill("luaguihuo") then
				return not player:hasUsed("#luaguihuor")
			end
		end
		if (player:hasSkill("luaguihuo") and not player:hasUsed("#luaguihuor")) then return true end 
		return false
	end,
}

sp_rin:addSkill(luamaobu)
sp_rin:addSkill(luaguihuo)

sp_rinA:addSkill(luamaobu)
sp_rinA:addSkill(luaguihuo)

sp_rinB:addSkill(luamaobu)
sp_rinB:addSkill(luaguihuo)
cesdsdsdsd:addSkill(luaguihuor)
 

luazhulou3 = sgs.CreateTriggerSkill{
	name = "#luazhulou2",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.ConfirmDamage},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()		
		if event == sgs.ConfirmDamage then
            local damage = data:toDamage()
			if damage.to and (damage.to:getMark("luazhulou") > 0) and damage.nature ~= sgs.DamageStruct_Normal then
				damage.damage = damage.damage + damage.to:getMark("luazhulou") 
				room:notifySkillInvoked(damage.to, "luazhulou")
				room:setPlayerMark(damage.to, "luazhulou", 0)	
				data:setValue(damage)
				return false
			end 
		end 
	end 
}

zhulouCard = sgs.CreateSkillCard{
	name = "luazhulou",
	target_fixed = false,
	filter = function(self, targets, to_select)
		return #targets < 1
	end,
	on_effect = function(self, effect)
		local target = effect.to
		local room = effect.from:getRoom()
		local x = effect.from:usedTimes("#luazhulou") - 1
		if not target:isChained() then 
			room:setPlayerProperty(target, "chained", sgs.QVariant(true))
			room:setEmotion(target, "chain")
		else
			room:setPlayerProperty(target, "chained", sgs.QVariant(false))
			room:setEmotion(target, "chain")
		end					
		if x == 0 then 
			local p = room:askForPlayerChosen(effect.from, room:getAlivePlayers(), "luazhulou1", "luazhuloua", false, true)
			if p then 
				if p:isWounded() then 
					room:recover(p, sgs.RecoverStruct(source))
				end 
				p:addMark("luazhulou")
			end 
		elseif x == 1 then 
			local p = room:askForPlayerChosen(effect.from, room:getAlivePlayers(), "luazhulou2", "luazhuloub", false, true)
			if p then 
				p:drawCards(1)
			end 
		elseif x == 2 then 
			local p = room:askForPlayerChosen(effect.from, room:getAlivePlayers(), "luazhulou3", "luazhulouc", false, true)
			if p then 
				local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				for _, card in sgs.list(p:getHandcards()) do
					if card:getSuit() == sgs.Card_Diamond then dummy:addSubcard(card:getEffectiveId()) end 
				end 
				for _, card in sgs.list(p:getEquips()) do
					if card:getSuit() == sgs.Card_Diamond then dummy:addSubcard(card:getEffectiveId()) end 
				end 			
				room:throwCard(dummy, p, effect.from)	
			end 				
		end 		
	end 
}

luazhulou = sgs.CreateOneCardViewAsSkill{
	name = "luazhulou",
	filter_pattern = "Slash",
	view_as = function(self,card)
		local skillcard = zhulouCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self,player)
		return true
	end,
}



luarebin = sgs.CreateTriggerSkill{
	name = "luarebin", 
	events = {sgs.CardsMoveOneTime, sgs.HpChanged}, 
	on_trigger = function(self, event, player, data, room)
		if event == sgs.HpChanged then 
			if player:getHandcardNum() == player:getHp() then 
				room:acquireSkill(player, "luarebin2")
			else
				room:detachSkillFromPlayer(player, "luarebin2")
			end 		
			return false
		else
			local move = data:toMoveOneTime()
			if move.to and move.to:objectName() == player:objectName() and move.to_place == sgs.Player_PlaceHand and not move.card_ids:isEmpty() then
				if player:getHandcardNum() == player:getHp() then 
					room:acquireSkill(player, "luarebin2")
				else
					room:detachSkillFromPlayer(player, "luarebin2")
				end 
			elseif move.from and move.from:objectName() == player:objectName() and move.from_places:contains(sgs.Player_PlaceHand) and not move.card_ids:isEmpty() then 
				if player:getHandcardNum() == player:getHp() then 
					room:acquireSkill(player, "luarebin2")
				else
					room:detachSkillFromPlayer(player, "luarebin2")
				end 			
			end
			return false
		end 
		
	end
}

luarebin2 = sgs.CreateFilterSkill{
	name = "luarebin2",	
	view_filter = function(self,to_select)
		local room = sgs.Sanguosha:currentRoom()
		local place = room:getCardPlace(to_select:getEffectiveId())
		if (place == sgs.Player_PlaceHand) then return true end
	end,	
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("fire_slash", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		local _card = sgs.Sanguosha:getWrappedCard(card:getId())
		_card:takeOver(slash)
		return _card
	end
}

yamame:addSkill(luazhulou)
yamame:addSkill(luazhulou3)
yamame:addSkill(luarebin)
cesdsdsdsd:addSkill(luarebin2)

luaxinyang2 = sgs.CreateTriggerSkill{
	name = "#luaxinyang2",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageForseen},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()		
		if event == sgs.DamageForseen then
            local damage = data:toDamage()
			if damage.to and (damage.to:getMark("luaxinyang") > 0) then
				damage.damage = damage.damage - 1
				room:setPlayerMark(damage.to, "luaxinyang", 0)	
				data:setValue(damage)
				return false
			end 
		end 
	end 
}

luaxinyang = sgs.CreateTriggerSkill{
	name = "luaxinyang" ,
	global = true,
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local change = data:toPhaseChange()
		if change.to == sgs.Player_NotActive then
			for _, kanako in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				local _data = sgs.QVariant()
				_data:setValue(kanako)
				if not player:isKongcheng() and room:askForSkillInvoke(player, "luaxinyang", _data) then
					local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
					dummy:addSubcards(player:getHandcards())
					room:obtainCard(kanako, dummy, false)
					player:addMark("luaxinyang")
				end 
			end 
		end
	end 
}

function Ru2(str)
	if string.find(str, "analeptic") or string.find(str, "peach") then return "peach" end 
	if string.find(str, "slash") then return "slash" end 
	if string.find(str, "jink") then return "jink" end 
end 

shendeCard = sgs.CreateSkillCard{
	name = "luashende",
	will_throw = false,
	filter = function(self, targets, to_select)
		local name = ""
		local card
		local plist = sgs.PlayerList()
		for i = 1, #targets do plist:append(targets[i]) end
		local aocaistring = self:getUserString()
		if aocaistring ~= "" then
			local uses = aocaistring:split("+")
			name = uses[1]
			card = sgs.Sanguosha:cloneCard(name)
		end
		return card and card:targetFilter(plist, to_select, sgs.Self) and not sgs.Self:isProhibited(to_select, card, plist)
	end ,	
	feasible = function(self, targets)
		local name = ""
		local card
		local plist = sgs.PlayerList()
		for i = 1, #targets do plist:append(targets[i]) end
		local aocaistring = self:getUserString()
		if aocaistring ~= "" then
			local uses = aocaistring:split("+")
			name = uses[1]
			card = sgs.Sanguosha:cloneCard(name)
		end
		return card and card:targetsFeasible(plist, sgs.Self)
	end,
	on_validate_in_response = function(self, user)
		local room = user:getRoom()
		local x = user:getHandcardNum() - user:getMaxCards()
		if x <= 0 then return end 
		local aocaistring = self:getUserString()
		local names = aocaistring:split("+")
		if table.contains(names, "slash") then
			table.insert(names,"fire_slash")
			table.insert(names,"thunder_slash")
		end
		
		local pattern = aocaistring
		if not room:askForDiscard(user, self:objectName(), x, x, false) then return end
		
		local choice = room:askForChoice(user, "luashende", table.concat(names, "+"))
		local card = sgs.Sanguosha:cloneCard(choice, sgs.Card_NoSuit, 0)
		card:setSkillName("luashende")
		return card
	end,
	on_validate = function(self, cardUse)
		cardUse.m_isOwnerUse = false

		local user = cardUse.from
		local room = user:getRoom()
		local x = user:getHandcardNum() - user:getMaxCards()
		if x <= 0 then return end 
		local aocaistring = self:getUserString()
		local names = aocaistring:split("+")
		if table.contains(names, "slash") then
			table.insert(names,"fire_slash")
			table.insert(names,"thunder_slash")
		end
		
		local pattern = aocaistring
		if not room:askForDiscard(user, self:objectName(), x, x, false) then return end
		local choice = room:askForChoice(user, "luashende", table.concat(names, "+"))
		local card = sgs.Sanguosha:cloneCard(choice, sgs.Card_NoSuit, 0)	
		card:setSkillName("luashende")
		return card
	end
}

shendeVS = sgs.CreateViewAsSkill{
	name = "luashende",
	n = 0,
	enabled_at_play = function()
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		if player:getPhase() == sgs.Player_Play then return end
		if player:getHandcardNum() <= player:getMaxCards() then return end 
		if pattern == "slash" then
			return sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE
		elseif (pattern == "peach") then
				 return not player:hasFlag("Global_PreventPeach")
		elseif string.find(pattern, "analeptic") or string.find(pattern, "nullification") then
			return true
		end
		return false
	end,
	enabled_at_nullification = function(self, player)
		if player:getPhase() == sgs.Player_Play then return false end
		if player:getHandcardNum() <= player:getMaxCards() then return false end 
		return true
	end, 
	view_as = function(self, cards)
		local acard = shendeCard:clone()
		local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
		if pattern == "peach+analeptic" and sgs.Self:hasFlag("Global_PreventPeach") then
			pattern = "analeptic"
		end
		acard:setUserString(pattern)
			return acard
	end,
}

luashende = sgs.CreateTriggerSkill{
	name = "luashende",
	view_as_skill = shendeVS,
	events = {sgs.CardAsked},
	on_trigger = function(self, event, player, data)
		if event == sgs.CardAsked then
			if player:getPhase() == sgs.Player_Play then return false end
			local room = player:getRoom()
			local pattern = data:toStringList()[1]
			if (pattern == "slash" or pattern == "jink") then
				local x = player:getHandcardNum() - player:getMaxCards()
				local _data = sgs.QVariant(pattern)
				if x > 0 and room:askForSkillInvoke(player, self:objectName(), _data) then
					
					room:askForDiscard(player, self:objectName(), x, x)
					local dummy = sgs.Sanguosha:cloneCard(pattern, sgs.Card_NoSuit, 0)
					room:provide(dummy)
					return true				
				end 

			end 			
		end 
	end,
	can_trigger = function(self, target)
		return target and target:hasSkill(self:objectName()) and target:getHandcardNum() > target:getMaxCards()
	end 
}

luashenji = sgs.CreateTriggerSkill{
	name = "luashenji$" ,
	global = true,
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local change = data:toPhaseChange()
		if change.to == sgs.Player_Start then
			for _, kanako in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do --.
				local _data = sgs.QVariant()
				_data:setValue(kanako)				
				if player:hasLordSkill(self:objectName()) and room:askForSkillInvoke(player, self:objectName(), _data) then kanako:drawCards(1) end 
			end 
		end 
	end, 
	can_trigger = function(self, target)
		return target and target:getHandcardNum() > target:getHp()
	end 	
}
kanako:addSkill(luashende)
kanako:addSkill(luaxinyang)
kanako:addSkill(luashenji)
kanako:addSkill(luaxinyang2)

luahongyu2 = sgs.CreateTriggerSkill{
	name = "#luahongyu",
	events = {sgs.EventPhaseStart, sgs.DrawNCards, sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart then
			if player:getPhase() == sgs.Player_Draw then
				local Eternitys = sgs.SPlayerList()
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if not p:hasSkill("luaouxiang") then
						Eternitys:append(p)
					end
				end
				local to = room:askForPlayerChosen(player, Eternitys, "luahongyu", "luahongyu", true, true)
				if to and to:getHandcardNum() <= to:getMaxCards() then
					to:drawCards(to:getMaxCards() - to:getHandcardNum() + 1, self:objectName())
					room:setPlayerFlag(player, "luahongyu")
					return false
				end
			end
		elseif event == sgs.DrawNCards then
			if player:hasFlag("luahongyu") then
				local count = data:toInt() - 1
				data:setValue(count)
			end
		elseif event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				local toziko = player:getRoom():findPlayerBySkillName("luahongyu")
				if toziko and toziko:hasFlag("luahongyu") then
					room:setPlayerFlag(toziko, "-luahongyu")
					--room:writeToConsole("回合结束")
				end
			end
		end
		return false
	end
}

function payRIGHT2(self, player)
	if player and player:isAlive() and player:hasSkill(self:objectName()) then return true else return false end
end

luahongyu = sgs.CreateMaxCardsSkill{
	name = "luahongyu" ,
	fixed_func = function(self, target)
    	if target:hasSkill("luahongyu") and target:hasFlag("luahongyu") then 
			return 1
		end 	
		return -1
	end
}
luafengshou_list = {}
luafengshou = sgs.CreateTriggerSkill{
	name = "luafengshou",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.CardsMoveOneTime, sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.CardsMoveOneTime then
			luafengshou_list = {}
			local move = data:toMoveOneTime()
			if not move.from then return false end
			for _,id in sgs.qlist(move.card_ids) do
				if bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) == sgs.CardMoveReason_S_REASON_DISCARD then
					table.insert(luafengshou_list, id)
				end
			end
		elseif event == sgs.EventPhaseEnd and player:getPhase() == sgs.Player_Discard then
			local ids_A = sgs.IntList()
			local jiyi = luafengshou_list
			local fengshou_l = {}
			for _,id in pairs(luafengshou_list) do
				if not sgs.Sanguosha:getCard(id):isKindOf("BasicCard") then
					return false
				end
				table.insert(fengshou_l, tostring(id))
				if room:getCardPlace(id) == sgs.Player_DiscardPile then ids_A:append(id) end 
			end
			
			
			local players = room:getAlivePlayers()
			if #luafengshou_list > 0 and #luafengshou_list <= players:length() then

				room:setTag("lfengshou", sgs.QVariant(table.concat(fengshou_l,"|")))
				local ij = 1
				while not players:isEmpty() do
					if ij <= 2 then
						room:setPlayerFlag(player, "fengshouA")
					else
						room:setPlayerFlag(player, "-fengshouA")
					end
					local target = room:askForPlayerChosen(player, players, self:objectName(), "luafengshou2", false, true)
					players:removeOne(target)
					if target then
						room:fillAG(ids_A)
						room:setPlayerFlag(target, "fengshouT")
						local card_id = room:askForAG(player, ids_A, false, self:objectName())
						ids_A:removeOne(card_id)
						local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
						dummy:addSubcard(card_id)
						room:obtainCard(target, dummy, false)
						room:clearAG()
						room:setPlayerFlag(target, "-fengshouT")
						if ids_A:isEmpty() then break end 
					else
						break 
					end
					ij = ij + 1
				end
				room:removeTag("lfengshou")
				local canRe = true
				for _,id in pairs(jiyi) do
					if not sgs.Sanguosha:getCard(id):isRed() then
						canRe = false
					end

				end
				if canRe and #jiyi > 0 then room:recover(player, sgs.RecoverStruct(player)) end
			end
		end
		return false
	end
}

minoriko:addSkill(luahongyu2)
minoriko:addSkill(luahongyu)
minoriko:addSkill(luafengshou)


luachaogancard = sgs.CreateSkillCard{
	name = "luachaogan" ,
	will_throw = true ,
	target_fixed = true ,
	on_use = function(self, room, source, targets)
		local x = source:getMark("luachaogan")
		if x <= 2 then
			source:drawCards(x)
		else
			local card_ids = room:getNCards(x)
			local obtained = sgs.IntList()
			room:fillAG(card_ids,source)
			local id1 = room:askForAG(source,card_ids,false,self:objectName())
			card_ids:removeOne(id1)
			obtained:append(id1)
			room:takeAG(source,id1,false)
			local id2 = room:askForAG(source,card_ids,false,self:objectName())
			card_ids:removeOne(id2)
			obtained:append(id2)
			room:clearAG(source)
			room:askForGuanxing(source,card_ids,sgs.Room_GuanxingUpOnly)
			local dummy = sgs.Sanguosha:cloneCard("jink",sgs.Card_NoSuit,0)
			for _,id in sgs.qlist(obtained) do
				dummy:addSubcard(id)
			end
			source:obtainCard(dummy,false)
		end

	end
}

luachaogan = sgs.CreateZeroCardViewAsSkill{
	name = "luachaogan",
	view_as = function()
		return luachaogancard:clone()
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#luachaogan")
	end
}

luachaogan2 = sgs.CreateTriggerSkill{
	name = "#luachaogan",
	events = {sgs.TargetConfirmed, sgs.EventPhaseChanging} ,
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			if not use.card then return false end
			if not use.from then return false end
			if (player:objectName() ~= use.from:objectName()) or (not use.to) or (use.card:getTypeId() == sgs.Card_TypeEquip) then return false end
			if use.to:length() ~= 1 then return false end
			if not player:hasSkill("luachaogan") then return false end
			player:addMark("luachaogan")
		else
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				if player:getMark("luachaogan") > 0 then
					room:setPlayerMark(player, "luachaogan", 0)
				end
			end
		end
	end
}

luanianli2 = sgs.CreateTriggerSkill{
	name = "#luanianli",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damaged},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.Damaged then
			local damage = data:toDamage()
			if damage.damage == 0 then return false end
			if player:objectName() == damage.to:objectName() and player:hasSkill("luanianli") then
				for i = 1, damage.damage, 1 do
					local card_id = room:drawCard()
					player:addToPile("luanianli", card_id)
				end
			end
		end
	end
}

luanianliCard = sgs.CreateSkillCard{
	name = "luanianli",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select)
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE then return true end
		if to_select:objectName() == sgs.Self:objectName() then return false end
		if #targets ~= 0 then return false end
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		local duel = sgs.Sanguosha:cloneCard("dismantlement", card:getSuit(), card:getNumber())
		local can_duel = true
		if sgs.Self:isProhibited(to_select, duel, to_select:getSiblings()) then can_duel = false end
		if to_select:isNude() then can_duel = false end
		local function TSATKR(to_selectF)
			if sgs.Self:getWeapon() and self:getSubcards():contains(sgs.Self:getWeapon():getId()) then
				local weapon = sgs.Self:getWeapon():getRealCard():toWeapon()
				local distance_fix = weapon:getRange() - 1
				if sgs.Self:getOffensiveHorse() and self:getSubcards():contains(sgs.Self:getOffensiveHorse():getId()) then
					distance_fix = distance_fix + 1
				end
				return sgs.Self:distanceTo(to_selectF, distance_fix) <= sgs.Self:getAttackRange()
			elseif sgs.Self:getOffensiveHorse() and self:getSubcards():contains(sgs.Self:getOffensiveHorse():getId()) then
				return sgs.Self:distanceTo(to_selectF, 1) <= sgs.Self:getAttackRange()
			end
			return sgs.Self:inMyAttackRange(to_selectF)
		end
		return TSATKR(to_select) or can_duel
	end,
	on_validate = function(self, carduse)
		local source = carduse.from
		local target = carduse.to:first()
		local room = source:getRoom()
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		local slash_va = true
		local duel_va = true 
		if target:isNude() then duel_va = false end
		local function TSATKR(to_select)
			if source:getWeapon() and self:getSubcards():contains(source:getWeapon():getId()) then
				local weapon = source:getWeapon():getRealCard():toWeapon()
				local distance_fix = weapon:getRange() - 1
				if source:getOffensiveHorse() and self:getSubcards():contains(source:getOffensiveHorse():getId()) then
					distance_fix = distance_fix + 1
				end
				return source:distanceTo(to_select, distance_fix) <= source:getAttackRange()
			elseif source:getOffensiveHorse() and self:getSubcards():contains(source:getOffensiveHorse():getId()) then
				return source:distanceTo(to_select, 1) <= source:getAttackRange()
			end
			return source:inMyAttackRange(to_select)
		end
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
			slash:addSubcard(card:getEffectiveId())
			slash:deleteLater()
			if (not slash:isAvailable(source)) or (not source:canSlash(target, nil, false) or (not TSATKR(target))) then slash_va = false end
		end
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			local duel = sgs.Sanguosha:cloneCard("dismantlement", card:getSuit(), card:getNumber())
			duel:addSubcard(card:getEffectiveId())
			duel:deleteLater()
			if (not duel:isAvailable(source)) or source:isProhibited(target, duel) then duel_va = false end
		end
		if (not slash_va) and duel_va then
			local duel = sgs.Sanguosha:cloneCard("dismantlement", card:getSuit(), card:getNumber())
			duel:setSkillName(self:objectName())
			duel:addSubcard(card:getEffectiveId())
			return duel
		elseif (not duel_va) and slash_va then
			local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
			slash:setSkillName(self:objectName())
			slash:addSubcard(card:getEffectiveId())
			return slash
		elseif duel_va and slash_va then
			local pdata ,cdata= sgs.QVariant() ,sgs.QVariant()
			pdata:setValue(target)
			cdata:setValue(card)
			room:setTag("LuaJiaosi_user",pdata)
			room:setTag("LuaJiaosi_card",cdata)
			local choice = room:askForChoice(source, "LuaJiaosi", "slash+dismantlement")

			if choice == "slash" then
				local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
				slash:setSkillName(self:objectName())
				slash:addSubcard(card:getEffectiveId())
				return slash
			else
				local duel = sgs.Sanguosha:cloneCard("dismantlement", card:getSuit(), card:getNumber())
				duel:setSkillName(self:objectName())
				duel:addSubcard(card:getEffectiveId())
				return duel
			end
			room:removeTag("LuaJiaosi_user")
			room:removeTag("LuaJiaosi_card")
		end
	end,
	on_validate_in_response = function(self, tenshi)
		local room = tenshi:getRoom()
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		slash:addSubcard(card:getEffectiveId())
		return slash
	end,
}
luanianli = sgs.CreateViewAsSkill{
	name = "luanianli",
	n = 1,
	expand_pile = "luanianli",
	view_filter = function(self, selected, to_select)
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
			slash:addSubcard(to_select:getEffectiveId())
			slash:deleteLater()
			if not slash:isAvailable(sgs.Self) then
				if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
					local duel = sgs.Sanguosha:cloneCard("dismantlement", sgs.Card_SuitToBeDecided, -1)
					duel:addSubcard(to_select:getEffectiveId())
					duel:deleteLater()
					if not duel:isAvailable(sgs.Self) then return false end
				end
			end
		end
		for _, cardX in sgs.list(sgs.Self:getHandcards()) do
			if cardX:getEffectiveId() == to_select:getEffectiveId() then return false end 
		end 
		return (#selected == 0) and (not sgs.Self:isJilei(to_select)) and not to_select:isEquipped()
	end ,
	view_as = function(self, cards)
		if #cards ~= 1 then return nil end
		local card = luanianliCard:clone()
		card:addSubcard(cards[1])
		return card
	end ,
	enabled_at_play = function(self, player)
		return player:getPile("luanianli"):length() > 0
	end
}

sumireko:addSkill(luanianli)
sumireko:addSkill(luanianli2)
sumireko:addSkill(luachaogan2)
sumireko:addSkill(luachaogan)

sumirekoA:addSkill(luanianli)
sumirekoA:addSkill(luanianli2)
sumirekoA:addSkill(luachaogan2)
sumirekoA:addSkill(luachaogan)

sumirekoB:addSkill(luanianli)
sumirekoB:addSkill(luanianli2)
sumirekoB:addSkill(luachaogan2)
sumirekoB:addSkill(luachaogan)

sumirekoC:addSkill(luanianli)
sumirekoC:addSkill(luanianli2)
sumirekoC:addSkill(luachaogan2)
sumirekoC:addSkill(luachaogan)

sumirekoD:addSkill(luanianli)
sumirekoD:addSkill(luanianli2)
sumirekoD:addSkill(luachaogan2)
sumirekoD:addSkill(luachaogan)
--card = room_0:askForUseCard(Akyuu, sgs.Sanguosha:getCard(id):toString(), "@luaqiuwen", -1, sgs.Card_MethodUse, true)


luatianyan = sgs.CreateTriggerSkill{
	name = "luatianyan",
	global = true,
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Damaged, sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data)
        local room = player:getRoom()
		if event == sgs.Damaged then
			local damage = data:toDamage()
			if damage.damage == 0 then return false end
			local victim = damage.to
			if not victim:isDead() then
				local list = room:findPlayersBySkillName("luatianyan") 
				for _,p in sgs.qlist(list) do
					room:setPlayerFlag(victim, "luatianyan")
				end
			end
		else
			if room:getCurrent():getPhase() == sgs.Player_Finish then 
				local plist = sgs.SPlayerList()
				for _,p in sgs.qlist(room:getAlivePlayers()) do
					if p:hasFlag("luatianyan") then
						plist:append(p)
					end
				end
				for _,p in sgs.qlist(plist) do
					room:setPlayerFlag(p, "-luatianyan")
				end
				if plist:isEmpty() then return end
				local ran = room:findPlayerBySkillName("luatianyan")
				if ran:isKongcheng() then return end
				local tp = room:askForPlayerChosen(ran, plist, "luatianyan", "@luatianyan", true, true)
				if tp then
					local _data = sgs.QVariant()
					_data:setValue(tp)
					room:setTag("luatianyanTP", _data)
					local card = room:askForCard(ran, ".|.|.|hand!", "@luatianyan2", data)
					room:removeTag("luatianyanTP")
					if card then
						tp:obtainCard(card)
						if room:getCardOwner(card:getEffectiveId()):objectName() == tp:objectName() then
							if card:isBlack() then
								local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
								slash:addSubcard(card)
								if tp:isCardLimited(slash, sgs.Card_MethodUse) then return false end
								local targets_list = sgs.SPlayerList()
								for  _, target in sgs.qlist(room:getAlivePlayers()) do
									if tp:canSlash(target, slash, false) and tp:inMyAttackRange(target) then
										targets_list:append(target)
									end
								end
								local targets = sgs.SPlayerList()
								local x = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, tp, slash)
								while x > 0 do
									local Carddata2 = sgs.QVariant() -- ai用
									Carddata2:setValue(slash)
									room:setTag("dangzuoshaTC", Carddata2)
									local target = room:askForPlayerChosen(tp, targets_list, "dangzuosha2", "@dangzuosha2", true)
									if not target then break end
									room:removeTag("dangzuoshaTC")
									targets:append(target)
									targets_list:removeOne(target)
									if targets_list:length() == 0 then break end
									x = x - 1
								end
								if targets:length() > 0 then
									room:useCard(sgs.CardUseStruct(slash, tp, targets))
								end
							else
								if tp:isWounded() then
									room:writeToConsole("ran test 2")
									local peach = sgs.Sanguosha:cloneCard("peach", sgs.Card_SuitToBeDecided, -1)
									peach:addSubcard(card)
									if tp:isCardLimited(peach, sgs.Card_MethodUse) then return false end
									peach:setSkillName(self:objectName())
									room:useCard(sgs.CardUseStruct(peach, tp, sgs.SPlayerList()))
								end
							end
						end
					end
				end
			end 
		end
	end,
	can_trigger = function(self, target)
		return target ~= nil
	end
}


tianhuCard = sgs.CreateSkillCard{
	name = "luatianhu",
	filter = function(self, targets, to_select)
		return (#targets == 0)
	end,
	on_effect = function(self, effect)
		effect.to:drawCards(1)
		local room = effect.to:getRoom()
		if effect.to:getHandcardNum() == 1 then
			room:addPlayerHistory(effect.from, "#luatianhu", 0)
			effect.from:drawCards(1)
		end
	end
}
luatianhu = sgs.CreateZeroCardViewAsSkill{
	name = "luatianhu",
	view_as = function(self, cards)
		return tianhuCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:hasSkill("luatianhu") and not player:hasUsed("#luatianhu")
	end,
}


ran:addSkill(luatianyan)
ran:addSkill(luatianhu)
luawangxiang = sgs.CreateTriggerSkill{
	name = "luawangxiang",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local phase = player:getPhase()
		if phase == sgs.Player_Finish then
			for _, aplayer in sgs.qlist(room:getOtherPlayers(player)) do
				if aplayer:getHp() <= player:getHp() then room:loseHp(aplayer) end
			end
		end
	end
}

luasidieCard = sgs.CreateSkillCard{
	name = "luasidie",
	filter = function(self, targets, to_select)
		return (#targets <= 0) and to_select:getHp() > 0 and to_select:objectName() ~= sgs.Self:objectName()
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		room:loseHp(effect.to)
	end
}

luasidie = sgs.CreateViewAsSkill{
	name = "luasidie" ,
	n = 99 ,
	view_filter = function(self, selected, to_select)
		if #selected > sgs.Self:getMaxHp()  then return false end
		return true
	end ,
	view_as = function(self, cards)
		if #cards ~= sgs.Self:getMaxHp() then return nil end
		local luasidieard = luasidieCard:clone()
		for _,card in pairs(cards) do
			luasidieard:addSubcard(card)
		end
		return luasidieard
	end ,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#luasidie")
	end ,
}

luahuaxu2 = sgs.CreateMaxCardsSkill
{
	name = "#luahuaxu",
	extra_func = function(self, target)
		if target:hasSkill(self:objectName()) then
			return 1
		end
	end
}

luahuaxu = sgs.CreateTriggerSkill{
	name = "luahuaxu",
	frequency = sgs.Skill_Compulsory,
	events = { sgs.Dying, sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.Dying and player:hasSkill("luahuaxu") then
			local dying = data:toDying()
			local _player = dying.who
			if _player:objectName() == player:objectName() then
				room:setPlayerProperty(player, "maxhp", sgs.QVariant(player:getMaxHp() + 1))
				room:recover(player, sgs.RecoverStruct(player, nil, player:getLostHp()))
			end
		elseif event == sgs.EventPhaseEnd and player:getHp() > 2 and player:hasSkill("luahuaxu") then
			if player:getPhase() == sgs.Player_Finish then
				room:killPlayer(player)
			end
		end
	end,
	can_trigger = function(self,target)
		return true
	end,
}

sp_yuyuko:addSkill(luawangxiang)
sp_yuyuko:addSkill(luasidie)
sp_yuyuko:addSkill(luahuaxu2)
sp_yuyuko:addSkill(luahuaxu)

zaowuCard = sgs.CreateSkillCard{
    name = "luazaowu",
    target_fixed = true,
    handling_method = sgs.Card_MethodResponse,
    on_use = function(self, room, source, targets)
		local target
		for _,p in sgs.qlist(room:getAlivePlayers()) do
			if p:hasFlag("luazaowu") then target = p end
		end
        room:recover(target, sgs.RecoverStruct(source, nil, 1))
    end
}
luazaowuVS = sgs.CreateViewAsSkill{
    name = "luazaowu",
    response_pattern = "@@luazaowu",
	n = 2,
	view_filter = function(self, selected, to_select)
		if #selected == 0 then return not sgs.Self:isCardLimited(to_select, sgs.Card_MethodResponse) end
		return to_select:getSuit() == selected[1]:getSuit() and not sgs.Self:isCardLimited(to_select, sgs.Card_MethodResponse)
	end,
	view_as = function(self, cards)
		if #cards <= 1 then return end
		local card = zaowuCard:clone()
		for _, cd in ipairs(cards) do
			card:addSubcard(cd)
		end
		return card
    end
}
luazaowu = sgs.CreateTriggerSkill{
    name = "luazaowu",
    view_as_skill = luazaowuVS,
    frequency = sgs.Skill_Compulsory,
	priority = 2,
    global = true,
    events = {sgs.Damaged, sgs.DamageForseen, sgs.DamageCaused},
    on_trigger = function(self, event, player, data, room)
		if event == sgs.Damaged then
			local damage = data:toDamage()
			if damage.damage == 0 then return false end
			if not damage.to or not damage.from then return false end
			if not damage.to:isAlive() then return false end
			if damage.to:hasSkill("luazaowu") then return false end
			for _, keiki in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				if damage.to:getMark("luazaowu") == 0 and room:askForSkillInvoke(damage.to, "luazaowu2", data) then
					keiki:drawCards(1)
					if damage.to:isWounded() then
						keiki:getRoom():setPlayerFlag(damage.to, "luazaowu")
						if room:askForUseCard(keiki, "@@luazaowu", "@luazaowu") then

						end
						keiki:getRoom():setPlayerFlag(damage.to, "-luazaowu")
					end
				end
			end
			return false
		elseif event == sgs.DamageForseen then
			for _, shikieki in sgs.qlist(room:findPlayersBySkillName("luashuojiao")) do
				local damage = data:toDamage()
				if damage.to and damage.nature == sgs.DamageStruct_Thunder then
					return false 
				end
			end 
			return player:getMark("@ouxiangWudi") > 0 and player:hasSkill("luaouxiang")
		elseif event == sgs.DamageCaused then
			local damage = data:toDamage()
			if damage.damage == 0 then return false end
			if not damage.to or not damage.from then return false end
			if player:objectName() == damage.from:objectName() and damage.to:hasSkill("luazaowu") then
				room:setPlayerMark(damage.from, "luazaowu", 1)
			end
		end
		return false
	end
}
luaouxiang = sgs.CreateTriggerSkill{
	name = "luaouxiang",
	events = {sgs.Dying},
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, keiki, data)
		local room = keiki:getRoom()
		local dying = data:toDying()
		local _player = dying.who
		if not _player then return false end
		if _player:objectName() ~= keiki:objectName() then return false end
		--if room:askForSkillInvoke(keiki, self:objectName(), data) then
			for _,p in sgs.qlist(room:getOtherPlayers(keiki)) do
				if not keiki:isAlive() then return false end
				if not p:isNude() then
					local choices = {"discard"}
					if p:getMark("luazaowu") == 0 then table.insert(choices,"giveCard") end
					local choice = room:askForChoice(p, "luaouxiang", table.concat(choices, "+"), data)
					if choice == "discard" then
						local card = room:askForCardChosen(p, p, "he", self:objectName())
						card = sgs.Sanguosha:getCard(card)
						room:throwCard(card, p, keiki)
						if not keiki:isAlive() then return false end
					elseif choice == "giveCard" then
						local card = room:askForCardChosen(p, p, "he", self:objectName())
						card = sgs.Sanguosha:getCard(card)
						keiki:obtainCard(card)
						p:drawCards(1)
						room:addPlayerMark(keiki, "@ouxiangWudi")
						if not keiki:isAlive() then return false end
					end
				end
			end
		--end
	end
}
luaouxiang2 = sgs.CreateTriggerSkill{
	name = "#luaouxiang",
	global = true,
	events = {sgs.EventPhaseStart},
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data, room)
		if room:getCurrent():isLord() and player:getMark("@extra_turn") == 0 and room:getCurrent():objectName() == player:objectName()
				and player:getPhase() == sgs.Player_Start then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName("luaouxiang")) do
				room:setPlayerMark(p2, "@ouxiangWudi", 0)
			end
		end
	end
} 
luashenxing = sgs.CreateViewAsSkill{
	name = "luashenxing$",
	n = 1,
	view_filter = function(self, selected, to_select)
		if #selected == 0 then
			return to_select:getSuit() == sgs.Card_Heart
		end
		return sgs.Self:getMark("@clock_time") + 1 ~= sgs.Self:getMark("luashenxingTurn")
	end,
	view_as = function(self, cards)
		if #cards == 1 and sgs.Self:getMark("@clock_time") + 1 ~= sgs.Self:getMark("luashenxingTurn") then
			local card = cards[1]
			local suit = card:getSuit()
			local point = card:getNumber()
			local id = card:getId()
			local peach = sgs.Sanguosha:cloneCard("peach", suit, point)
			peach:setSkillName(self:objectName())
			peach:addSubcard(id)
			return peach
		end
		return nil
	end,
	enabled_at_play = function(self, player)
		return player:isWounded()
	end,
	enabled_at_response = function(self, player, pattern)
		return string.find(pattern, "peach")
	end
}
luashenxing2 = sgs.CreateTriggerSkill{
	name = "#luashenxing" ,
	events = {sgs.GameStart, sgs.PreCardUsed} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.Damaged then
			if player:hasLordSkill("luashenxing") then
				room:setPlayerProperty(player, "maxhp", sgs.QVariant(1))
				room:setPlayerProperty(player, "hp", sgs.QVariant(1))
			end
		elseif event == sgs.PreCardUsed then 
			local use = data:toCardUse()
			local card = use.card
			if use.from and use.from:hasLordSkill("luashenxing") and card and card:isKindOf("Peach") and card:getSkillName() == "luashenxing" then
				local xo = room:getLord():getMark("@clock_time") + 1
				room:setPlayerMark(use.from, "luashenxingTurn", xo)
				room:writeToConsole("Hello World! 2025-3-1 07:46:33")
				room:writeToConsole("Hello World!" .. tostring(xo))
				room:writeToConsole("Hello World!" .. room:getLord():getMark("luashenxingTurn"))
				room:filterCards(use.from, use.from:getCards("h"), true)

			end 
		end 
	end
}
keiki:addSkill(luazaowu)
keiki:addSkill(luaouxiang)
keiki:addSkill(luaouxiang2)
keiki:addSkill(luashenxing)
keiki:addSkill(luashenxing2)

keikiA:addSkill(luazaowu)
keikiA:addSkill(luaouxiang)
keikiA:addSkill(luashenxing)
keikiA:addSkill(luashenxing2)

keikiB:addSkill(luazaowu)
keikiB:addSkill(luaouxiang)
keikiB:addSkill(luashenxing)
keikiB:addSkill(luashenxing2)

keikiC:addSkill(luazaowu)
keikiC:addSkill(luaouxiang)
keikiC:addSkill(luashenxing)
keikiC:addSkill(luashenxing2)

gejiCard = sgs.CreateSkillCard{
	name = "luageji",
	filter = function(self, targets, to_select)
		return (#targets == 0)
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		effect.from:getRoom():recover(effect.to, sgs.RecoverStruct(effect.from, nil, 1))
		if self:getSubcards():length() == 1 then
			local x = math.min(2, effect.to:getLostHp())
			effect.from:getRoom():recover(effect.to, sgs.RecoverStruct(effect.from, nil, x))
		elseif self:getSubcards():length() == 2 then
			effect.to:gainMark("@luageji2")
		elseif self:getSubcards():length() >= 3 then
			effect.to:gainMark("@luageji4")
		end
	end
}

luageji = sgs.CreateViewAsSkill{
	name = "luageji",
	n = 999,
	view_filter = function(self, selected, to_select)
		local n = to_select:getNumber()
		if n > 7 then return false end
		for _, card in ipairs(selected) do
			n = n + card:getNumber()
			if n > 7 then return false end
		end
		return true
	end,
	view_as = function(self, cards)
		if #cards < 1 then return nil end
		local n = 0
		for _, card in ipairs(cards) do
			n = n + card:getNumber()
		end
		if n < 7 then return nil end
		local card = gejiCard:clone()
		for _, acard in ipairs(cards) do
			card:addSubcard(acard)
		end
		return card
	end,
	enabled_at_play = function()
		return true
	end ,
}

luageji2 = sgs.CreateTriggerSkill{
	name = "#luageji",
	frequency = sgs.Skill_Compulsory ,
	global = true,
	events = {sgs.HpRecover},
	on_trigger = function(self, event, player, data)
		local recover = data:toRecover()
		if player:getMark("@luageji4") > 0 then
			player:drawCards(player:getMark("@luageji4") * recover.recover)
		end
	end
}
luageji3 = sgs.CreateTriggerSkill{
	name = "#luageji2",
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.PreCardUsed} ,
	on_trigger = function(self, event, youmu, data)
		if event == sgs.PreCardUsed then
			local function YouMuCheck(cardS, target)
				if cardS:isKindOf("Hui") or cardS:isKindOf("Ofuda") then
					return true
				elseif cardS:isKindOf("FaithCollection") then
					return not target:isNude()
				end
			end
			local use = data:toCardUse()
			local room = youmu:getRoom()
			if use.card:isKindOf("SkillCard") then return false end
			if use.from:objectName() == youmu:objectName() and use.from:getMark("@luageji2") > 0 then
				if (use.card:isNDTrick() or use.card:isKindOf("BasicCard")) then
					local y = use.from:getMark("@luageji2")
					if (sgs.Sanguosha:getCurrentCardUseReason() ~= sgs.CardUseStruct_CARD_USE_REASON_PLAY) then return false end
					local available_targets = sgs.SPlayerList()
					if (not use.card:isKindOf("AOE")) and (not use.card:isKindOf("GlobalEffect")) then
						for _, p in sgs.qlist(room:getAlivePlayers()) do
							if (use.to:contains(p) or room:isProhibited(youmu, p, use.card)) then continue end
							if (use.card:targetFixed()) then
								if (not use.card:isKindOf("Peach")) or (p:isWounded()) then
									available_targets:append(p)
								end
							else
								if (use.card:targetFilter(sgs.PlayerList(), p, youmu)  or YouMuCheck(use.card, p)) then
									available_targets:append(p)
								end
							end
						end
					end
					local extra
					if not use.card:isKindOf("Collateral") then
						while not available_targets:isEmpty() and y > 0 do
							local Carddata2 = sgs.QVariant() -- ai用
							Carddata2:setValue(use.card)
							room:setTag("luajianjiTC", Carddata2)
							extra = room:askForPlayerChosen(youmu, available_targets, "luageji", "luageji", true, true)
							room:removeTag("luajianjiTC")
							if extra then
								use.to:append(extra)
							else
								break
							end
							available_targets:removeOne(extra)
							y = y - 1
						end
					end
					room:sortByActionOrder(use.to)
					data:setValue(use)
					use.from:loseAllMarks("@luageji2")
					return false
				end
				use.from:loseAllMarks("@luageji2")
			end
		end
		return false
	end
}
luaqiyin = sgs.CreateFilterSkill{
	name = "luaqiyin",
	view_filter = function(self, to_select)
		return to_select:getNumber() > 7
	end,
	view_as = function(self, card)
		local id = card:getEffectiveId()
		local num = card:getNumber()
			num = num - 7
			num = math.max(1, num)
			num = math.min(13, num)

			local new_card = sgs.Sanguosha:getWrappedCard(id)
			new_card:setSkillName(self:objectName())
			new_card:setNumber(num)
			new_card:setModified(true)
			return new_card
	end
}
luaqiyin2 = sgs.CreateTriggerSkill{
	name = "#luaqiyin",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data, room)
		local move = data:toMoveOneTime()
		if move.from and move.from:objectName() == player:objectName() and player:hasSkill("luaqiyin") and not move.card_ids:isEmpty()
				and room:getCurrent():getPhase() == sgs.Player_Play then
			if bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) == sgs.CardMoveReason_S_REASON_DISCARD then
				local invoke = false
				local i = 0
				for _,id in sgs.qlist(move.card_ids) do
					card = sgs.Sanguosha:getCard(id)
					if move.from_places:at(i) == sgs.Player_PlaceHand
							or move.from_places:at(i) == sgs.Player_PlaceEquip then
						if card and card:getNumber() > 7 then
							player:drawCards(1)
						end
					end
					i = i + 1
				end
			end
		end
		return false
	end
}

toone:addSkill(luageji)
toone:addSkill(luageji2)
toone:addSkill(luageji3)
toone:addSkill(luaqiyin)
toone:addSkill(luaqiyin2)

local function isFriendQ(room, target, player)
	if player:getRole() == "rebel" then
		return target:getRole() == "rebel"
	end
	if player:getRole() == "loyalist" or player:getRole() == "lord" then
		return target:getRole() == "loyalist" or target:getRole() == "lord"
	end
end

luasanjie = sgs.CreateTriggerSkill{
	name = "luasanjie",
	global = true,
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.BeforeCardsMove, sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local move = data:toMoveOneTime()
		local hecatiaX = player:getRoom():findPlayerBySkillName("luasanjie")
		if not hecatiaX then return false end
		if player:objectName() ~= hecatiaX:objectName() then return end
		if not hecatiaX then return end
		if event == sgs.BeforeCardsMove then
			if (move.from and ((move.from:objectName() == player:objectName()) or (isFriendQ(room, move.from, hecatiaX) and move.from:getHp() == hecatiaX:getHp()))
					and (move.from_places:contains(sgs.Player_PlaceHand) or move.from_places:contains(sgs.Player_PlaceEquip)))
					and move.from:getPhase() == sgs.Player_NotActive then
				if not hecatiaX:isAlive() then return end
				--if not hecatiaX:askForSkillInvoke("luasanjie", data) then return end
				for i= 0, (move.card_ids:length() - 1), 1 do
					local card_id = move.card_ids:at(i)
					if room:getCardOwner(card_id):objectName() == move.from:objectName()
							and (move.from_places:at(i) == sgs.Player_PlaceHand
							or move.from_places:at(i) == sgs.Player_PlaceEquip) then
						local card = sgs.Sanguosha:getCard(card_id)
						if card:isKindOf("BasicCard") then
							hecatiaX:addMark("luasanjieA")
						elseif card:isKindOf("TrickCard") then
							hecatiaX:addMark("luasanjieB")
						else
							hecatiaX:addMark("luasanjieC")
						end
					end
				end
			end
		else
			if move.card_ids:length() > 0 then
				if not hecatiaX:isAlive() then return end
				if hecatiaX:getMark("xluasanjie") > 0 then return end
				--room:writeToConsole("xluasanjie test" .. " " .. hecatiaX:getMark("luasanjieA")  .. " " .. hecatiaX:getMark("luasanjieB") .. " " .. hecatiaX:getMark("luasanjieC"))
				for i = 1, hecatiaX:getMark("luasanjieA") do
					room:setPlayerMark(hecatiaX, "xluasanjie", 1)
					local targetX = room:askForPlayerChosen(hecatiaX, room:getOtherPlayers(hecatiaX), "luasanjieA", "luasanjie1", true)
					if targetX and targetX:isAlive() then
						targetX:drawCards(1)
					end
				end
				room:setPlayerMark(hecatiaX, "luasanjieA", 0)
				for i = 1, hecatiaX:getMark("luasanjieB") do
					room:setPlayerMark(hecatiaX, "xluasanjie", 1)
					local players = sgs.SPlayerList()
					for _, p in sgs.qlist(room:getAlivePlayers()) do
						if p:getEquips():length() > 0 or p:getJudgingArea():length() > 0 then
							players:append(p)
						end
					end
					if not players:isEmpty() then
						local target1 = room:askForPlayerChosen(hecatiaX, players, "luasanjieB", "luasanjie2", true)
						if target1 and target1:hasEquip() or target1:getJudgingArea():length() > 0 then
							local card_id = room:askForCardChosen(hecatiaX, target1, "ej", self:objectName())
							local card = sgs.Sanguosha:getCard(card_id)
							local place = room:getCardPlace(card_id)
							local equip_index = -1
							if place == sgs.Player_PlaceEquip then
								local equip = card:getRealCard():toEquipCard()
								equip_index = equip:location()
							end
							local tos = sgs.SPlayerList()
							local list = room:getAlivePlayers()
							for _,p in sgs.qlist(list) do
								if equip_index ~= -1 then
									if not p:getEquip(equip_index) then
										tos:append(p)
									end
								elseif place == sgs.Player_PlaceDelayedTrick then
									if not hecatiaX:isProhibited(p, card) and not p:containsTrick(card:objectName()) then
										tos:append(p)
									end
								end
							end
							local tag = sgs.QVariant()
							tag:setValue(target1)
							room:setTag("luasanjieTarget", tag)
							local to = room:askForPlayerChosen(hecatiaX, tos, "luasanjieC", "luasanjie3")
							if to then
								local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, hecatiaX:objectName(), self:objectName(), "")
								room:moveCardTo(card, target1, to, place, reason)
							end
							room:removeTag("luasanjieTarget")
						end
					end
				end
				room:setPlayerMark(hecatiaX, "luasanjieB", 0)
				for i = 1, hecatiaX:getMark("luasanjieC") do
					room:setPlayerMark(hecatiaX, "xluasanjie", 1)
					local targetX = room:askForPlayerChosen(hecatiaX, room:getAlivePlayers(), "luasanjieD", "luasanjie4", true)
					if targetX and targetX:isAlive() then
						local discard_ids = room:getDrawPile()
						for _, id in sgs.qlist(discard_ids) do
							local card = sgs.Sanguosha:getCard(id)
							if card:getNumber() == 1 then
								local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
								dummy:addSubcard(card)
								targetX:obtainCard(card)
								break
							end
						end
					end
				end
				room:setPlayerMark(hecatiaX, "luasanjieC", 0)
				room:setPlayerMark(hecatiaX, "xluasanjie", 0)
			else
				room:setPlayerMark(hecatiaX, "luasanjieA", 0)
				room:setPlayerMark(hecatiaX, "luasanjieB", 0)
				room:setPlayerMark(hecatiaX, "luasanjieC", 0)
				room:setPlayerMark(hecatiaX, "xluasanjie", 0)
			end
		end
	end
}
luayiti = sgs.CreateTriggerSkill{
	name = "luayiti",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.HpLost, sgs.DamageForseen},
	can_trigger = function(self,target)
		return target ~= nil
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local hecatiaX = player:getRoom():findPlayerBySkillName("luayiti")
		if not hecatiaX then return end
		local damage = data:toDamage()
		if event == sgs.DamageForseen then
			for _, shikieki in sgs.qlist(room:findPlayersBySkillName("luashuojiao")) do
				local damage = data:toDamage()
				if damage.to and damage.nature == sgs.DamageStruct_Thunder then
					return false 
				end
			end 
			if damage.to and (isFriendQ(room, damage.to, hecatiaX) and damage.to:objectName() ~= hecatiaX:objectName())
				and damage.to:getHp() == hecatiaX:getHp() then
				if player:objectName() ~= damage.to:objectName() then return false end
				damage.damage = damage.damage - 1
				room:notifySkillInvoked(hecatiaX, "luayiti")
				data:setValue(damage)
			end
			return false
		--[[else
			if isFriendQ(room, player, hecatiaX) and player:objectName() ~= hecatiaX:objectName() and player:getHp() == hecatiaX:getHp() then
				room:notifySkillInvoked(hecatiaX, "luayiti")
				data:setValue(data.toInt() - 1)
			end
			return false]]--
		end
	end
}

luayiti2 = sgs.CreateTriggerSkill{
	name = "#luayiti",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.MaxHpChanged, sgs.HpChanged},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local hecatiaX = player:getRoom():findPlayerBySkillName("luayiti")
		if not hecatiaX then return end 
		if hecatiaX:objectName() == player:objectName() then return end 
		if isFriendQ(room, player, hecatiaX) then  
			if player:getHp() == hecatiaX:getHp() then 
				room:setPlayerMark(player, "@defenseup", 1)
			else
				player:loseAllMarks("@defenseup")
			end 
		end 
	end
}

luamingshen = sgs.CreateTriggerSkill{
	name = "luamingshen$" ,
	events = {sgs.HpRecover} ,
	frequency = sgs.Skill_Compulsory ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.HpRecover then
			local recover = data:toRecover()
			if recover.who and (recover.who:objectName() ~= player:objectName()) then
				if player:getHp() <= 0 then
					room:addPlayerMark(player, "mingshen")
				else
					local x = player:getMark("mingshen") + 1
					for i = 1, x do
						local playerX = room:askForPlayerChosen(player, room:getOtherPlayers(player), self:objectName(), "luamingshen", true, true)
						if playerX then 
							local _data = sgs.QVariant()
							_data:setValue(playerX)
							room:setTag("luamingshenTP", _data)
							local choice = room:askForChoice(player, "luamingshen", "recover+loseHp", _data)
							if choice == "recover" then
								if playerX:isWounded() then
									room:recover(playerX, sgs.RecoverStruct(player, nil, 1))
								end
							else
								local function canLoseHp()
									for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
										if hecatiaX and isFriendQ(room, player, hecatiaX) and player:objectName() ~= hecatiaX:objectName()
												and player:getHp() == hecatiaX:getHp() then
											room:notifySkillInvoked(hecatiaX, "luayiti")
											return false
										end 
									end 
									for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
										if Erin and Erin:getKingdom() == player:getKingdom() then
											room:notifySkillInvoked(Erin, "luajiance")
											return false
										end 
									end 
									return true
								end 
								if canLoseHp() then room:loseHp(playerX, 1) end 
							end
						else
							break
						end 
					end  
					room:setPlayerMark(player, "mingshen", 0)
				end 
			end
		end
		return false
	end
}

hecatia:addSkill(luasanjie)
hecatia:addSkill(luayiti)
hecatia:addSkill(luayiti2)
hecatia:addSkill(luamingshen)

luajianta = sgs.CreateTriggerSkill{
	name = "luajianta",
	frequency = sgs.Skill_Frequent ,
	events = {sgs.EventPhaseStart, sgs.Death} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart then
			if player:getPhase() == sgs.Player_Start and room:askForSkillInvoke(player, self:objectName(), data) then
				local rp = sgs.SPlayerList()
				rp:append(player)
				for i = 1,room:getAlivePlayers():length() do
					for _, p in sgs.qlist(room:getAlivePlayers()) do
						if p:objectName() == rp:at(rp:length() - 1):getNextAlive():objectName() and not rp:contains(p) then
							rp:append(p)
						end
					end
				end
				for _, p in sgs.qlist(rp) do
					local to_gain = {}
					if not player:hasSkill("kuanggu") then
						table.insert(to_gain, "kuanggu")
					end
					if not player:hasSkill("luajuezhan") then
						table.insert(to_gain, "luajuezhan")
					end
					if not player:hasSkill("mashu") then
						table.insert(to_gain, "mashu")
					end
					if not player:hasSkill("luawushuang") then
						table.insert(to_gain, "luawushuang")
					end
					if not player:hasSkill("luayingzi") then
						table.insert(to_gain, "luayingzi")
					end
					local boolX = true
					if p:getMark("@luajianta") > 0 then boolX = false end
					if room:askForDiscard(p, self:objectName(), 1, 1, boolX, true) then
						if #to_gain > 0 then
							local choice = room:askForChoice(player, self:objectName(), table.concat(to_gain, "+"))
							if choice then
								room:handleAcquireDetachSkills(player, choice)
							end
						end
					end
				end
			end
		end
	end
}
luajianta2 = sgs.CreateTriggerSkill{
	name = "#luajianta2",
	priority = 3,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseChanging, sgs.Damage},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_Start then
				room:handleAcquireDetachSkills(player, "-kuanggu")
				room:handleAcquireDetachSkills(player, "-luajuezhan")
				if player:getMark("mashuX") == 0 then room:handleAcquireDetachSkills(player, "-mashu") end
				room:handleAcquireDetachSkills(player, "-luawushuang")
				room:handleAcquireDetachSkills(player, "-luayingzi")
			end
		else
			local damage = data:toDamage()
			if damage.from and damage.from:hasSkill("luajianta") and damage.card and damage.card:isKindOf("Slash") then
				room:setPlayerMark(damage.to, "@luajianta", 1)
			end
		end
	end
}
luachongfeng2 = sgs.CreateMasochismSkill{
	name = "#luachongfeng",
	on_damaged = function(self,player, damage)
		if not player:hasSkill("luachongfeng") then return false end
		if damage.damage == 0 then return false end
		if not damage.from then return false end
		local room = damage.from:getRoom()
		room:setPlayerFlag(damage.from, "luachongfeng")
	end,
	can_trigger = function(self, target)
		return target and target:hasSkill("luachongfeng")
	end
}
luachongfeng = sgs.CreateTriggerSkill{
	name = "luachongfeng" ,
	events = {sgs.EventPhaseChanging} ,
	--frequency = sgs.Skill_Frequent , 这句话源代码没有，但是我感觉应该加上，毕竟连破一点副作用都没有
	priority = 1,
	on_trigger = function(self, event, player, data)
		local saki = player:getRoom():findPlayerBySkillName("luachongfeng")
		if not saki then return false end 
		for _, damage_from in sgs.qlist(player:getRoom():getAlivePlayers()) do
			if damage_from:hasFlag("luachongfeng") then   
				if not damage_from:hasFlag("luachongfeng") then return false end
				local room = damage_from:getRoom()
				if damage_from:askForSkillInvoke("luachongfeng") then

					room:setPlayerFlag(damage_from, "-luachongfeng")
					local playerdata = sgs.QVariant()
					playerdata:setValue(saki)
					player:setTag("luachongfengInvoke", playerdata)
					return false
				else
					room:setPlayerFlag(damage_from, "-luachongfeng")
				end
			end
		end
	end ,
	can_trigger = function(self, target)
		return target
	end
}
luachongfengDo = sgs.CreateTriggerSkill{
	name = "#luachongfengDo-do" ,
	events = {sgs.EventPhaseStart},
	global = true,
	priority = 1 ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local saki = player:getRoom():findPlayerBySkillName("Luashenyi")
		for _, who in sgs.qlist(room:getAlivePlayers()) do
			if who:getTag("luachongfengInvoke") then
				local target = who:getTag("luachongfengInvoke"):toPlayer()
				who:removeTag("luachongfengInvoke")
				if target and target:isAlive() then
					local room_0 = target:getRoom()
					local thread = room_0:getThread()
					local old_phase = target:getPhase()

					target:setPhase(sgs.Player_Play)

					room_0:broadcastProperty(target, "phase")
					if not thread:trigger(sgs.EventPhaseStart, room_0, target) then
						thread:trigger(sgs.EventPhaseProceeding, room_0, target)
					end

					thread:trigger(sgs.EventPhaseEnd, room_0, target)
					target:setPhase(old_phase)
					room_0:broadcastProperty(target, "phase")

					room_0:writeToConsole(old_phase .. "  " .. sgs.Player_NotActive)
				end
			end
		end

	end
}

luachongfeng4 = sgs.CreateTriggerSkill{
	name = "#luachongfeng4",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageForseen},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.DamageForseen then
			local damage = data:toDamage()
			if damage.to and damage.from and (damage.from:hasSkill("luachongfeng")) and damage.to:getHp() == 1 then
				damage.damage = damage.damage - 1
				data:setValue(damage)
				return false
			end
		end
	end
}

saki:addSkill(luajianta)
saki:addSkill(luajianta2)
saki:addSkill(luachongfengDo)
saki:addSkill(luachongfeng4)
saki:addSkill(luachongfeng)
saki:addSkill(luachongfeng2)


sgs.LoadTranslationTable{
	["pay13"] = "风景 扩充", --注意这里每次要加逗号

	["paysage"] = "风景画",
	["#paysage"] = "翼展鸿图",
	["designer:paysage"] = "Paysage",
	["luahongtu"] = "鸿图",
	[":luahongtu"] = "出牌阶段，若合法，你使用的：第一张牌不能被响应；第二张牌无视距离；第X张牌可以额外指定一个目标；你使用第X+1张牌后，可以摸两张牌（X为你体力值）。",

	["sp_rin"] = "火焰猫燐",
	["sp_rinA"] = "火焰猫燐",
	["sp_rinB"] = "火焰猫燐",
	["#sp_rin"]= "火车",
	["designer:sp_rin"] = "Paysage",  -- :ファルケン
	["illustrator:sp_rin"] = "ファルケン",
	["illustrator:sp_rinB"] = "羽羽斩",
	["luamaobu"] = "猫步",
	[":luamaobu"] = "锁定技，当你受到伤害时，若伤害来源不处于连环状态，你须转置你与其的武将牌，然后防止此伤害。",
	["luaguihuo"] = "鬼火",
	["luaguihuorw"] = "请选择一名火焰猫燐",
	[":luaguihuo"] = "一名角色可以将一张黑桃牌当火【杀】使用或打出（不计入次数限制）。若其如此做，你须令你或其摸一张牌，且其于本回合不能再发动此技能。",
	["luaguihuor"] = "鬼火",
	["luaguihuor-invoke"] = "因 鬼火 技能的效果，你需要选一名角色摸一张牌",
	[":luaguihuor"] = "出牌阶段限一次，你可以将一张黑桃牌当火【杀】使用或打出（不计入次数限制）。然后猫车需令你或其摸一张牌。",
 

	["yamame"] = "黑谷山女",
	["#yamame"]= "桦黄小町",
	["designer:yamame"] = "Paysage",
	["luazhulou"] = "筑楼",
	["luazhulouc"] = "令一名角色弃置其所有方块牌。",
	["luazhuloua"] = "令一名角色回复一点体力，其受到的下次属性伤害+1",
	["luazhuloub"] = "令一名角色摸一张牌",
	[":luazhulou"] = "你可以弃置一张【杀】并转置一名角色；若X为0，你须令一名角色回复一点体力，其受到的下次属性伤害+1；若X为1，你须令一名角色摸一张牌；若X为2，你须令一名角色弃置其所有方块牌。（X为你本回合发动“筑楼”的次数）",
	["luarebin"] = "热病",
	["luarebin2"] = "热病",
	[":luarebin"] = "锁定技，若你手牌数等于体力值，你的所有手牌均视为火【杀】。",

	["kanako"] = "八坂神奈子",
	["#kanako"] = "山与湖之神",
	["designer:kanako"] = "Paysage",
	["illustrator:kanako"] = "砂雲",
	["luaxinyang"] = "信仰",
	[":luaxinyang"] = "一名角色的回合结束时，其可以将全部手牌交给你，若如此做，其受到的下次伤害-1（不可叠加）。",
	["luashende"] = "神德", 
	["luashende:jink"] = "你可以发动 <font color=\"green\"><b>神德</b></font> 来使用/打出【闪】",
	["luashende:slash"] = "你可以发动 <font color=\"green\"><b>神德</b></font> 来使用/打出【杀】",
	[":luashende"] = "每当你于出牌阶段以外需要使用或打出一张牌时，你可弃置手牌至手牌上限，视为你使用或打出了此牌。",
	["luashenji"] = "神祭",
	[":luashenji"] = "主公技，一名角色回合开始时若其手牌数大于体力值，你可以令其摸一张牌。",

	["minoriko"] = "秋穰子",
	["#minoriko"] = "丰收之神",
	["designer:minoriko"] = "Paysage",
	["illustrator:minoriko"] = "shinia",
	["luahongyu"] = "丰收",
	[":luahongyu"] = "摸牌阶段，你可以少摸一张牌并令一名角色将手牌补至X+1。若如此做，本回合你手牌上限改为1。（X为其手牌上限）",
	["luafengshou"] = "红芋",
	["luafengshou2"] = "请选择要分发的一个对象。",
	[":luafengshou"] = "弃牌阶段结束时，若你于此阶段弃置的手牌全为基本牌，你可以将这些牌分发给等量的角色。若这些牌均为红色，你回复一点体力。",

	["sp_yuyuko"] = "西行寺幽幽子",
	["#sp_yuyuko"] = "华胥的亡灵",
	["designer:sp_yuyuko"] = "Paysage",
	["illustrator:sp_yuyuko"] = "伊吹のつ",
	["luawangxiang"] = "亡乡",
	[":luawangxiang"] = "锁定技，你的回合结束阶段，体力值不大于你的所有其他角色流失一点体力。",
	["luasidie"] = "死蝶",
	[":luasidie"] = "出牌阶段限一次，你可以弃x张牌令一名其他角色流失一点体力（x为你体力上限）。",
	["luahuaxu"] = "华胥",
	[":luahuaxu"] = "锁定技，你的手牌上限始终+1。每当你濒死时，你需+1体力上限并回复体力至上限。你的回合结束时，若你体力值大于2，你死亡。",

	["sumireko"] = "宇佐见堇子",
	["sumirekoA"] = "宇佐见堇子",
	["sumirekoB"] = "宇佐见堇子",
	["sumirekoC"] = "宇佐见堇子",
	["sumirekoD"] = "宇佐见堇子",
	["#sumireko"] = "现世的秘术师",
	["designer:sumireko"] = "Paysage",
	["illustrator:sumireko"] = "kekkai",
	["illustrator:sumirekoA"] = "昙竹九月",
	["illustrator:sumirekoB"] = "りひと",
	["illustrator:sumirekoC"] = "shnva",
	["illustrator:sumirekoD"] = "昙竹九月",
	["luachaogan"] = "超感",
	["chaogan"] = "超感",
	[":luachaogan"] = "出牌阶段限一次，你可以翻开牌堆顶的X张牌，你获得其中至多两张牌，然后将其余的牌以任意顺序置于牌堆顶或弃置（X为你本回合使用非装备牌指定唯一目标的次数）。",
	["luanianli"] = "念力",
	[":luanianli"] = "每当你受到一点伤害后，你须将牌堆顶的一张牌扣置于武将牌上。出牌阶段，你可以将一张以此法扣置的牌当做【杀】或【过河拆桥】使用。  ",

	["ran"] = "八云蓝",
	["#ran"] = "九尾狐妖",
	["designer:ran"] = "Paysage",
	["illustrator:ran"] = "ルリア",
	["luatianyan"] = "天演",
	["@luatianyan"] = "请选择本回合受到过伤害的一名角色。",
	["@luatianyan2"] = "请选择要交出去的一张手牌。",
	["@luatianyan3"] = "请选择【杀】所需要指定的目标。",
	[":luatianyan"] = "一名角色的回合结束阶段，你可以将一张手牌交给本回合受到过伤害的一名角色。若此牌为红色，其将此牌当作【桃】使用；若为黑色，其将此牌当作【杀】使用。",
	["luatianhu"] = "天狐",
	["tianhu"] = "天狐",
	["luatianhu2"] = "天狐",
	[":luatianhu"] = "出牌阶段限一次，你可以令一名角色摸一张牌，然后若其手牌数为1，则你摸一张牌并重置此技能。",

	["saki"] = "骊驹早鬼",
	["#saki"] = "天马行空",
	["designer:saki"] = "Paysage",
	["illustrator:saki"] = "syuri22",
	["luajianta"] = "践踏",
	[":luajianta"] = "准备阶段，从你开始，每名角色可以弃一张牌，然后直到你的下回合前你获得下列技能之一：“狂骨”“决战”“马术”“无双”“英姿”。（受到你【杀】造成过伤害的角色必须弃牌）。",
	["luachongfeng"] = "冲锋",
	[":luachongfeng"] = "一名角色对你造成过伤害的阶段结束时，其可以令你执行一个额外的出牌阶段。锁定技，你对体力值为1的角色造成的伤害-1 。",

	["keiki"] = "埴安神袿姫",
	["keikiA"] = "埴安神袿姫",
	["keikiB"] = "埴安神袿姫",
	["keikiC"] = "埴安神袿姫",
	["#keiki"] = "造形神",
	["illustrator:keiki"] = "明ノ宮 飛鳥",
	["illustrator:keikiB"] = "alphatitus",
	["illustrator:keikiC"] = "匡吉",
	["designer:keiki"] = "Paysage",
	["luazaowu"] = "造物",
	["luazaowu2"] = "造物(令 <font color=\"yellow\"><b>埴安神袿姫</b></font> 摸牌）",
	["@luazaowu"] = "你可以发动“造物”",
	["~luazaowu"] = "选择两张同花色的牌→点击确定→受伤角色回复体力",
	[":luazaowu"] = "一名其他角色受到伤害后，若其从未对你造成过伤害，其可以令你摸一张牌，然后你可以打出两张同花色的牌令其回复一点体力。",
	["luaouxiang"] = "偶像",
	[":luaouxiang"] = "锁定技，你没有手牌上限。当你濒死时，所有其他角色须选择一项：1.弃置一张牌；2.交给你一张牌并摸一张牌，本轮你免受任何伤害。对你造成过伤害的角色不能选择选项2。",
	["luashenxing"] = "神形",
	[":luashenxing"] = "主公技，每轮限一次，你可以将一张红桃牌当【桃】使用。你体力上限不随身份与模式而发生改变。",

	["toone"] = "远音莉可",
	["#toone"] = "伶俐的歌姬",
	["designer:toone"] = "Paysage",
	["illustrator:toone"] = "きじ鳩",
	["luageji"] = "歌姬",
	[":luageji"] = "你可以弃置点数合计为7的牌令一名角色回复一点体力，若以此法弃置了：1张牌，该角色额外回复至多两点体力；2张牌，该角色下次使用牌可以额外指定一个目标；3张及以上，该角色此后每次回复体力时摸等量牌。",
	["luaqiyin"] = "七音",
	[":luaqiyin"] = "锁定技，你点数大于7的牌点数减少7。这些牌于出牌阶段被弃置后，你摸一张牌。",


    ["hecatia"] = "赫卡提亚",
    ["#hecatia"] = "亿万异世界的神",
    ["designer:hecatia"] = "まりさ",
    ["luasanjie"] = "三界",
    [":luasanjie"] = "当你于回合外失去牌时：①基本牌，你令一名其他角色摸一张牌；②锦囊牌，你可以移动场上一张牌。③装备牌，你令一名角色获得牌堆顶一张点数A的牌。",
	["luasanjie1"] = "令一名其他角色摸一张牌",
	["luasanjie2"] = "移动场上一张牌（选择被移动的角色）",
	["luasanjie3"] = "移动场上一张牌（选择牌移动至的角色）",
	["luasanjie4"] = "令一名角色获得牌堆顶一张点数A的牌",
	["luayiti"] = "一体",
    ["luayiti2"] = "令一名角色增加一点体力上限并回复一点体力。",
    [":luayiti"] = "锁定技，与你体力值相同的其他队友回合外失去牌时视为你回合外失去牌。其体力流失量与受到伤害量-1。",
	["luamingshen"] = "冥神",
    [":luamingshen"] = "主公技，其他角色令你体力值回复后，你可以令一名角色回复或失去一点体力。",



}















