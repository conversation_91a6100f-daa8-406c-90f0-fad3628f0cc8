module("extensions.htms",package.seeall)
extension = sgs.Package("htms")
--配置
local config_file_name = "htms_cfg.lua"
local update_file_name = "htms_updates.txt"
function Alert(toAlert)
	if type(toAlert) == "table" then
		sgs.Alert(table.concat(toAlert,'\n'))
		return nil
	end
	sgs.Alert(toAlert)
	return nil
end
function loadfile(name)--当文件为空时好像有BUG ZY：是的 我这里帮你们测试技能就出bug了
	local file = assert(io.open(name,'r'),"\n\n无法打开文件："..name.."，请从压缩包中提取该文件。")
	local content = file:read("*all"):split('\n')
	file:close()
	return content
end
function getConfig(name,dealwithnil)
	local content = loadfile(config_file_name)
	for _,info in pairs(content) do
		local item = info:split('=')
		if #item == 2 and item[1] == name then
			local toreturn = item[2]
			return toreturn
		end
	end
	if dealwithnil then
		assert(nil,"\n\n无法读取配置："..name)
	end
	return nil
end
function getAllConfig()
	local content = loadfile(config_file_name)
	local all = {}
	for _,info in pairs(content) do
		local item = info:split('=')
		table.insert(all,item)
	end
	return all
end
function saveConfig(name,value)
	local all = getAllConfig()
	for d,item in pairs(all) do
		if item[1] == name then
			item[2] = value
			break
		end
		if d == #all then
			local add = {name,value}
			table.insert(all,add)
		end
	end
	for d,item in pairs(all) do
		all[d] = table.concat(item,'=')
	end
	local file = io.open(config_file_name,'w')
	file:write(table.concat(all,'\n'))
	file:close()
end
--[[function needUpdateView()
	local last = getConfig("lastview")
	if last == nil or last == "" then last = "0/0/0" end
	last = last:split('/')
	local updates = loadfile(update_file_name)
	local i
	for i = 1,#updates,1 do
		local up_date = updates[i]:split('/')--分离‘.’时有问题
		if #up_date == 3 then
			for j = 1,3,1 do
				if tonumber(up_date[j]) < tonumber(last[j]) then break end
				if tonumber(up_date[j]) > tonumber(last[j]) then
					saveConfig("lastview",updates[i])
					needUpdateView()
					return i
				end
			end
		end
	end
	return false
end
local view_start = needUpdateView()
if view_start then
	local updates = loadfile(update_file_name)
	local toAlert = {"更新公告："}
	for i = view_start,#updates,1 do
		table.insert(toAlert,updates[i])
	end
	Alert(toAlert)
end]]
--更新公告
do
	local updates = loadfile(update_file_name)
	local last = getConfig("lastview")
	if last == nil or last == "" then last = "0/0/0" end
	last = last:split('/')
	local i = 1
	while i <= #updates do
		while true do
			local up_date = string.find(updates[i],'/')
			if up_date == nil or up_date ~= 5 then break end
			up_date = updates[i]:split('/')
			for j = 1, 3, 1 do
				local toAlert  = {}
				if tonumber(up_date[j]) < tonumber(last[j]) then break end
				if tonumber(up_date[j]) > tonumber(last[j]) then
					saveConfig("lastview",updates[i])
					table.insert(toAlert,"更新公告："..table.concat(up_date,'/'))
					i = i + 1
					while updates[i] ~= "" do
						table.insert(toAlert,updates[i])
						i = i + 1
					end
					Alert(toAlert)
					break
				end
			end
			break
		end
		i = i + 1
	end
end
--势力
do
    require  "lua.config" 
    local config = config
    local kingdoms = config.kingdoms
    table.insert(kingdoms,"htms_feng")
    table.insert(kingdoms,"htms_huo")
    table.insert(kingdoms,"htms_lin")
    table.insert(kingdoms,"htms_shan")
    table.insert(kingdoms,"htms_wu")
    --[[jian_colors= {
        htms_feng = "#00ff8a",
        htms_huo = "#ff2403",
        htms_lin = "#00ff00",
        htms_shan = "#bc4904",
        htms_wu = "#fefefe",
    }
    local colors=config.kingdom_colors
    --config.kingdom_colors = "#A500CC"
    --local colors=config.kingdom_colors
    --table.insert(colors,htms_feng="#00ff8a")]]
end

hidden_player = sgs.General(extension,"hiddien_player","htms_huo",1,false,true,true)
chitong = sgs.General(extension,"chitong","htms_huo",3,false)
haigls = sgs.General(extension,"haigls","htms_shan",4,true)
chanlz = sgs.General(extension,"chanlz","htms_huo",4,false)
aer = sgs.General(extension,"aer","htms_feng",4,false)
Kirito = sgs.General(extension,"Kirito","htms_shan",3,true)
TachibanaKanade = sgs.General(extension, "TachibanaKanade", "htms_feng", "4", false)
Yoshino = sgs.General(extension, "Yoshino", "htms_lin", "4", false)
chuyin = sgs.General(extension, "chuyin", "htms_lin", "3", false)
ydssx = sgs.General(extension, "ydssx", "htms_huo","4", false)
jiahe = sgs.General(extension,"jiahe","htms_feng",4,false)
paoj = sgs.General(extension, "paoj", "htms_huo", 3, false)
xili_gai = sgs.General(extension,"xili_gai","htms_huo",4,false)
chuannei = sgs.General(extension, "chuannei", "htms_huo", 4, false)
heixueji  = sgs.General(extension, "heixueji", "htms_feng", 3, false)
bended = sgs.General(extension, "bended", "htms_feng", 3, false)
lumuyuanxiang  = sgs.General(extension, "lumuyuanxiang", "htms_lin", 3, false)
guimgm = sgs.General(extension, "guimgm", "htms_lin", 3)
youxmj = sgs.General(extension, "youxmj", "htms_lin", 3, false)
chuixue=sgs.General(extension,"chuixue","htms_huo",4,false)
cangjingxingzi = sgs.General(extension,"cangjingxingzi","htms_huo",4,false)
xiana = sgs.General(extension,"xiana","htms_huo",4,false)
aierkuite = sgs.General(extension,"aierkuite","htms_lin",4,false)
qizui = sgs.General(extension,"qizui","god",3,false)
qiulaihuo = sgs.General(extension,"qiulaihuo","htms_lin",3,true)
chicheng = sgs.General(extension, "chicheng", "htms_feng", 3, false)
niepdl = sgs.General(extension,"niepdl","htms_feng",4,false)
xiaomeiyan = sgs.General(extension,"xiaomeiyan","htms_feng",3,false)
sp_Theresa = sgs.General(extension, "sp_Theresa", "htms_shan", 5, false)
nihuisly = sgs.General(extension, "nihuisly", "htms_feng",3)
xuefeng = sgs.General(extension,"xuefeng","htms_shan",3,false)
dangma = sgs.General(extension,"dangma","htms_shan",5,true)
yasina = sgs.General(extension,"yasina","htms_feng",3,false)
yikls = sgs.General(extension,"yikls","god",3,false,true,true)
lulux = sgs.General(extension,"lulux","htms_feng",4,true)
jieyi = sgs.General(extension,"jieyi","htms_lin",3,false)
youer = sgs.General(extension,"youer","htms_shan",3,true)
jejms = sgs.General(extension,"jejms","htms_feng",3,true)
nlls = sgs.General(extension,"nlls","htms_feng",4,true)
youj = sgs.General(extension,"youj","htms_feng",4,false)
kklt = sgs.General(extension,"kklt","htms_shan",3,true)
jianqm = sgs.General(extension,"jianqm","htms_lin",3,false)
liangys = sgs.General(extension,"liangys","htms_huo",4,false)
feicunjianxin = sgs.General(extension,"feicunjianxin","htms_huo","3",true)
bfsm = sgs.General(extension,"bfsm","htms_feng",4,true)
yuru = sgs.General(extension,"yuru","htms_feng",3,false,true,true)
yanhe = sgs.General(extension,"yanhe","htms_lin",3,false)
mssyx = sgs.General(extension,"mssyx","htms_huo",3,false)
woqiyounai = sgs.General(extension,"woqiyounai","htms_feng",4,false)
gushoulihua = sgs.General(extension,"gushoulihua","htms_feng",3,false)
ougenqinwang  = sgs.General(extension, "ougenqinwang", "htms_lin", 3, false,true,true)
gaowen = sgs.General(extension,"gaowen","htms_lin",2,false,true,true)
fuxiao = sgs.General(extension,"fuxiao","htms_lin",2,false,true,true)
gemingji = sgs.General(extension,"gemingji","htms_lin",2,false,true,true)
siluokayi = sgs.General(extension,"siluokayi","htms_huo",2,true)
shigure = sgs.General(extension, "shigure", "htms_shan", 3, false)
BlackRockShooter = sgs.General(extension, "BlackRockShooter", "htms_feng", 4, false)
sgs.LoadTranslationTable{
	--扩展包名
	["htms"] = "幻天漫杀",
	--势力
	["htms_feng"] = "風",
	["htms_lin"] = "林",
	["htms_huo"] = "火",
	["htms_shan"] = "山",
	["htms_wu"] = "无",
	--武将名
	["qizui"] = "七罪",
	["chitong"] = "赤瞳",
	["haigls"] = "海格力斯",
	["chanlz"] = "缠流子",
	["aer"] = "阿尔托利亚",
	["Kirito"] = "桐人",
	["TachibanaKanade"] = "立华奏",
	["Yoshino"] = "四糸乃",
	["chuyin"] = "初音未来",
	["ydssx"] = "夜刀神十香",
    ["jiahe"] = "加贺",
	["paoj"] = "御坂美琴",
	["xili_gai"] = "夕立改",
	["chuannei"] = "川内",
	["heixueji"] = "黑雪姬",
	["bended"] = "本多二代",
	["lumuyuanxiang"] = "鹿目圆香",
	["guimgm"] = "桂木桂马",
	["youxmj"] = "诱宵美九",
	["chuixue"] = "吹雪",
	["cangjingxingzi"] = "佐仓杏子",
	["xiana"] = "夏娜",
	["aierkuite"] = "爱尔奎特",
	["qiulaihuo"] = "秋濑或",
	["chicheng"] = "赤城",
	["niepdl"] = "聂普迪努",
	["xiaomeiyan"] = "晓美焰",
	["sp_Theresa"] = "SP·德丽莎",
	["nihuisly"] = "逆回十六夜",
	["xuefeng"] = "雪风",
	["dangma"] = "上条当麻",
	["yasina"] = "亚丝娜",
	["yikls"] = "伊卡洛斯",
	["lulux"] = "鲁鲁修",
	["jieyi"] = "结衣",
	["youer"] = "坂井悠二",
	["jejms"] = "吉尔伽美什",
	["nlls"] = "奴良陆生",
	["youj"] = "优纪",
	["kklt"] = "卡卡罗特",
	["jianqm"] = "间崎鸣",
	["liangys"] = "两仪式",
	["bfsm"] = "波风水门",
	["feicunjianxin"] = "绯村剑心",
	["yuru"] = "羽入",
	["yanhe"] = "言和",
	["mssyx"] = "美树沙耶香",
	["woqiyounai"] = "我妻由乃",
	["gushoulihua"] = "古手梨花",
	["ougenqinwang"] = "欧根亲王",
	["gaowen"] = "高文机甲",
	["gemingji"] = "革命机",
	["fuxiao"] = "拂晓",
	["siluokayi"] = "斯洛卡伊",
	["shigure"] = "时雨",
	--游戏内显示的武将名
	["&qizui"] = "七罪",
	["&chitong"] = "赤瞳",
	["&haigls"] = "海格力斯",
	["&chanlz"] = "缠流子",
	["&aer"] = "阿尔托利亚",
	["&Kirito"] = "桐人",
	["&TachibanaKanade"] = "立华奏",
	["&Yoshino"] = "四糸乃",
	["&chuyin"] = "初音未来",
	["&ydssx"] = "夜刀神十香",
	["&jiahe"] = "加贺",
	["&paoj"] = "御坂美琴",
	["&xili_gai"] = "夕立改",
	["&chuannei"] = "川内",
	["&heixueji"] = "黑雪姬",
	["&bended"] = "本多二代",
	["&lumuyuanxiang"] = "鹿目圆香",
	["&guimgm"] = "桂木桂马",
	["&youxmj"] = "诱宵美九",
	["&chuixue"] = "吹雪",
	["&cangjingxingzi"] = "佐仓杏子",
	["&xiana"] = "夏娜",
	["&aierkuite"] = "爱尔奎特",
	["&qiulaihuo"] = "秋濑或",
	["&chicheng"] = "赤城",
	["&niepdl"] = "聂普迪努",
	["&xiaomeiyan"] = "晓美焰",
	["&sp_Theresa"] = "德丽莎",
	["&nihuisly"] = "逆回十六夜",
	["&xuefeng"] = "雪风",
	["&dangma"] = "上条当麻",
	["&yasina"] = "亚丝娜",
	["&yikls"] = "伊卡洛斯",
	["&lulux"] = "鲁鲁修",
	["&jieyi"] = "结衣",
	["&youer"] = "坂井悠二",
	["&jejms"] = "吉尔伽美什",
	["&nlls"] = "奴良陆生",
	["&youj"] = "优纪",
	["&kklt"] = "卡卡罗特",
	["&jianqm"] = "间崎鸣",
	["&liangys"] = "两仪式",
	["&bfsm"] = "波风水门",
	["&feicunjianxin"] = "绯村剑心",
	["&yuru"] = "羽入",
	["&yanhe"] = "言和",
	["&mssyx"] = "美树沙耶香",
	["&woqiyounai"] = "我妻由乃",
	["&gushoulihua"] = "古手梨花",
	["&ougenqinwang"] = "欧根亲王",
	["&gaowen"] = "高文机甲",
	["&gemingji"] = "革命机",
	["&fuxiao"] = "拂晓",
	["&siluokayi"] = "斯洛卡伊",
	["BlackRockShooter"] = "黑岩射手",
	--武将称号
	["#chitong"] = "",
	["#haigls"] = "",
	["#chanlz"] = "",
	["#aer"] = "",
	["#Kirito"] = "",
	["#TachibanaKanade"] = "",
	["#Yoshino"] = "",
	["#chuyin"] = "",
	["#ydssx"] = "",
    ["#jiahe"] = "",
	["#paoj"] = "",
	["#xili_gai"] = "",
	["#chuannei"] = "",
	["#heixueji"] = "",
	["#bended"] = "",
	["#guimgm"] = "",
	["#youxmj"] = "",
	["#chuixue"] = "",
	["#cangjingxingzi"] = "",
	["#xiana"] = "",
	["#aierkuite"] = "",
	["#qiulaihuo"] = "",
	["#chicheng"] = "",
	["#niepdl"] = "",
	["#xiaomeiyan"] = "",
	["#sp_Theresa"] = "",
	["#nihuisly"] = "",
	["#xuefeng"] = "",
	["#dangma"] = "",
	["#yasina"] = "",
	["#yikls"] = "",
	["#lulux"] = "",
	["#jieyi"] = "",
	["#youer"] = "",
	["#jejms"] = "",
	["#nlls"] = "",
	["#youj"] = "",
	["#kklt"] = "",
	["#jianqm"] = "",
	["#liangys"] = "",
	["#bfsm"] = "",
	["#feicunjianxin"] = "",
	["#yuru"] = "",
	["#yanhe"] = "",
	["#mssyx"] = "",
	["#woqiyounai"] = "",
	["#gushoulihua"] = "",
	["#ougenqinwang"] = "",
	["#BlackRockShooter"] = "冰色冷焰",
	--配音
	["$chitong"] = "作战完成，返回基地",
	["$zhuisha1"] = "目标",
	["$zhuisha2"] = "不是目标",
	["$ansha"] = "葬送",
	["$chanlz"] = "可恶！居然输了，加油了啊！",
	["$xianxsy1"] = "那你有什么事！",
	["$xianxsy2"] = "你挺有种的啊！！",
	["$xianxsy3"] = "这么想知道初恋啊！",
	["$fengwang1"] = "如果你不打算上的话，就由我来吧！",
	["$fengwang2"] = "如果你因为我而笑的话，我会很高兴的！",
	["$wangzhe"] = " ex 咖喱棒",
	["$doubleslash1"] = " 结束吧！",
	["$doubleslash2"] = "星爆气流斩",
	["$betacheater1"] = "我叫桐人，请多多指教",
	["$betacheater2"] = "我叫桐人",
	["$defencefield1"] = "不要像我一样弱小",
	["$defencefield2"] = "我相信你所说的话",
	["$defencefield3"] = "四糸乃我是心中理想的自己",
	["$frozenpuppet"] = "你也是来欺负四糸乃吗",
	["$howling1"] = "GUARD SKILL HOWLING",
	["$howling2"] = "有你在的话，也许能做到。",
	["$chuszy1"] = "天空光芒",
	["$chuszy2"] = "这声音是为你而奏！",
	["~jiahe"] = "赤城，只要你没事就好。我先走一步了，等着你哦。",
	["$Luajianzai1"] = "一航战，出击。",
    ["$Luajianzai2"] = "甲板着火了。……怎么这样。",
	["$mie1"] = "让你们见识一下我的力量",
	["$mie2"] = "你真的不会否定我了吗？",
	["$Luazuihou"] = "Sandalphon！",
	["$diancp1"] = "嘿哈！",
	["$diancp2"] = "接下来是我个人的战斗！",
	["~chuannei"] = "還想……打更多的夜戰啊……",
	["$Luayezhan1"] = "什麼？夜戰？",
    ["$Luayezhan2"] = "嘛～不要那麼焦躁嘛，…夜晚可是很長的喲。",
    ["$Luayezhan3"] = "太好了！我期待已久了的夜戰～！",
	["$Luaemeng1"] = "那么，让我们举办一场华丽的派对吧！",
	["$Luaemeng2"] = "随便找一个打了poi",
	["$Luaemeng3"] = "所罗门哟，老娘又从地狱里回来啦",
	["$jiasushijie1"] = "那么，差不多要开始了。",
	["$jiasushijie2"] = "让我看看你的“心意”吧。",
	["$jiasuduijue1"] = "别碰我！",
	["$jiasuduijue2"] = "你不攻过来的话，那我就先攻了！",
	["$juedoujiasu"] = "StarBurst Stream！",
	["#juedoujiasu"] = "少年！你想不想达到更快的境界！",
	["$qingtq1"] = "敌将已经被我打败了！",
	["$qingtq2"] = "妨碍的人是谁？",
	["$xiangy1"] = "绝不会有那种事情",
	["$xiangy2"] = "既然阁下已经选择了要走那条路，那么就由身为极东武士的我来迎击！",
	["~lumuyuanxiang"] = "对不起了，是我太过勉强自己了。",
	["$jiujideqiyuan1"] = "成功了！",
	["$jiujideqiyuan2"] = "还早呢，我不能输啊！",
	["$fazededizao1"] = "稍微变得有趣了呢。再稍微努力一下！",
	["$fazededizao2"] = "轮到我出场了呢。好，加油！",
	["$fazededizao3"] = "还早呢，一起走吧！",
	["$fazededizao4"] = "虽然有点累，但我必须要更努力才行！",
	["$gonglzs"] = "我已经看到结局了！",
	["$shens1"] = "我就是游戏中的神！",
	["$shens2"] = "现实游戏",
	["$pojgj1"] = "我中意上你了！",
	["$pojgj2"] = "我已经拜托过了~",
	["~chuixue"] = "对不起，司令官，晚安",
	["$LuamuguanVS"] = "攻击开始！上吧！",
	["#LuamuguanBuff"] = "就由我来解决吧！",
	["$soulfire"] = "这就是全力一击！",
	["$soulfireDamage"] = "来一起大闹吧！",
	["$duanzui"] = "无路赛无路赛无路赛！",
	["$zhenhong"] = "烦死人了！",
	["$meihuomoyan1"] = "一起吧！",
	["$meihuomoyan2"] = "真是有趣啊",
	["$guancha1"] = "从你们进公园之后我就一直跟着了，这样下去的话你们就必死无疑了",
	["$guancha2"] = "你在发抖呢！真是可怜",
	["$jiyi1"] = "那么，太阳下山了，我们就死定了",
	["$jiyi2"] = "不用担心，我是或，秋濑或，是你的朋友",
	["$Luachicheng"] = "烈风？不，不知道的孩子呢。" ,
	["~chicheng"] = "对不起…雷击处分…请实行吧…",
	["$zhujuexz"] = "挺不错的",
	["$lunhui"] = "还不够！我必须变的更强",
	["$lunhui1"] = "下个对手是谁？",
	["$pocdsf"] = "只有小圆，我一定会拯救给你看！",
	["$lolita"] = "你在看哪里啊！你这个死萝！莉！控！",
	["$judas"] = "德丽莎今天又是大胜利！",
	["$yuandian1"] = "最强的主办者吗 那可真是太好了",
	["$yuandian2"] = "啊~因为和小鬼大人约好了啊~",
	["$moxing1"] = "别自大了",
	["$moxing2"] = "这个世界有趣吗？",
	["$xiangrui1"] = "舰队就由我来保护",
	["$xiangrui2"] = "绝对！没问题！",
	["~xuefeng"] = "不沉的话，或许是不可能的吧",
	["$Luachuyi1"] = "请一顿饭",
	["$Luachuyi2"] = "这样我们就扯平了！怎么样",
	["$Lualianji"] = "我是为了找回自我！",
	["$wnlz1"] = "只是失败一次，也要向别人低头吗",
	["$wnlz2"] = "还是说！就算失败了无数次！也要去帮助别人吗！",
	["$hxss"] = "你是要选择那一边",
	["$geass"] = "世界啊！臣服于我吧！",
	["$znai"] = "好像能用辅助摇杆，举起左手， 做出握拳的手势",
	["$changedfate"] = "结衣一点也不害怕",
	["$wangzbk"] = "普天之下，莫非王土",
	["$bings"] = "哼，真是嚣张啊，杂修",
	["$guailj"] = "你的脑袋，一片肉也别想留下。",
	["$ye"] = "二话不说把串击落不就好了 所以 我才想以愚直应愚直 而且 碍事的家伙 必须杀了之后再继续前进",
	["$zhou"] = "大家 都被重创了吗",
	["$guichan"] = "是爷爷的旧相识吗？",
	["$feils"] = "招式名为：飞雷神·时空疾风闪光连段",
	["$jssg"] = "身为父亲，总会想走在儿子面前，成为他努力的目标。",
	["$feils2"] = "招式名为：飞雷神·时空疾风闪光连段，零！",
	["$zsmy1"] = "只要是活着的东西 就算是神也杀给你看！",
	["$zsmy2"] = "无论什么我都会杀给你看！",
	["$qsas1"] = "我所体验的感情，只有杀人而已",
	["$qsas2"] = "能为我去死吗？",
	["$tjdzf"] = "还早呢！",
	["$qrdag1"] = "大家一起抵抗下来吧！",
	["$qrdag2"] = "想接下我这击可是白费力气",
	["$fenmao"] = "来吧！厮杀开始了",
	["$heihua"] = "哈哈哈哈哈",
	["$businiao1"] = "哇，被打中了！但是，还没结束…",
	["$businiao2"] = "祝好运。",
	["$zhanxianfanyu1"] = "欧根亲王号、移至追击战！",         
	["$zhanxianfanyu2"] = "重巡洋舰欧根亲王，出击！",
	["$zhanxianfanyu3"] = "重巡洋舰欧根亲王，出击！",
	["$zhanxianfanyu4"] = "祝您今天愉快！",
	["$slash_defence1"] = "开火！开火！ ",
	["$slash_defence2"] = "炮击，开始！开火！",
	["~ougenqinwang"] = "我…这次要先沉了，…酒匂…长门…再…见…",
    ["$jixieshen1"] = "咆哮",
    ["$jixieshen1"]  = "有趣就让我尝尝你的秘密是什么味的",
	["$DSTP"]  = "不要啊，不是会痛的嘛？",
	["$loyal_inu"] = "雨总是会有停的时候",
	["$kikann1"] = "可惜了",
	["$kikann2"] = "这里绝不退让",
	["$mozy"] = "能看见哦，死的颜色",
	["$zuihoudefanji"] = "已经太慢了！已经够了！大家死把！",

	--技能翻译
	["zhuisha"] = "追杀",
	["ansha"] = "暗杀",
	["shilian"] = "试炼",
	["shilianEX"] = "试炼",
	["zzsl"] = "最终试炼",
	["xianxsy"] = "鲜血神衣",
	["fengwang"] = "风王",
	["wangzhe"] = "王者",
	["doubleslash"] = "二刀流",
	["betacheater"] = "封弊者",
	["howling"] = "高频咆哮",
	["howlingCard"] = "高频咆哮",
	["defencefield"] = "防御结界",
	["frozenpuppet"] = "冰冻傀儡",
	["frozenpuppetCard"] = "冰冻傀儡",
	["chuszy"] = "初始之音",
	["xiaoshi"] = "消失",
	["mie"] = "灭杀",
	["Luazuihou"] = "最后",
	["Luajianzai"] = "舰载",
	["leij"] = "雷击",
	["diancp"] = "电磁炮",
	["Luaemeng"] = "噩梦",
	["Luayezhan"] = "夜战",
	["jiasushijie"] = "加速世界",
	["juedoujiasu"] = "决斗加速",
	["jiasuduijue"] = "加速对决",
	["qingtq"] = "蜻蜓切",
	["xiangy"] = "翔翼",
	["#Luajianzai"] = "舰载",
	["fazededizao"] = "法则的缔造",
	["#fazededizaoskip"] = "法则的缔造",
	["jiujideqiyuan"] = "救济的祈愿",
	["gonglzs"] = "攻略之神",
	["shens"] = "神知",
	["pojgj"] = "破军歌姬",
	["hunq"] = "魂曲",
	["LuamuguanVS"] = "目观",
	["Luamuguan"] = "目观",
	["#soulfireDamage"] = "余烬",
    ["soulfire"] = "魂火",
	["duanzui"] = "断罪",
	["zhenhong"] = "真红",
	["meihuomoyan"] = "魅惑之魔眼",
	["kaleidoscope"] = "千变万化镜",
	["haniel"] = "赝造魔女",
	["#yanzaostart"] = "赝造",
	["guancha"] = "观察",
	["jiyi"] = "畸意",
	["Luayihang"] = "一航",
	["Luachicheng"] = "吃撑",
	["zhujuexz"] = "主角修正",
	["lunhui"] = "轮回的宿命",
	["lunhui1"] = "轮回",
	["pocdsf"] = "破除的束缚",
	["lolita"] = "合法萝莉",
	["judas"] = "犹超级大",
	["yuandian"] = "原典",
	["moxing"] = "魔性",
	["#moxingMC"] = "魔性",
	["xiangrui"] = "祥瑞",
	["wnlz"] = "无能力者",
	["hxss"] = "幻想杀手",
	["Luachuyi"] = "厨艺Max",
	["Lualianji"] = "闪光连击",
	["kbyxt"] = "可变翼系统",
	["kznw"] = "空之女王",
	["geass"] = "绝对指令",
	["geasstarget"] = "绝对指令",
	["znai"] = "智能AI",
	["changedfate"] = "被改变的命运",
	["lsmz"] = "零时迷子",
	["bhjz"] = "避火戒指",
	["wangzbk"] = "王之宝库",
	["bings"] = "兵弑",
	["guailj"] = "乖离剑",
	["zhou"] = "昼",
	["#zhou"] = "昼",
	["ye"] = "夜",
	["guichan"] = "鬼缠",
	["dafan"] = "打反",
	["juej"] = "绝剑",
	["smsy"] = "圣母圣咏",
	["jiewq"] = "界王拳",
	["saiya"] = "赛亚人",
	["bczzr"] = "不存在之人",
	["mozy"] = "木偶之眼",
	["qsas"] = "情殇哀逝",
	["zsmy"] = "直死魔眼",
	["nirendao"] = "逆刃刀",
	["nidaoren"] = "逆刀刃",
	["#nidaorenDis"] = "逆刀刃",
	["badaozhai"] = "拔刀斋",
	["feils"] = "飞雷神之术",
	["jssg"] = "金色闪光",
	["feils2"] = "飞雷神二段",
	["kuixin"] = "窥心",
	["jiushu"] = "救赎",
	["xieheng"] = "协横",
	["tjdzf"] = "痛觉的止符",
	["qrdag"] = "青刃的哀歌",
	["fenmao"] = "粉毛",
	["changgui"] = "常规",
	["heihua"] = "黑化",
	["smlunhui"] = "轮回",
	["zuihoudefanji"] = "最后的反击",
	["businiao"] = "不死鸟",
	["zhanxianfanyu"] = "战线防御",
	["jixieshenslash"] = "革命机",
	["jixieshendefense"] = "拂晓",
	["jixieshenchain"] = "高文",
	["jixieshen"] = "机械公敌",
	["loyal_inu"] = "忠犬",
	["kikann"] = "归还",
	["loyal_inu_damage"] = "忠犬",
	["guangzijupao"] = "光子巨炮",
	["lanyuhua"] = "蓝羽化",
	["baozou"] = "暴走",
	["yazhi"] = "压制",
	["guangzijupaoCard"] = "光子巨炮",
	["yazhiCard"] = "压制",
	--技能描述
	[":zhuisha"] = "当你使用的【杀】被目标角色使用的【闪】抵消后，你可以摸一张牌，然后将一张手牌置于其武将牌上，称为“追”",
	[":ansha"] = "锁定技，当你使用的【杀】造成伤害时，若该角色未受伤，则此伤害+1；当你使用的【杀】造成伤害时，若该角色的武将牌上有“追”，则此伤害+X，然后弃置其所有的“追”（X为“追”的数量且至多为2）",
	[":shilian"] = "当你受到伤害后，你可以视为对伤害来源使用一张【杀】",
	[":shilianEX"] = "当你受到伤害时，你可以摸一张牌并视为对其使用一张【杀】，此杀无视防具。",
	[":zzsl"] = "觉醒技，当你使用三次【杀】后，修改“试炼”（当你受到伤害时，你可以摸一张牌并视为对其使用一张【杀】，此杀无视防具。）",
	[":xianxsy"] = "锁定技，若你的体力值为4或更少，你的攻击范围+1；若你的体力值为3或更少，你使用【杀】可以额外指定一个目标；若你的体力值为1，你使用【杀】造成的伤害+1",
	[":fengwang"] = "出牌阶段限一次，你可以弃置一张基本牌并令一名有手牌且装备区内有牌的其他角色展示所有手牌，然后该角色选择一项：1.弃置一张装备牌；2.弃置手牌中的【杀】",
	[":wangzhe"] = "限定技，出牌阶段，你可以弃置一张牌，若如此做，你视为使用了一张【杀】，此【杀】可以额外指定两名目标",
	[":doubleslash"] = "出牌阶段开始时，你可以进行一次判定，若结果为：红色，本回合你使用【杀】的上限+1；若为黑色，本回合你使用【杀】可选择的目标上限+1。",
	[":betacheater"] = "锁定技，结束阶段开始时，你须将手牌全部放置在武将牌上称为“隐藏”；每当你受到一点伤害时，你须弃置一张“隐藏”牌防止之。准备阶段开始时，你获得所有的“隐藏”牌。",
	[":howling"] = "<font color=\"green\"><b>出牌阶段限一次，</b></font>你可以弃置一张手牌，令攻击范围内所有其他角色打出一张【杀】或【闪】，否则你对其造成一点伤害。",
	[":defencefield"] = "每当一名角色需要使用或打出【闪】时，你可以弃置一张<font color=\"red\">红色</font>牌，视为该角色使用或打出了一张【闪】。",
	[":frozenpuppet"] = "<font color=\"red\"><b>限定技，</b></font>出牌阶段，你可以弃置所有手牌（至少3张）并选择一名角色，直到你下个回合开始，其他角色使用的牌对其无效。",
	[":chuszy"] = "当一名角色受到伤害后，若是其于本阶段内第一次受到伤害，则你可以弃置一张牌并令其回复1点体力",
	[":xiaoshi"] = "锁定技，当你死亡时，你令杀死你的角色扣减1点体力上限并弃置装备区中的所有牌",
	[":mie"] = "出牌阶段限一次，你可以弃置一张基本牌，并选择一名其他角色，你弃置其一张牌。",
	[":Luazuihou"] = "觉醒技：当你的体力值为1点时，你须将体力上限减少至1，然后跳过当前角色的所有阶段，你进行一个额外回合，并且你攻击距离和手牌上限始终+2，并且将【灭杀皇】的效果修改为，出牌阶段限两次，你可以弃置一张牌并选择一名其他角色，你获得其一张牌。",
	[":Luajianzai"] = "锁定技。每当你从摸牌堆获得牌时，你摸一张牌（不能发动“舰载”）。每当你受到一次伤害时，你须弃置一张手牌。",
	[":leij"] = "锁定技，你的【杀】均视为雷【杀】。锁定技，当你受到雷电伤害后，你摸两张牌。",
	[":diancp"] = "出牌阶段，你可以弃置一张雷【杀】并对一名其他角色造成1点雷电伤害，若该角色因此进入了濒死状态，则你失去1点体力，且此技能失效，直到回合结束。",
	[":Luaemeng"] = "每当你于出牌阶段造成伤害时，你可以弃置一张基本牌：若如此做，此伤害+1",
	[":Luayezhan"] = "摸牌阶段开始时，你可以进行一次判定，若结果为黑色，本回合内你使用的你为伤害来源的【杀】和【决斗】造成的伤害+1。",
	[":jiasushijie"] = "锁定技，当你成为【决斗】目标，在结算伤害后，因此【决斗】受到伤害的角色立即结束当前回合",
	[":juedoujiasu"] = "锁定技，当你成为或被指定为【决斗】的目标时，你摸一张牌，当此决斗结算结束后，其摸一张牌。",
	[":jiasuduijue"] = "当你成为黑色【杀】的目标时，你可以令此杀无效，视为其对你使用一张【决斗】。每当你使用黑色【杀】时，你可以令此杀无效，视为对其使用一张【决斗】",
	[":qingtq"] = "锁定技，你不能被选择为武器牌的目标；当你使用【杀】指定一个目标后，目标角色须弃置一张手牌；你的攻击范围为X（X为场上最大的体力值）",
	[":xiangy"] = "你可以将一张装备牌当【闪】使用或打出。若如此做，你摸一张牌。",
	[":fazededizao"] = "回合开始时，你可以选择一个未以此法选择过的游戏阶段（除准备阶段和结束阶段外），所有角色回合内均跳过此阶段直到你的下个回合开始；若你已选择过了所有阶段，你重置之。",
	[":jiujideqiyuan"] = "出牌阶段限一次，你可以弃置X张不同类型的卡牌，然后令X名角色各回复一点体力（X最大为3）。",
	[":gonglzs"] = "当你成为一名角色【杀】的目标后，你可以弃置你区域内的一张牌，然后你获得该角色相同区域内的一张牌。",
	[":shens"] = "当一名角色进入濒死状态时，你可以展示牌堆顶的四张牌，若其中至少有两张花色和类型均相同的牌，则该角色回复一点体力且你获得展示的牌中符合条件的牌中的一张。",
	[":pojgj"] = "出牌阶段，你可以失去1点体力并令一名已受伤的其他角色回复1点体力。",
	[":hunq"] = "当你的体力发生变化时，你可以令一名角色摸一张牌",
	[":LuamuguanVS"] = "出牌阶段，你可以弃置一张牌，并指定一名其他角色，你与其距离始终为1，且你对其，或其对你造成伤害时，此伤害+1（最多+1），直到你下回合开始。",
	[":soulfire"] = "你可以将一张【闪】或者【杀】当做火【杀】使用或打出。因此杀受到伤害的角色须弃置一张卡牌，然后你可以失去一点体力并获得其的一张手牌",
	[":duanzui"] = "当其他角色于摸牌阶段外获得牌后，若该角色在你的攻击范围内，你可以对其使用一张【杀】",
	[":zhenhong"] = "在你的回合外，你可以将一张牌当做火【杀】使用或打出：当你以此法使用的【杀】造成伤害时，你可以弃置目标的一张牌。",
	[":meihuomoyan"] = "当一名角色使用基本牌或普通锦囊牌指定唯一目标时，你可以将此牌的目标转移给一名合法角色，或取消此牌的目标。每名角色每局游戏限一次，额外的，一名角色对你造成伤害后，重置技能对其的次数。",
	[":kaleidoscope"] = "游戏开始时，回合开始或结束时，你可以选择一名角色，于此技能下次发动前拥有其的一项技能。",
	[":haniel"] = "<font color=\"blue\"><b>锁定技，</b></font>准备阶段开始时，你选择是否弃置一张牌，若选择否，你摸一张牌且于此回合内手牌上限-1且于下个准备阶段开始前所有技能无效。",
	[":guancha"] = "出牌阶段限一次，你可以将你的一半手牌（向上取整）交给一名其他角色，然后你回复一点体力。",
	[":jiyi"] = "每当你回复体力后，你可以失去一点体力并观看牌堆顶的4张牌，你获得其中的两张，若如此做，你弃置其余的牌或以任意顺序至于牌堆顶。",
	[":Luayihang"] = "锁定技。你计算与其他角色的距离始终-1。并且你手牌上限增加2X(X为已损失体力）",
	[":Luachicheng"] = "摸牌阶段开始时，你可以额外摸两张牌，若如此做，本回合你不能使用【杀】。",
	[":zhujuexz"] = "出牌阶段限一次，你可以弃置至少1张牌，然后亮出牌堆顶的1张牌直到所有亮出的牌总点数大于x为止，然后你获得这些亮出的牌。（x为你以此法弃置的牌的总点数）",
	[":lunhui"] = "你的回合外，当你失去一张牌时，你可以进行一次判定，若判定结果为黑色，则摸一张牌。出牌阶段，当你失去一张牌时，若此牌是因弃置进入弃牌堆并且此牌为红色，则你可以使用此牌。",
	[":pocdsf"] = "出牌阶段限一次，你可以弃置所有其他角色的武器牌，然后你弃置等量的牌（不足全弃）。",
	[":lolita"] = "<font color=\"blue\"><b>锁定技，</b></font>当一名其他角色使用【杀】指定除你之外的唯一目标后，若你同时处于此【杀】使用者以及目标角色的攻击范围内，则你也成为此【杀】的目标。",
	[":judas"] = "当你于回合外成为其他角色【杀】的目标后，你可以选择一项：对该角色使用一张【杀】，或摸一张牌。",
	[":yuandian"] = "<b>锁定技</b>，你每杀死一名角色，你摸2X张牌（X为你当前攻击范围）",
	[":moxing"] = "<b>锁定技</b>，摸牌阶段，你多摸X张牌（X为你已损失的体力值）。你的手牌上限始终为5",
	[":xiangrui"] = "每当你受到伤害时，你可以进行一次判定，若结果为红色，此伤害-1，若结果为黑色，你可以弃置一张牌对一名其他角色造成一点伤害。",
	[":wnlz"] = "<b>锁定技</b>，当你受到伤害时：若不为游戏牌造成的伤害，此伤害-1；若为【杀】造成的伤害，此伤害+1。",
	[":hxss"] = "<font color=\"green\"><b>出牌阶段限一次，</b></font>你可以把一张基本牌当【阵前嘴炮】使用（无距离限制），若你赢，目标角色所有技能无效直到回合结束。",
	[":Luachuyi"] = "你可以将一张装备牌当【桃】使用",
	[":Lualianji"] = "当你使用【杀】时，你可以摸一张牌，然后可以使用一张锦囊牌或者装备牌。",
	[":kbyxt"] = "出牌阶段限一次，你可以将一张手牌扣置于武将牌上，称为“变”。每名其他角色的回合限一次，你可以将一张“变”当做一张基本牌使用或打出。准备阶段开始时，你获得你武将牌上的“变”",
	[":kznw"] = "当你于回合外获得牌时，你可以将其中至少一张牌扣置于武将牌上，称为“变”。",
	[":geass"] = "出牌阶段限一次，你可以观看一名其他角色的手牌，并选择其中一张牌使用，若此牌能指定目标，则你可以选择一名任意角色成为此牌目标。",
	[":znai"] = "每名角色的回合限一次，当一名其他角色失去其最后一张手牌时，你可以令其获得场上一张装备牌或摸两张牌。",
	[":changedfate"] = "一名角色判定阶段开始时，若其判定区内有牌，你可以令此阶段内该角色的判定牌不能被更改且效果反转。",
	[":lsmz"] = "回合开始阶段，你可以弃置一张黑色手牌，令你的体力值回复至体力上限。",
	[":bhjz"] = "锁定技，你受到的火焰伤害至多为1。",
	[":wangzbk"] = "每回合限一次，当其他角色的装备置入弃牌堆时，你可以获得之。",
	[":bings"] = "当你受到一次伤害时，你可以弃置一张装备牌令此伤害-1；当你造成一次伤害时，你可以弃置一张装备牌令此伤害+1。",
	[":guailj"] = "限定技，出牌阶段，你可以令所有其他角色依次选择一项：1.弃置装备区中的防具牌；2.受到你造成的1点伤害。",
	[":zhou"] = "当你受到伤害后，本回合内其他角色计算与你距离时+1。",
	[":ye"] = "觉醒技，准备阶段，若你的体力值为全场最低（或之一），你回复一点体力并获得技能“鬼缠”（当有其他角色的死亡结算结束后，你可以声明其武将牌上的一项技能，你获得该技能并失去技能“鬼缠”）。",
	[":guichan"] = "当有其他角色的死亡结算结束后，你可以声明其武将牌上的一项技能，你获得该技能并失去技能“鬼缠”",
	[":dafan"] = "每当你受到一次伤害时，你可以弃置两张黑色牌令此伤害-1，然后伤害来源须交给你一张红色手牌或受到你造成的1点伤害。",
	[":juej"] = "觉醒技：准备阶段开始，若体力为1，白化病开始恶化，减少一点体力上限并回复一点体力，觉醒后不断恶化，然后获得技能“圣母圣咏”。（当你使用【杀】或【决斗】对其他角色造成伤害时，你可以弃置一张牌，然后若你：1.有手牌，此伤害+1；2.没有手牌，此伤害+2。）",
	[":smsy"] = "当你使用【杀】或【决斗】对其他角色造成伤害时，你可以弃置一张牌，然后若你：1.有手牌，此伤害+1；2.没有手牌，此伤害+2。",
	[":jiewq"] = "出牌阶段限一次，你可以失去任意点体力，本回合中你使用的下一张杀伤害增加x（x为你以此法失去的体力值）。",
	[":saiya"] = "锁定技，当你脱离濒死状态时，你增加一点体力上限，第四次脱离濒死状态后，你对其他角色造成的伤害+1。",
	[":bczzr"] = "锁定技，若你本回合内未使用过闪，则你无法成为锦囊牌的目标。",
	[":mozy"] = "出牌阶段限一次，你可以弃置一张牌并令一名角色判定：红色，该角色回复1点体力；黑色，该角色不能成为【桃】的目标直到回合结束。",
	[":qsas"] = "出牌阶段，你可以弃置一张锦囊牌并指定一名其他角色。若如此做，直到你的下个回合开始，其每受到一点伤害，你摸一张牌；其每回复一点体力，你须弃置其一张牌。",
	[":zsmy"] = "限定技，当你对体力值不大于2的角色造成伤害时，你可以翻开牌顶上的一张牌，若此牌不为【杀】，则此伤害+X（X为死亡角色数量，且最少为1）。若你以此法杀死一名角色，你可额外发动一次此技能。",
	[":nirendao"] = "锁定技，你的杀伤害+1，若此杀伤害不小于目标当前体力，则此杀无效。",
	[":nidaoren"] = "限定技，出牌阶段你可以失去任意点体力，然后你摸3x张牌然后本回合内你计算与其他角色的距离时-x。（x为以此法失去的体力）",
	[":badaozhai"] = "限定技：出牌阶段，你可以令所有体力值低于x的其他角色，进入濒死状态。（x为你已损失体力）",
	[":feils"] = "锁定技，当你体力大于等于你手牌时，你与其他角色计算距离时-2；反之其他角色与你计算距离时+2",
	[":jssg"] = "觉醒技，准备阶段开始时，若你的体力值为全场最低（或之一），你须回复1点体力，减少一点体力上限并获得技能“飞雷神二段”。（出牌阶段限一次，你可以视为对一名其他角色使用了一张杀（不计入出牌阶段使用次数），若如此做，直到该角色下个结束阶段开始时，其无视与你的距离。）",
	[":feils2"] = "出牌阶段限一次，你可以视为对一名其他角色使用了一张杀（不计入出牌阶段使用次数），若如此做，直到该角色下个结束阶段开始时，其无视与你的距离。",
	[":kuixin"] = "结束阶段，你可以选择一项：1.观看一名其他角色的手牌；2.观看牌堆顶的3张牌。然后若你的手牌数不大于你所观看牌的数量，你可以获得其中一张牌。",
	[":jiushu"] = "当一名其他角色进入濒死状态时，你可以摸一张牌，然后你可以弃置两张相同花色或种类的手牌，令其回复1点体力。",
	[":xieheng"] = "当你受到一次伤害时，你可以选择一名角色并选择一项：1.令其摸一张牌；2.你弃置一张红色牌，然后其回复1点体力。",
	[":tjdzf"] = "每当你受到一点伤害时，你可以防止此次伤害并将牌堆顶的一张牌置于你的武将牌上，称为“音符”。回合结束阶段开始时，你失去等同于“音符”数量的体力然后获得所有的“音符”。",
	[":qrdag"] = "出牌阶段你使用的第一张【杀】可以无视距离的指定x名角色为目标（x为“音符”的数量），每当该【杀】造成1点伤害，你可以回复1点体力或弃置一张“音符”。",
	[":fenmao"] = "准备阶段开始时，你可以进行一次判定，你获得相应的技能直到回合结束。红色：【常规】（出牌阶段限一次，你可以观看牌堆顶的3张牌，你获得其中一张牌然后将剩余的牌以任意顺序置于牌堆顶。）；黑色：【黑化】（出牌阶段限一次，你可以观看一名任意角色的手牌，然后你展示并获得其中的一张基本牌。）",
	[":changgui"] = "出牌阶段限一次，你可以观看牌堆顶的3张牌，你获得其中一张牌然后将剩余的牌以任意顺序置于牌堆顶。",
	[":heihua"] = "出牌阶段限一次，你可以观看一名任意角色的手牌，然后你展示并获得其中的一张基本牌。",
	[":smlunhui"] = "回合结束阶段，你可以将x张手牌当做你本回合出牌阶段内使用的第x张基本牌或普通锦囊牌使用，若你以此法使用了不少于两张牌，则你于此回合结束后获得一个额外回合。",
	[":zuihoudefanji"] = "限定技，当你处于濒死状态时，你可以明置你的身份牌，若如此做，你将体力值回复至3点，然后将副武将牌更换为“羽入”",
	[":businiao"] = "锁定技，当你受到伤害时，你进行一次判定，若结果为红桃则防止此伤害，否则获得其伤害来源的一张手牌。",
	[":zhanxianfanyu"] = "你可以跳过摸牌阶段或出牌阶段并选择一名角色获得“防御”标记，直到你的下个回合开始，当目标受到【杀】的伤害时，你可以防止此伤害，并视为对其使用了一张【决斗】",
	[":jixieshenslash"] = "当你使用一张【杀】时，你可以直接对目标造成一点火焰伤害。",
	[":jixieshendefense"] = "锁定技，你无视所有的属性伤害，并且你可以弃置一张牌，然后摸一张牌。",
	[":jixieshenchain"] = "你可以将你的红色手牌视为【杀】，黑色手牌视为【闪】。",
	[":jixieshen"] = "回合开始时，你可以选择三台不同的机体进行驾驶，每台机体具有耐久值，当耐久值下降为零点时便无法驾驶，同时可以更换为其他拥有耐久值的机体。三台机体分别为：革命机，你的所有【杀】可以直接目标造成火焰伤害。拂晓，无视所有的属性伤害，并且你可以弃置一张牌然后摸一张牌。高文，你的红色手牌可视为【杀】，黑色手牌可视为【闪】。当你的机甲全部无法使用时，你可以对一名角色造成一点伤害，并且摸一张牌。",
	[":loyal_inu"] = "<font color=\"red\"><b>限定技</b></font>，准备阶段，你可以选择一名其他角色，其获得“忠”标记。<font color=\"blue\"><b>锁定技</b></font>，有“忠”标记角色受到大于1的伤害时，你承受多余的伤害。",
	[":DSTP"] = "<font color=\"blue\"><b>锁定技</b></font>，当你受到伤害时，若此伤害大于1，则防止多余的伤害。",
	[":kikann"] = "当你受到伤害时，你可以令伤害来源选择，伤害来源弃置一张牌，或令你摸一张牌；当场上有“忠”标记的角色时，改为由其选择，并摸一张牌。",
	[":guangzijupao"] = "<font color=\"green\"><b>阶段技，</b></font>你可以弃置一张手牌并选择攻击范围内的一名其他角色：若其有牌，你展示其一张牌，若此牌与你弃置的牌颜色不同，弃置此牌；若颜色相同或其没牌，视为你对其使用一张雷【杀】。",
	[":lanyuhua"] = "<font color=\"blue\"><b>锁定技，</b></font>当你使用【杀】或受到【杀】造成伤害后，你获得一枚“蓝羽”标记，你的攻击范围和手牌上限+X（X为你“蓝羽”标记数的一半，向上取整）。",
	[":baozou"] = "<font color=\"purple\"><b>觉醒技，</b></font>回合开始时，若你的“蓝羽”标记数不少于于你的体力值，你加一点体力上限并回复一点体力，去除“蓝羽化”中攻击范围加成，然后失去技能“光子巨炮”并获得技能“压制”。",
	[":yazhi"] = "出牌阶段开始时，你可以弃置至多2枚“蓝羽”标记然后摸等量的牌。<font color=\"green\"><b>阶段技，</b></font>你可以失去一点体力或弃置2枚“蓝羽”标记，然后依次弃置一名其他角色的2张牌。",
	--房间消息
	["#ansha"] = "<b><font color=\"yellow\">暗杀</b></font><b><font color=\"white\">的效果触发，此杀的伤害</b></font><b><font color=\"yellow\">+%arg，现在为%arg2</b></font>",
	["#xianxsy"] = "<b><font color=\"yellow\">鲜血神衣</b></font><b><font color=\"white\">的效果触发，此杀的伤害</b></font><b><font color=\"yellow\">+1</b></font>",
	["#fengwang_equip"] = "%from<b><font color=\"white\">选择了</b></font><b><font color=\"yellow\"> 弃置一张装备牌</b></font>",
	["#fengwang_discard"] = "%from<b><font color=\"white\">选择了</b></font><b><font color=\"yellow\"> 弃置手牌中的【杀】</b></font>",
	["#doubleslash_black"] = "%from<b><font color=\"yellow\"> 本回合使用【杀】的目标上限+1</b></font>",
	["#doubleslash_red"] = "%from<b><font color=\"yellow\"> 本回合可使用【杀】的次数+1</b></font>",
	["#betacheater_movetopile"] = "<b><font color=\"yellow\">封弊者</b></font><b><font color=\"white\">的效果触发，</b></font>%from<b><font color=\"yellow\"> 将所有手牌置于武将牌上</b></font>",
	["#betacheater_movetohand"] = "<b><font color=\"yellow\">封弊者</b></font><b><font color=\"white\">的效果触发，</b></font>%from<b><font color=\"yellow\"> 获得所有\"隐藏\"牌</b></font>",
	["#betacheater_damage"] = "<b><font color=\"yellow\">封弊者</b></font><b><font color=\"white\">的效果触发，</b></font>%from<b><font color=\"yellow\">所受伤害-%arg，现在为%arg2</b></font>",
	["#xiaoshi"] = "<b><font color=\"yellow\">消失</b></font><b><font color=\"white\">的效果触发，</b></font>%from<b><font color=\"yellow\"> 受到消失的效果影响</b></font>",
	["#Luaemeng"] = "因为%from 的“<font color=\"yellow\"><b>噩梦</b></font>”效果被触发，伤害从 %arg 点增加至 %arg2 点",
	["#LuayezhanBuff"] = "%from 的“<font color=\"yellow\"><b>夜战</b></font>”效果被触发，伤害从 %arg 点增加至 %arg2 点",
	["#qingtq"] = "蜻蜓切",
	["#qingtqAR"] = "蜻蜓切",
	["#LuamuguanBuff"] = "吹雪的“<font color=\"yellow\"><b>目观</b></font>”效果被触发，伤害从 %arg 点增加至 %arg2 点",
	["#pocdsfcard"] = "解除束缚",
	["#wnlz-down"] = "<b><font color=\"yellow\">无能力者</b></font><b><font color=\"white\">的效果触发，</b></font>%from<b><font color=\"yellow\"> 受到的伤害-1，现在为%arg</b></font>",
	["#wnlz-up"] = "<b><font color=\"yellow\">无能力者</b></font><b><font color=\"white\">的效果触发，</b></font>%from<b><font color=\"yellow\"> 受到的伤害+1，现在为%arg</b></font>",
	["#xiangrui"] = "<b><font color=\"yellow\">祥瑞</b></font><b><font color=\"white\">的效果触发，</b></font>%from<b><font color=\"yellow\"> 受到的伤害-1，现在为%arg</b></font>",
	["#geass"] = "%from 发动“<b><font color=\"yellow\">绝对指令</b></font><b><font color=\"white\">”观看了%to的手牌</font>",
	["#bhjz"] = "%from<b><font color=\"yellow\">即将受到的伤害改为1</b></font>",
	["#znai1"] = "<b><font color=\"yellow\">%from 选择了令 %to 获得一张装备牌</b></font>",
	["#znai2"] = "<b><font color=\"yellow\">%from 选择了令 %to 摸两张牌</b></font>",
	["#bings-increase"] = "%from 发动“<b><font color=\"yellow\">兵弑</font></b>”，<b><font color=\"yellow\">令%to受到的伤害+1</font></b>，现在为%arg",
	["#bings-decrease"] = "%from 发动“<b><font color=\"yellow\">兵弑</font></b>”，<b><font color=\"yellow\">令其受到的伤害-1</font></b>，现在为%arg",
	["#dafan"] = "%from 发动“<b><font color=\"yellow\">打反</font></b>”，<b><font color=\"yellow\">令其受到的伤害-1</font></b>，现在为%arg",
	["#smsy"] = "%from 发动“<b><font color=\"yellow\">圣母圣咏</font></b>”，<b><font color=\"yellow\">令其造成的伤害+%arg</font></b>，现在为%arg2",
	["#jiewq"] = "%from 的“<b><font color=\"yellow\">界王拳</font></b>”被触发，<b><font color=\"yellow\">此杀造成的伤害+%arg</font></b>，现在为 %arg2",
	["#saiya"] = "%from 的“<b><font color=\"yellow\">赛亚人</font></b>”被触发，<b><font color=\"yellow\">造成的伤害+1</font></b>，现在为%arg",
	["#mozy"] = "%from<b><font color=\"yellow\"> 本回合不能成为【桃】的目标</b></font>",
	["#tjdzf"] = "%from 发动了“<b><font color=\"yellow\">痛觉的止符</font></b>”，免除了 %arg 点伤害",
	["$badaozhaiQP"] = "剑是杀人的工具，这终究是事实",
	["$nidaorenQP"] = "赎罪",
	["#ShowRole"] = "%from 的身份为 %arg",
	["jixieshenfire"] = "%from 使用了 %arg对目标造成了一点火焰伤害。",
	["jixieshenmianyi"] = "%from 使用了 %arg免疫了属性伤害。",
	["#lanyuhuaAtR"] = "蓝羽化",
	["#lanyuhuaMxC"] = "蓝羽化",
	["$baozouQP"] = "暴走",
	--提示信息
	["@zhuisha_ask"] = "请选择一张手牌作为“追”",
	["@fengwang_askforequip"] = "请选择一张装备牌弃置",
	["@wangzhe"] = "请选择【杀】的目标",
	["~wangzhe"] = "选择目标->点确定",
	["@howlingask"] = "请打出一张【杀】或【闪】，否则受到一点伤害",
	["@defencefieldask"] = "你可以弃置一张红色牌发动“防御结界”",
	["@chuszy_askforcard"] = "请弃置一张牌",
	["@Luazuihou"] = "最后之剑",
	["@emeng"] = "你可以弃置一张基本牌令此伤害+1",
	["hunq-invoke"] = "你可以发动“破军歌姬”令一名角色摸一张牌",
	["@duanzui"] = "你可以对一名角色使用【杀】",
	["@jiyi"] = "你可以发动技能“畸意”",
	["~jiyi"] = "点确定或取消",
	["~lunhui"] = "选择目标",
	["@judaseffect"] = "你可以对 %src 使用一张【杀】",
	["@xiangrui"] = "你可以弃置一张牌对一名其他角色造成一点伤害。",
	["~xiangrui"] = "选择目标和卡牌后点确定。",
	["@Lualianji"] = "你可以使用一张锦囊牌或者装备牌。",
	["#lunhui"] = "宿命",
	["#lunhui1"] = "宿命",
	["@geass"] = "请选择目标角色",
	["~geass"] = "选择目标->点确定",
	["@lsmz"] = "你可以发动技能“零时迷子”",
	["~lsmz"] = "请选择卡牌或取消",
	["@Luajiewei"] = "你可以使用一张锦囊牌或者装备牌",
	["@bings-increase"] = "你可以弃置一张装备牌令此伤害+1",
	["@bings-decrease"] = "你可以弃置一张装备牌令此伤害-1",
	["@dafan"] = "你可以发动“打反”",
	["~dafan"] = "选择两张黑色牌->点确定",
	["@dafantarget"] = "选择一张红色牌交给 %src 或者受到其造成的1点伤害",
	["@smsy"] = "你可以发动“圣母圣咏”",
	["~smsy"] = "选择一张牌->点确定",
	["@kuixin"] = "你可以发动“窥心”",
	["~kuixin"] = "选择一名有手牌其他角色（可不选）->点确定",
	["@jiushu"] = "你可以对 %src 发动“救赎”",
	["~jiushu"] = "选择要弃置的牌->点确定",
	["@xieheng"] = "弃置一张牌并令 %src 回复1点体力，或不弃置牌并令其摸一张牌。",
	["@smlunhui"] = "请将 %src 张手牌当【%dest】使用",
	["~smlunhui"] = "选择手牌→（选择目标）→确定",
	["zhanxianfanyu-invoke"] = "请选择一名目标",
	["slash_defence"] = "是否发动“战线防御”使该角色免疫此次伤害？",
	["jixieshen:fixmachine"] = "是否弃置一张手牌修理机甲？",
	["@kaleidoscope"] = "你可以发动“千变万化镜”选择一名角色<br/> <b>操作提示</b>: 选择一名角色→点击确定<br/>",
	["@haniel"] = "你可以弃置一张牌发动“赝造魔女”",
	["~qizui"] = "",
	["meihuomoyaninvoke"] = "你可以发动“魅惑魔眼”为【%src】指定一个目标",
	["yzlshp"] = "失去一点体力",
	["yzlsmk"] = "弃置2枚“蓝羽”标记",
	--标记
	["@frozenpuppet"] = "冰冻傀儡",
	["$frozenpuppetQP"] = "冰冻傀儡",
	["@zhanxianfanyu"] = "防御",
	["@lanyu"] = "蓝羽",
	["@zsmy"] = "直死魔眼",
	--选项
	--ps:建议格式["skillname:alternative"] = "",
	["fengwang_discard"] = "弃置手牌中的【杀】",
	["fengwang_equip"] = "弃置一张装备牌",
	["jiyi_guanxing"] = "将其余牌以任意顺序放回牌堆顶",
	["jiyi_throw"] = "弃置其余牌",
	["Judge"] = "令其他角色跳过判定阶段",
	["Draw"] = "令其他角色跳过摸牌阶段",
	["Play"] = "令其他角色跳过出牌阶段",
	["Discard"] = "令其他角色跳过弃牌阶段",
	["znai1"] = "令其获得场上一张装备牌",
	["znai2"] = "令其摸两张牌",
	["guailj1"] = "弃置装备区中的防具牌",
	["guailj2"] = "受到一点伤害",
	["qrdag:qrdag_recover"] = "回复1点体力",
	["qrdag:qrdag_discard"] = "弃置一张“音符”",
	["kikann:drac"] = "时雨摸一张牌",
	["kikann:disc"] = "伤害来源弃置一张牌",
	["fazededizao:fzndz_1"] = "跳过判定阶段",
	["fazededizao:fzndz_2"] = "跳过摸牌阶段",
	["fazededizao:fzndz_3"] = "跳过出牌阶段",
	["fazededizao:fzndz_4"] = "跳过弃牌阶段",

	--私家牌堆
	["zhui"] = "追",
	["hide"] = "隐藏",
	["bian"] = "变",
	["yin"] = "音符",
}
--信息发送
function sendLog(message_type,room,from,arg,arg2,to)
	local msg = sgs.LogMessage()
	msg.type = message_type
	if to then msg.to:append(to) end
	if from then msg.from = from end
	if arg then msg.arg = arg end
	if arg2 then msg.arg2 = arg2 end
	room:sendLog(msg)
	return
end
--table到playerlist的转换
function Table2Playerlist(thetable)
	local playerlist = sgs.PlayerList()
	for _,player in ipairs(thetable) do
		playerlist:append(player)
	end
	return playerlist
end
--table到serverplayerlist的转换
function Table2SPlayerlist(thetable)
	local playerlist = sgs.SPlayerList()
	for _,player in ipairs(thetable) do
		playerlist:append(player)
	end
	return playerlist
end
--追杀
zhuisha = sgs.CreateTriggerSkill{
	name = "zhuisha",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.SlashMissed},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local effect = data:toSlashEffect()
		if not room:askForSkillInvoke(player,self:objectName(),data) then return false end
		room:broadcastSkillInvoke(self:objectName())
		player:drawCards(1)
		if effect.to:isAlive() then
			local zhui = room:askForCard(player,".|.|.|hand!","@zhuisha_ask",data,sgs.Card_MethodNone)
			effect.to:addToPile("zhui",zhui,true)
		end
	end,
}
zhuisha_mod = sgs.CreateTriggerSkill{
	name = "#zhuishaMod",
	events = {sgs.Death,sgs.EventLoseSkill},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if event == sgs.Death and not data:toDeath().who:hasSkill(self:objectName()) then return false end
		if event == sgs.EventLoseSkill and data:toString() ~= "zhuisha" then return false end
		local playerlist = room:getAlivePlayers()
		for _,aplayer in sgs.qlist(playerlist) do
			aplayer:clearOnePrivatePile("zhui")
		end
	end,
	can_trigger = function(self,target)
		return target
	end,
}
extension:insertRelatedSkills("zhuisha","#zhuishaMod")
--暗杀
ansha = sgs.CreateTriggerSkill{
	name = "ansha",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageCaused},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if not damage.card:isKindOf("Slash") then return false end
		local target = damage.to
		local num = 0
		if not target:isWounded() then
			num = num + 1
		end
		if target:getPile("zhui"):length() > 0 then
			num = num + math.min(2,target:getPile("zhui"):length())
			target:clearOnePrivatePile("zhui")
		end
		if num == 0 then return false end
		damage.damage = damage.damage + num
		sendLog("#ansha",room,nil,num,damage.damage)
		room:broadcastSkillInvoke(self:objectName())
		data:setValue(damage)
	end,
}
--试炼
shilian = sgs.CreateTriggerSkill{
	name = "shilian",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Damaged},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		room:setPlayerMark(player,"shilian_ok",1)
		local damage = data:toDamage()
		if not damage.from then return false end
		if not player:canSlash(damage.from,nil,false) then return false end
		if not room:askForSkillInvoke(player,self:objectName(),data) then return false end
		local slash = sgs.Sanguosha:cloneCard("slash",sgs.Card_NoSuit,0)
		slash:setSkillName(self:objectName())
		local use = sgs.CardUseStruct()
		use.card = slash
		use.from = player
		use.to:append(damage.from)
		room:useCard(use)
	end,
}
--试炼EX
shilianEX = sgs.CreateTriggerSkill{
	name = "shilianEX",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Damaged},
	on_trigger = function(self,event,player,data)
		if player:getMark("shilian_ok") > 0 then return false end
		local room = player:getRoom()
		local damage = data:toDamage()
		if damage.damage == 0 then return false end
		if not room:askForSkillInvoke(player,self:objectName()) then return false end
		player:drawCards(1)
		if not damage.from then return false end
		if not player:canSlash(damage.from,nil,false) then return false end
		local slash = sgs.Sanguosha:cloneCard("slash",sgs.Card_NoSuit,0)
		slash:setSkillName(self:objectName())
		local use = sgs.CardUseStruct()
		use.card = slash
		use.from = player
		use.to:append(damage.from)
		damage.from:addQinggangTag(slash)
		room:broadcastSkillInvoke("shilian")
		room:useCard(use)
	end
}
shilian_usingjudge = sgs.CreateTriggerSkill{ --如果是在伤害结算过程中改描述则不可发动新试炼
	name = "#shilian_usingjudge",
	events = {sgs.Damaged},
	priority = -100,
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		room:setPlayerMark(player,"shilian_ok",0)
	end
}
--最终试炼
zzsl = sgs.CreateTriggerSkill{
	name = "zzsl",
	frequency = sgs.Skill_Wake,
	events = {sgs.SlashMissed},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		room:setPlayerMark(player,self:objectName(),player:getMark(self:objectName()) + 1)
		if player:getMark(self:objectName()) == 1--[[3]] then
			room:sendCompulsoryTriggerLog(player,self:objectName())
			room:broadcastSkillInvoke(self:objectName())
			room:changeMaxHpForAwakenSkill(player,0)
			room:detachSkillFromPlayer(player,"shilian",true)
			room:getThread():addTriggerSkill(sgs.Sanguosha:getTriggerSkill("shilianEX"))
			room:attachSkillToPlayer(player,"shilianEX")
		end
	end
}
extension:insertRelatedSkills("zzsl","#shilian_usingjudge")
--鲜血神衣
xianxsy_damage = sgs.CreateTriggerSkill{
	name = "xianxsy",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageCaused},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if player:getHp() ~= 1 then return false end
		if not damage.card:isKindOf("Slash") then return false end
		sendLog("#xianxsy",room)
		damage.damage = damage.damage + 1
		data:setValue(damage)
		room:broadcastSkillInvoke(self:objectName(),math.random(1,3))
	end,
}
xianxsy_range = sgs.CreateAttackRangeSkill{
	name = "#xianxsy_range",
	extra_func = function(self,player)
		if player:hasSkill(self:objectName()) and player:getHp() <= 4 then return 1 end
	end,
}
xianxsy_target = sgs.CreateTargetModSkill{
	name = "#xianxsy_target",
	pattern = "Slash",
	extra_target_func = function(self,from,card)
		if from:hasSkill(self:objectName()) and from:getHp() <= 3 then return 1 end
	end,
}
extension:insertRelatedSkills("xianxsy","#xianxsy_range")
extension:insertRelatedSkills("xianxsy","#xianxsy_target")
--风王
fengwangCard = sgs.CreateSkillCard{
	name = "fengwang",
	will_throw = true,
	target_fixed = false,
	filter = function(self,targets,to_select)
		return #targets < 1 and to_select:objectName() ~= sgs.Self:objectName() and to_select:hasEquip() and not to_select:isKongcheng()
	end,
	feasible = function(self,targets)
		return #targets == 1
	end,
	on_use = function(self,room,source,targets)
		local target = targets[1]
		room:showAllCards(target)
		local dummy = sgs.Sanguosha:cloneCard("slash",sgs.Card_NoSuit,0)
		for _,card in sgs.qlist(target:getHandcards()) do
			if card:isKindOf("Slash") then dummy:addSubcard(card) end
		end
		local choice = "fengwang_equip"
		if dummy:getSubcards():length() > 0 then
			choice = room:askForChoice(target,self:objectName(),"fengwang_discard+fengwang_equip")
		end
		if choice == "fengwang_equip" then
			sendLog("#fengwang_equip",room,target)
			room:askForCard(target,"EquipCard!","@fengwang_askforequip")
		else
			sendLog("#fengwang_discard",room,target)
			if dummy:getSubcards():length() > 0 then room:throwCard(dummy,target,target) end
		end
	end,
}
fengwangVS = sgs.CreateViewAsSkill{
	name = "fengwang",
	n = 1,
	view_filter = function(self,selected,to_select)
		return #selected == 0 and to_select:isKindOf("BasicCard")
	end,
	view_as = function(self,cards)
		if #cards == 0 then return nil end
		local card = fengwangCard:clone()
		card:addSubcard(cards[1])
		card:setSkillName(self:objectName())
		return card
	end,
	enabled_at_play = function(self,player)
		return not player:hasUsed("#fengwang")
	end,
}
--王者
wangzheCard = sgs.CreateSkillCard{
	name = "wangzhe",
	will_throw = true,
	feasible = function(self,targets)
		return #targets ~= 0
	end,
	filter = function(self,targets,to_select)
		local rangefix = 0
		if sgs.Self:getWeapon() and sgs.Self:getWeapon():getId() == self:getSubcards():first() then
			local card = sgs.Self:getWeapon():getRealCard():toWeapon()
			rangefix = card:getRange() - sgs.Self:getAttackRange(false)
		end
		local slash = sgs.Sanguosha:cloneCard("Slash",sgs.Card_NoSuit,0)
		slash:setSkillName(self:objectName())
		return sgs.Self:canSlash(to_select,slash,true,rangefix,Table2Playerlist(targets)) and #targets < (1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget,sgs.Self,slash))
	end,
	on_use = function(self,room,source,targets)
		room:setPlayerMark(source,"wangzhe_used",1)
		local slash = sgs.Sanguosha:cloneCard("Slash",sgs.Card_NoSuit,0)
		slash:setSkillName(self:objectName())
		local use = sgs.CardUseStruct(slash,source,Table2SPlayerlist(targets),false)
		room:useCard(use,false)
	end,
}
wangzheVS = sgs.CreateViewAsSkill{
	name = "wangzhe",
	n = 1,
	view_filter = function(self,selected,to_select)
		return sgs.Self:getMark(self:objectName()) == 0 and #selected < 1 and sgs.Self:canDiscard(sgs.Self,to_select:getId())
	end,
	view_as = function(self,cards)
		if #cards == 0 then return nil end
		local vs_card = wangzheCard:clone()
		vs_card:addSubcard(cards[1])
		vs_card:setSkillName(self:objectName())
		return vs_card
	end,
	enabled_at_play = function(self,player)
		local slash = sgs.Sanguosha:cloneCard("Slash",sgs.Card_NoSuit,0)
		return player:getMark("wangzhe_used") < 1 and not player:isLocked(slash)
	end,
}
wangzhe = sgs.CreateTriggerSkill{
	name = "wangzhe",
	frequency = sgs.Skill_Limited,
	view_as_skill = wangzheVS,
	events = {sgs.CardUsed},
	on_trigger = function(self,event,player,data)
		return false
	end,
}
wangzheEX = sgs.CreateTargetModSkill{
	name = "#wangzhe",
	extra_target_func = function(self, from, card)
		if from:hasSkill(self:objectName()) and card:getSkillName() == "wangzhe" then
			return 2
		end
	end,
}
extension:insertRelatedSkills("wangzhe","#wangzhe")
--二刀流
doubleslash = sgs.CreateTriggerSkill{
	name = "doubleslash",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseStart, sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart and player:getPhase() == sgs.Player_Play then
			if room:askForSkillInvoke(player, self:objectName(), data) then
				local judge = sgs.JudgeStruct()
				judge.reason = self:objectName()
				judge.play_animation = false
				judge.who = player
				room:judge(judge)
				if judge.card:isRed() then
					sendLog("#doubleslash_red",room,player)
					room:setPlayerFlag(player, "doubleslashred")
					room:broadcastSkillInvoke("doubleslash", 1)
				elseif judge.card:isBlack() then
					sendLog("#doubleslash_black",room,player)
					room:setPlayerFlag(player, "doubleslashblack")
					room:broadcastSkillInvoke("doubleslash", 2)
				end
			end
		elseif event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				if player:hasFlag("doubleslashred") then room:setPlayerFlag(player, "-doubleslashred") end
				if player:hasFlag("doubleslashblack") then room:setPlayerFlag(player, "-doubleslashblack") end
			end
		end
		return false
	end
}
doubleslashMod = sgs.CreateTargetModSkill{
	name = "#doubleslashMod" ,
	pattern = "Slash",
	residue_func = function(self, player)
		if player:hasSkill("doubleslash") and player:hasFlag("doubleslashred") then
			return 1
		end
	end,
	extra_target_func = function(self, from, card)
		if from:hasSkill("doubleslash") and from:hasFlag("doubleslashblack") then
			return 1
		end
	end,
}
extension:insertRelatedSkills("doubleslash", "#doubleslashMod")
--封弊者
betacheater = sgs.CreateTriggerSkill{
	name = "betacheater",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseStart, sgs.EventPhaseChanging, sgs.DamageInflicted},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart then
			local hide = player:getPile("hide")
			if player:getPhase() == sgs.Player_Finish and not player:isKongcheng() then
				local ids = sgs.IntList()
				for _,card in sgs.qlist(player:getHandcards()) do
					ids:append(card:getId())
				end
				sendLog("#betacheater_movetopile",room,player)
				room:broadcastSkillInvoke(self:objectName())
				player:addToPile("hide",ids,false)
			elseif player:getPhase() == sgs.Player_RoundStart and player:getPile("hide"):length() > 0 then
				local dummy = sgs.Sanguosha:cloneCard("slash",sgs.Card_NoSuit,0)
				for _,card_id in sgs.qlist(hide) do
					dummy:addSubcard(card_id)
				end
				sendLog("#betacheater_movetohand",room,player)
				room:broadcastSkillInvoke(self:objectName())
				room:obtainCard(player,dummy,false)
			end
		elseif event == sgs.DamageInflicted then
			local damage = data:toDamage()
			local hurt = damage.damage
			local hide = player:getPile("hide")
			local num = 0
			if not hide:isEmpty() then room:broadcastSkillInvoke(self:objectName()) end
			if hurt >= hide:length() then
				player:clearOnePrivatePile("hide")
				damage.damage = damage.damage - hide:length()
				num = num + hide:length()
			else
				for i=1, hurt, 1 do
					hide = player:getPile("hide")
					if not hide:isEmpty() then
						room:fillAG(hide,player)
						local card_id = room:askForAG(player, hide, false, self:objectName())
						room:throwCard(card_id, player)
						room:clearAG()
						damage.damage = damage.damage - 1
						num = num + 1
					else
						break
					end
				end
			end
			sendLog("#betacheater_damage",room,player,num,damage.damage)
			if damage.damage < 1 then return true end
			data:setValue(damage)
		end
	end
}
--高频咆哮
howlingCard = sgs.CreateSkillCard{
	name = "howlingCard",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		local list = room:getOtherPlayers(source)
		local targets = sgs.SPlayerList()
		for _,p in sgs.qlist(list) do
			if source:inMyAttackRange(p) then targets:append(p) end
		end
		if targets:isEmpty() then return false end
		for _,p in sgs.qlist(targets) do
			if not room:askForCard(p, "Slash,Jink", "@howlingask", sgs.QVariant(), sgs.Card_MethodResponse) then
				room:damage(sgs.DamageStruct("howling", source, p))
			end
		end
	end,
}
howling = sgs.CreateOneCardViewAsSkill{
	name = "howling", 
	view_filter = function(self, to_select)
    	return not to_select:isEquipped() 
	end,
	view_as = function(self, cards)
		local howlingCard = howlingCard:clone()
		howlingCard:addSubcard(cards)
		return howlingCard
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#howlingCard")
	end
}
--防御结界
defencefield = sgs.CreateTriggerSkill{
	name = "defencefield" ,
	events = {sgs.CardAsked},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local Yoshino = room:findPlayerBySkillName(self:objectName())
		if not Yoshino then return false end
		local pattern = data:toStringList()[1]
		if (pattern == "jink") and not Yoshino:isNude() then
			if room:askForSkillInvoke(Yoshino, self:objectName(), data) then
				local id = room:askForCard(Yoshino, ".|red", "@defencefieldask", sgs.QVariant(), self:objectName())
				if id then
					local jink = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
					jink:setSkillName(self:objectName())
					room:provide(jink)
					return true
				end
			end
		end
	end,
	can_trigger = function(self, target)
		return target
	end
}
--冰冻傀儡
frozenpuppetCard = sgs.CreateSkillCard{
	name = "frozenpuppetCard",
	target_fixed = false,
	will_throw = true,
	filter = function(self, targets, to_select, player)
		return #targets == 0
	end,
	on_use = function(self, room, source, targets)
		local target = targets[1]
		room:doSuperLightbox("Yoshino", "$frozenpuppetQP")
		source:loseMark("@frozenpuppet")
		source:throwAllHandCards()
		room:addPlayerMark(target, "@frozenpuppettarg")
	end
}
frozenpuppetVS = sgs.CreateZeroCardViewAsSkill{
	name = "frozenpuppet",
	view_as = function(self, cards)
		local card = frozenpuppetCard:clone()
		card:setSkillName(self:objectName())
		return card
	end,
	enabled_at_play = function(self, player)
		if player:getMark("@frozenpuppet") > 0 then
			return player:getHandcardNum() >= 3
		end
	end,
}
frozenpuppet = sgs.CreateTriggerSkill{
	name = "frozenpuppet",
	frequency = sgs.Skill_Limited,
	limit_mark = "@frozenpuppet",
	events = {sgs.CardEffected, sgs.EventPhaseStart, sgs.Death},
	view_as_skill = frozenpuppetVS,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardEffected then
			local effect = data:toCardEffect()
			if effect.from and effect.to and effect.to:getMark("@frozenpuppettarg") > 0 then
				if effect.from:objectName() ~= effect.to:objectName() then
					return true
				end
			end
		elseif event == sgs.EventPhaseStart then
			if player:getPhase() == sgs.Player_Start and player:hasSkill(self:objectName()) then
				for _,p in sgs.qlist(room:getAllPlayers()) do
					room:setPlayerMark(p, "@frozenpuppettarg", 0)
				end
			end
		elseif event == sgs.Death then
			local death = data:toDeath()
			if death.who:hasSkill(self:objectName()) then
				for _,p in sgs.qlist(room:getOtherPlayers(death.who)) do
					room:setPlayerMark(p, "@frozenpuppettarg", 0)
				end
			end
		end
	end,
	can_trigger = function(self, target)
		return target
	end
}
frozenpuppetPS = sgs.CreateProhibitSkill{
	name = "#frozenpuppetPS",
	is_prohibited = function(self, from, to, card)
		if to:getMark("@frozenpuppettarg") > 0 and from:objectName() ~= to:objectName() then
			return card:isKindOf("DelayedTrick")
		end
	end
}
extension:insertRelatedSkills("frozenpuppet", "#frozenpuppetPS")
--初始之音
chuszy = sgs.CreateTriggerSkill{
	name = "chuszy",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Damaged,sgs.EventPhaseStart,sgs.EventPhaseEnd},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if event == sgs.Damaged then
			local damage = data:toDamage()
			local chuyin = room:findPlayerBySkillName(self:objectName())
			if not chuyin then return false end
			if damage.to:hasFlag(self:objectName()) or chuyin:isNude() or not damage.to:isAlive() then return false end
			damage.to:setFlags(self:objectName())
			local d = sgs.QVariant()
			d:setValue(damage.to)
			if not room:askForSkillInvoke(chuyin,self:objectName(),d) then return false end
			if not room:askForDiscard(chuyin,self:objectName(),1,1,true,true,"@chuszy_askforcard") then return false end
			room:broadcastSkillInvoke("chuszy")
			room:recover(player,sgs.RecoverStruct(chuyin))
		end
		if event == sgs.EventPhaseStart or event == sgs.EventPhaseEnd then
			local playerlist = room:getAlivePlayers()
			for _,aplayer in sgs.qlist(playerlist) do
				aplayer:setFlags("-"..self:objectName())
			end
		end
	end,
	can_trigger = function(self,target)
		return target
	end,
}
--消失
xiaoshi = sgs.CreateTriggerSkill{
	name = "xiaoshi",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Death},
	on_trigger = function(self,event,player,data)
		local death = data:toDeath()
		local room = player:getRoom()
		if death.who:objectName() ~= player:objectName() or not death.who:hasSkill(self:objectName()) then return false end
		if death.damage and death.damage.from then
			sendLog("#xiaoshi",room,death.damage.from)
			death.damage.from:throwAllEquips()
			room:loseMaxHp(death.damage.from)
			room:broadcastSkillInvoke("xiaoshi",math.random(1,2))
		end
	end,
	can_trigger = function(self,target)
		return target
	end,
}
--灭杀
miecard = sgs.CreateSkillCard{
	name = "mie",
    target_fixed = false,
	will_throw = true,
	filter = function(self, targets, to_select, player)
		return #targets < 1 and to_select:objectName() ~= player:objectName()
	end,
	on_use = function(self, room, source, targets)
		local id = room:askForCardChosen(source, targets[1], "he", "mie")
		if source:getMark("@Luazuihou") > 0 then
			room:obtainCard(source, id, false)
		else
			room:throwCard(id, targets[1], source)
		end
	end
}

mie = sgs.CreateViewAsSkill{
	name = "mie",
	n = 1,
	view_filter = function(self, selected, to_select)
		if sgs.Self:getMark("@Luazuihou") > 0 then
			return true
		end
		return to_select:isKindOf("BasicCard")
	end,
	view_as = function(self, cards)
		if #cards == 1 then
			local card = cards[1]
			local acard = miecard:clone()
			acard:addSubcard(card:getId())
			acard:setSkillName(self:objectName())
			return acard
		end
	end,
	enabled_at_play = function(self, player)
		return (not player:hasUsed("#mie")) or (player:getMark("@Luazuihou") > 0 and player:usedTimes("#mie") < 2)
	end,
	enabled_at_response = function(self, player, pattern)
		return false
	end
}
--最后之剑
Luazuihou = sgs.CreateTriggerSkill{
	name = "Luazuihou" ,
	frequency = sgs.Skill_Wake ,
	events = {sgs.HpChanged, sgs.MaxHpChanged} ,
	on_trigger = function(self, event, player, data )
		local room = player:getRoom()
		if player:getHp() == 1 and player:getMark("@Luazuihou") == 0 then
			player:gainMark("@Luazuihou")
			if player:getMaxHp() > 1 then
				room:loseMaxHp(player, player:getMaxHp() - 1)
				room:broadcastSkillInvoke("Luazuihou")
			end
			room:detachSkillFromPlayer(player, "mie", true)
			sgs.Sanguosha:addTranslationEntry(":mie", "出牌阶段限两次，你可以弃置一张牌并选择一名其他角色，你获得其一张牌。")
			room:acquireSkill(player, "mie")
			player:gainAnExtraTurn()
			room:throwEvent(sgs.TurnBroken)
		end
	end
}

Luabei1 = sgs.CreateAttackRangeSkill{
	name = "#Luabei1",
	extra_func = function(self, player, include_weapon)
		if player:getMark("@Luazuihou") > 0 then
			return 2
		end
	end
}

Luabei2 = sgs.CreateMaxCardsSkill{
	name = "#Luabei2",
	extra_func = function(self, player)
		if player:getMark("@Luazuihou") > 0 then
			return 2
		end
	end
}
extension:insertRelatedSkills("Luazuihou", "#Luabei1")
extension:insertRelatedSkills("Luazuihou", "#Luabei2")
--舰载
Luajianzai = sgs.CreateTriggerSkill{
    name = "Luajianzai",
    frequency = sgs.Skill_Compulsory,
    events = {sgs.BeforeCardsMove},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if room:getTag("FirstRound"):toBool() then return false end 
        if player:hasFlag("Luajianzai") then
            player:setFlags("-Luajianzai") 
            return false
        end
        local move = data:toMoveOneTime()
        local dest = move.to
        if dest then
            if dest:objectName() == player:objectName() then
                if move.to_place == sgs.Player_PlaceHand and move.from_places:at(0)==sgs.Player_DrawPile then
                    player:setFlags("Luajianzai")
                    player:drawCards(1)
                    room:broadcastSkillInvoke("Luajianzai",1)
                end
            end
        end
    end
	
}
Luajianzai_keep = sgs.CreateTriggerSkill{
	name = "#Luajianzai",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Damaged},
	on_trigger = function(self, event, player, data)
		if player:isDead() then return end
		if not player:isKongcheng() then
			local room = player:getRoom()
				room:askForDiscard(player, self:objectName(), 1, 1, false, false)
				room:broadcastSkillInvoke("#Luajianzai")
		end
	end
}
extension:insertRelatedSkills("Luajianzai","#Luajianzai")
--雷击
leij = sgs.CreateFilterSkill{
	name = "leij",
	view_filter = function(self, card)
		return card:isKindOf("Slash")
	end,
	view_as = function(self, card)
		local ThunderSlash = sgs.Sanguosha:cloneCard("ThunderSlash", card:getSuit(), card:getNumber())
		ThunderSlash:setSkillName(self:objectName())
		local wrap = sgs.Sanguosha:getWrappedCard(card:getId())
		wrap:takeOver(ThunderSlash)
		return wrap
	end,
}
leijEx = sgs.CreateTriggerSkill{
	name = "#leij",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damaged},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if damage.nature ~= sgs.DamageStruct_Thunder then return false end
		room:sendCompulsoryTriggerLog(player,"leij")
		player:drawCards(2,"leij")
		return false
	end,
}
extension:insertRelatedSkills("leij", "#leij")
--电磁炮
diancpcard = sgs.CreateSkillCard{
	name = "diancp",
	target_fixed = false,
	will_throw = true,
	filter = function(self, targets, to_select, player)
		return #targets < 1 and to_select:objectName() ~= player:objectName()
	end,
	on_effect = function(self, effect)
		effect.from:getRoom():damage(sgs.DamageStruct("diancp", effect.from, effect.to, 1, sgs.DamageStruct_Thunder))
	end
}
diancpvs = sgs.CreateOneCardViewAsSkill{
	name = "diancp",
	filter_pattern = "ThunderSlash",
	view_as = function(self, card)
	    local acard = diancpcard:clone()
		acard:addSubcard(card)
		acard:setSkillName(self:objectName())
		return acard
	end,
	enabled_at_play = function(self, player)
		return player:canDiscard(player, "he") and (not player:hasFlag("tingdian"))
	end,
	enabled_at_response = function(self, player, pattern)
		return false
	end	
}
diancp = sgs.CreateTriggerSkill{
	name = "diancp",
	events = {sgs.EnterDying},
	view_as_skill = diancpvs,
	can_trigger = function(self, player)
		return player
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local dying = data:toDying()
		local damage = dying.damage
		if damage and damage.from and dying.who:objectName() == player:objectName() and damage:getReason() == "diancp" then
			room:loseHp(damage.from)
			room:setPlayerFlag(damage.from, "tingdian")
		end
	end
}
--噩梦
Luaemeng = sgs.CreateTriggerSkill{
    name = "Luaemeng",
    frequency = sgs.Skill_NotFrequent,
    events = sgs.DamageCaused,
    on_trigger = function(self, event, player, data)
        if player:getPhase() ~= sgs.Player_Play then return end
        local room = player:getRoom()
        local damage = data:toDamage()
        local to_data = sgs.QVariant()
        to_data:setValue(damage.to)
        if room:askForCard(player, "BasicCard", "@emeng", to_data, self:objectName()) then 
			room:broadcastSkillInvoke("Luaemeng")
            local msg = sgs.LogMessage()
            msg.type = "#Luaemeng"
            msg.from = player
            msg.to:append(damage.to)
            msg.arg = damage.damage
            msg.arg2 = damage.damage+1
            room:sendLog(msg)
            damage.damage = damage.damage+1
            data:setValue(damage)
        end
    end
}
--夜战
Luayezhan = sgs.CreateTriggerSkill{
	name = "Luayezhan",
	frequency = sgs.Skill_NotFrequent,
	events = sgs.EventPhaseStart,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Draw then
			if room:askForSkillInvoke(player,self:objectName(),data) then
				local judge = sgs.JudgeStruct()
					judge.pattern = ".|black"
					judge.good = true
					judge.reason = self:objectName()
					judge.who = player
					room:judge(judge)
				if judge:isGood() then
					room:broadcastSkillInvoke("Luayezhan",1)
					room:setPlayerFlag(player, "Luayezhan")
				else
					room:broadcastSkillInvoke("Luayezhan",2)
				end
			end
		end
	end,
}
LuayezhanBuff = sgs.CreateTriggerSkill{
    name = "#LuayezhanBuff",
    events = {sgs.DamageCaused},
    on_trigger = function(self, event, player, data)
        local room=player:getRoom()
        local damage = data:toDamage()
        if not damage.by_user then return false end
        local reason = damage.card
        if reason and (reason:isKindOf("Slash") or reason:isKindOf("Duel")) then
            local msg=sgs.LogMessage()
            msg.type="#LuayezhanBuff"
            msg.from=player
            msg.to:append(damage.to)
            msg.arg=damage.damage
            msg.arg2=damage.damage+1
            room:sendLog(msg)
            room:broadcastSkillInvoke("Luayezhan",3)
            damage.damage = damage.damage + 1
            data:setValue(damage)
        end
        return false
    end,
    can_trigger = function(self, target)
        return target and target:hasFlag("Luayezhan") and target:isAlive()
    end
}
extension:insertRelatedSkills("Luayezhan","#LuayezhanBuff")
jiasushijie = sgs.CreateTriggerSkill{
	name = "jiasushijie",
	frequency = sgs.Skill_Compulsory ,
	events = {sgs.Damaged,sgs.TargetConfirmed,sgs.CardFinished},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if event == sgs.Damaged then
			local damage = data:toDamage()
			local source = room:findPlayerBySkillName(self:objectName())
			if damage.to:getPhase() ~= sgs.Player_NotActive then
				if not damage.card:isKindOf("Duel") then return false end
				if source:getMark("@Duel_source") > 0 then return false end
				room:throwEvent(sgs.TurnBroken)
				room:broadcastSkillInvoke("jiasushijie")
			end
		elseif event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			if not use.card:isKindOf("Duel") then return false end
			room:setPlayerMark(use.from,"@Duel_source",1)
			return false
		elseif event == sgs.CardFinished then
			local use = data:toCardUse()
			if not use.card:isKindOf("Duel") then return false end
			room:setPlayerMark(use.from,"@Duel_source",0)
			return false
		end
	end,
	can_trigger = function(self,target)
		return target
	end,
}
--加速对决
jiasuduijue=sgs.CreateTriggerSkill{
	name = "jiasuduijue",
	events = {sgs.TargetConfirming, sgs.TargetConfirmed},
	on_trigger = function(self,event,player,data)
		if event == sgs.TargetConfirming then
			local use = data:toCardUse()
			local room = player:getRoom()
			local s = room:findPlayerBySkillName(self:objectName())
			if not (use.card:isKindOf("Slash") and use.card:isBlack()) then return false end
			if not player:askForSkillInvoke(self:objectName(),data) then return false end
			use.to:removeOne(player)
			data:setValue(use)
			local x = use.from
			local Duel = sgs.Sanguosha:cloneCard("Duel",sgs.Card_NoSuit,0)
			Duel:setSkillName(self:objectName())
			local use = sgs.CardUseStruct()
			use.card = Duel
			use.from = x
			use.to:append(s)
			room:useCard(use)
			room:broadcastSkillInvoke("jiasuduijue",1)
		elseif event == sgs.TargetConfirmed then
			local room = player:getRoom()
			local use = data:toCardUse()
			if (player:objectName() ~= use.from:objectName()) then return false end
			if not (use.card:isKindOf("Slash") and use.card:isBlack()) then return false end
			if not player:askForSkillInvoke(self:objectName(),data) then return false end
			for _, s in sgs.qlist(use.to) do
				use.to:removeOne(s)
				data:setValue(use)
				local x = use.from
				local Duel = sgs.Sanguosha:cloneCard("Duel",sgs.Card_NoSuit,0)
				Duel:setSkillName(self:objectName())
				local use = sgs.CardUseStruct()
				use.card = Duel
				use.from = x
				use.to:append(s)
				room:useCard(use)
				room:broadcastSkillInvoke("jiasuduijue",2)
			end
		end
	end
}
--决斗加速
juedoujiasu = sgs.CreateTriggerSkill{
	name = "juedoujiasu",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.TargetConfirmed,sgs.CardFinished},
	on_trigger = function(self,event,player,data)
		local use = data:toCardUse()
		local room = player:getRoom()
		if not use.card:isKindOf("Duel") then return false end
		local source = room:findPlayerBySkillName(self:objectName())
		if event == sgs.TargetConfirmed then
			if use.from:getSeat() ~= source:getSeat() and not use.to:contains(source) then return false end
			room:sendCompulsoryTriggerLog(source,self:objectName())
			source:drawCards(1)
		else
			if use.from:getSeat() == source:getSeat() then
				room:sendCompulsoryTriggerLog(source,self:objectName())
				for _,target in sgs.qlist(use.to) do
					target:drawCards(1)
				end
			elseif use.to:contains(source) then
				room:sendCompulsoryTriggerLog(source,self:objectName())
				use.from:drawCards(1)
			end
		end
	end,
}
--蜻蜓切
qingtq = sgs.CreateProhibitSkill{
	name = "qingtq" ,
	is_prohibited = function(self, from, to, card)
		return to:hasSkill(self:objectName()) and (card:isKindOf("Weapon"))
	end
}
qingtq_keep = sgs.CreateTriggerSkill{
	name = "#qingtq",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.TargetConfirmed, sgs.CardFinished},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			if player:objectName() == use.from:objectName() then
				if player:isAlive() and player:hasSkill(self:objectName()) then
					local slash = use.card
					if slash:isKindOf("Slash") then
						for _,p in sgs.qlist(use.to) do
						if not p:isKongcheng() then
							local ai_data = sgs.QVariant()
							ai_data:setValue(p)
							room:broadcastSkillInvoke("qingtq")
							room:askForDiscard(p, self:objectName(), 1, 1, false, false)		
							end
						end
					end
				end
			end
		end
	end
}
qingtq_keep_keep = sgs.CreateAttackRangeSkill{
	name = "#qingtqAR",
	fixed_func = function(self, player, include_weapon)
		if player:hasSkill("qingtq") then
			local x = 0
			local list = player:getAliveSiblings()
			list:append(player)
			for _,p in sgs.qlist(list) do
				local hp = p:getHp()
				if hp > x then
					x = hp
				end
			end
			return x
		end
	end,
}
extension:insertRelatedSkills("qingtq","#qingtq")
extension:insertRelatedSkills("qingtq","#qingtqAR")
--翔翼
xiangyvs = sgs.CreateOneCardViewAsSkill{
	name = "xiangy",
	response_or_use = true,	
	response_pattern = "jink",
	filter_pattern = "EquipCard",
	view_as = function(self, card) 
		local jink = sgs.Sanguosha:cloneCard("jink",card:getSuit(),card:getNumber())
        jink:setSkillName(self:objectName());
        jink:addSubcard(card:getId());
        return jink
	end, 
}
xiangy = sgs.CreateTriggerSkill{
	name = "xiangy" ,
	view_as_skill = xiangyvs,
	events = {sgs.CardResponded} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardResponded then
			local resp = data:toCardResponse()
			if (resp.m_card:getSkillName() == "xiangy")then
					player:drawCards(1)
				end
			end
		return false
	end
}

--救济的祈愿
jiujideqiyuanCard = sgs.CreateSkillCard{
	name = "jiujideqiyuan",
	target_fixed = false,
	will_throw = true,
	filter = function(self,targets,to_select)
		return to_select:isWounded() and #targets < self:getSubcards():length()
	end,
	feasible = function(self,targets)
		return #targets <= self:getSubcards():length() and #targets > 0
	end,
	on_use = function(self,room,source,targets)
		for _,target in ipairs(targets) do
			room:recover(target,sgs.RecoverStruct(source))
		end
	end
}
jiujideqiyuan = sgs.CreateViewAsSkill{
	name = "jiujideqiyuan",
	n = 999,
	view_filter = function(self, selected, to_select)
		if #selected == 0 then
			return true
		elseif #selected == 1 then
			return selected[1]:getTypeId() ~= to_select:getTypeId()
		elseif #selected == 2 then
			return selected[1]:getTypeId() ~= to_select:getTypeId() and selected[2]:getTypeId() ~= to_select:getTypeId() and selected[1]:getTypeId() ~= selected[2]:getTypeId()
		end
		return false
	end,
	view_as = function(self, cards)
		if #cards == 0 then return nil end
		local vs_card = jiujideqiyuanCard:clone()
		for _,card in ipairs(cards) do
			vs_card:addSubcard(card)
		end
		vs_card:setSkillName(self:objectName())
		return vs_card
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#jiujideqiyuan")
	end
}
--法则缔造

--[[
fazededizao = sgs.CreateTriggerSkill{
	name = "fazededizao",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase()~=sgs.Player_Start then return false end
		local s = room:findPlayerBySkillName(self:objectName())
		local players = room:getOtherPlayers(s)
		if s:isKongcheng() then return false end
		if not s:askForSkillInvoke(self:objectName(),data) then return false end
		room:askForDiscard(s, self:objectName(), 1, 1, false, false)
		local result = room:askForChoice(player, "fazededizao", "Judge+Draw+Play+Discard")
		if result == "Judge" then
		room:broadcastSkillInvoke("fazededizao",1)
			for _, p in sgs.qlist(players) do
				room:setPlayerMark(p,"dizaoJudge",1)
			end
		end
		if result == "Draw" then
		room:broadcastSkillInvoke("fazededizao",2)
			for _, p in sgs.qlist(players) do
				room:setPlayerMark(p,"dizaoDraw",1)
			end
		end
		if result == "Play" then
		room:broadcastSkillInvoke("fazededizao",3)
			for _, p in sgs.qlist(players) do
				room:setPlayerMark(p,"dizaoPlay",1)
			end
		end
		if result == "Discard" then
		room:broadcastSkillInvoke("fazededizao",4)
			for _, p in sgs.qlist(players) do
				room:setPlayerMark(p,"dizaoDiscard",1)
			end
		end
		room:loseHp(player,1)
	end
}
fazededizaoskip=sgs.CreateTriggerSkill{
	name = "#fazededizaoskip",
	events = {sgs.EventPhaseStart},
	on_trigger = function(self,event,player,data)
		if player:getPhase() ~= sgs.Player_Start then return false end
		local room = player:getRoom()
		if player:getMark("dizaoJudge") > 0 then
			player:skip(sgs.Player_Judge)
			room:setPlayerMark(player,"dizaoJudge",0)
		end
		if player:getMark("dizaoDraw") > 0 then
			player:skip(sgs.Player_Draw)
			room:setPlayerMark(player,"dizaoDraw",0)
		end
		if player:getMark("dizaoPlay") > 0 then
			player:skip(sgs.Player_Play)
			room:setPlayerMark(player,"dizaoPlay",0)
		end
		if player:getMark("dizaoDiscard") > 0 then
			player:skip(sgs.Player_Discard)
			room:setPlayerMark(player,"dizaoDiscard",0)
		end
	end,
	can_trigger = function(self,target)
		return target
	end,
}
]]--旧版

	fazededizao = sgs.CreateTriggerSkill{
		name = "fazededizao",
		events = {sgs.TurnStart} ,
		on_trigger = function(self, event, player, data)
			local room = player:getRoom()
			local ori_choice = {"fzndz_1", "fzndz_2", "fzndz_3", "fzndz_4"}
			local choices = {}
			for _, effcet in ipairs(ori_choice) do
				if player:getMark(effcet) == 0  then
					table.insert(choices, effcet)
				end
			end
			if #choices == 0 then
				choices = ori_choice
				for _, effcet in ipairs(ori_choice) do
					room:setPlayerMark(player, effcet, 0)
				end
			end
			table.insert(choices, "cancel")
			local choice = room:askForChoice(player, self:objectName(), table.concat(choices, "+"))
			if choice ~= "cancel" then
				if choice == "fzndz_1" then
					room:broadcastSkillInvoke(self:objectName(), 2)
				else
					room:broadcastSkillInvoke(self:objectName(), math.random(1,4))
				end
				room:notifySkillInvoked(player, "fazededizao")
				room:setPlayerMark(player, choice, 1)
				player:setTag("fzndz", sgs.QVariant(choice))
			else
				player:setTag("fzndz", sgs.QVariant())
			end
			return false
		end,
		priority = 5,
	}

	fzndz_skip = sgs.CreateTriggerSkill{
	name = "fzndz_skip",
	events = {sgs.EventPhaseStart},
	global = true,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local madoka = room:findPlayerBySkillName("fazededizao")
		if madoka then
			local effect = madoka:getTag("fzndz"):toString()
			if effect == "fzndz_1" then
				player:skip(sgs.Player_Judge)
			elseif effect == "fzndz_2" then
				player:skip(sgs.Player_Draw)
			elseif effect == "fzndz_3" then
				player:skip(sgs.Player_Play)
			elseif effect == "fzndz_4" then
				player:skip(sgs.Player_Discard)
			end
		end
		return false
	end,
	can_trigger = function(self, target)
		return target and target:isAlive() and target:getPhase() == sgs.Player_Start
	end,
	}


--攻略之神
gonglzs = sgs.CreateTriggerSkill{
	name = "gonglzs",
	events = {sgs.TargetConfirming},
	frequency = sgs.Skill_NotFrequent,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local use = data:toCardUse()
		if not use.card:isKindOf("Slash") then return false end
		if room:askForSkillInvoke(player, self:objectName(), data) then
			local target = use.from
			local selfcard = room:askForCardChosen(player, player, "jhe", self:objectName())
			local selfplace = room:getCardPlace(selfcard)
			room:throwCard(selfcard, player, player)
			local place_char = false

			if selfplace == sgs.Player_PlaceEquip and not target:getEquips():isEmpty() then
				place_char = "e"
			elseif selfplace == sgs.Player_PlaceHand and not target:isKongcheng() then
				place_char = "h"
			elseif selfplace == sgs.Player_PlaceDelayedTrick and not target:getJudgingArea():isEmpty() then
				place_char = "j"
			end

			if place_char then
				local A = room:askForCardChosen(player, target, place_char, self:objectName())
				room:broadcastSkillInvoke(self:objectName())
				room:obtainCard(player, A, false)
			end
		end
	end,
	can_trigger = function(self, target)
		return target and target:isAlive() and target:hasSkill(self:objectName()) and not target:isAllNude()
	end,
}
--神知
shens = sgs.CreateTriggerSkill{
	name = "shens",
	events = {sgs.Dying},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local dying = data:toDying()
		if room:askForSkillInvoke(player, self:objectName(), data) then
			local ids = sgs.IntList()
			for i = 0, 3, 1 do
				ids:append(room:getDrawPile():at(i))
			end
			room:fillAG(ids)
			room:broadcastSkillInvoke(self:objectName(), math.random(1,2))
			room:getThread():delay(2500)
			local to_select = sgs.IntList()
			local splitBySuit = function(list)
				local spade, heart, club, diamond = {}, {}, {}, {}
				for _,id in sgs.qlist(list) do
					local suit = sgs.Sanguosha:getCard(id):getSuitString()
					if suit == "spade" then
						table.insert(spade, id)
					elseif suit == "heart" then
						table.insert(heart, id)
					elseif suit == "club" then
						table.insert(club, id)
					elseif suit == "diamond" then
						table.insert(diamond, id)
					end
				end
				return {spade, heart, club, diamond}
			end
			local splitByType = function(t)
				local basic, trick, equip = {}, {}, {}
				for _,id in ipairs(t) do
					local typ = sgs.Sanguosha:getCard(id):getType()
					if typ == "basic" then
						table.insert(basic, id)
					elseif typ == "trick" then
						table.insert(trick, id)
					elseif typ == "equip" then
						table.insert(equip, id)
					end
				end
				return {basic, trick, equip}
			end
			for _,s in ipairs(splitBySuit(ids)) do
				for _,t in ipairs(splitByType(s)) do
					if #t > 1 then
						for _,e in ipairs(t) do
							to_select:append(e)
						end
					end
				end
			end
			if not to_select:isEmpty() then
				room:recover(dying.who, sgs.RecoverStruct(player))
				room:fillAG(to_select)
				local card_id = room:askForAG(player, to_select, false, self:objectName())
				room:moveCardTo(sgs.Sanguosha:getCard(card_id), nil, sgs.Player_PlaceTable)
				room:takeAG(player, card_id)
				room:clearAG()
			end
			room:clearAG()
		end
	end
}
--破军歌姬
pojgjCard = sgs.CreateSkillCard{
	name = "pojgjCard",
	target_fixed = false,
	will_throw = true,
	filter = function(self, targets, to_select)
		return (#targets == 0) and (to_select:isWounded()) and (to_select:objectName() ~= sgs.Self:objectName())
	end,
	feasible = function(self, targets)
		if #targets == 1 then
			return targets[1]:isWounded()
		end
	end,
	on_use = function(self, room, source, targets)
		local target = targets[1] or source
		local effect = sgs.CardEffectStruct()
		effect.card = self
		effect.from = source
		effect.to = target
		room:cardEffect(effect)
	end,
	on_effect = function(self, effect)
		local dest = effect.to
		local room = dest:getRoom()
		local recover = sgs.RecoverStruct()
		recover.card = self
		recover.who = effect.from
		room:loseHp(effect.from)
		room:recover(dest, recover)
	end
}
pojgj = sgs.CreateViewAsSkill{
	name = "pojgj", 
	n = 0, 
	view_filter = function(self, selected, to_select)
		if #selected == 0 then
			return true
		end
		return false
	end, 
	view_as = function(self, cards) 
		if #cards == 0 then
			local card = pojgjCard:clone()
			card:setSkillName(self:objectName())
			return card
		end
	end, 
}
--魂曲
hunq = sgs.CreateTriggerSkill{
	name = "hunq" ,
	events = {sgs.HpChanged} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local s = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "hunq-invoke", true, true)
		s:drawCards(1)
		room:broadcastSkillInvoke("hunq")
	end,
	can_trigger = function(self, target)
		return target:hasSkill(self:objectName())
	end
}
--目观
LuamuguanCard=sgs.CreateSkillCard{
    name="LuamuguanCard",
    target_fixed=false,
    will_throw=true,
    skill_name="LuamuguanVS",
    filter = function(self, targets, to_select)
        return #targets == 0 and to_select:objectName() ~= sgs.Self:objectName() 
        and not to_select:hasFlag("muguan")
    end,
    on_effect = function(self,effect)
        local room=effect.from:getRoom()
        room:setPlayerMark(effect.from,"muguan",1)
        room:setPlayerMark(effect.to,"muguan",1)
        room:setFixedDistance(effect.from,effect.to,1)
		room:setFixedDistance(effect.to,effect.from,1)
    end
}

LuamuguanVS = sgs.CreateViewAsSkill{
	name = "LuamuguanVS",
	n = 1,
	view_filter = function(self,selected,to_select)
		return true
	end,
	view_as = function(self,cards)
		if #cards == 0 then return nil end
		local muguan_card=LuamuguanCard:clone()
		muguan_card:addSubcard(cards[1])
		muguan_card:setSkillName(self:objectName())
		return muguan_card
    end,
	enabled_at_play = function(self, player)
		return not player:isNude()
    end,
}
Luamuguan = sgs.CreateTriggerSkill{
	name = "#Luamuguan",
	frequency = sgs.Skill_Compulsory,
	events = sgs.EventPhaseStart,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Start and player:getMark("muguan")>0 then
			room:removePlayerMark(player,"muguan",1)
			for _,p in sgs.qlist(room:getOtherPlayers(player)) do
				if p:getMark("muguan") > 0 then
					room:removeFixedDistance(p,player,1)
					room:removeFixedDistance(player,p,1)
					player:loseMark("muguan")
					p:loseMark("muguan")
				end
			end
		end
	end
}
LuamuguanBuff = sgs.CreateTriggerSkill{
    name = "#LuamuguanBuff",
    frequency = sgs.Skill_Compulsory,
    events = sgs.DamageInflicted,
    on_trigger = function(self,event,player,data)
        local room = player:getRoom()
        local damage = data:toDamage()
		if not damage.from then return false end
        if damage.from:getMark("muguan") > 0 and damage.to:getMark("muguan") > 0 then
		if damage.from:objectName() == damage.to:objectName() then return false end
		if damage.from:hasSkill("#LuamuguanBuff") or damage.to:hasSkill("#LuamuguanBuff") then
            damage.damage = damage.damage + 1
				data:setValue(damage)
			end
        end
    end,
	can_trigger = function(self,target)
		return target
	end,
}
extension:insertRelatedSkills("LuamuguanVS", "#Luamuguan")
extension:insertRelatedSkills("LuamuguanVS", "#LuamuguanBuff")
--魂火
soulfire = sgs.CreateOneCardViewAsSkill{
	name = "soulfire",
	view_filter = function(self, card)
		if not card:isKindOf("BasicCard") then return false end
		if card:isKindOf("Peach") then return false end
		if card:isKindOf("Analeptic") then return false end
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
    		local FireSlash = sgs.Sanguosha:cloneCard("FireSlash", sgs.Card_SuitToBeDecided, -1)
        	FireSlash:addSubcard(card:getEffectiveId())
        	FireSlash:deleteLater()
        	return FireSlash:isAvailable(sgs.Self)
    	end
    	return true
	end,
	view_as = function(self, originalCard)
		local FireSlash = sgs.Sanguosha:cloneCard("FireSlash", originalCard:getSuit(), originalCard:getNumber())
		FireSlash:addSubcard(originalCard:getId())
		FireSlash:setSkillName(self:objectName())
		return FireSlash
	end,
	enabled_at_play = function(self, player)
		return sgs.Slash_IsAvailable(player)
	end, 
	enabled_at_response = function(self, player, pattern)
		return pattern == "slash"
	end
}
soulfireDamage = sgs.CreateTriggerSkill{
	name = "#soulfire",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Damage},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		local source = room:findPlayerBySkillName(self:objectName())
		if damage.card and damage.card:getSkillName() == "soulfire" then
			if damage.to:isNude() then return false end
			room:askForDiscard(damage.to,"soulfire",1,1,false,true)
			if room:askForSkillInvoke(player,"soulfire",data) then
				room:loseHp(damage.from)
				if damage.to:isNude() or (damage.to:getSeat() == source:getSeat()) or (not damage.from:isAlive()) then return false end
				local card = room:askForCardChosen(source,damage.to,"h","soulfire")
				room:obtainCard(source,card,false)
			end
			return false
		end
	end,
}
extension:insertRelatedSkills("soulfire","#soulfire")
--真红
zhenhong = sgs.CreateOneCardViewAsSkill{
	name = "zhenhong",
	view_filter = function(self, card)
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
    		local FireSlash = sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_SuitToBeDecided, -1)
        	FireSlash:addSubcard(card:getEffectiveId())
        	FireSlash:deleteLater()
        	return FireSlash:isAvailable(sgs.Self)
    	end
    	return true
	end,
	view_as = function(self, originalCard)
		local FireSlash = sgs.Sanguosha:cloneCard("fire_slash", originalCard:getSuit(), originalCard:getNumber())
		FireSlash:addSubcard(originalCard:getId())
		FireSlash:setSkillName(self:objectName())
		return FireSlash
	end,
	enabled_at_play = function(self, player)
		return false
	end, 
	enabled_at_response = function(self, player, pattern)
		return pattern == "slash"
	end
}
zhenhongslash = sgs.CreateTriggerSkill{
	name = "#zhenhongslash",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.CardUsed,sgs.ConfirmDamage ,sgs.CardFinished},
	view_as_skill = zhenhongslashvs,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local use = data:toCardUse()
		if event == sgs.CardUsed then
			if use.card:isKindOf("FireSlash") and use.card:getSkillName() == "zhenhong" then
				room:setCardFlag(use.card, "zhenhongslash")
			end
		end
		if event == sgs.ConfirmDamage  then
			local damage = data:toDamage()
			local source = room:findPlayerBySkillName(self:objectName())
			if damage.card and damage.card:isKindOf("Slash") and damage.card:getSkillName() == "zhenhong" and damage.card:hasFlag("zhenhongslash") then
				room:throwCard(room:askForCardChosen(source,damage.to, "he", "zhenhongslash", false, sgs.Card_MethodDiscard), damage.to, source.from)
			return false
			end
		if event == sgs.CardFinished then
			if use.card:hasFlag("zhenhongslash") then
				room:clearCardFlag(use.card)
			end
		end
	end
	end,
}
--断罪
duanzui = sgs.CreateTriggerSkill{
	name = "duanzui",
	frequency = sgs.Skill_NotFrequent, 
	events = {sgs.CardsMoveOneTime}, 
	on_trigger = function(self, event, player, data)
		if player:getPhase() == sgs.Player_NotActive then
			local room = player:getRoom()
			if room:getTag("FirstRound"):toBool() then return false end
			local move = data:toMoveOneTime()
			local s = room:findPlayerBySkillName(self:objectName())
			local from = move.from
			local to = move.to
			local other_players = room:getOtherPlayers(player)
			for _,p in sgs.qlist(other_players) do
				if p:getPhase() == sgs.Player_Draw then return false end
				if s:inMyAttackRange(p) and move.to_place == sgs.Player_PlaceHand then
					if to:objectName() == p:objectName() then
						local room = player:getRoom()
						for _,p in sgs.qlist(room:getAlivePlayers()) do
							if p:objectName() == to:objectName() then
								poi = p
							end
						end
						if room:askForSkillInvoke(s,self:objectName(),data) then
							room:broadcastSkillInvoke("duanzui")
							if not room:askForUseSlashTo(s, poi, "@duanzui", true, false, false) then
								return false
							end
						end
					end
				end
			end
		end
	end
}
--魅惑
meihuomoyan = sgs.CreateTriggerSkill{
	name = "meihuomoyan",
	events = {sgs.CardUsed, sgs.Damaged},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardUsed then
			local use = data:toCardUse()
			if use.to:length() ~= 1 then return end
			if use.card:isKindOf("SkillCard") or use.card:isKindOf("EquipCard") or use.card:isKindOf("DelayedTrick") then return end
			local targets = sgs.SPlayerList()
			for _,all in sgs.qlist(room:getAllPlayers()) do
				if not room:isProhibited(use.from, all, use.card) then
					targets:append(all)
				end
			end
			for _,p in sgs.qlist(room:getAllPlayers()) do
				if p:hasSkill(self:objectName()) and use.from:getMark("mhmy"..p:objectName()) == 0 then
					if p:askForSkillInvoke(self:objectName(), data) then
						for _,to in sgs.qlist(use.to) do
							use.to:removeOne(to)
						end
						local target = room:askForPlayerChosen(p, targets, self:objectName(), "meihuomoyaninvoke:"..use.card:objectName(), true, true)
						if target then
							use.to:append(target)
						end
						room:addPlayerMark(use.from, "mhmy"..p:objectName())
						data:setValue(use)
						room:broadcastSkillInvoke("meihuomoyan")
					end
				end
			end
		elseif event == sgs.Damaged then
			local damage = data:toDamage()
			if damage.to:hasSkill(self:objectName()) and damage.from:getMark("mhmy"..damage.to:objectName()) > 0 then
				room:setPlayerMark(damage.from, "mhmy"..damage.to:objectName(), 0)
			end
		end
	end,
	can_trigger = function(self, target)
		return target
	end
}
--千变万化镜
kaleidoscope = sgs.CreateTriggerSkill{
	name = "kaleidoscope",
	events = {sgs.EventPhaseStart, sgs.GameStart},
	on_trigger = function(self, event, player, data, room)
		local targets = sgs.SPlayerList()
		for _,p in sgs.qlist(room:getOtherPlayers(player)) do
            local detachList = {}
            for _,skill in sgs.qlist(p:getVisibleSkillList()) do
				if not skill:inherits("SPConvertSkill") and not skill:isAttachedLordSkill() then
                    table.insert(detachList, skill:objectName())
                end
            end
			if #detachList > 0 then
				targets:append(p)
			end
		end
		if (event == sgs.GameStart or (event == sgs.EventPhaseStart and (player:getPhase() == sgs.Player_RoundStart or player:getPhase() == sgs.Player_NotActive))) and not targets:isEmpty() then
			player:speak("秘密不要被发现了")
			local target = room:askForPlayerChosen(player, targets, self:objectName(), "@kaleidoscope", true, true)
			if target then
				local skills = {}
				room:broadcastSkillInvoke(self:objectName())
				for _,skill in sgs.qlist(target:getVisibleSkillList()) do
					if not skill:inherits("SPConvertSkill") and not skill:isAttachedLordSkill() then
						table.insert(skills, skill:objectName())
					end
				end
				local choice = room:askForChoice(player, self:objectName(), table.concat(skills, "+"))
				local name = ""
				for _, m in sgs.list(player:getMarkNames()) do
					if player:getMark(m) > 0 and string.find(m, "kaleidoscope") then
						room:removePlayerMark(player, m)
						local new = string.sub(m, 13, string.len(m))
						if new ~= choice then
							name = "|-"..new
						end
					end
				end
				room:handleAcquireDetachSkills(player, choice..name)
				room:addPlayerMark(player, "kaleidoscope"..choice)
			end
		end
	end
}
--赝造魔女
haniel = sgs.CreateTriggerSkill{
	name = "haniel",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseStart, sgs.EventAcquireSkill, sgs.EventLoseSkill},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart and player:getPhase() == sgs.Player_Start then
			if player:getMark(self:objectName()) > 0 then
				room:removePlayerMark(player, self:objectName())
				for _,skill in sgs.qlist(player:getVisibleSkillList()) do
					if not skill:inherits("SPConvertSkill") and not skill:isAttachedLordSkill() and skill:objectName() ~= self:objectName() then
						room:removePlayerMark(player, "Qingcheng"..skill:objectName())
					end
				end
			end
			if not room:askForCard(player, "..", "@haniel", data, self:objectName()) then
				room:addPlayerMark(player, self:objectName())
				player:drawCards(1, self:objectName())
				for _,skill in sgs.qlist(player:getVisibleSkillList()) do
					if not skill:inherits("SPConvertSkill") and not skill:isAttachedLordSkill() and skill:objectName() ~= self:objectName() then
						room:addPlayerMark(player, "Qingcheng"..skill:objectName())
					end
				end
			end
		end
		if event == sgs.EventAcquireSkill and player:getMark(self:objectName()) > 0 then
			room:addPlayerMark(player, "Qingcheng"..data:toString())
		end
		if event == sgs.EventLoseSkill and player:getMark(self:objectName()) > 0 then
			room:removePlayerMark(player, "Qingcheng"..data:toString())
		end
	end
}
--畸意技能卡
jiyiCard = sgs.CreateSkillCard{
	name = "jiyi",
	target_fixed = true,
	feasible = function(self)
		return true
	end,
	on_use = function(self,room,source,targets)
		room:loseHp(source)
		local card_ids = room:getNCards(4)
		local to_get = sgs.IntList()
		for i = 1,2,1 do
			room:fillAG(card_ids,source)
			local choice = room:askForAG(source,card_ids,false,self:objectName())
			room:clearAG()
			card_ids:removeOne(choice)
			to_get:append(choice)
		end
		local dummy = sgs.Sanguosha:cloneCard("slash",sgs.Card_NoSuit,0)
		dummy:addSubcards(to_get)
		source:obtainCard(dummy,false)
		local choice = room:askForChoice(source,self:objectName(),"jiyi_throw+jiyi_guanxing")
		if choice == "jiyi_throw" then
			dummy:clearSubcards()
			dummy:addSubcards(card_ids)
			local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_NATURAL_ENTER,source:objectName(),self:objectName(),"")
			room:throwCard(dummy,reason,nil,source)
		else
			room:askForGuanxing(source,card_ids,sgs.Room_GuanxingUpOnly)
		end
	end,
}
--观察
guanchaCard = sgs.CreateSkillCard{
	name = "guancha",
	will_throw = false,
	filter = function(self,targets,to_select)
		return #targets < 1 and self:getSubcards():length() == (math.floor((sgs.Self:getHandcardNum() + 1) / 2)) and to_select:objectName() ~= sgs.Self:objectName()
	end,
	feasible = function(self,targets)
		return #targets == 1 and self:getSubcards():length() == math.floor((sgs.Self:getHandcardNum() + 1) / 2)
	end,
	on_use = function(self,room,source,targets)
		targets[1]:obtainCard(self,false)
		local recover = sgs.RecoverStruct(source,nil,1)
		room:recover(source,recover)
	end,
}
guancha = sgs.CreateViewAsSkill{
	name = "guancha",
	n = 999,
	view_filter = function(self,selected,to_select)
		return #selected < math.floor((sgs.Self:getHandcardNum() + 1) / 2) and not to_select:isEquipped()
	end,
	view_as = function(self,cards)
		if #cards == 0 then return nil end
		local vs_card = guanchaCard:clone()
		for _,card in ipairs(cards) do
			vs_card:addSubcard(card)
		end
		vs_card:setSkillName(self:objectName())
		return vs_card
	end,
	enabled_at_play = function(self,player)
		return (not player:isKongcheng()) and not player:hasUsed("#guancha")
	end,
}
--畸意
jiyiVS = sgs.CreateZeroCardViewAsSkill{
	name = "jiyi",
	response_pattern = "@@jiyi",
	view_as = function(self)
		local card = jiyiCard:clone()
		card:setSkillName(self:objectName())
		return card
	end,
}
jiyi = sgs.CreateTriggerSkill{
	name = "jiyi",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.HpRecover},
	view_as_skill = jiyiVS,
	on_trigger = function(self,event,player,data)
		if player:getHp() < 1 then return false end
		local room = player:getRoom()
		room:askForUseCard(player,"@@jiyi","@jiyi")
	end,
}

--一航
Luayihang = sgs.CreateDistanceSkill{
    name = "Luayihang",
    correct_func = function(self, from, to)
        if from:hasSkill(self:objectName()) then
            return -1
        end
    end
}
Luayihangpai = sgs.CreateMaxCardsSkill{
	name = "#Luayihangpai",
	extra_func = function(self, target)
		if target:hasSkill(self:objectName()) then
			local x = target:getLostHp()
			return 2*x
		end
		return 0
	end
}

--吃撑
Luachicheng = sgs.CreateTriggerSkill{
	name = "Luachicheng" ,
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.DrawNCards} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:askForSkillInvoke(player, self:objectName(), data) then
			room:broadcastSkillInvoke("Luachicheng")
			room:setPlayerCardLimitation(player, "use", "Slash", true)
			data:setValue(data:toInt() + 2)
		end
	end
}
--主角修正
zhujuexzCard = sgs.CreateSkillCard{
	name = "zhujuexz",
	will_throw = true,
	target_fixed = true,
	on_use = function(self,room,source)
		local x = 0
		local num = 0
		local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TURNOVER,source:objectName(),self:objectName(),"")
		for _,card_id in sgs.qlist(self:getSubcards()) do
			x = x + sgs.Sanguosha:getCard(card_id):getNumber()
		end
		local dummy = sgs.Sanguosha:cloneCard("Slash",sgs.Card_NoSuit,0)
		while num <= x do
			local card_id = room:getNCards(1):first()
			num = num + sgs.Sanguosha:getCard(card_id):getNumber()
			dummy:addSubcard(card_id)
			room:moveCardTo(sgs.Sanguosha:getCard(card_id),nil,sgs.Player_PlaceTable,reason)
			room:getThread():delay(500)
		end
		room:obtainCard(source,dummy)
		dummy:deleteLater()
	end,
}
zhujuexz = sgs.CreateViewAsSkill{
	name = "zhujuexz",
	n = 999,
	view_filter = function(self,selected,to_select)
		return sgs.Self:canDiscard(sgs.Self,to_select:getId())
	end,
	view_as = function(self,cards)
		if #cards < 1 then return nil end
		local vs_card = zhujuexzCard:clone()
		for _,card in ipairs(cards) do
			vs_card:addSubcard(card)
		end
		vs_card:setSkillName(self:objectName())
		return vs_card
	end,
	enabled_at_play = function(self,player)
		return not player:hasUsed("#zhujuexz")
	end,
}
--轮回的宿命
lunhui1 = sgs.CreateTriggerSkill{ 
 	name = "#lunhui1", 
 	frequency = sgs.Skill_NotFrequent, 
 	events = {sgs.CardsMoveOneTime, sgs.FinishJudge}, 
 	on_trigger = function(self, event, player, data) 
 		local room = player:getRoom() 
 		if event == sgs.CardsMoveOneTime then 
 			local move = data:toMoveOneTime() 
 			if (move.from and (move.from:objectName() == player:objectName()) and (move.from_places:contains(sgs.Player_PlaceHand) or  move.from_places:contains(sgs.Player_PlaceEquip))) and not (move.to and (move.to:objectName() == player:objectName() and (move.to_place == sgs.Player_PlaceHand or move.to_place == sgs.Player_PlaceEquip))) then 
 				if not player:askForSkillInvoke("lunhui1", data) then return end 
				room:broadcastSkillInvoke("lunhui")
 				local judge = sgs.JudgeStruct() 
 				judge.pattern = ".|black" 
 				judge.good = true 
 				judge.reason = self:objectName() 
 				judge.who = player 
 				room:judge(judge)
 			end 
 		elseif event == sgs.FinishJudge then 
 			local judge = data:toJudge() 
			if judge.reason == self:objectName() and judge:isGood() and room:getCardPlace(judge.card:getEffectiveId()) == sgs.Player_PlaceJudge then 
 				room:drawCards(player,1, self:objectName())
 			end 
 		end 
	end, 
 	can_trigger = function(self, target) 
 		return target and target:isAlive() and target:hasSkill(self:objectName()) and target:getPhase() == sgs.Player_NotActive 
 	end 
}
lunhuivs = sgs.CreateZeroCardViewAsSkill{ 
 	name = "lunhui", 
 	view_as = function() 
 		local id = sgs.Sanguosha:getCard(sgs.Self:getMark("lunhui"))
		local acard = sgs.Sanguosha:cloneCard(id:objectName(), id:getSuit(), id:getNumber())
		acard:setSkillName("lunhui")
		return acard
 	end ,
	enabled_at_play = function(self,player)
		return false
	end,
	response_pattern = "@@lunhui",
} 
lunhui = sgs.CreateTriggerSkill{ 
 	name = "lunhui", 
	events = {sgs.CardsMoveOneTime}, 
	view_as_skill = lunhuivs,
 	on_trigger = function(self, event, player, data) 
		local room = player:getRoom() 
		local move = data:toMoveOneTime() 
		local flag = bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) 
		if (flag == sgs.CardMoveReason_S_REASON_DISCARD) and (move.from and (move.from:objectName() == player:objectName()) and (move.from_places:contains(sgs.Player_PlaceHand) or  move.from_places:contains(sgs.Player_PlaceEquip))) and not (move.to and (move.to:objectName() == player:objectName() and (move.to_place == sgs.Player_PlaceHand or move.to_place == sgs.Player_PlaceEquip))) then 
			for _,id in sgs.qlist(move.card_ids) do
			    local card = sgs.Sanguosha:getCard(id)
				if card:isAvailable(player) and card:isRed() and (card:isKindOf("BasicCard") or card:isNDTrick()) then
					if card:isKindOf("Slash") and player:hasUsed("Slash") then return true end
					room:setPlayerMark(player,"lunhui",id)
					local use = room:askForUseCard(player, "@@lunhui", ("#lunhui:%s:%s:%s:%s"):format(card:objectName(),card:getSuitString(),card:getNumber(),card:getEffectiveId()),-1,sgs.Card_MethodUse,false)
					if use then
					end
				end
			end
		end
		room:setPlayerMark(player,"lunhui",0)
	end, 
	can_trigger = function(self, target) 
		return target and target:isAlive() and target:hasSkill(self:objectName()) and target:getPhase() == sgs.Player_Play 
	end
}

--破除的束缚
pocdsfcard = sgs.CreateSkillCard{
	name = "pocdsf",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		local n = 0
		for _,p in sgs.qlist(room:getOtherPlayers(source)) do
			local weapon = p:getWeapon()
			if weapon then
				n = n + 1
				room:throwCard(weapon:getRealCard(), p, source)
			end
		end
		if n > 0 and not source:isNude() then
			local hands = source:getCards("he"):length()
			if hands <= n then
				source:throwAllHandCardsAndEquips()
			else
				room:askForDiscard(source, self:objectName(), n, n, false, true)
			end
		end
	end,
}
pocdsf = sgs.CreateZeroCardViewAsSkill{
	name = "pocdsf",
	view_as = function()
		local card = pocdsfcard:clone()
		card:setSkillName(self:objectName())
		return card
	end , 
	enabled_at_play = function(self,player)
		return player:getMark("@poi") >= 0 and not player:hasUsed("#pocdsfcard")
	end
}
--合法萝莉
lolita = sgs.CreateTriggerSkill{
	name = "lolita",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardUsed},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local use = data:toCardUse()
		if (not use.from) or use.from:isDead() then return end
		if use.to:length() ~= 1 then return end
		if use.card and use.card:isKindOf("Slash") then
			local targets = sgs.SPlayerList()
			for _,p in sgs.qlist(room:getOtherPlayers(player)) do
				local uslash = false
				if p:hasSkill(self:objectName()) and (not use.to:contains(p)) and use.from:objectName() ~= p:objectName() then
					for _,i in sgs.qlist(use.to) do
						if i:inMyAttackRange(p) then
							uslash = true
						end
					end
					if (not room:isProhibited(player, p, use.card)) and uslash then
						targets:append(p)
					end
				end
			end
			if not targets:isEmpty() then
				for _,target in sgs.qlist(targets) do
					use.to:append(target)
				end
				data:setValue(use)
				room:broadcastSkillInvoke("lolita", math.random(1, 2))
			end
		end
	end,
	can_trigger = function(self, target)
		return target
	end
}
--犹超级大
judas = sgs.CreateTriggerSkill{
	name = "judas",
	frequency = sgs.Skill_Frequent, 
	events = {sgs.TargetConfirming},
	on_trigger = function(self, event, player, data)
		if player:getPhase() == sgs.Player_NotActive then
			local room = player:getRoom()
			local use = data:toCardUse()
			if use.card and use.card:isKindOf("Slash") and use.to:contains(player) then
				if room:askForSkillInvoke(player, self:objectName(), data) then
					if not room:askForUseSlashTo(player, use.from, "@judaseffect:"..use.from:objectName(), false) then
						player:drawCards(1)
					end
					room:broadcastSkillInvoke("judas")
				end
			end
		end
	end
}
--原典
yuandian = sgs.CreateTriggerSkill{
	name = "yuandian",
	events = {sgs.BuryVictim},
	frequency = sgs.Skill_Compulsory,
	priority = -2,
	can_trigger = function(target)
		return target
	end,
	on_trigger = function(self, event, player, data)
		local death = data:toDeath()
		local room = player:getRoom()
		if death.damage and death.damage.from and death.damage.from:hasSkill(self:objectName()) then
			local x = death.damage.from:getAttackRange()
				room:broadcastSkillInvoke("yuandian")
				death.damage.from:drawCards(2*x, self:objectName())
			end
		return false
	end,
}
--魔性
moxing = sgs.CreateTriggerSkill{
	name = "moxing",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DrawNCards},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local x = player:getLostHp()
		if x > 0 then
			room:broadcastSkillInvoke("moxing")
			local count = data:toInt() + x
			data:setValue(count)
		end
    end
}
moxingMC = sgs.CreateMaxCardsSkill{
	name = "#moxing",
	extra_func = function(self, target)
		if target:hasSkill("moxing") then
			local x = 5
			return x
		end
		return 0
	end
}
extension:insertRelatedSkills("moxing","#moxing")
--祥瑞
xiangruiCard = sgs.CreateSkillCard{
	name = "xiangruiCard",
	target_fixed = false,
	will_throw = true,
	filter = function(self, targets, to_select)
		if to_select:objectName() == sgs.Self:objectName() then return false end
		return #targets < 1
	end,
	on_effect = function(self, effect)
		local room = effect.to:getRoom()
		room:damage(sgs.DamageStruct(self:objectName(), effect.from, effect.to))
	end
}
xiangruiVS = sgs.CreateViewAsSkill{
	name = "xiangrui",
	n = 1,
	view_filter = function(self, selected, to_select)
		return true
	end,
	view_as = function(self, cards)
		if #cards == 1 then 
			local xiangrui_card = xiangruiCard:clone()
			xiangrui_card:addSubcard(cards[1])
			xiangrui_card:setSkillName(self:objectName())
			return xiangrui_card
		end
	end,
	enabled_at_play = function(self, player)
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@xiangrui"
	end,
}
xiangrui = sgs.CreateTriggerSkill{
	name = "xiangrui",
	frequency = sgs.Skill_NotFrequent,
	events = sgs.DamageInflicted,
	view_as_skill = xiangruiVS,
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if not player:askForSkillInvoke(self:objectName(),data) then return end
		local damage = data:toDamage()
		local judge = sgs.JudgeStruct()
		judge.pattern = ".|."
		judge.good = true
		judge.reason = self:objectName()
		judge.who = player
		room:judge(judge)
		if judge.card:isRed() then
			damage.damage = damage.damage - 1
			room:broadcastSkillInvoke("xiangrui",1)
			sendLog("#xiangrui",room,player,damage.damage)
			data:setValue(damage)
			if damage.damage < 1 then
				return true
			end
		elseif judge.card:isBlack() then
			room:askForUseCard(player, "@@xiangrui", "@xiangrui", -1, sgs.Card_MethodNone)
			room:broadcastSkillInvoke("xiangrui",2)
		end
	end
}
--无能力者
wnlz = sgs.CreateTriggerSkill{
	name = "wnlz",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageInflicted},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local damage = data:toDamage()
		local card = damage.card
		if (not card) or card:isVirtualCard() then
			damage.damage = damage.damage - 1
			sendLog("#wnlz-down",room,player,damage.damage)
			room:broadcastSkillInvoke(self:objectName(),math.random(1,2))
		end
		if card and card:isKindOf("Slash") then
			damage.damage = damage.damage + 1
			sendLog("#wnlz-up",room,player,damage.damage)
			room:broadcastSkillInvoke(self:objectName(),math.random(1,2))
		end
		data:setValue(damage)
		if damage.damage <= 0 then return true end
		return false
	end,
}

hxssVS = sgs.CreateOneCardViewAsSkill{
	name = "hxss",
	filter_pattern = "BasicCard",
	view_as = function(self, card) 
		local zqzp = sgs.Sanguosha:cloneCard("mouthgun", card:getSuit(), card:getNumber())
		zqzp:addSubcard(card)
		zqzp:setSkillName("hxss")
		return zqzp
	end,
	enabled_at_play = function(self,player)
		return not player:hasFlag("used_hxss")
	end,
}

hxss_mod = sgs.CreateTargetModSkill{
	name = "hxss_mod",
	distance_limit_func = function(self, from, card)
		if card:getSkillName() == "hxss" then
			return 1000
		else
			return 0
		end
	end
}

--[[hxssCard = sgs.CreateSkillCard{
	name = "hxss",
	target_fixed = false,
	will_throw = true,
	filter = function(self,targets,to_select)
		return #targets < 1 and sgs.Self:getSeat() ~= to_select:getSeat()
	end,
	feasible = function(self,targets)
		return #targets == 1
	end,
	on_use = function(self,room,source,targets)
		local target = targets[1]
		local skilllist = target:getVisibleSkillList()
		local skills = {}
		for _,skill in sgs.qlist(skilllist) do
			local name = skill:objectName()
			room:setPlayerMark(target,"Qingcheng"..name,1)
			if not table.contains(skills,name) then table.insert(skills,name) end
		end
		room:setTag(self:objectName().."-skills",sgs.QVariant(table.concat(skills,"+")))
		room:setPlayerMark(target,self:objectName(),1)
	end,
}
hxssVS = sgs.CreateViewAsSkill{
	name = "hxss",
	n = 0,
	view_as = function(self,cards)
		local card = hxssCard:clone()
		card:setSkillName("hxss")
		return card
	end,
	enabled_at_play = function(self,player)
		return not player:hasUsed("#hxss")
	end,
}

hxss = sgs.CreateTriggerSkill{
	name = "hxss",
	view_as_skill = hxssVS,
	events = {sgs.EventPhaseChanging, sgs.EventLoseSkill, sgs.EventAcquireSkill},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local source = room:findPlayerBySkillName(self:objectName())
		local target
		for _,aplayer in sgs.qlist(room:getOtherPlayers(source)) do
			if aplayer:getMark("hxss") > 0 then
				target = aplayer
				break
			end
		end
		if not target then return false end
		if event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to ~= sgs.Player_NotActive then return false end
			local tag = room:getTag("hxss-skills")
			if not tag then return false end
			local skills = tag:toString()
			local skill_list = skills:split("+")
			for _,skill_name in ipairs(skill_list) do
				room:setPlayerMark(target,"Qingcheng"..skill_name, 0)
			end
			room:removeTag("hxss-skills")
			room:setPlayerMark(target,"hxss",0)
		elseif event == sgs.EventLoseSkill then
			if player:getMark("hxss") == 0 then return false end
			local skill_name = data:toString()
			local tag = room:getTag("hxss-skills")
			if not tag then return false end
			local skill_list = tag:toString():split("+")
			if not table.contains(skill_list,skill_name) then return false end
			room:setPlayerMark(target,"Qingcheng"..skill_name,0)
			local skills = {}
			for _,skill in ipairs(skill_list) do
				if skill ~= skill_name then table.insert(skills,skill) end
			end
			if #skills == 0 then
				room:removeTag("hxss-skills")
				return false
			end
			room:setTag("hxss-skills",sgs.QVariant(table.concat(skills,"+")))
		elseif event == sgs.EventAcquireSkill then
			if player:getMark("hxss") == 0 then return false end
			local skill_name = data:toString()
			local tag = room:getTag("hxss-skills")
			if not tag then
				room:setTag("hxss-skills",sgs.QVariant(skill_name))
				tag = room:getTag("hxss-skills")
				return false
			end
			local skill_list = tag:toString():split("+")
			if table.contains(skill_list,skill_name) then return false end
			table.insert(skill_list,skill_name)
			room:setPlayerMark(player,"Qingcheng"..skill_name,1)
			room:setTag("hxss-skills",sgs.QVariant(table.concat(skill_list,"+")))
		end
	end,
	can_trigger = function(self,target)
		return target
	end,
}]]

hxss = sgs.CreateTriggerSkill{
	name = "hxss",
	view_as_skill = hxssVS,
	events = {sgs.Pindian, sgs.EventPhaseEnd, sgs.TrickEffect},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.Pindian then
			local pindian = data:toPindian()
			if pindian.from:hasFlag("hxss") and pindian.to and pindian.from_number > pindian.to_number then
				room:setPlayerMark(pindian.to, "@hxss_skill_valid", 1)
				room:setPlayerFlag(pindian.from, "used_hxss")
			end
		elseif event == sgs.EventPhaseEnd then
			if player:getPhase() ~= sgs.Player_Finish then return false end
			for _, p in sgs.qlist(room:getAllPlayers()) do
				room:setPlayerMark(p, "@hxss_skill_valid", 0)
			end
		elseif event == sgs.TrickEffect then
			local trick = data:toCardEffect()
			if trick.card:isKindOf("mouthgun") and trick.from:hasSkill(self:objectName()) and trick.card:getSkillName() == "hxss" then
				room:setPlayerFlag(trick.from, "hxss")
			end
		end
	end,
	can_trigger = function(self,target)
		return target
	end,
}

hxss_i = sgs.CreateInvaliditySkill{
	name = "hxss_i",
	skill_valid = function(self, player, skill)
		if player:getMark("@hxss_skill_valid") >= 1 then
			return false
		else
			return true
		end
	end
}


--厨艺Max
Luachuyi = sgs.CreateViewAsSkill{
	name = "Luachuyi",
	n = 1,
	view_filter = function(self, selected, to_select)
		if #selected == 0 then
			return to_select:isKindOf("EquipCard")
		end
		return false
	end,
	view_as = function(self, cards)
		if #cards == 1 then
			local card = cards[1]
			local suit = card:getSuit()
			local point = card:getNumber()
			local id = card:getId()
			local peach = sgs.Sanguosha:cloneCard("peach", suit, point)
			peach:setSkillName(self:objectName())
			peach:addSubcard(id)
			return peach
		end
		return nil
	end,
	enabled_at_play = function(self, player)
		local peach = sgs.Sanguosha:cloneCard("peach", sgs.Card_SuitToBeDecided, -1)
		peach:deleteLater()
		return peach:isAvailable(player)
	end,
	enabled_at_response = function(self, player, pattern)
		return string.find(pattern, "peach")
	end
}
--闪光连击
Lualianji = sgs.CreateTriggerSkill{
	name = "Lualianji",
	events = {sgs.TargetSpecified} ,
	frequency = sgs.Skill_NotFrequent,
	on_trigger = function(self, event, player, data)
	local room = player:getRoom()
		local use = data:toCardUse()
		if not use.card:isKindOf("Slash") then return false end
		player:drawCards(1)
		local card = room:askForUseCard(player, "TrickCard+^Nullification,EquipCard|.|.|hand", "@Luajiewei")
		if not card then return false end
		local targets = sgs.SPlayerList()
		if card:getTypeId() == sgs.Card_TypeTrick then
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				local can_discard = false
				for _, judge in sgs.qlist(p:getJudgingArea()) do
					if (judge:getTypeId() == sgs.Card_TypeTrick) and (player:canDiscard(p, judge:getEffectiveId())) then
						can_discard = true
						break
					elseif judge:getTypeId() == sgs.Card_TypeSkill then
						local real_card = sgs.Sanguosha:getEngineCard(judge:getEffectiveId())
						if (real_card:getTypeId() == sgs.Card_TypeTrick) and (player:canDiscard(p, real_card:getEffectiveId())) then
							can_discard = true
							break
						end
					end
				end
				if can_discard then targets:append(p) end
			end
		elseif (card:getTypeId() == sgs.Card_TypeEquip) then
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if (not p:getEquips():isEmpty()) and (player:canDiscard(p, "e")) then
					targets:append(p)
				else
					for _, judge in sgs.qlist(p:getJudgingArea()) do
						if judge:getTypeId() == sgs.Card_TypeSkill then
							local real_card = Sanguosha:getEngineCard(judge:getEffectiveId())
							if (real_card:getTypeId() == sgs.Card_TypeEquip) and (player:canDiscard(p, real_card:getEffectiveId())) then
								targets:append(p)
								break
							end
						end
					end
				end
			end
		end
	end	
}
--空之女王
kznwCard = sgs.CreateSkillCard{
	name = "kznw",
	will_throw = false,
	target_fixed = true,
	feasible = function(self,targets)
		return true
	end,
	on_use = function(self,room,source,targets)
		local card_ids = self:getSubcards()
		source:addToPile("bian",card_ids,false)
		return false
	end,
}
kznwVS = sgs.CreateViewAsSkill{
	name = "kznw",
	n = 999,
	view_filter = function(self,selected,to_select)
		return to_select:hasFlag(self:objectName())
	end,
	view_as = function(self,cards)
		if #cards < 1 then return nil end
		local vs_card = kznwCard:clone()
		for _,card in ipairs(cards) do
			vs_card:addSubcard(card)
		end
		vs_card:setSkillName(self:objectName())
		return vs_card
	end,
	enabled_at_play = function(self,player)
		return false
	end,
	enabled_at_response = function(self,player,pattern)
		return pattern == "@@kznw"
	end,
}
kznw = sgs.CreateTriggerSkill{
	name = "kznw",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.CardsMoveOneTime},
	view_as_skill = kznwVS,
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local move = data:toMoveOneTime()
		if not player:hasSkill(self:objectName()) then return false end
		if move.to:getSeat() ~= player:getSeat() then return false end
		if room:getTag("FirstRound"):toBool() then return false end
		if player:getPhase() == sgs.Player_NotActive and move.to and move.to_place == sgs.Player_PlaceHand and not (move.from and move.from:getSeat() == player:getSeat() and move.from_places:length() == 1 and move.from_places.contains(sgs.Player_PlaceHand)) then
			for _,card_id in sgs.qlist(move.card_ids) do
				room:setCardFlag(card_id,self:objectName())
			end
			room:askForUseCard(player,"@@kznw","@kznw")
			for _,card_id in sgs.qlist(move.card_ids) do
				room:setCardFlag(card_id,"-"..self:objectName())
			end
		end
	end,
}
geassUseCard = sgs.CreateSkillCard{
	name = "geass",
	will_throw = false,
	target_fixed = function(self)
		return sgs.Sanguosha:getCard(self:getSubcards():first()):targetFixed()
	end,
	filter = function(self,targets,to_select)
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		if card:targetFixed() then return false end
		--[[local playerlist = sgs.Self:getAliveSiblings()
		local target
		for _,player in sgs.qlist(playerlist) do
			if player:getMark("geass_target") > 0 then
				target = player
				break
			end
		end
		playerlist = Table2Playerlist(targets)]]
		return #targets < 1
	end,
	feasible = function(self,targets)
		--[[local playerlist = sgs.Self:getAliveSiblings()
		local target
		for _,player in sgs.qlist(playerlist) do
			if player:getMark("geass_target") > 0 then
				target = player
				break
			end
		end
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		if card:targetFixed() then return true end
		playerlist = Table2Playerlist(targets)
		return card:targetsFeasible(playerlist,target)]]--
		return true
	end,
	on_use = function(self,room,source,targets)
		local playerlist = room:getAlivePlayers()
		local target
		for _,player in sgs.qlist(playerlist) do
			if player:getMark("geass_target") > 0 then
				target = player
				break
			end
		end
		local splayerlist = Table2SPlayerlist(targets)
		local use = sgs.CardUseStruct(sgs.Sanguosha:getCard(self:getSubcards():first()),target,splayerlist,true)
		room:useCard(use)
		return false
	end,
}
geassCard = sgs.CreateSkillCard{
	name = "geasstarget",
	target_fixed = false,
	filter = function(self,targets,to_select)
		return #targets < 1 and to_select:getSeat() ~= sgs.Self:getSeat() and not to_select:isKongcheng()
	end,
	feasible = function(self,targets)
		return #targets == 1
	end,
	on_use = function(self,room,source,targets)
		local target = targets[1]
		local card_list = target:getCards("h")
		local playerlist = room:getAlivePlayers()
		local can_use = false
		local disabled_list = sgs.IntList()
		for _,card in sgs.qlist(card_list) do
			if card:targetFixed() and card:isAvailable(target) then can_use = true
			elseif card:targetFixed() then
				disabled_list:append(card:getId())
			else
				local enabled = false
				for _,player in sgs.qlist(playerlist) do
					if card:targetFilter(sgs.PlayerList(),player,target) and not target:isProhibited(player,card) then
						enabled = true
						can_use = true
						break
					end
				end
				if not enabled then disabled_list:append(card:getId()) end
			end
		end
		sendLog("#geass",room,source,nil,nil,target)
		if not can_use then
			room:showAllCards(target,source)
			return false
		else
			local card_ids = sgs.IntList()
			for _,card in sgs.qlist(card_list) do
				card_ids:append(card:getId())
			end
			room:fillAG(card_ids,source,disabled_list)
			local card_id = room:askForAG(source,card_ids,true,"geass")
			room:clearAG(source)
			if card_id == -1 then return false end
			room:setPlayerMark(source,"geassused",1)
			room:setPlayerMark(target,"geass_target",1)
			room:setPlayerMark(source,"geass",card_id)
			room:askForUseCard(source,"@@geass","@geass")
			room:setPlayerMark(source,"geass",0)
			room:setPlayerMark(target,"geass_target",0)
			room:setPlayerMark(source,"geassused",0)
			return false
		end
	end,
}
geass = sgs.CreateViewAsSkill{
	name = "geass",
	n = 0,
	view_as = function(self,cards)
		if sgs.Self:getMark("geassused") > 0 then
			local card = sgs.Sanguosha:getCard(sgs.Self:getMark("geass"))
			local vs_card = geassUseCard:clone()
			vs_card:addSubcard(card)
			vs_card:setSkillName(self:objectName())
			return vs_card
		else
			return geassCard:clone()
		end
	end,
	enabled_at_play = function(self,player)
		return not player:hasUsed("#geasstarget")
	end,
	enabled_at_response = function(self,player,pattern)
		return pattern == "@@geass"
	end,
}
--智能AI
znaiCard = sgs.CreateSkillCard{
	name = "znai",
	target_fixed = false,
	filter = function(self,targets,to_select)
		return #targets < 1 and to_select:hasEquip()
	end,
	feasible = function(self,targets)
		return #targets == 1
	end,
	on_use = function(self,room,source,targets)
		local card_id = room:askForCardChosen(source,targets[1],"e",self:objectName())
		room:setPlayerMark(source,self:objectName(),card_id)
		return false
	end,
}
znaiVS = sgs.CreateViewAsSkill{
	name = "znai",
	n = 0,
	response_pattern = "@@znai",
	view_as = function(self,cards)
		local card = znaiCard:clone()
		card:setSkillName(self:objectName())
		return card
	end,
}
znai = sgs.CreateTriggerSkill{
	name = "znai",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.CardsMoveOneTime},
	view_as_skill = znaiVS,
	on_trigger = function(self,event,player,data)
		if player:hasFlag(self:objectName()) then return false end
		local move = data:toMoveOneTime()
		if (not move.from) or move.from:getSeat() == player:getSeat() or move.from:isDead() or (not move.is_last_handcard) then return false end
		local room = player:getRoom()
		local target = room:findPlayer(move.from:getGeneralName())
		if not room:askForSkillInvoke(player,self:objectName()) then return false end
		local can_choose = false
		for _,aplayer in sgs.qlist(room:getAlivePlayers()) do
			if aplayer:hasEquip() then
				can_choose = true
				break
			end
		end
		local choice = "znai2"
		if can_choose then choice = room:askForChoice(player,self:objectName(),"znai1+znai2") end
		if choice == "znai1" then
			room:askForUseCard(player,"@@znai","@znai")
			local card_id = player:getMark(self:objectName())
			if card_id and card_id > 0 then room:obtainCard(target,card_id) end
			room:setPlayerMark(player,self:objectName(),0)
			sendLog("#znai1",room,player,nil,nil,target)
		else
			room:drawCards(target,2,self:objectName())
			sendLog("#znai2",room,player,nil,nil,target)
		end
		room:setPlayerFlag(player,self:objectName())
	end,
}
--被改变的命运

	changedfate = sgs.CreateTriggerSkill{
	name = "changedfate",
	events = {sgs.StartJudge, sgs.AskForRetrial, sgs.EventPhaseStart},
	global = true,
	priority = 5,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local poi = room:findPlayerBySkillName(self:objectName())
		if not poi or player:getPhase() ~= sgs.Player_Judge then return false end
		if event == sgs.EventPhaseStart then
			if poi:canDiscard(poi, "he") and not player:getJudgingArea():isEmpty() then
				if room:askForSkillInvoke(poi, self:objectName(), data) then
					room:setPlayerFlag(player, self:objectName())
				end
			end
		else
			local judge = data:toJudge()
			if judge.who:objectName() ~= player:objectName() or not player:hasFlag(self:objectName()) then return false end
			if event == sgs.StartJudge then
				if judge.good == true then
					judge.good = false
				elseif judge.good == false then
					judge.good = true
				end
				room:sendCompulsoryTriggerLog(poi, self:objectName())
			elseif event == sgs.AskForRetrial then
				return true
			end
			return false
		end
		return false
	end,
}

--零时迷子
lsmzCard = sgs.CreateSkillCard{
	name = "lsmz",
	will_throw = true,
	target_fixed = true,
	on_use = function(self,room,source)
		local recover = sgs.RecoverStruct(source,nil,source:getMaxHp() - source:getHp())
		room:recover(source,recover)
	end,
}
lsmzVS = sgs.CreateViewAsSkill{
	name = "lsmz",
	n = 1,
	response_pattern = "@@lsmz",
	view_filter = function(self,selected,to_select)
		return #selected < 1 and to_select:isBlack() and not to_select:isEquipped()
	end,
	view_as = function(self,cards)
		if #cards < 1 then return nil end
		local vs_card = lsmzCard:clone()
		vs_card:addSubcard(cards[1])
		vs_card:setSkillName(self:objectName())
		return vs_card
	end,
}
lsmz = sgs.CreateTriggerSkill{
	name = "lsmz",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseStart},
	view_as_skill = lsmzVS,
	on_trigger = function(self,event,player,data)
		if player:getPhase() ~= sgs.Player_Start or player:isKongcheng() or player:getHp() == player:getMaxHp() then return false end
		player:getRoom():askForUseCard(player,"@@lsmz","@lsmz")
	end,
}
--避火戒指
bhjz = sgs.CreateTriggerSkill{
	name = "bhjz",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageInflicted},
	priority = 0,
	on_trigger = function(self,event,player,data)
		local damage = data:toDamage()
		if damage.nature ~= sgs.DamageStruct_Fire or damage.damage <= 1 then return false end
		local room = player:getRoom()
		room:sendCompulsoryTriggerLog(player,self:objectName())
		sendLog("#bhjz",room,player)
		damage.damage = 1
		data:setValue(damage)
		return false
	end,
}
--王之宝库
wangzbk = sgs.CreateTriggerSkill{
	name = "wangzbk",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.CardsMoveOneTime, sgs.EventPhaseChanging},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if event == sgs.CardsMoveOneTime then
			local move = data:toMoveOneTime()
			if move.to_place ~= sgs.Player_DiscardPile then return false end
			if (not move.from) or move.from:getSeat() == player:getSeat() then return false end
			if not player:hasSkill(self:objectName()) then return false end
			local equips = sgs.CardList()
			for _,card_id in sgs.qlist(move.card_ids) do
				local card = sgs.Sanguosha:getCard(card_id)
				if card:isKindOf("EquipCard") then equips:append(card) end
			end
			if equips:isEmpty() then return false end
			if player:hasFlag("wzbk") then return false end
			if not room:askForSkillInvoke(player,self:objectName()) then return false end
			local dummy = sgs.Sanguosha:cloneCard("slash",sgs.Card_NoSuit,0)
			for _,card in sgs.qlist(equips) do
				dummy:addSubcard(card)
			end
			room:obtainCard(player, dummy)
			room:broadcastSkillInvoke(self:objectName())
			room:setPlayerFlag(player, "wzbk")
		elseif event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				for _,p in sgs.qlist(room:getAllPlayers()) do
					room:setPlayerFlag(p, "-wzbk")
				end
			end
		end
	end,
	can_trigger = function(self,target)
		return target
	end,
}
--兵弑
bings = sgs.CreateTriggerSkill{
	name = "bings",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.DamageCaused,sgs.DamageInflicted},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if event == sgs.DamageCaused then
			if room:askForCard(player,"EquipCard","@bings-increase",data,self:objectName()) then
				damage.damage = damage.damage + 1
				sendLog("#bings-increase",room,player,damage.damage,nil,damage.to)
				room:broadcastSkillInvoke(self:objectName())
				data:setValue(damage)
				return false
			end
		end
		if event == sgs.DamageInflicted then
			if room:askForCard(player,"EquipCard","@bings-decrease",data,self:objectName()) then
				damage.damage = damage.damage - 1
				sendLog("#bings-decrease",room,player,damage.damage,nil)
				room:broadcastSkillInvoke(self:objectName())
				if damage.damage < 1 then return true end
				data:setValue(damage)
				return false
			end
		end
	end,
}
--乖离剑
guailjCard = sgs.CreateSkillCard{
	name = "guailj",
	target_fixed = true,
	on_use = function(self,room,source)
		room:removePlayerMark(source,"@guailj")
		local playerlist = room:getOtherPlayers(source)
		for _,player in sgs.qlist(playerlist) do
			if player:isAlive() then
				local choice = "guailj2"
				if player:getArmor() and player:canDiscard(player,player:getArmor():getRealCard():getId()) then
					choice = room:askForChoice(player,self:objectName(),"guailj1+guailj2")
				end
				if choice == "guailj1" then
					room:throwCard(player:getArmor():getRealCard(),player,player)
				else
					room:damage(sgs.DamageStruct(self:objectName(),source,player,1))
				end
			end
		end
	end,
}
guailjVS = sgs.CreateZeroCardViewAsSkill{
	name = "guailj",
	view_as = function(self)
		return guailjCard:clone()
	end,
	enabled_at_play = function(self,player)
		return player:getMark("@guailj") >= 1
	end,
}
guailj = sgs.CreateTriggerSkill{
	name = "guailj",
	frequency = sgs.Skill_Limited,
	view_as_skill = guailjVS,
	limit_mark = "@guailj",
	on_trigger = function()
	end,
}
--昼
zhouClear = sgs.CreateTriggerSkill{
	name = "zhou-clear",
	global = true,
	events = {sgs.EventPhaseChanging},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local change = data:toPhaseChange()
		if change.to == sgs.Player_NotActive then
			local playerlist = room:getAllPlayers()
			for _,target in sgs.qlist(playerlist) do
				room:setPlayerMark(target,"zhou",0)
			end
		end
	end,
	can_trigger = function(self,target)
		return target
	end,
}
zhou = sgs.CreateTriggerSkill{
	name = "zhou",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damaged},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		room:sendCompulsoryTriggerLog(player,self:objectName())
		room:setPlayerMark(player,"zhou",player:getMark("zhou") + 1)
	end,
}
zhouDistance = sgs.CreateDistanceSkill{
	name = "#zhou",
	correct_func = function(self,from,to)
		return to:getMark("zhou")
	end,
}
extension:insertRelatedSkills("zhou","#zhou")
--夜
ye = sgs.CreateTriggerSkill{
	name = "ye",
	frequency = sgs.Skill_Wake,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if player:getPhase() ~= sgs.Player_Start or player:getMark(self:objectName()) > 0 then return false end
		local playerlist = room:getAlivePlayers()
		for _,aplayer in sgs.qlist(playerlist) do
			if aplayer:getHp() < player:getHp() then return false end
		end
		room:sendCompulsoryTriggerLog(player,self:objectName())
		player:gainMark("@waked",1)
		room:setPlayerMark(player,self:objectName(),1)
		room:recover(player,sgs.RecoverStruct(player))
		room:acquireSkill(player,"guichan")
	end,
}
--鬼缠
guichan = sgs.CreateTriggerSkill{
	name = "guichan",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.BuryVictim},
	priority = -2,
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local death = data:toDeath()
		local source = room:findPlayerBySkillName(self:objectName())
		if death.who:getSeat() == source:getSeat() then return false end
		local skilllist = death.who:getGeneral():getVisibleSkillList()
		local skills = {}
		for _,skill in sgs.qlist(skilllist) do
			local name = skill:objectName()
			if not table.contains(skills,name) and skill:getFrequency() ~= sgs.Skill_Wake and not skill:isLordSkill() and not skill:isAttachedLordSkill() then
				table.insert(skills,name)
			end
		end
		if #skills < 1 then return false end
		local p = sgs.QVariant()
		p:setValue(death.who)
		if not room:askForSkillInvoke(source,self:objectName(),p) then return false end
		local choice = room:askForChoice(source,self:objectName(),table.concat(skills,"+"))
		room:acquireSkill(source,choice)
		room:detachSkillFromPlayer(source,self:objectName())
		room:broadcastSkillInvoke(self:objectName())
	end,
	can_trigger = function(self,target)
		return target
	end,
}
--打反
dafanSourceCard = sgs.CreateSkillCard{
	name = "dafan",
	will_throw = true,
	target_fixed = true,
	feasible = function(self,targets)
		return true
	end,
	on_use = function(self,room,source,data)
		return false
	end,
}
dafanSourceVS = sgs.CreateViewAsSkill{
	name = "dafan",
	n = 2,
	response_pattern = "@@dafan",
	view_filter = function(self,selected,to_select)
		return #selected < 2 and to_select:isBlack() and sgs.Self:canDiscard(sgs.Self,to_select:getId())
	end,
	view_as = function(self,cards)
		if #cards < 2 then return nil end
		local vs_card = dafanSourceCard:clone()
		for _,card in ipairs(cards) do
			vs_card:addSubcard(card)
		end
		vs_card:setSkillName(self:objectName())
		return vs_card
	end,
}
dafan = sgs.CreateTriggerSkill{
	name = "dafan",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.DamageInflicted},
	view_as_skill = dafanSourceVS,
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if player:isNude() then return false end
		if room:askForCard(player,"@@dafan","@dafan") then
			damage.damage = damage.damage - 1
			sendLog("#dafan",room,player,damage.damage)
			if damage.from then
				local card = nil
				if damage.from:getSeat() ~= player:getSeat() then card = room:askForCard(damage.from,".|red|.|hand","@dafantarget:"..player:objectName(),sgs.QVariant(),sgs.Card_MethodNone) end
				if card then
					player:obtainCard(card)
				else
					local d = sgs.DamageStruct()
					d.from = player
					d.to = damage.from
					d.damage = 1
					d.reason = self:objectName()
					room:damage(d)
				end
			end
			if damage.damage < 1 then return true end
			data:setValue(damage)
		end
	end,
}
--绝剑
juej = sgs.CreateTriggerSkill{
	name = "juej",
	frequency = sgs.Skill_Wake,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if player:getPhase() ~= sgs.Player_Start or player:getHp() ~= 1 or player:getMark(self:objectName()) > 1 then return false end
		room:sendCompulsoryTriggerLog(player,self:objectName())
		room:changeMaxHpForAwakenSkill(player,-1)
		room:recover(player,sgs.RecoverStruct(player,nil,1))
		room:acquireSkill(player,"smsy")
	end,
}
--圣母圣咏
smsyCard = sgs.CreateSkillCard{
	name = "smsy",
	will_throw = true,
	target_fixed = true,
	feasible = function(self,targets)
		return true
	end,
	on_use = function(self,room,source,targets)
		return false
	end,
}
smsyVS = sgs.CreateViewAsSkill{
	name = "smsy",
	n = 1,
	response_pattern = "@@smsy",
	view_filter = function(self,selected,to_select)
		return #selected < 1 and sgs.Self:canDiscard(sgs.Self,to_select:getId())
	end,
	view_as = function(self,cards)
		if #cards < 1 then return nil end
		local vs_card = smsyCard:clone()
		vs_card:addSubcard(cards[1])
		vs_card:setSkillName(self:objectName())
		return vs_card
	end,
}
smsy = sgs.CreateTriggerSkill{
	name = "smsy",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.DamageCaused},
	view_as_skill = smsyVS,
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if damage.from and damage.to:getSeat() ~= player:getSeat() and (damage.card:isKindOf("Slash") or damage.card:isKindOf("Duel")) and (not player:isNude()) and room:askForCard(player,"@@smsy","@smsy") then
			local x = 1
			if player:isKongcheng() then x = x + 1 end
			damage.damage = damage.damage + x
			sendLog("#smsy",room,player,x,damage.damage)
			data:setValue(damage)
		end
	end,
}
--界王拳
jiewqCard = sgs.CreateSkillCard{
	name = "jiewq",
	target_fixed = true,
	on_use = function(self,room,source)
		local choices = {}
		for i = 1,source:getHp(),1 do
			table.insert(choices,tostring(i))
		end
		local choice = tonumber(room:askForChoice(source,self:objectName(),table.concat(choices,'+')))
		room:loseHp(source,choice)
		room:setPlayerMark(source,self:objectName(),choice)
		return false
	end,
}
jiewqVS = sgs.CreateViewAsSkill{
	n = 0,
	name = "jiewq",
	view_as = function(self,cards)
		local card = jiewqCard:clone()
		card:setSkillName(self:objectName())
		return card
	end,
	enabled_at_play = function(self,player)
		return player:getHp() > 0 and not player:hasUsed("#jiewq")
	end,
}
jiewq = sgs.CreateTriggerSkill{
	name = "jiewq",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.CardFinished,sgs.DamageCaused},
	view_as_skill = jiewqVS,
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local mark = player:getMark(self:objectName())
		if event == sgs.CardFinished then
			local use = data:toCardUse()
			if use.card:isKindOf("Slash") then
				room:setPlayerMark(player,self:objectName(),0)
			end
		end
		if event == sgs.DamageCaused then
			local damage = data:toDamage()
			if damage.card and damage.card:isKindOf("Slash") and mark > 0 then
				damage.damage = damage.damage + mark
				sendLog("#jiewq",room,player,mark,damage.damage)
				data:setValue(damage)
				return false
			end
		end
	end,
}
--赛亚人
saiya = sgs.CreateTriggerSkill{
	name = "saiya",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.QuitDying,sgs.DamageCaused},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if event == sgs.QuitDying then
			room:sendCompulsoryTriggerLog(player,self:objectName())
			room:setPlayerMark(player,self:objectName(),player:getMark(self:objectName()) + 1)
			sendLog("#GainMaxHp",room,player,1)
			room:setPlayerProperty(player,"maxhp",sgs.QVariant(player:getMaxHp() + 1))
			return false
		end
		if event == sgs.DamageCaused then
			local damage = data:toDamage()
			if player:getMark(self:objectName()) >= 4 then
				damage.damage = damage.damage + 1
				sendLog("#saiya",room,player,damage.damage)
				data:setValue(damage)
				return false
			end
		end
	end,
}
--不存在之人
bczzr = sgs.CreateTriggerSkill{
	name = "#bczzr",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardResponded},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local resp = data:toCardResponse()
		if (not resp.m_card:isKindOf("Jink")) or (not resp.m_isUse) then return false end
		room:setPlayerMark(player,self:objectName(),1)
	end,
}
bczzrClear = sgs.CreateTriggerSkill{
	name = "#bczzr-clear",
	events = {sgs.EventPhaseChanging},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local change = data:toPhaseChange()
		if change.to == sgs.Player_NotActive then
			for _,target in sgs.qlist(room:getAlivePlayers()) do
				room:setPlayerMark(target,"bczzr",0)
			end
		end
	end,
	can_trigger = function(self,target)
		return target
	end,
}
bczzrProhibit = sgs.CreateProhibitSkill{
	name = "bczzr",
	is_prohibited = function(self,from,to,card)
		return card:isKindOf("TrickCard") and to:hasSkill("bczzr") and to:getMark("bczzr") <= 0
	end,
}
extension:insertRelatedSkills("bczzr","#bczzr")
extension:insertRelatedSkills("bczzr","#bczzr-clear")
--木偶之眼
mozyCard = sgs.CreateSkillCard{
	name = "mozy",
	will_throw = true,
	target_fixed = false,
	filter = function(self,targets,to_select)
		return #targets < 1
	end,
	feasible = function(self,targets)
		return #targets == 1
	end,
	on_use = function(self,room,source,targets)
		local target = targets[1]
		local judge = sgs.JudgeStruct()
		judge.reason = self:objectName()
		judge.play_animation = false
		judge.who = target
		room:judge(judge)
		if judge.card:isRed() then
			room:recover(target,sgs.RecoverStruct(source,nil,1))
		end
		if judge.card:isBlack() then
			sendLog("#mozy",room,target)
			room:setPlayerMark(target,"mozy",1)
		end
		return false
	end,
}
mozyVS = sgs.CreateViewAsSkill{
	n = 1,
	name = "mozy",
	view_filter = function(self,selected,to_select)
		return #selected < 1
	end,
	view_as = function(self,cards)
		if #cards < 1 then return nil end
		local vs_card = mozyCard:clone()
		vs_card:addSubcard(cards[1])
		vs_card:setSkillName(self:objectName())
		return vs_card
	end,
	enabled_at_play = function(self,player)
		return not player:hasUsed("#mozy")
	end,
}
mozy = sgs.CreateTriggerSkill{
	name = "mozy",
	view_as_skill = mozyVS,
	events = {sgs.EventPhaseChanging},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local change = data:toPhaseChange()
		if change.to == sgs.Player_NotActive then
			for _,target in sgs.qlist(room:getAlivePlayers()) do
				room:setPlayerMark(target, "mozy", 0)
			end
		end
	end,
}
mozyProhibit = sgs.CreateProhibitSkill{
	name = "#mozy",
	is_prohibited = function(self,from,to,card)
		if not card:isKindOf("Peach") then return false end
		if from:getMark("mozy") > 0 and (not to) then return true end
		if to and to:getMark("mozy") > 0 then return true end
	end,
}
extension:insertRelatedSkills("mozy","#mozy")
--情殇哀逝
qsasCard = sgs.CreateSkillCard{
	name = "qsas",
	will_throw = true,
	target_fixed = false,
	filter = function(self,targets,to_select)
		return #targets < 1 and to_select:getSeat() ~= sgs.Self:getSeat()
	end,
	feasible = function(self,targets)
		return #targets == 1
	end,
	on_use = function(self,room,source,targets)
		room:setPlayerMark(targets[1],self:objectName(),1)
		return false
	end,
}
qsasVS = sgs.CreateViewAsSkill{
	n = 1,
	name = "qsas",
	view_filter = function(self,selected,to_select)
		return #selected < 1 and to_select:isKindOf("TrickCard")
	end,
	view_as = function(self,cards)
		if #cards < 1 then return nil end
		local vs_card = qsasCard:clone()
		vs_card:addSubcard(cards[1])
		vs_card:setSkillName(self:objectName())
		return vs_card
	end,
	enabled_at_play = function(self,player)
		return not player:hasUsed("#qsas")
	end,
}
qsas = sgs.CreateTriggerSkill{
	name = "qsas",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.TurnStart,sgs.HpRecover,sgs.Damaged},
	view_as_skill = qsasVS,
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local source = room:findPlayerBySkillName(self:objectName())
		if (not source) or (not source:isAlive()) then return false end
		if event == sgs.Damaged then
			local damage = data:toDamage()
			if damage.to:getMark(self:objectName()) > 0 then
				room:sendCompulsoryTriggerLog(player,self:objectName())
				source:drawCards(damage.damage)
				return false
			end
		end
		if event == sgs.HpRecover and player:getMark(self:objectName()) > 0 then
			local recover = data:toRecover()
			room:sendCompulsoryTriggerLog(source,self:objectName())
			for i = 1,recover.recover,1 do
				if not source:canDiscard(player,"he") then break end
				local card_id = room:askForCardChosen(source,player,"he",self:objectName(),false,sgs.Card_MethodDiscard)
				room:throwCard(card_id,player,source)
			end
			return false
		end
		if event == sgs.TurnStart and player:getSeat() == source:getSeat() then
			for _,target in sgs.qlist(room:getAllPlayers()) do
				room:setPlayerMark(target,self:objectName(),0)
			end
		end
	end,
	can_trigger = function(self,target)
		return target
	end,
}
--直死魔眼
zsmy = sgs.CreateTriggerSkill{
	name = "zsmy",
	frequency = sgs.Skill_Limited,
	limit_mark = "@zsmy",
	events = {sgs.Death, sgs.DamageCaused},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local msg = sgs.LogMessage()
		local damage = data:toDamage()
		if event == sgs.Death then
			local death = data:toDeath()
			if death.who:objectName() ~= player:objectName() then return false end
			local killer = damage.from
			if death.damage then
				killer = death.damage.from
			else
				killer = nil
			end
			if killer and killer:hasSkill(self:objectName()) and (death.damage.reason == self:objectName()) then
				killer:gainMark("@zsmy")
				room:broadcastSkillInvoke("zsmy")
			end
		elseif event == sgs.DamageCaused then
			if damage.to:getHp() <= 2 and player:hasSkill(self:objectName()) and player:getMark("@zsmy") > 0 then
				if room:askForSkillInvoke(player,self:objectName()) then
					local card = sgs.Sanguosha:getCard(room:getNCards(1):first())
					local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_DEMONSTRATE,player:objectName(),self:objectName(),"")
					room:moveCardTo(card, nil, sgs.Player_PlaceTable, reason)
					if not card:isKindOf("Slash") then
						player:loseMark("@zsmy")
						room:broadcastSkillInvoke("zsmy")
						local x = math.ceil(room:getAllPlayers(true):length() - room:getAlivePlayers():length(), 1)
						damage.damage = damage.damage + x 
						data:setValue(damage)
					end
				end
			end
		end
	end,
	can_trigger = function(self, target)
		return target
	end
}
--逆刃刀
nirendao = sgs.CreateTriggerSkill{
	name = "nirendao", 
	events = {sgs.DamageCaused},
	frequency = sgs.Skill_Compulsory,
	priority = 1,
	on_trigger = function(self, event, player, data) 
		local room = player:getRoom()
		local msg = sgs.LogMessage()
		local damage = data:toDamage()
		local count = damage.damage
		if damage.chain or damage.transfer then return false end
		if damage.card:isKindOf("Slash") then
			if count + 1 >= damage.to:getHp() then
				return true
			else
				count = count + 1
				damage.damage = count
				data:setValue(damage)
			end
		end
	end
}
--逆刀刃
nidaorenCard = sgs.CreateSkillCard{
	name = "nidaorenCard",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		local drawnum = {}
		for i = 1,source:getHp(),1 do
			table.insert(drawnum,tostring(i))
		end
		local num = tonumber(room:askForChoice(source, "nidaorendraw", table.concat(drawnum, "+")))
		room:doSuperLightbox("feicunjianxin", "$nidaorenQP")
		room:broadcastSkillInvoke("nidaoren", 1)
		source:loseMark("@nidaoren")
		room:setPlayerMark(source,"nidaorendying",0)
		room:loseHp(source, num)
		if not source:isAlive() then return false end
		source:drawCards(num * 3, "nidaoren")
		room:setPlayerMark(source, "nidaoren", num)
	end,
}
nidaorenVS = sgs.CreateZeroCardViewAsSkill{
	name = "nidaoren",
	view_as = function()
		return nidaorenCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@nidaoren") > 0
	end
}
nidaoren = sgs.CreateTriggerSkill{
	name = "nidaoren",
	frequency = sgs.Skill_Limited,
	limit_mark = "@nidaoren",
	events = {sgs.EventPhaseChanging,sgs.Dying},
	view_as_skill = nidaorenVS,
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to ~= sgs.Player_NotActive then return false end
			room:setPlayerMark(player,"nidaoren",0)
		end
	end,
}
nidaorenDis = sgs.CreateDistanceSkill{
	name = "#nidaorenDis",
	correct_func = function(self, from, to)
		if from:hasSkill(self:objectName()) then
			return - from:getMark("nidaoren")
		end
	end,
}
extension:insertRelatedSkills("nidaoren", "#nidaorenDis")
--拔刀斋
badaozhaiCard = sgs.CreateSkillCard{
	name = "badaozhaiCard",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		room:doSuperLightbox("feicunjianxin", "$badaozhaiQP")
		room:broadcastSkillInvoke("badaozhai", 1)
		source:loseMark("@badaozhai")
		for _,p in sgs.qlist(room:getOtherPlayers(source)) do
			if p:getHp() < source:getLostHp() then
				room:setPlayerMark(p, "badaozhaihp", p:getHp())
				room:setPlayerProperty(p, "hp", sgs.QVariant(0))
				room:enterDying(p, nil)
			end
		end
	end,
}
badaozhaiVS = sgs.CreateZeroCardViewAsSkill{
	name = "badaozhai",
	view_as = function()
		return badaozhaiCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@badaozhai") > 0
	end
}
badaozhai = sgs.CreateTriggerSkill{
	name = "badaozhai",
	frequency = sgs.Skill_Limited,
	limit_mark = "@badaozhai",
	events = {sgs.AskForPeachesDone},
	view_as_skill = badaozhaiVS,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local hp = player:getMark("badaozhaihp")
		if hp > 0 then
			room:setPlayerMark(player, "badaozhaihp", 0)
			if player:getHp() <= 0 then return false end
			room:setPlayerProperty(player, "hp", sgs.QVariant(hp))
		end
	end,
	can_trigger = function(self, target)
		return target and target:isAlive()
	end
}
--飞雷神之术
feils = sgs.CreateDistanceSkill{
	name = "feils" ,
	correct_func = function(self, from, to)
		local correct = 0
		if from:hasSkill(self:objectName()) and (from:getHp() >= from:getHandcardNum() ) then
			correct = correct - 2
		end
		if to:hasSkill(self:objectName()) and (to:getHp() < from:getHandcardNum() ) then
			correct = correct + 2
		end
		return correct
	end
}
--金色闪光
jssg = sgs.CreateTriggerSkill{
	name = "jssg",
	frequency = sgs.Skill_Wake,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self,event,player,data)
		if player:getPhase() ~= sgs.Player_Start or player:getMark(self:objectName()) > 0 then return false end
		local room = player:getRoom()
		for _,target in sgs.qlist(room:getOtherPlayers(player)) do
			if target:getHp() < player:getHp() then return false end
		end
		room:sendCompulsoryTriggerLog(player,self:objectName())
		if player:getHp() < player:getMaxHp() then
			room:recover(player,sgs.RecoverStruct(player,nil,1))
		end
		room:setPlayerMark(player,self:objectName(),1)
		room:changeMaxHpForAwakenSkill(player,-1)
		room:acquireSkill(player,"feils2")
		return false
	end,
}
--飞雷神二段
feils2Card = sgs.CreateSkillCard{
	name = "feils2",
	target_fixed = false,
	filter = function(self,targets,to_select)
		return #targets < 1 and sgs.Self:canSlash(to_select,false)
	end,
	feasible = function(self,targets)
		return #targets == 1
	end,
	on_use = function(self,room,source,targets)
		local slash = sgs.Sanguosha:cloneCard("slash",sgs.Card_NoSuit,0)
		local use = sgs.CardUseStruct(slash,source,targets[1])
		room:useCard(use,false)
		room:setPlayerMark(targets[1],self:objectName(),1)
		room:setFixedDistance(targets[1],source,1)
		return false
	end,
}
feils2VS = sgs.CreateViewAsSkill{
	n = 0,
	name = "feils2",
	view_as = function(self,cards)
		local card = feils2Card:clone()
		card:setSkillName(self:objectName())
		return card
	end,
	enabled_at_play = function(self,player)
		return not player:hasUsed("#feils2")
	end,
}
feils2 = sgs.CreateTriggerSkill{
	name = "feils2",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseStart},
	view_as_skill = feils2VS,
	on_trigger = function(self,event,player,data)
		if player:getPhase() ~= sgs.Player_Finish or player:getMark(self:objectName()) < 1 then return false end
		local room = player:getRoom()
		local source = room:findPlayerBySkillName(self:objectName())
		room:setPlayerMark(player,self:objectName(),0)
		room:setFixedDistance(player,source,-1)
		return false
	end,
	can_trigger = function(self,target)
		return target
	end,
}
--窥心
kuixinCard = sgs.CreateSkillCard{
	name = "kuixin",
	target_fixed = false,
	filter = function(self,targets,to_select)
		return #targets < 1 and to_select:getSeat() ~= sgs.Self:getSeat() and not to_select:isKongcheng()
	end,
	feasible = function(self,targets)
		return true
	end,
	on_use = function(self,room,source,targets)
		local target = targets[1]
		if target then
			local cards = target:getCards("h")
			if source:getHandcardNum() <= target:getHandcardNum() then
				local card_ids = sgs.IntList()
				for _,card in sgs.qlist(cards) do
					card_ids:append(card:getId())
				end
				room:fillAG(card_ids,source)
				local card_id = room:askForAG(source,card_ids,true,self:objectName())
				if card_id ~= -1 then source:obtainCard(sgs.Sanguosha:getCard(card_id),false) end
				room:clearAG()
				return false
			end
			room:showAllCards(target,source)
			return false
		else
			local card_ids = room:getNCards(3, false)
			if source:getHandcardNum() <= 3 then
				room:fillAG(card_ids,source)
				local card_id = room:askForAG(source,card_ids,true,self:objectName())
				room:clearAG()
				if card_id ~= -1 then source:obtainCard(sgs.Sanguosha:getCard(card_id),false) end
				return false
			end
			room:fillAG(card_ids,source)
			room:getThread():delay(2000)
			room:clearAG()
			return false
		end
	end,
}
kuixinVS = sgs.CreateViewAsSkill{
	n = 0,
	name = "kuixin",
	response_pattern = "@@kuixin",
	view_as = function(self,cards)
		local card = kuixinCard:clone()
		card:setSkillName(self:objectName())
		return card
	end,
}
kuixin = sgs.CreateTriggerSkill{
	name = "kuixin",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseStart},
	view_as_skill = kuixinVS,
	on_trigger = function(self,event,player,data)
		if player:getPhase() ~= sgs.Player_Finish then return false end
		local room = player:getRoom()
		room:askForUseCard(player,"@@kuixin","@kuixin")
		return false
	end,
}
--救赎
jiushuCard = sgs.CreateSkillCard{
	name = "jiushu",
	target_fixed = true,
	will_throw = true,
	on_use = function(self,room,source,targets)
		return false
	end,
}
jiushuVS = sgs.CreateViewAsSkill{
	n = 2,
	name = "jiushu",
	response_pattern = "@@jiushu",
	view_filter = function(self,selected,to_select)
		if to_select:isEquipped() then return false end
		if not sgs.Self:canDiscard(sgs.Self,to_select:getId()) then return false end
		if #selected == 0 then return true end
		if #selected == 1 then
			return selected[1]:getTypeId() == to_select:getTypeId() or selected[1]:getSuitString() == to_select:getSuitString()
		end
		return false
	end,
	view_as = function(self,cards)
		if #cards < 2 then return nil end
		local vs_card = jiushuCard:clone()
		for _,card in ipairs(cards) do
			vs_card:addSubcard(card)
		end
		vs_card:setSkillName(self:objectName())
		return vs_card
	end,
}
jiushu = sgs.CreateTriggerSkill{
	name = "jiushu",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Dying},
	view_as_skill = jiushuVS,
	on_trigger = function(self,event,player,data)
		local dying = data:toDying()
		local room = player:getRoom()
		if dying.who:getSeat() == player:getSeat() then return false end
		local p = sgs.QVariant()
		p:setValue(dying.who)
		if not room:askForSkillInvoke(player,self:objectName()) then return false end
		player:drawCards(1)
		if not room:askForUseCard(player,"@@jiushu","@jiushu:"..dying.who:objectName()) then return false end
		room:recover(dying.who,sgs.RecoverStruct(player,nil,1))
		return false
	end,
}
--协横
xieheng = sgs.CreateTriggerSkill{
	name = "xieheng",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Damaged},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if not room:askForSkillInvoke(player,self:objectName()) then return false end
		local target = room:askForPlayerChosen(player,room:getAlivePlayers(),self:objectName())
		if room:askForCard(player,".|red","@xieheng:"..target:objectName()) then
			room:recover(target,sgs.RecoverStruct(player,nil,1))
		else
			target:drawCards(1)
		end
	end,
}
--痛觉的止符
tjdzf = sgs.CreateTriggerSkill{
	name = "tjdzf",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.DamageInflicted,sgs.EventPhaseStart},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if event == sgs.DamageInflicted then
			if not room:askForSkillInvoke(player,self:objectName()) then return false end
			local damage = data:toDamage()
			local card_ids = room:getNCards(damage.damage)
			player:addToPile("yin",card_ids)
			sendLog("#tjdzf",room,player,damage.damage)
			room:broadcastSkillInvoke(self:objectName())
			return true
		end
		if event == sgs.EventPhaseStart then
			if player:getPhase() ~= sgs.Player_Finish or player:getPile("yin"):isEmpty() then return false end
			room:loseHp(player,player:getPile("yin"):length())
			local dummy = sgs.Sanguosha:cloneCard("slash",sgs.Card_NoSuit,0)
			dummy:addSubcards(player:getPile("yin"))
			player:obtainCard(dummy)
			return false
		end
	end,
}
--青刃的哀歌
qrdag = sgs.CreateTriggerSkill{
	name = "qrdag",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.PreCardUsed,sgs.Damage,sgs.CardFinished,sgs.EventPhaseChanging},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if event == sgs.PreCardUsed then
			local use = data:toCardUse()
			if player:getPhase() == sgs.Player_Play and use.card:isKindOf("Slash") and player:getMark(self:objectName()) == 0 then
				room:setPlayerMark(player,self:objectName(),1)
				use.card:setFlags(self:objectName())
				room:broadcastSkillInvoke(self:objectName(),1)
			end
		end
		if event == sgs.Damage then
			local damage = data:toDamage()
			if damage.card and damage.card:hasFlag(self:objectName()) then
				for i = 1,damage.damage,1 do
					local yin = player:getPile("yin")
					local choices = {}
					if yin:length() > 0 then
						table.insert(choices,"qrdag_discard")
					end
					if player:isWounded() then
						table.insert(choices,"qrdag_recover")
					end
					if #choices == 0 then return false end
					if not room:askForSkillInvoke(player,self:objectName()) then return false end
					room:broadcastSkillInvoke(self:objectName(),2)
					local choice = room:askForChoice(player,self:objectName(),table.concat(choices,"+"))
					if choice == "qrdag_recover" then
						room:recover(player,sgs.RecoverStruct(player,nil,1))
					else
						room:fillAG(yin,player)
						local card_id = room:askForAG(player, yin, false, self:objectName())
						local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_REMOVE_FROM_PILE, player:objectName(), self:objectName(), "")
						local ac = sgs.Sanguosha:getCard(card_id)
						room:throwCard(ac, reason, nil)
						room:clearAG()
					end
				end
			end
		end
		if event == sgs.CardFinished then
			local use = data:toCardUse()
			if use.card:hasFlag(self:objectName()) then
				use.card:setFlags("-"..self:objectName())
			end
		end
		if event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.from == sgs.Player_Play then
				room:setPlayerMark(player,self:objectName(),0)
			end
		end
	end,
}
qrdagEx = sgs.CreateTargetModSkill{
	name = "qrdag_ex",
	pattern = "Slash",
	distance_limit_func = function(self, player)
		if player:hasSkill("qrdag") and not player:hasUsed("Slash") and player:getPhase() == sgs.Player_Play then
			return 999
		else
			return 0
		end
	end,
	extra_target_func = function(self, from)
		if not from:hasSkill("qrdag") then return 0 end
		if from:hasUsed("Slash") or from:getPhase() ~= sgs.Player_Play then return 0 end
		return math.max(0 ,from:getPile("yin"):length() - 1)
	end,
}
--粉毛
fenmao = sgs.CreateTriggerSkill{
	name = "fenmao",
	events = {sgs.EventPhaseStart},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Start then
			if room:askForSkillInvoke(player, self:objectName(), data)  then
				local judge = sgs.JudgeStruct()
				judge.pattern = "."
				judge.good = true
				judge.reason = self:objectName()
				judge.who = player
				room:judge(judge)
				if judge.card:isRed() then
					room:acquireSkill(player, "changgui")
				elseif judge.card:isBlack() then
					room:acquireSkill(player, "heihua")
				end
			end
		elseif player:getPhase() == sgs.Player_Finish then
			if player:hasSkill("changgui") then
				room:detachSkillFromPlayer(player, "changgui", false, true)
			end
			if player:hasSkill("heihua") then
				room:detachSkillFromPlayer(player, "heihua", false, true)
			end
		end
	end
}
--常规
changguicard = sgs.CreateSkillCard{
	name = "changgui",
	target_fixed = true,
	will_throw = false,
	on_use = function(self, room, source, targets)
		local ids = room:getNCards(3)
		room:fillAG(ids)
		local id = room:askForAG(source, ids, false, "changgui")
		if id ~= -1 then
			room:obtainCard(source, id, false)
			ids:removeOne(id)
		end
		room:clearAG()
		room:askForGuanxing(source, ids, 1)
	end
}
changgui = sgs.CreateZeroCardViewAsSkill{
	name = "changgui",
	view_as = function(self)
		return changguicard:clone()
	end,
	enabled_at_play = function(self,player)
		return not player:hasUsed("#changgui")
	end
}
--黑化
heihuacard = sgs.CreateSkillCard{
	name = "heihua",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select, player)
		return #targets < 1 and (not to_select:isKongcheng()) and to_select:objectName() ~= player:objectName()
	end,
	on_use = function(self, room, source, targets)
		local ids = targets[1]:handCards()
		room:fillAG(ids, source)
		room:getThread():delay(2000)
		room:clearAG(source)
		local basic = sgs.IntList()
		for _,d in sgs.qlist(ids) do
			if sgs.Sanguosha:getCard(d):isKindOf("BasicCard") then
				basic:append(d)
			end
		end
		if basic:isEmpty() then return false end
		room:fillAG(basic, source)
		local id = room:askForAG(source, basic, false, "heihua")
		if id ~= -1 then
			room:showCard(targets[1], id)
			room:obtainCard(source, id)
		end
		room:clearAG(source)
	end
}
heihua = sgs.CreateZeroCardViewAsSkill{
	name = "heihua",
	view_as = function(self)
		return heihuacard:clone()
	end,
	enabled_at_play = function(self,player)
		return not player:hasUsed("#heihua")
	end
}
--轮回
smlunhuivs = sgs.CreateViewAsSkill{
    name = "smlunhui",
	n = 998,
	view_filter = function(self, selected, to_select)
		return not to_select:isEquipped()
	end,
    view_as = function(self, cards)
	    local pattern = sgs.Self:property("smlunhui_use"):toString():split("+")
		if #cards == tonumber(pattern[1]) then
			local acard = sgs.Sanguosha:cloneCard(pattern[2], sgs.Card_SuitToBeDecided, -1)
			for _,card in ipairs(cards) do
				acard:addSubcard(card)
			end
			acard:setSkillName(self:objectName())
			return acard
		end
    end,
    enabled_at_play = function(self,player)
        return false
    end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@smlunhui"
	end
}
smlunhui = sgs.CreateTriggerSkill{
	name = "smlunhui",
	events = {sgs.CardUsed, sgs.EventPhaseStart},
	view_as_skill = smlunhuivs,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardUsed then
			if player:getPhase() == sgs.Player_Play then
				card = data:toCardUse().card
				if card and (card:getHandlingMethod() == sgs.Card_MethodUse) then
					if card:isKindOf("BasicCard") then
						local basic = player:property("smlunhui_basic"):toString()
						if basic == "" then
							basic = card:objectName()
						else
							basic = basic.."+"..card:objectName()
						end
						room:setPlayerProperty(player, "smlunhui_basic", sgs.QVariant(basic))
					elseif card:isNDTrick() then
						local trick = player:property("smlunhui_trick"):toString()
						if trick == "" then
							trick = card:objectName()
						else
							trick = trick.."+"..card:objectName()
						end
						room:setPlayerProperty(player, "smlunhui_trick", sgs.QVariant(trick))
					end
				end
			end
		elseif event == sgs.EventPhaseStart then
			if player:getPhase() == sgs.Player_Finish then
				local basic = player:property("smlunhui_basic"):toString()
				basic = basic:split("+")
				local trick = player:property("smlunhui_trick"):toString()
				trick = trick:split("+")
				local invoke = 0
				for i = 1, math.max(#basic, #trick), 1 do
					if player:getHandcardNum() < i then break end
					local b = basic[i] or ""
					local t = trick[i] or ""
					if b ~= "" and (not sgs.Sanguosha:cloneCard(basic[i], sgs.Card_NoSuit, 0):isAvailable(player)) then
						b = ""
					end
					if t ~= "" and (not sgs.Sanguosha:cloneCard(trick[i], sgs.Card_NoSuit, 0):isAvailable(player)) then
						t = ""
					end
					local pattern = b
					if pattern == "" then
						pattern = t
					else
						if t ~= "" then
							pattern = pattern.."+"..t
						end
					end
					if pattern == "" then break end
					pattern = pattern.."+".."cancel"
					local choice = room:askForChoice(player, self:objectName(), pattern, data)
					if choice and choice ~= "cancel" then
						room:setPlayerProperty(player, "smlunhui_use", sgs.QVariant(tostring(i).."+"..choice))
						if room:askForUseCard(player, "@@smlunhui", "@smlunhui:"..tostring(i)..":"..choice) then
							invoke = i
						else
							room:setPlayerProperty(player, "smlunhui_use", sgs.QVariant())
							break
						end
						room:setPlayerProperty(player, "smlunhui_use", sgs.QVariant())
					else
						break
					end
				end
				room:setPlayerProperty(player, "smlunhui_basic", sgs.QVariant())
				room:setPlayerProperty(player, "smlunhui_trick", sgs.QVariant())
				if invoke >= 2 then
					player:gainAnExtraTurn()
				end
			elseif player:getPhase() == sgs.Player_Start then
				room:setPlayerProperty(player, "smlunhui_basic", sgs.QVariant())
				room:setPlayerProperty(player, "smlunhui_trick", sgs.QVariant())
			end
		end
	end
}
--最后的反击
zuihoudefanji = sgs.CreateTriggerSkill
{
	name = "zuihoudefanji",
	events = {sgs.Dying},
	frequency = sgs.Skill_Limited,
	limit_mark = "@zuihoudefanji",
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local dying = data:toDying()
		if dying.who:objectName() == player:objectName() and player:getMark("@zuihoudefanji") > 0 and room:askForSkillInvoke(player, self:objectName(), data) then
			player:loseMark("@zuihoudefanji")
			for _,p in sgs.qlist(room:getPlayers()) do
				room:notifyProperty(p, player, "role", player:getRole())
			end
			room:broadcastProperty(player, "role", player:getRole())
			room:updateStateItem()
			local log = sgs.LogMessage()
			log.type = "#ShowRole"
			log.from = player
			log.arg = player:getRole()
			room:sendLog(log)
			room:loseMaxHp(player)
			room:recover(player, sgs.RecoverStruct(player, nil, 3 - player:getHp()))
			room:changeHero(player, "yuru", false, true, true)
		end
	end
}
--不死鸟
businiao = sgs.CreateTriggerSkill{
	name = "businiao", 
	frequency = sgs.Skill_Compulsory, 
	events = {sgs.DamageInflicted}, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		local judge = sgs.JudgeStruct()
		judge.pattern = ".|heart"
		judge.good = true
		judge.reason = self:objectName()
		judge.who = player
		room:judge(judge)
		if judge:isGood() then
			room:broadcastSkillInvoke(self:objectName(), 1)
			return true
		else
			if damage.from and damage.from:isAlive() and (not damage.from:isKongcheng()) then
				local poi = room:askForCardChosen(player, damage.from, "h", "businiao")
				room:obtainCard(player, poi, false)
				room:broadcastSkillInvoke(self:objectName(), 2)
			end
		end
	end
}
--战线防御
zhanxianfanyu = sgs.CreateTriggerSkill{
	name = "zhanxianfanyu",
	events = {sgs.EventPhaseChanging, sgs.TurnStart, sgs.TargetConfirming},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to ~= sgs.Player_Draw and change.to ~= sgs.Player_Play then return false end
			if player:isSkipped(change.to) then return false end
			if not player:askForSkillInvoke(self:objectName(),data) then return false end
			if change.to == sgs.Player_Draw then
				room:broadcastSkillInvoke(self:objectName(),math.random(1,2))
			else
				room:broadcastSkillInvoke(self:objectName(),math.random(3,4))
			end
			player:skip(change.to)
			local s = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "zhanxianfanyu-invoke", true, true)
			if not s then return false end
			s:gainMark("@zhanxianfanyu", 1)
		elseif event == sgs.TurnStart then
			local all_players = room:getAllPlayers()
			for _, poi in sgs.qlist(all_players) do
				room:setPlayerMark(poi, "@zhanxianfanyu", 0)
			end
		end
	end
}
--战线防御二段技能
slash_defence = sgs.CreateTriggerSkill{
	name = "slash_defence",
	events = {sgs.TargetConfirming},
	global = true,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local use = data:toCardUse()
		local supproter = room:findPlayerBySkillName("zhanxianfanyu")
		if use.card and use.from then
			if use.card:isKindOf("Slash") and  use.to:contains(player) then
			if not supproter:askForSkillInvoke(self:objectName(),data) then return false end
				use.to:removeOne(player)
				data:setValue(use)
				local duel = sgs.Sanguosha:cloneCard("duel", sgs.Card_NoSuit, 0)
				duel:setSkillName("zhanxianfanyu")
				room:useCard(sgs.CardUseStruct(duel, supproter, use.from))
				room:broadcastSkillInvoke(self:objectName(),math.random(1,2))
			end
		end
	end,
	can_trigger = function(self, target)
		return target:getMark("@zhanxianfanyu") > 0
	end,
}
--革命机火焰技能
jixieshenslash = sgs.CreateTriggerSkill{
	name = "jixieshenslash" ,
	events = {sgs.TargetSpecified} ,
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		local room = player:getRoom()
		if use.card:isKindOf("Slash") and player:askForSkillInvoke(self:objectName(),data) then
			for _, p in sgs.qlist(use.to) do
				room:damage(sgs.DamageStruct("jixieshenslash", player, p, 1, sgs.DamageStruct_Fire))
				room:broadcastSkillInvoke(self:objectName(),math.random(1,2))
				if not player:isAlive() then break end
				local log = sgs.LogMessage()
				log.from = player
				log.arg = self:objectName()
				log.type = "jixieshenfire"
				room:sendLog(log)
				use.to:removeOne(use.to:first())
				room:sortByActionOrder(use.to)
				data:setValue(use)
				room:addPlayerMark(player, self:objectName())
			end
		end
	end
}
--拂晓伤害免疫
jixieshendefense=sgs.CreateTriggerSkill{
name="jixieshendefense",
frequency=sgs.Skill_Compulsory,
events=sgs.DamageInflicted,
on_trigger=function(self,event,player,data)
local room = player:getRoom()
	local damage=data:toDamage()
	if damage.nature~=sgs.DamageStruct_Normal then
	local room=player:getRoom()
	local log=sgs.LogMessage()
	log.from=player
	log.arg=self:objectName()
	log.type="jixieshenmianyi"
	room:sendLog(log)
	if not player:isNude() then
	if not player:askForSkillInvoke(self:objectName(),data) then return false end
	room:askForDiscard(player, self:objectName(), 1, 1, false, true)
	room:broadcastSkillInvoke(self:objectName(),math.random(1,2))
	room:drawCards(player, 1)
	end
	room:broadcastSkillInvoke(self:objectName())
	if damage.damage <= 9999999999999 then --什么鬼
		return true
	end
	end
end
}
--高文卡牌变化
jixieshenchain = sgs.CreateOneCardViewAsSkill{
    name = "jixieshenchain",
    response_or_use = true,
    view_filter = function(self, card)
		if card:isEquipped() then return false end
        if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
            if card:isRed() then
                local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
                slash:addSubcard(card:getEffectiveId())
                slash:deleteLater()
                return slash:isAvailable(sgs.Self)
            end
        elseif sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE or sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE then
            local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
            if pattern == "jink" then
                return card:isBlack()
			elseif pattern == "slash" then
                return card:isRed()
            end
        end
        return false
    end,
    view_as = function(self, card)
        local new_card
		if card:isBlack() then
            new_card = sgs.Sanguosha:cloneCard("jink", sgs.Card_SuitToBeDecided, 0)
        elseif card:isRed() then
            new_card = sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_SuitToBeDecided, 0)
        end
        if new_card then
            new_card:setSkillName(self:objectName())
			new_card:addSubcard(card)
        end
        return new_card
    end,
    enabled_at_play = function(self, player)
        return sgs.Slash_IsAvailable(player)
    end,
    enabled_at_response = function(self, player, pattern)
        return pattern == "slash" or pattern == "jink"
    end
}
--机械公敌
jixieshen = sgs.CreateTriggerSkill{
	name = "jixieshen", 
	frequency = sgs.Skill_NotFrequent, 
	events = {sgs.TurnStart, sgs.GameStart, sgs.AskForPeachesDone}, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local s=room:findPlayerBySkillName(self:objectName())
		if event == sgs.GameStart then
			room:setPlayerMark(player, "gemingji", 2)
			room:setPlayerMark(player, "gaowen", 2)
			room:setPlayerMark(player, "fuxiao", 2)
		elseif event == sgs.TurnStart then
		 if player:getMark("fuxiao") == 0 and player:getMark("gaowen") == 0 and player:getMark("gemingji") == 0 and player:getMark("siluokayi") == 0 then
	            local target = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "jixieshendamage", true, true)
	            room:damage(sgs.DamageStruct("jixieshen", player, target, 1, sgs.DamageStruct_Thunder))
				room:broadcastSkillInvoke(self:objectName(),math.random(1,2))
	            end
			if (player:getMark("gemingji") <= 1 or player:getMark("fuxiao") <= 1 or player:getMark("gaowen") <= 1) and not player:isKongcheng() and player:askForSkillInvoke(self:objectName(), sgs.QVariant("fixmachine")) then
				room:askForDiscard(s, self:objectName(), 1, 1, false, false)
				local choices = {}
				if player:getMark("gemingji") <= 1 then
					table.insert(choices, "gemingji")
				end
				if player:getMark("fuxiao") <= 1 then
					table.insert(choices, "fuxiao")
				end
				if player:getMark("gaowen") <= 1 then
					table.insert(choices, "gaowen")
				end
				local result = room:askForChoice(player, self:objectName(), table.concat(choices, "+"))
				room:addPlayerMark(player, result, 1)
				room:broadcastSkillInvoke(self:objectName(),math.random(3,4))
	        end
			if player:getMark("gemingji") >= 1 or player:getMark("fuxiao") >= 1 or player:getMark("siluokayi") >= 1 or player:getMark("gemingji") >= 1 then
			if player:askForSkillInvoke(self:objectName(), data) then
				local choices = {}
				if player:getMark("gemingji") >= 1 then
					table.insert(choices, "gemingji")
				end
				if player:getMark("fuxiao") >= 1 then
					table.insert(choices, "fuxiao")
				end
				if player:getMark("gaowen") >= 1 then
					table.insert(choices, "gaowen")
				end
				if player:getMark("siluokayi") >= 1 then
					table.insert(choices, "siluokayi")
				end
				local result = room:askForChoice(player, self:objectName(), table.concat(choices, "+"))
				if player:getGeneralName() == "siluokayi" or player:getGeneral2Name() == "siluokayi" then
				room:setPlayerMark(player, "siluokayi", player:getHp())
				end
				if player:getGeneralName() == "gaowen" or player:getGeneral2Name() == "gaowen" then
				room:setPlayerMark(player, "gaowen", player:getHp())
				end
				if player:getGeneralName() == "gemingji" or player:getGeneral2Name() == "gemingji" then
				room:setPlayerMark(player, "gemingji", player:getHp())
				end
				if player:getGeneralName() == "fuxiao" or player:getGeneral2Name() == "fuxiao" then
				room:setPlayerMark(player, "fuxiao", player:getHp())
				end
				local B = player:getHp()
				room:changeHero(player, result, false, false, player:getGeneral2Name() == "siluokayi", true)
				if B >= player:getMark(result) + 1 then
				local C = B - player:getMark(result)
				room:loseHp(player,C)
				else
				room:recover(player, sgs.RecoverStruct(player, nil, player:getMark(result) - B))
				end
				room:setPlayerMark(player, "result", 0)
				local x = math.random(5,6)
				if player:getGeneralName() == "gaowen" or player:getGeneral2Name() == "gaowen" then 
					x = math.random(7,8)
				elseif player:getGeneralName() == "gemingji" or player:getGeneral2Name() == "gemingji" then
					x = math.random(9,10)
				elseif player:getGeneralName() == "siluokayi" or player:getGeneral2Name() == "siluokayi" then
					x = math.random(11,12)
				elseif player:getGeneralName() == "fuxiao" or player:getGeneral2Name() == "fuxiao" then
					x = math.random(13,14)
				end
				room:broadcastSkillInvoke(self:objectName(), x)
				end
			end
		elseif event == sgs.AskForPeachesDone then
			if (player:getGeneralName() == "gemingji" or player:getGeneral2Name() == "gemingji") and room:askForSkillInvoke(player, self:objectName()) then
				local choices = {}
				if player:getMark("fuxiao") >= 1 then
					table.insert(choices, "fuxiao")
				end
				if player:getMark("gaowen") >= 1 then
					table.insert(choices, "gaowen")
				end
				if player:getMark("siluokayi") >= 1 then
					table.insert(choices, "siluokayi")
				end
				local result = room:askForChoice(player, self:objectName(), table.concat(choices, "+"))
				if player:getGeneralName() == "gemingji" or player:getGeneral2Name() == "gemingji" then
				room:setPlayerMark(player, "gemingji", player:getHp())
				end
				local B = player:getHp()
				room:broadcastSkillInvoke(self:objectName(),math.random(15,16))
				room:changeHero(player, result, false, false, player:getGeneral2Name() == "siluokayi", true)
			   if B >= player:getMark(result) + 1 then
				local C = B - player:getMark(result)
				room:loseHp(player,C)
				else
				room:recover(player, sgs.RecoverStruct(player, nil, player:getMark(result) - B))
				end
				room:setPlayerMark(player, "result", 0) 
				if player:getGeneralName() == "gaowen" or player:getGeneral2Name() == "gaowen" then
				elseif player:getGeneralName() == "siluokayi" or player:getGeneral2Name() == "siluokayi" then
				end
				return true
			end
			if (player:getGeneralName() == "fuxiao" or player:getGeneral2Name() == "fuxiao") and player:askForSkillInvoke(self:objectName(), data) then
				local choices = {}
				if player:getMark("gemingji") >= 1 then
					table.insert(choices, "gemingji")
				end
				if player:getMark("gaowen") >= 1 then
					table.insert(choices, "gaowen")
				end
				if player:getMark("siluokayi") >= 1 then
					table.insert(choices, "siluokayi")
				end
				local result = room:askForChoice(player, self:objectName(), table.concat(choices, "+"))
				if player:getGeneralName() == "fuxiao" or player:getGeneral2Name() == "fuxiao" then
					room:setPlayerMark(player, "fuxiao", player:getHp())
				end
				local B = player:getHp()
				room:broadcastSkillInvoke(self:objectName(),math.random(17,18))
				room:changeHero(player, result, false, false, player:getGeneral2Name() == "siluokayi", true)
				if B >= player:getMark(result) + 1 then
				local C = B - player:getMark(result)
				room:loseHp(player,C)
				else
				room:recover(player, sgs.RecoverStruct(player, nil, player:getMark(result) - B))
				end
				room:setPlayerMark(player, result, 0) 
				if result == "gaowen" then
				elseif result == "siluokayi" then
				end
				return true
			end
			if (player:getGeneralName() == "gaowen" or player:getGeneral2Name() == "gaowen") and player:askForSkillInvoke(self:objectName(), data) then
				local choices = {}
				if player:getMark("gemingji") >= 1 then
					table.insert(choices, "gemingji")
				end
				if player:getMark("fuxiao") >= 1 then
					table.insert(choices, "fuxiao")
				end
				if player:getMark("siluokayi") >= 1 then
					table.insert(choices, "siluokayi")
				end
				local result = room:askForChoice(player, self:objectName(), table.concat(choices, "+"))
				if player:getGeneralName() == "gaowen" or player:getGeneral2Name() == "gaowen" then
					room:setPlayerMark(player, "gaowen", player:getHp())
				end
				local B = player:getHp()
				room:broadcastSkillInvoke(self:objectName(),math.random(19,20))
				room:changeHero(player, result, false, false, player:getGeneral2Name() == "siluokayi", true)
				if B >= player:getMark(result) + 1 then
				local C = B - player:getMark(result)
				room:loseHp(player,C)
				else
				room:recover(player, sgs.RecoverStruct(player, nil, player:getMark(result) - B))
				end
				room:setPlayerMark(player, result, 0) 
				if result == "gaowen" then
				elseif result == "siluokayi" then
				end
				 if player:getMark("fuxiao") == 0 and player:getMark("gaowen") == 0 and player:getMark("gemingji") == 0 and player:getMark("siluokayi") == 0 then
	            local target = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "jixieshendamage", true, true)
	            room:damage(sgs.DamageStruct("jixieshen", player, target, 1, sgs.DamageStruct_Thunder))
				room:broadcastSkillInvoke(self:objectName(),math.random(1,2))
	           end
				return true
			end
		end
	end
}

loyal_inu = sgs.CreateTriggerSkill{
	name = "loyal_inu",
	events = {sgs.EventPhaseStart},
	frequency = sgs.Skill_Limited,
	limit_mark = "@inu_from",
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart then			
			local targets = sgs.SPlayerList()
			for _, p in sgs.qlist(room:getOtherPlayers(player)) do
				if player:getMark("@inu_to") == 0 then
					targets:append(p)
				end
			end
			local to = room:askForPlayerChosen(player, targets, self:objectName(), nil, true)
			if to then
				local log = sgs.LogMessage()
				log.type = "#ChoosePlayerWithSkill"
				log.from = player
				log.arg = self:objectName()
				log.to:append(to)
				room:sendLog(log)
				room:setPlayerMark(player, "@inu_from", 0)
				room:setPlayerMark(to, "@inu_to", 1)
				room:broadcastSkillInvoke(self:objectName())
			end
		end
	end,
	can_trigger = function(self, target)
		return target and target:isAlive() 
		and target:hasSkill(self:objectName()) 
		and target:getMark("@inu_from") > 0 
		and target:getPhase() == sgs.Player_Start
	end
}

loyal_inu_damage = sgs.CreateTriggerSkill{
	name = "loyal_inu_damage",
	events = {sgs.DamageInflicted},
	global = true,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local source = room:findPlayerBySkillName("loyal_inu")
		if not source then return false end
		local damage = data:toDamage()
		if damage.damage > 1 then
			local reduce = damage.damage - 1
			damage.damage = 1
			room:sendCompulsoryTriggerLog(source, self:objectName()) --或者自己写一个log
			data:setValue(damage)
			local from = damage.from or nil
			room:damage(sgs.DamageStruct(damage.reason, from, source, reduce, damage.nature))
		end
	end,
	can_trigger = function(self,target)
		return target and target:isAlive() and target:getMark("@inu_to") > 0
	end
}

DSTP = sgs.CreateTriggerSkill{
	name = "DSTP",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageInflicted},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if damage.damage > 1 then
			damage.damage = 1
			room:sendCompulsoryTriggerLog(player, self:objectName()) --或者自己写一个log
			data:setValue(damage)
		end
	end,
}

kikann = sgs.CreateMasochismSkill{
	name = "kikann",
	on_damaged = function(self, player, damage)
		local room = player:getRoom()
		local master
		for _, p in sgs.qlist(room:getAllPlayers()) do
			if p:getMark("@inu_to") > 0 then
				master = p
				break
			end
		end
		local decider = master or damage.from
		if not decider then return false end
		if not room:askForSkillInvoke(player, self:objectName()) then return false end
		local draw_card = false
		room:sendCompulsoryTriggerLog(player, self:objectName()) --或者自己写一个log
		if master then
			master:drawCards(1)
		end
		if not damage.from then
			draw_card = true
		else
			if master and damage.from:canDiscard(damage.from, "he") then
				local choice = room:askForChoice(master, "kikann", "disc+drac") --此处需要ai（不写-->ai随机选择）
				if choice == "drac" then
					draw_card = true
				else
					if not room:askForDiscard(damage.from, self:objectName(), 1, 1, false, true) then
						draw_card = true
					end
				end
			elseif not master and damage.from:canDiscard(damage.from, "he") then
				if not room:askForDiscard(damage.from, self:objectName(), 1, 1, true, true) then
					draw_card = true
				end
			elseif not damage.from:canDiscard(damage.from, "he") then
				draw_card = true
			end
		end
		if draw_card then
			player:drawCards(1)
		end
	end,
	can_trigger = function(self,target)
		return target and target:isAlive() and target:hasSkill(self:objectName())
	end
}
--光子巨炮
guangzijupaoCard = sgs.CreateSkillCard{
	name = "guangzijupaoCard",
	filter = function(self, targets, to_select)
		if (#targets == 0) and to_select:objectName() ~= sgs.Self:objectName() then
			return sgs.Self:inMyAttackRange(to_select)
		end
	end,
	on_use = function(self, room, source, targets)
		local target = targets[1]
		if target:isDead() then return end
		if target:isNude() then
			local ucard = sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_NoSuit, 0)
			ucard:setSkillName("guangzijupao")
			room:useCard(sgs.CardUseStruct(ucard, source, target))
		else
			local id = room:askForCardChosen(source, target, "he", "guangzijupao")
			local cd = sgs.Sanguosha:getCard(id)
			local subcd = sgs.Sanguosha:getCard(self:getSubcards():first())
			room:showCard(target, id)
			if cd:sameColorWith(subcd) then
				local ucard = sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_NoSuit, 0)
				ucard:setSkillName("guangzijupao")
				room:useCard(sgs.CardUseStruct(ucard, source, target))
			else
				room:throwCard(cd, target, source)
			end
		end
	end,
}
guangzijupaoVS = sgs.CreateOneCardViewAsSkill{
	name = "guangzijupao",
	view_filter = function(self, card)
		return not card:isEquipped()
	end,
	view_as = function(self, cards)
		local rdcard = guangzijupaoCard:clone()
		rdcard:addSubcard(cards)
		return rdcard
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#guangzijupaoCard")
	end
}
guangzijupao = sgs.CreateTriggerSkill{
	name = "guangzijupao",
	events = {sgs.PreCardUsed},
	view_as_skill = guangzijupaoVS,
	on_trigger = function(self, event, player, data, room)
		if data:toCardUse().card:getSkillName() == "guangzijupao" then return true end
	end
}
--蓝羽化
lanyuhua = sgs.CreateTriggerSkill{
	name = "lanyuhua",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damage, sgs.Damaged},
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		if damage.card and damage.card:isKindOf("Slash") then
			--[[if event == sgs.Damage then
				room:broadcastSkillInvoke("lanyuhua", 1)
			elseif event == sgs.Damaged then
				room:broadcastSkillInvoke("lanyuhua", 2)
			end]]--
			player:gainMark("@lanyu")
		end
	end,
}
lanyuhuaAtR = sgs.CreateAttackRangeSkill{
	name = "#lanyuhuaAtR",
	extra_func = function(self, player, include_weapon)
		if player:hasSkill("lanyuhua") and player:getMark("baozou") == 0 then
			return math.ceil(player:getMark("@lanyu")/2)
		end
	end,
}
lanyuhuaMxC = sgs.CreateMaxCardsSkill{
	name = "#lanyuhuaMxC",
	extra_func = function(self, target)
		if target:hasSkill("lanyuhua") then
			return math.ceil(target:getMark("@lanyu")/2)
		end
	end
}
extension:insertRelatedSkills("lanyuhua", "#lanyuhuaAtR")
extension:insertRelatedSkills("lanyuhua", "#lanyuhuaMxC")
--暴走
baozou = sgs.CreateTriggerSkill{
	name = "baozou", 
	frequency = sgs.Skill_Wake, 
	events = {sgs.EventPhaseStart}, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local msg = sgs.LogMessage()
		local can_invoke = false
		if player:getMark("@lanyu") >= player:getHp() then
			can_invoke = true
		end
		if can_invoke then
			room:doSuperLightbox("BlackRockShooter", "$baozouQP")
			msg.type = "#baozouEffect"
			msg.arg = "yazhi"
			msg.arg2 = 1
			msg.from = player
			room:sendLog(msg)
			--room:broadcastSkillInvoke("baozou", math.random(1, 2))
			room:setPlayerMark(player, "baozou", 1)
			room:addPlayerMark(player, "@waked")
			local maxhp = player:getMaxHp() + 1
			room:setPlayerProperty(player, "maxhp", sgs.QVariant(maxhp))
			room:recover(player, sgs.RecoverStruct(player))
			room:detachSkillFromPlayer(player, "guangzijupao")
			room:acquireSkill(player, "yazhi")
		end
	end, 
	can_trigger = function(self, target)
		if target and target:isAlive() and target:hasSkill(self:objectName()) then
			if target:getPhase() == sgs.Player_Start then
				return target:getMark("baozou") == 0
			end
		end
	end
}
--压制
yazhiCard = sgs.CreateSkillCard{
	name = "yazhiCard",
	filter = function(self, targets, to_select)
		if (#targets == 0) and to_select:objectName() ~= sgs.Self:objectName() then
			return not to_select:isNude()
		end
	end,
	on_use = function(self, room, source, targets)
		local target = targets[1]
		if target:isDead() then return end
		local choices = {"yzlshp"}
		if source:getMark("@lanyu") >= 2 then table.insert(choices, "yzlsmk") end
		choices = table.concat(choices, "+")
		local choice = room:askForChoice(source, "yazhi", choices)
		if choice == "yzlshp" then
			room:loseHp(source)
		elseif choice == "yzlsmk" then
			source:loseMark("@lanyu", 2)
		end
		--room:broadcastSkillInvoke("yazhi")
		for i = 1, 2, 1 do
			if not target:isNude() then
				local card = sgs.Sanguosha:getCard(room:askForCardChosen(source, target, "he", "yazhi"))
				room:throwCard(card, target, source)
			end
		end
	end,
}
yazhiVS = sgs.CreateZeroCardViewAsSkill{
	name = "yazhi",
	view_as = function(self, cards)
		return yazhiCard:clone()
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#yazhiCard")
	end
}
yazhi = sgs.CreateTriggerSkill{
	name = "yazhi",
	events = {sgs.EventPhaseStart, sgs.PreCardUsed}, 
	view_as_skill = yazhiVS,
	on_trigger = function(self, event, player, data, room) 
		if event == sgs.EventPhaseStart then
			if player:getPhase() == sgs.Player_Play and player:getMark("@lanyu") > 0 then
				--if player:askForSkillInvoke(self:objectName(), data) then
					--room:broadcastSkillInvoke("yazhi")
					local x = math.min(player:getMark("@lanyu"), 2)
					local poi = {}
					for i = 0, x, 1 do
						table.insert(poi, tostring(i))
					end
					local num = tonumber(room:askForChoice(player, "yazhi", table.concat(poi, "+")))
					player:loseMark("@lanyu", num)
					player:drawCards(num, self:objectName())
				--end
			end
		elseif event == sgs.PreCardUsed then
			if data:toCardUse().card:getSkillName() == "yazhi" then return true end
		end
	end
}
--技能暗将
hidden_player:addSkill(guichan)
hidden_player:addSkill(smsy)
hidden_player:addSkill(shilianEX)
--技能添加
--赤瞳
chitong:addSkill(zhuisha)
chitong:addSkill(zhuisha_mod)
chitong:addSkill(ansha)
--海格力斯
haigls:addSkill(shilian)
haigls:addSkill(zzsl)
haigls:addSkill(shilian_usingjudge)
--缠流子
chanlz:addSkill(xianxsy_damage)
chanlz:addSkill(xianxsy_range)
chanlz:addSkill(xianxsy_target)
--c8
aer:addSkill(fengwangVS)
aer:addSkill(wangzheEX)
aer:addSkill(wangzhe)
--桐人
Kirito:addSkill(doubleslash)
Kirito:addSkill(doubleslashMod)
Kirito:addSkill(betacheater)
--立华奏
TachibanaKanade:addSkill(howling)
--四糸乃
Yoshino:addSkill(defencefield)
Yoshino:addSkill(frozenpuppet)
Yoshino:addSkill(frozenpuppetPS)
--初音未来
chuyin:addSkill(chuszy)
chuyin:addSkill(xiaoshi)
--夜刀神十香
ydssx:addSkill(mie)
ydssx:addSkill(Luazuihou)
ydssx:addSkill(Luabei1)
ydssx:addSkill(Luabei2)
--加贺
jiahe:addSkill(Luajianzai)
jiahe:addSkill(Luajianzai_keep)
--炮姐
paoj:addSkill(leij)
paoj:addSkill(leijEx)
paoj:addSkill(diancp)
--夕立
xili_gai:addSkill(Luaemeng)
--川内
chuannei:addSkill(Luayezhan)
chuannei:addSkill(LuayezhanBuff)
--黑雪姬
heixueji:addSkill(jiasushijie)
heixueji:addSkill(juedoujiasu)
heixueji:addSkill(jiasuduijue)
--本多二代
bended:addSkill(qingtq)
bended:addSkill(qingtq_keep)
bended:addSkill(qingtq_keep_keep)
bended:addSkill(xiangy)
--鹿目圆香
lumuyuanxiang:addSkill(jiujideqiyuan)
lumuyuanxiang:addSkill(fazededizao)
--桂木桂马
guimgm:addSkill(gonglzs)
guimgm:addSkill(shens)
--诱宵美九
youxmj:addSkill(pojgj)
youxmj:addSkill(hunq)
--吹雪
chuixue:addSkill(Luamuguan)
chuixue:addSkill(LuamuguanVS)
chuixue:addSkill(LuamuguanBuff)
--杏子
cangjingxingzi:addSkill(soulfireDamage)
cangjingxingzi:addSkill(soulfire)
--夏娜
xiana:addSkill(zhenhongslash)
xiana:addSkill(zhenhong)
xiana:addSkill(duanzui)
--爱尔奎特
aierkuite:addSkill(meihuomoyan)
--七罪
qizui:addSkill(kaleidoscope)
qizui:addSkill(haniel)
--秋濑或
qiulaihuo:addSkill(guancha)
qiulaihuo:addSkill(jiyi)
--赤城
chicheng:addSkill(Luayihang)
chicheng:addSkill(Luachicheng)
chicheng:addSkill(Luayihangpai)
--聂普迪努
niepdl:addSkill(zhujuexz)
--晓美焰
xiaomeiyan:addSkill(pocdsf)
xiaomeiyan:addSkill(lunhui1)
xiaomeiyan:addSkill(lunhui)
--德丽莎
sp_Theresa:addSkill(lolita)
sp_Theresa:addSkill(judas)
--逆回十六夜
nihuisly:addSkill(yuandian)
nihuisly:addSkill(moxing)
nihuisly:addSkill(moxingMC)
--雪风
xuefeng:addSkill(xiangrui)
--上条当麻
dangma:addSkill(wnlz)
dangma:addSkill(hxss)
--亚丝娜
yasina:addSkill(Luachuyi)
yasina:addSkill(Lualianji)
--伊卡洛斯
yikls:addSkill(kznw)
--鲁鲁修
lulux:addSkill(geass)
--结衣
jieyi:addSkill(znai)
jieyi:addSkill(changedfate)
--坂井悠二
youer:addSkill(lsmz)
youer:addSkill(bhjz)
--吉尔伽美什
jejms:addSkill(wangzbk)
jejms:addSkill(bings)
jejms:addSkill(guailj)
--奴良陆生
nlls:addSkill(zhou)
nlls:addSkill(zhouDistance)
nlls:addSkill(ye)
nlls:addRelateSkill("guichan")
--优纪
youj:addSkill(dafan)
youj:addSkill(juej)
youj:addRelateSkill("smsy")
--卡卡罗特
kklt:addSkill(jiewq)
kklt:addSkill(saiya)
--间崎鸣
jianqm:addSkill(bczzr)
jianqm:addSkill(bczzrClear)
jianqm:addSkill(bczzrProhibit)
jianqm:addSkill(mozy)
jianqm:addSkill(mozyProhibit)
--两仪式
liangys:addSkill(qsas)
liangys:addSkill(zsmy)
--绯村剑心
feicunjianxin:addSkill(nirendao)
feicunjianxin:addSkill(nidaoren)
feicunjianxin:addSkill(nidaorenDis)
feicunjianxin:addSkill(badaozhai)
--波风水门
bfsm:addSkill(feils)
bfsm:addSkill(jssg)
bfsm:addRelateSkill("feils2")
--羽入
yuru:addSkill(kuixin)
--言和
yanhe:addSkill(jiushu)
yanhe:addSkill(xieheng)
--美树沙耶香
mssyx:addSkill(tjdzf)
mssyx:addSkill(qrdag)
--我妻由乃
woqiyounai:addSkill(fenmao)
woqiyounai:addRelateSkill("changgui")
woqiyounai:addRelateSkill("heihua")
--古手梨花
gushoulihua:addSkill(smlunhui)
gushoulihua:addSkill(zuihoudefanji)
--欧根亲王
ougenqinwang:addSkill(zhanxianfanyu)
ougenqinwang:addSkill(businiao)
--高文
gaowen:addSkill(jixieshenchain)
gaowen:addSkill(jixieshen)
--革命机
gemingji:addSkill(jixieshenslash)
gemingji:addSkill(jixieshen)
--拂晓
fuxiao:addSkill(jixieshendefense)
fuxiao:addSkill(jixieshen)
--斯洛卡伊
siluokayi:addSkill(jixieshen)
--時雨
shigure:addSkill(loyal_inu)
shigure:addSkill(DSTP)
shigure:addSkill(kikann)
--黑岩射手
BlackRockShooter:addSkill(guangzijupao)
BlackRockShooter:addSkill(lanyuhua)
BlackRockShooter:addSkill(lanyuhuaAtR)
BlackRockShooter:addSkill(lanyuhuaMxC)
BlackRockShooter:addSkill(baozou)
BlackRockShooter:addRelateSkill("yazhi")
--全局技能添加
local skills = sgs.SkillList()
if not sgs.Sanguosha:getSkill("zhou-clear") then skills:append(zhouClear) end
if not sgs.Sanguosha:getSkill("feils2") then skills:append(feils2) end
if not sgs.Sanguosha:getSkill("changgui") then skills:append(changgui) end
if not sgs.Sanguosha:getSkill("heihua") then skills:append(heihua) end
if not sgs.Sanguosha:getSkill("slash_defence") then skills:append(slash_defence) end
if not sgs.Sanguosha:getSkill("loyal_inu_damage") then skills:append(loyal_inu_damage) end
if not sgs.Sanguosha:getSkill("yazhi") then skills:append(yazhi) end
if not sgs.Sanguosha:getSkill("fzndz_skip") then skills:append(fzndz_skip) end
if not sgs.Sanguosha:getSkill("qrdag_ex") then skills:append(qrdagEx) end
if not sgs.Sanguosha:getSkill("hxss_i") then skills:append(hxss_i) end
if not sgs.Sanguosha:getSkill("hxss_mod") then skills:append(hxss_mod) end

sgs.Sanguosha:addSkills(skills)

