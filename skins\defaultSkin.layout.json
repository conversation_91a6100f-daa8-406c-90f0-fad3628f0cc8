{
"common":
{
    "cardNormalWidth": 93,
    "cardNormalHeight": 130,
    "cardMainArea": [0, 0, 93, 130],
    "cardFrameArea": [-5, -5, 98, 135],
    "cardSuitArea": [1, 17, 21, 17],
    "cardNumberArea": [-2, -1, 27, 28],
    "cardFootnoteArea": [5, 75, 85, 50],
    "cardAvatarArea": [26, 67, 40, 40],
    "cardFootnoteFont": ["SimSun", [12, 12, 1], 0, [228, 213, 160, 255], 1, 10, [0, 0], [20, 20, 20, 255]],
    "promptInfoSize": [900, 110],
    "promptInfoFont": ["SimHei", [20, 20, 3], 0, [255, 255, 255, 255], 5, 20, [0, 0], [7, 50, 78, 255]],
    "chooseGeneralBoxSwitchIconSizeThreshold": 12,
    "chooseGeneralBoxSwitchIconEachRow": 6,
    "chooseGeneralBoxSwitchIconEachRowForTooManyGenerals": 7,
    "chooseGeneralBoxNoIconThreshold": 21, 
    "chooseGeneralBoxSparseIconSize": [171, 240],
    "chooseGeneralBoxDenseIconSize": [130, 130],
    "tinyAvatarSize": [40, 40],
    // When hp exceeds 5, we use text (e.g. 5/8) instead of drawing more than 5 magatamas. The color
    // and font are specified below.
    // All the font in skin file takes the following format:
    // simple font: ["fontName", fontSize, fontWeight(bold), foreColor(rgba)] 
    // shawdow font: ["fontName", fontSize, fontWeight(bold), foreColor(rgba), shadowRadius,
    //                shadowDecadeFactor, shadowOffset, shadowDeepestColor]
    // the shadow radius is the width of shadow on each end; the shadow decade factor specifies how
    // fast shadow will disappear; specifying a shadow radius of 5 but decade factor of 0 is equivalent
    // to specify a solid border of width 5.
    // @todo: adjust these colors!
    "magatamaFont": [
        ["Arial", [15, 15, -2], 80, [233, 0, 0, 255], 2, 10, [0, 0], [20, 20, 20, 155]],
        ["Arial", [15, 15, -2], 80, [233, 34, 34, 255], 2, 10, [0, 0], [20, 20, 20, 155]],
        ["Arial", [15, 15, -2], 80, [233, 116, 34, 255], 2, 10, [0, 0], [20, 20, 20, 155]],
        ["Arial", [15, 15, -2], 80, [195, 195, 34, 255], 2, 10, [0, 0], [20, 20, 20, 155]],
        ["Arial", [15, 15, -2], 80, [141, 195, 34, 255], 2, 10, [0, 0], [20, 20, 20, 155]],
        ["Arial", [15, 15, -2], 80, [66, 174, 34, 255], 2, 10, [0, 0], [20, 20, 20, 155]]
    ],
    // I realized that some MOD might need a wider background area than the foreground magatamas in order
    // to draw certain effect. The following factor specifies the number of invisible magatama to be appended. 
    "hpExtraSpaceHolder": 1
},
"room":
{
    "scenePadding": 0,
    "roleBoxHeight": 60,
    "chatTextBoxHeight": 35,
    "logBoxHeightPercentage": 0.6,
    "chatBoxHeightPercentage": 0.4,
    "infoPlaneWidthPercentage": 0.2,
    "photoRoomPadding": 10,
    "photoDashboardPadding": 40,
    "photoHDistance": 32,
    "photoVDistance": 32,
    "discardPileMinWidth": 465,
    "discardPilePadding": 50,
    "minimumSceneSize": [1130, 650], // if smaller than this size, switch to compact skin
    //make sure that default skin's minimum scene size is smaller than maximum scene size in compact skin to avoid deadloop!!!
    "minimumSceneSize-10player": [1050, 800]
},
"photo":
{
    "normalWidth": 157,
    "normalHeight": 181,
    "mainFrameArea": [0, 0, 157, 181],
    "canvasArea": [0, 0, 157, 181],
    "handCardNumIconArea": [3, 94, 21, 22],
    "handCardFont": ["Arial", [12, 12, -1], 20, [255, 255, 255, 255]],
    "equipAreas": [[3, 122, 150, 13], [3, 136, 150, 13], [3, 150, 75, 13], [76, 150, 75, 13], [3, 164, 150, 13]],
    "equipImageArea": [-2, 0, 130, 18],
    "equipSuitArea": [131, -3, 21, 17],
    "equipPointArea": [120, -8, 25, 25],
    "horseImageArea": [-2, 0, 65, 18],
    "horseSuitArea": [58, -3, 21, 17],
    "horsePointArea": [47, -8, 25, 25],
    "equipPointFont": ["font/font.ttf", [13, 13, 3], 20, [0, 0, 0, 255], 1, 1, [0, 0], [255, 255, 190, 128]], 
    "delayedTrickFirstRegion": [-10, 16, 28, 28],
    "delayedTrickStep": [0, 19],
    "roleComboBoxPos": [130, 1],
    "changePrimaryHeroSkinBtnPos": [25, 35],
    "changeSecondaryHeroSkinBtnPos": [32, 75],
    "borderWidth": 15,
    "avatarImageType": 1,
    "secondaryAvatarImageType": 4,
    "primaryAvatarImageType": 6,
    "circleImageType": 0,
    "avatarArea": [4, 27, 148, 89],
    "secondaryAvatarArea": [20, 65, 51, 51],
    "circleArea": [4, 27, 148, 89],
    "avatarNameArea": [-1, 23, 40, 60],
    "avatarNameFont": ["@SimLi", [18, 18, 0], 0, [255, 255, 255, 255], 4, 10, [0, 0], [50, 50, 50, 200]],
    //"smallAvatarNameArea": [25, 25, 30, 45],
    "smallAvatarNameFont": ["@SimLi", [14, 14, 0], 0, [255, 255, 255, 255], 2, 5, [0, 0], [50, 50, 50, 200]],
    "kingdomIconArea": [0, -1, 28, 28],
    "kingdomMaskArea": [-3, 12, 36, 98],
    "screenNameArea": [0, 6, 157, 15],
    "screenNameFont": ["SimSun", 12, 0, [255, 255, 255, 255], 0, 10, [0, 0], [20, 20, 20, 155]],
    "magatamasHorizontal": true,
    "magatamaSize": [14, 17],
    "magatamaImageArea": [-3, 0, 14, 17],
    "magatamasBgVisible": true,
    "magatamasAnchor": ["bottomRight", [163, 115]],
    "cardMoveArea": [-15, 35, 200, 130],
    "phaseArea": [25, 176, 140, 12],
    "onlineStatusArea": [122, 24, 30, 25],
    "onlineStatusBgColor": [0, 0, 0, 0],
    "onlineStatusFont": ["SimSun",12, 0, [255, 255, 255, 255], 3, 10, [0, 0], [20, 20, 20, 155]],
    "skillNameArea": [25, 30, 300, 50],
    "skillNameFont": ["SimLi", 30, 0, [255, 255, 255, 255], 2, 20, [0, 0], [255, 255, 20, 128]],
    "progressBarArea": ["topLeft", "bottomLeft", [0, 6], [157, 13]],
    "progressBarHorizontal": true,
    "privatePileStartPos": [34, 27],
    "privatePileStep": [0, 16],
    "privatePileButtonSize": [75, 16],
    "actionedIconRegion": [100, 78, 52, 21],
    "saveMeIconRegion": [25, 46, 122, 50],
    "votesIconRegion": [53, 66, 50, 50],
    "chainedIconRegion": [0, 108, 156, 19],
    "readyIconRegion": [97, 125, 44, 53],
    "deathIconRegion": [0, 50, 157, 102],
    "drankMaskColor": [250, 0, 0, 115],
    "duanchangMaskColor": [255, 255, 255, 128],
    "deathEffectColor": [50, 50, 50, 255],
    "extraSkillArea": [101, 77, 56, 20],
    "extraSkillFont": ["SimLi", [16, 16, 3], 0, [255, 255, 255, 255], 2, 100, [0, 0], [102, 16, 120, 255]],
    "extraSkillTextArea": [101, 74, 56, 20]
},
"dashboard":
{
    "leftWidth": 164,
    "rightWidth": 171,
    "normalHeight": 170,
    "floatingAreaHeight": 50,
    "handCardNumIconArea": [0, 145, 21, 22],
    "handCardFont": ["Arial", [12, 12, -1], 20, [255, 255, 255, 255]],
    "equipAreas": [[7, 42, 149, 25], [7, 67, 149, 25], [7, 92, 149, 25], [7, 117, 149, 25], [7, 142, 149, 25]],
    "equipBorderPos": [-6, -6],
    "equipSelectedOffset": [10, 0],
    "equipImageArea": [0, 0, 149, 25],
    "equipSuitArea": [128, 5, 21, 17],
    "equipPointArea": [117, -4, 30, 30],
    "horseImageArea": [0, 0, 149, 25],
    "horseSuitArea": [128, 5, 21, 17],
    "horsePointArea": [117, -5, 30, 30],
    "equipPointFont": ["font/font.ttf", [14, 14, 3], 20, [0, 0, 0, 255], 1, 0, [0, 0], [240, 240, 240, 200]], 
    "delayedTrickFirstRegion": [3, 3, 28, 28],
    "delayedTrickStep": [28, 0],
    // the width of the region to disperse cards when the cards are to be moved to the special pile. 
    "disperseWidth": 250,
    "markTextArea": [0, 0, 50, 50],
    "roleComboBoxPos": [75, 5],
    "changePrimaryHeroSkinBtnPos": [25, 30],
    "changeSecondaryHeroSkinBtnPos": [102, 30],
    "borderWidth": 6,
    "avatarImageType": 2,
    "secondaryAvatarImageType": 5,
    "primaryAvatarImageType": 7,
    "circleImageType": 1,
    "avatarArea": [8, 10, 134, 134],
    "secondaryAvatarArea": [81, 10, 70, 70],
    "circleArea": [8, 10, 134, 134],
    "avatarNameArea": [-1, 23, 40, 60],
    "focusFrameArea": [8, 10, 134, 134],
    "smallAvatarNameArea": [74, 23, 40, 60],
    "avatarNameFont": ["@SimLi", [18, 18, 0], 0, [255, 255, 255, 255], 4, 10, [0, 0], [50, 50, 50, 200]],
    "smallAvatarNameFont": ["@SimLi", [18, 18, 0], 0, [255, 255, 255, 255], 4, 10, [0, 0], [50, 50, 50, 200]],
    // must be in one of the following format:
    // [offsetX, offestY, sizeX, sizeY]
    // [childAnchor, parentAnchor, [offsetX, offsetY]]
    // [childAnchor, parentAnchor, [offsetX, offsetY], [sizeX, sizeY]]
    // if childAnchor and/or parentAnchor are not set, then area will be aligned using top left corner of both child and parent
    // otherwise, the corner of child specified by "childAnchor" will first be aligned to that of parent specified by "parentAnchor",
    // and offset if applied after the alignment is done. When the 4th parameter [sizeX, sizeY] is specified, fixed size of the area
    // is used; otherwise, it means area's size is variable with the content of that area, the program will adjust the size automatically
    "markTextArea": ["bottomRight", "bottomRight",  [-10, -10]],
    "phaseArea": ["bottomRight", "bottomRight", [-100, 2], [209, 18]],
    "kingdomIconArea": [0, 0, 28, 28],
    "kingdomMaskArea": [-3, 13, 36, 98],
    "screenNameArea": [0, 7, 157, 25],
    "screenNameFont": ["SimSun", 12, 0, [255, 255, 255, 255], 2, 10, [0, 0], [0, 0, 0, 224]],
    "magatamasHorizontal": false,
    "magatamaSize": [23, 30],
    "magatamaImageArea": [0, 3, 22, 26],
    "magatamasBgVisible": false,
    "magatamasAnchor": ["bottomRight", [165, 190]],
    "cardMoveArea": [-50, 65, 200, 130],
    "skillNameArea": [25, 30, 300, 50],
    "skillNameFont": ["SimLi", 30, 0, [255, 255, 255, 255], 2, 20, [0, 0], [0, 0, 255, 128]],
    "progressBarArea": ["bottomCenter", "bottomCenter", [-8, 0], [262, 15]],
    "progressBarHorizontal": true,
    "privatePileStartPos": [26, 13],
    "privatePileStep": [0, 16],
    "privatePileButtonSize": [75, 16],
    "actionedIconRegion": [84, 30, 52, 21],
    "saveMeIconRegion": [24, 15, 122, 50],
    "votesIconRegion": [50, 52, 50, 50],
    "chainedIconRegion": [8, 90, 134, 19],
    "readyIconRegion": [86, 102, 44, 53],
    "deathIconRegion": ["center", "center", [0, 12], [172, 112]],
    "drankMaskColor": [250, 0, 0, 115],
    "duanchangMaskColor": [255, 255, 255, 128],
    "buttonSetSize": [106, 168],
    "confirmButtonArea": [5, 3, 61, 75],
    "cancelButtonArea": [4, 92, 61, 75],
    "discardButtonArea": [70, 45, 33, 81],
    "trustButtonArea": [68, 132, 36, 35],
    "trustEffectColor": [38, 26, 66]
},
"skillButton":
{
    "height": 19,
    "width": [134, 66, 44], 
    "textArea": [[20, -2, 112, 20], [20, -2, 44, 20], [9, -2, 40, 20]],
    "textAreaDown": [[21, -1, 112, 20], [21, -1, 44, 20], [10, -1, 40, 20]],
    "textFont": [["simkai", [13, 13, 2], 50, [0, 0, 0, 255], 1, 1, [0, 0], [255, 255, 190, 128]],  // wide
                 ["simkai", [13, 13, 1], 50, [0, 0, 0, 255], 1, 1, [0, 0], [255, 255, 190, 128]],  // medium
                 ["simkai", [13, 13, 0], 50, [0, 0, 0, 255], 1, 1, [0, 0], [255, 255, 190, 128]]],  // narrow

    // The following settings override the text color defined above
    // xxButtonColor = [normalColor, downColor, disabledColor, hoverColor]
    "awakenFontColor": [[[255, 255, 255, 255], [0, 0, 0, 120]], // normalColor = [penColor, shadowColor]
                        [[255, 255, 255, 255], [0, 0, 0, 120]], // downColor = [penColor, shadowColor]
                        [[255, 255, 255, 255], [0, 0, 0, 120]], // disableColor = [penColor, shadowColor]
                        [[255, 255, 255, 255], [0, 0, 0, 120]]], // hoverColor = [penColor, shadowColor]
    "compulsoryFontColor": [[[255, 255, 255, 255], [0, 0, 0, 120]],
                            [[255, 255, 255, 255], [0, 0, 0, 120]],
                            [[255, 255, 255, 255], [0, 0, 0, 120]],
                            [[255, 255, 255, 255], [0, 0, 0, 120]]],
    "frequentFontColor": [[[255, 255, 255, 255], [0, 0, 0, 120]],
                          [[255, 255, 255, 255], [0, 0, 0, 120]],
                          [[255, 255, 255, 255], [0, 0, 0, 120]],
                          [[255, 255, 255, 255], [0, 0, 0, 120]]],
    "oneoffFontColor": [[[255, 255, 255, 255], [0, 0, 0, 120]],
                        [[255, 255, 255, 255], [0, 0, 0, 120]],
                        [[255, 255, 255, 255], [0, 0, 0, 120]],
                        [[255, 255, 255, 255], [0, 0, 0, 120]]],
    "proactiveFontColor": [[[255, 255, 255, 255], [0, 0, 0, 120]],
                           [[255, 255, 255, 255], [0, 0, 0, 120]],
                           [[255, 255, 255, 255], [0, 0, 0, 120]],
                           [[255, 255, 255, 255], [0, 0, 0, 120]]],
    "attachedlordFontColor": [[[255, 255, 255, 255], [0, 0, 0, 120]],
                              [[255, 255, 255, 255], [0, 0, 0, 120]],
                              [[255, 255, 255, 255], [0, 0, 0, 120]],
                              [[255, 255, 255, 255], [0, 0, 0, 120]]],
    "changeFontColor": [[[255, 255, 255, 255], [0, 0, 0, 120]],
                            [[255, 255, 255, 255], [0, 0, 0, 120]],
                            [[255, 255, 255, 255], [0, 0, 0, 120]],
                            [[255, 255, 255, 255], [0, 0, 0, 120]]],
}
}
