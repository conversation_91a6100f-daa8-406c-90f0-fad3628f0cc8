# 诳言技能问题修复

## 🚨 发现的问题

根据老板的反馈，诳言技能存在两个问题：

### 问题1：乐不思蜀目标错误
**现象**：乐不思蜀对技能发出者使用
**期望**：乐不思蜀对拼点对象自身使用

### 问题2：秽牌判断错误  
**现象**：秽牌被当成乐不思蜀使用
**期望**：秽牌应该正常使用

## 📋 测试日志分析

```
SP稀神探女 发动了"诳言"，目标是 若鹭姬
SP稀神探女 向 若鹭姬 发起了拼点
SP稀神探女 的拼点牌为 秽[6]
若鹭姬 的拼点牌为 桃园结义[A]
SP稀神探女 (对 若鹭姬) 拼点赢！
若鹭姬 使用了 桃园结义[A]，目标是 若鹭姬, SP稀神探女
若鹭姬 发动了"诳言"将 秽[6] 当成 乐不思蜀[6] 使用，目标是 若鹭姬
```

### 分析结果

#### 1. 乐不思蜀目标分析
- **实际目标**：若鹭姬（拼点对象）
- **技能描述**："将余下的拼点牌当【乐不思蜀】对自身使用"
- **结论**：目标是正确的，"自身"指拼点对象自己

#### 2. 秽牌使用分析
- **秽牌性质**：锦囊牌，应该可以正常使用
- **实际情况**：被当成乐不思蜀使用
- **原因**：卡牌使用条件检查失败，进入了剩余牌列表

## 🔧 修复方案

### 修复1：加强卡牌使用条件检查
```lua
-- 修复前：简单的类型判断
elseif card:isKindOf("TrickCard") then
    if card:isKindOf("AOE") or card:targetFixed() then
        room:useCard(use, false)
        can_use = true
    else
        use.to:append(chosen_target)
        room:useCard(use, false)
        can_use = true
    end

-- 修复后：详细的有效性检查
elseif card:isKindOf("TrickCard") then
    if card:isKindOf("DelayedTrick") then
        -- 延时锦囊：检查目标有效性
        if card:targetFilter(sgs.PlayerList(), chosen_target, targets[1]) then
            use.to:append(chosen_target)
            room:useCard(use, false)
            can_use = true
        end
    elseif card:isKindOf("AOE") then
        -- 群体锦囊
        room:useCard(use, false)
        can_use = true
    elseif card:targetFixed() then
        -- 无目标锦囊
        room:useCard(use, false)
        can_use = true
    else
        -- 单体锦囊：检查目标有效性
        if card:targetFilter(sgs.PlayerList(), chosen_target, targets[1]) then
            use.to:append(chosen_target)
            room:useCard(use, false)
            can_use = true
        else
            -- 如果目标过滤失败，尝试无目标使用
            if card:canRecast() or not card:isTargeted() then
                room:useCard(use, false)
                can_use = true
            end
        end
    end
```

### 修复2：添加卡牌可用性检查
```lua
-- 在使用卡牌前添加检查
if card:isAvailable(targets[1]) and not targets[1]:isCardLimited(card, card:getHandlingMethod()) then
    -- 执行卡牌使用逻辑
end
```

### 修复3：完善乐不思蜀使用条件
```lua
-- 检查是否可以对拼点对象使用乐不思蜀
if indulgence:isAvailable(targets[1]) and indulgence:targetFilter(sgs.PlayerList(), targets[1], targets[1]) then
    local indulgence_use = sgs.CardUseStruct()
    indulgence_use.from = targets[1]  -- 拼点对象使用
    indulgence_use.to:append(targets[1])  -- 对自己使用
    indulgence_use.card = indulgence
    room:useCard(indulgence_use, false)
end
```

## 🎯 秽牌特殊处理

### 秽牌的特性
- **类型**：锦囊牌
- **效果**：通常是单体目标锦囊
- **使用条件**：需要目标，但可能有特殊的目标限制

### 可能的问题
1. **目标过滤失败**：`card:targetFilter()` 返回false
2. **距离限制**：目标不在攻击范围内
3. **特殊限制**：卡牌有特殊的使用限制

### 解决方案
```lua
-- 对于单体锦囊，如果目标过滤失败，尝试其他方式
if card:targetFilter(sgs.PlayerList(), chosen_target, targets[1]) then
    use.to:append(chosen_target)
    room:useCard(use, false)
    can_use = true
else
    -- 如果目标过滤失败，检查是否可以无目标使用
    if card:canRecast() or not card:isTargeted() then
        room:useCard(use, false)
        can_use = true
    end
end
```

## 📊 修复效果预期

### 修复后的预期行为
1. **秽牌**：应该正常使用，不进入剩余牌列表
2. **桃园结义**：正常使用（已经正确）
3. **乐不思蜀**：只有真正不能使用的牌才会转换

### 测试验证点
- [ ] 秽牌是否正常使用
- [ ] 乐不思蜀的目标是否正确
- [ ] 不同类型卡牌的使用是否正确
- [ ] 边界情况的处理是否正确

## 🚀 下一步测试

请重新测试诳言技能，特别关注：
1. 秽牌是否还会被当成乐不思蜀
2. 其他锦囊牌的使用是否正常
3. 只有真正不能使用的牌才会转换为乐不思蜀

如果问题仍然存在，可能需要进一步分析秽牌的具体使用条件。
