extension1 = sgs.Package("god_po", sgs.Package_GeneralPack)
huangzhong_po = sgs.General(extension1, "huangzhong_po", "shu")
liegong_po = sgs.CreateTriggerSkill{
	name = "liegong_po", 
	events = {sgs.TargetSpecified, sgs.DamageCaused}, 
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		local room = player:getRoom()
		if event == sgs.TargetSpecified and player:objectName() == use.from:objectName() and use.from:hasSkill(self:objectName()) and use.card:isKindOf("Slash") and player:getMark("liegong_play") == 0 and player:getPhase() == sgs.Player_Play then
			local index = 1
			local jink_table = sgs.QList2Table(player:getTag("Jink_" .. use.card:toString()):toIntList())
			for _, p in sgs.qlist(use.to) do
				if not player:isAlive() or player:getMark("liegong_play") ~= 0  then break end
				local invoke = 0
				if p:getAttackRange() <= player:getAttackRange() then
					invoke = invoke+1
				end
				if p:getHandcardNum() >= player:getHandcardNum() then
					invoke = invoke+1
				end
				if p:getHp() >= player:getHp() then
					invoke = invoke+1
				end
				room:addPlayerMark(player, "liegong_play")	
				local _data = sgs.QVariant()
				_data:setValue(p)
				if invoke >= 1 and room:askForSkillInvoke(player, self:objectName(), _data) then
					jink_table[index] = 0
					if invoke >= 2 then
						room:setCardFlag(use.card, "liegong_play"..p:objectName())	
					end
				end
				index = index+1
			end
			local jink_data = sgs.QVariant()
			jink_data:setValue(Table2IntList(jink_table))
			player:setTag("Jink_" .. use.card:toString(), jink_data)
		elseif event == sgs.DamageCaused then
			local damage = data:toDamage()
			if damage.card and damage.card:hasFlag("liegong_play"..damage.to:objectName()) and room:askForSkillInvoke(player, self:objectName(), data) then
				local log = sgs.LogMessage()
				log.type = "$hanyong"
				log.from = player
				log.card_str = damage.card:toString()
				log.arg = self:objectName()
				room:sendLog(log)
				damage.damage = damage.damage+1
				data:setValue(damage)
			end
		end
		return false
	end
}
huangzhong_po:addSkill(liegong_po)
--[[xiaoqiao_po = sgs.General(extension1, "xiaoqiao_po", "wu", 3, false, false)
tianxiangCard = sgs.CreateSkillCard{
	name = "tianxiang", 
	filter = function(self, selected, to_select)
		return (#selected == 0) and (to_select:objectName() ~= sgs.Self:objectName())
	end, 
	on_effect = function(self, effect)
		local room = effect.to:getRoom()
		effect.to:addMark("tianxiangTarget")
		local damage = effect.from:getTag("tianxiangDamage"):toDamage()
		if damage.card and damage.card:isKindOf("Slash") then
			effect.from:removeQinggangTag(damage.card)
		end
		damage.to = effect.to
		damage.transfer = true
		room:damage(damage)
	end
}
tianxiangVS = sgs.CreateOneCardViewAsSkill{
	name = "tianxiang", 
	filter_pattern = ".|heart|.|hand", 
	view_as = function(self, card)
		local SkillCard = tianxiangCard:clone()
		SkillCard:addSubcard(card)
		return SkillCard
	end, 
	enabled_at_play = function()
		return false
	end, 
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@tianxiang"
	end
}
tianxiang = sgs.CreateTriggerSkill{
	name = "tianxiang", 
	events = {sgs.DamageInflicted}, 
	view_as_skill = tianxiangVS, 
	on_trigger = function(self, event, player, data)
		if player:canDiscard(player, "h") then
			player:setTag("tianxiangDamage", data)
			return player:getRoom():askForUseCard(player, "@@tianxiang", "@tianxiang-card", -1, sgs.Card_MethodDiscard)
		end
		return false
	end
}
tianxiangDraw = sgs.CreateTriggerSkill{
	name = "#tianxiang", 
	events = {sgs.DamageComplete}, 
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		if player:isAlive() and (player:getMark("tianxiangTarget") > 0) and damage.transfer then
			player:drawCards(player:getLostHp())
			player:removeMark("tianxiangTarget")
		end
		return false
	end, 
	can_trigger = function(self, target)
		return target
	end
}]]--
sunjian_po = sgs.General(extension1, "sunjian_po", "wu")
yinghun_po = sgs.CreatePhaseChangeSkill{
	name = "yinghun_po", 
	on_phasechange = function(self, player)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Start and player:isWounded() then
			local to = room:askForPlayerChosen(player, room:getOtherPlayers(player), self:objectName(), "yinghun-invoke", true, true)
			local x = player:getLostHp()
			if player:getEquips():length() >= player:getHp() then
				x = player:getMaxHp()
			end
			local choices = {"yinghun1"}
			if to then
				if not to:isNude() and x ~= 1 then
					table.insert(choices, "yinghun2")
				end
				local choice = room:askForChoice(player, self:objectName(), table.concat(choices, "+"))
				ChoiceLog(player, choice)
				if choice == "yinghun1" then
					to:drawCards(1)
					room:askForDiscard(to, self:objectName(), x, x, false, true)
				else
					to:drawCards(x)
					room:askForDiscard(to, self:objectName(), 1, 1, false, true)
				end
				room:broadcastSkillInvoke(self:objectName())
			end
		end
		return false
	end
}
sunjian_po:addSkill(yinghun_po)
yuanshao_po = sgs.General(extension1, "yuanshao_po$", "qun")
luanji_poVS = sgs.CreateViewAsSkill{
	name = "luanji_po",
	n = 2,
	view_filter = function(self, selected, to_select)
		return  #selected < 2 and not to_select:isEquipped() and sgs.Self:getMark(self:objectName()..to_select:getSuitString().."-Clear") == 0
	end,
	view_as = function(self, cards)
		if #cards ~= 2 then return nil end
		local card = sgs.Sanguosha:cloneCard("archery_attack", sgs.Card_SuitToBeDecided, 0)
		card:addSubcard(cards[1])
		card:addSubcard(cards[2])
		card:setSkillName(self:objectName())
		return card
	end
}
luanji_po = sgs.CreateTriggerSkill{
	name = "luanji_po", 
	view_as_skill = luanji_poVS, 
	global = true, 
	events = {sgs.CardUsed, sgs.CardEffected, sgs.CardResponded}, 
	on_trigger = function(self, event, player, data, room)
		if event == sgs.CardUsed then
			local use = data:toCardUse()
			if use.card:getSkillName() == "luanji_po" then
				for _,id in sgs.qlist(use.card:getSubcards()) do
					room:addPlayerMark(player, self:objectName()..sgs.Sanguosha:getCard(id):getSuitString().."-Clear")
				end
			end
		elseif event == sgs.CardEffected then
			local effect = data:toCardEffect()
			if effect.card:getSkillName() == "luanji_po" then
				room:addPlayerMark(player, "luanji-Clear")
			end
		else
			local response = data:toCardResponse()
			if response.m_card:isKindOf("Jink") and response.m_isRetrial == false and response.m_isUse == false and player:getMark("luanji-Clear") > 0 then
				if player:isWounded() then
					room:sendCompulsoryTriggerLog(response.m_who, self:objectName())
					player:drawCards(1, self:objectName())
				end
				room:removePlayerMark(player, "luanji-Clear")
			end
		end
	end
}
yuanshao_po:addSkill(luanji_po)
yuanshao_po:addSkill("xueyi")
--[[caojie = sgs.General(extension, "caojie", "qun", 3, false, true)
zuyin = sgs.CreateTriggerSkill{
	name = "zuyin", 
	events = {sgs.TargetConfirmed, sgs.CardEffected}, 
	on_trigger = function(self, event, player, data, room)
		if event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			for _, p in sgs.qlist(use.to) do
				local _data = sgs.QVariant()
				_data:setValue(p)
				if player and player:isAlive() and player:hasSkill(self:objectName()) and p:objectName() ~= use.from:objectName() and use.from:objectName() ~= player:objectName() and (use.card:isKindOf("Slash") or use.card:isNDTrick()) and player:distanceTo(p) < 2 and room:askForSkillInvoke(player, self:objectName(), _data) then
					local ids = sgs.IntList()
					for _, id in sgs.qlist(room:getDrawPile()) do
						if not sgs.Sanguosha:getCard(id):isKindOf("BasicCard") then
							ids:append(id)
							break
						end
					end
					if not ids:isEmpty() then
						local move = sgs.CardsMoveStruct()
						move.card_ids = ids
						move.to = player
						move.to_place = sgs.Player_PlaceTable
						move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TURNOVER, player:objectName(), self:objectName(), nil)
						room:moveCardsAtomic(move, true)
						local card = sgs.Sanguosha:getCard(ids:first())
						if card:getSuit() == use.card:getSuit() then
							room:addPlayerMark(p, self:objectName().."-Clear", use.card:getId())
							room:setCardFlag(use.card, self:objectName())
							room:throwCard(card, nil, nil)
						else
							player:obtainCard(card)
						end
					end
				end
			end
		else
			local effect = data:toCardEffect()
			if effect.card:hasFlag(self:objectName()) and (effect.card:isKindOf("Slash") or effect.card:isNDTrick()) and effect.to:getMark(self:objectName().."-Clear") == effect.card:getId() then
				room:setPlayerMark(effect.to, self:objectName().."-Clear", 0)
				return true 
			end
		end
		return false
	end, 
	can_trigger = function(self, target)
		return target
	end
}
caojie:addSkill(zuyin)
tianzuo = sgs.CreatePhaseChangeSkill{
	name = "tianzuo", 
	frequency = sgs.Skill_Compulsory, 
	on_phasechange = function(self, player)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Start and player:getHandcardNum() > player:getHp() then
			local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			for _, card in sgs.qlist(player:getHandcards()) do
				if not card:isKindOf("BasicCard") then
					dummy:addSubcard(card:getId())
				end
			end
			room:throwCard(dummy, player, player)
			room:addPlayerMark(player, self:objectName(), dummy:subcardsLength())
			room:askForUseCard(player, "@@tianzuoEX", "@tianzuoEX")
			room:setPlayerMark(player, self:objectName(), 0)
		end
		return false
	end
}
caojie:addSkill(tianzuo)]]--
return {extension1}