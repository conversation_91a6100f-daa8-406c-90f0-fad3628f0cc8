--hahaha

math.randomseed(tostring(os.time()):reverse():sub(1, 7)) --------设置时间种子

Pay = require "paysage" --加载价值模组

local Luaxinwu_skill={} -- 初始化 Luaxinwu_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 Luaxinwu_skill 只是为了出于习惯
Luaxinwu_skill.name="Luaxinwu" -- 设置 name
table.insert(sgs.ai_skills, Luaxinwu_skill) -- 把这个表加入到 sgs.ai_skills 中

Luaxinwu_skill.getTurnUseCard = function(self) --考虑留杀的情况，防嘲讽等
-- 这个函数的作用仅仅是让 AI 在出牌阶段考虑使用技能卡的可能性
-- 至于对谁使用，子卡是什么，这里先不用管
	if self.player:hasUsed("#Luaxinwu") or self.player:isKongcheng() then return end
	local cards = self:getTurnUse(true)
	local bref = false 
	for _, card in ipairs(cards) do
		if card:isKindOf("EquipCard") or card:isKindOf("ExNihilo") then bref = true end 
	end 
	if not bref then 
		return sgs.Card_Parse("#Lu<PERSON>inwu:.:")  --我必取你们狗头
	end 
end 
function getMaxCard(self, zhongzhang, if_peach , to)
	local handcards = self.player:getHandcards()
	local equip_num = self.player:getEquips()
	if equip_num then equip_num = equip_num:length() else equip_num = 0  end 
	local MaxCard    --最古老之秘技！冒泡排序法！
	local allcards = {}
	if to then 
		for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
			if c:getId() ~= to:getId() then table.insert(allcards, c) end 
		end
	end 
	if #allcards == 0 then return end 
	if zhongzhang then  
		MaxCard = allcards[1]
		for i = 1, #allcards do  
			if Qinxin(allcards[i]:getNumber(), equip_num, if_peach) > Qinxin(MaxCard:getNumber(), equip_num, if_peach) then  
				MaxCard = allcards[i]  
			end  		
		end  
		if not MaxCard then MaxCard = allcards[#allcards] end 
	elseif not zhongzhang then 
		MaxCard = allcards[1]
		for i = 1, #allcards do  
			if allcards[i]:getNumber() > MaxCard:getNumber() then  
				MaxCard = allcards[i]  
			end  		
		end  
		if not MaxCard then MaxCard = allcards[#allcards] end 		
	end 	
	return MaxCard
end 
function Qinxin(x, y, if_peach)
	local abc
	if if_peach then 
		abc = (6-x)/2
	else
		abc = (8-x)/2
	end 
	return 1 - math.abs(math.pow(abc,3))*(1-y/10)*(1-y/10)+(8*y/50)
end 
function Qinxinzika(self, to_win)
	--决定小心心的技能子卡
		local cards = self.player:getCards("h")
		cards = sgs.QList2Table(cards)
		local jink_num = self:getCardsNum("Jink")
		local peach_num = self:getCardsNum("Peach")
		local jpg = jink_num + peach_num - self.player:getLostHp()
		local min_card = self:getMinCard()
		local max_card = self:getMaxCard()
		local zhongzhang_card = getMaxCard(self, true, false)
		local function diancha(card1, card2)
			local x = card1:getNumber() - card2:getNumber()
			return math.abs(x)
		end 
		if to_win == 0 then 
			if (jink_num >= 2) or (jpg >= 2) then  
				self:sortByUseValue(cards)
				if cards[1]:getId() == max_card:getId() and (cards[1]:getNumber() > 9) then 
					local duel = sgs.Sanguosha:cloneCard("duel")
					if (self:getUseValue(cards[2]) >= self:getUseValue(duel)) or (diancha(cards[1], cards[2]) >= 4) then 
						return cards[1]
					else
						return cards[2]
					end 
				else
					return cards[1]
				end 
			else
				self:sortByKeepValue(cards)
				if cards[1]:getId() == max_card:getId() and (cards[1]:getNumber() > 9) then 
					local jink = sgs.Sanguosha:cloneCard("jink")
					if self:getUseValue(cards[2]) >= self:getUseValue(jink) and (diancha(cards[1], cards[2]) < 4) then 
						return cards[1]
					else
						return cards[2]
					end 
				else
					return cards[1]
				end 				
			end 
		elseif to_win == 1 then 
			if (jink_num >= 2) or (jpg >= 2) then  
				self:sortByUseValue(cards)
				if cards[1]:getId() == min_card:getId() and (cards[1]:getNumber() < 4)  then 
					local duel = sgs.Sanguosha:cloneCard("duel")
					if self:getUseValue(cards[2]) >= self:getUseValue(duel) and (diancha(cards[1], cards[2]) < 4) then 
						return cards[1]
					else
						return cards[2]
					end 
				else
					return cards[1]
				end 
			else
				self:sortByKeepValue(cards)
				if cards[1]:getId() == min_card:getId() and (cards[1]:getNumber() < 4) then 
					local jink = sgs.Sanguosha:cloneCard("jink")
					if self:getUseValue(cards[2]) >= self:getUseValue(jink) and (diancha(cards[1], cards[2]) < 4) then 
						return cards[1]
					else
						return cards[2]
					end 
				else
					return cards[1]
				end 				
			end 	
		elseif to_win == 2 then 
			if (jink_num >= 2) or (jpg >= 2) then  
				self:sortByUseValue(cards)
				return cards[1]
			else
				self:sortByKeepValue(cards)
				return cards[1]				
			end 			
		end 
end 
sgs.ai_skill_use_func["#Luaxinwu"] = function(cardS, use, self)
	if self.player:isKongcheng() then return end 
	local Qx_zhongzhang_to = Qinxinzika(self, 2)
	local Qx_min_to = Qinxinzika(self, 1)
	local Qx_max_to = Qinxinzika(self, 0)	
	local use_anal = false
	--决定是否使用桃奶自己
		local shouldUsePeach = false
		local card_peach = sgs.Sanguosha:cloneCard("peach")
		local dummy_use_peach = {isDummy = true}
		self:useBasicCard(card_peach, dummy_use_peach)
		if dummy_use_peach.card then
			self.room:writeToConsole("秦心需要使用一张桃")
			shouldUsePeach = true 
		end
	--是否可以用 “酒”
        if sgs.Analeptic_IsAvailable(self.player) then
            -- local anal = sgs.Sanguosha:cloneCard("analeptic")
            -- anal:deleteLater()
            -- local dummy_use = {
                -- isDummy = true,
            -- }
            -- self:useBasicCard(anal, dummy_use)
            -- if dummy_use.card then
                -- use_anal = true
			-- end 
		end 
	--
	--首先开始和队友配合
	local qinxinPara_card = getMaxCard(self, true, shouldUsePeach, Qx_zhongzhang_to)
	if qinxinPara_card then 
		local qinxinPara = qinxinPara_card:getNumber()
		--self.room:writeToConsole("秦心参数 qinxinPara_card 为" .. qinxinPara)
		local equip_num = self.player:getEquips()
		if equip_num then equip_num = equip_num:length() else equip_num = 0  end 
		qinxinPara = Qinxin(qinxinPara,equip_num)	
		
		--self.room:writeToConsole("秦心参数 qinxinPara 为" .. qinxinPara)
		--自家没受伤决定酒杀，友军有损失两血以上
			local cards = self.player:getCards("h")
			cards = sgs.QList2Table(cards)
			self:sortByUseValue(cards, true)
			local shouldUseWine = false
			local shouldUseSlash = false

			for _,card in ipairs(cards) do				
				if card:isKindOf("Slash") then
					local dummy_use = {isDummy = true}
					self:useBasicCard(card, dummy_use)
					if dummy_use.card then 
						shouldUseWine = true 
						self.room:writeToConsole("秦心需要使用一张酒")
						break
					end
				end 
			end 
			if self:getCardsNum("Slash") == 0 and self:getCardsNum("Analeptic") >= 0 then
				if Pay.DummyUseSlash(self) ~= false and use_anal then 
					self.room:writeToConsole("秦心需要使用一张杀")
					shouldUseSlash = true 
				end 
			end 
			if (shouldUsePeach or shouldUseWine or shouldUseSlash) and Qx_zhongzhang_to
				and (self.player:getHandcardNum() > 1) then 
				local qinxinPara_0 = 0
				local qinxinPara_0_max = 0
				local n_0 = 0
				local toPlayer
				for _, friend in ipairs(self.friends_noself) do
					if not friend:isKongcheng() and ((friend:getLostHp() >= 2) or (friend:getLostHp() >= 1 and shouldUsePeach)) then 
						local para_3 = friend:getHandcardNum()
						qinxinPara_0 = qinxinPara_0 + qinxinPara*(math.pow(para_3,2)/20+para_3/30) 
						self.room:writeToConsole("秦心参数 qinxinPara_0 为" .. qinxinPara_0)
						if qinxinPara_0 > qinxinPara_0_max then
							qinxinPara_0_max = qinxinPara_0
							self.room:writeToConsole("秦心参数 qinxinPara_0_max 为" .. qinxinPara_0_max)
							toPlayer = friend
						end 
						n_0 = n_0 + 1
					end 
				end 
				if n_0 > 0 and toPlayer then 
					qinxinPara_0 = qinxinPara_0/n_0
					if not shouldUsePeach then qinxinPara_0 = math.pow(qinxinPara_0,2.5) end 
					self.room:writeToConsole("秦心参数 qinxinPara_0 为" .. qinxinPara_0)				
					if math.random() < qinxinPara_0 then 
						-- local tocards = self.player:getHandcards() -- 获得所有手牌
						-- tocards = sgs.QList2Table(tocards) -- 将列表转换为表
						-- self:sortByKeepValue(tocards) -- 按保留值排序
						use.card = sgs.Card_Parse("#Luaxinwu:" .. Qx_zhongzhang_to:getEffectiveId()..":")
						if use.to then 
							use.to:append(toPlayer) 
							self.room:writeToConsole("秦心测试")
							if shouldUseWine or shouldUsePeach then use.to:append(self.player) end 
						end				
						return					
					end 
				end 
			end 
		end 
	-- 拿装备  或 酒杀
	-- 退一万步说，我满血输了，还可以酒杀
			-- local card_str = ("analeptic:jiuchi[spade:%s]=%d"):format(number, card_id)
			-- local analeptic = sgs.Card_Parse(card_str)
			-- if sgs.Analeptic_IsAvailable(self.player, analeptic) then
			-- end 
	local enemies_0 = {}
	for _, enemy in ipairs(self.enemies) do
		if not enemy:isKongcheng() then table.insert(enemies_0, enemy) end 
	end 
	if #enemies_0 == 0 then return end 
	if math.random() < 0.5 then 
		self:sort(enemies_0, "handcard")
	else
		self:sort(enemies_0, "defense")
	end 
	local equip_num = self.player:getEquips():length()
	local randomset = 0.55 + 0.15*(equip_num - 3)
	if (math.random() > randomset) and (self.player:getHandcardNum() > 1) then 
		for _, enemy in ipairs(enemies_0) do --抢价值装备
			local equips = enemy:getCards("e")
			if not equips:isEmpty() and not self:hasSkills(sgs.lose_equip_skill, enemy) then
				if self:getValuableCard(enemy) then
					local usecard = Qx_max_to
					if not usecard then usecard = Qx_zhongzhang_to end 
					if (not usecard) and (shouldUseWine or shouldUsePeach) then usecard = Qx_min_to end 
					if usecard then 
						use.card = sgs.Card_Parse("#Luaxinwu:" .. usecard:getEffectiveId()..":")
						if use.to then 
							use.to:append(enemy) 
							self.room:writeToConsole("秦心抢价值装备")
							use.to:append(self.player)  
						end		
						return
					end 
				end 
			end 				
		end 
		
		for _, enemy in ipairs(enemies_0) do
			local equips = enemy:getCards("e")
			if not equips:isEmpty() and not self:hasSkills(sgs.lose_equip_skill, enemy) then
				for _, equip in sgs.qlist(equips) do
					if (not self:getSameEquip(equip)) and not equip:isKindOf("GaleShell") then
						local usecard = Qx_max_to
						if not usecard then usecard = Qx_zhongzhang_to end 
						if (not usecard) and (shouldUseWine or shouldUsePeach) then usecard = Qx_min_to end 
						if usecard then 
							use.card = sgs.Card_Parse("#Luaxinwu:" .. usecard:getEffectiveId()..":")
							if use.to then 
								use.to:append(enemy) 
								self.room:writeToConsole("秦心抢没有装备")
								use.to:append(self.player)  
							end		
							return
						end 
						
					end 
				end 
			end 				
		end 

		if shouldUseWine or shouldUsePeach then 
			local usecard = Qx_zhongzhang_to
			if not usecard then usecard = Qx_min_to end 
			if usecard then 
				use.card = sgs.Card_Parse("#Luaxinwu:" .. usecard:getEffectiveId()..":")
				if use.to then 
					use.to:append(enemies_0[1]) 
					self.room:writeToConsole("秦心酒杀或恰桃骚扰")
					use.to:append(self.player)  
				end		
				return
			end 
		end 
	end 
	
	local fin_cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(fin_cards)
	if #enemies_0 > 1 then 
		use.card = sgs.Card_Parse("#Luaxinwu:" .. fin_cards[1]:getEffectiveId()..":")
		if use.to then 
			use.to:append(enemies_0[1]) 
			self.room:writeToConsole("秦心杀两个")
			use.to:append(enemies_0[2])  
		end			
		return
	elseif (self.player:getHandcardNum() > 1) then 
		use.card = sgs.Card_Parse("#Luaxinwu:" .. fin_cards[1]:getEffectiveId()..":")
		if use.to then 
			use.to:append(enemies_0[1]) 
			self.room:writeToConsole("秦心随便玩玩")
			use.to:append(self.player)  
		end				
		return
	end 
	
end 
-- 假设秦心打算对自己用xx基本牌，那么那张就是废牌
sgs.ai_use_priority["Luaxinwu"] = 11 --卡牌使用优先级
sgs.ai_use_value["Luaxinwu"] = 11

function sgs.ai_skill_pindian.Luaxinwu(minusecard, self, requestor, maxcard, mincard)
	if self.player:objectName() == requestor:objectName() then
		self.room:writeToConsole("秦心拼点测试")
		for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
			if p:hasFlag("Luaxinwu_Target") then
				req = p
				break
			end
		end
		if not req then self.room:writeToConsole("UUZ出严重bug") end 
		if req and self:isFriend(req) then 
			if self.player:isWounded() then 
				local qinxinPara_card = getMaxCard(self, true, false)
				return qinxinPara_card
			end 
			if req:isWounded() and (not (req:hasSkill("luaminghe")) or req:getLostHp() > 1) then 
				local qinxinPara_card = getMaxCard(self, true, true)
				return qinxinPara_card
			end 
		else
			local cards = self.player:getCards("h")
			cards = sgs.QList2Table(cards)
			self:sortByUseValue(cards, true)
			local shouldUseWine = false

			for _,card in ipairs(cards) do				
				if card:isKindOf("Slash") then
					local dummy_use = {isDummy = true}
					self:useBasicCard(card, dummy_use)
					if dummy_use.card then 
						shouldUseWine = true 
						self.room:writeToConsole("秦心需要使用一张酒")
						break
					end
				end 
			end 	

			if (self.player:isWounded()) or shouldUseWine then 	
				return getMaxCard(self, true, true)
			end 
			return getMaxCard(self, false, false)
		end 
	end 
end 

sgs.ai_skill_choice.LuaxinwuX = function(self, choices, data)
	local slash = sgs.Sanguosha:cloneCard("slash")
	local pindian = data:toPindian()
	local winner
	local loser
	local loser2
	local fromNumber = pindian.from_number
	local toNumber = pindian.to_number
	if fromNumber > toNumber then
		winner = pindian.from
		loser = pindian.to
	elseif fromNumber == toNumber then 
		loser = pindian.to
		loser2 = pindian.from
	elseif fromNumber <= toNumber then
		loser = pindian.from
		winner = pindian.to					
	end
	if not winner then return "Luaxinwu2" end 
	if (winner and winner:objectName() == self.player:objectName())
		and self:isFriend(loser) then 
		return "Luaxinwu2"
	elseif (winner and winner:objectName() == self.player:objectName())
		and self:isEnemy(loser) then 
		if self.player:canSlash(loser, nil, false) and self:slashIsEffective(slash, loser, self.player)
			and self:isWeak(loser) and not self:slashProhibit(slash, loser) then 
			return "Luaxinwu2"
		end 
		if not loser:isNude() then 
			return "Luaxinwu1"
		end 
		return "Luaxinwu2"
	end 
	return "Luaxinwu2"
end 

sgs.ai_skill_choice.Luaxinwu = function(self, choices, data)
	local target = data:toPlayer()
	if self:isFriend(target) then 
		if self.player:objectName() == target:objectName() then 
			if self.player:isWounded() then return "peach" end 
			return "analeptic"
		end 
		return "peach"
	else
		return "slash"
	end 
end 

sgs.ai_skill_playerchosen.LuaxinwuTarget = function(self, targets)
	if targets:contains(self.player) then
		return self.player
	end 
	return targets[1]
end 

sgs.ai_skill_playerchosen.Luanengmiani = function(self, targets)
	local from
	local to
	local fromNumber
	local toNumber
	local enumber = self.player:getEquips():length()
	for _, p in sgs.qlist(self.room:getAllPlayers()) do
		if p:hasFlag("nengmian_Target1") then
			from = p
			fromNumber = from:getMark("nengmian")
			break
		end
	end	
	for _, p in sgs.qlist(self.room:getAllPlayers()) do
		if p:hasFlag("nengmian_Target2") then
			to = p
			toNumber = to:getMark("nengmian")
			break
		end
	end		
	if fromNumber == 0 then return end 
	if toNumber == 0 then return end 
	if toNumber == fromNumber then return end
	self.room:writeToConsole("from " .. from:getGeneralName() .. " fromNumber " .. fromNumber)
	self.room:writeToConsole("to " .. to:getGeneralName() .. " toNumber " .. toNumber)
	if (toNumber >= fromNumber - enumber) and (toNumber <= fromNumber + enumber) then return from end
	if (fromNumber >= toNumber - enumber) and (fromNumber <= toNumber + enumber)  then return to end --这他妈不是一个意思吗2019年7月11日20:10:06
	return
end

sgs.ai_skill_cardchosen["Luaxinwu2"] = function(self, who, flags)
	if who:getArmor() and not self.player:getArmor() and not self:needToThrowArmor(who) then return who:getArmor() end
	if who:getWeapon() and not self.player:getWeapon() then return who:getWeapon() end
	if who:getDefensiveHorse() and not self.player:getDefensiveHorse() then return who:getDefensiveHorse() end
	if who:getOffensiveHorse() and not self.player:getOffensiveHorse() then return who:getOffensiveHorse() end
	return
end

sgs.ai_skill_choice.Luanengmian = function(self, choices, data)
	local from
	local to
	local fromNumber
	local toNumber
	local enumber = self.player:getEquips():length()
	for _, p in sgs.qlist(self.room:getAllPlayers()) do
		if p:hasFlag("nengmian_Target3") then
			from = p
			fromNumber = from:getMark("nengmian")
			break
		end
	end	
	for _, p in sgs.qlist(self.room:getAllPlayers()) do
		if p:hasFlag("nengmian_Target4") then
			to = p
			toNumber = to:getMark("nengmian")
			break
		end
	end		
	choices = choices:split("+")
	for _,name in ipairs(choices) do
		if fromNumber + tonumber(name) == toNumber then self.room:writeToConsole("秦心拼点结果为" ..name);return name end 
	end
	if not self:isFriend(from) then
		for _,name in ipairs(choices) do
			if (fromNumber + tonumber(name) <= 13) and (fromNumber + tonumber(name) >= 1) then
				return name
			end
		end
	else
		for _,name in ipairs(choices) do
			if (fromNumber + tonumber(name) <= 13) and (tonumber(name) >= 1) then
				return name
			end
		end
	end

end 

local Luazonghuo_skill={} -- 初始化 Luazonghuo_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 Luazonghuo_skill 只是为了出于习惯
Luazonghuo_skill.name="Luazonghuo" -- 设置 name
table.insert(sgs.ai_skills, Luazonghuo_skill) -- 把这个表加入到 sgs.ai_skills 中

Luazonghuo_skill.getTurnUseCard = function(self)
	--self.room:writeToConsole("布嘟嘟测试")
	if #self.enemies > 0 and not self.player:hasUsed("#Luazonghuo") then 
		return sgs.Card_Parse("#Luazonghuo:.:")
	end 
end 
sgs.ai_skill_use_func["#Luazonghuo"] = function(card, use, self)	
	local enermy = self.enemies
	self:sort(enermy, "defense")	
	local slash = sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_NoSuit, 0)
	for _, to in ipairs(enermy) do
		if ((to:isChained() and self:isGoodChainTarget(to, nil, nil, 1)) or self:hasHeavySlashDamage(self.player, slash, to)) and not to:isNude()
				and self:damageIsEffective(self.player, sgs.DamageStruct_Fire) then
			use.card = sgs.Card_Parse("#Luazonghuo:.:") 
			if use.to then use.to:append(to) end
			return
		end 	
	end 
	for _, to in ipairs(enermy) do
		if not to:isNude() and self:damageIsEffective(self.player, sgs.DamageStruct_Fire)
			and not to:hasSkill("Luayuyi") then
			use.card = sgs.Card_Parse("#Luazonghuo:.:") 
			if use.to then use.to:append(to) end
			return
		end 
	end 
	for _, to in ipairs(enermy) do
		if not to:isNude() then 
			use.card = sgs.Card_Parse("#Luazonghuo:.:") 
			if use.to then use.to:append(to) end
			return
		end 
	end 
end 

sgs.ai_skill_cardchosen["Luazonghuo"] = function(self, who, flags)
	local cards = sgs.QList2Table(who:getEquips())
	local handcards = sgs.QList2Table(who:getHandcards())
	if #handcards == 1 then --火葬场来电话问你妈要几分熟
		local carde = handcards[1]
		local acard = sgs.Sanguosha:cloneCard("fire_slash", carde:getSuit(), carde:getNumber())
		if self:AtomDamageCount2(who, self.player, nil, carde) >= who:getHp()
			or self:AtomDamageCount2(who, self.player, nil, carde) >= 2 then 
			return carde
		end 
	end 
end 
sgs.ai_card_intention.Luazonghuo = 60
sgs.ai_use_priority.Luazonghuo = sgs.ai_use_priority.Slash + 0.5

sgs.ai_damage_effect["LuaFengshui"] = function(self, to, nature, from)
	--self.room:writeToConsole("布嘟嘟测试")
	local bool0 = ((nature ~= sgs.DamageStruct_Thunder) and (nature ~= sgs.DamageStruct_Fire))
	local bool1 = ((from:getHp() == to:getHp()) and bool0)
	local bool2 = ((from:getHp() ~= to:getHp()) and not bool0)		
	return bool1 or bool2
end 

local Luameiyin_skill={}
Luameiyin_skill.name="Luameiyin"
table.insert(sgs.ai_skills,Luameiyin_skill)
Luameiyin_skill.getTurnUseCard = function(self)
	local cards = self.player:getCards("h")
	cards = sgs.QList2Table(cards)
	local count = 0
	local jink_card

	self:sortByUseValue(cards,true)

	for _,card in ipairs(cards)  do
		if card:isKindOf("Jink") or card:isKindOf("Nullification") then
			jink_card = card
			break
		end
	end
	
	for _,card in ipairs(cards)  do
		if card:isKindOf("Jink") or card:isKindOf("Nullification") or card:isKindOf("Slash") then
			count = count + 1
		end
	end
	
	if not jink_card then return nil end
	if (count == 1) and self.player:hasSkill("Luahengong") and (not self.player:hasSkill("Luamengyan") or (self.player:getHandcardNum() == 1) or (self.player:getHandcardNum() == 2 and (math.random() > 0.6))) then 
		local enemies_0 = self.enemies
		local slashes = {}
		slashes[1] = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		slashes[2] = sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_NoSuit, 0)
		slashes[3] = sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_NoSuit, 0)
		local slashes_0 = sgs.Sanguosha:cloneCard("slash", jink_card:getSuit(), jink_card:getNumber())
		local bool_0 = false
		local bool_1 = false
		for _, ap in ipairs(enemies_0) do
			local no_distance = sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_DistanceLimit, self.player, slashes_0) > 50 
				or ap:hasFlag("slashNoDistanceLimit")	
			if (self:hasHeavySlashDamage(self.player, slashes_0, ap) or (ap:getHp() <= 1 and ap:getHandcardNum() <= 2))
				and self.player:canSlash(ap, slashes_0, not no_distance) and not self:slashProhibit(slashes_0, ap)
				and self:slashIsEffective(slashes_0, ap) then
				bool_1 = true
			end 
		end 
		for _, ap in ipairs(enemies_0) do
			if ap:hasSkills("huoji|lianhuan|shuangxiong|qixi|zhanjue|luanji|Luazonghuo") then 
				bool_0 = true 
				break
			end 
			for _, slash in ipairs(slashes) do
				local no_distance = sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_DistanceLimit, ap, slash) > 50 
					or ap:hasFlag("slashNoDistanceLimit")
				if ap:canSlash(self.player, slash, not no_distance) 
					and not self:slashProhibit(slash, self.player, ap) and self:slashIsEffective(slash, self.player, ap, false) then
					bool_0 = true
					break
				end 
			end 
		end
		if bool_0 and not bool_1 then return nil end 
		if not bool_1 and (math.random() > 0.15) then return nil end 
	end 
	local suit = jink_card:getSuitString()
	local number = jink_card:getNumberString()
	local card_id = jink_card:getEffectiveId()
	local card_str = ("slash:Luameiyin[%s:%s]=%d"):format(suit, number, card_id) --return ("nullification:kanpo[%s:%s]=%d"):format(suit, number, card_id)
	local slash = sgs.Card_Parse(card_str)
	assert(slash)

	return slash

end

sgs.ai_skill_invoke.luayaoyun = function(self, data)
	local use = data:toCardUse()
	local top = use.to:at(0)
	if self:isEnemy(top) then return true end
	if top:objectName() == self.player:objectName() then
		if not self:slashIsAvailable(self.player) then return false end
		if self:getOverflow() < 1 then
			local table_X = self:getTurnUse(true)
			for _, card in ipairs(table_X) do
				if card:isKindOf("Snatch") or card:isKindOf("Banquet") or card:isKindOf("Indulgence") or card:isKindOf("EquipCard") or card:isKindOf("SupplyShortage") or card:isKindOf("Duel")
						or (card:isKindOf("AOE") and self:getAoeValue(card) > 40) or card:isKindOf("Peach") or card:isKindOf("Ofuda") or card:isKindOf("ExNihilo") then
					return false
				end
			end
		end
	end
end


sgs.ai_skill_invoke.Luaganran = function(self, data)
	local damage = data:toDamage()
	local target = damage.to
	if self:isFriend(target) then return false end
	return (1 == 1) --哈哈哈
end 

local Luabaige_skill = {}
Luabaige_skill.name = "Luabaige"
table.insert(sgs.ai_skills, Luabaige_skill)
Luabaige_skill.getTurnUseCard = function(self)
	if (self.room:getTag("Luabaige") and self.room:getTag("Luabaige"):toInt() < 3) then return end 
	local cards = sgs.QList2Table(self.player:getHandcards())
	for _, card in ipairs(cards) do
		if card:isKindOf("Slash") then return false end 
	end 
	if not self.player:hasFlag("Luabaige_used") and not self.player:hasFlag("hasUsedSlash") and not self.player:isKongcheng() then
		local card_str = ("slash:Luabaige[no_suit:0]=.")
		local slash = sgs.Card_Parse(card_str)
		self.room:setPlayerFlag(self.player,"Luabaige_used")
		assert(slash)
		return slash
	end
end
sgs.ai_cardsview_valuable.Luabaige = function(self, class_name, player) 
    if self.player:hasFlag("Luabaige_used") or self.player:isKongcheng() then return end
	local cards = sgs.QList2Table(self.player:getHandcards())
	for _, card in ipairs(cards) do
		if card:isKindOf("Slash") then return false end 
	end 
	--if self:getCardsNum("Slash") > 0  then return end 不可用
	if not self.room:getTag("Luabaige") then self.room:setTag("Luabaige", sgs.QVariant(0)) end 
	if self.room:getTag("Luabaige") and self.room:getTag("Luabaige"):toInt() > 4 then return end 
    if class_name == "Slash" then		
		local x = self.room:getTag("Luabaige"):toInt()
		self.room:setTag("Luabaige", sgs.QVariant(x+1))
        local card_str = ("slash:Luabaige[no_suit:0]=.")
        return card_str
    end
end
sgs.ai_use_priority.Luabaige = sgs.ai_use_priority.Slash


sgs.ai_skill_playerchosen.LuaFengshen = function(self, targets)
	targets = sgs.QList2Table(targets)
	local shikieiki = self.room:findPlayerBySkillName("luapanjue")
	if shikieiki and shikieiki:isAlive() and self:isEnemy(shikieiki) then return nil end
	for _, target in ipairs(targets) do
		if self:isFriend(target) and target:isAlive() then
			return target
		end
	end
	return nil
end

sgs.ai_playerchosen_intention.LuaFengshen = -40

sgs.ai_card_intention.LuaShuifu = 30   --local cardX = self.room:getTag("LuaxianzheTC"):toCard()


function abcdefg(player) --肯定有杀
	local saki = (player:hasSkill("luajuezhan") or player:hasTreasure("wanbaochui") or player:getHandcardNum() > 2)
	if (player:hasSkills("LuaLeishi|wusheng|Luameiyin|LuaYuanzu") and not player:isKongcheng())
		or (player:hasSkill("nosgongqi") and player:getEquips() and player:getEquips():length() > 0)
		or (getCardsNum("Slash", player, player) > 1)
		or (player:hasSkill("luachongfeng") and saki)
		or (player:hasSkill("LuaJiaosi") and player:getMark("LuaJiaosiZ") < player:getPile("qizhi2"):length()) then
		return true 
	end 
	return false 
end 

local luawangshi_skill={} -- 初始化 luawangshi_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 luawangshi_skill 只是为了出于习惯
luawangshi_skill.name="luawangshi" -- 设置 name
table.insert(sgs.ai_skills, luawangshi_skill) -- 把这个表加入到 sgs.ai_skills 中

luawangshi_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luawangshi") then return end 
	if #self.enemies == 1 then 
		local enemy = self.enemies[1]
		if not abcdefg(enemy) then 
			if (sgs.card_lack[enemy:objectName()]["Slash"] == 1) or enemy:isKongcheng() then return sgs.Card_Parse("#luawangshi:.:") end 
			local hh = enemy:getHandcardNum()/3 - 0.09
			if self:isWeak(enemy) then hh = hh + 0.2 end  
			if (math.random() < hh) then return sgs.Card_Parse("#luawangshi:.:") end 
		end 
	end 
	if (not self.player:hasUsed("#luawangshi")) and #self.enemies ~= 1 then 
		return sgs.Card_Parse("#luawangshi:.:")
	end 
end 

sgs.ai_skill_use_func["#luawangshi"] = function(card, use, self)
	local enemies = self.enemies
	local friends = self.friends_noself
	self:sort(friends, "handcard", true)
	
	if #friends > 0 and #self.enemies > 0 then
		for _, to in ipairs(friends) do
			if abcdefg(to) then 
				use.card = sgs.Card_Parse("#luawangshi:.:") 
				if use.to then use.to:append(to) end
				return
			end 
		end 	
		for _, to in ipairs(friends) do
			if to:hasFlag("mikomiko") and not to:isKongcheng() then 
				use.card = sgs.Card_Parse("#luawangshi:.:") 
				if use.to then use.to:append(to) end
				return
			end 
		end 
	end 
	if #enemies > 0 then 
		self:sort(enemies, "defenseSlash")
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)		
		for _, to in ipairs(enemies) do
			if sgs.card_lack[to:objectName()]["Slash"] == 1 and not abcdefg(to) then 
				use.card = sgs.Card_Parse("#luawangshi:.:") 
				if use.to then use.to:append(to) end
				return
			end 
		end 	
		for _, to in ipairs(enemies) do
			if self:hasHeavySlashDamage(self.player, slash, to) and not abcdefg(to) then 
				use.card = sgs.Card_Parse("#luawangshi:.:") 
				if use.to then use.to:append(to) end
				return
			end 
		end 

		for _, to in ipairs(enemies) do
			if self:slashIsEffective(slash, to, self.player) and not abcdefg(to) then 
				use.card = sgs.Card_Parse("#luawangshi:.:") 
				if use.to then use.to:append(to) end
				return
			end 
		end 
		
		for _, to in ipairs(enemies) do
			use.card = sgs.Card_Parse("#luawangshi:.:") 
			if use.to then use.to:append(to) end
			return
		end 			
	end 
	if #friends > 0 and #self.enemies > 0 then
		for _, to in ipairs(friends) do
			local hh = to:getHandcardNum()/3 - 0.09
			if (not self:isWeak(to)) and (math.random() < hh) and sgs.card_lack[to:objectName()]["Slash"] ~= 1 then 
				use.card = sgs.Card_Parse("#luawangshi:.:") 
				if use.to then use.to:append(to) end
				return				
			end 
		end 
	end 
	local players = sgs.QList2Table(self.room:getAllPlayers())
	self:sort(players, "defense")
	for _, to in ipairs(players) do
		use.card = sgs.Card_Parse("#luawangshi:.:") 
		if use.to then use.to:append(to) end
		return
	end 	
end 

sgs.ai_skill_cardask["luawangshiz"] = function(self)
	local targetP = self.room:getTag("Jiantingsource"):toPlayer()
	local targetT = self.room:getTag("Jiantingsource3"):toPlayer()
	local slashid = self:getCardId("Slash", self.player, nil , true)
	if not slashid then return "." end 
	
	if not self:isFriend(targetT) and self.player:hasSkill("Luachongsheng") then return "." end 
	
	if not self:isFriend(targetT) then return slashid or "." end
	if self:isFriend(targetP) then return slashid or "." end
	if self:getCardsNum("Jink") ~= 0 then return "." end  
	
	if self.player:hasSkills(sgs.need_slash_skill) then return "." end 
	return slashid or "."

end

local LuaJianting_skill = {}
LuaJianting_skill.name = "LuaJiantingAsk"
table.insert(sgs.ai_skills,LuaJianting_skill)
LuaJianting_skill.getTurnUseCard = function(self)
	if not self.player:hasUsed("#LuaJianting") then 
		
		local miko = self.room:findPlayerBySkillName("LuaJianting")
		if not miko then return end
		if not self:isFriend(miko) then return end
		if not miko:isLord() then return end
		if miko:objectName() == self.player:objectName() then return end 
		local handcard = sgs.QList2Table(self.player:getHandcards())
		local y = 0 
		for _,p in sgs.qlist(self.room:getAlivePlayers()) do
			if p:getRole() == "loyalist" and p:isAlive() then y = y + 1 end 
		end
		if (y == 0) or (#handcard == 0) then return end 
		return sgs.Card_Parse("#LuaJianting:.:")
	end
end 


sgs.ai_skill_use_func["#LuaJianting"] = function(card, use, self)
	local handcard = sgs.QList2Table(self.player:getHandcards())
	local y = 0 
	for _,p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:getRole() == "loyalist" and p:isAlive() then y = y + 1 end 
	end 	
	local togive = {}
	local i = 1
	while #togive < y and (#handcard >= i) do
		self:sortByKeepValue(handcard)
		table.insert(togive, handcard[i]:getId())
		i = i + 1
	end 	
	if #togive > 0 then 
		use.card = sgs.Card_Parse("#LuaJianting:" .. table.concat(togive, "+") ..":")
		for _, friend in ipairs(self.friends_noself) do
			if friend:hasLordSkill("LuaJianting") then 
				
				if use.to then
					use.to:append(friend) 
					return
				end 
			end 
		end 
	end 
end 
sgs.ai_skill_playerchosen.luawangshi = function(self, targets)  --没有处理务必闪的情况
	local enemies = self.enemies	
	local friends = self.friends
	self.room:writeToConsole("luawangshi testy")
	if #enemies > 0 then 
		self:sort(enemies, "defense")
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)	
		for _, to in ipairs(enemies) do
			if self:hasHeavySlashDamage(self.player, slash, to) then 
				return to
			end 			
		end
		for _, to in ipairs(enemies) do
			if targets:contains(to) then
				return to
			end
		end
	end 

	for _, aplayer in sgs.qlist(self.room:getAlivePlayers()) do
		if (math.random() < 0.4) and aplayer:objectName() ~= self.player:objectName() then return aplayer end
	end
	if #self.friends_noself > 0 then
		self:sort(self.friends_noself, "defense", true)
		return self.friends_noself[1]
	end
	self:sort(friends, "defense", true)
	return friends[1]
end 
sgs.ai_skill_discard.LuaJianting = function(self, discard_num, min_num, optional, include_equip)
	local togive = {}
	local handcard = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(handcard)
	local target = self.room:getTag("LuaJiantingsource"):toPlayer()
	if target:isWounded() then 
		for _, card in ipairs(handcard) do
			if self:OverFlowPeach(card) then 
				table.insert(togive, card:getEffectiveId())
			end 
			if (#togive >= min_num) or (#togive >= #handcard) then return togive end 
		end 
	end 
	if target:isKongcheng() and self:getCardsNum("Jink") > 1 and self:needtoKeepAJink(target, true) then 
		for _, card in ipairs(handcard) do
			if card:isKindOf("Jink") then 
				table.insert(togive, card:getEffectiveId())
			end 			
			if (#togive >= min_num) or (#togive >= #handcard) then return togive end 
		end 
	end 
	if (target:getHp() == 1) and ((self:getCardsNum("Analeptic") > 1) or ((self:getCardsNum("Analeptic") == 1) and self.player:getHp() > 1)) then 
		for _, card in ipairs(handcard) do
			if card:isKindOf("Analeptic") then 
				table.insert(togive, card:getEffectiveId())
			end 			
			if (#togive >= min_num) or (#togive >= #handcard) then return togive end 
		end 		
	end 
	local slash_0 = sgs.Sanguosha:cloneCard("jink")
	local slash_v = self:getKeepValue(slash_0, true, true) - 1.4
	for _, card in ipairs(handcard) do
		if card:isKindOf("Slash") and self:getKeepValue(card) < slash_v then 			
			target:getRoom():setPlayerFlag(target, "mikomiko")
			sgs.card_lack[target:objectName()]["Slash"] = 0	
			table.insert(togive, card:getEffectiveId())
		end 			
		if (#togive >= min_num) or (#togive >= #handcard) then return togive end 		
	end 	
	for _, card in ipairs(handcard) do
		for _, skill in sgs.qlist(target:getVisibleSkillList(true)) do
			local callback = sgs.ai_cardneed[skill:objectName()]
			if type(callback) == "function" and callback(self.player, card, self) then
				table.insert(togive, card:getEffectiveId())
				if (#togive >= min_num) or (#togive >= #handcard) then return togive end 
			end
		end
	end
	if #togive < min_num then 
		for _, card in ipairs(handcard) do
			local gived = false
			for _, cardid in ipairs(togive) do
				if card:getEffectiveId() == cardid then gived = true end 
			end 
			if not gived then 
				if card:isKindOf("Slash") then 
					target:getRoom():setPlayerFlag(target, "mikomiko")
					sgs.card_lack[target:objectName()]["Slash"] = 0			
				end 
				table.insert(togive, card:getEffectiveId())
			end 
			if (#togive >= min_num) or (#togive >= #handcard) then return togive end 	
		end 
	end 
	
end 

sgs.ai_skill_use["@luatongling"] = function(self, prompt)
	return sgs.ai_skill_use.slash(self, prompt)
end

sgs.ai_card_intention.luaxiexian = -10

sgs.ai_playerchosen_intention.luaxiexian = 20

local luatongling_skill = {}
luatongling_skill.name = "luatongling"
table.insert(sgs.ai_skills,luatongling_skill)
luatongling_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luatongling") then return end 
	return sgs.Card_Parse("#luatongling:.:")
end 
sgs.ai_skill_use_func["#luatongling"] = function(card, use, self) --:getAttackRange()
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill("luayuyi") then
			use.card = sgs.Card_Parse("#luatongling:.:")
			if use.to then use.to:append(friend) ; return end
		end
	end
	self:sort(self.enemies, "defense2")
	for _, enemy in ipairs(self.enemies) do
		local count = 0
		for _, aplayer in sgs.qlist(self.room:getAllPlayers()) do
			if enemy:inMyAttackRange(aplayer) and self:isEnemy(aplayer, enemy) then count = count + 1 end 
		end 
		if ((count == 0) or enemy:isKongcheng() or ((enemy:getHandcardNum() == 1) and (getCardsNum("Slash", enemy, self.player) == 0) and (math.random() > 0.85))) 
			and (self.player:getMark("@tongling") == #self.enemies - 1) then 
			use.card = sgs.Card_Parse("#luatongling:.:") 
			if use.to then use.to:append(enemy) ; return end			
		end 
	end
	for _, enemy in ipairs(self.enemies) do
		local count = 0
		for _, aplayer in sgs.qlist(self.room:getAllPlayers()) do	
			if enemy:inMyAttackRange(aplayer) and self:isEnemy(aplayer, enemy) then count = count + 1 end 
		end 
		local p = enemy:getHandcardNum()
		local q = 0
        local flag = string.format("%s_%s_%s", "visible", enemy:objectName(), self.player:objectName())
		for _, acard in sgs.qlist(enemy:getHandcards()) do
			if (acard:hasFlag("visible") or acard:hasFlag(flag)) and acard:isKindOf("Slash") then q = q + 1 end  	
		end 
		if ((count == 0) and (((enemy:getHandcardNum() == 3) and math.random() > 0.65) or (enemy:getHandcardNum() > 3) or q > 0)) then 
			use.card = sgs.Card_Parse("#luatongling:.:") 
			if use.to then use.to:append(enemy) ; return end			
		end 		
	end
	for _, friend in ipairs(self.friends) do
		local count = 0
		for _, aplayer in sgs.qlist(self.room:getAllPlayers()) do
			if friend:inMyAttackRange(aplayer) and self:isEnemy(aplayer, friend) then count = count + 1 end 
		end 
		
		if count > 0 and ((getCardsNum("Slash", friend, self.player) > 0) or (friend:getHandcardNum() >= 3))  
			and friend:hasSkills("Luashenpan|luashuangren|LuaBisha|LuaShanguang|Luashenqiang|bawang|kuangfu|wushuang|chuanxin|nosqianxi|nostieji|tieji|liegong|kofliegong|xiemu|qiangwu|huxiao|fengpo|conqueror|zhaxiang|lieren|badao|jianchu|luashaojie") then
			use.card = sgs.Card_Parse("#luatongling:.:") 
			if use.to then use.to:append(friend) ; return end				
		end 
	end
	for _, friend in ipairs(self.friends) do
		if friend:hasSkills("Luashenpan|luashuangren|LuaBisha|LuaShanguang|Luashenqiang|bawang|kuangfu|wushuang|chuanxin|nosqianxi|nostieji|tieji|liegong|kofliegong|xiemu|qiangwu|huxiao|fengpo|conqueror|zhaxiang|lieren|jianchu|luashaojie") then
			use.card = sgs.Card_Parse("#luatongling:.:") 
			if use.to then use.to:append(friend) ; return end				
		end 
	end
	for _, friend in ipairs(self.friends) do
		local count = 0
		for _, aplayer in sgs.qlist(self.room:getAllPlayers()) do
			if friend:inMyAttackRange(aplayer) and self:isEnemy(aplayer, friend) then count = count + 1 end 
		end 
		
		if count > 0 and ((getCardsNum("Slash", friend, self.player) > 0) or (friend:getHandcardNum() >= 3)) then 
			use.card = sgs.Card_Parse("#luatongling:.:") 
			if use.to then use.to:append(friend) ; return end				
		end 
	end 
	
	use.card = sgs.Card_Parse("#luatongling:.:") 
	if use.to then use.to:append(self.player) ; return end		
end 
sgs.ai_use_priority.luatongling = function(self)
	local duel = sgs.Sanguosha:cloneCard("duel")
	local x = self:getDynamicUsePriority(duel) - 0.15
	return x
end 

local luaxiexian_skill = {}
luaxiexian_skill.name = "luaxiexian"
table.insert(sgs.ai_skills,luaxiexian_skill)
luaxiexian_skill.getTurnUseCard = function(self)
	
	if self:getCardsNum("Slash") == 0 then return end 
	if #self.friends_noself == 0 then return end 
	if self.player:hasUsed("#luaxiexian") then return end 
	for _, acard in sgs.qlist(self.player:getHandcards()) do
		if acard:isKindOf("Slash") and self.player:isCardLimited(acard, sgs.Card_MethodUse) then return sgs.Card_Parse("#luaxiexian:.:") end 
	end 
	
	local cards = self:getTurnUse(true)
	if (self:hasCrossbowEffect() or self:getCardsNum("Crossbow") > 0) then 
		local count = 0
		for _, card in ipairs(cards) do
			if card:isKindOf("Slash") then count = count + 1 end 
		end 
		if count > 1 then return end 
	end 
	
	for _, acard in sgs.qlist(self.player:getHandcards()) do
		if acard:isKindOf("Analeptic") and sgs.Analeptic_IsAvailable(self.player, acard) then return end 
	end

	if self.player:getMark("drank") > 0 then return end
	
	local p = self.player:getHandcardNum() - #cards - self.player:getMaxCards()
	local card, frienda = self:getCardNeedPlayer(self:getCards("Slash"))
	if card and frienda and frienda:objectName() ~= self.player:objectName() then return sgs.Card_Parse("#luaxiexian:.:") end	
	if (p > 0) or self:getCardsNum("Slash") > 0 then return sgs.Card_Parse("#luaxiexian:.:") end 
	
end 
sgs.ai_skill_use_func["#luaxiexian"] = function(cardD, use, self)
	local cards = self:getCards("Slash")
	self:sortByUseValue(cards)
	
	local card, frienda = self:getCardNeedPlayer(cards)
	if card and frienda and frienda:objectName() ~= self.player:objectName() then 
		local toUse = {}
		for _, cardA in ipairs(cards) do
			table.insert(toUse, cardA:getId())
		end 
		use.card = sgs.Card_Parse("#luaxiexian:" .. table.concat(toUse, "+") ..":") 
		if use.to then use.to:append(frienda) ; return end	
	end		
	
	self:sort(self.friends_noself, "handcard")
	for _, friend in ipairs(self.friends_noself) do
		local toUse = {}
		for _, cardA in ipairs(cards) do
			table.insert(toUse, cardA:getId())
		end 
		use.card = sgs.Card_Parse("#luaxiexian:" .. table.concat(toUse, "+") ..":") 
		if use.to then use.to:append(friend) ; return end		
	end 
end 

sgs.ai_cardneed.luaxiexian = function(to, card)
	return isCard("Slash", card, to) --and not card:hasFlag("luashouhuc")
end

sgs.ai_use_priority.luaxiexian = 11

sgs.ai_skill_playerchosen.luatongling = function(self, targets)
	local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
	self:sort(self.enemies, "defenseSlash")
	for _, enemy in ipairs(self.enemies) do
		if targets:contains(enemy) and self:slashIsEffective(slash, enemy, self.player) then
			return enemy
		end		
	end 
	
	local players = sgs.QList2Table(self.room:getAllPlayers())
	self:sort(players, "defenseSlash")
	for _, p in ipairs(players) do
		if targets:contains(p) and not self:isFriend(p) then
			return p
		end		
	end 

	self:sort(players, "defenseSlash2")
	for _, p in ipairs(players) do
		if targets:contains(p) then
			return p
		end		
	end
end 



sgs.ai_view_as.LuaLeishi = function(card, player, card_place)
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	if card_place ~= sgs.Player_PlaceSpecial and not card:isKindOf("Peach") and not card:hasFlag("using") and not player:hasFlag("LuaLeishi_used")
		and card_place ~= sgs.Player_PlaceEquip then
		return ("thunder_slash:LuaLeishi[%s:%s]=%d"):format(suit, number, card_id)
	end
end

local function findleishicard(self)
	local cards = self.player:getCards("h")
	cards = sgs.QList2Table(cards)
	local red_card
	self:sortByUseValue(cards, true)
	local slashX = sgs.Sanguosha:cloneCard("thunder_slash")
	local slash_v = self:getUseValue(slashX)
	
	local useAll = false
	self:sort(self.enemies, "defense")
	for _, enemy in ipairs(self.enemies) do
		if enemy:getHp() == 1 and not enemy:hasArmorEffect("eight_diagram") and self.player:distanceTo(enemy) <= self.player:getAttackRange() and self:isWeak(enemy)
			and getCardsNum("Jink", enemy, self.player) + getCardsNum("Peach", enemy, self.player) + getCardsNum("Analeptic", enemy, self.player) == 0 then
			useAll = true
			break
		end
	end
	if not useAll then 
		local x = self.player:getMark("Luayuanling")
		local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
		if (not self.player:isKongcheng()) and self:YouMu() and (x > 1) and self.player:getMark("@yuanxing") > 0 then
			useAll = true
		end 
	end 
	
	local disCrossbow = false
	if self:getCardsNum("Slash") < 2 or self.player:hasSkill("paoxiao") then
		disCrossbow = true
	end

	if not red_card then 	
		if self:needToThrowArmor() then 
			red_card = self.player:getArmor()
		end 	
	end 
	
	if not red_card then 	
		for _, card in ipairs(cards) do
			if  (not isCard("Peach", card, self.player) and not isCard("ExNihilo", card, self.player) and not useAll)
				and (not isCard("Crossbow", card, self.player) and not disCrossbow)
				and (self:getUseValue(card) <= slash_v or inclusive or self.player:hasSkill("paoxiao") or self:getOverflow() > 0	--yun
					or sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_Residue, self.player, sgs.Sanguosha:cloneCard("thunder_slash")) > 0) then
				red_card = card
				break
			end
		end
	end 
	return red_card
end 
local LuaLeishi_skill = {}
LuaLeishi_skill.name = "LuaLeishi"
table.insert(sgs.ai_skills, LuaLeishi_skill)
LuaLeishi_skill.getTurnUseCard = function(self, inclusive)
	if self.player:hasFlag("LuaLeishi_used") then return end 
	local red_card = findleishicard(self)
	if red_card then
		return sgs.Card_Parse("#LuaLeishi:.:")
	end
end

sgs.ai_skill_use_func["#LuaLeishi"] = function(card, use, self)
	local red_card = findleishicard(self)
	if not red_card then return end 
	self.room:writeToConsole("tojiko test")
	local slash = sgs.Sanguosha:cloneCard("thunder_slash", red_card:getSuit(), red_card:getNumber())
	slash:setSkillName("LuaLeishi")
	slash:addSubcard(red_card:getEffectiveId())	
	local dummy_use = { isDummy = false , to = sgs.SPlayerList() }
	self:useBasicCard(slash, dummy_use)	
	if dummy_use.card and dummy_use.card:isKindOf("ThunderSlash") and dummy_use.to and dummy_use.to:length() > 0 then 
		self.room:writeToConsole("tojiko test 2")
		use.card = sgs.Card_Parse("#LuaLeishi:" .. red_card:getId() ..":") 
		if use.to then 
			use.to = dummy_use.to 
			return
		end 		
	end 
end 

sgs.ai_use_priority.LuaLeishi = 6
sgs.ai_cardneed.LuaLeishi = function(to, card)
	return (to:getHandcardNum() < 3) 
end

sgs.ai_skill_invoke.Luashenyi = function(self, data)
	return true 
end 
sgs.ai_skill_playerchosen.Luashenyi = function(self, targets)
	self.room:writeToConsole("toziko test")
	local slashX = sgs.Sanguosha:cloneCard("thunder_slash")
	local x = self.player:getMark("Luayuanling")
	if (not self.player:isKongcheng()) and self:YouMu() then
		return self.player
	end
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill("Lualianji") and not friend:faceUp() then
			return friend
		end
	end
	for _, friend in ipairs(self.friends) do
		if friend:hasSkills("Luazonghuo|luacaihuo|lualiuzhi") then
			return friend
		end
	end
	for _, friend in ipairs(self.friends) do
		if friend:hasSkills("luatongling|luawangshi|luaduhuo|luajieao|luatiaobo|luaxingyun|Luabaige|gongxin") then
			return friend
		end
	end
	for _, friend in ipairs(self.friends) do
		if friend:containsTrick("indulgence") and self:getOverflow(friend) > 0 and friend:getHandcardNum() > 2 then
			return friend
		end
	end
	if self.player:getHandcardNum() > 2 then
		return self.player
	end
	for _, friend in ipairs(self.friends) do
		if friend:getHandcardNum() > self.player:getHandcardNum() then return friend end
	end	
	return self.player
end 

sgs.ai_playerchosen_intention.Luashenyi = -10

sgs.ai_skill_invoke.Luayuanxing = function(self, data)
	local damage = data:toDamage()
	--if self.role == "rebel" and damage.to:isLord() then return true end
	local damagecount = damage.damage + 1

	if self:isGoodChainTarget(damage.to, damage.from, sgs.DamageStruct_Thunder, damagecount, damage.card, 2) then return true end
	if self:isWeak() then return true end 
	return false 
end 


sgs.ai_skill_use["@@Luayuanling"] = function(self, prompt)
	local ironC = sgs.Sanguosha:cloneCard("iron_chain")
	local dummy_use = { isDummy = true , to = sgs.SPlayerList() }
	self:useTrickCard(ironC, dummy_use)	
	local tos = {}
	if dummy_use.to and dummy_use.to:length() > 0 then 
		for _, p in sgs.qlist(dummy_use.to) do
			table.insert(tos, p:objectName())
		end 
	end 
	if #tos > 0 then 
		return "#Luayuanling:.:->" .. table.concat(tos, "+")
	end 
end 


sgs.ai_skill_use["@@LuaGuanwei"] = function(self, prompt)
	local card
	if not self.player:isNude() then 
		local cards = sgs.QList2Table(self.player:getCards("he"))
		self:sortByKeepValue(cards)
		if not cards[1]:isKindOf("Peach") and not (cards[1]:isKindOf("Analeptic") and self.player:getHp() == 1) then
			card = cards[1]
		end
	end 
	self:sort(self.friends, "defense")
	for _, p in ipairs(self.friends) do
		if self:Miko(p, 4) and card then return "#LuaGuanwei:" .. card:getEffectiveId() .. ":->" .. p:objectName() end
	end
	for _, p in ipairs(self.friends_noself) do
		if p:hasSkills("jieming|guixin|fangzhu|nosmiji|luayuetuan|chouce|huituo|ytchengxiang|luagongcheng|luafengmo|luayequ|Luahuaimeng") and p:getHp() > 1 then
			return "#LuaGuanwei:.:->" .. p:objectName() 
		end 
	end
	for _, p in ipairs(self.friends) do
		if self:Miko(p, 3) and card then return "#LuaGuanwei:" .. card:getEffectiveId() .. ":->" .. p:objectName() end
	end
	for _, p in ipairs(self.friends) do
		if p:hasSkill("luaxiaodian") and card then 
			local targets = {}
			for _, aplayer in ipairs(self.friends) do
				if aplayer:getHp() == 1 then
					table.insert(targets, aplayer)
				else
					local bool_2 = true	
					for _, p2 in sgs.qlist(self.room:getAlivePlayers()) do
						if (aplayer:getHandcardNum() < p2:getHandcardNum()) or (not aplayer:isWounded()) then
							bool_2 = false 
						end 
					end 	
					if bool_2 then table.insert(targets, aplayer) end 
				end 
			end 
			if #targets > 0 then 
				return "#LuaGuanwei:" .. card:getEffectiveId() .. ":->" .. p:objectName()
			end 
		end 
	end
	for _, p in ipairs(self.friends) do
		if self:Miko(p, 2) and card then return "#LuaGuanwei:" .. card:getEffectiveId() .. ":->" .. p:objectName() end
	end

	local white = self.room:findPlayerBySkillName("Luajingzhe")
	local hasWhite = white and white:isAlive() and self.room:getCurrent():objectName() == white:objectName()
	for _,enemy in ipairs(self.enemies) do
		if hasWhite and self:isFriend(white) and self:damageIsEffective(self.player, sgs.DamageStruct_Normal, enemy) then
			return "#LuaGuanwei:.:->" .. enemy:objectName()
		end
	end
	for _,enemy in ipairs(self.enemies) do
		if enemy:getHp() == 1 and not self:HasGou(true, enemy) and self:damageIsEffective(self.player, sgs.DamageStruct_Normal, enemy) then
			return "#LuaGuanwei:.:->" .. enemy:objectName()
		end
	end
	for _, p in ipairs(self.friends) do
		if p:hasSkill("luachongfeng") and (p:hasSkill("luajuezhan") or p:hasTreasure("wanbaochui") or p:getHandcardNum() > 2)
			then return "#LuaGuanwei:.:->" .. p:objectName() end
	end
	for _, p in ipairs(self.friends) do
		if self:Miko(p, 1) and card then return "#LuaGuanwei:" .. card:getEffectiveId() .. ":->" .. p:objectName() end
	end
	if card and not (self.player:getTag("LuaGuanwei") and self.player:getTag("LuaGuanwei"):toString() ~= "")
		then return "#LuaGuanwei:" .. card:getEffectiveId() .. ":->" .. self.player:objectName() end

	if #self.friends_noself > 0 then
		for _, p in ipairs(self.friends_noself) do
			if p:getHp() > 1 and hasWhite and self:isEnemy(white) then
				return "#LuaGuanwei:.:->" .. p:objectName()
			end
		end
	end
	for _,p in ipairs(self.enemies) do
		if p:getHp() == 1 then
			return "#LuaGuanwei:.:->" .. p:objectName()
		end
	end
end 

sgs.ai_skill_choice.LuaGuanwei = function(self, choices)
	local target = self.room:getTag("LuaGuanwei"):toPlayer()
	if target and target:objectName() == self.player:objectName() and self.player:getHandcardNum() < 2 then return "Player_Draw" end
	if target and self:Miko(target) then return self:Miko(target) end
	return "Player_Draw" 
end 



function Weiguangcard(self)
	local toUse = {}
	local cards = self:getTurnUse(true)
	for _, card in ipairs(cards) do
		if self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand and card:isKindOf("EquipCard") then 
			if self:getSameEquip(card, self.player) then 
				table.insert(toUse, self:getSameEquip(card, self.player))
			end 
		end 
	end 
	local fcaerds = sgs.QList2Table(self.player:getCards("he"))
	local jink = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
	local jink_v = self:getKeepValue(jink, true, true) - 0.5
	for _, card in ipairs(fcaerds) do
		if (not table.contains(cards, card)) and self:getKeepValue(card) <= jink_v and not table.contains(toUse, card) then 
			table.insert(toUse, card)
		end 
	end 
	
	local jinks = self:getCardsNum("Jink")
	local jcount = 0
	for _, card in ipairs(fcaerds) do
		if (jcount == jinks - 1) or (jinks == 0) then break end 
		if card:isKindOf("Jink") and not table.contains(toUse, card) then 
			table.insert(toUse, card)
			jcount = jcount + 1
		end
	end 
	
	local peachs = self:getCardsNum("Peach")
	for _, card in ipairs(fcaerds) do
		if card:isKindOf("Peach") and not table.contains(toUse, card) and self:OverFlowPeach(card) then 
			table.insert(toUse, card)
		end
	end 	

	return toUse
end 

local Luaweiguang_skill = {}
Luaweiguang_skill.name = "Luaweiguang"
table.insert(sgs.ai_skills, Luaweiguang_skill)
Luaweiguang_skill.getTurnUseCard = function(self, inclusive)
	if (self.player:hasUsed("quanxiang")) then return end
	if self.player:getHandcardNum() < 3 then return end
	local red_card = Weiguangcard(self)
	
    local compare_func = function(a, b)
        return a:getNumber() < b:getNumber()
    end
    table.sort(red_card, compare_func)
	
	if #red_card > 1 then
		self.room:writeToConsole("威光测试")
		local card_id1 = red_card[1]:getEffectiveId()
		local card_id2 
		for _, card in ipairs(red_card) do
			if card:getId() ~= card_id1 then 
				card_id2 = card:getEffectiveId()
			end 
		end 
		if card_id1 and card_id2 then 
			self.room:writeToConsole("威光测试F")
			local card_str = ("quanxiang:%s[%s:%s]=%d+%d"):format("quanxiang", "to_be_decided", 0, card_id1, card_id2)
			local slash = sgs.Card_Parse(card_str)
			if math.random() < 0.009 then self.player:speak("叫爸爸我就给你翻回来") end 
			return slash
		end 
	end
end

sgs.ai_skill_invoke.Luafadeng = function(self, data)
	local target = self.room:getCurrent()
	return self:isFriend(target)
end 

sgs.ai_skill_playerchosen.luajingjuan = function(self, targets)
	local callback = sgs.ai_skill_playerchosen.slash_extra_targets 
	return callback(self, targets)
end 

sgs.ai_skill_invoke.Luatanbao = function(self, data)
	return true
end 

local luashanji_skill = {}
luashanji_skill.name = "luashanji"
table.insert(sgs.ai_skills, luashanji_skill)
luashanji_skill.getTurnUseCard = function(self, inclusive)
	if #self.enemies == 0 then 
		local bool 
		for _, friend in ipairs(self.friends) do
			if not friend:faceUp() then bool = true end 
		end 
		if not bool then return end 
	end 
	if self.player:getHandcardNum() == 2 then 
		if #self.enemies > 1 then return end 
		if self.enemies[1]:getHp() == 1 and (not self:HasGou(true, self.enemies[1])) then 
			return sgs.Card_Parse("#luashanji:.:")
		end 
	end 
	local jinks = {}
	local Acards = sgs.QList2Table(self.player:getCards("he"))
	for _, card in ipairs(Acards) do
		if card:isKindOf("Jink") then table.insert(jinks, card) end 
	end 	
	if #jinks == 3 then return sgs.Card_Parse("#luashanji:.:") end 
	if #jinks == 2 and (math.random() > 0.6) then return sgs.Card_Parse("#luashanji:.:") end 
end 

sgs.ai_skill_use_func["#luashanji"] = function(card, use, self)
	local jinks = {}
	if #jinks < 2 then return end 
	local Acards = sgs.QList2Table(self.player:getCards("he"))
	for _, cardF in ipairs(Acards) do
		if cardF:isKindOf("Jink") then table.insert(jinks, cardF) end
	end 	
	
	for _, friend in ipairs(self.friends) do
		if not friend:faceUp() then 
			use.card = sgs.Card_Parse("#luashanji:".. jinks[1]:getId() .. "+" .. jinks[2]:getId() .. ":") 
			if use.to then use.to:append(friend) ; return end	
		end 
	end 
	
	
	local enemies = self.enemies
	if math.random() > 0.5 then 
		self:sort(enemies, "defense")
		use.card = sgs.Card_Parse("#luashanji:".. jinks[1]:getId() .. "+" .. jinks[2]:getId() .. ":") 
		if use.to then use.to:append(enemies[1]) ; return end	
	end 

end 

sgs.ai_skill_choice.luashanji = function(self, choices, data)
	local target = self.room:getTag("luashanji"):toPlayer()
	if self:isFriend(target) then return "turnover2" end 
	if target:getHp() == 1 then return "damage" end 
	if target:faceUp() then return "damage" end 
	local laohu = self.room:findPlayerBySkillName("Luafadeng")
	if (laohu and self:isFriend(laohu)) or (math.random() > 0.5) then return "turnover2" end 
	return "damage"
end 

sgs.ai_card_intention.luashanji = function(self, card, from, tos)
	local target = tos[1]
	if self:isFriend(from, target) and target:faceUp() then 
		sgs.updateIntention(from, target, -70) 
	else	
		sgs.updateIntention(from, target, 70) 
	end 
end 

sgs.ai_use_priority.luashanji = 1.6

function sgs.ai_slash_prohibit.Luatanbao(self, from, to, card)
	if to:hasSkill("Luatanbao") and not (from:hasSkills("luahakurei|luasuiyue")) then 
		if getCardsNum("Jink", to, from) > 0 then return true end 
	end 
end

local luahuishen_skill = {}
luahuishen_skill.name = "luahuishen"
table.insert(sgs.ai_skills, luahuishen_skill)
luahuishen_skill.getTurnUseCard = function(self, inclusive)
	if #self.friends_noself == 0 then return end
	if self.player:getMark("@huishen") == 0 then return end
	local x = #self.friends + #self.enemies
	local z = self.room:getAlivePlayers():length() - 1
	if x >= z then return sgs.Card_Parse("#luahuishen:.:") end
end
sgs.ai_skill_use_func["#luahuishen"] = function(card, use, self)
	use.card = sgs.Card_Parse("#luahuishen:.:")
	if use.to then
		for _, friend in ipairs(self.friends_noself) do
			use.to:append(friend)
		end
		return
	end
end
sgs.ai_use_priority.luahuishen = 15
sgs.ai_card_intention.luahuishen = -50
sgs.ai_skill_cardask["luahuishen"] = function(self, data, pattern, target)
	if self.player:isKongcheng() then return "." end
	local card = sgs.Sanguosha:cloneCard(self.player:getTag("luahuishen"):toString())
	local target0 = self.player:getTag("luahuishenT"):toPlayer()
	local cards = sgs.QList2Table(self.player:getCards("h"))
	local cardsA = {}
	for _, card_0 in ipairs(cards) do
		if not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
			table.insert(cardsA, card_0)
		end
	end
	self:sortByUseValue(cardsA, true)
	if #cardsA == 0 then return "." end
	if card:isKindOf("Peach") or card:isKindOf("ExNihilo") or card:isKindOf("GodSalvation") or card:isKindOf("AmazingGrace") then
		if self:isFriend(target0) then return "$" .. cardsA[1]:getEffectiveId() else return "." end
	else
		if (self:getUseValue(card) >= self:getUseValue(cardsA[1]) - 0.2) then
			if self:isFriend(target0) then return "." else return "$" .. cardsA[1]:getEffectiveId() end
		end
	end
	return "."
end
-- luachanyuan
sgs.ai_aoe_value.luachanyuan = function(self, card, to, from, sj_num)
	if (card:isKindOf("SavageAssault")) then
		return -20
	end
	return 0
end

sgs.ai_cardneed.luahuishen = function(to, card, self)
	return to:hasSkill("luahuishen") and to:getHandcardNum() < 3
end
sgs.ai_skill_use["@@luayinbao"] = function(self, prompt)
	if #self.enemies == 0 then return "." end
	if self.player:getMark("@yinbao") == 0 then return "." end
	local cards = sgs.QList2Table(self.player:getCards("h"))
	local slash = sgs.Sanguosha:cloneCard("slash")
	local function pandin(enemy)
		local k = 0
		for _, card in ipairs(cards) do
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			if isCard("Slash", card, self.player) then
				self:useBasicCard(card, dummy_use)
				if dummy_use.card and dummy_use.to and (dummy_use.to:length() > 0) then
					k = k + 1
				end
			end
			if isCard("SavageAssault", card, self.player) or isCard("ArcheryAttack", card, self.player) then
				self:useTrickCard(card, dummy_use)
				if dummy_use.card and self:aoeIsEffective(card, to, self.player) then
					k = k + 1
				end
			end
		end
		return k
	end
	self:sort(self.enemies, "handcard2")
	for _, enemy in ipairs(self.enemies) do
		local x = self:getFriendNumBySeat(self.player, enemy) / 2
		local y = enemy:getHandcardNum()
		if enemy:hasSkill("lianying") or enemy:hasSkill("luatianhu") then y = 0 end
		if x + y > 7 then return "#luayinbao:.:->" .. enemy:objectName()  end
	end

	self:sort(self.enemies, "defense")
	for _, enemy in ipairs(self.enemies) do
		local x = self:getFriendNumBySeat(self.player, enemy)
		if self.player:inMyAttackRange(enemy) and enemy:getHp() <= 4 - x and self:slashIsEffective(slash, enemy, self.player)
			and enemy:getHandcardNum() > 1 and pandin(enemy) > 1 and self.player:canSlash(enemy, slash, false) then
			return "#luayinbao:.:->" .. enemy:objectName()
		end
	end
	self:sort(self.enemies, "handcard2")
	for _, enemy in ipairs(self.enemies) do
		local x = self:getFriendNumBySeat(self.player, enemy) / 2
		local y = enemy:getHandcardNum()
		if enemy:hasSkill("lianying") or enemy:hasSkill("luatianhu") then y = 0 end
		if x + y > 5 then return "#luayinbao:.:->" .. enemy:objectName()  end
	end

	self:sort(self.enemies, "defense")
	for _, enemy in ipairs(self.enemies) do
		local x = self:getFriendNumBySeat(self.player, enemy)
		if self.player:inMyAttackRange(enemy) and enemy:getHp() <= 3 - x and self:slashIsEffective(slash, enemy, self.player)
				and enemy:getHandcardNum() > 1 and pandin(enemy) > 0 and self.player:canSlash(enemy, slash, false) then
			return "#luayinbao:.:->" .. enemy:objectName()
		end
	end

	if #self.enemies == 1 then
		if self:slashIsEffective(slash, self.enemies[1], self.player) and self.player:canSlash(self.enemies[1], slash, false)
			and self:isWeak(self.enemies[1]) then
			return "#luayinbao:.:->" .. self.enemies[1]:objectName()
		end
	end
end
sgs.ai_card_intention.luayinbao = 50

local function findWeapon(self)
	for _, c in sgs.qlist(self.player:getHandcards()) do
		if c:isKindOf("EquipCard") then return c end
	end
end
sgs.ai_skill_invoke.luajingxia = function(self, data)
	local damage = data:toDamage()
	local target
	if self.player:objectName() == damage.from:objectName() then target = damage.to end
	if self.player:objectName() == damage.to:objectName() then target = damage.from end
	if not self:isFriend(target) and not target:faceUp() then return false end
	if self:isFriend(target) and not target:faceUp() then return true end
	if self:isFriend(target) and target:getWeapon() and not findWeapon(self) then return true end
	if self:isFriend(target) then return false end
	return true
end
sgs.ai_skill_choice.luajingxia = function(self, choices)
	local tatara = self.room:findPlayerBySkillName("luajingxia")
	if self:isFriend(tatara) then
		if not self.player:faceUp() then return "turnOver" end
		return "giveEquip"
	else
		if self.player:getEquips():isEmpty() then
			if (self:Miko(self.player, 4) == "Player_Play") or (self:Miko(self.player, 4) == "Player_Draw") then return "giveEquip" end
			if (self:Miko(self.player, 3) == "Player_Play") or (self:Miko(self.player, 3) == "Player_Draw") then return "giveEquip" end
			if (self:Miko(self.player, 2) == "Player_Play") or (self:Miko(self.player, 2) == "Player_Draw") then return "giveEquip" end
			return "turnOver"
		else
			return "giveEquip"
		end
	end
end
sgs.ai_skill_askforag.luajingxia = function(self, card_ids)
	local target = self.player:getTag("luajingxiaTP"):toPlayer()
	local cards = {}
	for _, card_id in ipairs(card_ids) do
		table.insert(cards, sgs.Sanguosha:getCard(card_id))
	end
	self:sortByKeepValue(cards, true)
	for _, cardP in ipairs(cards) do
		for _, askill in sgs.qlist(target:getVisibleSkillList(true)) do
			local callback = sgs.ai_cardneed[askill:objectName()]
			if (type(callback)=="function" and callback(target, cardP, self)) then
				return cardP:getEffectiveId()
			end
		end
	end
	return cards[1]:getEffectiveId()
end
local luajinglian_skill = {}
luajinglian_skill.name = "luajinglian"
table.insert(sgs.ai_skills, luajinglian_skill)
luajinglian_skill.getTurnUseCard = function(self, inclusive)
	if self.player:hasUsed("#luajinglian") then return end
	return sgs.Card_Parse("#luajinglian:.:")
end

sgs.ai_skill_use_func["#luajinglian"] = function(cardS, use, self)
	local card0 = findWeapon(self)
	if not card0 then return end
	local cards = {card0}
	local card, friend = self:getCardNeedPlayer(cards, false, 2)

	use.card = sgs.Card_Parse("#luajinglian:".. card0:getId() ..":")
	if friend then
		if use.to then
			use.to:append(friend)
			return
		end
	end
	if not self.player:getWeapon() then
		if use.to then
			use.to:append(self.player)
			return
		end
	else
		for _, friendS in ipairs(self.friends) do
			if not friendS:getWeapon() then
				if use.to then
					use.to:append(friendS)
					return
				end
			end
		end
		self:sort(self.friends, "defense")
		if use.to then
			use.to:append(self.friends[1])
			return
		end
	end
end

sgs.ai_card_intention.luajinglian = -50
sgs.ai_use_priority.luajinglian = 8

sgs.ai_skill_cardask["@luajinglian"] = function(self, data, pattern, target)
	local function conTain(cardQ, cardsS)
		for _,c in ipairs(cardsS) do
			if cardQ:getEffectiveId() == c:getEffectiveId() then return true end
		end
	end
	local function damucard()
		local toUse = {}
		local cards = self:getTurnUse(true)
		for _, card in ipairs(cards) do
			if self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand and card:isKindOf("EquipCard") then
				if self:getSameEquip(card, self.player) and not (card:isKindOf("Weapon") and self.player:hasSkill("luajinglian")) then
					table.insert(toUse, self:getSameEquip(card, self.player))
				end
			end
		end
		local fcaerds = sgs.QList2Table(self.player:getCards("he"))
		local jink = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
		local jink_v = self:getKeepValue(jink, true, true) - 0.5
		for _, card in ipairs(fcaerds) do
			if (not conTain(card, cards)) and self:getKeepValue(card) <= jink_v and not conTain(card, toUse) then
				table.insert(toUse, card)
			end
		end

		local jinks = self:getCardsNum("Jink")
		local jcount = 0
		for _, card in ipairs(fcaerds) do
			if (jcount == jinks - 1) or (jinks == 0) then break end
			if card:isKindOf("Jink") and not conTain(card, toUse) then
				table.insert(toUse, card)
				jcount = jcount + 1
			end
		end

		for _, card in ipairs(fcaerds) do
			if card:isKindOf("Peach") and not conTain(card, toUse) and self:OverFlowPeach(card) then
				table.insert(toUse, card)
			end
		end

		return toUse
	end

	local ause = damucard()
	if #ause == 0 then return "." end
	local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
	slash:addSubcard(ause[1])
	local dummy_use = { isDummy = true , to = sgs.SPlayerList()}
	self:useBasicCard(slash, dummy_use)
	if dummy_use.to:length() > 0 then
		return "$" .. ause[1]:getEffectiveId()
	end
	return "."
end
sgs.ai_skill_playerchosen.luajinglian = function(self, targets)
	local cardX = self.room:getTag("luajinglianTC"):toCard()
	local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
	slash:addSubcard(cardX)
	local dummy_use = { isDummy = true , to = sgs.SPlayerList()}
	self:useBasicCard(slash, dummy_use)
	if dummy_use.to:length() > 0 then
		return dummy_use.to:at(0)
	end
end


local function isFriendQ(room, target, player)
	if player:getRole() == "rebel" then
		return target:getRole() == "rebel"
	end
	if player:getRole() == "loyalist" or player:getRole() == "lord" then
		return target:getRole() == "loyalist" or target:getRole() == "lord"
	end
end
local luajianglin_skill = {}
luajianglin_skill.name = "luajianglin"
table.insert(sgs.ai_skills, luajianglin_skill)
luajianglin_skill.getTurnUseCard = function(self, inclusive)
	if self.player:hasUsed("#luajianglin") then return end
	return sgs.Card_Parse("#luajianglin:.:")
end
sgs.ai_skill_use_func["#luajianglin"] = function(cardS, use, self)
	local death = sgs.SPlayerList()
	for _,p in sgs.qlist(self.room:getAllPlayers(true)) do
		if not p:isAlive() and isFriendQ(self.room, p, self.player) then
			death:append(p)
		end
	end
	if death:isEmpty() then return end
	for _, ap in sgs.qlist(death) do
		if self.player:getNext():objectName() == ap:objectName() then
			use.card = sgs.Card_Parse("#luajianglin:.:")
			if use.to then
				use.to = sgs.SPlayerList()
				return
			end
		end
	end
	use.card = sgs.Card_Parse("#luajianglin:.:")
	if use.to then
		use.to = sgs.SPlayerList()
		return
	end
end

sgs.ai_skill_playerchosen.luajianglin = function(self, targets)
	local death = sgs.SPlayerList()
	for _,p in sgs.qlist(self.room:getAllPlayers(true)) do
		if not p:isAlive() and isFriendQ(self.room, p, self.player) then
			death:append(p)
		end
	end
	if death:isEmpty() then return end
	for _, ap in sgs.qlist(death) do
		if self.player:getNext():objectName() == ap:objectName() then
			return ap
		end
	end
	return death:at(0)
end



local luarumor_skill = {}
luarumor_skill.name = "luarumor"
table.insert(sgs.ai_skills, luarumor_skill)
luarumor_skill.getTurnUseCard = function(self, inclusive)
	if self.player:hasUsed("#luarumor") then return end
	local sp_seiga = self.room:findPlayerBySkillName("luajianglin")
	if not self.player:isWounded() then return end
	if not sp_seiga then return end
	if not sp_seiga:isWounded() then return end
	if self.player:getHp() < 2 and not self:isWeak() then return end
	if not sp_seiga then return end
	if not sp_seiga:isAlive() then return end
	return sgs.Card_Parse("#luarumor:.:")
end
sgs.ai_skill_use_func["#luarumor"] = function(cardS, use, self)
	local sp_seiga = self.room:findPlayerBySkillName("luajianglin")
	if not sp_seiga then return end
	if not sp_seiga:isAlive() then return end
	if self.player:getMaxHp() > 2 and self.player:getLostHp() == 1 and self.player:hasSkill("luashenjun") then
		use.card = sgs.Card_Parse("#luarumor:.:")
		if use.to then
			use.to = sgs.SPlayerList()
			return
		end
	end
	if self.player:getMaxHp() > 4 and self:getOverflow() < 2 then
		use.card = sgs.Card_Parse("#luarumor:.:")
		if use.to then
			use.to = sgs.SPlayerList()
			return
		end
	end
	if not (self:getOverflow() > 1 and self:getCardsNum("Peach") > 0)
		and sp_seiga:isWounded() then
		use.card = sgs.Card_Parse("#luarumor:.:")
		if use.to then
			use.to = sgs.SPlayerList()
			return
		end
	end
end

local function JiangsuiNaHaoPai(self, cards, cardId, needR)
	if not cardId then cardId = -1 end
	for _, cardX in ipairs(cards) do
		for _, askill in sgs.qlist(self.player:getVisibleSkillList(true)) do
			local filter = sgs.ai_cardneed[askill:objectName()]
			if filter and type(filter) == "function" and cardX:getEffectiveId() ~= cardId
					and sgs.ai_cardneed[askill:objectName()](self.player, cardX, self) then return cardX:getEffectiveId() end
		end
	end

	for _, cardX in ipairs(cards) do
		if cardX:getEffectiveId() ~= cardId then
			if cardX:isKindOf("Peach") or cardX:isKindOf("ExNihilo") or cardX:isKindOf("Snatch") or cardX:isKindOf("Duel") then
				return cardX:getEffectiveId()
			end
		end
	end
	for _, cardX in ipairs(cards) do
		if cardX:getEffectiveId() ~= cardId then
			if (cardX:isKindOf("AOE") and self:getAoeValue(cardX) > 45) then return cardX:getEffectiveId() end
		end
	end

	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end
	for _, cardX in ipairs(cards) do
		if cardX:getEffectiveId() ~= cardId then
			if cardX:isKindOf("Lightning") and self:willUseLightning(cardX) then return cardX:getEffectiveId() end
			if cardX:isKindOf("IronChain") and shuxin then return cardX:getEffectiveId() end
		end
	end

	if needR then
		local acards = cards
		local compare_func = function(a, b)
			return a:getNumber() < b:getNumber()
		end
		table.sort(acards, compare_func)
		acards[1]:getEffectiveId()
	end
	self:sortByKeepValue(cards, true)
	if cards[1]:getEffectiveId() ~= cardId then return cards[1]:getEffectiveId() end

end
sgs.ai_skill_askforag.luajiangsui = function(self, card_ids)
	local cards = {}
	local trickcard = {}
	for _, card_id in ipairs(card_ids) do
		local acard = sgs.Sanguosha:getCard(card_id)
		table.insert(cards, acard)
		if acard:isKindOf("TrickCard") then
			table.insert(trickcard , acard)
		end
	end
	local teshu = false
	if #cards == 3 then
		for _, cardX in ipairs(cards) do
			local a = cardX:getNumber()
			for _, cardY in ipairs(cards) do
				if cardY:getEffectiveId() ~= cardX:getEffectiveId() then
					local b = cardY:getNumber()
					for _, cardZ in ipairs(cards) do
						if cardZ:getEffectiveId() ~= cardX:getEffectiveId() or cardZ:getEffectiveId() ~= cardY:getEffectiveId() then
							if cardZ:getNumber() == a + b then self.room:writeToConsole("luajiangsui test") ;teshu = true end
						end
					end
				end
			end
		end
	end
	if not self.player:hasSkill("luajiangsui") then
		local suwako = self.room:findPlayerBySkillName("luajiangsui")
		if (self:needToLoseHp(self.player, suwako) or not self:damageIsEffective(self.player, sgs.DamageStruct_Normal, suwako)) then
			for _, cardX in ipairs(cards) do
				for _, askill in sgs.qlist(self.player:getVisibleSkillList(true)) do
					local filter = sgs.ai_cardneed[askill:objectName()]
					if filter and type(filter) == "function" and sgs.ai_cardneed[askill:objectName()](self.player, cardX, self) then return cardX:getEffectiveId() end
				end
			end

			self:sortByKeepValue(cards, true)
			return cards[1]:getEffectiveId()
		end

		if teshu then
			local acards = cards
			local compare_func = function(a, b)
				return a:getNumber() > b:getNumber()
			end
			table.sort(acards, compare_func)
			local cardX = JiangsuiNaHaoPai(self, cards, acards[1]:getEffectiveId())
			if cardX then return cardX end
		end
		if self:isWeak() then
			for _, cardX in ipairs(cards) do
				if cardX:isKindOf("Peach") or cardX:isKindOf("Analeptic") then
					return cardX:getEffectiveId()
				end
			end
		else
			local hasShi = false
			for _, cardX in ipairs(cards) do
				if cardX:isKindOf("Shit") or cardX:isKindOf("Hui") then
					hasShi = true
				end
			end
			if hasShi then
				local acards = cards
				local compare_func = function(a, b)
					return a:getNumber() > b:getNumber()
				end
				table.sort(acards, compare_func)
				local cardX = JiangsuiNaHaoPai(self, cards, acards[1]:getEffectiveId())
				if cardX then return cardX end
			else
				return JiangsuiNaHaoPai(self, cards, nil, true)
			end
		end
	else
		local num = tonumber(self.room:getTag("luajiangsui"):toString())
		if not num then return cards[1]:getEffectiveId() end
		local targetT = self.room:getTag("luajiangsuiTP"):toPlayer()
		local cardA = cards[1]
		local cardB = cards[2]
		for _, cardY in ipairs(cards) do
			local b = cardY:getNumber()
			for _, cardZ in ipairs(cards) do
				if cardZ:getEffectiveId() ~= cardY:getEffectiveId() then
					if cardZ:getNumber() == num + b then self.room:writeToConsole("luajiangsui test") ;teshu = true end
				end
			end
		end
		if teshu then
			if self:isFriend(targetT) then
				for _, cardY in ipairs(cards) do
					local b = cardY:getNumber()
					for _, cardZ in ipairs(cards) do
						if cardZ:getEffectiveId() ~= cardY:getEffectiveId() then
							if cardZ:getNumber() == num + b then return cardZ:getEffectiveId() end
						end
					end
				end
			else
				for _, cardY in ipairs(cards) do
					local b = cardY:getNumber()
					for _, cardZ in ipairs(cards) do
						if cardZ:getEffectiveId() ~= cardY:getEffectiveId() then
							if cardZ:getNumber() ~= num + b then return cardZ:getEffectiveId() end
						end
					end
				end
			end
		end
		if cardA:getNumber() > math.abs(cardB:getNumber() - num) and cardB:getNumber() > math.abs(cardA:getNumber() - num) then
			self:sortByUseValue(cards)
			return cards[1]:getEffectiveId()
		end
		local acards = cards
		local compare_func = function(a, b)
			return a:getNumber() < b:getNumber()
		end
		table.sort(acards, compare_func)
		return acards[1]:getEffectiveId()
	end
end


local luajiangsui_skill={}
luajiangsui_skill.name="luajiangsui"
table.insert(sgs.ai_skills,luajiangsui_skill)
luajiangsui_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luajiangsui") then return false end
	return sgs.Card_Parse("#luajiangsui:.:")
end
sgs.ai_skill_use_func["#luajiangsui"] = function(card, use, self)
	local friends = self.friends_noself
	self:sort(friends, "defense")
	local enemies = self.enemies
	self:sort(enemies, "defense")
	for _, friend in ipairs(friends) do
		if (self:hasSkills(sgs.masochism_skill, friend) or friend:hasSkills("wangxi|luafenxing|luaqiji"))  and (friend:getHp() >= 2) then
			use.card = sgs.Card_Parse("#luajiangsui:.:")
			if use.to then use.to:append(friend) end
			return
		end
	end
	if self:getOverflow() <= 0 then
		local i = #(self:getTurnUse(true))
		local p = self.player:getHandcardNum() - i + 2
		if ((self.player:getHp() >= 4) and (p < self.player:getMaxCards())) or ((self.player:getMaxCards() >= 2) and (self.player:getHandcardNum() <= 1))  then
			use.card = sgs.Card_Parse("#luajiangsui:.:")
			if use.to then use.to:append(self.player) end
			return
		end
	end
	for _, enemy in ipairs(enemies) do
		local x = enemy:getMaxCards() - enemy:getHandcardNum()
		if ((not self:hasSkills(sgs.masochism_skill, enemy) and not enemy:hasSkills("wangxi|luafenxing")) or (enemy:getHp() == 1 and (not self:HasGou(true))))
				and ((x <= 2) or (enemy:getMaxCards() <=2) and not self:hasSkills("duwu|danshou|LuaWeishi|jijiu",enemy))
				and not enemy:containsTrick("gaina") then
			use.card = sgs.Card_Parse("#luajiangsui:.:")
			if use.to then use.to:append(enemy) end
			return
		end
	end
	for _, enemy in ipairs(enemies) do
		use.card = sgs.Card_Parse("#luajiangsui:.:")
		if use.to then use.to:append(enemy) end
		return
	end
end
sgs.ai_card_intention.luajiangsui = function(self, card, from, tos)
	local to = tos[1]
	local x = to:getMaxCards() - to:getHandcardNum()
	local intention = 10
	if (self:hasSkills(sgs.masochism_skill, to) or to:hasSkill("wangxi")) and ((x >= 2) and (to:getMaxCards() > 2))then
		intention = 0
	elseif self:needKongcheng(to, true) then
		intention = 0
	end
	sgs.updateIntention(from, to, intention)
end
sgs.ai_use_priority.luajiangsui = 7

local luatielun_skill={}
luatielun_skill.name="luatielun"
table.insert(sgs.ai_skills,luatielun_skill)
luatielun_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luatielun") then return false end
	return sgs.Card_Parse("#luatielun:.:")
end

local function useTieLunCard(self)
	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end
		local function Check_R(card)
			local nazrin = self.room:findPlayerBySkillName("luatanbao")
			if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
				if card:hasFlag("prelingbai") then return false end
			end
			if card:isKindOf("TrickCard") then
				if card:isKindOf("Nullification") then return true end
				if card:isKindOf("IronChain") and not shuxin then return true end
				if card:isKindOf("Dismantlement") or card:isKindOf("NeedMaribel") or card:isKindOf("FaithCollection") then return true end
				if card:isKindOf("Lightning") and not self:willUseLightning(card) then return true end
				if card:isKindOf("AOE") and self:getAoeValue(card) > 35 then return false end
				if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
				if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
				if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
				local dummy_use = {isDummy = true}
				self:useTrickCard(card, dummy_use)
				if not dummy_use.card then return true end
				return false
			end
			if card:isKindOf("EquipCard") then --or card:isKindOf("DefensiveHorse")
				if card:isKindOf("OffensiveHorse") then
					return true
				end
				if card:isKindOf("DefensiveHorse") then
					return true
				end
				local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
				if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) and rinnosuke
					and rinnosuke:isAlive() and self:isFriend(rinnosuke) then return false end
				if card:isKindOf("Armor") and self:needToThrowArmor() then return true end
				if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
					local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
							or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
					if not bool_3 then return true end
				end
				if card:isKindOf("Weapon") then
					local dummy_use = {isDummy = true}
					self:useEquipCard(card, dummy_use)
					if not dummy_use.card then return true end
					if dummy_use.card and self.player:getWeapon() then return true end
				end
			end
		end
	for _, card in sgs.qlist(self.player:getCards("he")) do
		if Check_R(card) then return card end
	end
end

sgs.ai_skill_use_func["#luatielun"] = function(X, use, self)
	local card = useTieLunCard(self)
	if not card then return end
	local function getBCount(enemy)
		local count = 0
		local cards = sgs.QList2Table(enemy:getHandcards())
		local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
			for _, cc in ipairs(cards) do
				if (cc:hasFlag("visible") or cc:hasFlag(flag)) then
					if (cc:isKindOf("BasicCard")) then
						count = count + 1
						if cc:isKindOf("Peach") or (cc:isKindOf("Analeptic") and self:isWeak(enemy)) then
							count = count + 0.5
						end
					end
				else
					count = count + 0.5
				end
			end
		if enemy:containsTrick("gainb") then count = count - 1 end
		return count
	end
	local compare_func = function(a, b)
		return getBCount(a) > getBCount(b)
	end
	local enemies = self.enemies
	table.sort(enemies, compare_func)
	if #enemies > 0 and getBCount(enemies[1]) >= 2 then
		use.card = sgs.Card_Parse("#luatielun:".. card:getId() ..":")
		if use.to then use.to:append(enemies[1]) end
		return
	end
	for _, friend in ipairs(self.friends_noself) do
		if friend:isKongcheng() then
			use.card = sgs.Card_Parse("#luatielun:".. card:getId() ..":")
			if use.to then use.to:append(enemies[1]) end
			return
		end
	end
end

sgs.ai_use_priority.luatielun = 6.5
sgs.ai_card_intention.luatielun = 90

local function findtielunCard(self, cardX)
	local function Check_R(card)
		if card:isKindOf("BasicCard") then return true end
		return false
	end
	if cardX then
		local allcards = sgs.QList2Table(self.player:getCards("h"))
		for _,card2 in ipairs(allcards) do
			if  card2:getId() ~= cardX:getId() then
				if Check_R(card2) then return card2 end
			end
		end
	else
		local allcards = sgs.QList2Table(self.player:getCards("h"))
		self:sortByKeepValue(allcards)
		for _, card in ipairs(allcards) do
			for _,card2 in ipairs(allcards) do
				if card2:getId() ~= card:getId() then
					if Check_R(card) then return card end
				end
			end
		end
	end
end
sgs.ai_skill_cardask["@luatielunA"] = function(self, data)
	local to_give = findtielunCard(self)
	if not to_give then return "." end
	return "$" .. to_give:getEffectiveId()
end
sgs.ai_skill_cardask["@luatielunB"] = function(self, data)
	local cardX = self.player:getMark("kuangxiangx") - 1
	if cardX and cardX >= 0 then
		cardX = sgs.Sanguosha:getCard(cardX)
		if cardX and cardX:objectName() then
			local to_give = findtielunCard(self, cardX)
			if not to_give then return "." end
			self.room:writeToConsole("huadie test" .. cardX:objectName() .. cardX:getId())
			return "$" .. to_give:getEffectiveId()
		end
	end
	return "."
end


