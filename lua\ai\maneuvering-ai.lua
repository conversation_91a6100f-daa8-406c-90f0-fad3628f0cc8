function SmartAI:useCardThunderSlash(...)
	self:useCardSlash(...)
end

sgs.ai_card_intention.ThunderSlash = sgs.ai_card_intention.Slash

sgs.ai_use_value.ThunderSlash = 4.55
sgs.ai_keep_value.ThunderSlash = 3.66
sgs.ai_use_priority.ThunderSlash = 2.5

function SmartAI:useCardFireSlash(...)
	self:useCardSlash(...)
end

sgs.ai_card_intention.FireSlash = sgs.ai_card_intention.Slash

sgs.ai_use_value.FireSlash = 4.6
sgs.ai_keep_value.FireSlash = 3.63
sgs.ai_use_priority.FireSlash = 2.5

sgs.weapon_range.Fan = 4
sgs.ai_use_priority.fan = 2.655
sgs.ai_use_priority.Vine = 0.95

sgs.ai_skill_invoke.fan = function(self, data)
	local use = data:toCardUse()
	local jinxuandi = self.room:findPlayerBySkillName("wuling")

	for _, target in sgs.qlist(use.to) do
		if self:isFriend(target) then
			if not self:damageIsEffective(target, sgs.DamageStruct_Fire) then return true end
			if target:isChained() and self:isGoodChainTarget(target, nil, nil, nil, use.card) then return true end
		else
			if not self:damageIsEffective(target, sgs.DamageStruct_Fire) then return false end
			if target:isChained() and not self:isGoodChainTarget(target, nil, nil, nil, use.card) then return false end
			if target:hasArmorEffect("vine") or target:getMark("@gale") > 0 or (jinxuandi and jinxuandi:getMark("@wind") > 0) then
				return true
			end
		end
	end
	return false
end
sgs.ai_view_as.fan = function(card, player, card_place)
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	if sgs.Sanguosha:getCurrentCardUseReason() ~= sgs.CardUseStruct_CARD_USE_REASON_RESPONSE
		and card_place ~= sgs.Player_PlaceSpecial and card:objectName() == "slash" then
		return ("fire_slash:fan[%s:%s]=%d"):format(suit, number, card_id)
	end
end

local fan_skill={}
fan_skill.name="fan"
table.insert(sgs.ai_skills,fan_skill)
fan_skill.getTurnUseCard=function(self)
	local cards = self.player:getCards("h")
	cards=sgs.QList2Table(cards)
	local slash_card

	for _,card in ipairs(cards)  do
		if card:isKindOf("Slash") and not (card:isKindOf("FireSlash") or card:isKindOf("ThunderSlash")) then
			slash_card = card
			break
		end
	end

	if not slash_card  then return nil end
	local suit = slash_card:getSuitString()
	local number = slash_card:getNumberString()
	local card_id = slash_card:getEffectiveId()
	local card_str = ("fire_slash:fan[%s:%s]=%d"):format(suit, number, card_id)
	local fireslash = sgs.Card_Parse(card_str)
	assert(fireslash)

	return fireslash

end

function sgs.ai_weapon_value.fan(self, enemy)
	if enemy and (enemy:hasArmorEffect("vine") or enemy:getMark("@gale") > 0) then return 6 end
end

function sgs.ai_armor_value.vine(player, self)	--yun
	if self:needKongcheng(player) and player:getHandcardNum() == 1 then
		return player:hasSkill("kongcheng") and 5 or 3.8
	end
	if self:hasSkills(sgs.lose_equip_skill, player) then return 3.8 end
	if not self:damageIsEffective(player, sgs.DamageStruct_Fire) then return 6 end
	if self.player:hasSkill("sizhan") then return 4.9 end
	if player:hasSkill("jujian") and not player:getArmor() and #(self:getFriendsNoself(player)) > 0 and player:getPhase() == sgs.Player_Play then return 3 end
	if player:hasSkills("diyyicong|yinbing|yfangtong") and not player:getArmor() and player:getPhase() == sgs.Player_Play then return 3 end
	if player:hasSkill("xiansi") and player:getPile(counter):length() > 1 then return 4 end
	if player:hasSkills("tiaoxin|mouzhu|yjinshen|qiuyuan|jijiu|huomo|nosqiuyuan") then return 4 end
	if player:hasSkill("Luamengyan") then return 5 end
	local fslash = sgs.Sanguosha:cloneCard("fire_slash")
	local tslash = sgs.Sanguosha:cloneCard("thunder_slash")
	if player:isChained() and (not self:isGoodChainTarget(player, self.player, nil, nil, fslash) or not self:isGoodChainTarget(player, self.player, nil, nil, tslash)) then return -3 end

	for _, enemy in ipairs(self:getEnemies(player)) do
		if enemy:hasSkills("luanji|luanji_po|yzhaoxiang") then return 2 end
		if (enemy:canSlash(player) and enemy:hasWeapon("fan")) or enemy:hasSkills("huoji|longhun|shaoying|zonghuo|wuling|ol_xueji|luaguihuo|luarebin")
		  or (enemy:hasSkill("yeyan") and enemy:getMark("@flame") > 0) then return -4 end
		if getKnownCard(enemy, player, "FireSlash", true) >= 1 or getKnownCard(enemy, player, "FireAttack", true) >= 1 or
			getKnownCard(enemy, player, "fan") >= 1 then return -3 end
	end

	if (#self.enemies < 3 and sgs.turncount > 2) or player:getHp() <= 2 then return 5 end
	return 0
end

function SmartAI:shouldUseAnaleptic(target, slash)
	if sgs.turncount <= 1 and self.role == "renegade" and sgs.isLordHealthy() and self:getOverflow() < 2 then return false end
	if self.player:hasSkill("luahongtu") and self.player:getMark("@luahongtu") == 0 then return false end
	if target:hasArmorEffect("silver_lion") and not (self.player:hasWeapon("qinggang_sword") or self.player:hasSkill("jueqing")) then
		return
	end
	if target:hasSkill("lualvzhi") then return end
	if self.player:getMark("@luajulian1") > 0 then return end
	if target:getMark("@luashaojie") > 0 then return end
	if (target:getMark("@qiangweiw") > 0 and target:hasSkill("luaqiangwei")) then return end 
	if target:hasSkill("zhenlie") then return false end
	if target:getDefensiveHorse() and target:getDefensiveHorse():objectName() == "hongrai" then return end
	if target:getOffensiveHorse() and target:getOffensiveHorse():objectName() == "shanghai" then return end
	if target:hasSkill("luacaihuosp") then return false end
	if #self.enemies > 2 then
		local shinki = self.room:findPlayerBySkillName("luatianzhao")
		if shinki and self:isFriend(shinki) and self:getOverflow() and self.player:objectName() ~= shinki:objectName() then
			if not self:YouMu2(target,true) then return end
		end
	end

	if target:hasSkills("luabeihuan") and not self:YouMu2(target,true) then return false end
	if target:hasSkill("xiangle") or self.player:hasSkill("luacuiruo") then
		local basicnum = 0
		for _, acard in sgs.qlist(self.player:getHandcards()) do
			if acard:getTypeId() == sgs.Card_TypeBasic and not acard:isKindOf("Peach") then basicnum = basicnum + 1 end
		end
		if basicnum < 3 then return false end
	end
	if self.player:hasSkill("canshi") and self.player:hasFlag("canshi") and self.player:getHandcardNum() < 3 then return false end

	if self.player:hasSkill("luachenti") and self.player:getHandcardNum() < 4 and self:getOverflow() < 2 and not self:isWeak(target) then
		return false
	end

	if target:hasSkill("anxian") and not target:isKongcheng() then return false end	--yun
	local yuji = self.room:findPlayerBySkillName("qianhuan")
	if yuji and self:isEnemy(yuji) and yuji:getPile("sorcery"):length() > 0 then return false end
	if target:hasSkill("buqu") and target:getHp() < 2 then return false end
	if target:hasSkill("luamaobu") and not self.player:isChained() then return false end
	if target:hasSkill("yqinku") and target:isWounded() then return false end
	if self.player:hasWeapon("spear") and self.player:getCards("h"):length() < 4 and not self:needKongcheng() then return false end	--yun
	if target:hasSkill("yjinshen") and not self.player:hasSkill("jueqing") and self:hasSuit("spade", true, target) then return false end
	if self:hasSkills(sgs.masochism_skill .. "|longhun|buqu|nosbuqu|" .. sgs.recover_skill .. "|".. sgs.exclusive_skill .. "|" .. sgs.need_maxhp_skill, target)	--yun
		and self.player:hasSkill("nosqianxi") and self.player:distanceTo(target) == 1 then
		return
	end

	if self:canLiegong(target, self.player) then return true end	--yun
	if self.player:hasSkill("tieji") then return true end

	if self.player:hasWeapon("axe") and self.player:getCards("he"):length() > 4 then return true end
	if target:hasFlag("dahe") then return true end

	if ((self.player:hasSkill("roulin") and target:isFemale()) or (self.player:isFemale() and target:hasSkill("roulin"))) or self.player:hasSkill("wushuang") then
		if getKnownCard(target, player, "Jink", true, "he") >= 2 then return false end
		return getCardsNum("Jink", target, self.player) < 2
	end
	
	if self.player:hasSkill("niaoxiang") and self.player:isAdjacentTo(target) then	--yun 
		if getKnownCard(target, player, "Jink", true, "he") >= 2 then return false end
		return getCardsNum("Jink", target, self.player) < 2
	end
	if (self.player:hasSkill("luatianzhao") or (self.player:hasSkill("luajunzhen") and self:ALICE())) then return true end
	if getKnownCard(target, self.player, "Jink", true, "he") >= 1 and not (self:getOverflow() > 0 and self:getCardsNum("Analeptic") > 1) then return false end
	return self:getCardsNum("Analeptic") > 1 or getCardsNum("Jink", target) < 1 or sgs.card_lack[target:objectName()]["Jink"] == 1 or self:getOverflow() > 0

end

function SmartAI:useCardAnaleptic(card, use)
	if card:getSkillName() == "luaqiji" then --card:getSkillName() == "luazhengyi" or
		use.card = card
	end
	if self.player:hasSkill("luahongtu") and self.player:getMark("@luahongtu") == self.player:getHp() then
		use.card = card
	end
	if not self.player:hasEquip(card) and self:needKongcheng() and self.player:getCards("h"):length() < 2 --and not self:isWeak()	--yun
		and sgs.Analeptic_IsAvailable(self.player, card) then
		use.card = card
	end
	for _, acard in sgs.qlist(self.player:getHandcards()) do
		if acard:isKindOf("Slash") then
			if self.player:hasSkill("luajulian") and self:Skadi(acard, nil, self.player, true)  then
				use.card = card
			end
		end
	end
	if self.player:hasSkill("zhanjue") and self.player:getMark("zhanjuedraw") < 2 and self.player:getHandcardNum() > 1 
	and not self.player:hasEquip(card) and sgs.Analeptic_IsAvailable(self.player, card) then	--yun
		local duel = sgs.Sanguosha:cloneCard("duel")
		local dummy_use = {isDummy = true}
		self:useTrickCard(duel, dummy_use)
		if dummy_use.card then use.card = card end
		return
	end
	if ((self.player:hasFlag("jianji")) and (self.player:getPhase() == sgs.Player_Play) and (self.player:getMark("jianji") == 1)) then return nil end
	if self.player:hasSkill("taoxi") and self.player:hasFlag("TaoxiRecord") then	--yun
        local taoxi_id = self.player:getTag("TaoxiId"):toInt()
        if taoxi_id and taoxi_id == card:getEffectiveId() and sgs.Analeptic_IsAvailable(self.player, card) then
            use.card = card
        end
    end
	
	if (self.player:hasSkill("luaqishu") and (self.player:getMark("qishublack") > 0) and (card:getSkillName() == "luaqishu")) then	--yun
		local diamond_card
		local cards = self.player:getCards("he")
		cards = sgs.QList2Table(cards)
		self:sortByUseValue(cards,true)

		local disCrossbow = false
		if self:getCardsNum("Slash") < 2 or self.player:hasSkill("paoxiao") then
			disCrossbow = true
		end

		local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
		for _,card_0 in ipairs(cards)  do
			local useAll = false
			local slash_0 = sgs.Sanguosha:cloneCard("slash", card_0:getSuit(), card_0:getNumber())
			slash_0:addSubcard(card_0)
			slash_0:setSkillName("LuaYuanzu")		
			if self:canKillEnermyAtOnce(false, slash_0) then useAll = true end 
		
			local heavy_dmg = false 
			
			self:useBasicCard(slash_0, dummy_use)
			if not dummy_use.to:isEmpty() then
				for _, playerX in sgs.qlist(dummy_use.to) do
					if self:hasHeavySlashDamage(self.player, slash_0, playerX) then heavy_dmg = true end 
				end 
			end 	
			local snatch = sgs.Sanguosha:cloneCard("snatch")
			local dismantlement = sgs.Sanguosha:cloneCard("slash")
			local val1 = self:getUseValue(snatch)
			local val0 = self:getUseValue(dismantlement) + 1
			local bool_0 = (self:getUseValue(card_0) <= val0) or (heavy_dmg and (self:getUseValue(card_0) <= val1))
			
			if card_0:isRed()
			and (((not self:OverFlowPeach(card_0)) and not isCard("ExNihilo", card_0, self.player)) or useAll)
			and (not isCard("Crossbow", card_0, self.player) and not disCrossbow)
			and (bool_0 or sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_Residue, self.player, sgs.Sanguosha:cloneCard("slash")) > 0) then
				diamond_card = card_0
				break
			end
		end
			
		if diamond_card then 

			local hasBlack = {}
			local allcards = sgs.QList2Table(self.player:getCards("he")) 
			for _,acard in ipairs(allcards) do
				if acard:isBlack() then table.insert(hasBlack, acard) end 
			end 	
			
			local slash = sgs.Sanguosha:cloneCard("slash", diamond_card:getSuit(), diamond_card:getNumber())
			
			if not dummy_use.to:isEmpty() then 
				use.card = card
				return
			end 
			
		end 
	end

	if self.player:hasSkill("lualueying") then
		local callback = sgs.ai_skill_use["@@lualueying"]
		if callback and callback(self) and callback(self) ~= "." then
			use.card = card
			return
		end
	end

	if self:ayalight(card) then
		use.card = card
	end

	if self.player:hasSkill("luamoulue") and self:Kitcho() then
		use.card = card
	end

	if self.player:hasSkill("luajingjuan") and not self.player:isKongcheng() and self.room:getCardPlace(card:getEffectiveId()) == sgs.Player_PlaceHand then
		local all = self:Hiziri()
		if #all == 0 then return v end
		local compare_func = function(a, b)
			return #a > #b
		end
		table.sort(all, compare_func)
		local coulduse = true
		for _, cardX in ipairs(all[1]) do
			if cardX:getId() == card:getId() then coulduse = false end
		end
		if coulduse then use.card = card end

	end
end


function SmartAI:findjingjieCard(cardex)
	local fcaerds = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(fcaerds)

	local function Check_R(card)
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return end
		end
        if cardex and cardex:getEffectiveId() == card:getEffectiveId() then return false end
		if card:getSuit() ~= sgs.Card_Heart and card:getSuit() ~= sgs.Card_Spade then return false end
		if card:isKindOf("TrickCard") then
			if card:isKindOf("Nullification") then return true end
			if card:isKindOf("IronChain") then return true end
			if card:isKindOf("Dismantlement") or card:isKindOf("NeedMaribel") or card:isKindOf("FaithCollection") then return true end
			if card:isKindOf("Lightning") and not self:willUseLightning(card) then return true end
			if card:isKindOf("AOE") and self:getAoeValue(card) > 35 then return false end
			if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
			if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
			local dummy_use = {isDummy = true}
			self:useTrickCard(card, dummy_use)
			if not dummy_use.card then return true end
			return false
		end
		if card:isKindOf("EquipCard") then --or card:isKindOf("DefensiveHorse")
			if card:isKindOf("Wanbaochui") then return false end
			if card:isKindOf("OffensiveHorse") then
				return true
			end
			if card:isKindOf("DefensiveHorse") then
				return true
			end
			if card:isKindOf("Armor") and self:needToThrowArmor() then return true end
			if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
				local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
						or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
				if not bool_3 then return true end
			end
			if card:isKindOf("Weapon") then

				local dummy_use = {isDummy = true}
				self:useEquipCard(card, dummy_use)
				if not dummy_use.card then return true end
				if dummy_use.card and self.player:getWeapon() then return self.player:getWeapon() end
			end
		end
		if card:isKindOf("Slash") or card:isKindOf("Jink") or card:isKindOf("Ofuda") then return true end
		if card:isKindOf("Peach") and self:OverFlowPeach(card) then return true end
		if card:isKindOf("Analeptic") and self:getCardsNum("Analeptic") > 0 then return true end
	end

	for _, card in ipairs(fcaerds) do
		local a = Check_R(card)
		if a and type(a) == "boolean" then
			return card
		elseif a then
			return a
		end
	end
end

function SmartAI:searchForAnaleptic(use, enemy, slash)
	if not self.toUse then return nil end
	if not use.to then return nil end

	local card_str = self:getCardId("Banquet")
	if card_str then
		local Banquet = sgs.Card_Parse(card_str)
		assert(Banquet)
		return Banquet
	end

	local analeptic = self:getCard("Analeptic")
	if not analeptic then 
		analeptic = sgs.Sanguosha:cloneCard("analeptic") 
	end

	local analepticAvail = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_Residue, self.player, analeptic)
	local slashAvail = 0

	for _, card in ipairs(self.toUse) do
		if analepticAvail == 1 and card:getEffectiveId() ~= slash:getEffectiveId() and card:isKindOf("Slash") then return nil end
		if card:isKindOf("Slash") then slashAvail = slashAvail + 1 end
	end

	if analepticAvail > 1 and analepticAvail < slashAvail then return nil end
	if not sgs.Analeptic_IsAvailable(self.player) then return nil end
	for _, p in sgs.qlist(use.to) do
		if p:hasSkill("zhenlie") then return end
		if p:hasSkill("anxian") and not p:isKongcheng() and self:getOverflow() < 0 then return end
	end
	if ((self.player:hasFlag("jianji")) and (self.player:getPhase() == sgs.Player_Play) and (self.player:getMark("jianji") == 1)) then return nil end
	local cards = self.player:getHandcards()
	cards = sgs.QList2Table(cards)
	self:fillSkillCards(cards)
	local allcards = self.player:getCards("he")
	allcards = sgs.QList2Table(allcards)

	if self.player:getPhase() == sgs.Player_Play then
		if self.player:hasFlag("lexue") then
			local lexuesrc = sgs.Sanguosha:getCard(self.player:getMark("lexue"))
			if lexuesrc:isKindOf("Analeptic") then
				local cardsD = sgs.QList2Table(self.player:getHandcards())
				self:sortByUseValue(cardsD, true)
				for _, hcard in ipairs(cardsD) do
					if hcard:getSuit() == lexuesrc:getSuit() then
						local lexue = sgs.Sanguosha:cloneCard("analeptic", lexuesrc:getSuit(), lexuesrc:getNumber())
						lexue:addSubcard(hcard:getId())
						lexue:setSkillName("lexue")
						if self:getUseValue(lexuesrc) > self:getUseValue(hcard) then
							return lexue
						end
					end
				end
			end
		end

		if self.player:hasLordSkill("weidai") and not self.player:hasFlag("Global_WeidaiFailed") then
			return sgs.Card_Parse("@WeidaiCard=.")
		end

		if self.player:hasSkill("luatianzhao") and not self.player:hasFlag("Global_luatianzhaoAnaFailed") then
			self.room:setPlayerFlag(self.player, "tianzhaojiu")
			return sgs.Card_Parse("#luatianzhao:.:")
		end
		
		if self.player:hasSkill("luazhengyi") and not self.player:hasFlag("luazhengyi") then
			local a0 = self:justice(true, slash)
			if a0 then
				local card_stR = ("analeptic:luazhengyi[%s:%s]=%d"):format(a0:getSuitString(), a0:getNumberString(), a0:getEffectiveId())
				local analeptiC = sgs.Card_Parse(card_stR)
				assert(analeptiC)
				return analeptiC
			end
		end
		if self.player:hasSkill("luajunzhen") and self:ALICE() then
			local x = 0
			for _, acard in sgs.qlist(self.player:getCards("he")) do
				if acard:isKindOf("EquipCard") then
					x = x + 1
				end
			end
			local y = self.player:getMark("@luajunzhen") + x
			if not self.player:hasFlag("luajunzhen") or (self:ALICE(true) or y > 2) then
				self.room:writeToConsole("alice ana test")
				return sgs.Card_Parse("#luajunzhen:" .. self:ALICE():getEffectiveId()..":")
			end
		end
		if self.player:hasSkill("luaqiji") and self.player:usedTimes("guhuo_select") <= self.player:getLostHp() and self.player:hasFlag("qiji9_success")
			and (self.player:getHandcardNum() > 1) then
			self.room:setPlayerFlag(self.player, "qijijiu")
			return sgs.Card_Parse("#guhuo_select:.:")
		end
		
	end


	card_str = self:getCardId("Analeptic")
	if card_str then return sgs.Card_Parse(card_str) end

	for _, anal in ipairs(cards) do
		if (anal:getClassName() == "Analeptic") and not (anal:getEffectiveId() == slash:getEffectiveId()) then
			local bool = self.player:hasSkill("luachaofan") and (not self.player:hasFlag("forbidChaofan")) and anal:getNumber() <= self.player:getMark("chaofan")
			local bool2 = self.player:hasSkill("luachaofan") and self.player:getMark("chaofan") == 0 and (not self.player:hasFlag("forbidChaofan")) and anal:getNumber() > self.player:getMark("chaofan")
			if not bool and not bool2 then
				return anal
			end
		end
	end
end

sgs.dynamic_value.benefit.Analeptic = true

sgs.ai_use_value.Analeptic = 5.98
sgs.ai_keep_value.Analeptic = 4.1
sgs.ai_use_priority.Analeptic = 3.0

local function handcard_subtract_hp(a, b)
	local diff1 = a:getHandcardNum() - a:getHp()
	local diff2 = b:getHandcardNum() - b:getHp()

	return diff1 < diff2
end

function SmartAI:getSupplyShortageValue(enemy, enemies)
	local zhanghe = self.room:findPlayerBySkillName("qiaobian")
	local zhanghe_seat = zhanghe and zhanghe:faceUp() and not zhanghe:isKongcheng() and self:isEnemy(zhanghe) and zhanghe:getSeat() or 0	--yun

	local sb_daqiao = self.room:findPlayerBySkillName("yanxiao")
	local yanxiao = sb_daqiao and self:isEnemy(sb_daqiao) and sb_daqiao:faceUp() and	--yun
			(getKnownCard(sb_daqiao, self.player, "diamond", nil, "he") > 0
					or sb_daqiao:getHandcardNum() + self:ImitateResult_DrawNCards(sb_daqiao, sb_daqiao:getVisibleSkillList(true)) > 3
					or sb_daqiao:containsTrick("YanxiaoCard"))

	if #enemies == 0 then return 0 end
	if enemy:containsTrick("supply_shortage") or enemy:containsTrick("YanxiaoCard") then return -100 end
	if enemy:getMark("juao") > 0 then return -100 end
	if enemy:hasSkill("qiaobian") and not enemy:containsTrick("supply_shortage") and not enemy:containsTrick("indulgence") then return -100 end
	if zhanghe_seat > 0 and (self:playerGetRound(zhanghe) <= self:playerGetRound(enemy) and self:enemiesContainsTrick() <= 1 or not enemy:faceUp()) then
		return - 100 end
	if yanxiao and (self:playerGetRound(sb_daqiao) <= self:playerGetRound(enemy) and self:enemiesContainsTrick(true) <= 1 or not enemy:faceUp()) then
		return -100 end
	local value = 0 - enemy:getHandcardNum()
	if self:hasSkills("yongsi|haoshi|tuxi|nostuxi|noslijian|lijian|fanjian|neofanjian|dimeng|jijiu|jieyin|manjuan|"..
			"beige|canshi|olzishou|kuangcai|luasanaex|luahuapu|luajianji|lualindong|luakuangwang", enemy)	--yun
	then value = value + 10
	end
	if self:hasSkills("zaiqi|yyuanlao", enemy) then value = value + 5 * enemy:getLostHp() end	--yun
	if self:hasSkills(sgs.cardneed_skill,enemy) or self:hasSkills("zhaolie|tianxiang|qinyin|yanxiao|zhaoxin|toudu|renjie|luanji|yzhaoxiang|ol_tianxiang", enemy)	--yun
	then value = value + 5
	end
	if enemy:hasSkill("luaduhuo") and not enemy:isKongcheng() then value = value - 5 end
	if self:hasSkills("yingzi|shelie|xuanhuo|buyi|jujian|jiangchi|luacaihuo|luapaomo|luafengshou|luaaoshu|luajulian|"..
			"LuaXinXing|luayuetuan|mizhao|hongyuan|chongzhen|duoshi|tuntian|yyuanlv|luafenxing",enemy) then value = value + 5 end
	if enemy:getMark("@LuaBisha2") == 2 and (value > - 70) and self.player:hasSkill("LuaBisha") then value = value + 70 end   --pay
	if enemy:getMark("@LuaBisha2") == 1 and (value > - 70) and self.player:hasSkill("LuaBisha") then value = value + 25 end
	if enemy:hasSkills("zishou|luachongsheng") then value = value + enemy:getLostHp() end
	if self:isWeak(enemy) then value = value + 5 end
	if enemy:isLord() then value = value + 3 end
	if enemy:hasSkill("luashuangyue") then value = value - 10 end
	if self:objectiveLevel(enemy) < 3 then value = value - 10 end
	if not enemy:faceUp() then value = value - 10 end
	if self:hasSkills("keji|shensu|qingyi", enemy) then value = value - enemy:getHandcardNum() end
	if self:hasSkills("mishu9|guanxing|xiuluo|tiandu|guidao|noszhenlie|Luasishu|LuaTaiji|luaxiaosa|luaxieli|luaguijie", enemy) then value = value - 5 end
	if not sgs.isGoodTarget(enemy, self.enemies, self) then value = value - 1 end
	if self:needKongcheng(enemy) then value = value - 1 end
	if enemy:getMark("@kuiwei") > 0 then value = value - 2 end
	if (self.player:hasSkill("luabenwo") and not enemy:inMyAttackRange(self.player) and not enemy:isChained()) then value = value + 3 end
	if enemy:hasSkill("luahonggguang") then value = value - 100 end
	return value
end
function SmartAI:useCardSupplyShortage(card, use)
	local enemies = self:exclude(self.enemies, card, self.player, use.distance)
	local cmp = function(a,b)
		return self:getSupplyShortageValue(a, enemies) > self:getSupplyShortageValue(b, enemies)
	end

	table.sort(enemies, cmp)
	if #enemies == 0 then return end
	local target = enemies[1]
	if (self:getSupplyShortageValue(target, enemies) <= 0) and
			(self.player:hasSkill("Luayuelong") and card and card:isBlack() and (not ((self:YouMu2(target, true)
					and self:isWeak(target)) or (self:getOverflow() > 0))) and card:getSkillName() ~= "Luayuelong" and not use.shoutu) then return end -- pay 受兔
				
	if (self.player:hasSkill("luafenxing") and (self.player:getHandcardNum() <= 2) and (getvalue(target) <= -20) and (self:getOverflow() <= 0)) then return end 	
	if self:getSupplyShortageValue(target, enemies) > -100 then
		use.card = card
		if use.to then use.to:append(target) end
		return
	end
end

sgs.ai_use_value.SupplyShortage = 7
sgs.ai_keep_value.SupplyShortage = 3.48
sgs.ai_use_priority.SupplyShortage = 0.5
sgs.ai_card_intention.SupplyShortage = 120

sgs.dynamic_value.control_usecard.SupplyShortage = true

function SmartAI:getChainedFriends(player)
	player = player or self.player
	local chainedFriends = {}
	for _, friend in ipairs(self:getFriends(player)) do
		if friend:isChained() then
			table.insert(chainedFriends, friend)
		end
	end
	return chainedFriends
end

function SmartAI:getChainedEnemies(player)
	player = player or self.player
	local chainedEnemies = {}
	for _, enemy in ipairs(self:getEnemies(player)) do
		if enemy:isChained() then
			table.insert(chainedEnemies,enemy)
		end
	end
	return chainedEnemies
end

function SmartAI:isGoodChainPartner(player) --稍微掉那么点血没太大关系的那种队友返回真
	player = player or self.player
	if hasBuquEffect(player) or (player:hasSkill("niepan") and player:getMark("@nirvana") > 0) 
		or self:needToLoseHp(player, player, false, true)	--yun
		or (player:hasSkill("luaqiangwei") and player:getMark("@qiangweiw") > 0)
		or (player:hasSkill("luachongsheng") and not player:isWounded() and player:getMark("@luachongsheng") > 0)
		or self:getDamagedEffects(player) or (player:hasSkill("fuli") and player:getMark("@laoji") > 0) then
		return true
	end
	return false
end

function SmartAI:isGoodChainTarget(who, source, nature, damagecount, card, koishi_a) --返回真，表示这伤害可以打，无论是不是属性伤害都不亏
	source = source or self.player
	if not who then self.room:writeToConsole(debug.traceback()) return end
	if source:hasSkill("jueqing") then return not self:isFriend(who) end --不是队友返回真
	if who:getMark("@luashaojie") > 0 and nature ~= sgs.DamageStruct_Normal then return end
	if not koishi_a then 
		if (not card) or (card and not card:isKindOf("AOE")) then 
			local koishi = self.player:hasSkill("luabenwo") and (not who:inMyAttackRange(self.player)) and who:objectName() ~= self.player:objectName() and (not who:isChained())
			local koishi2 = self.player:hasSkill("luabenwo") and (not who:inMyAttackRange(self.player)) and who:objectName() ~= self.player:objectName() and who:isChained()
			if koishi2 then return not self:isFriend(who) end 
			if (not who:isChained()) and not koishi then return not self:isFriend(who) end --不是队友返回真,因为他没有被连锁，给对方伤害总是好的，这是预设 
		end 
	end 
	nature = nature or sgs.DamageStruct_Fire
	damagecount = damagecount or 1

	if card and card:isKindOf("Slash") then
		nature = card:isKindOf("FireSlash") and sgs.DamageStruct_Fire
					or card:isKindOf("ThunderSlash") and sgs.DamageStruct_Thunder
					or sgs.DamageStruct_Normal
		damagecount = damagecount + self:hasHeavySlashDamage(source, card, who, true) - 1
	elseif nature == sgs.DamageStruct_Fire then
		if who:hasArmorEffect("vine") then damagecount = damagecount + 1 end
		if who:getMark("@gale") > 0 and self.room:findPlayerBySkillName("kuangfeng") then damagecount = damagecount + 1 end
	end

	if hasWulingEffect("@fire") then nature = sgs.DamageStruct_Fire
	elseif not (card and card:isKindOf("Slash")) and hasWulingEffect("@thunder") and nature == sgs.DamageStruct_Thunder then damagecount = damagecount + 1
	elseif not (card and card:isKindOf("Slash")) and hasWulingEffect("@wind") and nature == sgs.DamageStruct_Fire then damagecount = damagecount + 1
	end

	if not self:damageIsEffective(who, nature, source) then return end
	if card and card:isKindOf("TrickCard") and not self:hasTrickEffective(card, who, self.player) then return end

	if who:hasArmorEffect("silver_lion") then damagecount = 1 end


	local kills, killlord, the_enemy = 0
	local good, bad, F_count, E_count = -0.5, 0, 0, 0
	local peach_num = self.player:objectName() == source:objectName() and self:getCardsNum("Peach") or getCardsNum("Peach", source, self.player)

	local function getChainedPlayerValue(target, dmg) -- 返回值越高，越表示这个人被伤害时掉点血是值得的，对伤害来源也是
		local newvalue = 0
		if self:isGoodChainPartner(target) then newvalue = newvalue + 1 end
		if self:hasSkills(sgs.masochism_skill, target) then newvalue = newvalue + 0.5 end
		if target:hasSkill("Luashenyi") and target:hasSkill("Luayuanling") and target:objectName() ~= self.player:objectName()
			and target:getMark("@yuanxing") > 0 then newvalue = newvalue - 1 end
		if self:isWeak(target) then newvalue = newvalue - 1 end
		if dmg then
			if target:getMark("@luachongsheng") > 0 then dmg = dmg + target:getMark("@luachongsheng") end
			if who:getMark("@luachongsheng") > 0 then dmg = dmg + who:getMark("@luachongsheng") end
			if nature == sgs.DamageStruct_Fire then
				if target:hasArmorEffect("vine") then dmg = dmg + 1 end
				if target:getMark("@gale") > 0 then dmg = dmg + 1 end
				if hasWulingEffect("wind") then dmg = dmg + 1 end
			elseif nature == sgs.DamageStruct_Thunder then
				if hasWulingEffect("@thunder") then dmg = dmg + 1 end
			end
		end
		if self:cantbeHurt(target, source, damagecount) then newvalue = newvalue - 100 end  --防吃菜、武魂、天香
		if not source:isChained() and target:hasSkill("luamaobu") then newvalue = newvalue + 2 end
		if damagecount + (dmg or 0) >= target:getHp() then
			newvalue = newvalue - 2
			if target:isLord() and not self:isEnemy(target) then killlord = true end
			if self:isEnemy(target) then kills = kills + 1 end
		else
			if self:isEnemy(target) and source:getHandcardNum() < 2 and target:hasSkills("ganglie|neoganglie|vsganglie") and source:getHp() == 1	--yun
				and self:damageIsEffective(source, nil, target) and peach_num < 1 then newvalue = newvalue - 100 end
			if target:hasSkill("vsganglie") then
				local can
				for _, t in ipairs(self:getFriends(source)) do
					if t:getHp() == 1 and t:getHandcardNum() < 2 and self:damageIsEffective(t, nil, target) and peach_num < 1 then
						if t:isLord() then
							newvalue = newvalue - 100
							if not self:isEnemy(t) then killlord = true end
						end
						can = true
					end
				end
				if can then newvalue = newvalue - 2 end
			end
		end

		if target:hasArmorEffect("silver_lion") then return newvalue - 1 end
		return newvalue - damagecount - (dmg or 0)
	end


	local value = getChainedPlayerValue(who)
	if self:isFriend(who) then
		good = good + value
		F_count = F_count + 1
	elseif self:isEnemy(who) then
		bad = bad + value
		E_count = E_count + 1
	end

	if nature == sgs.DamageStruct_Normal then return good >= bad end

	for _, player in sgs.qlist(self.room:getAllPlayers()) do
		if player:objectName() ~= who:objectName() and player:isChained() and self:damageIsEffective(player, nature, source)
			and not (card and card:isKindOf("FireAttack") and not self:hasTrickEffective(card, who, self.player)) then
			local getvalue = getChainedPlayerValue(player, 0)
			if kills == #self.enemies and not killlord and sgs.getDefenseSlash(player, self) < 2 then
				if card then self.room:setCardFlag(card, "AIGlobal_KillOff") end
				return true
			end
			if self:isFriend(player) then
				good = good + getvalue
				F_count = F_count + 1
			elseif self:isEnemy(player) then
				bad = bad + getvalue
				E_count = E_count + 1
				the_enemy = player
			end
		end
	end

	if killlord and self.role == "rebel" and not sgs.GetConfig("EnableHegemony", false) then return true end

	if card and F_count == 1 and E_count == 1 and the_enemy and the_enemy:isKongcheng() and the_enemy:getHp() == 1 then
		for _, c in ipairs(self:getCards("Slash")) do -- 先用属性杀刀他
			if not c:isKindOf("NatureSlash") and not self:slashProhibit(c, the_enemy, source) then return end
		end
	end

	if F_count > 0 and E_count <= 0 then return end
	if koishi_a then
		--self.room:writeToConsole("goodchaintarget test ，good " .. good .. "bad " .. bad .. "target " .. who:getGeneralName())
		return good >= bad + koishi_a
	end 
	
	return good >= bad
end

function SmartAI:useCardIronChain(card, use)	--@todo 刘协1血时，在敌方全连状态下，对自己一个人出连环	--yun
	local needTarget = (card:getSkillName() == "guhuo" or card:getSkillName() == "nosguhuo" or card:getSkillName() == "qice" or (self.player:hasSkills(sgs.shuxin_skill) and #self.enemies > 1)
		or self.player:hasSkills("luaqizou|Luasishu|Luafengmi|luelangchao|lualushou|luatanmi|luashuangfeng|luabeihuan|luahuapu")) --用此条控制卡牌不重铸
	local f_has_shuxin = false
	if self.player:hasSkill("luaqiuwen") then --这条也能控制
		local bool = false 
		for _, friend in ipairs(self.friends) do
			if friend:hasSkills(sgs.shuxin_skill) then bool = true; f_has_shuxin = true; break end
		end 		
		for _, friend in ipairs(self.friends) do
			if friend:hasSkill("luaqiangwei") and (friend:getHp() <= 2) and self.player:inMyAttackRange(friend) then bool = true; break end
		end 	
		if not bool then return end 
		if not needTarget then needTarget = true end 
	end 
	if self.player:hasSkill("luabeihuan") and self.player:hasSkill("lualihe") then needTarget = true end 
	if self.player:hasSkill("luazhengyi") and self.player:getWeapon() and card:getSuit() == self.player:getWeapon():getSuit() then needTarget = true end 
	if self.player:hasSkill("luayueshi") then  --pay 露米娅
		local count1 = 0
		for _, acard in sgs.qlist(self.player:getHandcards()) do
			if acard:getNumber() > 9 then count1 = count1 + 1 end 
		end 
		if count1 <= 1 then needTarget = true end 
	end 
	
	if not (self.player:hasSkill("noswuyan") and needTarget) then use.card = card end
	if not needTarget then
		if self.player:hasSkill("noswuyan") then return end
		if self.player:isLocked(card) then return end
		if #self.enemies == 1 and #(self:getChainedFriends()) <= 1 then return end
		if self:needBear() then return end
		--祖茂的引兵：如果手里只有一张铁索，这时最大效率化还是将其保留为好。
		if self.player:hasSkill("yinbing") and self.player:getPile("yinbing"):length() == 0 and self.player:getHandcardNum() - self:getCardsNum("BasicCard") == 1 and not self:isWeak() then return end
		if self:getOverflow() <= 0 and self.player:hasSkill("manjuan") then return end
		if self.player:hasSkill("wumou") and self.player:getMark("@wrath") < 7 then return end
		if self:getOverflow() > 1 and self.player:hasSkill("Lualeidian") then return end
	end
	local friendtargets, friendtargets2 = {}, {}
	local otherfriends = {}
	local enemytargets = {}
	local yangxiu = self.room:findPlayerBySkillName("danlao")
	local liuxie = self.room:findPlayerBySkillName("huangen")
	local komeiji = self.room:findPlayerBySkillName("luaqiangwei")
	local satori = self.room:findPlayerBySkillName("luahuiyi")
	self:sort(self.friends, "defense")
	local sp_rin = self.room:findPlayerBySkillName("luamaobu") 
	for _, friend in ipairs(self.friends) do
		if use.current_targets and table.contains(use.current_targets, friend:objectName()) then continue end
		if friend:isChained() and ((not self:isGoodChainPartner(friend)) or (friend:hasSkill("luaqiangwei") and friend:getMark("@qiangweiw") > 0 and self.player:objectName() ~= friend:objectName()))
				and self:hasTrickEffective(card, friend) and not friend:hasSkill("danlao")
				and not (satori and satori:isAlive() and not friend:isKongcheng() and satori:getMark("@huiyi") > 0) then
			if (not sp_rin) or (not self:isEnemy(sp_rin)) then 
				if friend:containsTrick("lightning") then
					table.insert(friendtargets, friend)
				elseif not f_has_shuxin then
					table.insert(friendtargets2, friend)
				end
			end 
		else			
			if sp_rin and self:isEnemy(sp_rin) and (#self.enemies < 3) and not friend:isChained() and not f_has_shuxin then
				table.insert(friendtargets2, friend)
			else
				table.insert(otherfriends, friend)
			end 
		end
	end
	table.insertTable(friendtargets, friendtargets2)
	if not (liuxie and self:isEnemy(liuxie)) then
		self:sort(self.enemies, "defense")
		for _, enemy in ipairs(self.enemies) do
			if (not use.current_targets or not table.contains(use.current_targets, enemy:objectName()))
				and not enemy:isChained() and not self.room:isProhibited(self.player, enemy, card) and not enemy:hasSkill("danlao")
				and not (enemy:hasSkill("luaqiangwei") and enemy:getHp() < 3)
				and self:hasTrickEffective(card, enemy) and not (self:objectiveLevel(enemy) <= 3)
				and not (self.player:hasSkill("luabenwo") and not enemy:inMyAttackRange(self.player)) 
				and not self:getDamagedEffects(enemy) and not self:needToLoseHp(enemy, nil, false, true)	--yun 
				and sgs.isGoodTarget(enemy, self.enemies, self) then
				table.insert(enemytargets, enemy)
			end
		end
	end

	local chainSelf = (not use.current_targets or not table.contains(use.current_targets, self.player:objectName()))
						and (self:needToLoseHp(self.player, nil, false, true) or self:getDamagedEffects(self.player))	--yun
						and not self.player:isChained()
						and not self.player:hasSkill("jueqing")
						and (self:getCardId("FireSlash") or self:getCardId("ThunderSlash") or
							(self:getCardId("Slash") and (self.player:hasWeapon("fan") or self.player:hasSkill("lihuo")))
						or (self:getCardId("FireAttack") and self.player:getHandcardNum() > 2))
						-- or (self.player:hasSkill("luaqiangwei") and self.player:getHp() < 3)
	if (self.player:hasSkill("LuaLeishi") and self.player:getMark("@yuanxing") > 0 and self.player:hasSkill("Luashenyi") and not self:isWeak())
		and (not use.current_targets or not table.contains(use.current_targets, self.player:objectName())) and not chainSelf then
		chainSelf = true
	end
	if (self.player:hasSkill("luahonghun") or (not self:isWeak() and self.player:getMark("@luachongsheng") < 2) and self.player:hasSkill("luachongsheng"))
			and (not use.current_targets or not table.contains(use.current_targets, self.player:objectName())) and not chainSelf then
		chainSelf = true
	end

	local targets_num = 2 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, self.player, card)
	if not self.player:hasSkill("noswuyan") then
		if #friendtargets > 1 then
			if use.to then
				for _, friend in ipairs(friendtargets) do
					use.to:append(friend)
					if use.to:length() == targets_num then return end
				end
			end
		elseif #friendtargets == 1 then
			if #enemytargets > 0 then
				if use.to then
					use.to:append(friendtargets[1])
					for _, enemy in ipairs(enemytargets) do
						use.to:append(enemy)
						if use.to:length() == targets_num then return end
					end
				end
			elseif chainSelf then
				if use.to then use.to:append(friendtargets[1]) end
				if use.to then use.to:append(self.player) end
			elseif liuxie and self:isFriend(liuxie) and liuxie:getHp() > 0 and #otherfriends > 0 then
				if use.to then
					use.to:append(friendtargets[1])
					for _, friend in ipairs(otherfriends) do
						use.to:append(friend)
						if use.to:length() == math.min(targets_num, liuxie:getHp() + 1) then return end
					end
				end
			elseif komeiji and self:isFriend(komeiji) and komeiji:getHp() < 3 then
				if use.to then
					use.to:append(friendtargets[1])
					if use.to then use.to:append(komeiji) end
				end				
			elseif yangxiu and self:isFriend(yangxiu) then
				if use.to then use.to:append(friendtargets[1]) end
				if use.to then use.to:append(yangxiu) end
			elseif use.current_targets then
				if use.to then use.to:append(friendtargets[1]) end
			end
		elseif #enemytargets > 1 then
			if use.to then
				for _, enemy in ipairs(enemytargets) do -- 勇仪 pay
					if enemy:getMark("@LuaBisha2") == 2 and self.player:hasSkill("LuaBisha") and not self.player:hasFlag("LuaBishaX") then
						use.to:append(enemy)
						if use.to:length() == targets_num then return end
					end 
				end		
				for _, enemy in ipairs(enemytargets) do
					if enemy:getMark("@LuaBisha2") == 1 and self.player:hasSkill("LuaBisha") and not self.player:hasFlag("LuaBishaX") then
						use.to:append(enemy)
						if use.to:length() == targets_num then return end
					end 
				end						
				for _, enemy in ipairs(enemytargets) do
					use.to:append(enemy)
					if use.to:length() == targets_num then return end
				end
			end
		elseif #enemytargets == 1 then
			if chainSelf then
				if use.to then use.to:append(enemytargets[1]) end
				if use.to then use.to:append(self.player) end
			elseif liuxie and self:isFriend(liuxie) and liuxie:getHp() > 0 and #otherfriends > 0 then
				if use.to then
					use.to:append(enemytargets[1])
					for _, friend in ipairs(otherfriends) do
						use.to:append(friend)
						if use.to:length() == math.min(targets_num, liuxie:getHp() + 1) then return end
					end
				end
			elseif komeiji and self:isFriend(komeiji) and komeiji:getHp() < 3 then
				if use.to then
					use.to:append(enemytargets[1])
					if use.to then use.to:append(komeiji) end
				end						
			elseif yangxiu and self:isFriend(yangxiu) then
				if use.to then use.to:append(enemytargets[1]) end
				if use.to then use.to:append(yangxiu) end
			elseif use.current_targets then
				if use.to then use.to:append(enemytargets[1]) end
			end
		elseif #friendtargets == 0 and #enemytargets == 0 then
			if use.to and liuxie and self:isFriend(liuxie) and liuxie:getHp() > 0
				and (#otherfriends > 1 or (use.current_targets and #otherfriends > 0)) then
				local current_target_length = use.current_targets and #use.current_targets or 0
				for _, friend in ipairs(otherfriends) do
					if use.to:length() + current_target_length == math.min(targets_num, liuxie:getHp()) then return end
					use.to:append(friend)
				end
			elseif use.current_targets then
				if yangxiu and not table.contains(use.current_targets, yangxiu:objectName()) and self:isFriend(yangxiu) then
					if use.to then use.to:append(yangxiu) end
				elseif liuxie and not table.contains(use.current_targets, liuxie:objectName()) and self:isFriend(liuxie) and liuxie:getHp() > 0 then
					if use.to then use.to:append(liuxie) end
				end
			end
		end
	end
	if use.to then assert(use.to:length() < targets_num + 1) end
	if needTarget and use.to and use.to:isEmpty() then use.card = nil end
end

sgs.ai_card_intention.IronChain = function(self, card, from, tos)
	if card and card:hasFlag("lualiuzhi") then return end
	
	local sp_rin = self.room:findPlayerBySkillName("luamaobu") 
	if sp_rin and self:isEnemy(from, sp_rin) then 
		for _, to in ipairs(tos) do
			local enemyc = 0
			for _, aplayer in sgs.qlist(self.room:getAlivePlayers()) do
				if self:isEnemy(to, aplayer) then enemyc = enemyc + 1 end 
			end 			
			if self:isEnemy(to, sp_rin) and (enemyc < 3) and not to:isChained() then 
				return	
			end 
		end 
	end 
	
	local liuxie = self.room:findPlayerBySkillName("huangen")
	for _, to in ipairs(tos) do
		if not to:isChained() then
			local enemy = true
			if to:hasSkill("danlao") and #tos > 1 then enemy = false end
			if to:hasSkill("luaqiangwei") then
				if to:getHp() < 3 then 
					sgs.updateIntention(from, to, -30)	
				end 				
			else
				if liuxie and liuxie:getHp() >= 1 and #tos > 1 and self:isFriend(to, liuxie) then enemy = false end
				sgs.updateIntention(from, to, enemy and 60 or -30)				
			end
		else
			sgs.updateIntention(from, to, -60)
		end
	end
end

sgs.ai_use_value.IronChain = 5.4
sgs.ai_keep_value.IronChain = 3.34
sgs.ai_use_priority.IronChain = 9.1

sgs.ai_skill_cardask["@fire-attack"] = function(self, data, pattern, target)
	local cards = sgs.QList2Table(self.player:getHandcards())
	local convert = { [".S"] = "spade", [".D"] = "diamond", [".H"] = "heart", [".C"] = "club"}
	local card
	
	if self:isFriend(target) and target:objectName() ~= self.player:objectName() then	--yun
		return "." 
	end	
	
	self:sortByUseValue(cards, true)
	local lord = self.room:getLord()
	if sgs.GetConfig("EnableHegemony", false) then lord = nil end

	for _, acard in ipairs(cards) do
		if acard:getSuitString() == convert[pattern] then
			if self.player:hasSkill("Luafengmi") then 
				local count1 = 0
				local count2 = 0
				local count3 = 0
				local count4 = 0				
				for _, bcard in ipairs(cards) do
					if bcard:getSuit() == sgs.Card_Heart then count1 = count1 + 1 end 
					if bcard:getSuit() == sgs.Card_Diamond then count2 = count2 + 1 end 
					if bcard:getSuit() == sgs.Card_Club then count3 = count3 + 1 end 
					if bcard:getSuit() == sgs.Card_Spade then count4 = count4 + 1  end 					
				end 
				if (acard:getSuit() == sgs.Card_Heart) and (not self.player:hasFlag("Luafengmi_Heart")) and (count1 == 1) and not acard:isKindOf("Jink") then return "." end 
				if (acard:getSuit() == sgs.Card_Diamond) and (not self.player:hasFlag("Luafengmi_Diamond")) and (count2 == 1) and not acard:isKindOf("Jink") then return "." end 
				if (acard:getSuit() == sgs.Card_Club) and (not self.player:hasFlag("Luafengmi_Club")) and (count3 == 1) and not acard:isKindOf("Jink") then return "." end 
				if (acard:getSuit() == sgs.Card_Spade) and (not self.player:hasFlag("Luafengmi_Spade")) and (count4 == 1) and not acard:isKindOf("Jink") then return "." end 			
			end 
			if not isCard("Peach", acard, self.player) then
				card = acard
				break
			else
				local needKeepPeach = true
				if (self:isWeak(target) and not self:isWeak()) or target:getHp() == 1
						or self:isGoodChainTarget(target) or target:hasArmorEffect("vine") or target:getMark("@gale") > 0 then
					needKeepPeach = false
				end
				if lord and not self:isEnemy(lord) and sgs.isLordInDanger() and self:getCardsNum("Peach") == 1 and self.player:aliveCount() > 2 then
					needKeepPeach = true
				end
				if not needKeepPeach then
					card = acard
					break
				end
			end
		end
	end
	return card and card:getId() or "."
end

function SmartAI:useCardFireAttack(fire_attack, use)
	if self.player:hasSkill("wuyan") and not self.player:hasSkill("jueqing") then return end
	if self.player:hasSkill("noswuyan") then return end
	if self.player:hasSkill("luabeihuan") then return end
	if self.player:hasSkill("luahuishen") then return end
	if self.player:hasSkill("luachenti") and self.player:getHandcardNum() == 3 and self:getOverflow() < 2 then return end
	if (self.player:hasSkill("luafenxing") and (self.player:getHandcardNum() <= 2) and (self:getOverflow() <= 0)) then return end
    if self.player:hasSkill("luatanmi") and (self:getOverflow() <= 0) and not self.player:hasFlag("luatanmi2") then return end
    local lack = {
		spade = true,
		club = true,
		heart = true,
		diamond = true,
	}

	local cards = self.player:getHandcards()
	local canDis = {}
	for _, card in sgs.qlist(cards) do
		if card:getEffectiveId() ~= fire_attack:getEffectiveId() then
			table.insert(canDis, card)
			lack[card:getSuitString()] = false
		end
	end

	if self.player:hasSkill("hongyan") then
		lack.spade = true
	end

	local suitnum = 0
	for suit,islack in pairs(lack) do
		if not islack then suitnum = suitnum + 1  end
	end


	self:sort(self.enemies, "defense")

	local can_attack = function(enemy, again)
		if self.player:hasFlag("FireAttackFailed_" .. enemy:objectName()) and not (self.player:hasSkill("LuaBisha") and enemy:getMark("@LuaBisha2") > 0) then
			return false
		end
		if enemy:hasSkill("qianxun") then return false end	--yun
		local damage = 1
		if not self.player:hasSkill("jueqing") and not enemy:hasArmorEffect("silver_lion") then
			if enemy:hasArmorEffect("vine") then damage = damage + 1 end
			if enemy:getMark("@gale") > 0 then damage = damage + 1 end
		end
		if not self.player:hasSkill("jueqing") and enemy:hasSkill("mingshi") and self.player:getEquips():length() <= enemy:getEquips():length() then
			damage = damage - 1
		end
		if (self.room:getCardPlace(fire_attack:getId()) == sgs.Player_PlaceSpecial) and self.player:getPileName(fire_attack:getId()) == "&zui" and again then
			if self:objectiveLevel(enemy) > 3 and not self.room:isProhibited(self.player, enemy, fire_attack) and not enemy:hasSkill("luahuoqiu") then
				return true
			end
		end
		return self:objectiveLevel(enemy) > 3 and damage > 0 and not enemy:isKongcheng() and not self.room:isProhibited(self.player, enemy, fire_attack) and not enemy:hasSkill("luahuoqiu")
				and self:damageIsEffective(enemy, sgs.DamageStruct_Fire, self.player) and not self:cantbeHurt(enemy, self.player, damage)
				and self:hasTrickEffective(fire_attack, enemy) 
				and ((self:AtomDamageCount2(enemy, self.player, sgs.DamageStruct_Fire, fire_attack) > 1) or (not self.player:hasSkill("luachuanming")) or (not self.player:hasSkill("luaqiuwen")))
				and sgs.isGoodTarget(enemy, self.enemies, self)
				and (self.player:hasSkill("jueqing")
					or (not (enemy:hasSkill("jianxiong") and not self:isWeak(enemy))
						and not (self:getDamagedEffects(enemy, self.player))
						and not ((enemy:isChained() or (self.player:hasSkill("luabenwo") and not enemy:inMyAttackRange(self.player) and not enemy:isChained())) and not self:isGoodChainTarget(enemy, self.player, sgs.DamageStruct_Fire, nil, fire_attack))))
	end

	local enemies, targets = {}, {}
	for _, enemy in ipairs(self.enemies) do
		if (not use.current_targets or not table.contains(use.current_targets, enemy:objectName())) and can_attack(enemy) then
			table.insert(enemies, enemy)
		end
	end
	if #enemies == 0 then
		for _, enemy in ipairs(self.enemies) do
			if (not use.current_targets or not table.contains(use.current_targets, enemy:objectName())) and can_attack(enemy, true) then
				table.insert(enemies, enemy)
			end
		end
	end
	local can_FireAttack_self
	for _, card in ipairs(canDis) do
		if (not isCard("Peach", card, self.player) or self:getCardsNum("Peach") >= 3)
			and (not isCard("Analeptic", card, self.player) or self:getCardsNum("Analeptic") >= 2) then
			can_FireAttack_self = true
		end
	end

	if (not use.current_targets or not table.contains(use.current_targets, self.player:objectName()))
		and self.role ~= "renegade" and can_FireAttack_self and self.player:isChained() and self:isGoodChainTarget(self.player, self.player, sgs.DamageStruct_Fire, nil, fire_attack)
		and (self.player:getHandcardNum() > 1 or self.player:hasSkill("luayanfeng")) and not self.player:hasSkill("jueqing") and not self.player:hasSkill("mingshi")
		and not self.room:isProhibited(self.player, self.player, fire_attack)
		and self:damageIsEffective(self.player, sgs.DamageStruct_Fire, self.player) and not self:cantbeHurt(self.player)
		and self:hasTrickEffective(fire_attack, self.player) then

		if self.player:hasSkill("niepan") and self.player:getMark("@nirvana") > 0 then
			table.insert(targets, self.player)
		elseif hasBuquEffect(self.player)then
			table.insert(targets, self.player)
		else
			local leastHP = 1
			if self.player:hasArmorEffect("vine") then leastHP = leastHP + 1 end
			if self.player:getMark("@gale") > 0 then leastHP =leastHP + 1 end
			local jxd = self.room:findPlayerBySkillName("wuling")
			if jxd and jxd:getMark("@wind") > 0 then leastHP = leastHP + 1 end
			if self.player:getHp() > leastHP and (self.player:hasSkill("luayanfeng") and self.player:isChained()) then
				table.insert(targets, self.player)
			elseif self:getCardsNum("Peach") + self:getCardsNum("Analeptic") > self.player:getHp() - leastHP then
				table.insert(targets, self.player)
			end
		end
	end

	local forbidden = {}
	
	for _, enemy in ipairs(enemies) do
		if (not use.current_targets or not table.contains(use.current_targets, enemy:objectName())) and enemy:getHandcardNum() == 1 then
			local handcards = sgs.QList2Table(enemy:getHandcards())
			local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
			if handcards[1]:hasFlag("visible") or handcards[1]:hasFlag(flag) then
				local suitstring = handcards[1]:getSuitString()
				if not lack[suitstring] and not self:PayContains(targets, enemy)then
					if not self:getDamagedEffects(enemy, self.player, false) then 
						table.insert(targets, enemy)
					else 
						table.insert(forbidden, enemy) 
					end
				end
			end
		end
	end

	if ((suitnum == 2 and lack.diamond == true) or suitnum <= 1) and not self.player:hasSkill("luayanfeng")
		and self:getOverflow() <= (self.player:hasSkills("jizhi|nosjizhi|luachaogan") and -2 or 0)
		and #targets == 0 then return end

	if ((suitnum == 3 and lack.diamond == true) or suitnum <= 2) and self:getCardsNum("Nullification") == 0
			and self.player:getHandcardNum() <= 4 and self.player:hasSkill("luayanfeng") and self.player:getHandcardNum() > 1 then return end

	for _, enemy in ipairs(enemies) do
		local damage = 1
		if not enemy:hasArmorEffect("silver_lion") then
			if enemy:hasArmorEffect("vine") then damage = damage + 1 end
			if enemy:getMark("@gale") > 0 then damage = damage + 1 end
		end
		if not self.player:hasSkill("jueqing") and enemy:hasSkill("mingshi") and self.player:getEquips():length() <= enemy:getEquips():length() then
			damage = damage - 1
		end
		if (not use.current_targets or not table.contains(use.current_targets, enemy:objectName()))
			and not self.player:hasSkill("jueqing") and self:damageIsEffective(enemy, sgs.DamageStruct_Fire, self.player) and damage > 1 then
			if not self:PayContains(targets, enemy) then
				if not self:getDamagedEffects(enemy, self.player, false) then 
					table.insert(targets, enemy)
				else 
					table.insert(forbidden, enemy) 
				end
			end
		end
	end
	for _, enemy in ipairs(enemies) do
		if (not use.current_targets or not table.contains(use.current_targets, enemy:objectName())) and not table.contains(targets, enemy) then 
			if not self:getDamagedEffects(enemy, self.player, false)
				and ((not self.player:hasSkill("lualushou")) or enemy:hasArmorEffect("vine") or enemy:isChained()) then
				table.insert(targets, enemy)
			else 
				table.insert(forbidden, enemy) 
			end
		end
	end

	if #targets == 0 and #forbidden > 0 then 
		if self:getOverflow() > 0 then 
			targets = forbidden 
		end 
	end
	
	if #targets > 0 then
		local godsalvation = self:getCard("GodSalvation")
		if godsalvation and godsalvation:getId() ~= fire_attack:getId() and self:willUseGodSalvation(godsalvation) then
			local use_gs = true
			for _, p in ipairs(targets) do
				if not p:isWounded() or not self:hasTrickEffective(godsalvation, p, self.player) then break end
				use_gs = false
			end
			if use_gs then
				use.card = godsalvation
				return
			end
		end

		local targets_num = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, self.player, fire_attack)
		if use.isDummy and use.extra_target then targets_num = targets_num + use.extra_target end
		local lx = self.room:findPlayerBySkillName("huangen")
		use.card = fire_attack
		for i = 1, #targets, 1 do
			if use.to and not (use.to:length() > 0 and targets[i]:hasSkill("danlao"))
				and not (use.to:length() > 0 and lx and self:isFriend(lx, targets[i]) and self:isEnemy(lx) and lx:getHp() > targets_num / 2) then
				use.to:append(targets[i])
				if use.to:length() == targets_num then return end
			end
		end
	end
end

sgs.ai_cardshow.fire_attack = function(self, requestor)
	local cards = sgs.QList2Table(self.player:getHandcards())
	if requestor:objectName() == self.player:objectName() then
		self:sortByUseValue(cards, true)
		return cards[1]
	end

	local priority = { heart = 4, spade = 3, club = 2, diamond = 1 }
	if requestor:hasSkill("hongyan") then priority = { spade = 10, club = 2, diamond = 1, heart = 0 } end
	local index = -1
	local result
	for _, card in ipairs(cards) do
		if priority[card:getSuitString()] > index then
			result = card
			index = priority[card:getSuitString()]
		end
	end

	return result
end

sgs.ai_use_value.FireAttack = 4.8
sgs.ai_keep_value.FireAttack = 3.3
sgs.ai_use_priority.FireAttack = sgs.ai_use_priority.Dismantlement + 0.1

sgs.dynamic_value.damage_card.FireAttack = true

sgs.ai_card_intention.FireAttack = function(self, card, from, tos)
	if card:hasFlag("lualiuzhi") then return end
	for _, to in ipairs(tos) do
		if to:hasFlag("jili") then continue end	--yun
		sgs.updateIntention(from, to, 80)
	end
end

sgs.dynamic_value.damage_card.FireAttack = true
