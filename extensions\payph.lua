module("extensions.payph",package.seeall)
extension = sgs.Package("payph")

payex = sgs.Package("payex", sgs.Package_CardPack)
payex2 = sgs.Package("payex2", sgs.Package_CardPack)

ceshiex = sgs.General(extension,"ceshiex","god",4,false,true,true)
wriggle = sgs.General(extension, "wriggle", "luaxing", 3, false, true, false)
clownpiece = sgs.General(extension, "clownpiece", "luacai", 3, false, false, false)
clownpieceA = sgs.General(extension, "clownpieceA", "luacai", 3, false, true, true)
clownpieceB = sgs.General(extension, "clownpieceB", "luacai", 3, false, true, true)
akyuu = sgs.General(extension, "akyuu", "luacai", 3, false, false, false)
akyuuA = sgs.General(extension, "akyuuA", "luacai", 3, false, true, true)
akyuuB = sgs.General(extension, "akyuuB", "luacai", 3, false, true, true)
akyuuC = sgs.General(extension, "akyuuC", "luacai", 3, false, true, true)
eternity = sgs.General(extension, "eternity", "luaxing", 3, false, false, false)
eternityA = sgs.General(extension, "eternityA", "luaxing", 3, false, true, true)
eternityB = sgs.General(extension, "eternityB", "luaxing", 3, false, true, true)
eternityC = sgs.General(extension, "eternityC", "god", 3, false, true, true)
koishi = sgs.General(extension,"koishi","luadi",3,false,false,false)
koishiA = sgs.General(extension,"koishiA","luadi",3,false,true,true)
koishiB = sgs.General(extension,"koishiB","luadi",3,false,true,true)
koishiC = sgs.General(extension,"koishiC","luadi",3,false,true,true)
chen = sgs.General(extension,"chen","luayao",4,false,false,false)
teimu = sgs.General(extension,"teimu$","luafeng",4,false,true,true)
kitcho = sgs.General(extension,"kitcho","luadi",3,false,false,false)
kitchoA = sgs.General(extension,"kitchoA","luadi",3,false,true,true)
kitchoB = sgs.General(extension,"kitchoB","luadi",3,false,true,true) 
satori = sgs.General(extension,"satori$","luadi",3,false,false,false)
satoriA = sgs.General(extension,"satoriA$","luadi",3,false,true,true)
satoriB = sgs.General(extension,"satoriB$","luadi",3,false,true,true)


local function isFriendQ(room, target, player)
	if player:getRole() == "rebel" then
		return target:getRole() == "rebel"
	end
	if player:getRole() == "loyalist" or player:getRole() == "lord" then
		return target:getRole() == "loyalist" or target:getRole() == "lord"
	end
end


luaxiehuiCard1 = sgs.CreateTrickCard{
	name = "xiehuia",
	class_name = "xiehuia",
	target_fixed = false,
	subclass = sgs.LuaTrickCard_TypeDelayedTrick, -- LuaTrickCard_TypeNormal, LuaTrickCard_TypeSingleTargetTrick, LuaTrickCard_TypeDelayedTrick, LuaTrickCard_TypeAOE, LuaTrickCard_TypeGlobalEffect
	filter = function(self, targets, to_select) 
		if #targets ~= 0 then return false end
		return true
	end,
	is_cancelable = function(self, effect)
		return false
	end,
}
luaxiehuiCard2 = sgs.CreateTrickCard{
	name = "xiehuib",
	class_name = "xiehuib",
	target_fixed = false,
	subclass = sgs.LuaTrickCard_TypeDelayedTrick, -- LuaTrickCard_TypeNormal, LuaTrickCard_TypeSingleTargetTrick, LuaTrickCard_TypeDelayedTrick, LuaTrickCard_TypeAOE, LuaTrickCard_TypeGlobalEffect
	filter = function(self, targets, to_select) 
		if #targets ~= 0 then return false end
		return true
	end,
	is_cancelable = function(self, effect)
		return false
	end,
}


longwen = sgs.CreateTrickCard{
	name = "longwen",
	class_name = "longwen",
	subtype = "delayed_trick",
	subclass = sgs.LuaTrickCard_TypeDelayedTrick,
	target_fixed = false,
	filter = function(self, targets, to_select)
		if #targets >=2 then return false end
		return true
	end,
	is_cancelable = function(self, effect)
		return true
	end,
	on_effect = function(self, effect)
		local target = effect.to
		local room = effect.to:getRoom()
		if target and target:isAlive() then
			local judge = sgs.JudgeStruct()
			judge.pattern = ".|heart"
			judge.good = true
			judge.reason = self:objectName()
			judge.who = target
			room:judge(judge)
			if not judge:isGood() then
				local function canLoseHp()
					for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
						if hecatiaX and isFriendQ(room, target, hecatiaX) and target:objectName() ~= hecatiaX:objectName()
								and target:getHp() == hecatiaX:getHp() then
							room:notifySkillInvoked(hecatiaX, "luayiti")
							return false
						end 
					end 
					for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
						if Erin and Erin:getKingdom() == target:getKingdom() then
							room:notifySkillInvoked(Erin, "luajiance")
							return false
						end 
					end 
					return true
				end 
				if canLoseHp() then room:loseHp(target, 1) end 
			end
		end

		local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, effect.to:objectName())
		room:moveCardTo(self, target, nil, sgs.Player_DiscardPile, reason, true)
	end
}

clearmind = sgs.CreateTrickCard{
	name = "clearmind",
	class_name = "clearmind",
	subtype = "single_target_trick",
    subclass = sgs.LuaTrickCard_TypeSingleTargetTrick,
	target_fixed = true, 
	is_cancelable = function(self, effect)
		return true
	end,
	on_use = function(self, room, source, targets) 
		if source:isWounded() then
			local choice = room:askForChoice(source, "clearmind", "recover+draw2")
			if choice == "recover" then 
				room:recover(source, sgs.RecoverStruct(source))
			else 
				source:drawCards(2, self:objectName())
			end
		else  
			source:drawCards(2, self:objectName())
		end 
	end
}

clearmind:setParent(payex2)
longwen:setParent(payex2)
--[[
luaxiehuiCard1:setParent(payex2)
luaxiehuiCard2:setParent(payex2)
]]--

quanxiang2 = sgs.CreateTriggerSkill{
	name = "#quanxiang" ,
	global = true,
	events = {sgs.Pindian},
	on_trigger = function(self, event, player, data)
		local pindian = data:toPindian()
		if pindian.reason == "quanxiang" then
			local room = player:getRoom()
			--room:writeToConsole("劝降测试")
			if pindian.from_card:getNumber() > pindian.to_card:getNumber() then				
				pindian.to:drawCards(1)
				pindian.to:turnOver()
			else
				if pindian.from:hasFlag("quanxiangS") then 
					pindian.from:obtainCard(pindian.from_card)
					pindian.from:turnOver()
					room:setPlayerFlag(pindian.from, "-quanxiangS") 					
				end 
				pindian.from:obtainCard(pindian.to_card)
			end
		end
		return false
	end,
	priority = -1
}
ceshiex:addSkill(quanxiang2)

zhongzhuangCard = sgs.CreateSkillCard{
	name = "zhongzhuang", 
	filter = function(self, targets, to_select)		
		return (#targets == 0)  
	end,
	on_effect = function(self, effect)
		effect.from:getRoom():damage(sgs.DamageStruct("zhongzhuang", effect.from, effect.to))
	end
}
zhongzhuang = sgs.CreateZeroCardViewAsSkill{
	name = "zhongzhuang",
	view_as = function(self, cards)
		return zhongzhuangCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:hasSkill("zhongzhuang") and (player:getEquips():length() > 4) and not player:hasUsed("#zhongzhuang")
	end,
}


ceshiex:addSkill(zhongzhuang)

sgs.Sanguosha:addPackage(payex)
sgs.Sanguosha:addPackage(payex2)
 
luayingdeng = sgs.CreateTriggerSkill{
	name = "luayingdeng",
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if not p:hasSkill("luayingdengr") and isFriendQ(room, p, p2) then room:attachSkillToPlayer(p, "luayingdengr") end
				end
			end
		end
	end
}

luachongqun = sgs.CreateTriggerSkill{
	name = "luachongqun",
	events = {sgs.CardUsed, sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.CardUsed then
			local use = data:toCardUse()
			if use.card and use.card:isKindOf("ExNihilo") then
				for i = 1,9 do
					room:detachSkillFromPlayer(player, "luachongqun2")
				end
			end
			return false
		else
			if player:getPhase() == sgs.Player_Start then
				room:acquireSkill(player, "luachongqun2", false)
			end
			return false
		end
	end
}

luachongqun3 = sgs.CreateTriggerSkill{
	name = "#luachongqun4", --必须
	global = true,
	frequency = sgs.Skill_Frequent,
	events = {sgs.TurnStart}, --技能触发时机,必须
	on_trigger = function(self, event, player, data) --必须
		local room = player:getRoom()
		for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
			if p2:getMark("chongqun") == 0 then
				room:acquireSkill(p2, "luachongqun2", false)
				room:setPlayerMark(p2, "chongqun", 1)
			end
		end
	end

}
wriggle:addSkill(luachongqun)
wriggle:addSkill(luayingdeng)


function Ru(str)
	if string.find(str, "analeptic") or string.find(str, "peach") then return "peach" end 
	if string.find(str, "slash") then return "slash" end 
	if string.find(str, "jink") then return "jink" end 
end 
luayuekuang = sgs.CreateTriggerSkill{
	name = "luayuekuang",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.HpRecover, sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		if event == sgs.EventPhaseStart then
			if player:getPhase() ~= sgs.Player_Finish then return false end 
		end 		 
		local room = player:getRoom()
		for _,p in sgs.qlist(room:getAlivePlayers()) do
			if p:getHp() <= 0 then return false end 
		end 
		local plist = sgs.SPlayerList()	
		for _,p in sgs.qlist(room:getAlivePlayers()) do
			if p:getHandcardNum() < player:getHp() then
				plist:append(p)
			end 
		end
		local tp = room:askForPlayerChosen(player, plist, "luayuekuang", "@luayuekuang", true, true)
		if tp then
			local x = player:getHp() - tp:getHandcardNum()
			tp:drawCards(x)
			if not room:askForUseCard(tp, "slash", "@askforslash") then 
				local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				room:useCard(sgs.CardUseStruct(dummy, tp, tp))
			end 
		end
	end
}

kuangxiangCard = sgs.CreateSkillCard{
	name = "luakuangxiang",
	filter = function(self, targets, to_select)
		return (#targets == 0)
	end,
	on_effect = function(self, effect)
		effect.to:drawCards(1)
		local room = effect.to:getRoom() 
		if not effect.to:isWounded() then room:setPlayerFlag(effect.from, "Global_PlayPhaseTerminated"); return false end
		local could = false
		for _,card in sgs.qlist(effect.to:getHandcards()) do
			for _,card2 in sgs.qlist(effect.to:getHandcards()) do
				if card2:getSuit() == card:getSuit() and card2:getId() ~= card:getId() then could = true end
			end
		end
		if not could then room:setPlayerFlag(effect.from, "Global_PlayPhaseTerminated"); return false end

		local huase2 = "|"
		for _,card in sgs.qlist(effect.to:getCards("h")) do
			for _,card2 in sgs.qlist(effect.to:getCards("h")) do
				if card2:getSuit() == card:getSuit() and card2:getId() ~= card:getId() then
					if not string.find(huase2, card:getSuitString()) then
						if huase2 ~= "|" then
							huase2 = huase2 .. "," .. card:getSuitString()
						else
							huase2 = huase2 .. card:getSuitString()
						end
					end
				end
			end
		end
		local huase3 = "." .. huase2
		local card1 = room:askForCard(effect.to, huase3, "@luakuangxiangA", sgs.QVariant(), sgs.Card_MethodNone)
		if card1 then
			room:setPlayerMark(effect.to, "kuangxiangx", card1:getId() + 1)
			local dummy = sgs.Sanguosha:cloneCard("jink")
			room:writeToConsole("^" .. card1:toString() .. "|" .. card1:getSuitString() .. "!")
			local card2 = room:askForCard(effect.to, "^" .. card1:toString() .. "|" .. card1:getSuitString() .. "!", "@luakuangxiangB", sgs.QVariant(), sgs.Card_MethodNone) --牛逼，极其牛逼
			room:setPlayerMark(effect.to, "kuangxiangx", 0)
			if (not card2) or (card1:getId() == card2:getId()) then
				room:setPlayerFlag(effect.from, "Global_PlayPhaseTerminated")
				room:writeToConsole("kuangxiang test failed")
				return false
			end
			dummy:addSubcard(card1)
			dummy:addSubcard(card2)
			room:throwCard(dummy, effect.to)
			room:recover(effect.to, sgs.RecoverStruct(effect.from, nil, 1))
		else
			room:setPlayerFlag(effect.from, "Global_PlayPhaseTerminated")
		end
	end
}
luakuangxiang = sgs.CreateZeroCardViewAsSkill{
	name = "luakuangxiang",
	view_as = function(self, cards)
		return kuangxiangCard:clone()
	end,
	enabled_at_play = function(self, player)
		return true
	end,
}


clownpiece:addSkill(luayuekuang)
clownpiece:addSkill(luakuangxiang)

clownpieceA:addSkill(luayuekuang)
clownpieceA:addSkill(luakuangxiang)

clownpieceB:addSkill(luayuekuang)
clownpieceB:addSkill(luakuangxiang)



qiuwen_list = {}
luaqiuwen = sgs.CreateTriggerSkill{
	name = "luaqiuwen",
	global = true,
	events = {sgs.EventPhaseChanging, sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive and (#qiuwen_list > 0) then
				local ids = sgs.IntList()
				local jink_table = {}
				for _, id in ipairs(qiuwen_list) do
					if room:getCardPlace(id) == sgs.Player_DiscardPile then							
						ids:append(id)						
						table.insert(jink_table, tostring(id))	
					end 
				end 
				if not ids:isEmpty() then 					
					for _, Akyuu in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
						local room_0 = Akyuu:getRoom()
						room_0:setTag("uaqiuwen", sgs.QVariant(table.concat(jink_table, "|")))
						local card_h = room_0:askForCard(Akyuu, "TrickCard,EquipCard|black", "@qiuwen", data, sgs.Card_MethodNone)	
						if card_h then room_0:setPlayerFlag(Akyuu, "luaqiuwen") end 						
						while card_h do
							local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, Akyuu:objectName(), nil, "luaqiuwen", nil)
							room_0:moveCardTo(card_h, Akyuu, nil, sgs.Player_DrawPile, reason, true)	
							local card_ids = room_0:getNCards(1)
							room_0:askForGuanxing(Akyuu, card_ids, sgs.Room_GuanxingDownOnly)
							room_0:fillAG(ids, Akyuu)
							if not ids:isEmpty() then
								local id = room_0:askForAG(Akyuu, ids, false, "luaqiuwen")
								local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
								dummy:addSubcard(id)
								Akyuu:obtainCard(dummy)
								if room:getCardOwner(id):objectName() == Akyuu:objectName() then
									local Carddata2 = sgs.QVariant() -- ai用
									Carddata2:setValue(sgs.Sanguosha:getCard(id))
									room_0:setTag("luaqiuwenTC", Carddata2)
									--room_0:writeToConsole("阿求牌测试 " .. sgs.Sanguosha:getCard(id):toString())
									local card = room_0:askForUseCard(Akyuu, sgs.Sanguosha:getCard(id):toString(), "@luaqiuwen", -1, sgs.Card_MethodUse, true)
									room_0:removeTag("luaqiuwenTC")
									if not card then room_0:clearAG();break end
								end
								ids:removeOne(id)
							end
							room_0:clearAG()
							jink_table = {}
							if ids:isEmpty() then break end 
							for _, id in sgs.qlist(ids) do
								table.insert(jink_table, tostring(id))	
							end 
							room_0:setTag("uaqiuwen", sgs.QVariant(table.concat(jink_table, "|")))
							card_h = room_0:askForCard(Akyuu, "TrickCard,EquipCard|black", "@qiuwen", data, sgs.Card_MethodNone)						
						end 
						room_0:setPlayerFlag(Akyuu, "-luaqiuwen")		
						room_0:removeTag("uaqiuwen")
					end 
				end 
				qiuwen_list = {}
			end 
		elseif event == sgs.CardsMoveOneTime then	
			local move = data:toMoveOneTime()
			if move.to_place == sgs.Player_DiscardPile then 
				local Akyuu = room:findPlayerBySkillName("luaqiuwen")
				if Akyuu and not Akyuu:hasFlag("luaqiuwen") then 
					for _, id in sgs.qlist(move.card_ids) do
						if not table.contains(qiuwen_list, id) then
							table.insert(qiuwen_list, id)
						end 
					end 
				end 
			end 
		end 
	end 	
}

jinxiCard = sgs.CreateSkillCard{
	name = "luajinxi", 
	will_throw = false,
	target_fixed = true, 
	handling_method = sgs.Card_MethodNone, 
	on_use = function(self, room, source, targets)
		if source:isAlive() then
			source:addToPile("yuanqi", self)
			source:drawCards(1)
		end		
	end 
}
luajinxi = sgs.CreateViewAsSkill{
	name = "luajinxi",
	n = 99,
	view_filter = function(self, selected, to_select)
		return to_select:getSuit() == sgs.Card_Diamond
	end,
	view_as = function(self, cards)
		if #cards == 0 then
			return nil
		else
			local skillcard = jinxiCard:clone()
			for _, c in ipairs(cards) do
				skillcard:addSubcard(c)
			end
			return skillcard			
		end 
	end, 
	enabled_at_play = function(self, player)
		return (player:getPile("yuanqi"):length() == 0) 
	end 
}

luajinxi2 = sgs.CreateTriggerSkill{
	name = "#luajinxi",
	events = {sgs.Dying},
	on_trigger = function(self, event, player, data)
		if event == sgs.Dying and player:hasSkill("luajinxi") then 
			local room = player:getRoom()
			local dying = data:toDying()
			local _player = dying.who		
			local y = player:getPile("yuanqi"):length() 
			if _player:objectName() == player:objectName() and (y > 0) and _player:faceUp()
				and room:askForSkillInvoke(player, "luajinxi") then 
				local yuanqi = player:getPile("yuanqi")
				local dummy = sgs.Sanguosha:cloneCard("jink")
				if not yuanqi:isEmpty() then
					dummy:addSubcards(yuanqi)	
					room:throwCard(dummy, player)
					local xp = y - player:getHp()
					room:recover(player, sgs.RecoverStruct(player, nil, xp))
				end		
				room:clearAG()
				player:turnOver()
			end 			
			return false 
		end 
	end 
}

luachuanming = sgs.CreateFilterSkill{
	name = "luachuanming",
	view_filter = function(self, to_select)
		return to_select:getSuit() == sgs.Card_Heart
	end,
	view_as = function(self, card)
		local id = card:getEffectiveId()
		local new_card = sgs.Sanguosha:getWrappedCard(id)
		new_card:setSkillName(self:objectName())
		new_card:setSuit(sgs.Card_Spade)
		new_card:setModified(true)
		return new_card
	end
}
luachuanming2 = sgs.CreateTriggerSkill{
	name = "#luachuanming" ,
	frequency = sgs.Skill_Compulsory ,
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		if change.to == sgs.Player_Discard and player:hasSkill("luachuanming") then player:skip(change.to) end
		return false
	end
}

akyuu:addSkill(luaqiuwen)
akyuu:addSkill(luajinxi)
akyuu:addSkill(luajinxi2)
akyuu:addSkill(luachuanming)
akyuu:addSkill(luachuanming2)

akyuuA:addSkill(luaqiuwen)
akyuuA:addSkill(luajinxi)
akyuuA:addSkill(luajinxi2)
akyuuA:addSkill(luachuanming)
akyuuA:addSkill(luachuanming2)

akyuuB:addSkill(luaqiuwen)
akyuuB:addSkill(luajinxi)
akyuuB:addSkill(luajinxi2)
akyuuB:addSkill(luachuanming)
akyuuB:addSkill(luachuanming2)

akyuuC:addSkill(luaqiuwen)
akyuuC:addSkill(luajinxi)
akyuuC:addSkill(luajinxi2)
akyuuC:addSkill(luachuanming)
akyuuC:addSkill(luachuanming2)

zhenchiCard = sgs.CreateSkillCard{
	name = "luazhenchi",

	will_throw = false,
	filter = function(self, targets, to_select)
		return (#targets == 0) and to_select:getMark("@luayi") > 0 and to_select:hasFlag("zhenchiX")
	end,
	on_effect = function(self, effect)

		local room = effect.to:getRoom()

		local move = sgs.CardsMoveStruct()
		move.from = effect.from
		move.from_place = sgs.Player_PlaceHand
		move.to = nil
		move.to_place = sgs.Player_DiscardPile
		move.card_ids = self:getSubcards()
		move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, effect.from:objectName())
		room:moveCardsAtomic(move, true)


		effect.to:drawCards(effect.to:getMark("@luayi"))
		effect.to:loseAllMarks("@luayi")
	end
}
zhenchiVS = sgs.CreateOneCardViewAsSkill{
	name = "luazhenchi",
	filter_pattern = ".",
	view_as = function(self, card)
		local skillcard = zhenchiCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self, player)
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luazhenchi"
	end
}

luazhenchi = sgs.CreateTriggerSkill{
	name = "luazhenchi",
	global = true,
	view_as_skill = zhenchiVS,
	frequency = sgs.Skill_Frequent,
	events = {sgs.BeforeCardsMove, sgs.TurnStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.BeforeCardsMove then
			local move = data:toMoveOneTime() 
			if (player:getPhase() ~= sgs.Player_NotActive) then
				return
			end

			if move.from and move.from:objectName() ~= player:objectName() then
				return
			end
			local eternity = room:findPlayerBySkillName("luazhenchi")
			if (not eternity) or (not eternity:isAlive()) then return false end
			local reason = move.reason.m_reason
			local reasonx = bit32.band(reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)
			local Yes = reasonx == sgs.CardMoveReason_S_REASON_DISCARD
			or reasonx == sgs.CardMoveReason_S_REASON_USE or reasonx == sgs.CardMoveReason_S_REASON_RESPONSE
			 
			if Yes then 
				local card
				local i = 0				
				for _,id in sgs.qlist(move.card_ids) do
					card = sgs.Sanguosha:getCard(id)
					if move.from_places:at(i) == sgs.Player_PlaceHand
						or move.from_places:at(i) == sgs.Player_PlaceEquip then
						if card and room:getCardOwner(id):getSeat() == player:getSeat() then
							player:gainMark("@luayi")
							break
						end
					end
					i = i + 1
				end
			end
		elseif event == sgs.TurnStart then
			if player:hasSkill("luazhenchi") and not player:isNude() then
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if p:getMark("@luayi") > 0 then
						room:setPlayerFlag(p, "zhenchiX")
						room:askForUseCard(player, "@@luazhenchi", "@luazhenchi")
						room:setPlayerFlag(p, "-zhenchiX")
					end
				end
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					p:loseAllMarks("@luayi")
				end
			end
		end
	end,
}

luahuadie = sgs.CreateTriggerSkill{
	name = "luahuadie",
	view_as_skill = luahuadieVS,
	global = true,
	frequency = sgs.Skill_Frequent,
	events = {sgs.BeforeCardsMove, sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:getTag("FirstRound"):toBool() then return end
		local move = data:toMoveOneTime()
		if not move.to then return false end
		if not move.from_places:contains(sgs.Player_DrawPile) then return false end 
		if move.to and move.to:objectName() ~= player:objectName() then
			return
		end
		if move.card_ids:length() <= 1 then return false end 
		if event == sgs.BeforeCardsMove then
			local card
			local i = 0				
			for _,id in sgs.qlist(move.card_ids) do
				card = sgs.Sanguosha:getCard(id)
				if move.from_places:at(i) == sgs.Player_DrawPile then
					if card then
						player:addMark("luahuadie")
					end
				end
				i = i + 1
			end
			if player:getMark("luahuadie") <= 1 then player:setMark("luahuadie", 0); return false end 
		else
			if player:getMark("luahuadie") > 0 then
				local list = player:getHandcards()
				if player:getEquips() then 
					for _,card in sgs.qlist(player:getEquips())do
						list:append(card)
					end 
				end 
				if list:length() > 1 then 
					local Eternitys = sgs.SPlayerList()
					for _, p in sgs.qlist(room:getAllPlayers()) do
						if p:hasSkill("luahuadie") and p:getHandcardNum() <= p:getHp() then
							Eternitys:append(p)							
						end
					end	
					if Eternitys:length() > 0 then 
						local card1 = room:askForCard(player, ".", "@luahuadie", data, sgs.Card_MethodNone)
						if card1 then 
							room:setPlayerMark(player, "luahuadiex", card1:getId() + 1)							
							local dummy = sgs.Sanguosha:cloneCard("jink")
							local card2 = room:askForCard(player, "^" .. card1:toString() .."!", "@luahuadie", data, sgs.Card_MethodNone) --牛逼，极其牛逼
							if card1:getId() == card2:getId() then room:writeToConsole("扑棱蛾子测试失败") end 
							dummy:addSubcard(card1)
							dummy:addSubcard(card2)
						
							local Eternity = room:askForPlayerChosen(player, Eternitys, self:objectName(), "@luahuadie")
							Eternity:obtainCard(dummy)
							if player:isWounded() then 
								room:recover(player, sgs.RecoverStruct(Eternity))
							end  
							room:setPlayerMark(player, "luahuadiex", 0)	
						end 	
					end 
				end 
				player:setMark("luahuadie", 0)
			end 
		end
	end,
}

 
eternity:addSkill(luazhenchi)
eternity:addSkill(luahuadie)

eternityA:addSkill(luazhenchi)
eternityA:addSkill(luahuadie)

eternityB:addSkill(luazhenchi)
eternityB:addSkill(luahuadie)


luacifuCard = sgs.CreateSkillCard{
    name = "luacifu",
    will_throw = false,
	handling_method = sgs.Card_MethodNone, 
	filter = function(self, targets, to_select)
		if to_select:objectName() == sgs.Self:objectName() then return false end
		return not to_select:isKongcheng() and #targets == 0
	end, 
    on_use = function(self, room, source, targets)
		if #targets == 0 then return end 
		local card = room:askForCard(targets[1], ".!", "@luacifuF", sgs.QVariant())
		local number = card:getNumber()
		local number2 = sgs.Sanguosha:getCard(self:getSubcards():at(0)):getNumber()
		targets[1]:obtainCard(self)
		source:obtainCard(card)
		if number > number2 then 
			targets[1]:turnOver()
		end 
	end
}
luacifuVS = sgs.CreateOneCardViewAsSkill{
	name = "luacifu",
	view_filter = function(self, card) 
    	return true
	end,
	view_as = function(self,card)
		local skillcard = luacifuCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function()
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luacifu"
	end
}
luacifu = sgs.CreateTriggerSkill{
	name = "luacifu" ,
	global = true , 
	priority = 2,
	view_as_skill = luacifuVS,
	events = {sgs.EventPhaseStart, sgs.TurnStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart then 
			if (player:getPhase() == sgs.Player_Play) and room:getCurrent():objectName() == player:objectName() and player:hasSkill("luacifu")
				and not player:isKongcheng() and room:askForUseCard(player, "@@luacifu", "@luacifu") then 
			end 
		elseif event == sgs.TurnStart then 
			local p = room:getLord()
			if p:getMark("luacifuu") == 0 then 
				room:setPlayerMark(p, "luacifuu", 1)
				for _, pe in sgs.qlist(room:findPlayersBySkillName("luacifu")) do
					if not pe:isKongcheng() then room:askForUseCard(pe, "@@luacifu", "@luacifu") end 
				end 
			end 
		end 
	end 
}


luachangye = sgs.CreateTriggerSkill{
	name = "luachangye",
	global = true,
	events = {sgs.TurnedOver, sgs.EventPhaseChanging} ,
	frequency = sgs.Skill_Compulsory, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local eternity = room:findPlayerBySkillName("luachangye")
		if event == sgs.TurnedOver then
			if room:getCurrent():objectName() == player:objectName() and player:getPhase() == 7 
				and eternity and eternity:isAlive() and player:getMark("luachangyepp") == 0 then
				room:setPlayerMark(player, "luachangyepp", 1)
				player:turnOver()
				room:setPlayerMark(player, "luachangyepp", 0)
			end 
		elseif event == sgs.EventPhaseChanging then 
			local change = data:toPhaseChange() 
			if eternity and eternity:isAlive() and change.from == sgs.Player_Play and room:getCurrent():objectName() == eternity:objectName()
				and room:getCurrent():objectName() == player:objectName() then
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if not p:faceUp() then
						local room2 = p:getRoom()
						p:drawCards(1)
						local playerdata = sgs.QVariant() -- ai用
						playerdata:setValue(p)
						room2:setTag("luajunying", playerdata)
						room2:removeTag("luajunying")
						local thread = room2:getThread()
						room2:setPlayerFlag(p, "luajunying")
						local old_phase = p:getPhase()
						p:setPhase(sgs.Player_Play)
						room2:broadcastProperty(p, "phase")
						if not thread:trigger(sgs.EventPhaseStart, room2, p) then
							thread:trigger(sgs.EventPhaseProceeding, room2, p)
						end
						thread:trigger(sgs.EventPhaseEnd, room2, p)
						p:setPhase(old_phase)
						room2:broadcastProperty(p, "phase")						
					end 
				end 
			end 
		end 
	end 
}

luazhuwuxx = sgs.CreateTriggerSkill{
	name = "luazhuwuxx", 
	events = sgs.DamageInflicted, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()		 
		if player:hasSkill("luazhuwuxx") and damage.to and damage.to:objectName() == player:objectName() then
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if not p:faceUp() then
					if p:isAlive() and room:askForSkillInvoke(p, "luazhuwuxxinvoke", data) then 
						p:turnOver() 
						if damage.card and damage.card:isKindOf("Slash") then
							player:removeQinggangTag(damage.card)
						end
						damage.to = p
						damage.transfer = true
						room:damage(damage)	
						return true
					end 
				end 
			end 
		end 
	end
}
eternityC:addSkill(luacifu)
eternityC:addSkill(luachangye)
eternityC:addSkill(luazhuwuxx)

luabenwo = sgs.CreateTriggerSkill{
	name = "luabenwo",
	events = {sgs.DamageCaused, sgs.TargetConfirmed, sgs.TrickCardCanceling},
	global = true,
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		if event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			local room = player:getRoom()	
			if use.to:length() == 1 then  
				local top = use.to:at(0)
				if use.from and use.from:objectName() == player:objectName() and player:hasSkill("luabenwo")
					and top:objectName() ~= player:objectName() then
					room:setCardFlag(use.card, "luabenwo")
					
					if not top:inMyAttackRange(use.from) then 
						if not top:isChained() then 
							room:setPlayerProperty(top, "chained", sgs.QVariant(true))
							room:setEmotion(top, "chain")
						else
							room:setPlayerProperty(top, "chained", sgs.QVariant(false))
							room:setEmotion(top, "chain")
						end		
					end 
				end 
			end 
			if use.from and player:objectName() == use.from:objectName() and use.card:isKindOf("Slash") and player:hasSkill("luabenwo") then 
				for _, t in sgs.qlist(use.to) do
					if not t:inMyAttackRange(use.from) then 
						local jink_table = sgs.QList2Table(player:getTag("Jink_" .. use.card:toString()):toIntList())
						local index = 1
						for _, p in sgs.qlist(use.to) do
							jink_table[index] = 0
							index = index + 1
						end
						local jink_data = sgs.QVariant()
						jink_data:setValue(Table2IntList(jink_table))
						player:setTag("Jink_" .. use.card:toString(), jink_data)					
					end 
				end 
			end 
		elseif event == sgs.TrickCardCanceling then
			local effect = data:toCardEffect()
			local room = player:getRoom()
			if effect.from and effect.from:hasSkill(self:objectName()) and effect.from:isAlive()
					and effect.to and not effect.to:inMyAttackRange(effect.from) then
				return true
			end
		elseif event == sgs.DamageCaused then
			local damage = data:toDamage()
			local room = player:getRoom()
			if damage.to and damage.to:isAlive() and player:getHandcardNum() == player:getHp() and damage.card and damage.card:hasFlag("luabenwo")
				and (not damage.chain) and (not damage.transfer)
				and player:objectName() == damage.from:objectName() and player:hasSkill("luabenwo") then
				damage.damage = damage.damage + 1
				data:setValue(damage)
				return false 
			end
		end 
	end,
	can_trigger = function(self, target)
		return target and target:isAlive()
	end
}
luaqiangwei2 = sgs.CreateMasochismSkill{
	name = "#luaqiangwei",
	on_damaged = function(self, player, damage)
		if not player:hasSkill("luaqiangwei") then return false end
		if damage.damage == 0 then return false end
		local room = player:getRoom()
		room:recover(player, sgs.RecoverStruct(player, nil, damage.damage))
		room:setPlayerMark(player, "@qiangweir", 1)
		room:setPlayerMark(player, "@qiangweiw", 0)
	end,
	can_trigger = function(self, target)
		return target and target:hasSkill("luaqiangwei") and target:getMark("@qiangweiw") > 0 
	end
}

luaqiangwei3 = sgs.CreateDistanceSkill{
	name = "#luaqiangwei2",
	correct_func = function(self, from, to)
		if (from:getMark("@qiangweiw") > 0) and from:hasSkill("luaqiangwei") then
			return -1
		end
		if (to:getMark("@qiangweiw") > 0) and to:hasSkill("luaqiangwei") then
			return 1
		end
	end,
}

qiangweiCard = sgs.CreateSkillCard{
	name = "luaqiangwei",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		if not source:isChained() then 
			room:setPlayerProperty(source, "chained", sgs.QVariant(true))
			room:setEmotion(source, "chain")
		else
			room:setPlayerProperty(source, "chained", sgs.QVariant(false))
			room:setEmotion(source, "chain")
		end			
		room:setPlayerMark(source, "@qiangweir", 0)
		room:setPlayerMark(source, "@qiangweiw", 1)		
	end 
}
luaqiangwei = sgs.CreateViewAsSkill{
	name = "luaqiangwei",
	n = 99,
	view_filter = function(self, selected, to_select)
		return #selected < sgs.Self:getHp()
	end, 
	view_as = function(self, cards)
		local y = sgs.Self:getHp() - 1
		local qiangwei = qiangweiCard:clone()
		if #cards == y then 
			for _, c in ipairs(cards) do
				qiangwei:addSubcard(c)
			end
			return qiangwei
		end 
	end,
	enabled_at_play = function(self, player)
		return (player:getMark("@qiangweiw") == 0) and player:hasSkill("luaqiangwei")
	end
}

koishi:addSkill(luabenwo)
koishi:addSkill(luaqiangwei2)
koishi:addSkill(luaqiangwei3)
koishi:addSkill(luaqiangwei)

koishiA:addSkill(luabenwo)
koishiA:addSkill(luaqiangwei2)
koishiA:addSkill(luaqiangwei3)
koishiA:addSkill(luaqiangwei)

koishiB:addSkill(luabenwo)
koishiB:addSkill(luaqiangwei2)
koishiB:addSkill(luaqiangwei3)
koishiB:addSkill(luaqiangwei)

koishiC:addSkill(luabenwo)
koishiC:addSkill(luaqiangwei2)
koishiC:addSkill(luaqiangwei3)
koishiC:addSkill(luaqiangwei)


lualianpo = sgs.CreateTriggerSkill{
	name = "lualianpo" ,
	events = {sgs.EventPhaseChanging} ,
	--frequency = sgs.Skill_Frequent , 这句话源代码没有，但是我感觉应该加上，毕竟连破一点副作用都没有
	priority = 1,
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		if change.to ~= sgs.Player_NotActive then return false end
		local shensimayi = player:getRoom():findPlayerBySkillName("lualianpo")
		if (not shensimayi) or (shensimayi:getMark("lualianpo") <= 0) then return false end
		local n = shensimayi:getMark("lualianpo")
		shensimayi:setMark("lualianpo",0)
		if not shensimayi:askForSkillInvoke("lualianpo") then return false end
		local p = shensimayi
		local playerdata = sgs.QVariant()
		playerdata:setValue(p)
		player:getRoom():setTag("lualianpoInvoke", playerdata)
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end
}
ceshiex:addSkill(lualianpo)

luaqingnangCard = sgs.CreateSkillCard{
	name = "luaqingnang",
	target_fixed = false,
	will_throw = true,
	filter = function(self, targets, to_select)
		return (#targets == 0) and (to_select:isWounded())
	end,
	feasible = function(self, targets)
		if #targets == 1 then
			return targets[1]:isWounded()
		end
		return #targets == 0 and sgs.Self:isWounded()
	end,
	on_use = function(self, room, source, targets)
		local target = targets[1] or source
		local effect = sgs.CardEffectStruct()
		effect.card = self
		effect.from = source
		effect.to = target
		room:cardEffect(effect)
	end,
	on_effect = function(self, effect)
		local dest = effect.to
		local room = dest:getRoom()
		local recover = sgs.RecoverStruct()
		recover.card = self
		recover.who = effect.from
		room:recover(dest, recover)
	end
}
luaqingnang = sgs.CreateOneCardViewAsSkill{
	name = "luaqingnang", 
	filter_pattern = ".|.|.|hand!",
	view_as = function(self, card) 
		local qnc = luaqingnangCard:clone()
		qnc:addSubcard(card)
		qnc:setSkillName(self:objectName())
		return qnc
	end, 
	enabled_at_play = function(self, player)
		return player:canDiscard(player, "h") and not player:hasUsed("#luaqingnang") and not player:getArmor()
	end, 
}
ceshiex:addSkill(luaqingnang)
luahuoqiu = sgs.CreateTriggerSkill{
	name = "luahuoqiu",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardEffected},
	on_trigger = function(self, event, player, data)
		local effect = data:toCardEffect()
		if effect.card and (effect.card:isKindOf("AOE") or effect.card:isKindOf("FireAttack") or effect.card:isKindOf("Duel") or effect.card:isKindOf("yuzhi") or effect.card:isKindOf("Lightning")
			or effect.card:isKindOf("ThunderSlash") or effect.card:isKindOf("FireSlash")) and not player:getArmor() then
			return true
		end
	end
}
ceshiex:addSkill(luahuoqiu)
luayuzhi = sgs.CreateOneCardViewAsSkill{
	name = "luayuzhi", 
	filter_pattern = "Slash", 
	view_as = function(self, card) 
		local acard = sgs.Sanguosha:cloneCard("yuzhi", card:getSuit(), card:getNumber())
		acard:addSubcard(card:getId())
		acard:setSkillName(self:objectName())
		return acard
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("yuzhi") and not player:getArmor()
	end,
}
ceshiex:addSkill(luayuzhi)
luayongyeVS = sgs.CreateViewAsSkill{  --(#selected == 0)
	name = "luayongye",
	n = 1,
	view_filter = function(self, selected, to_select)
		for i = 1, 10 do
			local str = "luayongye" .. i
			if sgs.Self:getMark(str) == to_select:getEffectiveId() then 
				return true
			end 
		end 
		return false
	end,
	view_as = function(self, cards)
		if #cards ~= 1 then return nil end
		if cards[1]:isRed() then 
			local shortage = sgs.Sanguosha:cloneCard("indulgence",cards[1]:getSuit(),cards[1]:getNumber())
			shortage:setSkillName(self:objectName())
			shortage:addSubcard(cards[1])
			return shortage
		else
			local shortage = sgs.Sanguosha:cloneCard("supply_shortage",cards[1]:getSuit(),cards[1]:getNumber())
			shortage:setSkillName(self:objectName())
			shortage:addSubcard(cards[1])
			return shortage		
		end 
	end,
	enabled_at_play = function()
		return false
	end ,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luayongye"
	end
}
luayongye = sgs.CreateTriggerSkill{
	name = "luayongye",
	view_as_skill = luayongyeVS,
	global = true,
	frequency = sgs.Skill_Frequent,
	events = {sgs.BeforeCardsMove, sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:getTag("FirstRound"):toBool() then return end
		
		local move = data:toMoveOneTime()
		if not move.to then return end
		if move.to and move.to:objectName() ~= player:objectName() then
			return
		end
		if move.card_ids:length() <= 1 then return false end 
		if player:hasFlag("luayongyef") or not player:hasSkill("luayongye") or player:getHp() ~= 4 then return false end  
		if player:getArmor() then return false end 
		if event == sgs.BeforeCardsMove then 
			local reason = move.reason.m_reason
			local card
			local i = 0				
			for _,id in sgs.qlist(move.card_ids) do
				card = sgs.Sanguosha:getCard(id)
					if card then
						player:addMark("luayongye")
					end
				i = i + 1
			end
		else
			if player:getMark("luayongye") > 0 then 
				local nids = {}
				for _,id in sgs.qlist(move.card_ids) do
					card = sgs.Sanguosha:getCard(id)
					if ((room:getCardPlace(id) == sgs.Player_PlaceEquip) or (move.to_place == sgs.Player_PlaceHand)) 
						and room:getCardOwner(id) and room:getCardOwner(id):objectName() == player:objectName() then
						if card then
							table.insert(nids, tostring(id))
							for i = 1, 10 do
								local str = "luayongye" .. i
								if player:getMark(str) == 0 then 
									room:setPlayerMark(player, str, id)
									break
								end 
							end 
						end
					end
				end
				if #nids > 0 then 				
					if room:askForUseCard(player, "@@luayongye", "@luayongye") then
						room:setPlayerFlag(player, "luayongyef")
						player:drawCards(1)
					end 
					for i = 1, 10 do
						local str = "luayongye" .. i
						room:setPlayerMark(player, str, 0)
					end 					
				end 				
				player:setMark("luayongye", 0)
			end 
		end
	end 
}
luayongye2 = sgs.CreateTriggerSkill{
	name = "#luayongye" ,
	global = true,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom() 
		for _, toziko in sgs.qlist(room:findPlayersBySkillName("shenen")) do 
			if toziko and toziko:hasFlag("luayongyef") then 
				room:setPlayerFlag(toziko, "-luayongyef") 
			end 	
		end 
		return false
	end , 
	priority = 1
}
ceshiex:addSkill(luayongye)
ceshiex:addSkill(luayongye2)
lualongyu = sgs.CreateTriggerSkill{
	name = "lualongyu",
	events = {sgs.HpLost},
	can_trigger = function(self,target)
		return target ~= nil and target:hasSkill("lualongyu") and target:getHp() == 5
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local lose = data:toInt()
		for i = 0, lose - 1, 1 do
			player:drawCards(3)
		end 
		return false
	end
}
ceshiex:addSkill(lualongyu)

local function string2suit(suit)
	if suit == "spade" then
		return sgs.Card_Spade
	elseif suit == "heart" then
		return sgs.Card_Heart
	elseif suit == "club" then
		return sgs.Card_Club
	elseif suit == "diamond" then
		return sgs.Card_Diamond
	end
end
luajingjie3 = sgs.CreateFilterSkill{
	name = "luajingjie3",
	view_filter = function(self,to_select)
		local room = sgs.Sanguosha:currentRoom()
		local place = room:getCardPlace(to_select:getEffectiveId())
		return (place == sgs.Player_PlaceHand) and to_select:hasFlag("luajingjie")
	end,
	view_as = function(self, card)
		local as = {"1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13"}
		local bs = {"heart", "diamond", "club", "spade"}
		local cs = {"slash", "jink", "analeptic", "peach", "ofuda", "hui"}
		for _, a in ipairs(as) do
			for _, b in ipairs(bs) do
				for _, c in ipairs(cs) do
					if card:hasFlag("luajingjie|" .. a .. "|" .. b .. "|" .. c) then --居然有这么脑瘫的实现方式，我惊了
						local slash = sgs.Sanguosha:cloneCard(c, string2suit(b), tonumber(a))
						slash:setSkillName("luajingjie")
						local _card = sgs.Sanguosha:getWrappedCard(card:getId())
						_card:takeOver(slash)
						_card:setModified(true)
						return _card
					end
				end
			end
		end
	end
}

ceshiex:addSkill(luajingjie3)


luamaoyou = sgs.CreateTriggerSkill{
	name = "luamaoyou",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardUsed},
	on_trigger = function(self, event, player, data)
		if event == sgs.CardUsed then
			local use = data:toCardUse()
			if use.card:isKindOf("Slash") then
				player:gainMark("@luamaoyou")
				player:drawCards(1)
				return false
			end
		end
	end
}

luaguitu = sgs.CreateTriggerSkill{
	name = "luaguitu" ,
	frequency = sgs.Skill_Wake ,
	events = {sgs.MarkChanged} ,
	on_trigger = function(self, event, player, data)
		local mark = data:toMark().name
		local room = player:getRoom()
		if mark == "@luamaoyou" and player:getMark("@luamaoyou") >= 3 then
			room:setPlayerMark(player, "luaguitu", 1)
			if room:changeMaxHpForAwakenSkill(player) then
				player:drawCards(1)
				room:acquireSkill(player, "luashidan")
			end
		end
		return false
	end ,
	can_trigger = function(self, target)
		return (target and target:isAlive() and target:hasSkill(self:objectName()))
				and (target:getMark("luaguitu") == 0)
	end
}


luashidanCard = sgs.CreateSkillCard{
	name = "luashidan",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		source:loseMark("@luamaoyou")
		source:gainMark("@luashidan2")
	end
}
luashidan = sgs.CreateZeroCardViewAsSkill{
	name = "luashidan",
	view_as = function()
		return luashidanCard:clone()
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#luashidan") and player:getMark("@luamaoyou") > 0
	end
}
luashidan3 = sgs.CreateTriggerSkill{
	name = "#luashidan2" ,
	global = true,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Start then
			local toziko = player:getRoom():findPlayerBySkillName("luashidan")
			if toziko then
				toziko:loseAllMarks("@luashidan2")
			end
		end
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}
luashidan6 = sgs.CreateTriggerSkill{
	name = "#luashidan6",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.PreCardUsed} ,
	on_trigger = function(self, event, youmu, data)
		if event == sgs.PreCardUsed then
			local function YouMuCheck(card, target)
				if card:isKindOf("Hui") or card:isKindOf("Ofuda") then
					return true
				elseif card:isKindOf("FaithCollection") then
					return not target:isNude() 	
				elseif card:isKindOf("Banquet") then
					return not target:containsTrick("banquet")
				end
			end
			local use = data:toCardUse()
			local card = use.card
			local room = youmu:getRoom()
			if use.from:objectName() == youmu:objectName() and (use.card:isNDTrick() or use.card:isKindOf("BasicCard")) and use.from:hasSkill("luashidan")
					and use.from:getMark("@luashidan2") > 0 and use.from:getPhase() == sgs.Player_Play then
				if (sgs.Sanguosha:getCurrentCardUseReason() ~= sgs.CardUseStruct_CARD_USE_REASON_PLAY) then return false end
				local available_targets = sgs.SPlayerList()
				if (not use.card:isKindOf("AOE")) and (not use.card:isKindOf("GlobalEffect")) then
					for _, p in sgs.qlist(room:getAlivePlayers()) do
						if (use.to:contains(p) or room:isProhibited(youmu, p, use.card)) then continue end
						if (use.card:targetFixed()) then
							if (not use.card:isKindOf("Peach")) or (p:isWounded()) then
								available_targets:append(p)
							end
						else
							if (use.card:targetFilter(sgs.PlayerList(), p, youmu)  or YouMuCheck(use.card, p)) then
								available_targets:append(p)
							end
						end
					end
				end
				local extra
				if not use.card:isKindOf("Collateral") then

					local Carddata2 = sgs.QVariant() -- ai用
					Carddata2:setValue(use.card)
					room:setTag("luajianjiTC", Carddata2)

					extra = room:askForPlayerChosen(youmu, available_targets, "luajianjic", "luajianjic", true, true)
					room:removeTag("luajianjiTC")
					if extra then
						use.to:append(extra)
					end
				end
				room:sortByActionOrder(use.to)
				data:setValue(use)
				return false
			end
		end
		return false
	end
}

chen:addSkill(luamaoyou)
chen:addSkill(luaguitu)
chen:addSkill(luashidan6)
chen:addSkill(luashidan3)
ceshiex:addSkill(luashidan)


Table2IntList = function(theTable)
	local result = sgs.IntList()
	for i = 1, #theTable, 1 do
		result:append(theTable[i])
	end
	return result
end
luawushuang = sgs.CreateTriggerSkill{
	name = "luawushuang" ,
	frequency = sgs.Skill_Compulsory ,
	events = {sgs.TargetConfirmed,sgs.CardEffected } ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			local can_invoke = false
			if use.card:isKindOf("Slash") and (player and player:isAlive() and player:hasSkill(self:objectName())) and (use.from:objectName() == player:objectName()) then
				can_invoke = true
				local jink_table = sgs.QList2Table(player:getTag("Jink_" .. use.card:toString()):toIntList())
				for i = 0, use.to:length() - 1, 1 do
					if jink_table[i + 1] == 1 then
						jink_table[i + 1] = 2 --只要设置出两张闪就可以了，不用两次askForCard
					end
				end
				local jink_data = sgs.QVariant()
				jink_data:setValue(Table2IntList(jink_table))
				player:setTag("Jink_" .. use.card:toString(), jink_data)
			end
		elseif event == sgs.CardEffected then
			local effect = data:toCardEffect()
			if effect.card:isKindOf("Duel") then
				if effect.from and effect.from:isAlive() and effect.from:hasSkill(self:objectName()) then
					can_invoke = true
				end
				if effect.to and effect.to:isAlive() and effect.to:hasSkill(self:objectName()) then
					can_invoke = true
				end
			end
			if not can_invoke then return false end
			if effect.card:isKindOf("Duel") then
				if room:isCanceled(effect) then
					effect.to:setFlags("Global_NonSkillNullify")
					return true;
				end
				if effect.to:isAlive() then
					local second = effect.from
					local first = effect.to
					room:setEmotion(first, "duel");
					room:setEmotion(second, "duel")
					while true do
						if not first:isAlive() then
							break
						end
						local slash
						if second:hasSkill(self:objectName()) then
							slash = room:askForCard(first,"slash","@wushuang-slash-1:" .. second:objectName(),data,sgs.Card_MethodResponse, second);
							if slash == nil then
								break
							end

							slash = room:askForCard(first, "slash", "@wushuang-slash-2:" .. second:objectName(),data,sgs.Card_MethodResponse,second);
							if slash == nil then
								break
							end
						else
							slash = room:askForCard(first,"slash","duel-slash:" .. second:objectName(),data,sgs.Card_MethodResponse,second)
							if slash == nil then
								break
							end
						end
						local temp = first
						first = second
						second = temp
					end
					local daamgeSource = function() if second:isAlive() then return second else return nil end end
					local damage = sgs.DamageStruct(effect.card, daamgeSource() , first)
					if second:objectName() ~= effect.from:objectName() then
						damage.by_user = false;
					end
					room:damage(damage)
				end
				room:setTag("SkipGameRule",sgs.QVariant(true))
			end
		end
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end,
	priority = 1,
}
ceshiex:addSkill(luawushuang)
lualongwen = sgs.CreateTriggerSkill{
	name = "lualongwen",
	frequency = sgs.Skill_NotFrequent,
	global = true,
	events = {sgs.DamageInflicted},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		local card = damage.card

		if card and card:isKindOf("Slash") then
			local victim = damage.to
			if not victim:isDead() and damage.damage > 0 and not victim:hasSkill("lualongwen") then  --
				for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
					local Carddata2 = sgs.QVariant() -- ai用
					Carddata2:setValue(victim)
					p:setTag("lualongwen", Carddata2)
					if room:askForSkillInvoke(p, self:objectName(), data) then
						p:removeTag("lualongwen")
						damage.damage = damage.damage - 1
						local thc = sgs.Sanguosha:getCard(room:getDrawPile():first())
						local light = sgs.Sanguosha:cloneCard("longwen", thc:getSuit(), thc:getNumber())
						light:addSubcard(thc)

						local use_X = sgs.CardUseStruct()
						use_X.card = light
						use_X.from = p
						use_X.to:append(p)
						room:useCard(use_X, false)
						data:setValue(damage)
						--[[data:setValue(damage)
						if cardsX:length() == 1 then
							local ccc = sgs.Sanguosha:getCard(cardsX:at(0))
							local cardW = sgs.Sanguosha:cloneCard("indulgence", ccc:getSuit(), ccc:getNumber())
							cardW:addSubcard(ccc:getEffectiveId())
							cardW:setSkillName("lualongwen")
							room:useCard(sgs.CardUseStruct(cardW, p, p), false)
						end]]--
						room:writeToConsole("guijie ceshi")
					end
				end
			end
		end
		return false
	end 
}
luamoulue = sgs.CreateTriggerSkill{
	name = "luamoulue" ,
	events = {sgs.TargetConfirmed},
	frequency = sgs.Skill_Frequent,
	on_trigger = function(self, event, player, data, room)
		local use = data:toCardUse()
		if player:getHp() <= 0 then return false end
		if use.to:contains(player) and not use.card:isKindOf("EquipCard") and not player:hasFlag("luamoulue") and not use.card:isKindOf("SkillCard")
			and use.to:length() == 1 then
			if room:askForSkillInvoke(player, self:objectName()) then
				--room:writeToConsole("luamoulue ceshi " .. player:getGeneralName())
				room:setPlayerFlag(player, "luamoulue")
				local judge = sgs.JudgeStruct()
				judge.pattern = "."
				judge.good = true
				judge.reason = self:objectName()
				judge.who = player
				room:judge(judge)
				if judge.card:getSuit() == sgs.Card_Heart then
					local splist = sgs.SPlayerList()
					local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_Heart, 0)
					for _, Dplayer in sgs.qlist(room:getAlivePlayers()) do
						if not Dplayer:isCardLimited(slash, sgs.Card_MethodUse) then
							splist:append(Dplayer)
						end
					end
					if not splist:isEmpty() then
						local target1 = room:askForPlayerChosen(player, splist, "luamoulue2", "luamoulue2", false)
						if not target1 then return end
						local splist2 = sgs.SPlayerList()
						for _, Dplayer in sgs.qlist(room:getAlivePlayers()) do
							if not room:isProhibited(target1, Dplayer, slash) then
								splist2:append(Dplayer)
							end
						end
						if not splist2:isEmpty() then
							local target2 = room:askForPlayerChosen(player, splist2, "luamoulue3", "luamoulue3", false)
							if not target2 then return end
							slash:setSkillName("luamoulue")
							room:useCard(sgs.CardUseStruct(slash, target1, target2))
						end
					end
				elseif judge.card:getSuit() == sgs.Card_Spade then
					local targets = sgs.SPlayerList()
					for _,p in sgs.qlist(room:getAlivePlayers()) do
						if player:canDiscard(p, "hej") and p:objectName() ~= player:objectName() then
							targets:append(p)
						end
					end
					if not targets:isEmpty() then
						local to = room:askForPlayerChosen(player, targets, "luamoulue4", "luamoulue4", true, true)
						if to then
							local id = room:askForCardChosen(player, to, "hej", self:objectName(), false, sgs.Card_MethodDiscard)
							room:throwCard(id, to, player)
						end
					end
				end
			end
		end
		return false
	end 
}

luamoulue2 = sgs.CreateTriggerSkill{
	name = "#luamoulue" ,
	global = true,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local toziko = player:getRoom():findPlayerBySkillName("luamoulue")
		if toziko and toziko:hasFlag("luamoulue") and player:getPhase() == sgs.Player_Start and player:objectName() == room:getCurrent():objectName() then
			room:setPlayerFlag(toziko, "-luamoulue")
			--room:writeToConsole("回合结束")
		end
		return false
	end , 
	priority = 1
}

luaguijie = sgs.CreateTriggerSkill{
	name = "luaguijie",
	frequency = sgs.Skill_Frequent,
	events = {sgs.FinishJudge},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local judge = data:toJudge()
		local card = judge.card
		local card_data = sgs.QVariant()
		card_data:setValue(card)
		if card:isBlack() and room:getCardPlace(card:getEffectiveId()) == sgs.Player_PlaceJudge and player:askForSkillInvoke(self:objectName(), card_data) then
			player:obtainCard(card)
		end
		return false
	end 
}

kitcho:addSkill(luaguijie)
kitcho:addSkill(lualongwen)
kitcho:addSkill(luamoulue)
kitcho:addSkill(luamoulue2)

kitchoA:addSkill(luaguijie)
kitchoA:addSkill(lualongwen)
kitchoA:addSkill(luamoulue)
kitchoA:addSkill(luamoulue2)

kitchoB:addSkill(luaguijie)
kitchoB:addSkill(lualongwen)
kitchoB:addSkill(luamoulue)
kitchoB:addSkill(luamoulue2)


luasuideCard = sgs.CreateSkillCard{
	name = "luasuide",
	filter = function(self, targets, to_select)
		return (#targets == 0)
	end,
	on_effect = function(self, effect)
		effect.to:gainMark("@suide")
	end
}
luasuide = sgs.CreateOneCardViewAsSkill{
	name = "luasuide",
	filter_pattern = "Slash",
	view_as = function(self,card)
		local skillcard = luasuideCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self,player)
		for _, p in sgs.qlist(player:getAliveSiblings()) do
			if p:getMark("@suide") > 0 then return false end
		end
		return true
	end
}
luasuide2 = sgs.CreateTriggerSkill{
	name = "#luasuide",
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.Damaged},
	on_trigger = function(self, event, player, data, room)
		if player:getMark("@suide") > 0 then
			local damage = data:toDamage()
			if damage.damage == 0 then return false end
			if ((not damage.from) or (not damage.from:hasSkill("luasuide"))) and (player:objectName() == damage.to:objectName()) then
				player:loseAllMarks("@suide")
			end
		end
		return false
	end
}
luaweixinq = sgs.CreateTriggerSkill{
	name = "luaweixinq",
	global = true,
	events = {sgs.EventPhaseEnd},
	on_trigger = function(self, event, splayer, data, room)
		if splayer:getPhase() == sgs.Player_Draw and not splayer:isNude() then
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:getMark("@suide") > 0 then
					if splayer:canSlash(p, nil, false) and room:askForSkillInvoke(splayer, "luaweixinqw") then
						local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
						slash:setSkillName("luaweixinq")
						slash:addSubcard(room:getDrawPile():first())
						room:useCard(sgs.CardUseStruct(slash, splayer, p))
						room:askForDiscard(splayer, self:objectName(), 1, 1, false, true)
					end
				end
			end
		end
	end
}
luaanyuan = sgs.CreateFilterSkill{
	name = "luaanyuan",
	view_filter = function(self,to_select)
		return (to_select:isRed()) and to_select:objectName() == "slash"
	end,
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("fire_slash", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		local _card = sgs.Sanguosha:getWrappedCard(card:getId())
		_card:takeOver(slash)
		return _card
	end
}
luaanyuan2 = sgs.CreateTriggerSkill{
	name = "#luaanyuan" ,
	events = {sgs.TargetConfirmed},
	global = true,
	frequency = sgs.Skill_Frequent,
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		local room = player:getRoom()
		if use.from and (use.from:objectName() == player:objectName()) then
			if (use.card:isKindOf("Slash") and use.card:isRed()) then
				for _, p in sgs.qlist(room:findPlayersBySkillName("luaanyuan")) do
					if player:askForSkillInvoke("luaanyuan", data) then
						p:drawCards(1)
					end
				end
			end
		end
	end
}
tianhuangCard = sgs.CreateSkillCard{
	name = "luatianhuang",
	filter = function(self, targets, to_select)
		return true
	end,
	on_use = function(self, room, source, targets)
		for _,sp in sgs.list(targets) do
			sp:gainMark("@tianhuanga")
		end
	end
}
luatianhuangVS = sgs.CreateZeroCardViewAsSkill{
	name = "luatianhuang",
	response_pattern = "@@luatianhuang",
	view_as = function(self, cards)
		return tianhuangCard:clone()
	end
}
luatianhuang = sgs.CreateTriggerSkill{
	name = "luatianhuang$",
	global = true,
	view_as_skill = luatianhuangVS,
	events = {sgs.EventPhaseStart, sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				if p:hasLordSkill(self:objectName()) then
					local xo = room:getLord():getRoom():getTag("TurnLengthCount"):toInt() + 1
					if xo ~= p:getMark("tianhuangt") and room:askForUseCard(p, "@@luatianhuang", "@luatianhuang") then

					end
					room:setPlayerMark(p, "tianhuangt", xo)
				end
			end
		else
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				if room:getCurrent():objectName() == player:objectName() and player:getMark("@tianhuanga") then
					player:loseAllMarks("@tianhuanga")
				end
			end
		end
	end
}

teimu:addSkill(luasuide)
teimu:addSkill(luasuide2)
teimu:addSkill(luaweixinq)
teimu:addSkill(luaanyuan)
teimu:addSkill(luaanyuan2)
teimu:addSkill(luatianhuang)
 

luaduxin = sgs.CreateTriggerSkill{
	name = "luaduxin", --必须
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.TurnStart}, --技能触发时机,必须
	on_trigger = function(self, event, player, data) --必须
		local room = player:getRoom()
		if player:objectName() == room:getCurrent():objectName() then
			for _, satori in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				if player:getHp() == 1 or satori:distanceTo(player) == 1 and satori:objectName() ~= player:objectName() then
					player:drawCards(1)
					local range_list = sgs.IntList()
					for _, card in sgs.list(player:getHandcards()) do
						range_list:append(card:getEffectiveId())
						local flag = string.format("%s_%s_%s","visible", player:objectName(), satori:objectName())
						if not card:hasFlag("visible") then card:setFlags(flag) end
					end
					room:fillAG(range_list, satori)
					room:getThread():delay(2000)
					room:clearAG()
					if player:isKongcheng() then
						if not player:isChained() then
							room:setPlayerProperty(player, "chained", sgs.QVariant(true))
							room:setEmotion(player, "chain")
						else
							room:setPlayerProperty(player, "chained", sgs.QVariant(false))
							room:setEmotion(player, "chain")
						end
						return false
					end
					local _data = sgs.QVariant()
					_data:setValue(player)
					local bool1 = satori:askForSkillInvoke("luaduxin1", _data)
					if bool1 then
						local id2 = room:askForCardChosen(satori, player, "h", self:objectName(), true)
						room:obtainCard(satori, id2)
						if not satori:isChained() then
							room:setPlayerProperty(satori, "chained", sgs.QVariant(true))
							room:setEmotion(satori, "chain")
						else
							room:setPlayerProperty(satori, "chained", sgs.QVariant(false))
							room:setEmotion(satori, "chain")
						end
					end
					if not bool1 or satori:askForSkillInvoke("luaduxin2", _data) then
						if not player:isChained() then
							room:setPlayerProperty(player, "chained", sgs.QVariant(true))
							room:setEmotion(player, "chain")
						else
							room:setPlayerProperty(player, "chained", sgs.QVariant(false))
							room:setEmotion(player, "chain")
						end
					end
				end
			end
		end
	end
}

function qiangxingshiyong3(stars, targets, source, seija)
	local room = source:getRoom()
	if source:isCardLimited(stars, sgs.Card_MethodUse) then return false end
	local _targets = sgs.SPlayerList()
	for _, p in sgs.qlist(targets) do
		if not source:isProhibited(p, stars) then
			_targets:append(p)
		end
	end
	if (not _targets) or (_targets:length() == 0) then return false end
	if stars:isKindOf("Jink") or stars:isKindOf("Nullification") or stars:isKindOf("Collateral") or stars:isKindOf("sakura") then return false end  --借刀杀人我不想做了

	if stars:isKindOf("TrickCard") or stars:isKindOf("EquipCard") or stars:isKindOf("Analeptic") then
		local targets2 = sgs.SPlayerList()
		for _, _player in sgs.qlist(_targets) do
			local canuse = true
			if stars:isKindOf("DelayedTrick") then
				if _player:containsTrick(stars:objectName()) then
					canuse = false
				end
			end
			if canuse then
				targets2:append(_player)
			end
		end
		if targets2 and targets2:length() > 0 then
			local target = room:askForPlayerChosen(seija, targets2, "luafanze2")
			if stars:isKindOf("AOE") or stars:isKindOf("AmazingGrace") or stars:isKindOf("GodSalvation") then
				room:setPlayerFlag(source, "qucaiAOE")
				room:setPlayerFlag(target, "qucaiAOEs")
				stars:setSkillName("luahuiyi")
				room:useCard(sgs.CardUseStruct(stars, seija, sgs.SPlayerList()))
				room:setPlayerFlag(source, "-qucaiAOE")
				room:setPlayerFlag(target, "-qucaiAOEs")
				return true
			end
			stars:setSkillName("luahuiyi")
			room:useCard(sgs.CardUseStruct(stars, seija, target))
			return true
		end
	end
	if stars:isKindOf("Slash") then
		local targets2 = sgs.SPlayerList()
		for _, _player in sgs.qlist(_targets) do
			if source:canSlash(_player, stars) then
				targets2:append(_player)
			end
		end
		if targets2 and targets2:length()>0 then
			local target = room:askForPlayerChosen(seija, targets2, "luafanze2")
			if target then
				stars:setSkillName("luahuiyi")
				room:useCard(sgs.CardUseStruct(stars, seija, target))
				return true
			end
		end
		return false
	end
	if stars:isKindOf("Peach") then
		local targets2 = sgs.SPlayerList()
		for _, _player in sgs.qlist(_targets) do
			if _player:isWounded() then
				targets2:append(_player)
			end
		end
		if targets2 and targets2:length() > 0 then
			local target = room:askForPlayerChosen(seija, targets2, "luafanze2")
			if target then
				stars:setSkillName("luahuiyi")
				room:useCard(sgs.CardUseStruct(stars, seija, target))
				return true
			end
		end
		return false
	end
	return false
end
luahuiyicard = sgs.CreateSkillCard{
	name = "luahuiyi",
	target_fixed = true,
	on_use = function(self, room, source, targetS)
		for _,p in sgs.qlist(room:getAlivePlayers()) do
			if p:isChained() and not p:isKongcheng() then
				room:writeToConsole("huiyi test".. p:objectName())
				local _data = sgs.QVariant()
				_data:setValue(p)
				room:setTag("luafanzeTP", _data)
				local range_list = sgs.IntList()
				for _, card in sgs.list(p:getHandcards()) do
					range_list:append(card:getEffectiveId())
				end

				local plist = sgs.SPlayerList()
				plist:append(p)
				local olayer = room:askForPlayerChosen(source, plist, self:objectName(), "luahuiyi2", true, false)
				if olayer then
					room:fillAG(range_list, source)
					local id1 = room:askForAG(source, range_list, false, self:objectName()) --S_REASON_CHANGE_EQUIP
					room:clearAG()
					if id1 and id1 >= 0 then
						local cardX = sgs.Sanguosha:getCard(id1)
						local Carddata2 = sgs.QVariant() -- ai用
						Carddata2:setValue(cardX)
						room:setTag("luafanzeTC", Carddata2)
						local bool2 = qiangxingshiyong3(cardX, room:getAlivePlayers(), p, source)
						if not bool2 then room:damage(sgs.DamageStruct(self:objectName(), p, p)) end
						room:removeTag("luafanzeTC")
						room:removeTag("luafanzeTP")
					else
						room:damage(sgs.DamageStruct(self:objectName(), p, p))
					end
				else
					room:damage(sgs.DamageStruct(self:objectName(), p, p))
					room:clearAG()
				end
			elseif p:isKongcheng() then
				room:damage(sgs.DamageStruct(self:objectName(), p, p))
			end
		end
		source:loseAllMarks("@huiyi")
	end
}
luahuiyiVS = sgs.CreateViewAsSkill{
	name = "luahuiyi",
	n = 0,
	view_filter = function()
		return false
	end,
	view_as = function()
		return luahuiyicard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@huiyi") > 0
	end,
}
luahuiyi = sgs.CreateTriggerSkill{
	name = "luahuiyi",
	frequency = sgs.Skill_Limited,
	limit_mark = "@huiyi",
	view_as_skill = luahuiyiVS,
	events = {sgs.AfterDrawInitialCards},
	on_trigger = function(self, triggerEvent, cirno, data)

	end,
	priority = -1
}
luashanyang = sgs.CreateTriggerSkill{
	name = "luashanyang$", --必须
	global = true,
	events = {sgs.TurnStart}, --技能触发时机,必须
	on_trigger = function(self, event, player, data) --必须
		local room = player:getRoom()
		if player:objectName() == room:getCurrent():objectName() then
			for _, satori in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				if satori:hasLordSkill("luashanyang") then
					if not player:isChained() and room:askForSkillInvoke(player, "luashanyang", data) then
						room:setPlayerProperty(player, "chained", sgs.QVariant(true))
						room:setEmotion(player, "chain")
						local pets = sgs.SPlayerList()
						for _, p in sgs.qlist(room:getAllPlayers()) do
							if player:distanceTo(p) <= 1 then
								pets:append(p)
							end
						end
						local target = room:askForPlayerChosen(player, pets, "luashanyang", "luashanyang", false)
						if target then
							room:setPlayerMark(target, "@shanyang", 1)
						end
					end
				end
			end
		end
	end
}
luashanyang2 = sgs.CreateDistanceSkill{
	name = "#luashanyang",
	correct_func = function(self, from, to)
		if from:hasLordSkill("luashanyang") and to:getMark("@shanyang") > 0 then
			return -999
		end
	end,
}
satori:addSkill(luaduxin)
satori:addSkill(luahuiyi)
satori:addSkill(luashanyang)


satoriA:addSkill(luaduxin)
satoriA:addSkill(luahuiyi)
satoriA:addSkill(luashanyang)


satoriB:addSkill(luaduxin)
satoriB:addSkill(luahuiyi)
satoriB:addSkill(luashanyang)



sgs.LoadTranslationTable{
	["payex"] = "幻梦华笺", --注意这里每次要加逗号
	["payph"] = "幻梦华笺", --注意这里每次要加逗号
	["bonus"] = "彩", 
	["sakura"] = "樱", 
	["sakurabonus"] = "樱", 
	[":sakura"] = "基本牌<br/>" ..
				"<b>时机：</b>你成为牌的唯一指定目标时（使用者不为你）<br/>"..
				"<b>目标：</b>你<br/>" .. 
				"<b>效果：</b>取消此牌所指定的目标。<br/>" .. 
				"<b><font color=\"orange\">悬赏：</font></b><font color=\"orange\">摸一张牌，并且手牌上限+1。</font><br/>" .. 
				"<p><i><font color=\"gray\">别乱想，不能取消技能卡（skillcard）指定的目标。</font></i></p>" ,
	[":bonus"] = "Spell Card Bonus!",
	["@askforsakura"] = "你可以使用一张【樱】使此牌对你无效。",
	
	
	["wriggle"] = "莉格露",	
	["#wriggle"]= "夜空萤灯",	
	["designer:wriggle"] = "Paysage",			
	["luayingdeng"] = "萤灯",	
	["luayingdengr"] = "萤灯",
	[":luayingdengr"] = "你可以选莉格露的一张手牌令其对你使用之，然后她回复一点体力。",
	[":luayingdeng"] = "友方角色于其回合内限一次，可以选择你的一张手牌对其使用之（若不能使用则弃置），然后你回复一点体力。",
	["luachongqun"] = "虫群",	
	["luachongqun2"] = "虫群",
	[":luachongqun"] = "锁定技，直到你上个回合结束前，若你未使用过【无中生有】，则你的手牌均视为【无中生有】。",

	["zhongzhuang"] = "重装",	
	[":zhongzhuang"] = "锁定技，若你装备区的装备数：大于0，你手牌上限+1；大于1，你于出牌阶段可以额外使用一张【杀】；大于2，摸牌阶段你额外摸1张牌；大于3，你体力上限+1；大于4，你可于出牌阶段对一名角色造成一点伤害；",
	
	["clownpiece"] = "克劳恩皮丝",	
	["clownpieceA"] = "克劳恩皮丝",
	["clownpieceB"] = "克劳恩皮丝",
	["#clownpiece"] = "地狱的妖精",
	["designer:clownpiece"] = "Paysage",	
	["luayuekuang"] = "月魇",
	[":luayuekuang"] = "结束阶段或你回复体力后，若无人濒死，你可以令一名角色将手牌补至X，然后其需使用一张【杀】，或视为对自己使用了一张【杀】（X为你体力值）。",
	["luakuangxiang"] = "狂想",	
	["@luakuangxiangA"] = "你可以弃两张同花色的牌回复一点体力，请选择第一张牌。",
	["@luakuangxiangB"] = "你可以弃两张同花色的牌回复一点体力，请选择第二张牌。",
	[":luakuangxiang"] = "出牌阶段，你可以令一名角色摸一张牌，然后其若已受伤，则须弃两张同花色的手牌回复一点体力，否则结束你的此阶段。",

	["@luayuekuang"] = "请选择“月魇”要指定的角色",
	["@luakuangxiang"] = "请选择“狂想”要指定的角色 。",
	["kxslash"] = "你可以使用一张【杀】，若不如此做，克劳恩皮丝将视为使用或打出了她当前所需的基本牌。",	

	
	["akyuu"] = "稗田阿求",	
	["akyuuA"] = "稗田阿求",
	["akyuuB"] = "稗田阿求",
	["akyuuC"] = "稗田阿求",
	["#akyuu"]= "幻想乡的记忆",
	["designer:akyuu"] = "Paysage",			
	["luaqiuwen"] = "求闻",	
	["@qiuwen"] = "请选择要置于牌堆底的黑飞机",	
	
	[":luaqiuwen"] = "一名角色的回合结束时，你可以将一张黑色非基本牌置于牌堆底，获得本回合置于弃牌堆的一张牌并使用之，然后你可以重复此流程。",	--
	["@luaqiuwen"] = "请使用你刚刚选上来的那张牌。",
	["luajinxi"] = "今昔",	
	[":luajinxi"] = "出牌阶段，若你没有“缘起”，你可以将X张方片牌牌置于你的武将牌上，称为“缘起”，并摸一张牌。当你处于濒死状态时，若你武将牌正面朝上，你弃置所有“缘起”并翻面，然后回复体力至X。",		
	["luachuanming"] = "舛命",	
	[":luachuanming"] = "锁定技，你的红桃牌均视为黑桃牌。你始终跳过弃牌阶段。",
	["yuanqi"] = "缘起",	
	
	["eternity"] = "爱塔妮缇拉尔瓦 ",	
	["eternityA"] = "爱塔妮缇拉尔瓦 ",
	["eternityB"] = "爱塔妮缇拉尔瓦 ",
	["#eternity"]= "蝴蝶妖精",
	["designer:eternity"] = "Paysage",			
	["@luahuadie"] = "你可以发动“化蝶”<br/> <b>操作提示</b>: 点击确定，把两张牌给爱塔妮缇拉尔瓦，回1滴血<br/>",	
	["@luazhenchi"] = "你可以弃置一张牌发动“振翅”",	
	["~luazhenchi"] = "选择一张牌→如果你不想对亮起的这名角色使用，请点击取消，会自动询问下一名角色。",
	["forbid_zhenchi"] = "是否对其关闭“振翅”提示",
	["forbid_zhenchi:yes"] = "是，永久关闭（不可逆操作）",
	["forbid_zhenchi:no"] = "不，谢谢",
	["forbid_zhenchi:maybe"] = "是，暂时关闭（直到其下回合结束）",
	["luazhenchi"] = "振翅",	
	[":luazhenchi"] = "一名角色于其回合外使用、打出或弃置牌后，其获得一个“翼”标记。回合开始，依座次对每名角色，你可以将一张牌置于弃牌堆，令其摸X张牌（X为其“翼”标记数）。之后你清除场上所有“翼”。",
	["luahuadie"] = "化蝶",	
	["luadie"] = "蝶",
	[":luahuadie"] = "一名角色摸一张以上的牌时，若你手牌数不大于体力值，其可以将两张牌交给你，并回复一点体力。",
	
	["eternityC"] = "爱塔妮缇拉尔瓦 ",
	["luacifu"] = "赐福",	 
	["@luacifuF"] = "爱塔妮缇拉尔瓦要求你和她交换一张手牌，请给出",	
	["@luacifu"] = "你可以发动“赐福”",	
	["~luacifu"] = "选择一张牌→选择一名角色。",
	[":luacifu"] = "游戏开始或出牌阶段开始时，你可以与一名其他角色互相交换一张手牌。若其给出的点数较大，其翻面。",
	["luachangye"] = "永眠",	
	[":luachangye"] = "锁定技，除技能以外，角色的武将牌不会翻面，那些角色于你出牌阶段结束时摸一张牌并执行一个额外的出牌阶段。", 
	["luazhuwuxx"] = "祝巫",	
	[":luazhuwuxx"] = "武将牌背面朝上的角色可以翻面转移你受到的一次伤害。", 
	["luazhuwuxxinvoke"] = "“祝巫”<br/> <b>提示</b>: 点击确定，翻面，替爱塔妮缇拉尔瓦承受本次伤害<br/>",
	
	
	["koishi"] = "古明地恋 ",	
	["koishiA"] = "古明地恋 ",	
	["koishiB"] = "古明地恋 ",	
	["koishiC"] = "古明地恋 ",	
	["#koishi"]= "紧闭的恋之瞳",	
	["designer:koishi"] = "Paysage",			
	["luabenwo"] = "本我",	
	[":luabenwo"] = "锁定技，你使用牌指定唯一其他角色为目标后，若你手牌数等于体力值，此牌伤害+1；若你不在其攻击范围内，其不能响应此牌并转置。",
	["luaqiangwei"] = "蔷薇",	
	[":luaqiangwei"] = "<b>转化技</b>，①：出牌阶段，你可以弃X-1张牌并转置（X为你体力值）；②：锁定技，你受到伤害后，回复等量体力。你计算的与其他角色的距离-1；其他角色计算与你的距离+1。",
	

	["luaqingnang"] = "青囊",	
	[":luaqingnang"] = "出牌阶段限一次，你可以弃置一张手牌并选择一名已受伤的角色，令该角色回复1点体力。",		
	["luahuoqiu"] = "火裘",	
	[":luahuoqiu"] = "锁定技，伤害类锦囊和属性【杀】对你无效。",
	["luayuzhi"] = "玉枝",	
	[":luayuzhi"] = "出牌阶段限一次，你可以将一张【杀】当作【弹幕】对一名其他角色使用。",
	["luayongye"] = "永夜",	
	[":luayongye"] = "每回合你首次获得牌时，你可以将其中的一张红色牌当【乐不思蜀】或一张黑色牌当【兵粮寸断】对一名角色使用，然后摸一张牌。",
	["@luayongye"] = "你可以发动“永夜”",
	["~luayongye"] = "选定获得牌中的一张，然后为此延时类锦囊指定一个目标",
	
	["lualongyu"] = "龙玉",	
	[":lualongyu"] = "锁定技，每当你流失1点体力后，你摸三张牌。",
	
	["chen"] = "橙",	
	["#chen"]= "凶兆的黑猫",	
	["designer:chen"] = "Paysage",			
	["luamaoyou"] = "猫又",	
	[":luamaoyou"] = "锁定技，你使用【杀】结算完毕后，你获得一个“式”标记并摸一张牌。",
	["luaguitu"] = "归途",	
	[":luaguitu"] = "觉醒技，若你拥有3枚或更多的“式”标记，你需减少一点体力上限，摸一张牌并获得“式弹”。\
		[<i><b>式弹</b>：出牌阶段限一次，你可以弃置一枚“式”标记，若如此做，本阶段你使用牌均可以额外指定一名角色成为此牌的目标。</i>]",
	["luashidan"] = "式弹",
	[":luashishen"] = "出牌阶段限一次，你可以弃置一枚“式”标记，若如此做，本阶段你使用牌均可以额外指定一名角色成为此牌的目标。",
	["@luashishen"] = "你可以发动“式弹”",
	["~luashishen"] = "选择两张牌→选择一名其他角色→点击确定。",

	["kitcho"] = "吉吊八千慧 ",
	["kitchoA"] = "吉吊八千慧 ",
	["kitchoB"] = "吉吊八千慧 ",
	["#kitcho"]= "运筹帷幄",
	["designer:kitcho"] = "Paysage",
	["lualongwen"] = "龙纹",
	["longwen"] = "龙纹",

	[":longwen"] = "锦囊牌<br/>" ..
			"<b>时机：</b>出牌阶段<br/>"..
			"<b>目标：</b>一名其他角色<br/>" ..
			"<b>效果：</b>目标角色判定，若结果不为♥，其流失一点体力。<br/>" ,
	
	["clearmind"] = "明镜止水",

	[":clearmind"] = "锦囊牌<br/>" ..
			"<b>时机：</b>出牌阶段<br/>"..
			"<b>目标：</b>你<br/>" ..
			"<b>效果：</b>回复一点体力或摸两张牌。<br/>" ,

	[":lualongwen"] = "一名其他角色受到【杀】造成的伤害时，你可以将牌堆顶的牌当作【龙纹】置于你的判定区，令此伤害-1。",
	["luamoulue"] = "谋略",
	[":luamoulue"] = "每回合限一次，若你未濒死且成为非装备牌唯一指定的目标时，你可以判定，若为红桃，你指定一名角色，视为其对另一名角色使用了一张红桃【杀】；若为黑桃，弃置一名其他角色区域里的一张牌。",
	["luaguijie"] = "鬼杰",
	[":luaguijie"] = "锁定技，你的黑色判定牌生效后，你获得之。",
	["luamoulue2"] = "请选择【杀】的使用者",
	["luamoulue3"] = "请选择【杀】的目标",
	["luamoulue4"] = "请选择要被你弃牌的角色",

	["teimu"] = "崇德院天梦 ",
	["#teimu"]= "祟德天皇",
	["designer:teimu"] = "Paysage",
	["luasuide"] = "祟德",
	[":luasuide"] = "转化技，①出牌阶段，你可以弃置一张【杀】，令一名角色获得一枚“祟”标记；②“祟”标记角色受到你以外角色造成的伤害后，其弃置该标记。",
	["luaweixinq"] = "威信",
	["luaweixinqw"] = "威信（视为对靶记角色用杀）",
	[":luaweixinq"] = "所有角色于其摸牌阶段结束时可以将牌堆顶的一张牌当作【杀】对“祟”角色使用，然后弃一张牌。",
	["luaanyuan"] = "安元",
	[":luaanyuan"] = "一名角色使用红【杀】指定目标后，可以令你摸一张牌，锁定技，你的红【杀】均视为火【杀】。",
	["luatianhuang"] = "天皇",
	[":luatianhuang"] = "主公技，每轮开始时，你可以选择至多X+1名角色，其于其回合内手牌上限+1，且可以额外使用一张【杀】（X为存活忠臣数）。",
	["@luatianhuang"] = "你可以发动“天皇”",
	["~luatianhuang"] = "选择任意队友→点击确定。",

	["draw2"] = "抽二",
 

	["satori"] = "古明地觉 ",
	["satoriA"] = "古明地觉 ",
	["satoriB"] = "古明地觉 ",
	["#satori"]= "地灵殿之主",
	["designer:satori"] = "Paysage、まりさ",
	["luaduxin"] = "读心",
	["luaduxin1"] = "获得其中一张牌并使你转置",
	["luaduxin2"] = "令该角色转置",
	[":luaduxin"] = "锁定技，你距离1以内，或是体力值为1的角色回合开始时，其摸一张牌，然后你观看其手牌并选择一至二项：1，获得其中一张牌并使你转置；2，其转置。",
	["luahuiyi"] = "回忆",
	["luahuiyi2"] = "操作提示：点选该角色→确定（若取消则直接造成伤害）→弹框，选择你要决定使用的牌→选择牌的目标。",
	[":luahuiyi"] = "限定技，依座次对所有横置角色，你观看其手牌，选其中一张对一名角色使用之。若未如此做，其对自身造成一点伤害。",
	["luashanyang"] = "缮养",
	[":luashanyang"] = "主公技，每名角色于其回合开始时可以横置并选择一名其距离1以内的角色，你与选择角色计算距离时始终为1。",
	

}



























