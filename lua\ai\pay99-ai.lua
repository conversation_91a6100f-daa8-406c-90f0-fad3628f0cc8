---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON><PERSON>)
--- Created by Administrator.
--- DateTime: 2019/11/14 21:59
---


--一号数据表示进攻，二号数据表示生存，三号强度，往后是配合
--定义：八意永琳与受兔的配合度为标准1
--冈崎梦美的进攻强度为标准10
--每回合稳定一伤为7.7
--每回合稳定一拆为6.9
--钟会的生存能力为标准10
--界曹操生存为7.8
--四血白板生存为6.9
--三血白板生存为5.5 --"medicine"
local xuanjiangA = {
  --["名字"]     = {进攻,生存，强度}
    ["shinki"]  = {5.5, 8.0, 8.0, "alice|2", "dengai|0.7", "marisa|0.8", "mima|1.1", "kanako|1.2", "lolice|1"},
    ["sanae"]   = {7.2, 6.3, 7.5, "suwako|1.2", "nitor<PERSON>|0.7", "nitori|0.7", "benben|1"},
    ["yuka"]    = {7.4, 6.8, 7.3, "nyasama|2", "yukari|1", "miyoi|1", "elliy|2", "daiyousei|1.5"},
    ["reisen"]  = {6.8, 8.2, 6.2, "erin|1", "daiyousei|2", "shinmyoumaru|0.8"},
    ["toyohime"]= {7.3, 7.2, 6.7, "koishi|1"},
    ["parsee"]  = {7.5, 6.7, 6.7, "akong|1", "sp_rin|1.5", "fujiwara|0.8", "yamame|0.8"},
    ["nyasama"] = {6.0, 7.7, 6.9, "marisa|0.8", "yuka|2", "rh_erin|1.3", "elliy|2", "hiziri|1.5", "nitori|1.2"},
    ["joon"]    = {7.5, 7.8, 7.4, "satsuki|1"},
    ["hatate"]  = {7.2, 8.0, 6.6, "whiterock|1", "xizhicai|0.8", "ran|1",}, --适合主位
    ["alice"]   = {7.7, 7.3, 7.4, "marisa|1", "shinki|1.2"},
["morino_hon"]  = {7.1, 6.8, 7.2, "daiyousei|1.2", "liubei|0.8", "minoriko|0.8"},
    ["paysage"] = {7.5, 6.9, 7.5, "daiyousei|1.1"},
    ["sp_rin"]  = {7.8, 8.7, 9.5, "shuxin_skill|1.3", "futo|0.2", "reimu|0.5", "jz_reimu|0.5", "medicine|1.2", "miyoi|1"},
    ["yamame"]  = {7.4, 6.5, 6.9, "shuxin_skill|0.7"},
    ["kanako"]  = {5.8, 8.5, 7.7, "suwako|0.9", "shinki|1.2", "ringoseiran|1.3", "rumia|-0.5", "medicine|-0.7", "sp_youmu|0.5", "hecatia|2"},
    ["minoriko"]= {5.0, 7.6, 7.4, "kanako|0.9", "fujiwara|1.2", "liuchen|1.5", "marisa|0.8", "mugetsu|1"},
    ["sumireko"]= {7.4, 7.8, 7.1},
    ["ran"]     = {6.9, 8.1, 7.9, "nyasama|1", "iku|2", "yukari|1", "sp_seiga|0.5", "kosuzu|0.5", "eternity|1.5"},
    ["marisa"]  = {8.4, 6.9, 7.4, "alice|1", "nyasama|0.8", "shinki|0.6", "minoriko|0.8", "daiyousei|1"},
    ["fujiwara"]= {7.6, 7.3, 7.4, "shuxin_skill|0.7", "keine|1.4", "minoriko|1.2", "mystia|1.2"},
    ["reimu"]   = {8.0, 7.0, 8.0, "aunn|0.7", "teimu|0.4", "sp_rin|0.5", "miyoi|1"},
    ["huashan"] = {7.4, 7.0, 6.9, "nitori|1.2", "daiyousei|1", "benben|1.2"},
    ["yugi"]    = {7.7, 7.0, 6.4, "daiyousei|0.6", "zhengzhen|0.8"},
    ["akong"]   = {7.8, 7.8, 7.1, "sp_rin|1.5", "fujiwara|0.2", "shuxin_skill|0.8", "satsuki|0.8"},
    ["mima"]    = {7.9, 7.0, 7.4, "daiyousei|1", "shinki|1.1", "nitori|0.6", "benben|1", "toyosatomimi|0.4", "aya|-1"},
    ["suika"]   = {8.0, 6.9, 7.3, "minoriko|1.2", "miyoi|1"},
    ["kaguya"]  = {7.5, 7.1, 7.3, "erin|1"},
["ringoseiran"] = {7.7, 8.3, 7.6, "kanako|1.3", "saki|1", "minamoto|1"},
["shinmyoumaru"]= {7.8, 6.7, 7.1, "minoriko|0.8", "eternity|0.8", "reisen|0.8"},
    ["seija"]   = {7.8, 7.4, 7.1, },
    ["mystia"]  = {6.9, 8.4, 8.1, "fujiwara|1.2", "grani|1.2", "eternity|1.5", "white|1.7", "erin|1.2", "wriggle|1.5", "kosuzu|1", "minamoto|0.7",
                   "akyuu|1", "daiyousei|1.2", "ellen|1.2", "medicine|0.7", "mayumi|1", "toyosatomimi|1.5"},
    ["tokiko"]  = {7.6, 7.4, 7.7, "mystia|0.8", "minoriko|0.8",},
 ["sekibanki"]  = {7.4, 6.9, 7.2, "mystia|0.8", "minoriko|0.8",},
    ["kosuzu"]  = {6.7, 7.9, 7.1, "akyuu|1", "keiki|0.6", "ran|0.5", "minamoto|0.7"},
    ["sp_aya"]  = {6.7, 6.9, 6.9, },
    ["kana"]    = {7.5, 7.7, 7.3, "fujix|0.6"},
    ["yumemi"]  = {10 , 6.1, 9.2, "daiyousei|1.5"},
   ["daiyousei"]= {5.5, 7.8, 8.2, "cirno|2", "active_cardneed_skill|0.8", "mystia|0.7", "white|0.8", "masochism|1","miyoi|1.2",
                   "kutaka|0.5", "mihana|1", "yumemi|1.5","iku|1","reisen|2", "yukimai|1", "yuka|1.5" },
   ["koakuma"]  = {5.5, 7.6, 7.4, "patchouli|2",},
    ["white"]   = {7.5, 7.1, 7.5, "daiyousei|0.8"},
    ["erin"]    = {7.4, 7.1, 7.3, "reisen|1", "kaguya|1", "grani|1"},
    ["keine"]   = {6.7, 7.9, 8.0, "fujiwara|1.2", "doremi|0.8", "daiyousei|0.6"},
    ["aunn"]    = {6.9, 7.6, 7.5, "reimu|1", "jz_reimu|1", "doremi|1.2", "remilia|0.8"},
    ["doremi"]  = {7.2, 7.7, 7.6, "aunn|1.2", "keine|0.8", "sagume|1",},
    ["nitori"]  = {7.0, 7.9, 7.7, "mima|0.6", "huashan|1.2", "sanae|0.7", "sakuya|0.7", "nyasama|1.2"},
    ["hina"]    = {7.5, 6.8, 7.3, },
    ["remilia"] = {7.7, 7.2, 7.4, "sakuya|0.8", "patchouli|1", "aunn|0.8"},
    ["yuyuko"]  = {7.8, 6.6, 7.6, },
    ["meiling"] = {7.6, 7.4, 7.5, "sakuya|1.2", "sp_meili|1", "eternity|0.8"},
  ["patchouli"] = {8.0, 6.3, 7.7, "koakuma|2", "sakuya|0.8"},
        ["iku"] = {7.9, 7.9, 7.7, "daiyousei|1", "toyosatomimi|0.5", "wakasagihime|2", "ran|2", "seiga|1.2", "miyoi|1", "clownpiece|1.2"},
     ["tenshi"] = {8.0, 6.9, 7.6, "sagume|0.7", "ryouko|1"},
     ["cirno"]  = {7.8, 7.6, 7.7,  "daiyousei|1.5"},
     ["rumia"]  = {8.1, 6.7, 7.8, },
  ["sp_youmu"]  = {7.6, 7.6, 7.6, "kanako|0.5"},
  ["whiterock"] = {7.5, 7.1, 7.1, },
        ["aya"] = {7.0, 6.9, 6.9, "teimu|0.8", "minamoto|1"}, 
       ["tewi"] = {7.3, 6.9, 7.1, },
     ["kokoro"] = {7.8, 6.8, 7.6, "daiyousei|0.6"},
     ["toziko"] = {7.5, 6.8, 7.5, "shuxin_skill|1", "toyosatomimi|0.8", "shikieiki|0.5", "saki|0.5"},
     ["tatara"] = {7.4, 7.4, 7.6, },
       ["futo"] = {8.0, 7.1, 7.8, "toziko|1", "satsuki|1", "sagume|1.0"},
       ["syou"] = {7.1, 6.9, 7.2, "hiziri|1"},
    ["yoshika"] = {7.2, 7.1, 7.2, "seiga|0.7"},
  ["sp_suwako"] = {7.3, 7.2, 7.4, "sanae|1.2", "kanako|0.9"},
     ["kyouko"] = {7.0, 7.7, 7.3, "minoriko|1"},
["toyosatomimi"]= {7.4, 7.3, 7.6, "toziko|0.8", "sangetsusei|1.2", "mima|0.4", "mystia|1.5", "iku|0.5", "saki|1"},
       ["seiga"]= {7.2, 7.4, 7.5, "yoshika|0.7", "mugetsu|1", "iku|1.2"},
      ["hiziri"]= {7.3, 6.7, 7.5, "syou|1", "nyasama|1.5"},
["mai_satono"]  = {7.4, 7.8, 7.4, "okina|0.4"},
     ["sakuya"] = {7.5, 6.8, 7.7, "patchouli|0.8", "remilia|0.8", "meiling|1.2", "nitori|0.7", "eternity|1.2", "sp_seiga|1.2"},
      ["grani"] = {7.0, 8.0, 7.6, "erin|1", "mystia|1.2", "kutaka|0.7"},
      ["skadi"] = {8.1, 6.8, 7.5, },
      ["okina"] = {7.2, 7.2, 7.1, "mai_satono|0.4"},
     ["mihana"] = {7.5, 7.9, 7.7, "daiyousei|1", "ellen|0.6", "kutaka|1.2"}, 
       ["hifu"] = {7.8, 6.7, 7.6, "daiyousei|1"},
 ["sangetsusei"]= {7.7, 7.1, 7.7, "toyosatomimi|1.2", "koishi|-1", "minamoto|-1"},
     ["wriggle"]= {6.8, 7.7, 7.4, "mystia|1.5"},
  ["clownpiece"]= {6.8, 8.1, 7.6, "eternity|0.7", "ellen|1.2", "iku|1.2", "hecatia|1.2"},
      ["akyuu"] = {7.2, 7.9, 7.7, "kosuzu|1", "eternity|1.5"},
   ["eternity"] = {7.0, 7.5, 7.5, "clownpiece|0.7", "shinmyoumaru|0.8", "akyuu|1.5", "sakuya|1.2", "ran|1.5", "meiling|0.8"},
     ["koishi"] = {8.1, 6.7, 7.7, },
      ["teimu"] = {7.2, 7.4, 7.4, "aya|0.8", "chenn|0.5", "reimu|0.4", "jz_reimu|0.4",},
     ["kitcho"] = {7.8, 6.6, 7.6, "chiyuri|0.8"},
    ["chiyuri"] = {7.9, 6.6, 7.5, "kitcho|0.8", "sagume|1",},
    ["satsuki"] = {7.5, 6.7, 7.6, "kutaka|1", "minamoto|0.7", "futo|1", "joon|1", "akong|0.8", "hecatia|1"},
   ["xizhicai"] = {7.7, 6.8, 7.9, "hatate|0.8"},
       ["lusu"] = {7.7, 6.9, 7.9, },
   ["medicine"] = {6.8, 7.8, 7.5, "mystia|0.7", "sp_rin|1.2"},
      ["chenn"] = {7.5, 7.2, 7.6, "teimu|0.5"},
      ["ellen"] = {6.7, 7.2, 7.6, "mihana|0.6", "mystia|1.2", "clownpiece|1.2"},
     ["kutaka"] = {7.6, 7.0, 7.5, "daiyousei|0.5", "grani|0.7", "mihana|1.2","toone|0.7", "satsuki|1", "keiki|0.7"},
      ["fujix"] = {7.2, 7.0, 7.5, "kana|0.6", "lolice|0.6"},
     ["lolice"] = {7.3, 7.7, 7.5, "fujix|0.6", "keiki|0.6", "shinki|1"},
    ["komachi"] = {7.7, 6.8, 7.6, "shikieiki|1.2"},
     ["benben"] = {6.9, 7.7, 7.7, "need_equip_skill|1", "shikieiki|1"},
    ["kagerou"] = {7.6, 6.6, 7.6, },
    ["rh_erin"] = {7.6, 7.8, 7.6, "mayumi|1", "nyasama|1.3"},
     ["mayumi"] = {7.6, 6.9, 7.7, "rh_erin|1", "mystia|1", "keiki|1", "sp_seiga|0.8"},
    ["ichirin"] = {7.6, 7.2, 7.6, },
    -- ["yukari"] = {7.6, 7.4, 7.7, "reimu|1", "yuka|1", "ran|1", },
   ["Rh_junko"] = {7.2, 7.6, 7.4, "hecatia|1"},
    ["shizuha"] = {7.1, 7.5, 7.6, },
   ["minamoto"] = {7.7, 7.7, 7.7, "aya|1", "mystia|0.7", "satsuki|0.7", "kosuzu|0.7", "ringoseiran|1"},
   ["jz_reimu"] = {8.7, 6.9, 7.6, "aunn|0.7", "teimu|0.4", "sp_rin|0.5", "miyoi|1"},
      ["keiki"] = {7.0, 8.0, 7.6, "mayumi|1", "kutaka|0.7", "daiyousei|1", "kosuzu|0.6", "lolice|0.6", "marisa|-1", "medicine|-0.5"},
      ["toone"] = {7.1, 7.6, 7.8, "kutaka|0.7"},
["wakasagihime"]= {7.7, 6.6, 7.9, "iku|2", "sagume|1",},
    ["sp_seiga"]= {7.0, 7.9, 8.0, "ran|0.5", "sakuya|1.2", "mayumi|0.8"},
    ["mugetsu"] = {7.8, 6.5, 7.7, "minoriko|1", "seiga|1",},
    ["sagume"]  = {6.9, 7.2, 7.6, "chiyuri|1", "doremi|1", "wakasagihime|1", "tenshi|0.7", "futo|1.0"},
    ["rrandom"] = {6.9, 6.9, 6.9, },
    ["miyoi"]   = {6.9, 7.8, 7.7, "daiyousei|1.2", "suika|1", "iku|1", "yuka|1", "sp_rin|1", "reimu|1", "jz_reimu|1",},
    ["ryouko"]  = {7.8, 7.0, 7.5, "tenshi|1"},
["prismriver"]  = {9.0, 6.8, 7.7, },
   ["yukimai"]  = {7.8, 7.0, 7.5, "daiyousei|1"},
["sarielelis"]  = {6.5, 8.1, 7.0, },
     ["elliy"]  = {7.6, 6.9, 7.6, "yuka|2", "nyasama|2"},
   ["hecatia"]  = {6.9, 7.8, 7.7, "clownpiece|1.2", "satsuki|1", "Rh_junko|1", "kanako|1"},
  ["sp_meili"]  = {7.5, 6.9, 7.5, "meiling|1"},
  ["xiandina"]  = {7.0, 7.0, 7.5, "suika|1", "iku|1"},
      ["saki"]  = {7.5, 7.1, 7.6, "toyosatomimi|1", "toziko|0.5", "ringoseiran|1"},
 ["rinnosuke"]  = {6.9, 7.2, 7.9, "need_equip_skill|1", "hatate|1", "benben|0.5", "nitori|0.5"},
     ["renko"]  = {7.5, 6.7, 7.4, },
     ["eika"]   = {7.5, 6.9, 7.4, },
   ["nazrin"]   = {7.5, 6.9, 7.4, },
   ["torisumi"] = {7.0, 7.5, 7.6, },
    ["tsukasa"] = {7.0, 7.5, 7.6, },
}
local function getNSeats(room, player)
    local lord = room:getLord()
    if player:objectName() == lord:objectName() then
        return 1
    elseif player:objectName() == lord:getNextAlive():objectName() then
        return 2
    elseif player:objectName() == lord:getNextAlive():getNextAlive():objectName() then
        return 3
    elseif player:objectName() == lord:getNextAlive():getNextAlive():getNextAlive():objectName() then
        return 4
    elseif player:objectName() == lord:getNextAlive():getNextAlive():getNextAlive():getNextAlive():objectName() then
        return 5
    else
        return 6
    end
end
local function peiheA(self, name0, TAB, listA, minus) -- 找到选将列表中可以和这个武将配合的那些武将，打包成列表并返回 listA是选将框里的武将
    if #TAB < 4 then return {}, {} end
    local listB = {}
    local listC = {}
    for i = 3, #TAB, 1 do
        if type(TAB[i]) == "string" then
            local peihe = TAB[i]:split("|")
            for _, name in ipairs(listA) do
				local general = sgs.Sanguosha:getGeneral(name)
                if name ~= name0 and general then
                    if name == peihe[1] then
                        if (tonumber(peihe[2]) > 0 and not minus) or (minus and tonumber(peihe[2]) < 0) then
                            table.insert(listB, name)
                            table.insert(listC, tonumber(peihe[2]))
                        end
                    elseif peihe[1] == "shuxin_skill" then
                        local general = sgs.Sanguosha:getGeneral(name)
                        local shuxin_skill2 = sgs.shuxin_skill:split("|")
                        for _, askill in sgs.qlist(general:getVisibleSkillList()) do
                            for _, name2 in ipairs(shuxin_skill2) do
                                if name2 == askill:objectName() then table.insert(listB, name);table.insert(listC, tonumber(peihe[2])) end
                            end
                        end
                    elseif peihe[1] == "active_cardneed_skill" then
                        local general = sgs.Sanguosha:getGeneral(name)
                        local active_cardneed_skill2 = sgs.Active_cardneed_skill:split("|")
                        for _, askill in sgs.qlist(general:getVisibleSkillList()) do
                            for _, name2 in ipairs(active_cardneed_skill2) do
                                if name2 == askill:objectName() then table.insert(listB, name);table.insert(listC, tonumber(peihe[2])) end
                            end
                        end
                    elseif peihe[1] == "need_equip_skill" then
                        local general = sgs.Sanguosha:getGeneral(name)
                        local need_equip_skill = sgs.need_equip_skill:split("|")
                        for _, askill in sgs.qlist(general:getVisibleSkillList()) do
                            for _, name2 in ipairs(need_equip_skill) do
                                if name2 == askill:objectName() then table.insert(listB, name);table.insert(listC, tonumber(peihe[2])) end
                            end
                        end
                    end
                end
            end
        end
    end
    if #listB > 0 then
        self.room:writeToConsole(name0 .. " peihe jiang ".. table.concat(listB, " | "))
    end
    return listB, listC
end
local function isFriendP(p1, p2)
    if p1:getRole() == "loyalist" or p1:getRole() == "lord" or p1:getRole() == "renegade" then
        return p2:getRole() == "loyalist" or p2:getRole() == "lord" or p2:getRole() == "renegade"
    end
    if p1:getRole() == "rebel" then
        return p2:getRole() == "rebel"
    end
end
local function AdjustValue(self, str1, listA, jieguo)
    local seat = getNSeats(self.room, self.player)
    if self.player:getGeneralName() == "sujiang" and not self.player:hasFlag("luaFive") then
        --self.room:writeToConsole("I'm white")
        if seat == 1 then
            if str1 == "sp_yuyuko" then return -100 end
            if str1 == "paysage" then jieguo = jieguo - 1 end
            if str1 == "mayumi" then jieguo = jieguo - 1 end
            if str1 == "keiki" then jieguo = jieguo + 1 end
            if str1 == "rh_flandre" then return -1 end
            if str1 == "yuyuko" then return -100 end
        end
		
		if self.room:getAlivePlayers():length() == 6 then 
			if seat == 6 then
				if str1 == "sp_yuyuko" then return -2 end
			end
			if str1 == "kosuzu" then
				if seat == 1 then jieguo = jieguo - 0.2 end
				if seat == 2 or seat == 5 then jieguo = jieguo - 0.3 end
				if seat == 4 or seat == 6 then jieguo = jieguo + 0.3 end
			end
		end 
        if (seat == 1 and str1 == "sp_suwako") or str1 == "xizhicai" or str1 == "sanae" then
            for _, p in sgs.qlist(self.room:getAlivePlayers()) do
                if p:hasSkill("luapanjue") then return -100 end
            end
        end
        if str1 == "mihana" then
            for _, p in sgs.qlist(self.room:getAlivePlayers()) do
                if p:hasSkill("luaguihuo") then return -2 end
            end
        end
    else -- ban掉克制自己或者队友的将领
        if str1 == "shikieiki" then
            local str2 = self.player:getGeneralName()
            if (seat == 1 and str2 == "sp_suwako") or str2 == "xizhicai" or str2 == "sanae" then
                return 100
            end
        end
        if str1 == "yuyuko" then
            local str2 = self.player:getGeneralName()
            if str2 == "keiki" then
                return 100
            end
        end
        if str1 == "sp_rin" then
            local str2 = self.player:getGeneralName()
            if str2 == "mihana" then
                jieguo = jieguo + 2
            end
        end
        if str1 == "medicine" or str1 == "marisa" then
            local str2 = self.player:getGeneralName()
            if str2 == "keiki" then
                jieguo = jieguo + 2
            end
        end
        if str1 == "medicine" or str1 == "rumia" then
            local str2 = self.player:getGeneralName()
            if str2 == "kanako" then
                jieguo = jieguo + 1
            end
        end
        if str1 == "aya" then
            local str2 = self.player:getGeneralName()
            if str2 == "mima" then
                jieguo = jieguo + 1
            end
        end
        if str1 == "rh_flandre" or str1 == "koishi" or str1 == "minamoto" then
            local str2 = self.player:getGeneralName()
            if str2 == "sangetsusei" then
                jieguo = jieguo + 2
            end
        end
    end
    return jieguo
end
local function caculaterValue(self, str1, table1, jieguo, xyz1, xyz2) -- 和场上的已经选择的角色考虑BP,如果没有找到的话，再从选将框里考虑
    --这一部分只考虑配合，不考虑克制
    local listZ = {} --Pick时，未选将，考虑优先配合自己人；Ban时，已选将，则和敌方有配合的价值增高
	if self.room:getAlivePlayers():length() == 6 then 
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if (isFriendP(p, self.player) and self.player:getGeneralName() == "sujiang" and not self.player:hasFlag("luaFive"))
				or (not isFriendP(p, self.player) and self.player:getGeneralName() ~= "sujiang") then
				table.insert(listZ, p:getGeneralName())
			end
		end
	else
		local lord = self.room:getLord()
		if (isFriendP(lord, self.player) and self.player:getGeneralName() == "sujiang" and not self.player:hasFlag("luaFive"))
			or (not isFriendP(lord, self.player) and self.player:getGeneralName() ~= "sujiang") then
			table.insert(listZ, lord:getGeneralName())
		end
	end 


	local listZ2 = {} --Pick时，未选将，和敌方有配合的价值增高，通过选将来让对方无法拿到此角色	
	if self.room:getAlivePlayers():length() == 6 then 
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if (not isFriendP(p, self.player) and self.player:getGeneralName() == "sujiang" and not self.player:hasFlag("luaFive")) then
				table.insert(listZ2, p:getGeneralName())
			end
		end
	else
		local lord = self.room:getLord()
		if (not isFriendP(lord, self.player) and self.player:getGeneralName() == "sujiang" and not self.player:hasFlag("luaFive")) then
			table.insert(listZ2, lord:getGeneralName())
		end
	end 	

	local listZ25 = {} --Pick时，未选将，已经被场上的角色压制的就不要选了
	if self.room:getAlivePlayers():length() == 6 then 
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if (not isFriendP(p, self.player) and self.player:getGeneralName() == "sujiang" and not self.player:hasFlag("luaFive")) then
				table.insert(listZ25, p:getGeneralName())
			end
		end
	else
		local lord = self.room:getLord()
		if (not isFriendP(lord, self.player) and self.player:getGeneralName() == "sujiang" and not self.player:hasFlag("luaFive")) then
			table.insert(listZ25, lord:getGeneralName())
		end
	end 

	local listZ3 = {} --Ban时，已选将，和队友有配合的价值降低，不要ban掉，留给队友
	if self.room:getAlivePlayers():length() == 6 then 
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if (isFriendP(p, self.player) and self.player:getGeneralName() ~= "sujiang") then
				table.insert(listZ3, p:getGeneralName())
			end
		end
	else
		local lord = self.room:getLord()
		if (isFriendP(lord, self.player) and self.player:getGeneralName() ~= "sujiang") then
			table.insert(listZ3, lord:getGeneralName())
		end
	end 

    if #listZ > 0 or #listZ2 > 0 or #listZ3 > 0 or #listZ25 > 0 then
        if #listZ > 0 then
            local xyz3, xyz4 = peiheA(self, str1, table1, listZ) --找能和场上队友配合的武将
            if #xyz3 > 0 then
                --if self.player:getGeneralName() ~= "sujiang" then self.room:writeToConsole("start to ban3") end
                for _, num in ipairs(xyz4) do
                    jieguo = jieguo + num
                end
            end
        end
        if #listZ2 > 0 then
            local xyz5, xyz6 = peiheA(self, str1, table1, listZ2) --从场上里找和对方配合的武将
            if #xyz5 > 0 then
                --if self.player:getGeneralName() ~= "sujiang" then self.room:writeToConsole("start to ban3") end
                for _, num in ipairs(xyz6) do
                    jieguo = jieguo + num*0.75
                end
            end
        end
        if #listZ3 > 0 then
            local xyz5, xyz6 = peiheA(self, str1, table1, listZ3) --从场上里找和对方配合的武将
            if #xyz5 > 0 then
                --if self.player:getGeneralName() ~= "sujiang" then self.room:writeToConsole("start to ban3") end
                for _, num in ipairs(xyz6) do
                    jieguo = jieguo - num*0.75
                end
            end
        end
        if #listZ25 > 0 then
            local xyz5, xyz6 = peiheA(self, str1, table1, listZ25, true) --从场上里找被克制的角色
            if #xyz5 > 0 then
                for _, num in ipairs(xyz6) do
                    jieguo = jieguo + num
                end
            end
        end
    else --从选将框里考虑
        if #xyz1 > 0 then
            local chengshu = #xyz2
            for _, num in ipairs(xyz2) do
                jieguo = jieguo + num*0.8/chengshu
            end
        end
    end
    return jieguo
end
--返回在位置weizhi下某一给定武将名str1的选将价值 listA是选将框里的武将
local function getJiangValue(self, str1, weizhi, listA, randomSeed, tiaoshi)
	local table1 = xuanjiangA[str1]
	if self.room:getAllPlayers(true):length() == 6 or self.room:getAllPlayers(true):length() == 4 then
		if type(table1) == "table" then
			local atk = table1[1]
			local def = table1[2]
			local qj  = table1[3]
			local xyz1, xyz2 = peiheA(self, str1, table1, listA) --从选将框里找配合的武将，第一个值是配合的武将列表，第二个值是其对应的配合值
			local jieguo = 0
			if weizhi == 1 then
				if self.room:getAllPlayers(true):length() == 6 then
					jieguo = atk*0.2 + def*0.4 + qj*0.4
				elseif self.room:getAllPlayers(true):length() == 4 then
					self.room:writeToConsole("This is a 2v2")
					jieguo = atk*0.3 + def*0.3 + qj*0.4
				end
				if #xyz1 > 0 then
					local chengshu = #xyz2
					for _, num in ipairs(xyz2) do
						jieguo = jieguo + num*0.8/chengshu
					end
				end
				jieguo = AdjustValue(self, str1, listA, jieguo) --调整一下，有的武将实在是不适合主位等，别乱选
			elseif weizhi == 2 then
				if self.room:getAllPlayers(true):length() == 6 then
					jieguo = atk*0.35 + def*0.25 + qj*0.4
				elseif self.room:getAllPlayers(true):length() == 4 then
					self.room:writeToConsole("This is a 2v2")
					jieguo = atk*0.3 + def*0.3 + qj*0.4
				end
				if self.player:getGeneralName() == "sujiang" and not self.player:hasFlag("luaFive") then
					if #xyz1 > 0 then
						local chengshu = #xyz2
						for _, num in ipairs(xyz2) do
							jieguo = jieguo + num*0.8/chengshu
						end
					end
				else   --2号位选好将了，这个时候他ban一个将
					jieguo = caculaterValue(self, str1, table1, jieguo, xyz1, xyz2)
				end
				jieguo = AdjustValue(self, str1, listA, jieguo) --调整一下，有的武将实在是不适合主位等，别乱选
			elseif weizhi >= 3 then
				if weizhi == 5 or weizhi == 4 then
					jieguo = atk*0.3 + def*0.25 + qj*0.45
				else
					jieguo = atk*0.25 + def*0.3 + qj*0.45
				end
				jieguo = caculaterValue(self, str1, table1, jieguo, xyz1, xyz2)
				jieguo = AdjustValue(self, str1, listA, jieguo) --调整一下，有的武将实在是不适合主位等，别乱选
			end
			if tiaoshi then
				self.room:writeToConsole(" seat is " .. weizhi .. " " .. str1 .. " value is ".. jieguo)
			end
			return jieguo
		else
			return randomSeed
		end
	else
        if type(table1) == "table" then
            local atk = table1[1]
            local def = table1[2]
            local qj  = table1[3]
            local xyz1, xyz2 = peiheA(self, str1, table1, listA) --从选将框里找配合的武将，第一个值是配合的武将列表，第二个值是其对应的配合值
            local jieguo = 0
            jieguo = atk*0.33 + def*0.33 + qj*0.33
            if self.player:getGeneralName() == "sujiang" and not self.player:hasFlag("luaFive") then
                if self.player:getRole() == "lord" then
                    jieguo = atk*0.2 + def*0.4 + qj*0.4
                elseif self.player:getRole() == "rebel" then
                    jieguo = atk*0.35 + def*0.25 + qj*0.4
                end
            end
            jieguo = caculaterValue(self, str1, table1, jieguo, xyz1, xyz2)
            jieguo = AdjustValue(self, str1, listA, jieguo) --调整一下，有的武将实在是不适合主位等，别乱选
            if tiaoshi then
                self.room:writeToConsole(" seat is " .. weizhi .. " " .. str1 .. " value is ".. jieguo)
            end
            return jieguo

        end
	end 
    return 0
end

sgs.ai_skill_choice.ChooseJiang = function(self, choices, data)
    local items = choices:split("+")
    self.room:writeToConsole("ChooseJiang " .. getNSeats(self.room, self.player))
	--self.room:writeToConsole("ChooseJiang " .. self.room:getAllPlayers(true))
    if #items == 0 then self.room:writeToConsole("ChooseJiang BUG!!") end
    local randomSeed = math.random()*math.random() + 6.6
    local compare_func = function(a, b)
        local v1 = getJiangValue(self, a, getNSeats(self.room, self.player), items, randomSeed, true)
        local v2 = getJiangValue(self, b, getNSeats(self.room, self.player), items, randomSeed)
        if v1 == v2 then return false end
        return v1 > v2
    end
    local ip = getJiangValue(self, items[1], getNSeats(self.room, self.player), items, randomSeed)
    if ip then
        table.sort(items, compare_func)
        ip = getJiangValue(self, items[1], getNSeats(self.room, self.player), items, randomSeed)
        self.room:writeToConsole("ChooseJiang " .. items[1])
        self.room:writeToConsole("the best choice for seat"  .. getNSeats(self.room, self.player) ..  " is " .. items[1] .. ip)
    end
    if self.player:getGeneralName() == "sujiang" and not self.player:hasFlag("luaFive") then
        if getNSeats(self.room, self.player) == 1 then
            if math.random() > 0.5 then
                if math.random() > 0.75 then
                    if math.random() > 0.9 then
                        local chat ={
                            "今天给你们展示一手我的秘藏 ",
                            "这局必玩我老婆 ",
                            "我直接秒选 "}
                        local index =1+ (os.time() % #chat)
                        self.player:speak(chat[index] .. sgs.Sanguosha:translate(items[4]))
                        return items[4]
                    end
                    local chat ={
                        "今天给你们展示一手我的秘藏 ",
                        "这局必玩我老婆 ",
                        "我直接秒选 "}
                    local index =1+ (os.time() % #chat)
                    self.player:speak(chat[index] .. sgs.Sanguosha:translate(items[3]))
                    return items[3]
                end
                return items[2]
            end
        end
        if getNSeats(self.room, self.player) == 2 then
            if math.random() > 0.75 then
                if math.random() > 0.5 then
                    return items[3]
                end
                return items[2]
            end
        end
    end
    self:updatePlayers(true, true)
    return items[1]
end

sgs.ai_skill_invoke.luafanji = function(self, data)
    local target = self.room:getCurrent()
    if self:isFriend(target) then
        return true
    else
        return false
    end
end


sgs.ai_skill_discard.luabijin = function(self, discard_num, min_num, optional, include_equip)	--yun
    if self:needToLoseHp() or self.player:getHandcardNum() < 1 then
        return "."
    end
    if not self:damageIsEffective(self.player, nil, nil) then return "." end
    self.room:writeToConsole("hifu bijin test")
    local fin_cards = sgs.QList2Table(self.player:getHandcards())
    self:sortByKeepValue(fin_cards)
    local function Check_F(card_0)
        if (card_0:isKindOf("Peach") or card_0:isKindOf("ExNihilo")) then
            return true
        end
        if not self:isWeak() and not self:willSkipPlayPhase() then
            if card_0:isKindOf("Duel") and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
                return true
            end
            if card_0:isKindOf("Indulgence") and not self.player:getHp() == 1 then
                for _, enemy in ipairs(self.enemies) do
                    if self:getIndulgenceValue(enemy) > 3 then return false end
                end
            end
        end
        if not card_0:isKindOf("Dismantlement") and self:ThreeCheck(card_0) then return true end
        if card_0:isKindOf("Treasure") then return true end
        local bool_3 = (card_0:isKindOf("EightDiagram") or card_0:isKindOf("RenwangShield") or card_0:isKindOf("Tengu"))
                and ((self.room:getCardPlace(card_0:getId()) == sgs.Player_PlaceEquip)
                or ((self.room:getCardPlace(card_0:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
        if bool_3 and not self.player:getHp() == 1 then return true end
        for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
            local callback = sgs.ai_cardneed[skill:objectName()]
            if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](self.player, card_0, self) then
                return true
            end
        end
        return false
    end
    local to_discard = {} -- 初始化 to_discard 为空表
    for _, card in sgs.list(self.player:getHandcards()) do
        if not Check_F(card) then table.insert(to_discard, fin_cards[1]:getEffectiveId()); break end
    end
    if #to_discard == 0 then return "." end
    return to_discard
end
sgs.ai_card_intention.luabijin = 50
local luageming_skill = {}
luageming_skill.name = "luageming"
table.insert(sgs.ai_skills, luageming_skill)
luageming_skill.getTurnUseCard = function(self)
    local y = 0
    for _, friend in ipairs(self.friends) do
        if friend:getMark("@luafanji1") > 0 then y = y + 1 end
    end
    if self.player:getMark("@geming") < 1 then return end
    local x = #self.friends + #self.enemies
    local z = self.room:getAlivePlayers():length() - 1
    if y >= #self.friends - 1 and x >= z then return sgs.Card_Parse("#luageming:.:") end
end

sgs.ai_skill_use_func["#luageming"] = function(card, use, self)
    use.card = sgs.Card_Parse("#luageming:.:")
    if use.to then
        for _, friend in ipairs(self.friends) do
            use.to:append(friend)
        end
    end
    return
end
sgs.ai_use_priority.luageming = 15

sgs.ai_choicemade_filter.skillInvoke.luafanji = function(self, player, promptlist)
    if promptlist[3] == "yes" then
        sgs.updateIntention(player, self.room:getCurrent(), -40)
    else
        sgs.updateIntention(player, self.room:getCurrent(), 10)
    end
end
sgs.ai_card_intention.luageming = -50

local luatanmi_skill = {}
luatanmi_skill.name = "luatanmi"
table.insert(sgs.ai_skills, luatanmi_skill)
luatanmi_skill.getTurnUseCard = function(self)
    --if not self.player:hasFlag("luatanmi2") then return false end
    if self.player:hasUsed("#luatanmi") then return end
    return sgs.Card_Parse("#luatanmi:.:")
end

sgs.ai_skill_use_func["#luatanmi"] = function(card, use, self)
    use.card = sgs.Card_Parse("#luatanmi:.:")
    if use.to then
        use.to = sgs.SPlayerList()
    end
    return
end
sgs.ai_skill_invoke.luatanmi = function(self, data)
    local has_E = 0
    local has_T = 0
    local has_B = 0
    local cards = self:getTurnUse(true)
    for _, card in ipairs(cards) do
        if card:isKindOf("BasicCard") then has_B = has_B + 1
        elseif card:isKindOf("TrickCard") then has_T = has_T + 1
        elseif card:isKindOf("EquipCard") then has_E = has_E + 1
        end
    end
    if has_B + has_T == 0 then return false end
    if has_B + has_E == 0 then return false end
    if has_B == 0 and self.player:getHandcardNum() < 2 then return false end
    return true
end
sgs.ai_use_priority.luatanmi = function(self)
    local has_E = false
    local has_T = false
    local has_B = false

    local cards = self:getTurnUse(true)
    for _, card in ipairs(cards) do
        if card:isKindOf("BasicCard") then has_B = true
        elseif card:isKindOf("TrickCard") then has_T = true
        elseif card:isKindOf("EquipCard") then has_E = true
        end
    end
    if has_B then
        if has_E and has_T then return 16 end
        if has_T and math.random() > 0.5 then return 16 end
        if has_B and math.random() > 0.6 then return 16 end
    end
    return 14
end

local function findBijinTarget(self)
    local enemies = self.enemies
    self:sort(enemies, "handcard")
    for _, enemy in ipairs(enemies) do
        if enemy:getHandcardNum() < 2 and self:damageIsEffective(enemy, nil, nil) then return enemy end
    end

    self:sort(enemies, "defense")
    for _, enemy in ipairs(enemies) do
        if self:damageIsEffective(enemy, nil, nil) then return enemy end
    end
end
local function useBijinCard(self, target)
    local cards_0 = self:getTurnUse(true)
    local allcards = sgs.QList2Table(self.player:getCards("he"))
    self:sortByUseValue(allcards, true)

    local uaqiuwen = self.player:getTag("luatanmi"):toString()
    uaqiuwen = uaqiuwen:split("|")

    local function pandin(card_0, type2)
        local count = 0
        for _, card in ipairs(cards_0) do
            if card:getTypeId() == card_0:getTypeId() then
                count = count + 1
            end
        end
        if type2 then
            for _, card in ipairs(cards_0) do
                if card:getEffectiveId() == card_0:getEffectiveId() and #uaqiuwen > 0 then
                    local i = 0
                    for _, cardP in ipairs(uaqiuwen) do
                        if card_0:getTypeId() == sgs.Sanguosha:getCard(cardP):getTypeId() then return false end
                        i = i + 1
                        if i > 3 then break end
                    end
                end
            end
            return true
        else
            if count <= 1 then return false end
            return true
        end
    end
    if self.player:getTag("luatanmi") and self.player:getTag("luatanmi"):toString() ~= "" then
        local function tannn(card_X)
            if target:getHandcardNum() < 2 and #uaqiuwen > 0 then
                for _, card in ipairs(cards_0) do
                    if card:getTypeId() == sgs.Sanguosha:getCard(uaqiuwen[1]):getTypeId() and card:getEffectiveId() ~= card_X:getEffectiveId() then
                        return true
                    end
                end
            end
            return false
        end
        for _, card in ipairs(allcards) do
            if (card:getSuit() == sgs.Card_Heart or card:getSuit() == sgs.Card_Spade) and pandin(card, true) then
                if tannn(card) or (self:isFriend(self.player:getNextAlive()) and self.player:getNextAlive()) then return card
                elseif not (card:isKindOf("Peach") or card:isKindOf("ExNihilo"))
                    and not (card:isKindOf("AOE") and self:getAoeValue(card) > 35) and not card:isKindOf("Duel") and not self.player:isCardLimited(card, sgs.Card_MethodUse) then
                    return card
                end
            end
        end
    else
        for _, card in ipairs(allcards) do
            if (card:getSuit() == sgs.Card_Heart or card:getSuit() == sgs.Card_Spade) and ((pandin(card) and self.player:hasFlag("luatanmi2"))
                or (not (card:isKindOf("Peach") or card:isKindOf("ExNihilo")) and not (card:isKindOf("AOE") and self:getAoeValue(card) > 35) and not card:isKindOf("Duel") and not self.player:isCardLimited(card, sgs.Card_MethodUse))) then
                return card
            end
        end
    end
end


local luabijin_skill = {}
luabijin_skill.name = "luabijin"
table.insert(sgs.ai_skills, luabijin_skill)
luabijin_skill.getTurnUseCard = function(self)
    if self.player:hasUsed("#luabijin") then return end
    if #self.enemies == 0  then return end
    return sgs.Card_Parse("#luabijin:.:")
end

sgs.ai_skill_use_func["#luabijin"] = function(card, use, self)
    local target = findBijinTarget(self)
    if not target then return end
    local card_0 = useBijinCard(self, target)
    if not card_0 then return end
    use.card = sgs.Card_Parse("#luabijin:".. card_0:getId() ..":")
    if use.to then use.to:append(target) end
    return
end
sgs.ai_skill_turnUse["luatanmi"] = function(self, turnUse)
    if self.player:getTag("luatanmi") and self.player:getTag("luatanmi"):toString() ~= "" and self.player:getPhase() == sgs.Player_Play then
        local bool_k = false
        for _, c2 in sgs.qlist(self.player:getHandcards()) do
            if c2:isKindOf("Slash") and self:slashIsAvailable(self.player, c2)  then bool_k = true end
        end

        if self.player:hasSkill("luatanmi") and self:getOverflow() <= 1 and not bool_k then
            for _, c in sgs.qlist(self.player:getHandcards()) do
                if c:isKindOf("Jink") then
                    local uaqiuwen = self.player:getTag("luatanmi"):toString()
                    uaqiuwen = uaqiuwen:split("|")
                    if #uaqiuwen > 0 then
                        if sgs.Sanguosha:getCard(uaqiuwen[1]):isKindOf("BasicCard") then
                            local turnUse_p = {}
                            for _, card in ipairs(turnUse) do
                                if card:isKindOf("SkillCard") then table.insert(turnUse_p, card) end
                            end
                            return turnUse_p
                        end
                    end
                end
                if c:isKindOf("Nullification") then
                    local uaqiuwen = self.player:getTag("luatanmi"):toString()
                    uaqiuwen = uaqiuwen:split("|")
                    if #uaqiuwen > 0 then
                        if sgs.Sanguosha:getCard(uaqiuwen[1]):isKindOf("TrickCard") then
                            local turnUse_p = {}
                            for _, card in ipairs(turnUse) do
                                if card:isKindOf("SkillCard") then table.insert(turnUse_p, card) end
                            end
                            return turnUse_p
                        end
                    end
                end
            end
        end
    end
end

sgs.ai_card_intention.luageming = 70
sgs.ai_use_priority.luabijin = 15.5


local luaxielislash_skill = {}
luaxielislash_skill.name = "luaxielislash"
table.insert(sgs.ai_skills, luaxielislash_skill)
luaxielislash_skill.getTurnUseCard = function(self)
    if not self:slashIsAvailable() then return end
    if self.player:hasUsed("#luaxielislash") then return end
    local sangetsusei = self.room:findPlayerBySkillName("luaxieli")
    if (not sangetsusei) or (sangetsusei:getMark("luaxieli") == 0) or (not self.player:canSlash(sangetsusei)) then return end

    return sgs.Card_Parse("#luaxielislash:.:")

end

sgs.ai_skill_use_func["#luaxielislash"]  = function(card, use, self)
    local sangetsusei = self.room:findPlayerBySkillName("luaxieli")
    if (not sangetsusei) or (sangetsusei:getMark("luaxieli") == 0) or (not self.player:canSlash(sangetsusei)) then return "." end
    local slash = sgs.Sanguosha:cloneCard("slash")

    if self:slashIsAvailable() and not self:slashIsEffective(slash, sangetsusei, self.player) and self:isFriend(sangetsusei) then
    else
        if self.player:hasSkill("luabenwo") and self.player:getHp() == self.player:getHandcardNum() then
            sgs.ai_use_priority.luaxielislash = 10
            use.card = card
            if use.to then use.to:append(sangetsusei) end
        end

        if self:getOverflow() > 0 then	--yun
            sgs.ai_use_priority.luaxielislash = 2.49
        else
            sgs.ai_use_priority.luaxielislash = 2.61
        end
        local dummy_use = { to = sgs.SPlayerList() }
        self:useCardSlash(slash, dummy_use)
        if dummy_use.card then
            self.room:writeToConsole("三月精测试K2")
            if (dummy_use.card:isKindOf("GodSalvation") or dummy_use.card:isKindOf("Analeptic") or dummy_use.card:isKindOf("Weapon"))
                    and self:getCardsNum("Slash") > 0 then
                use.card = dummy_use.card
                if use.to then use.to:append(sangetsusei) end
            else
                if dummy_use.card:isKindOf("Slash") and dummy_use.to:length() > 0 then
                    local lf
                    for _, p in sgs.qlist(dummy_use.to) do
                        if p:objectName() == sangetsusei:objectName() then
                            lf = true
                            break
                        end
                    end
                    if lf then
                        use.card = card
                        if use.to then use.to:append(sangetsusei) end
                    end
                end
            end
        end
    end
    if not use.card then
        sgs.ai_use_priority.luaxielislash = 2.0
        if self:slashIsAvailable() and self:isEnemy(sangetsusei)
                and not self:slashProhibit(slash, sangetsusei) and self:slashIsEffective(slash, sangetsusei) and sgs.isGoodTarget(sangetsusei, self.enemies, self) then
            use.card = card
            if use.to then use.to:append(sangetsusei) end
        end
    end
end

sgs.ai_use_priority.luaxielislash = 5.5


local function usetongxinC(self, cards, min)
    local handcard = sgs.QList2Table(self.player:getHandcards())
    local bool_k = false
    if (#self.enemies == 1) then
        local count = 0
        for  _, kplayer in sgs.qlist(self.room:getAlivePlayers()) do
            if self.enemies[1]:inMyAttackRange(kplayer) then count = count + 1 end
        end
        if count == 1 and self.enemies[1]:inMyAttackRange(self.player) then bool_k = true end
    end
    if self:isWeak() or (bool_k) then
        self:sortByKeepValue(handcard)
        return handcard[1]
    end
    local compare_func
    if min then
        compare_func = function(a, b)
            return a:getNumber() < b:getNumber()
        end
    else
        compare_func = function(a, b)
            return a:getNumber() > b:getNumber()
        end
    end
    table.sort(handcard, compare_func)

    for _, card in ipairs(handcard) do
        local contai = false
        for _, cardX in ipairs(cards) do
            if cardX:getEffectiveId() == card:getEffectiveId() then contai = true end
        end
        if not (card:isKindOf("Peach") and not self:OverFlowPeach(card)) and not (card:isKindOf("Indulgence") and contai)
            and not (card:isKindOf("Duel") and contai) and not (card:isKindOf("Snatch") and contai) and not (card:isKindOf("quanxiang") and contai)
                and not (card:isKindOf("ExNihilo") and contai) and not (card:isKindOf("Analeptic") and card:getNumber() > 9) then
            return card
        end
    end
end
local function usetongxinT(self, card, cards)
    for  _, kplayer in sgs.qlist(self.room:getAlivePlayers()) do
        if self:isEnemy(kplayer) and kplayer:getMark("@lualongyan") > 0 and not kplayer:isKongcheng() then
            return kplayer
        end
    end
    local y = self.player:getHandcardNum() - #cards - self.player:getMaxCards()
    for  _, kplayer in sgs.qlist(self.room:getAlivePlayers()) do
        if self:isEnemy(kplayer) and self:getKnownNum(kplayer) == kplayer:getHandcardNum() and self:getMaxCard(kplayer)
            and card:getNumber() > self:getMaxCard(kplayer):getNumber()  and kplayer:getHandcardNum() < 4 then
            for _, cardD in sgs.qlist(kplayer:getHandcards()) do
                if cardD:isKindOf("ExNihilo") or cardD:isKindOf("Peach") then return kplayer end
            end
        end
    end

    local RCount = 0
    local QCount = 0
    local slash = sgs.Sanguosha:cloneCard("slash")
    for _, enemy in ipairs(self.enemies) do
        local bool_k = false
        local count = 0
        for  _, kplayer in sgs.qlist(self.room:getAlivePlayers()) do
            if enemy:inMyAttackRange(kplayer) then count = count + 1 end
        end
        if count == 1 and enemy:inMyAttackRange(self.player) and self:slashIsEffective(slash, self.player, enemy) and not enemy:isKongcheng() then bool_k = true end
        if enemy:inMyAttackRange(self.player) and self:slashIsEffective(slash, self.player, enemy) then QCount = QCount + 1 end
        if bool_k then RCount = RCount + 1 end
    end

    if (y < 0 or self.player:isWounded()) and RCount > 1 then
        local cardA = usetongxinC(self, cards, true)
        for _, enemy in ipairs(self.enemies) do
            if enemy:inMyAttackRange(self.player) then
                if not enemy:isKongcheng() and not (self:getMaxCard(enemy) and cardA:getNumber() <= self:getMaxCard(enemy):getNumber()
                   and (self:getKnownNum(enemy) == enemy:getHandcardNum())) then return cardA, enemy end
            end
        end
    end

    local trick3 = sgs.Sanguosha:cloneCard("dismantlement", sgs.Card_NoSuit, 0)   --
    trick3:setSkillName("luatongxin")
    local dummy_use = { isDummy = true , to = sgs.SPlayerList()}
    self:useTrickCard(trick3, dummy_use)
    if dummy_use.to:length() > 0 then
        for _, p in sgs.qlist(dummy_use.to) do
            if not (self:getMinCard(p) and card:getNumber() <= self:getMinCard(p):getNumber()) and not p:isKongcheng() then return false, p end
        end
    end

    if (y < 0 or self.player:isWounded()) and (RCount > 0 or QCount > 1) and self:isWeak() then
        local cardA = usetongxinC(self, cards, true)
        for _, enemy in ipairs(self.enemies) do
            if enemy:inMyAttackRange(self.player) then
                if not enemy:isKongcheng() and not (self:getMaxCard(enemy) and cardA:getNumber() <= self:getMaxCard(enemy):getNumber()
                        and (self:getKnownNum(enemy) == enemy:getHandcardNum())) then return cardA, enemy end
            end
        end
    end

    local enemies = self.enemies
    self:sort(enemies, "defense")
    for _, enemy in ipairs(self.enemies) do
        local count = enemy:getHandcardNum()
        if enemy:getEquips() and enemy:getEquips():length() then
            count = count + enemy:getEquips():length()
        end
        if count > 1 and not enemy:isKongcheng() then return false, enemy end
    end
end
local luatongxin_skill = {}
luatongxin_skill.name = "luatongxin"
table.insert(sgs.ai_skills, luatongxin_skill)
luatongxin_skill.getTurnUseCard = function(self)
    if self.player:hasUsed("#luatongxin") then return end
    return sgs.Card_Parse("#luatongxin:.:")
end
sgs.ai_skill_use_func["#luatongxin"]  = function(cardS, use, self)
    local cards = self:getTurnUse(true)
    local card = usetongxinC(self, cards)
    if not card then return end
    local card_I, target = usetongxinT(self, card, cards)
    if card_I then card = card_I end
    if not target then return end
    use.card = sgs.Card_Parse("#luatongxin:".. card:getId() ..":")
    if use.to then use.to:append(target) end
    return
end

sgs.ai_use_priority.luatongxin = sgs.ai_use_priority.Dismantlement + 0.5
sgs.ai_card_intention.luatongxin = 40

local luaxieli_skill = {}
luaxieli_skill.name = "luaxieli"
table.insert(sgs.ai_skills, luaxieli_skill)
luaxieli_skill.getTurnUseCard = function(self)
    if self.player:hasUsed("#luaxieli") then return end
    local x = self.player:getHandcardNum() - self.player:getMaxCards()
    local flandre = self.room:findPlayerBySkillName("luajingdan")
    if flandre and flandre:isAlive() and self:isEnemy(flandre) then x = x - 1 end
    local koishi = self.room:findPlayerBySkillName("luabenwo")
    if koishi and koishi:isAlive() and self:isEnemy(koishi) and koishi:inMyAttackRange(self.player) then return end
    local minamoto = self.room:findPlayerBySkillName("luafuzheng")
    if minamoto and minamoto:isAlive() and self:isEnemy(minamoto) and #self.enemies > 1 and minamoto:getMark("@luafuzheng") > 2 then return end
    if (not self.player:isWounded()) and (x >= 0) then return end
    local RCount = 0
    local slash = sgs.Sanguosha:cloneCard("slash")
    for _, enemy in ipairs(self.enemies) do
        if enemy:inMyAttackRange(self.player) and self:slashIsEffective(slash, self.player, enemy) then RCount = RCount + 1 end
    end
    if RCount > 1 and x > -2 and not self.player:isWounded() then return end
    return sgs.Card_Parse("#luaxieli:.:")
end

sgs.ai_skill_use_func["#luaxieli"]  = function(card, use, self)
    use.card = sgs.Card_Parse("#luaxieli:.:")
    if use.to then use.to = sgs.SPlayerList() end
    return
end

sgs.ai_use_priority.luaxieli = 2

function sgs.ai_cardneed.luaxieli(to, card, self)
    if not to:hasSkill("luaxieli") then return end
    if to:getArmor() and not self:needToThrowArmor(to) then return end
    local notVine = false
    for _, enemy in ipairs(self.enemies) do
        if (enemy:canSlash(to) and enemy:hasWeapon("fan")) or enemy:hasSkills("huoji|longhun|shaoying|zonghuo|wuling|ol_xueji|luaguihuo") then
            notVine = true
        end
    end
    return card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or (card:isKindOf("Vine") and not notVine)
end

sgs.ai_skill_choice.luaxieli = function(self, choices)
    if self.player:getMaxCards() - self.player:getHandcardNum() > 1 then return "draw" end
    if self.player:isWounded() then return "recover" end
    return "draw"
end

sgs.ai_skill_invoke.change2loyalist = true

sgs.ai_skill_invoke.luacaihuosp2 = function(self, data)
    local yorigami = data:toPlayer()
    if self:isFriend(yorigami) then
        return true
    end
    local count = yorigami:getMark("@zhai")
    if count > self.room:getAlivePlayers():length() + 1 then return true end
end
--[[
sgs.ai_skill_cardask["@luamoucheng2"] = function(self, data, pattern, target, target2, prompt)
    local slashid = self:getCardId("Slash", self.player, nil , true)
    if not slashid then return "." end
    local slash = sgs.Card_Parse(slashid)
    if self:Skadi2(slash, target, self.player, false) then return slashid end
    return true
end
sgs.ai_skill_cardask["@luamoucheng3"] = function(self, data, pattern, target, target2, prompt)
    local yukari = target
    local slashid = self:getCardId("Slash", self.player, nil , true)
    if not slashid then return "." end
    local slash = sgs.Card_Parse(slashid)
    if self:isFriend(yukari) then return "." end
    for _, aplayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do
        if aplayer:getMark("damageYukari") > 0 then
            if self:Skadi2(slash, yukari, self.player, false) then return slashid end
        end
    end
    return "."
end]]--
sgs.ai_skill_invoke.luamoucheng2 = function(self, data)
    local yukari = self.room:findPlayerBySkillName("luamoucheng")
    if not yukari then return false end
    if self:isFriend(yukari) then return false end
    for _, aplayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do
        if aplayer:getMark("damageYukari") > 0 then
            return true
        end
    end
end


sgs.ai_skill_cardask["@luayaowugive"]  = function(self, data)
    if self.player:isKongcheng() then return "." end 
    local cards = sgs.QList2Table(self.player:getHandcards())
    self:sortByUseValue(cards, true)
    return "$" .. cards[1]:getEffectiveId() 
end 