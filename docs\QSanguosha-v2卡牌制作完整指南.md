# QSanguosha-v2 卡牌制作完整指南

## 概述

在QSanguosha-v2中，卡牌是通过Lua脚本定义的，包括卡牌的基本属性、使用逻辑、效果实现和翻译文本。本指南将详细介绍如何从零开始创建一个完整的卡牌，包括代码实现、翻译文件和图片资源。

## 第一部分：开发环境准备

### 1. 文件结构
```
extensions/
├── your_card_package.lua       # 你的卡牌扩展包文件
└── ...

lang/zh_CN/Package/
├── YourCardPackage.lua         # 翻译文件
└── ...

image/card/
├── your_card.png              # 卡牌图片
└── ...
```

### 2. 基本依赖
- 了解Lua编程语言基础
- 熟悉三国杀游戏规则
- 理解QSanguosha的卡牌系统

## 第二部分：卡牌类型系统

### 卡牌分类

#### 1. 基本牌 (Basic Cards)
- **杀**：攻击类基本牌
- **闪**：防御类基本牌  
- **桃**：回复类基本牌
- **酒**：增强类基本牌

#### 2. 锦囊牌 (Trick Cards)
- **即时锦囊**：立即生效的锦囊牌
- **延时锦囊**：延迟生效的锦囊牌

#### 3. 装备牌 (Equipment Cards)
- **武器**：提供攻击范围和特殊效果
- **防具**：提供防御能力
- **坐骑**：影响距离计算
- **宝物**：提供特殊能力

## 第三部分：卡牌创建步骤

### 第一步：创建扩展包

```lua
-- 创建卡牌扩展包
module("extensions.your_cards", package.seeall)
extension_your_cards = sgs.Package("your_cards", sgs.Package_CardPack)
```

### 第二步：定义基本牌

```lua
-- 创建基本牌示例：治疗药
healing_potion = sgs.CreateBasicCard{
    name = "healing_potion",           -- 卡牌内部名称
    class_name = "HealingPotion",      -- 类名
    suit = sgs.Card_Heart,             -- 花色：红桃
    number = 5,                        -- 点数：5
    subtype = "basic_card",            -- 子类型
    target_fixed = false,              -- 是否固定目标
    can_recast = false,                -- 是否可重铸
    
    -- 目标选择函数
    filter = function(self, targets, to_select)
        return #targets < 1 and to_select:isWounded()
    end,
    
    -- 目标数量检查
    feasible = function(self, targets)
        return #targets == 1
    end,
    
    -- 使用条件检查
    available = function(self, player)
        return not player:hasUsed("HealingPotion")
    end,
    
    -- 卡牌效果实现
    on_effect = function(self, effect)
        local room = effect.to:getRoom()
        local recover = sgs.RecoverStruct()
        recover.who = effect.from
        recover.recover = 1
        room:recover(effect.to, recover)
    end,
}
healing_potion:setParent(extension_your_cards)
```

### 第三步：定义锦囊牌

```lua
-- 创建即时锦囊示例：群体治疗
mass_heal = sgs.CreateTrickCard{
    name = "mass_heal",
    class_name = "MassHeal", 
    suit = sgs.Card_Heart,
    number = 8,
    subclass = sgs.LuaTrickCard_TypeNormal,  -- 即时锦囊
    target_fixed = true,                     -- 固定目标（全体）
    
    -- 卡牌效果
    on_use = function(self, room, source, targets)
        local all_players = room:getAllPlayers()
        for _, player in sgs.qlist(all_players) do
            if player:isWounded() then
                local recover = sgs.RecoverStruct()
                recover.who = source
                recover.recover = 1
                room:recover(player, recover)
            end
        end
    end,
}
mass_heal:setParent(extension_your_cards)

-- 创建延时锦囊示例：诅咒
curse_card = sgs.CreateTrickCard{
    name = "curse_card",
    class_name = "CurseCard",
    suit = sgs.Card_Spade,
    number = 13,
    subclass = sgs.LuaTrickCard_TypeDelayedTrick,  -- 延时锦囊
    
    -- 目标选择
    filter = function(self, targets, to_select)
        return #targets < 1 and to_select:objectName() ~= sgs.Self:objectName()
    end,
    
    feasible = function(self, targets)
        return #targets == 1
    end,
    
    -- 判定效果
    on_effect = function(self, effect)
        local room = effect.to:getRoom()
        local judge = sgs.JudgeStruct()
        judge.who = effect.to
        judge.reason = self:objectName()
        judge.pattern = ".|black"  -- 黑色判定失败
        room:judge(judge)
        
        if not judge:isGood() then
            room:damage(sgs.DamageStruct(self, effect.from, effect.to))
        end
    end,
}
curse_card:setParent(extension_your_cards)
```

### 第四步：定义装备牌

```lua
-- 创建武器示例：魔法剑
magic_sword = sgs.CreateWeapon{
    name = "magic_sword",
    class_name = "MagicSword",
    suit = sgs.Card_Diamond,
    number = 10,
    range = 3,                    -- 攻击范围
    
    -- 武器技能
    on_install = function(self, player)
        local room = player:getRoom()
        room:broadcastSkillInvoke("magic_sword")
    end,
    
    -- 武器效果触发
    trigger_skill = sgs.CreateTriggerSkill{
        name = "magic_sword_skill",
        events = {sgs.SlashHit},
        on_trigger = function(self, event, player, data)
            local effect = data:toSlashEffect()
            if effect.slash:objectName() == "slash" then
                local room = player:getRoom()
                if room:askForSkillInvoke(player, "magic_sword") then
                    effect.to:drawCards(1)  -- 目标摸一张牌
                end
            end
            return false
        end
    }
}
magic_sword:setParent(extension_your_cards)

-- 创建防具示例：魔法护甲
magic_armor = sgs.CreateArmor{
    name = "magic_armor",
    class_name = "MagicArmor", 
    suit = sgs.Card_Club,
    number = 7,
    
    -- 防具效果
    trigger_skill = sgs.CreateTriggerSkill{
        name = "magic_armor_skill",
        events = {sgs.DamageInflicted},
        on_trigger = function(self, event, player, data)
            local damage = data:toDamage()
            if damage.nature == sgs.DamageStruct_Fire then
                return true  -- 防止火焰伤害
            end
            return false
        end
    }
}
magic_armor:setParent(extension_your_cards)

-- 创建坐骑示例：飞行坐骑
flying_mount = sgs.CreateOffensiveHorse{
    name = "flying_mount",
    class_name = "FlyingMount",
    suit = sgs.Card_Heart,
    number = 12,
    correct_func = function(self, from, to)
        return -2  -- 距离-2
    end
}
flying_mount:setParent(extension_your_cards)

-- 创建宝物示例：魔法水晶
magic_crystal = sgs.CreateTreasure{
    name = "magic_crystal", 
    class_name = "MagicCrystal",
    suit = sgs.Card_Diamond,
    number = 6,
    
    -- 宝物技能
    trigger_skill = sgs.CreateTriggerSkill{
        name = "magic_crystal_skill",
        events = {sgs.DrawNCards},
        on_trigger = function(self, event, player, data)
            local count = data:toInt()
            data:setValue(count + 1)  -- 摸牌+1
            return false
        end
    }
}
magic_crystal:setParent(extension_your_cards)
```

## 第四部分：翻译文件配置

### 创建翻译文件 (lang/zh_CN/Package/YourCardPackage.lua)

```lua
return {
    -- 扩展包信息
    ["your_cards"] = "自定义卡牌包",
    ["designer:your_cards"] = "你的名字",
    
    -- 基本牌翻译
    ["healing_potion"] = "治疗药水",
    [":healing_potion"] = "基本牌<br/><b>时机</b>：出牌阶段<br/><b>目标</b>：一名已受伤的角色<br/><b>效果</b>：目标角色回复1点体力。",
    
    -- 锦囊牌翻译
    ["mass_heal"] = "群体治疗",
    [":mass_heal"] = "锦囊牌<br/><b>时机</b>：出牌阶段<br/><b>目标</b>：所有角色<br/><b>效果</b>：所有已受伤的角色回复1点体力。",
    
    ["curse_card"] = "诅咒卡牌",
    [":curse_card"] = "延时锦囊牌<br/><b>时机</b>：出牌阶段<br/><b>目标</b>：一名其他角色<br/><b>效果</b>：目标角色判定，若为黑色，其受到1点伤害。",
    
    -- 装备牌翻译
    ["magic_sword"] = "魔法剑",
    [":magic_sword"] = "装备牌·武器<br/><b>攻击范围</b>：3<br/><b>武器技能</b>：当你使用【杀】命中目标后，你可以令目标摸一张牌。",
    
    ["magic_armor"] = "魔法护甲", 
    [":magic_armor"] = "装备牌·防具<br/><b>防具技能</b>：防止你受到的火焰伤害。",
    
    ["flying_mount"] = "飞行坐骑",
    [":flying_mount"] = "装备牌·坐骑<br/><b>坐骑技能</b>：你计算与其他角色的距离-2。",
    
    ["magic_crystal"] = "魔法水晶",
    [":magic_crystal"] = "装备牌·宝物<br/><b>宝物技能</b>：你的摸牌阶段多摸一张牌。",
}
```

## 第五部分：卡牌属性详解

### 基本属性

| 属性 | 类型 | 说明 | 示例 |
|------|------|------|------|
| name | string | 卡牌内部名称 | "healing_potion" |
| class_name | string | 卡牌类名 | "HealingPotion" |
| suit | number | 花色 | sgs.Card_Heart |
| number | number | 点数 | 5 |
| subtype | string | 子类型 | "basic_card" |

### 花色常量

| 常量 | 值 | 中文名 |
|------|----|----|
| sgs.Card_Spade | 0 | 黑桃 |
| sgs.Card_Club | 1 | 梅花 |
| sgs.Card_Heart | 2 | 红桃 |
| sgs.Card_Diamond | 3 | 方块 |
| sgs.Card_NoSuit | 4 | 无花色 |

### 功能属性

| 属性 | 类型 | 说明 |
|------|------|------|
| target_fixed | boolean | 是否固定目标 |
| can_recast | boolean | 是否可重铸 |
| will_throw | boolean | 是否弃置使用 |
| mute | boolean | 是否静音 |

## 第六部分：高级功能

### 1. 条件判断函数

```lua
-- 复杂的目标选择
filter = function(self, targets, to_select)
    -- 不能选择自己
    if to_select:objectName() == sgs.Self:objectName() then
        return false
    end
    
    -- 最多选择2个目标
    if #targets >= 2 then
        return false
    end
    
    -- 只能选择已受伤的角色
    if not to_select:isWounded() then
        return false
    end
    
    return true
end
```

### 2. 复杂效果实现

```lua
-- 多重效果的卡牌
on_effect = function(self, effect)
    local room = effect.to:getRoom()
    local source = effect.from
    local target = effect.to
    
    -- 效果1：造成伤害
    local damage = sgs.DamageStruct()
    damage.card = self
    damage.from = source
    damage.to = target
    damage.damage = 1
    room:damage(damage)
    
    -- 效果2：摸牌
    target:drawCards(2)
    
    -- 效果3：条件触发
    if target:getHp() <= 1 then
        local recover = sgs.RecoverStruct()
        recover.who = source
        recover.recover = 1
        room:recover(target, recover)
    end
end
```

### 3. 技能卡实现

```lua
-- 创建技能卡
skill_card = sgs.CreateSkillCard{
    name = "special_skill_card",
    skill_name = "special_skill",
    target_fixed = false,
    
    filter = function(self, targets, to_select)
        return #targets < 1
    end,
    
    on_effect = function(self, effect)
        -- 技能卡效果
        local room = effect.to:getRoom()
        effect.to:drawCards(3)
    end,
}
```

## 第七部分：图片资源

### 图片规格
- **尺寸**：标准卡牌图片为 93×130 像素
- **格式**：PNG格式，支持透明背景
- **命名**：与卡牌name属性一致

### 图片路径
```
image/card/your_card_name.png
```

## 第八部分：配置加载

### 在config.lua中添加包名

```lua
package_names = {
    -- 其他包...
    "your_cards",  -- 添加你的卡牌包
}
```

### 在扩展包中加载翻译

```lua
-- 在扩展包文件末尾添加
sgs.LoadTranslationTable{
    ["your_cards"] = "自定义卡牌包",
}
```

## 第九部分：测试与调试

### 1. 基本测试
- 检查卡牌是否正确加载
- 验证目标选择逻辑
- 测试卡牌效果是否正常

### 2. 平衡性测试
- 评估卡牌强度
- 检查与其他卡牌的交互
- 测试极端情况

### 3. 常见问题
- 翻译文件路径错误
- 函数语法错误
- 效果逻辑冲突

## 第十部分：完整示例

### 示例：创建"雷电风暴"卡牌

```lua
-- 扩展包定义
module("extensions.storm_cards", package.seeall)
extension_storm = sgs.Package("storm_cards", sgs.Package_CardPack)

-- 雷电风暴锦囊牌
thunder_storm = sgs.CreateTrickCard{
    name = "thunder_storm",
    class_name = "ThunderStorm",
    suit = sgs.Card_Spade,
    number = 13,
    subclass = sgs.LuaTrickCard_TypeNormal,
    target_fixed = true,
    
    on_use = function(self, room, source, targets)
        local all_players = room:getOtherPlayers(source)
        for _, player in sgs.qlist(all_players) do
            local damage = sgs.DamageStruct()
            damage.card = self
            damage.from = source
            damage.to = player
            damage.damage = 1
            damage.nature = sgs.DamageStruct_Thunder
            room:damage(damage)
        end
    end,
}
thunder_storm:setParent(extension_storm)

-- 翻译加载
sgs.LoadTranslationTable{
    ["storm_cards"] = "风暴卡牌包",
    ["thunder_storm"] = "雷电风暴",
    [":thunder_storm"] = "锦囊牌<br/><b>时机</b>：出牌阶段<br/><b>目标</b>：所有其他角色<br/><b>效果</b>：对所有其他角色造成1点雷电伤害。",
}
```

## 总结

卡牌制作的关键步骤：
1. **设计卡牌概念**：确定卡牌类型和效果
2. **编写Lua代码**：实现卡牌逻辑
3. **配置翻译文件**：添加中文描述
4. **准备图片资源**：制作卡牌图片
5. **测试调试**：确保功能正常
6. **平衡调整**：优化游戏体验

通过本指南，您可以创建出功能完整、平衡合理的自定义卡牌，丰富QSanguosha-v2的游戏内容！
