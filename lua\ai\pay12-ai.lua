
math.randomseed(tostring(os.time()):reverse():sub(1, 7)) --------设置时间种子


function usebeihuancard(self)
	local handcards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(handcards)	
	if #handcards == 0 then return end 
	local slash = {}
	slash[1] = "fire_slash"
	slash[2] = "thunder_slash"
	slash[3] = "slash"

	local aoe = {}
	aoe[1] = "archery_attack"
	aoe[2] = "savage_assault"
	
	local lostH = self.player:getLostHp() + 1 

	for i = 1, 2 do
		local trick2 = sgs.Sanguosha:cloneCard(aoe[i], sgs.Card_SuitToBeDecided, -1)
		trick2:addSubcard(handcards[1]:getEffectiveId())	
		if (self:getAoeValue(trick2, self.player) >= 80) and (self.player:getMark("luabeihuan1") == 0) then 
			return aoe[i]
		end 
	end 	

	local trick9 = sgs.Sanguosha:cloneCard("god_salvation", sgs.Card_SuitToBeDecided, -1)
	trick9:addSubcard(handcards[1]:getEffectiveId())	
	local value_o = self:godSalvationValue(trick9)
	--self.room:writeToConsole(value_o)
	if (value_o > 25) and (self.player:getMark("luabeihuan1") == 0) then 
		return "god_salvation"
	end 
	
	local basic1 = sgs.Sanguosha:cloneCard("peach", sgs.Card_SuitToBeDecided, -1)
	basic1:addSubcard(handcards[1]:getEffectiveId())	
	if (self.player:getLostHp() > 1) and (#handcards == 1) and (self.player:getMark("luabeihuan1") == 1) then 
		local dummyuse = { isDummy = true }
		self:useBasicCard(basic1, dummyuse)
		if dummyuse.card then 
			return "peach"  
		end 
	end 
	
	local trick = sgs.Sanguosha:cloneCard("duel", sgs.Card_SuitToBeDecided, -1)
	trick:addSubcard(handcards[1]:getEffectiveId())	
	if self:ceshi(trick, false, true) and (self.player:getMark("luabeihuan1") == 0) then return "duel" end 

	local trick8 = sgs.Sanguosha:cloneCard("ex_nihilo", sgs.Card_SuitToBeDecided, -1)
	trick8:addSubcard(handcards[1]:getEffectiveId())	
	if trick8:isAvailable(self.player) and (self.player:getMark("luabeihuan1") == 0) then return "ex_nihilo" end 
			
	local trick99 = sgs.Sanguosha:cloneCard("god_salvation", sgs.Card_SuitToBeDecided, -1)
	trick99:addSubcard(handcards[1]:getEffectiveId())
	local value_oo = self:godSalvationValue(trick99)
	self.room:writeToConsole(value_oo)
	if (value_oo > 15) and (self.player:getMark("luabeihuan1") == 0) then
		return "god_salvation"
	end 
	
	local basic11 = sgs.Sanguosha:cloneCard("peach", sgs.Card_SuitToBeDecided, -1)
	basic11:addSubcard(handcards[1]:getEffectiveId())
	if (self.player:getLostHp() == 1) and (self.player:getMark("luabeihuan1") == 1) then 
		local dummyuse = { isDummy = true }
		self:useBasicCard(basic11, dummyuse)
		if dummyuse.card then 
			return "peach"  
		end 
	end 

	local trick2 = sgs.Sanguosha:cloneCard("snatch", sgs.Card_SuitToBeDecided, -1)
	trick2:addSubcard(handcards[1]:getEffectiveId())	
	if self:ceshi(trick2, true) and (self.player:getMark("luabeihuan1") == 0) then return "snatch" end 

	for i = 1, 2 do
		local trick23 = sgs.Sanguosha:cloneCard(aoe[i], sgs.Card_SuitToBeDecided, -1)
		trick23:addSubcard(handcards[1]:getEffectiveId())
		if (self:getAoeValue(trick23, self.player) >= 60) and (self.player:getMark("luabeihuan1") == 0) then
			return aoe[i]
		end 
	end 	
	
	for i = 1, 3 do
		local basic2 = sgs.Sanguosha:cloneCard(slash[i], sgs.Card_SuitToBeDecided, -1)
		basic2:addSubcard(handcards[1]:getEffectiveId())	
		if self:YouMu() and (self.player:getMark("luabeihuan1") == 1) then 
			local dummyuse = { isDummy = true }
			self:useBasicCard(basic2, dummyuse)
			if dummyuse.card then 
				return slash[i]  
			end 
		end 
	end 
		
	local trick3 = sgs.Sanguosha:cloneCard("dismantlement", sgs.Card_SuitToBeDecided, -1)   --
	trick3:addSubcard(handcards[1]:getEffectiveId())	
	if self:ceshi(trick3, true) and (self.player:getMark("luabeihuan1") == 0) then return "dismantlement" end 
	
	local trick4 = sgs.Sanguosha:cloneCard("snatch", sgs.Card_SuitToBeDecided, -1)
	trick4:addSubcard(handcards[1]:getEffectiveId())	
	if self:ceshi(trick4) and (self.player:getMark("luabeihuan1") == 0) then return "snatch" end 	
	
	local trick5 = sgs.Sanguosha:cloneCard("duel", sgs.Card_SuitToBeDecided, -1)
	trick5:addSubcard(handcards[1]:getEffectiveId())	
	if self:ceshi(trick5) and (self.player:getMark("luabeihuan1") == 0) then return "duel" end 

	for i = 1, 2 do
		local basic2 = sgs.Sanguosha:cloneCard(slash[i], sgs.Card_SuitToBeDecided, -1)
		basic2:addSubcard(handcards[1]:getEffectiveId())	
		local dummyuse = { isDummy = true, to = sgs.SPlayerList() }
		self:useBasicCard(basic2, dummyuse)
		if dummyuse.card and not dummyuse.to:isEmpty() and (self.player:getMark("luabeihuan1") == 1) then
			for _, p in sgs.qlist(dummyuse.to) do
				if self:isGoodChainTarget(p, self.player, nil, nil, dummyuse.card) then 
					return slash[i]  
				end 
			end 
		end 
	end 

	for i = 1, 3 do
		local basic2 = sgs.Sanguosha:cloneCard(slash[i], sgs.Card_SuitToBeDecided, -1)
		basic2:addSubcard(handcards[1]:getEffectiveId())	
		local dummyuse = { isDummy = true, to = sgs.SPlayerList() }
		self:useBasicCard(basic2, dummyuse)
		if dummyuse.card and not dummyuse.to:isEmpty() and (self.player:getMark("luabeihuan1") == 1) then
			for _, p in sgs.qlist(dummyuse.to) do
				if self:hasHeavySlashDamage(self.player, damage.card, p) then 
					return slash[i]  
				end 
			end 
		end 
	end 

	for i = 1, 2 do
		local trick24 = sgs.Sanguosha:cloneCard(aoe[i], sgs.Card_SuitToBeDecided, -1)
		trick24:addSubcard(handcards[1]:getEffectiveId())
		if (self:getAoeValue(trick24, self.player) >= 40) and (self.player:getMark("luabeihuan1") == 0) then
			return aoe[i]
		end 
	end 
	
	local basic123 = sgs.Sanguosha:cloneCard("peach", sgs.Card_SuitToBeDecided, -1)
	basic123:addSubcard(handcards[1]:getEffectiveId())
	if (self.player:getLostHp() > 1) and (self.player:getMark("luabeihuan1") == 1) then 
		local dummyuse = { isDummy = true }
		self:useBasicCard(basic123, dummyuse)
		if dummyuse.card then 
			return "peach"  
		end 
	end 

	local trick85 = sgs.Sanguosha:cloneCard("ex_nihilo", sgs.Card_SuitToBeDecided, -1)
	trick85:addSubcard(handcards[1]:getEffectiveId())
	if trick85:isAvailable(self.player) and (self.player:getHandcardNum() <= self.player:getMaxCards()) and (self.player:getMark("luabeihuan1") == 0) then return "ex_nihilo" end
	
	for _, ecard in ipairs(self:getTurnUse(true)) do
		if ecard:isKindOf("Slash") and (self.player:getMark("luabeihuan1") == 1) then 	
			return "analeptic"
		end 
	end 
	return 
end 

local function beihuansubcard(self, bool1, table_X, has_Slash)
	local toUse = {}
	if not bool1 then
		local cards = sgs.QList2Table(self.player:getHandcards())
		self:sortByUseValue(cards, true)

		for _, acard in ipairs(cards) do
			if acard:isKindOf("Peach") and self:OverFlowPeach(acard) then
				table.insert(toUse, acard:getId())
			end
		end

		local jinks = self:getCardsNum("Jink")
		if jinks > 0 then
			local jcount = 0
			for _, acard in ipairs(cards) do
				if (jcount == jinks - 1) and self.player:getHp() < 3 then break end
				if acard:isKindOf("Jink") and not table.contains(toUse, acard:getId()) then
					table.insert(toUse, acard:getId())
					jcount = jcount + 1
				end
			end
		end

		for _, acard in ipairs(cards) do
			if acard:isKindOf("Slash") and (not table_X or (not table.contains(table_X, acard))) and (not table.contains(toUse, acard:getId())) then
				table.insert(toUse, acard:getId())
			end
		end

		for _, acard in ipairs(cards) do
			if acard:isKindOf("Analeptic") and (not has_Slash) and (not table.contains(toUse, acard:getId()))
					and not self:isWeak() then
				table.insert(toUse, acard:getId())
			end
		end
	else
		local cards = sgs.QList2Table(self.player:getCards("he"))
		for _, card in ipairs(table_X) do
			if self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand and card:isKindOf("EquipCard") then
				if self:getSameEquip(card, self.player) then
					local pcard = self:getSameEquip(card, self.player)
					table.insert(toUse, pcard:getId())
				end
			end
		end

		for _, card in ipairs(cards) do
			if (not table.contains(table_X, card)) and not table.contains(toUse, card:getId())
					and self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand then
				table.insert(toUse, card:getId())
			end
		end
	end
	return toUse
end
local luabeihuan_skill = {}
luabeihuan_skill.name = "luabeihuan"
table.insert(sgs.ai_skills, luabeihuan_skill)

luabeihuan_skill.getTurnUseCard = function(self)	
	if self.player:isNude() then return end
	return sgs.Card_Parse("#luabeihuan:.:")
end 

sgs.ai_skill_use_func["#luabeihuan"] = function(cardR, use, self)
	local table_X = self:getTurnUse(true)
	self.room:writeToConsole("sanae test")
	local has_Slash = false 
	for _, card in ipairs(table_X) do
		if card:isKindOf("Slash") then  
			has_Slash = true
		end 
	end 
	if (self.player:getMark("luabeihuan1") == 0) then 
		local toUse = beihuansubcard(self, false, table_X, has_Slash)
		local str = usebeihuancard(self) 
		if str and #toUse > 1 then 
			local taoluancard = sgs.Sanguosha:cloneCard(str)
			local dummy_use = { isDummy = false , to = sgs.SPlayerList() }
			taoluancard:setSkillName("luabeihuan")
			self.room:writeToConsole("sanae test2")
			taoluancard:addSubcard(toUse[1])	
			taoluancard:addSubcard(toUse[2])	
			
			assert(taoluancard)
			--self.room:writeToConsole("早苗测试")
			self:useTrickCard(taoluancard, dummy_use)		
			if not dummy_use.card then return end
			self.room:writeToConsole("sanae testB" .. str)
			if dummy_use.to and not dummy_use.to:isEmpty() then 
				if dummy_use.to[1] then 
					self.room:writeToConsole("早苗测试目标" .. dummy_use.to[1]:objectName()) 
				end 
			end 
			use.card = taoluancard
			self.room:writeToConsole("luabeihuan test2")
			if use.to then
				use.to = dummy_use.to
				self.room:writeToConsole("luabeihuan test3")
				return
			else
				self.room:writeToConsole("luabeihuan test3")
				return
			end
		end 
	else
		local toUse = beihuansubcard(self, false, table_X, has_Slash)
		local str = usebeihuancard(self) 
		if str and #toUse > 1 then 
			local taoluancard = sgs.Sanguosha:cloneCard(str)
			local dummy_use = { isDummy = false , to = sgs.SPlayerList() }
			taoluancard:setSkillName("luabeihuan")
			self.room:writeToConsole("sanae test2")
			taoluancard:addSubcard(toUse[1])	
			taoluancard:addSubcard(toUse[2])	
			
			assert(taoluancard)
			--self.room:writeToConsole("早苗测试")
			self:useTrickCard(taoluancard, dummy_use)		
			if not dummy_use.card then return end
			self.room:writeToConsole("早苗测试B" .. str)
			if dummy_use.to and not dummy_use.to:isEmpty() then 
				if dummy_use.to[1] then 
					self.room:writeToConsole("早苗测试目标" .. dummy_use.to[1]:objectName()) 
				end 
			end 
			use.card = taoluancard
			self.room:writeToConsole("luabeihuan test2")
			if use.to then
				use.to = dummy_use.to
				self.room:writeToConsole("luabeihuan test3")
				return
			else
				self.room:writeToConsole("luabeihuan test3")
				return
			end
		end
	end 
end 

function sgs.ai_cardsview_valuable.luabeihuan(self, class_name, player)
	local classname2objectname = {
		["Slash"] = "slash", ["Jink"] = "jink",
		["Peach"] = "peach", ["Analeptic"] = "analeptic",
		["Nullification"] = "nullification",
		["FireSlash"] = "fire_slash", ["ThunderSlash"] = "thunder_slash"
	}
	local name = classname2objectname[class_name]
	
	if not name then return end
	local no_have = true
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)	--yun
	self:sortByKeepValue(cards)	
	for _,c in ipairs(cards) do	--yun
		if c:isKindOf(class_name) then
			no_have = false
			break
		end
	end
	local x = self.player:getMark("@fushi") 
	if (not no_have) and ((x > 2) or not self.player:hasSkill("lualihe")) then return end
	if class_name == "Peach" and player:getMark("Global_PreventPeach") > 0 then return end
	
	if not cards[1] then return end	--yun	1329有警告
	local toUse = {}
	--self.room:writeToConsole("森之虫测试" .. class_name)
	if class_name == "Nullification" then 
		
		if (self.player:getMark("luabeihuan1") == 1) then return end 
		local jinks = self:getCardsNum("Jink")
		local jcount = 0
		for _, acard in ipairs(cards) do
			if (jcount == jinks - 1) or (jinks == 0) then break end 
			if acard:isKindOf("Jink") and not table.contains(toUse, acard:getId()) then 
				table.insert(toUse, acard:getId())
				jcount = jcount + 1
			end
		end 

		for _, acard in ipairs(cards) do
			if acard:isKindOf("Slash") and (not table.contains(toUse, acard:getId())) then 
				table.insert(toUse, acard:getId())
			end 
		end 
		
		for _, acard in ipairs(cards) do
			if acard:isKindOf("Analeptic") and (not has_Slash) and (not table.contains(toUse, acard:getId())) 
				and not self:isWeak() then 
				table.insert(toUse, acard:getId())
			end 
		end 			
		if #toUse > 1 then
			return "#luabeihuan:" .. toUse[1] .. "+" .. toUse[2] .. ":" .. "nullification"	
		end 
	else
		if (self.player:getMark("luabeihuan1") == 0) then return end
		if self.player:getPhase() == sgs.Player_Play then self.room:writeToConsole("morino_hon test P") end
		for _, acard in ipairs(cards) do
			local boola = ((acard:isKindOf("Weapon") and self.player:getHp() < 2) or (acard:isKindOf("DefensiveHorse") and self.player:getHp() < 2)
					or acard:isKindOf("EightDiagram") or acard:isKindOf("RenwangShield"))
					and self.room:getCardPlace(acard:getId()) == sgs.Player_PlaceEquip
			local boolb = acard:isKindOf("ExNihilo")
			if (not boola) and (not boolb) then 
				table.insert(toUse, acard:getId())
			end 
		end 
		if #toUse > 1 then 
			return "#luabeihuan:" .. toUse[1] .. "+" .. toUse[2] .. ":" .. name
		end 
	end 
end 

sgs.ai_use_priority.luabeihuan = function(self)
	if (self.player:getMark("luabeihuan1") == 0) then return 7 end 
	return 4
end 

sgs.ai_skill_use["@@lualihe"] = function(self, prompt)-- 义舍技能卡的使用函数
	local x = self.player:getMark("@fushi") - 1
	if x <= 0 then return "." end 
	self:sort(self.enemies, "defense")
	for _, enemy in ipairs(self.enemies) do	
		if enemy:getHandcardNum() < x then 
			return "#lualihe:.:->" .. enemy:objectName()
		end 
	end 
end 

sgs.ai_card_intention.lualihe = 40

local lualianai_skill = {}
lualianai_skill.name = "lualianai"
table.insert(sgs.ai_skills, lualianai_skill)

lualianai_skill.getTurnUseCard = function(self)
	if self.player:getMark("@lianai") < 1 then return end
	return sgs.Card_Parse("#lualianai:.:")
end

sgs.ai_skill_use_func["#lualianai"] = function(card, use, self)
	local Friends = self.friends
	self:sort(Friends, "defense")
	local xizi = self.room:findPlayerBySkillName("xianfu")
	if xizi and xizi:isAlive() and self:isFriend(xizi) then
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if self:isFriend(p) and p:getMark("@fu") > 0 then
				use.card = sgs.Card_Parse("#lualianai:.:")
				if use.to then
					use.to:append(p)
					return
				end
			end
		end
	end
	local daiyousei = self.room:findPlayerBySkillName("LuaYizuo")
	if daiyousei and daiyousei:isAlive() and self:isFriend(daiyousei) then
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if self:isFriend(p) and p:getMark("@LuaYizuo") > 0 then
				use.card = sgs.Card_Parse("#lualianai:.:")
				if use.to then
					use.to:append(p)
					return
				end
			end
		end
	end
	local clownpiece = self.room:findPlayerBySkillName("luayuekuang")
	if clownpiece and clownpiece:isAlive() and self:isFriend(clownpiece) then
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if self:isFriend(p) then
				use.card = sgs.Card_Parse("#lualianai:.:")
				if use.to then
					use.to:append(p)
					return
				end
			end
		end
	end
	for _, p in ipairs(Friends) do
		if p:getMaxHp() > 1 and p:isWounded() and not self:hasSkills(sgs.need_maxhp_skill, p) then
			use.card = sgs.Card_Parse("#lualianai:.:")
			if use.to then
				use.to:append(p)
				return
			end
		end
	end
end

sgs.ai_use_priority.lualianai = 4

local function findluacpaCard(self)
    local zb
	if self.player:getMark("@cp2") > 0 then
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if p:hasSkill("lualianai") then zb = p end
		end
	else
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if p:getMark("@cp2") > 0 then zb = p end
		end
	end

	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end

	local function Check_F(card, jijiu)
        if card:isKindOf("TrickCard") then
            if card:isKindOf("IronChain") and not shuxin then return false end
            if card:isKindOf("Lightning") and self:willUseLightning(card) and not jijiu then return true end
			if card:isKindOf("AOE") and self:getAoeValue(card) < 35 then return false end
			if card:isKindOf("GodSalvation") and self:godSalvationValue(card) < 10 then return false end
			if card:isKindOf("AmazingGrace") and self:godSalvationValue(card) < 2 then return false end
			if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
            local dummy_use = {isDummy = true}
            self:useTrickCard(card, dummy_use)
            if dummy_use.card then return true end
			return false
        end
        if card:isKindOf("EquipCard") then --or card:isKindOf("DefensiveHorse")
            if card:isKindOf("OffensiveHorse") then
                if self.player:getOffensiveHorse() and not jijiu then return true end
            end
            if card:isKindOf("DefensiveHorse") then
                if self.player:getDefensiveHorse() and not jijiu then return true end  
            end
            if card:isKindOf("Armor") and self:needToThrowArmor() then return true end
            if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
                local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
                        or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor()
                if not bool_3 then return true end
            end
            if card:isKindOf("Weapon") then
                local dummy_use = {isDummy = true}
                self:useEquipCard(card, dummy_use)
                if not dummy_use.card then return true end
                if dummy_use.card and self.player:getWeapon() then return true end
            end
        end
		for _, askill in sgs.qlist(self.player:getVisibleSkillList(true)) do
			local callback = sgs.ai_cardneed[askill:objectName()]
			if type(callback) == "function" and callback(self.player, card, self) then
				return true
			end
		end
		return false
    end
	if true then
        local cards = sgs.QList2Table(self.player:getHandcards())
        self:sortByKeepValue(cards)
        local toCard = cards[1]
        if not (toCard:isKindOf("Jink") and self.player:getHp() < 3 and self:getCardsNum("Jink") < 2) and not (toCard:isKindOf("Peach") and not self:OverFlowPeach(toCard))
            and not (toCard:isKindOf("Analeptic") and self:isWeak() and self.player:getHp() == 1) and not Check_F(toCard, self:isWeak(zb)) then
            return cards[1]
        end
	end
end
local luacpa_skill = {}
luacpa_skill.name = "luacpa"
table.insert(sgs.ai_skills, luacpa_skill)
luacpa_skill.getTurnUseCard = function(self)
	if self.player:isKongcheng() then return end
	if self.player:hasUsed("#luacpa") then return end

	local zb
	if self.player:getMark("@cp2") > 0 then
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if p:hasSkill("lualianai") then zb = p end
		end
	else
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if p:getMark("@cp2") > 0 then zb = p end
		end
	end
	if not zb then return end
	if not zb:isAlive() then return end
	if not zb:isWounded() then return end
	if not self:isFriend(zb) then return end
	return sgs.Card_Parse("#luacpa:.:")
end

sgs.ai_skill_use_func["#luacpa"] = function(card, use, self)
	local card0 = findluacpaCard(self)
	if not card0 then return end
	use.card = sgs.Card_Parse("#luacpa:".. card0:getId() ..":")
	if use.to then
		use.to = sgs.SPlayerList()
		return
	end
	return
end




















