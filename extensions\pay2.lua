
extension_pay_b =sgs.Package("pay2")

marisa = sgs.General(extension_pay_b,"marisa","luacai",3,false,false,false)
fujiwara = sgs.General(extension_pay_b,"fujiwara","luacai",4,false,false,false)

reimu = sgs.General(extension_pay_b,"reimu","luacai",4,false,false,false)
reimuA = sgs.General(extension_pay_b,"reimuA","luacai",4,false,true,true)
reimuB = sgs.General(extension_pay_b,"reimuB","luacai",4,false,true,true)
reimuC = sgs.General(extension_pay_b,"reimuC","luacai",4,false,true,true)
reimuD = sgs.General(extension_pay_b,"reimuD","luacai",4,false,true,true)
reimuE = sgs.General(extension_pay_b,"reimuE","luacai",4,false,true,true)
reimuX = sgs.General(extension_pay_b,"reimuX","luacai",4,false,true,true)
huashan = sgs.General(extension_pay_b,"huashan","luacai",4,false,true,true)
chenn = sgs.General(extension_pay_b,"chenn","qun",3,false,true,true)
yugi = sgs.General(extension_pay_b,"yugi","luadi",4,false,false,false)
akong = sgs.General(extension_pay_b,"akong","luadi",5,false,false,false)
akongA = sgs.General(extension_pay_b,"akongA","luadi",5,false,true,true)
akongB = sgs.General(extension_pay_b,"akongB","luadi",5,false,true,true)
akongC = sgs.General(extension_pay_b,"akongC","luadi",5,false,true,true)
akongD = sgs.General(extension_pay_b,"akongD","luadi",5,false,true,true)
akongO = sgs.General(extension_pay_b,"akongO","luadi",5,false,true,true)
mima = sgs.General(extension_pay_b,"mima","luaxi",3,false,false,false)

momiji = sgs.General(extension_pay_b,"momiji","luafeng",4,false,false,false)
momijiA = sgs.General(extension_pay_b,"momijiA","luafeng",4,false,true,true)
momijiB = sgs.General(extension_pay_b,"momijiB","luafeng",4,false,true,true)
momijiC = sgs.General(extension_pay_b,"momijiC","luafeng",4,false,true,true)
momijiD = sgs.General(extension_pay_b,"momijiD","luafeng",4,false,true,true)

accirno = sgs.General(extension_pay_b,"accirno","luacai",3,false,true,true)

suika = sgs.General(extension_pay_b,"suika","luacai",4,false,false,false)
suikaA = sgs.General(extension_pay_b,"suikaA","luacai",4,false,true,true)
suikaB = sgs.General(extension_pay_b,"suikaB","luacai",4,false,true,true)
suikaC = sgs.General(extension_pay_b,"suikaC","luacai",4,false,true,true)
suikaD = sgs.General(extension_pay_b,"suikaD","luacai",4,false,true,true)
suikaE = sgs.General(extension_pay_b,"suikaE","luacai",4,false,true,true)
kaguya = sgs.General(extension_pay_b,"kaguya","luayue",4,false,false,false)
kaguyaA = sgs.General(extension_pay_b,"kaguyaA","luayue",4,false,true,true)
kaguyaB = sgs.General(extension_pay_b,"kaguyaB","luayue",4,false,true,true)
kaguyaC = sgs.General(extension_pay_b,"kaguyaC","luayue",4,false,true,true)
kaguyaD = sgs.General(extension_pay_b,"kaguyaD","luayue",4,false,true,true)

ringoseiran = sgs.General(extension_pay_b,"ringoseiran","luayue",3,false,false,false)


local function isFriendQ(room, target, player)
	if player:getRole() == "rebel" then
		return target:getRole() == "rebel"
	end
	if player:getRole() == "loyalist" or player:getRole() == "lord" then
		return target:getRole() == "loyalist" or target:getRole() == "lord"
	end
end
luaaoshu = sgs.CreatePhaseChangeSkill{
	name = "luaaoshu",
	on_phasechange = function(self, player)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Draw then

			local target = room:askForPlayerChosen(player, room:getOtherPlayers(player), self:objectName(), "luaaoshu", true, false)
			if target and target:isAlive() then
				target:drawCards(1)
				local roomx = target:getRoom()
				local thread = roomx:getThread()
				local old_phase = target:getPhase()
				target:setPhase(sgs.Player_Draw)
				roomx:broadcastProperty(target, "phase")
				if not thread:trigger(sgs.EventPhaseStart, roomx, target) then
					thread:trigger(sgs.EventPhaseProceeding, roomx, target)
				end
				thread:trigger(sgs.EventPhaseEnd, roomx, target)
				target:setPhase(old_phase)
				roomx:broadcastProperty(target, "phase")

				local zuiduo = true
				for  _, kplayer in sgs.qlist(room:getAlivePlayers()) do
					if kplayer:getHandcardNum() > target:getHandcardNum() then zuiduo = false end
				end
				if zuiduo then
					local x = math.ceil(target:getHandcardNum()/2)
					for i = 1,x do
						local cardA = room:askForCardChosen(player, target, "he", self:objectName())
						room:obtainCard(player, cardA, false)
					end
				end
				return true
			end
		end
		return false
	end
}
danmuCard = sgs.CreateSkillCard{
	name = "luadanmu" ,
	target_fixed = true,
	on_use = function(self, room, source, targets)
		room:askForDiscard(source, self:objectName(), 99, 1, false, false, "luadanmu2")
		local plist = sgs.SPlayerList()
		for _,p in sgs.qlist(room:getAlivePlayers()) do
			if p:getHandcardNum() == source:getHandcardNum() and source:inMyAttackRange(p) then
				plist:append(p)
			end
		end
		local target = room:askForPlayerChosen(source, plist, "luadanmu", "luadanmu", true, true)
		if target then
			room:addPlayerMark(target, "@skill_invalidity")
			local dummy = sgs.Sanguosha:cloneCard("yuzhi", sgs.Card_NoSuit, 0)
			room:useCard(sgs.CardUseStruct(dummy, source, target))
		end
	end
}
luadanmu = sgs.CreateZeroCardViewAsSkill{
	name = "luadanmu" ,
	enabled_at_play = function(self, player)
		return player:canDiscard(player, "h") and not player:isNude()
	end ,
	view_as = function(self, cards)
		return danmuCard:clone()
	end,
}


marisa:addSkill(luaaoshu)
marisa:addSkill(luadanmu)

luayanfeng = sgs.CreateFilterSkill{
    name = "luayanfeng" ,
    view_filter = function(self, card)
        return card:objectName() == "slash" or card:objectName() == "fire_slash" or card:objectName() == "thunder_slash"
    end ,
    view_as = function(self, card)
        local slash = sgs.Sanguosha:cloneCard("fire_attack", card:getSuit(), card:getNumber())
        slash:setSkillName(self:objectName())
        local wrap = sgs.Sanguosha:getWrappedCard(card:getId())
        wrap:takeOver(slash)
        return wrap
    end
}
luayanfeng2 = sgs.CreateTriggerSkill{
	name = "#luayanfeng2" ,
	frequency = sgs.Skill_Compulsory ,
	events = {sgs.TargetConfirmed} ,
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		if not use.from then return false end
		local room = use.from:getRoom()
		if (player:objectName() ~= use.from:objectName()) or (not use.card:isKindOf("FireAttack")) then return false end
		if player:isKongcheng() then
			for _, p in sgs.qlist(use.to) do
				room:damage(sgs.DamageStruct(self:objectName(), use.from, p, 1, sgs.DamageStruct_Fire))
			end
		end
	end
}

luachongsheng = sgs.CreateTriggerSkill{
    name = "luachongsheng",
    frequency = sgs.Skill_NotFrequent,
    events = {sgs.EventPhaseChanging},
    can_trigger = function(self, target)
        return target and target:hasSkill(self:objectName()) and target:isAlive() and target:isWounded()
    end,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        local change = data:toPhaseChange()
        if change.to == sgs.Player_Draw and player:isWounded() and room:askForSkillInvoke(player, "luachongsheng") then
            room:recover(player, sgs.RecoverStruct(player, nil, player:getLostHp()))
            room:setPlayerFlag(player, "luachongsheng")
            player:gainMark("@luachongsheng")
        end
        return false
    end
}
luachongsheng2 = sgs.CreateTriggerSkill{
    name = "#luachongsheng",
    frequency = sgs.Skill_Frequent,
    events = {sgs.DrawNCards},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if player:hasFlag("luachongsheng") then
            local count = data:toInt() + 1
            data:setValue(count)
        end
    end
}
luachongsheng3 = sgs.CreateTriggerSkill{
    name = "#luachongsheng2" ,
    events = {sgs.DamageCaused} ,
    global = true,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if event == sgs.DamageCaused then
            local damage = data:toDamage()
			for _, shikieki in sgs.qlist(room:findPlayersBySkillName("luashuojiao")) do 
				if damage.to and damage.nature == sgs.DamageStruct_Thunder then
					return false 
				end
			end 
            if damage.to and damage.to:getMark("@luachongsheng") > 0 then
                damage.damage = damage.damage + damage.to:getMark("@luachongsheng")
                data:setValue(damage)
            end
        end
        return false
    end
}

newDuel = function(card)
	return sgs.Sanguosha:cloneCard("iron_chain", card:getSuit(), card:getNumber())
end
luahonghunCard = sgs.CreateSkillCard{
	name = "luahonghun" ,
	target_fixed = false ,
	will_throw = true ,
	filter = function(self, targets, to_select)
		if #targets == 0 then
			local card = self:getSubcards():first()
			local duel = newDuel(sgs.Sanguosha:getCard(card))
			duel:addSubcard(card)
			if to_select:isProhibited(sgs.Self, duel, sgs.Self:getSiblings()) then return false end
			if sgs.Self:isCardLimited(duel, sgs.Card_MethodUse) then return false end
			return true
		end
		return false
	end ,
	about_to_use = function(self, room, cardUse)
		local mokou = cardUse.from
		local logg = sgs.LogMessage()
		logg.from = mokou
		logg.to = cardUse.to
		logg.type = "#UseCard"
		logg.card_str = self:toString()
		room:sendLog(logg)
		local data = sgs.QVariant()
		data:setValue(cardUse)
		local thread = room:getThread()
		thread:trigger(sgs.PreCardUsed, room, mokou, data)
		local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_THROW, mokou:objectName(), nil, "luahonghun", nil)
		room:moveCardTo(self, mokou, nil, sgs.Player_DiscardPile, reason, true)
		thread:trigger(sgs.CardUsed, room, mokou, data)
		thread:trigger(sgs.CardFinished, room, mokou, data)
	end ,
	on_use = function(self, room, source, targets)
		local card = self:getSubcards():first()
		local humans = sgs.SPlayerList()
		humans:append(source)
		humans:append(targets[1])
		local from = source
		local duel = newDuel(sgs.Sanguosha:getCard(card))
		duel:addSubcard(card)
		duel:toTrick():setCancelable(true)
		duel:setSkillName(self:objectName())
		if (not from:isCardLimited(duel, sgs.Card_MethodUse)) and (not from:isProhibited(targets[1], duel)) then
			room:useCard(sgs.CardUseStruct(duel, from, humans))
		end
	end
}
luahonghun = sgs.CreateViewAsSkill{
	name = "luahonghun" ,
	n = 1 ,
	view_filter = function(self, cards, to_select)
		return (#cards == 0) and (not sgs.Self:isJilei(to_select))
	end ,
	view_as = function(self, cards)
		if #cards ~= 1 then return nil end
		local card = luahonghunCard:clone()
		card:addSubcard(cards[1])
		return card
	end ,
	enabled_at_play = function(self, target)
		return not target:hasUsed("#luahonghun")
	end
}


fujiwara:addSkill(luachongsheng)
fujiwara:addSkill(luachongsheng2)
fujiwara:addSkill(luachongsheng3)
fujiwara:addSkill(luayanfeng)
fujiwara:addSkill(luayanfeng2)
fujiwara:addSkill(luahonghun)


--[[luahakurei = sgs.CreateTriggerSkill{
	name = "luahakurei" ,
	events = {sgs.DamageCaused} ,
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		if (damage.nature == sgs.DamageStruct_Normal) and damage.by_user and damage.card and damage.to:objectName() ~= player:objectName()
			and damage.from:hasSkill(self:objectName()) then
			local room = player:getRoom()
			if damage.card:isKindOf("AOE") and damage.to:getHp() == 1 then  --use for AI
				--room:setPlayerFlag(player, "luahakurei_AOE")
				room:setTag("luahakurei_AOE", sgs.QVariant(1))
				room:writeToConsole("AI正在使用一张AOE")
			end 
			if player:askForSkillInvoke(self:objectName(), data) then
				room:setPlayerFlag(player, "luahakurei_used")
				local pattern
				if damage.card:isKindOf("BasicCard") then
					pattern = "BasicCard"
				elseif damage.card:isKindOf("TrickCard") then
					pattern = "TrickCard"
				elseif damage.card:isKindOf("EquipCard") then
					pattern = "EquipCard"
				end
				local function huase2(card)
					local huase
					if card:getSuit() == sgs.Card_Heart then
						huase = "heart"
					elseif card:getSuit() == sgs.Card_Diamond then
						huase = "diamond"
					elseif card:getSuit() == sgs.Card_Club then
						huase = "club"
					elseif card:getSuit() == sgs.Card_Spade then
						huase = "spade"
					end
					return huase
				end 
				
				local judge = sgs.JudgeStruct()
				judge.pattern = pattern .. "|"..huase2(damage.card)
				judge.good = true
				judge.who = player
				judge.reason = self:objectName()
				room:judge(judge)
				local card = judge.card
				if damage.card:getSuit() ~= card:getSuit() or not card:isKindOf(pattern) then
					
					local x = damage.to:getHp() - 1
					if damage.card:getSuit() ~= card:getSuit() and x > 0 then 						
						damage.damage = damage.damage + x
						data:setValue(damage)
						room:setTag("luahakurei2", sgs.QVariant(x))
						local p = damage.to
						local playerdata = sgs.QVariant()
						playerdata:setValue(p)
						room:setTag("luahakurei2Target", playerdata)
					end 
					if not card:isKindOf(pattern) then 
						local skills = damage.to:getVisibleSkillList()
						if skills:length() > 0 then 
							local detachList = {}
							local gainList = {}
							for _,skill in sgs.qlist(skills) do
								if not skill:inherits("SPConvertSkill") and not skill:isAttachedLordSkill() then
									table.insert(detachList,"-"..skill:objectName())
									table.insert(gainList,skill:objectName())
								end
							end
							room:handleAcquireDetachSkills(damage.to, table.concat(detachList,"|"))
							if damage.to:isAlive() then
								damage.to:gainMark("@luahakurei")
								room:writeToConsole(damage.to:objectName().."失去了以下技能"..table.concat(gainList,"|"))
								room:setTag("luahakurei", sgs.QVariant(table.concat(gainList,"|")))
							end
						end 
					end 
					return false
				end
				room:setPlayerFlag(player, "-luahakurei_AOE")
			end
		end
		return false
	end,
	can_trigger = function(self, target)
		return not target:hasFlag("luahakurei_used")
	end
}
luahakureiGain = sgs.CreateTriggerSkill{
	name = "#luahakureiGain",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart then
			local playerp
			local all_players = room:getAllPlayers()
			for _, splayer in sgs.qlist(all_players) do
				if splayer:getMark("@luahakurei") > 0 and splayer:isAlive()  then
					playerp = splayer
				end
			end	
			if not room:getTag("luahakurei") then room:writeToConsole("灵梦时空传送丢失") end
			if player:getPhase() == sgs.Player_Start and playerp and room:getTag("luahakurei") and room:getTag("luahakurei"):toString() ~= ""
				and player:hasSkill("luahakurei") then
				local gainList = room:getTag("luahakurei"):toString()
				gainList = gainList:split("|")
				room:removeTag("luahakurei")
				playerp:loseAllMarks("@luahakurei")
				for i = 1,#gainList do 
					room:writeToConsole(playerp:objectName().."得到了以下技能"..gainList[i])
					room:acquireSkill(playerp, gainList[i])
				end 
			end
		end
	end
}
luahakureiRecover = sgs.CreateTriggerSkill{
	name = "#luahakureiRecover" ,
	events = {sgs.Damaged},
	global = true, --究极重要！全场技能必备，花了5个小时买一场教训！
	frequency = sgs.Skill_NotFrequent,
	on_trigger = function(self, event, player, data, room)
		if event == sgs.Damaged then
			for _, p in sgs.qlist(room:findPlayersBySkillName("luahakurei")) do
				local room = p:getRoom()
				local target = room:getTag("luahakurei2Target")
				if room:getTag("luahakurei2") and room:getTag("luahakurei2"):toInt() > 0 and player:isAlive() 
					and target and target:toPlayer() and target:toPlayer():objectName() == player:objectName() then
					local x = room:getTag("luahakurei2"):toInt()
					room:removeTag("luahakurei2")					
					room:recover(player, sgs.RecoverStruct(player, nil, x))
					room:removeTag("luahakurei2Target")		
				end
			end
		end 
	end 
}]]--
luahakurei4 = sgs.CreateTargetModSkill{
	name = "#luahakurei3",
	pattern = "Slash",
	residue_func = function(self, player, card)
		if player:hasFlag("luahakurei_unlimited") or card:getSkillName() == "luahakurei2" then
			return 1
		end
	end,
    distance_limit_func = function(self, player, card)
        if card:getSkillName() == "luahakurei2" then
            return 999
        else
            return 0
        end
    end
}
luahakurei = sgs.CreateOneCardViewAsSkill{
	name = "luahakurei",
	--filter_pattern = ".|.|.|hand",
	view_filter = function(self, card)
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
			slash:addSubcard(card:getEffectiveId())
			slash:setSkillName("luahakurei2")
			slash:deleteLater()
			return slash:isAvailable(sgs.Self)
		end
		return false
	end,
	view_as = function(self, originalCard)
		local Leishi_card = luahakureiCard:clone()
		Leishi_card:addSubcard(originalCard:getId())
		return Leishi_card
	end,
	enabled_at_play = function(self, player)
		return not player:hasFlag("luahakurei_used")
	end,
}
luahakureiCard = sgs.CreateSkillCard{
	name = "luahakurei",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select)
		if to_select:objectName() == sgs.Self:objectName() then return false end
		if #targets ~= 0 then return false end
		return true --sgs.Self:inMyAttackRange(to_select)
	end,
	on_validate = function(self,carduse)
		local source = carduse.from
		local target = carduse.to:first()
		local room = source:getRoom()
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		slash:addSubcard(card:getEffectiveId())

		--八云紫相关
		room:writeToConsole("luashishenyCE0")
		local xx = source:getTag("luashisheny")
		if not xx then 
			xx = ""
		else
			xx = xx:toString()
		end 
		xx = analysisString(xx, "hakurei")	
		room:writeToConsole("luashishenyCE0 " .. xx)
		source:setTag("luashisheny", sgs.QVariant(xx)) 
		
		if source:inMyAttackRange(target) and slash:isAvailable(source) then
			if source:canSlash(target, slash, true) then
                local _data = sgs.QVariant()
                _data:setValue(target)
				local choice = room:askForChoice(source, "luahakurei", "luahakurei1+luahakurei2", _data)
				if choice == "luahakurei1" then
					room:setPlayerFlag(source,"luahakurei_unlimited")
					room:setPlayerFlag(source,"luahakurei_used")
					return slash
				else
					source:drawCards(1)
					room:setPlayerFlag(source,"luahakurei_used")
					return slash
				end
			end
		elseif not slash:isAvailable(source) and source:inMyAttackRange(target) then
			if source:canSlash(target, slash, true) then
				room:setPlayerFlag(source,"luahakurei_unlimited")
				room:setPlayerFlag(source,"luahakurei_used")
				return slash
			end
		elseif not source:inMyAttackRange(target) and slash:isAvailable(source) then
			if source:canSlash(target, slash, false) then
				room:setPlayerFlag(source,"luahakurei_used")
				return slash
			end
		end
		room:setPlayerFlag(source,"luahakurei_used")
	end,
}
luahakureiX = sgs.CreateTriggerSkill{
	name = "#luahakureiX" ,
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseEnd} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			room:setPlayerFlag(p,"-luahakurei_unlimited")
			room:setPlayerFlag(p,"-luahakurei_used")
		end
	end
}
LuaFengyin = sgs.CreateDistanceSkill{
	name = "LuaFengyin",
	correct_func = function(self, from)
		if from:getPhase() == sgs.Player_Play then
			return 0
		else
			return 0
		end
	end,
}
LuaFengyinSlash = sgs.CreateTriggerSkill {
	name = "#LuaFengyinSlash",
	events = {sgs.TargetSpecified},
	frequency = sgs.Skill_Compulsory,
	can_trigger = function(self, target)
		return target and target:isAlive() 
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local use = data:toCardUse()	
		if event == sgs.TargetSpecified and player:hasSkill("LuaFengyin") and player:isAlive() and use.card:isKindOf("Slash")  then
			local function huase2(card)
			local huase
				if card:getSuit() == sgs.Card_Heart then
					huase = "heart"
				elseif card:getSuit() == sgs.Card_Diamond then
					huase = "diamond"
				elseif card:getSuit() == sgs.Card_Club then
					huase = "club"
				elseif card:getSuit() == sgs.Card_Spade then
					huase = "spade"
				end
				if not huase then return "." end
				return huase
			end 

			for _, p in sgs.qlist(use.to) do
				if p:objectName() ~= player:objectName() then
					local patternX = {}
					if p:getTag("LuaFengyinSlash"):toString() and p:getTag("LuaFengyinSlash"):toString() ~= "" then
						if p:getTag("LuaFengyinSlash"):toString() == "." then return false end
						patternX = p:getTag("LuaFengyinSlash"):toString()
						room:removePlayerCardLimitation(p,  "use,response", ".|"..patternX.."|.|hand$0") 
						room:writeToConsole("reimu test SS" .. patternX)
						patternX = patternX:split(",")
					end
					if huase2(use.card) == "." then
						return false
					end
					local ccan = true
					for _, huaseX in ipairs(patternX) do
						if huase2(use.card) == huaseX then
							ccan = false
						end  
					end
					if ccan then table.insert(patternX,huase2(use.card)) end  --BUG
					patternX = table.concat(patternX, ",")
					room:writeToConsole("reimu test 2 " .. patternX) 
					local pattern = ".|".. patternX .."|.|hand"
					room:setPlayerCardLimitation(p, "use,response", pattern, false)
					
					p:setTag("LuaFengyinSlash", sgs.QVariant(patternX))
					room:writeToConsole("LuaFengyinSlash " .. p:getTag("LuaFengyinSlash"):toString())
					p:gainMark("@LuaFengyin")
				end 
			end 
			return false
		end
	end
}		
LuaFengyinClear = sgs.CreateTriggerSkill{
	name = "#LuaFengyin-clear" ,
	frequency = sgs.Skill_Compulsory,
	global = true, --究极重要！全场技能必备，花了5个小时买一场教训！
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		if event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				local room = player:getRoom()
				if player:getMark("@LuaFengyin") > 0 then room:writeToConsole("LuaFengyinSlashQ ") end
				if player:getTag("LuaFengyinSlash") then room:writeToConsole("LuaFengyinSlashQ ") end
				if player:getMark("@LuaFengyin") > 0 and player:getTag("LuaFengyinSlash") and player:getTag("LuaFengyinSlash"):toString() ~= "" then
					local str1 = player:getTag("LuaFengyinSlash"):toString()
					room:writeToConsole("reimu test 3 " .. str1)
					for i = 1,49 do
						room:removePlayerCardLimitation(player,  "use,response", ".|"..str1.."|.|hand$0")
					end
					player:removeTag("LuaFengyinSlash")
					player:loseAllMarks("@LuaFengyin")
				end 
				return false
			end 
		end
		return false
	end ,
	can_trigger = function(self, target)
		local room = target:getRoom() 
		return target:getTag("LuaFengyinSlash"):toString() ~= ""
	end
}
reimu:addSkill(luahakurei)
reimu:addSkill(luahakureiX)
reimu:addSkill(luahakurei4)
reimu:addSkill(LuaFengyin)
reimu:addSkill(LuaFengyinSlash)
reimu:addSkill(LuaFengyinClear)

reimuA:addSkill(luahakurei)
reimuA:addSkill(luahakurei4)
reimuA:addSkill(LuaFengyin)
reimuA:addSkill(LuaFengyinSlash)
reimuA:addSkill(LuaFengyinClear)

reimuB:addSkill(luahakurei)
reimuB:addSkill(luahakurei4)
reimuB:addSkill(LuaFengyin)
reimuB:addSkill(LuaFengyinSlash)
reimuB:addSkill(LuaFengyinClear)

reimuC:addSkill(luahakurei)
reimuC:addSkill(luahakurei4)
reimuC:addSkill(LuaFengyin)
reimuC:addSkill(LuaFengyinSlash)
reimuC:addSkill(LuaFengyinClear)

reimuD:addSkill(luahakurei)
reimuD:addSkill(luahakurei4)
reimuD:addSkill(LuaFengyin)
reimuD:addSkill(LuaFengyinSlash)
reimuD:addSkill(LuaFengyinClear)
--
reimuE:addSkill(luahakurei)
reimuE:addSkill(luahakurei4)
reimuE:addSkill(LuaFengyin)
reimuE:addSkill(LuaFengyinSlash)
reimuE:addSkill(LuaFengyinClear)
--
reimuX:addSkill(luahakurei)
reimuX:addSkill(luahakurei4)
reimuX:addSkill(LuaFengyin)
reimuX:addSkill(LuaFengyinSlash)
reimuX:addSkill(LuaFengyinClear)



luayanxun = sgs.CreateTriggerSkill{
	name = "luayanxun" ,
	events = {sgs.Damage} ,
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local room = player:getRoom()
		if damage.by_user and damage.from:hasSkill(self:objectName()) and (not damage.from:hasFlag("luayanxun")) and damage.to and damage.to:isAlive()
			and (not damage.to:isNude()) and room:askForSkillInvoke(player, "luayanxun", data) then
			if player:getMark("@luahuaxian") < 4 then
				room:recover(damage.to, sgs.RecoverStruct(player, nil, 1))
			end
			local playerdata = sgs.QVariant() --ai用
			playerdata:setValue(damage.to)
			room:setTag("luayanxund", playerdata)
			local to_throw = room:askForCardChosen(player, damage.to, "he", self:objectName(), false, sgs.Card_MethodDiscard)
			room:removeTag("luayanxund")
			if player:getMark("@luahuaxian") < 4 then
				room:obtainCard(player, to_throw, false)
			else
				room:throwCard(sgs.Sanguosha:getCard(to_throw), damage.to, player)
			end
			player:drawCards(1)
			room:setPlayerFlag(player, "luayanxun")
		end
		return false
	end
}
luayanxun2 = sgs.CreateProhibitSkill{
    name = "#luayanxun",
    is_prohibited = function(self, from, to, card)
        return to:hasSkill("luayanxun") and card:getSuit() == sgs.Card_Diamond and from:objectName() ~= to:objectName()
			and not card:isKindOf("SkillCard")
	end
}

luahuaxian = sgs.CreateTriggerSkill
{
	name = "luahuaxian",
	events = {sgs.CardUsed},
	frequency = sgs.Skill_Wake,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local card
		if event == sgs.CardUsed then
			card = data:toCardUse().card
			if card:isKindOf("EquipCard") then
				player:gainMark("@luahuaxian")--[[
					room:setPlayerMark(player, "huaxianW", 1)
				elseif card:isKindOf("Armor") and player:getMark("huaxianA") == 0 then

					player:gainMark("@luahuaxian")
					room:setPlayerMark(player, "huaxianA", 1)
				elseif card:isKindOf("DefensiveHorse") and player:getMark("huaxianD") == 0  then
					player:gainMark("@luahuaxian")
					room:setPlayerMark(player, "huaxianD", 1)
				elseif card:isKindOf("OffensiveHorse") and player:getMark("huaxianO") == 0  then
					player:gainMark("@luahuaxian")
					room:setPlayerMark(player, "huaxianO", 1)
				elseif card:isKindOf("Treasure") and player:getMark("huaxianT") == 0  then
					player:gainMark("@luahuaxian")
					room:setPlayerMark(player, "huaxianT", 1)
				end]]--

				if player:getMark("@luahuaxian") == 4 then
					room:setPlayerProperty(player, "maxhp", sgs.QVariant(player:getMaxHp() + 1))
					room:recover(player, sgs.RecoverStruct(player, nil, 1))
					room:acquireSkill(player, "luaxiedao")
					room:acquireSkill(player, "luaguiwan")
				end
			end
		end
	end
}
luaxiedao2 = sgs.CreateTriggerSkill{
	name = "#luaxiedao" ,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damage} ,
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local room = player:getRoom()
		if damage.by_user then
			room:setPlayerFlag(player, "luaxiedao")
		end
		return false
	end
}
huashan:addSkill(luayanxun)
huashan:addSkill(luayanxun2)
huashan:addSkill(luaxiedao2)
huashan:addSkill(luahuaxian)

LuaYuanzu = sgs.CreateViewAsSkill{
	name = "LuaYuanzu" ,
	n = 1 ,
	view_filter = function(self, selected, to_select)
		if #selected > 0 then return false end
		local card = to_select
		local usereason = sgs.Sanguosha:getCurrentCardUseReason()
		if usereason == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			return card:isRed() and not card:isEquipped()
		elseif (usereason == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE) or (usereason == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE) then
			local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
			if pattern == "slash" then
				return card:isRed() and not card:isEquipped()
			elseif pattern == "jink" then 
				return card:isRed() and not card:isEquipped()
			end
		else
			return false
		end
	end ,
	view_as = function(self, cards)
		if #cards ~= 1 then return nil end
		local originalCard = cards[1]
		local usereason = sgs.Sanguosha:getCurrentCardUseReason()
		if usereason == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			local slash = sgs.Sanguosha:cloneCard("slash", originalCard:getSuit(), originalCard:getNumber())
			slash:addSubcard(originalCard)
			slash:setSkillName(self:objectName())
			return slash
		else 
			--local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
			local n = sgs.Self:getMark("LuaYuanzu")
			if (not n) or n == 0 then n = 3 end 
			if n == 3 then
				local slash = sgs.Sanguosha:cloneCard("slash", originalCard:getSuit(), originalCard:getNumber())
				slash:addSubcard(originalCard)
				slash:setSkillName(self:objectName())
				return slash
			elseif n == 1 then 
				local jink = sgs.Sanguosha:cloneCard("jink", originalCard:getSuit(), originalCard:getNumber())
				jink:addSubcard(originalCard)
				jink:setSkillName(self:objectName())
				return jink
			else
				return nil
			end			
		end
	end ,
	enabled_at_play = function(self, target)
		return sgs.Slash_IsAvailable(target)
	end,
	enabled_at_response = function(self, target, pattern)
		return (pattern == "slash")
				or (pattern == "jink")
	end,
}
LuaYuanzu2 = sgs.CreateTriggerSkill{
	name = "#LuaYuanzu",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardAsked},
	on_trigger = function(self,event,player,data)
		if player:getPhase() ~= sgs.Player_NotActive then return end
		local room = player:getRoom()
		local pattern = data:toStringList()[1]
		if (pattern == "jink") then 
			room:setPlayerMark(player, "LuaYuanzu", 1)
		elseif (pattern == "slash") then 
			room:setPlayerMark(player, "LuaYuanzu", 3)
		end
	end,
}
LuaYuanzu3 = sgs.CreateTriggerSkill{
	name = "#LuaYuanzu2" ,
	events = {sgs.CardResponded, sgs.TargetConfirmed} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardResponded then
			local resp = data:toCardResponse()
			if (resp.m_card:getSkillName() == "LuaYuanzu") then
				local thc = sgs.Sanguosha:getCard(resp.m_card:getSubcards():first())

				local light = sgs.Sanguosha:cloneCard("light", thc:getSuit(), thc:getNumber())
				light:addSubcard(thc)
				room:writeToConsole(light:objectName())
				local use_X = sgs.CardUseStruct()
				use_X.card = light
				use_X.from = player
				use_X.to:append(player)
				room:useCard(use_X, false)		
			end
		else
			local use = data:toCardUse()
			if use.from and (use.from:objectName() == player:objectName()) and (use.card:getSkillName() == "LuaYuanzu") then
				local thc = sgs.Sanguosha:getCard(use.card:getSubcards():first())
				local light = sgs.Sanguosha:cloneCard("light", thc:getSuit(), thc:getNumber())
				light:addSubcard(thc)
			
				local use_X = sgs.CardUseStruct()
				use_X.card = light
				use_X.from = player
				use_X.to:append(player)
				room:useCard(use_X, false)				
				-- local ids = sgs.IntList()
				-- ids:append(thc:getId())
				-- local moveX = sgs.CardsMoveStruct()
				-- moveX.card_ids = ids
				-- moveX.from = nil
				-- moveX.to = player
				-- moveX.to_place = sgs.Player_PlaceJudge
				-- moveX.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, player:objectName(), "LuaYuanzu", "")
				-- room:moveCardsAtomic(moveX, true)		
	        	--room:moveCardTo(light, player, player, sgs.Player_PlaceJudge, reason)	
			end
		end
		return false
	end
}

    -- bool askForDiscard(ServerPlayer *target, const QString &reason, int discard_num, int min_num,
        -- bool optional = false, bool include_equip = false, const QString &prompt = QString(), const QString &pattern = ".");
LuaShanguang = sgs.CreateTriggerSkill{
	name = "LuaShanguang" ,
	events = {sgs.DamageCaused} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.DamageCaused then
			local damage = data:toDamage()
			if damage.card and damage.card:isKindOf("Slash")
					and damage.by_user and (not damage.chain) and (not damage.transfer) then
				if player:containsTrick("light") and player:askForSkillInvoke(self:objectName(), data) then
					local choice1 = {}
					local count = player:getJudgingArea():length()
					local i = 1
					while i <= count do 
						table.insert(choice1,tostring(i))					
						i = i + 1 
					end 					
					local choice = room:askForChoice(player, "LuaShanguang",table.concat(choice1,"+"), data)
					count = tonumber(choice)
					local ids = sgs.IntList()
					local ids_x = {}
					for J = 1, choice do
						local id = room:askForCardChosen(player, player, "j", "LuaShanguang", false, sgs.Card_MethodDiscard, ids)
						ids:append(id)
						table.insert(ids_x,tostring(id))					
						room:setTag("LuaShanguang", sgs.QVariant(table.concat(ids_x,"|")))	
					end 
					room:removeTag("LuaShanguang")
					local moveX = sgs.CardsMoveStruct()
					moveX.card_ids = ids
					moveX.from = nil
					moveX.to = nil
					moveX.to_place = sgs.Player_DiscardPile
					moveX.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_DISCARD, player:objectName(), "LuaYuanzu", "")
					room:moveCardsAtomic(moveX, true)
					damage.damage = count
					data:setValue(damage)
					
					if count > 0 then 
						player:drawCards(1)
						if count > 1 then 
							if not damage.to:isNude() then 
								local id = room:askForCardChosen(player, damage.to, "he", "LuaShanguang", false)
								room:throwCard(id, damage.to)
								local thc = sgs.Sanguosha:getCard(id)
								local light = sgs.Sanguosha:cloneCard("light", thc:getSuit(), thc:getNumber())
								light:addSubcard(thc)						
								local use_X = sgs.CardUseStruct()
								use_X.card = light
								use_X.from = player
								use_X.to:append(player)
								room:useCard(use_X, false)							
							end 
							if count > 2 then 
								if not player:hasSkill("wansha") then room:acquireSkill(player, "wansha") end 
								if count > 3 then 
									if not player:hasSkill("lualianpo") then room:acquireSkill(player, "lualianpo") end 
								end 
							end 
						end 
					end 
					return false
				end
			end
		end 
	end 
}
LuaShanguang2 = sgs.CreateTriggerSkill{
	name = "#LuaShanguang" ,
	events = {sgs.EventPhaseChanging} ,
	--frequency = sgs.Skill_Frequent , 这句话源代码没有，但是我感觉应该加上，毕竟连破一点副作用都没有
	priority = 1,
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		if change.to ~= sgs.Player_NotActive then return false end 
		local room = player:getRoom()
		for _, mima in sgs.qlist(room:findPlayersBySkillName("LuaShanguang")) do
			local p = mima
			if not p then return false end 
			local room = p:getRoom()
			if p and p:hasSkill("wansha") then room:detachSkillFromPlayer(p, "wansha", false, true) end 
			if p and p:hasSkill("lualianpo") and (p:getMark("lualianpo") <= 0) then room:detachSkillFromPlayer(p, "lualianpo", false, true) end 
		end 
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end
}

lualianpoCount = sgs.CreateTriggerSkill{
	name = "#lualianpo-count" ,
	events = {sgs.Death, sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		if event == sgs.Death then
			local death = data:toDeath() 
			if death.who:objectName() ~= player:objectName() then return false end 
			local killer
			if death.damage then
				killer = death.damage.from
			else
				killer = nil
			end
			local current = player:getRoom():getCurrent()
			if killer and current and current:isAlive() and (current:getPhase() ~= sgs.Playr_NotActive) then 
				killer:addMark("lualianpo")
			end
		elseif player:getPhase() == sgs.Player_NotActive then
			for _, p in sgs.qlist(player:getRoom():getAlivePlayers()) do
				p:setMark("lualianpo", 0)
			end
		end
	end,
	can_trigger = function(self,target)
		return target
	end
}

lualianpoDo = sgs.CreateTriggerSkill{
	name = "#lualianpo-do" ,
	events = {sgs.EventPhaseStart},
	priority = 1 ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:getTag("lualianpoInvoke") then
			local target = room:getTag("lualianpoInvoke"):toPlayer()
			room:removeTag("lualianpoInvoke")
			if target and target:isAlive() then
				room:detachSkillFromPlayer(target, "lualianpo", false, true)
				room:setPlayerMark(target, "@extra_turn", 1)
				target:gainAnExtraTurn()
				room:setPlayerMark(target, "@extra_turn", 0)
			end
		end
	end,
	can_trigger = function(self, target)
		return target and (target:getPhase() == sgs.Player_NotActive)
	end
}

mima:addSkill(LuaYuanzu)
mima:addSkill(LuaYuanzu2)
mima:addSkill(LuaYuanzu3)
mima:addSkill(LuaShanguang)
mima:addSkill(lualianpoCount)
mima:addSkill(lualianpoDo)
mima:addSkill(LuaShanguang2)
LuaGuishen = sgs.CreateTriggerSkill{
	name = "LuaGuishen" ,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (player:getPhase() == sgs.Player_Play) and (not player:isKongcheng())
			and room:askForUseSlashTo(player, room:getAlivePlayers(), self:objectName(), false, false, false) then
			player:drawCards(1)
		end 
	end 
}

LuaBisha = sgs.CreateTriggerSkill {
	name = "LuaBisha",
	events = {sgs.TargetConfirming},
	can_trigger = function(self, target)
		return target and target:isAlive() 
	end,
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		if not use.from then return false end 
		local room = use.from:getRoom()
		if event == sgs.TargetConfirming and use.from:hasSkill("LuaBisha") and use.from:isAlive() and not use.card:hasFlag(self:objectName()) then
			room:setCardFlag(use.card, self:objectName())
			for _, p in sgs.qlist(use.to) do
				if p:objectName() ~= use.from:objectName() then
					p:gainMark("@LuaBisha2")
					local _data = sgs.QVariant()
					_data:setValue(p)
					if p:getMark("@LuaBisha2") == 3 and not use.from:hasFlag("LuaBishaX") and use.from:askForSkillInvoke(self:objectName(), _data)  then
						room:setPlayerFlag(use.from, "LuaBishaX")
						local num = p:getHp()
						room:setPlayerProperty(p, "hp", sgs.QVariant(0))
						local damage = sgs.DamageStruct()
						damage.card = nil
						damage.from = use.from
						damage.to = p
						room:enterDying(p, damage)
						if p:isAlive() then
							local num2 = p:getHp()
							num2 = num + num2
							num2 = math.min(num2,p:getMaxHp())
							room:setPlayerProperty(p, "hp", sgs.QVariant(num2))
						else
							return true
						end
					end
				end
			end 
			return false
		end 
	end
}		
LuaBishaClear = sgs.CreateTriggerSkill{
	name = "#LuaBisha-clear" ,
	frequency = sgs.Skill_Compulsory,
	global = true, --究极重要！全场技能必备，花了5个小时买一场教训！
	events = {sgs.EventPhaseChanging, sgs.Death} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				for _, p in sgs.qlist(room:getAllPlayers()) do
					local room = p:getRoom()
					if p:getMark("@LuaBisha2") > 0 then 
						p:loseAllMarks("@LuaBisha2")
					end 
				end 
				return false
			end 
		else
			if data:toDeath().who:objectName() == player:objectName() then
				for _, p in sgs.qlist(room:getAllPlayers()) do
					local room = p:getRoom()
					if p:getMark("@LuaBisha2") > 0 then 
						p:loseAllMarks("@LuaBisha2")
					end 
				end 
				return false			
			end 
		end
		return false
	end ,
}
yugi:addSkill(LuaGuishen)
yugi:addSkill(LuaBisha)
yugi:addSkill(LuaBishaClear)

LuaYaobancard = sgs.CreateSkillCard{
	name = "LuaYaoban", --必须
	target_fixed = false,
	will_throw = true, --必须
	skill_name = "LuaYaoban",
	filter = function(self, targets, to_select) --必须
		if #targets == 0 then return true end 
		return (#targets <= 99) and not to_select:isChained()
	end,
	on_use = function(self, room, source, targets)
		room:writeToConsole("灵乌路空测试0")
		if #targets == 1 then 
			if not (targets[1]:isChained()) then 
				local choices = {"damage", "chain"}
				local choice = room:askForChoice(source, "LuaYaoban2", table.concat(choices, "+"))		
				if choice == "damage" then 
					room:damage(sgs.DamageStruct("LuaYaoban", source, targets[1], 1, sgs.DamageStruct_Fire))
				else
					room:setPlayerProperty(targets[1], "chained", sgs.QVariant(true))
					room:setEmotion(targets[1], "chain")
					source:drawCards(1)
				end 
			else
				room:damage(sgs.DamageStruct("LuaYaoban", source, targets[1], 1, sgs.DamageStruct_Fire))
			end 
		else
			for _, p in pairs(targets) do
				room:setPlayerProperty(p, "chained", sgs.QVariant(true))
				room:setEmotion(p, "chain")
			end 
			source:drawCards(1)
		end 
		--if not source:hasFlag("Yaoban_damage") then source:drawCards(1) end 
	end,
}
LuaYaoban = sgs.CreateViewAsSkill{
	name = "LuaYaoban", --必须
	n = 3, --使用卡牌最大数目,必须
	view_filter = function(self, selected, to_select) 
		if to_select:isEquipped() or sgs.Self:isJilei(to_select) then
			return false
		end
		if #selected ~= 0 then 
			for _, card in ipairs(selected) do
				if card:getSuit() ~= to_select:getSuit() then return false end
			end
		end 
		return true
	end, 
	view_as = function(self, cards) --必须
		local LuaYaobanCard = LuaYaobancard:clone()
		if #cards < 3 then return nil end
		for _,card in ipairs(cards) do
			LuaYaobanCard:addSubcard(card)
		end
		return LuaYaobanCard
	end, 
}

LuaXinXing = sgs.CreateTriggerSkill{
	name = "LuaXinXing",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		if player:getPhase() ~= sgs.Player_Draw then
			return false
		end
		local room = player:getRoom()
		if not player:askForSkillInvoke(self:objectName()) then
			return false
		end
		local card_ids = room:getNCards(4)
		room:fillAG(card_ids)
		while (not card_ids:isEmpty()) do
			local card_id = room:askForAG(player, card_ids, false, self:objectName())
			card_ids:removeOne(card_id)
			local card = sgs.Sanguosha:getCard(card_id)
			local suit = card:getSuit()
			room:takeAG(player, card_id)
			local removelist = {}
			for _,id in sgs.qlist(card_ids) do
				local c = sgs.Sanguosha:getCard(id)
				if c:getSuit() ~= suit then
					room:takeAG(nil, c:getId())
					table.insert(removelist, id)
				end
			end
			if #removelist > 0 then
				for _,id in ipairs(removelist) do
					if card_ids:contains(id) then
						card_ids:removeOne(id)
					end
				end
			end
		end
		room:broadcastInvoke("clearAG")
		room:clearAG()
		return true
	end
}

LuaHebao = sgs.CreateTriggerSkill{
	name = "LuaHebao" ,
	events = {sgs.CardsMoveOneTime} ,
	frequency = sgs.Skill_Wake ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local move = data:toMoveOneTime()
		room:writeToConsole("test 11111")
		if move.to and move.to:objectName() == player:objectName() and move.to_place == sgs.Player_PlaceHand and not move.card_ids:isEmpty() then
			room:addPlayerMark(player, "LuaHebao")
			if room:changeMaxHpForAwakenSkill(player, -2) then
				room:handleAcquireDetachSkills(player, "zonghuo|LuaJubian")
			end
		end 
		return false
	end ,
	can_trigger = function(self, target)
		return (target and target:isAlive() and target:hasSkill(self:objectName()))
				and (target:getMark("LuaHebao") == 0)
				and (target:getHandcardNum() >= 7)
	end
}
LuaHebaosdf = sgs.CreateTriggerSkill{
	name = "LuaHebaosdf",
	frequency = sgs.Skill_Limited,
	events = {sgs.GameStart},
	on_trigger = function()
	end
}

akong:addSkill(LuaYaoban)
akong:addSkill(LuaXinXing)
akong:addSkill(LuaHebaosdf)

akongA:addSkill(LuaYaoban)
akongA:addSkill(LuaXinXing)
akongA:addSkill(LuaHebao)

akongB:addSkill(LuaYaoban)
akongB:addSkill(LuaXinXing)
akongB:addSkill(LuaHebao)

akongC:addSkill(LuaYaoban)
akongC:addSkill(LuaXinXing)
akongC:addSkill(LuaHebao)

akongD:addSkill(LuaYaoban)
akongD:addSkill(LuaXinXing)
akongD:addSkill(LuaHebao)

akongO:addSkill(LuaYaoban)
akongO:addSkill(LuaXinXing)
akongO:addSkill(LuaHebao)

luashaojie = sgs.CreateTriggerSkill{
	name = "luashaojie",
	global = true,
	events = {sgs.EventPhaseStart, sgs.DamageForseen},
	--frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data, room)
		if room:getCurrent():isLord() and player:getMark("@extra_turn") == 0 and room:getCurrent():objectName() == player:objectName()
				and player:getPhase() == sgs.Player_Start then
			for _, momiji in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				for _,p in sgs.qlist(room:getAlivePlayers()) do
					if p:getMark("luashaojie" .. momiji:objectName()) > 0 then
						p:loseAllMarks("@luashaojie")
					end 
				end
				local xp = room:askForPlayerChosen(momiji, room:getAlivePlayers(), "luashaojie", "luashaojie", true)
				if xp and xp:isAlive() then
					xp:gainMark("@luashaojie")
					room:setPlayerMark(xp, "luashaojie" .. momiji:objectName(), 1)
				end
			end
		elseif event == sgs.DamageForseen then
			local damage = data:toDamage() 
			if damage.to and damage.to:isAlive() and (damage.to:getMark("@luashaojie") > 0) then
				for _, momiji in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do 
					if momiji and momiji:isAlive() and damage.to:getMark("luashaojie" .. momiji:objectName()) > 0 then
						local function canLoseHp()
							for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
								if hecatiaX and isFriendQ(room, momiji, hecatiaX) and momiji:objectName() ~= hecatiaX:objectName()
										and momiji:getHp() == hecatiaX:getHp() then
									room:notifySkillInvoked(hecatiaX, "luayiti")
									return false
								end 
							end 
							for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
								if Erin and Erin:getKingdom() == momiji:getKingdom() then
									room:notifySkillInvoked(Erin, "luajiance")
									return false
								end 
							end 
							return true
						end 
						if canLoseHp() then room:loseHp(momiji, 1) end 
						return true
					end
				end 
			end
		end
	end
}

lualangshiCard = sgs.CreateSkillCard{
	name = "lualangshi",
	filter = function(self, targets, to_select)
		return not to_select:isKongcheng()
	end,
	on_use = function(self, room, source, targets)
		for _, p in ipairs(targets) do
			local range_list = sgs.IntList()
			for _, card in sgs.list(p:getHandcards()) do
				range_list:append(card:getEffectiveId())
				local flag = string.format("%s_%s_%s","visible", source:objectName(), p:objectName())
				if not card:hasFlag("visible") then card:setFlags(flag) end
			end
			room:fillAG(range_list, source)
			room:getThread():delay(2500)
			room:clearAG()
		end
		room:setPlayerFlag(source, "lualangshiB")
	end
}
lualangshiVS = sgs.CreateZeroCardViewAsSkill{
	name = "lualangshi",
	response_pattern = "@@lualangshi",
	view_as = function(self, cards)
		return lualangshiCard:clone()
	end
}
lualangshi = sgs.CreateTriggerSkill{
	name = "lualangshi" ,
	view_as_skill = lualangshiVS,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Start and room:askForSkillInvoke(player, "lualangshi", data) then
			player:drawCards(3)
			room:askForDiscard(player, self:objectName(), player:getHp(), player:getHp(), false, true, "lualangshi")
			room:askForUseCard(player, "@@lualangshi", "@lualangshi")
		end
	end
}
lualangshiMod = sgs.CreateTargetModSkill{
	name = "#lualangshiMod" ,
	pattern = ".",
	distance_limit_func = function(self, player, card)
		if card:isBlack() and player:hasFlag("lualangshiB") then
			return 1000
		else
			return 0
		end
	end
}
lualangshi3 = sgs.CreateTriggerSkill{
	name = "#lualangshi3",
	events = {sgs.TargetConfirmed, sgs.TrickCardCanceling},
	global = true,
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			if use.from and player:objectName() == use.from:objectName() and use.card:isRed() and player:hasFlag("lualangshiB")
				and use.card:isKindOf("Slash") then
				local jink_table = sgs.QList2Table(player:getTag("Jink_" .. use.card:toString()):toIntList())
				local index = 1
				for _, p in sgs.qlist(use.to) do
					jink_table[index] = 0
					index = index + 1
				end
				local jink_data = sgs.QVariant()
				jink_data:setValue(Table2IntList(jink_table))
				player:setTag("Jink_" .. use.card:toString(), jink_data)
			end
		elseif event == sgs.TrickCardCanceling then
			local effect = data:toCardEffect()
			if effect.from and effect.from:hasSkill(self:objectName()) and effect.card:isRed() and effect.from:hasFlag("lualangshiB")
					and effect.to then
				return true
			end
		end
	end
}
momiji:addSkill(luashaojie)
momiji:addSkill(lualangshi)
momiji:addSkill(lualangshi3)
momiji:addSkill(lualangshiMod)


momijiA:addSkill(luashaojie)
momijiA:addSkill(lualangshi)
momijiA:addSkill(lualangshiMod)

momijiB:addSkill(luashaojie)
momijiB:addSkill(lualangshi)
momijiB:addSkill(lualangshiMod)

momijiD:addSkill(luashaojie)
momijiD:addSkill(lualangshi)
momijiD:addSkill(lualangshiMod)

momijiC:addSkill(luashaojie)
momijiC:addSkill(lualangshi)
momijiC:addSkill(lualangshiMod)

luaqijians = sgs.CreateTriggerSkill{
	name = "#luaqijians",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.AfterDrawInitialCards, sgs.EventPhaseEnd},
	on_trigger = function(self, triggerEvent, cirno, data)
		local room = cirno:getRoom()
		if triggerEvent == sgs.AfterDrawInitialCards then
			room:notifySkillInvoked(cirno, "luaqijian")
			local card_ids = room:getNCards(7)
            cirno:addToPile("sword", card_ids, false)
		elseif triggerEvent == sgs.EventPhaseEnd then 
			if cirno:getPhase() == sgs.Player_Finish and cirno:hasFlag("luaqijianx") then 
				if cirno:hasSkill("luawushuang") then room:detachSkillFromPlayer(cirno, "luawushuang") end
				if cirno:hasSkill("luajuezhan") then room:detachSkillFromPlayer(cirno, "luajuezhan") end 
			end 
			
        end
	end,
	priority = -1
}

luaqijianCard = sgs.CreateSkillCard{
	name = "luaqijian",
	target_fixed = true,
	will_throw = false,
	on_use = function(self, room, source, targets)
		local fields = source:getPile("sword")
		local count = fields:length()
		local idD
		if count == 0 then
			return
		elseif count == 1 then
			idD = fields:first()
		else
			room:fillAG(fields, source)
			idD = room:askForAG(source, fields, false, self:objectName())
			room:clearAG()
			if idD == -1 then
				return
			end
		end
		local dummy = sgs.Sanguosha:cloneCard("jink")
		dummy:addSubcard(idD)
		room:throwCard(dummy, source, source)

		local discard_ids = room:getDiscardPile()
		local trickcard = sgs.IntList()
		for _, id in sgs.qlist(discard_ids) do 
			local card = sgs.Sanguosha:getCard(id)
			if card:isKindOf("Weapon") then 
				trickcard:append(id)
			end 
		end 
		discard_ids = room:getDrawPile()
		for _, id in sgs.qlist(discard_ids) do 
			local card = sgs.Sanguosha:getCard(id)
			if card:isKindOf("Weapon") then 
				trickcard:append(id)
			end 
		end
		room:writeToConsole("qijian testA")
		if trickcard:length() > 0 then
			room:writeToConsole("qijian testB")
			room:fillAG(trickcard, source)
			local card_id = room:askForAG(source, trickcard, false, "luaqijian")
			room:clearAG(source)
			--local n = math.random(1,trickcard:length()) - 1
			--local card_id = trickcard:at(n)
			local card_po = sgs.Sanguosha:getCard(card_id):objectName()
			local dummy_0 = sgs.Sanguosha:cloneCard(card_po, sgs.Card_NoSuit, 0)
			dummy_0:addSubcard(card_id)
			room:writeToConsole("qijian testC")
			room:useCard(sgs.CardUseStruct(dummy_0, source, sgs.SPlayerList()))
		end 		
		room:setPlayerFlag(source, "luaqijianx")
		local choice = room:askForChoice(source, "luaqijian", "luawushuang+luajuezhan")
		if choice then
			room:acquireSkill(source, choice)
		end 
	end
}
luaqijianVS = sgs.CreateViewAsSkill{
	name = "luaqijian",
	n = 0,
	view_as = function(self, cards)
		return luaqijianCard:clone()
	end,
	enabled_at_play = function()
		return false
	end, 
	enabled_at_response = function(self, player, pattern)
		return string.startsWith(pattern, "@@luaqijian")
	end 
}
luaqijian = sgs.CreateTriggerSkill{
	name = "luaqijian",  
	view_as_skill = luaqijianVS, 
	events = {sgs.EventPhaseStart}, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()  
		if event == sgs.EventPhaseStart and player:getPhase() == sgs.Player_Play and player:getPile("sword"):length() > 0
			and player:hasSkill(self:objectName()) and room:askForSkillInvoke(player, self:objectName(), data) then
			room:askForUseCard(player, "@@luaqijian!", "@luaqijian", -1, sgs.Card_MethodNone)
		end
	end
}


zhenyiCard = sgs.CreateSkillCard{
	name = "luazhengyi",
	will_throw = false,
	--handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		local card = sgs.Self:getTag("luazhengyi")
		local response = false
		if card then 
			card = card:toCard()
		else		
			response = true			
			card = sgs.Sanguosha:cloneCard(self:getUserString())
			card = sgs.self
		end 
		if not card then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		if self:getUserString() and self:getUserString() ~= "" then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		card:addSubcards(self:getSubcards())
		card:setSkillName(self:objectName())
		if card and card:targetFixed() and not response then
			return false
		end
		if response then return true end 
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end

		return card and card:targetFilter(qtargets, to_select, sgs.Self) and not sgs.Self:isProhibited(to_select, card, qtargets)
	end,
	feasible = function(self, targets)
		local card = sgs.Self:getTag("luazhengyi")
		local response = false
		if card then 
			card = card:toCard()
		else
			response = true
			card = sgs.Sanguosha:cloneCard(self:getUserString())
		end 
		if not card then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		if self:getUserString() and self:getUserString() ~= "" then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		card:addSubcards(self:getSubcards())
		card:setSkillName(self:objectName())
		--if card:isKindOf("BasicCard") then card = kcard:clone() end 
		if response then return true end 
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		return card and card:targetsFeasible(qtargets, sgs.Self)
	end,	
	on_validate = function(self, card_use)
		local use_card = sgs.Sanguosha:cloneCard(self:getUserString())
		use_card:addSubcards(self:getSubcards())
		use_card:setSkillName(self:objectName())
		local available = true
		for _,p in sgs.qlist(card_use.to) do
			if card_use.from:isProhibited(p,use_card)then
				available = false
				break
			end
		end
		available = available and use_card:isAvailable(card_use.from)
		if not available then return nil end
		card_use.from:getRoom():setPlayerFlag(card_use.from, "zhenyi_used")
		return use_card		
	end,
	on_validate_in_response = function(self, user)
		local room = user:getRoom()
		local aocaistring = self:getUserString()
		local use_card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, -1)
		if string.find(aocaistring, "+")  then
			local uses = {}
			for _, name in pairs(aocaistring:split("+"))do
				table.insert(uses, name)
			end
			local name = room:askForChoice(user, "luazhengyi", table.concat(uses, "+"))
			use_card = sgs.Sanguosha:cloneCard(name, sgs.Card_NoSuit, -1)
		end
		use_card:addSubcards(self:getSubcards())
		use_card:setSkillName("luazhengyi")
		room:setPlayerFlag(user, "zhenyi_used")
		return use_card	
	end
}
luazhengyi = sgs.CreateViewAsSkill{
	name = "luazhengyi",
	n = 1,
	view_filter = function(self, selected, to_select)
		return sgs.Self:getWeapon() and sgs.Self:getWeapon():getSuit() == to_select:getSuit()
	end,
	view_as = function(self, cards)
		if #cards < 1 then return nil end 
		if (sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY) then			
			local c = sgs.Self:getTag("luazhengyi"):toCard()
			if c then
				local card = zhenyiCard:clone()
				for _, acard in ipairs(cards) do
					card:addSubcard(acard)
				end			
				card:setUserString(c:objectName())	
				return card			
			end
		else
			local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
			if pattern == "slash" then 
				pattern = "slash+thunder_slash+fire_slash"
			end
			local acard = zhenyiCard:clone()
			if #cards ~= 1 then return end
			for _, bcard in ipairs(cards) do
				acard:addSubcard(bcard)
			end			
			if pattern == "peach+analeptic" and sgs.Self:hasFlag("Global_PreventPeach") then 
				pattern = "analeptic" 
			end
			acard:setUserString(pattern)
			return acard		
		end 
		return nil
	end,
	enabled_at_play = function(self, player)
		return not player:isNude() and not player:hasFlag("zhenyi_used")
	end,
	enabled_at_response = function(self, player, pattern)
		if player:isNude() or string.sub(pattern, 1, 1) == "." or string.sub(pattern, 1, 1) == "@" then
			return false
		end
		return player:getWeapon() and not player:hasFlag("zhenyi_used")
	end,	
	enabled_at_nullification = function(self, player)		
		return not player:isNude() and player:getWeapon() and not player:hasFlag("zhenyi_used")
	end
}
luazhengyi:setGuhuoDialog("l")
luazhengyi3 = sgs.CreateTriggerSkill{
	name = "#luazhengyi2" ,
	global = true,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		
		for _, toziko in sgs.qlist(room:findPlayersBySkillName("luazhengyi")) do 
			if toziko and toziko:hasFlag("zhenyi_used") then
				room:setPlayerFlag(toziko, "-zhenyi_used")
			end
		end 
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}
accirno:addSkill(luaqijians)
accirno:addSkill(luaqijian)
accirno:addSkill(luazhengyi)
accirno:addSkill(luazhengyi3)
chenn:addSkill(luazhengyi)
--[[
luayuetuan = sgs.CreateTriggerSkill{
	name = "luayuetuan" ,
	events = {sgs.Damaged} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if damage.damage == 0 then return false end
		for i = 0, damage.damage - 1, 1 do
			local target = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "luayuetuan2", true, false)
			if target and target:isAlive() then				
				local roomx = target:getRoom()				
				local thread = roomx:getThread()
				roomx:setPlayerFlag(target, "luayuetuan")
				local old_phase = target:getPhase()
				target:setPhase(sgs.Player_Draw)
				roomx:broadcastProperty(target, "phase")
				if not thread:trigger(sgs.EventPhaseStart, roomx, target) then
					thread:trigger(sgs.EventPhaseProceeding, roomx, target)
				end
				thread:trigger(sgs.EventPhaseEnd, roomx, target)
				target:setPhase(old_phase)
				roomx:broadcastProperty(target, "phase")
				
				
				-- if player and player:hasFlag("Luashenyi") then 
					-- roomx:setPlayerFlag(player, "-Luashenyi")
				-- end 
			end
		end 
	end 
}
luayuetuan4 = sgs.CreateTriggerSkill{
	name = "#luayuetuan3" ,
	global = true,
	events = {sgs.EventPhaseEnd} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			if p and p:hasFlag("luayuetuan") then 
				room:setPlayerFlag(p, "-luayuetuan")
			end 
		end 
		return false
	end ,
	can_trigger = function(self, target)
		return target 
	end ,
	priority = 1
}

luayuechongCard = sgs.CreateSkillCard{
	name = "luayuechong",
	will_throw = false,
	--handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		local card = sgs.Self:getMark("yuechong") - 1
		card = sgs.Sanguosha:getCard(card)
		card:setSkillName(self:objectName())
		if card and card:targetFixed() and not response then
			return false
		end 
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end

		return card and card:targetFilter(qtargets, to_select, sgs.Self) and not sgs.Self:isProhibited(to_select, card, qtargets)
	end,
	feasible = function(self, targets)
		local card = sgs.Self:getMark("yuechong") - 1
		card = sgs.Sanguosha:getCard(card)
		card:setSkillName(self:objectName()) 
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		return card and card:targetsFeasible(qtargets, sgs.Self)
	end,	
	on_validate = function(self, card_use)
		local card = card_use.from:getMark("yuechong") - 1
		card = sgs.Sanguosha:getCard(card)
		local use_card = sgs.Sanguosha:cloneCard(card:objectName())
		use_card:addSubcard(card)
		use_card:setSkillName(self:objectName())
		local available = true
		for _,p in sgs.qlist(card_use.to) do
			if card_use.from:isProhibited(p,use_card) then
				available = false
				break
			end
		end
		available = available and use_card:isAvailable(card_use.from)
		if not available then 
			local dummy = sgs.Sanguosha:cloneCard("jink")
			dummy:addSubcard(card)
			card_use.from:getRoom():throwCard(dummy, nil, card_use.from)
			return nil 
		end
		if (not card_use.from:hasFlag("luayuetuan")) and use_card:isKindOf("Slash") then card_use.from:getRoom():setPlayerFlag(card_use.from, "yuechongslash") end 
		return use_card		
	end,
}
yuechongVS = sgs.CreateZeroCardViewAsSkill{
	name = "luayuechong",
	response_pattern = "@@luayuechong",
	
	view_as = function(self) 
		return luayuechongCard:clone()
	end
}
luayuechong = sgs.CreateTriggerSkill{
	name = "luayuechong",
	frequency = sgs.Skill_NotFrequent,
	events = { sgs.DrawNCards },
	view_as_skill = yuechongVS,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local y = 3 - player:getHandcardNum()
		if  y > 0 and room:askForSkillInvoke(player, self:objectName()) then
			local count = data:toInt() - 1
			data:setValue(count)
			room:setPlayerFlag(player, "luayuechong")
			local card_ids = room:getNCards(y)
				
			local function throw(id)
				local dummy = sgs.Sanguosha:cloneCard("jink")						
				dummy:addSubcard(id)
				room:throwCard(dummy, nil, player)				
			end 
			while true do
				room:fillAG(card_ids)		
				local id1 = room:askForAG(player, card_ids,  true, self:objectName()) --S_REASON_CHANGE_EQUIP
				if not id1 or (id1 == -1) then
					for _, id in sgs.qlist(card_ids) do
						throw(id)
					end
					room:clearAG()
					break
				end
				local id2 = id1 + 1
				card_ids:removeOne(id1)
				room:clearAG()
				room:setPlayerMark(player, "yuechong", id2)
				local card = sgs.Sanguosha:getCard(id1)
				if card:targetFixed() then
					if card:isKindOf("DefensiveHorse") or card:isKindOf("OffensiveHorse") then 
						local dummy_p = sgs.Sanguosha:cloneCard("jink")						
						dummy_p:addSubcard(id1)						
						if card:isKindOf("DefensiveHorse") then 
							if player:getDefensiveHorse() then 
								local moveA = sgs.CardsMoveStruct()
								moveA.card_ids = sgs.IntList()
								moveA.from = player
								moveA.from_place = sgs.Player_PlaceEquip
								moveA.card_ids:append(player:getDefensiveHorse():getId())
								moveA.to = nil
								moveA.to_place = sgs.Player_DiscardPile
								moveA.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_CHANGE_EQUIP, player:objectName(), "luayuechong", "")
								room:moveCardsAtomic(moveA, true)	--我也很尽力了，只能伪实现	
								
							end 
							room:moveCardTo(dummy_p, player, player, sgs.Player_PlaceEquip,
									sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, player:objectName(), self:objectName(), nil))
							--room:getThread():trigger(sgs.CardUsed, room, player, data)
						else
							if player:getOffensiveHorse() then 
								local moveA = sgs.CardsMoveStruct()
								moveA.card_ids = sgs.IntList()
								moveA.from = player
								moveA.from_place = sgs.Player_PlaceEquip
								moveA.card_ids:append(player:getOffensiveHorse():getId())
								moveA.to = nil
								moveA.to_place = sgs.Player_DiscardPile
								moveA.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_CHANGE_EQUIP, player:objectName(), "luayuechong", "")
								room:moveCardsAtomic(moveA, true)	--我也很尽力了，只能伪实现	
								
							end 
							room:moveCardTo(dummy_p, player, player, sgs.Player_PlaceEquip,
									sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, player:objectName(), self:objectName(), nil))						
						end 
					else
						local dummy_0 = sgs.Sanguosha:cloneCard(card:objectName(), sgs.Card_NoSuit, 0)
						dummy_0:addSubcard(id1)
						room:clearAG()
						if player:isCardLimited(dummy_0, sgs.Card_MethodUse) or card:isKindOf("Jink") or card:isKindOf("sakura") or card:isKindOf("Nullification")
							or ((not player:isWounded()) and card:isKindOf("Peach")) then 
							throw(id1)
						else	
							room:useCard(sgs.CardUseStruct(dummy_0, player, sgs.SPlayerList()))		
						end 						
					end 
				else
					if ((not card:isKindOf("Slash")) or (not player:hasFlag("yuechongslash")))
						and room:askForUseCard(player, "@@luayuechong", "@luayuechong") then 
						--这样就能从异次元发射子弹了！
					else
						throw(id1)
					end 
				end 
				y = y - 1
				
				room:setPlayerMark(player, "yuechong", 0)
				if card_ids:isEmpty() then break end 
				if y == 0 then break end 
				if not room:askForSkillInvoke(player, self:objectName()) then 
					for _, id in sgs.qlist(card_ids) do
						throw(id)
					end 
					break 
				end 				
			end 
			
		end 
	end
}

luayuetuan2 = sgs.CreateTargetModSkill{
	name = "#luayuetuan",
	pattern = "Slash",
	residue_func = function(self, player)
		if player:hasFlag("luayuetuan") then
			return 1000
		end
	end,
	distance_limit_func = function(self, from, card)
		if from:hasFlag("luayuechong") then
			return 1000
		end
	end,
}
luayuetuan3 = sgs.CreateTargetModSkill{
	name = "#luayuetuan2",
	pattern = "Analeptic",
	residue_func = function(self, player)
		if player:hasFlag("luayuetuan") then
			return 1000
		end
	end,
}

ringoseiran:addSkill(luayuetuan)
ringoseiran:addSkill(luayuetuan2)
ringoseiran:addSkill(luayuetuan3)
ringoseiran:addSkill(luayuetuan4)
ringoseiran:addSkill(luayuechong)
]]--
luayuetuan = sgs.CreateTriggerSkill{
	name = "luayuetuan" ,
	events = {sgs.Damaged, sgs.Damage} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if damage.damage == 0 then return false end
		if ((damage.card and (not damage.card:isKindOf("AOE"))) or not damage.card) and damage.by_user and not player:hasFlag("luayuetuan") then
			room:setPlayerFlag(player, "luayuetuan")
			local x = damage.damage
			for i = 0, x - 1, 1 do
				if room:askForDiscard(player, self:objectName(), 1, 1, true, true) then
					local p = room:askForPlayerChosen(player, room:getAlivePlayers(), "luayuetuan", "luayuetuan", false, true)
					p:drawCards(1)
				end
			end
			room:setPlayerFlag(player, "-luayuetuan")
		end
	end
}

luayuechong = sgs.CreateTriggerSkill{
	name = "luayuechong" ,
	frequency = sgs.Skill_NotFrequent ,
	events = {sgs.CardsMoveOneTime} ,
	on_trigger = function(self, event, player, data)
		if event == sgs.CardsMoveOneTime then
			local room = player:getRoom()
			local move = data:toMoveOneTime()
			if not move.from then return false end
			if not move.from:hasSkill(self:objectName()) then return false end
			local old_card_ids = {}
			for _,card_idX in sgs.qlist(move.card_ids) do
				table.insert(old_card_ids, card_idX)
			end
			local aicard = sgs.IntList()
			local j = 0
			local bool = false
			for _,card_id in sgs.qlist(move.card_ids) do
				local place = move.from_places:at(j)
				if place == sgs.Player_PlaceHand or place == sgs.Player_PlaceEquip then
					bool = true
					aicard:append(card_id)
				end
				j = j + 1
			end

			local _movefrom
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if move.from:objectName() == p:objectName() then
					_movefrom = p
					break
				end
			end
			local reason = move.reason
			local basic = bit32.band(reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)
			if _movefrom and _movefrom:objectName() == player:objectName() and _movefrom:hasSkill("luayuechong") and bool
					and (basic == sgs.CardMoveReason_S_REASON_DISCARD) then
				local function throw(id)
					local dummy = sgs.Sanguosha:cloneCard("jink")
					dummy:addSubcard(id)
					room:throwCard(dummy, nil, player)
				end
				local ids = room:getNCards(1)
				room:fillAG(ids)
				room:getThread():delay()
				room:getThread():delay()
				room:clearAG()
				local card = sgs.Sanguosha:getCard(ids:at(0))
				if player:isCardLimited(card, sgs.Card_MethodUse) then throw(ids:at(0)); return false end
				if card:isKindOf("Jink") or card:isKindOf("sakura") or card:isKindOf("Nullification") then
					throw(ids:at(0))
				else  --room:useCard(sgs.CardUseStruct(peach, tp, sgs.SPlayerList()))
					local Carddata2 = sgs.QVariant() -- ai用
					Carddata2:setValue(sgs.Sanguosha:getCard(ids:at(0)))
					room:setTag("luayuechongTC", Carddata2)
					local p = room:askForPlayerChosen(player, room:getAlivePlayers(), "luayuechong", "luayuechong", true, true)
					room:removeTag("luayuechongTC")
					if p then
						room:useCard(sgs.CardUseStruct(card, player, p))
					end
				end
			end
		end
	end
}

ringoseiran:addSkill(luayuetuan)
ringoseiran:addSkill(luayuechong)


luajiuchi = sgs.CreateViewAsSkill{
	name = "luajiuchi",
	n = 1,
	view_filter = function(self, selected, to_select)
		return (not to_select:isEquipped()) and (to_select:getSuit() == sgs.Card_Spade)
	end,
	view_as = function(self, cards)
		if #cards == 1 then
			local analeptic = sgs.Sanguosha:cloneCard("analeptic", cards[1]:getSuit(), cards[1]:getNumber())
			analeptic:setSkillName(self:objectName())
			analeptic:addSubcard(cards[1])
			return analeptic
		end
	end,
	enabled_at_play = function(self, player)
		local newanal = sgs.Sanguosha:cloneCard("analeptic", sgs.Card_NoSuit, 0)
		if player:isCardLimited(newanal, sgs.Card_MethodUse) or player:isProhibited(player, newanal) then return false end
		return player:usedTimes("Analeptic") <= sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_Residue, player , newanal)
	end,
	enabled_at_response = function(self, player, pattern)
		return string.find(pattern, "analeptic")
	end
}

luahuaifei = sgs.CreateTriggerSkill{
	name = "luahuaifei", 
	global = true, 
	events = {sgs.TargetSpecified}, 
	frequency = sgs.Skill_Compulsory, 
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		local room = player:getRoom()
		if event == sgs.TargetSpecified and player:objectName() == use.from:objectName() and use.from:hasSkill(self:objectName()) and use.to:length() >= 1 and (use.card:isKindOf("Slash")) then
			if room:getLord():hasFlag("luafanzeSlash") then return false end 
			
			local _data = sgs.QVariant()
			_data:setValue(use.to:first())
			--if room:askForSkillInvoke(player, self:objectName(), _data) then
				room:addPlayerMark(use.to:first(), "@skill_invalidity")
			--end
		end
		return false
	end
}
luahuaifei2 = sgs.CreateTriggerSkill{
	name = "#luahuaifei" ,
	global = true,
	events = {sgs.EventPhaseEnd} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:getCurrent():objectName() == player:objectName() and player:getPhase() == sgs.Player_Finish then
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:getMark("@skill_invalidity") > 0 then
					room:setPlayerMark(p, "@skill_invalidity", 0)
				end
			end
		end
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = -1
}

suiyueCard = sgs.CreateSkillCard{
	name = "luasuiyue",
	filter = function(self, targets, to_select)
		local targets_list = sgs.PlayerList()
		for _, target in ipairs(targets) do
			targets_list:append(target)
		end
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		slash:setSkillName("luasuiyue")
		slash:deleteLater()
		return (#targets < 1) and not sgs.Self:isProhibited(to_select, slash, to_select:getSiblings())
				and sgs.Self:canSlash(to_select, true)
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		room:setPlayerMark(effect.from, "luasuiyueY", 0)
		room:setPlayerMark(effect.from, "luasuiyue", 0)
		for _, cd in sgs.qlist(self:getSubcards()) do
			effect.from:addMark("luasuiyueY")
		end 
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)	
		slash:setSkillName("luasuiyue")
		effect.from:getRoom():useCard(sgs.CardUseStruct(slash, effect.from, effect.to))
	end 
}
	
luasuiyue = sgs.CreateZeroCardViewAsSkill{
	name = "luasuiyue",	
	view_as = function()
		local suiyuecard = suiyueCard:clone()
        suiyuecard:addSubcards(sgs.Self:getHandcards())
        suiyuecard:setSkillName("luasuiyue")
		return suiyuecard
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#luasuiyue") and not player:isKongcheng()
	end
}

luasuiyue2 = sgs.CreateTriggerSkill{
	name = "#luasuiyue" ,
	global = true,
	events = {sgs.CardFinished, sgs.PreDamageDone} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.PreDamageDone then 
            local damage = data:toDamage()
            if damage.card and damage.card:isKindOf("Slash") and damage.card:getSkillName() == "luasuiyue" and damage.from then 
				local x = damage.from:getMark("luasuiyueY") 
				for i = 1, x do
					damage.from:addMark("luasuiyue")
				end 
            end 
        elseif event == sgs.CardFinished then 
            local use = data:toCardUse()
            if use.card and use.card:isKindOf("Slash") and use.card:getSkillName() == "luasuiyue" and player:objectName() == use.from:objectName() then 
                local x = player:getMark("luasuiyue") 
				use.from:drawCards(x)
				room:setPlayerMark(player, "luasuiyueY", 0)
				room:setPlayerMark(player, "luasuiyue", 0)
            end 
        end 
        return false
	end
}
suika:addSkill(luajiuchi)
suika:addSkill(luahuaifei)
suika:addSkill(luahuaifei2)
suika:addSkill(luasuiyue)
suika:addSkill(luasuiyue2)

suikaA:addSkill(luajiuchi)
suikaA:addSkill(luahuaifei)
suikaA:addSkill(luasuiyue)
suikaA:addSkill(luasuiyue2)

suikaB:addSkill(luajiuchi)
suikaB:addSkill(luahuaifei)
suikaB:addSkill(luasuiyue)
suikaB:addSkill(luasuiyue2)

suikaC:addSkill(luajiuchi)
suikaC:addSkill(luahuaifei)
suikaC:addSkill(luasuiyue)
suikaC:addSkill(luasuiyue2)

suikaD:addSkill(luajiuchi)
suikaD:addSkill(luahuaifei)
suikaD:addSkill(luasuiyue)
suikaD:addSkill(luasuiyue2)

suikaE:addSkill(luajiuchi)
suikaE:addSkill(luahuaifei)
suikaE:addSkill(luasuiyue)
suikaE:addSkill(luasuiyue2)

local function shenbaoChange(room, player, boolA)
	if not boolA then
		if player:getHp() == 1 and player:hasSkill("luaqingnang") and not player:hasSkill("luahuoqiu") and not player:hasSkill("luayuzhi") and not player:hasSkill("luayongye") and not player:hasSkill("lualongyu") then
			return
		elseif player:getHp() == 2 and not player:hasSkill("luaqingnang") and player:hasSkill("luahuoqiu") and not player:hasSkill("luayuzhi") and not player:hasSkill("luayongye") and not player:hasSkill("lualongyu") then
			return
		elseif player:getHp() == 3 and not player:hasSkill("luaqingnang") and not player:hasSkill("luahuoqiu") and player:hasSkill("luayuzhi") and not player:hasSkill("luayongye") and not player:hasSkill("lualongyu") then
			return
		elseif player:getHp() == 4 and not player:hasSkill("luaqingnang") and not player:hasSkill("luahuoqiu") and not player:hasSkill("luayuzhi") and player:hasSkill("luayongye") and not player:hasSkill("lualongyu") then
			return
		elseif player:getHp() == 5 and not player:hasSkill("luaqingnang") and not player:hasSkill("luahuoqiu") and not player:hasSkill("luayuzhi") and not player:hasSkill("luayongye") and player:hasSkill("lualongyu") then
			return
		end
	end

	if player:hasSkill("luaqingnang") then
		room:handleAcquireDetachSkills(player, "-luaqingnang")
	end
	if player:hasSkill("luahuoqiu") then
		room:handleAcquireDetachSkills(player, "-luahuoqiu")
	end
	if player:hasSkill("luayuzhi") then
		room:handleAcquireDetachSkills(player, "-luayuzhi")
	end
	if player:hasSkill("luayongye") then
		room:handleAcquireDetachSkills(player, "-luayongye")
		room:writeToConsole("luayongye test" .. player:getGeneralName())
	end
	if player:hasSkill("lualongyu") then
		if player:getHp() < 5 then
			player:drawCards(3)
		end
		room:handleAcquireDetachSkills(player, "-lualongyu")
		room:writeToConsole("longyu test" .. player:getGeneralName())
	end
	if not player:hasSkill("luashenbao") then return end
	if not boolA then
		if player:getHp() == 1 then
			room:handleAcquireDetachSkills(player, "luaqingnang")
		elseif player:getHp() == 2 then
			room:handleAcquireDetachSkills(player, "luahuoqiu")
		elseif player:getHp() == 3 then
			room:handleAcquireDetachSkills(player, "luayuzhi")
		elseif player:getHp() == 4 then
			room:handleAcquireDetachSkills(player, "luayongye")
		elseif player:getHp() == 5 then
			room:handleAcquireDetachSkills(player, "lualongyu")
		end
	end
end
luashenbao = sgs.CreateTriggerSkill{
	name = "luashenbao" ,
	events = {sgs.TurnStart, sgs.HpChanged, sgs.MaxHpChanged, sgs.EventLoseSkill, sgs.CardsMoveOneTime, sgs.BeforeCardsMove} ,
	frequency = sgs.Skill_Compulsory ,
	on_trigger = function(self, event, player, data)		
		local room = player:getRoom()
		if event == sgs.TurnStart then			 
			for _, kaguya in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do  
				if not kaguya or not kaguya:isAlive() then return false end
				shenbaoChange(room, kaguya)
			end 
			return false
	    end
		if event == sgs.BeforeCardsMove then
			local move = data:toMoveOneTime()
			if move.from and move.from:objectName() == player:objectName() and move.from_places:contains(sgs.Player_PlaceEquip) then
				for _, id in sgs.qlist(move.card_ids) do
					if room:getCardOwner(id):objectName() == move.from:objectName() and room:getCardPlace(id) == sgs.Player_PlaceEquip
							and sgs.Sanguosha:getCard(id):isKindOf("Armor") then
						shenbaoChange(room, player)
						return false
					end
				end
			end
			return false
		end
		if event == sgs.CardsMoveOneTime then
			local move = data:toMoveOneTime()
			if move.to and move.to:objectName() == player:objectName() and move.to_place == sgs.Player_PlaceEquip then
				for _, id in sgs.qlist(move.card_ids) do
					if room:getCardOwner(id):objectName() == move.to:objectName() and room:getCardPlace(id) == sgs.Player_PlaceEquip
							and sgs.Sanguosha:getCard(id):isKindOf("Armor") then
						shenbaoChange(room, player, true)
						return false
					end
				end
			end
			return false
		end
		if player:getArmor() then return false end
		if event == sgs.EventLoseSkill then
			if data:toString() == self:objectName() then
				shenbaoChange(room, player, true)
			end
			return false
		end
		if not player:isAlive() or not player:hasSkill(self:objectName(), true) then return false end		
		shenbaoChange(room, player)
		return false
	end ,
	can_trigger = function(self, target)
		return target ~= nil
	end
}
kaguya:addSkill(luashenbao)
kaguyaA:addSkill(luashenbao)
kaguyaB:addSkill(luashenbao)
kaguyaC:addSkill(luashenbao)
kaguyaD:addSkill(luashenbao)

Luaxiuyuecard = sgs.CreateSkillCard{
	name = "Luaxiuyue", 	
	target_fixed = true,
	on_use = function(self, room, source, targets)
		if source:getMark("Luaxiuyue1") == 0 then 
			room:loseHp(source)
			source:getRoom():setPlayerMark(source, "Luaxiuyue1", 1)
			source:loseAllMarks("@manyue")
			source:loseAllMarks("@huiyue")
			source:gainMark("@huiyue")		
		else
			room:setPlayerProperty(source, "hp", sgs.QVariant(source:getHp() + 1))
			source:getRoom():setPlayerMark(source, "Luaxiuyue1", 0)
			source:loseAllMarks("@manyue")
			source:loseAllMarks("@huiyue")
			source:gainMark("@manyue")			
		end 
	
	end 
}

Luaxiuyue = sgs.CreateZeroCardViewAsSkill{
	name = "Luaxiuyue",
	
	view_as = function()
		return Luaxiuyuecard:clone()
	end,

	enabled_at_play = function(self, player)
		return (not player:hasUsed("#Luaxiuyue"))
	end
}
kaguya:addSkill(Luaxiuyue)
kaguyaA:addSkill(Luaxiuyue)
kaguyaB:addSkill(Luaxiuyue)
kaguyaC:addSkill(Luaxiuyue)
kaguyaD:addSkill(Luaxiuyue)
shuangfengCard = sgs.CreateSkillCard{
	name = "luashuangfeng",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		source:drawCards(1)
		local target = room:getTag("luashuangfeng"):toPlayer()
		local choice = room:askForChoice(source, self:objectName(), "fire_slash+thunder_slash")
		local slash = sgs.Sanguosha:cloneCard(choice)
		room:useCard(sgs.CardUseStruct(slash, source, target))
	end
}
shuangfengVS = sgs.CreateViewAsSkill{
	name = "luashuangfeng",
	n = 1,
	view_filter = function(self, selected, to_select)
		return not sgs.Self:isJilei(to_select)
	end ,
	view_as = function(self, cards)
		local card_1 =  shuangfengCard:clone()
		for _, c in ipairs(cards) do
			card_1:addSubcard(c)
		end
		return card_1
	end,
	enabled_at_play = function(self, player)
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luashuangfeng"
	end
}
luashuangfeng = sgs.CreateTriggerSkill{
	name = "luashuangfeng" ,
	view_as_skill = shuangfengVS,
	events = {sgs.CardFinished} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardFinished then
			local use = data:toCardUse()
			if not use.from then return end
			local slash1 = sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_NoSuit, 0)
			local slash2 = sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_NoSuit, 0)

			room:writeToConsole("luashuangfeng test")
			if player:hasSkill("luashuangfeng") and player:objectName() == use.from:objectName() then
				for _, t in sgs.qlist(use.to) do
					if use.card and use.card:isKindOf("Slash") and not (use.card:objectName() == "fire_slash" or use.card:objectName() == "thunder_slash")
							and (player:canSlash(t, slash1, true) or player:canSlash(t, slash2, true)) then
						local playerdata = sgs.QVariant() --ai用
						playerdata:setValue(t)
						room:setTag("luashuangfeng", playerdata)
						room:askForUseCard(player, "@@luashuangfeng", "@luashuangfeng")
						room:removeTag("luashuangfeng")
						return false
					end
				end
			end
		end
		return false
	end,
	can_trigger = function(self, target)
		return target
	end
}

luachixiaocard = sgs.CreateSkillCard{
	name = "luachixiao",
	filter = function(self, targets, to_select)
		return #targets == 0
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		effect.from:loseMark("@chixiao")
		room:swapSeat(effect.from, effect.to)
		
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			if effect.from:canSlash(p, nil, true) then
				room:getThread():delay()
				room:doAnimate(1, effect.from:objectName(), p:objectName())
				local playerdata = sgs.QVariant() --ai用
				playerdata:setValue(p)
				if room:askForSkillInvoke(effect.from, self:objectName(), playerdata) then
					local slash = sgs.Sanguosha:cloneCard("slash")
					room:useCard(sgs.CardUseStruct(slash, effect.from, p))
				end
			end
		end
	end
}
luachixiaoVS = sgs.CreateViewAsSkill{
	name = "luachixiao",
	n = 0,
	view_filter = function()
		return false
	end,
	view_as = function()
		return luachixiaocard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@chixiao") > 0
	end,
}
luachixiao = sgs.CreateTriggerSkill{
	name = "luachixiao",
	frequency = sgs.Skill_Limited,
	limit_mark = "@chixiao",
	view_as_skill = luachixiaoVS,
	events = {sgs.AfterDrawInitialCards},
	on_trigger = function(self, triggerEvent, cirno, data)
		local room = cirno:getRoom()
		if triggerEvent == sgs.AfterDrawInitialCards then
			local discard_ids = room:getDiscardPile()
			local trickcard = sgs.IntList()
			for _, id in sgs.qlist(discard_ids) do
				local card = sgs.Sanguosha:getCard(id)
				if card:isKindOf("Weapon") and not card:isKindOf("Crossbow") and card:isRed() then
					trickcard:append(id)
				end
			end
			discard_ids = room:getDrawPile()
			for _, id in sgs.qlist(discard_ids) do
				local card = sgs.Sanguosha:getCard(id)
				if card:isKindOf("Weapon") and not card:isKindOf("Crossbow") and card:isRed() then
					trickcard:append(id)
				end
			end
			if trickcard:length() > 0 then
				local count = trickcard:length()
				local idD
				if count == 0 then
					return
				elseif count == 1 then
					idD = trickcard:first()
				else
					room:fillAG(trickcard, cirno)
					idD = room:askForAG(cirno, trickcard, false, self:objectName())
					room:clearAG()
					if idD == -1 then
						return
					end
				end
				local card_id = idD
				local card_po = sgs.Sanguosha:getCard(card_id):objectName()
				local dummy_0 = sgs.Sanguosha:cloneCard(card_po, sgs.Card_NoSuit, 0)
				dummy_0:addSubcard(card_id)
				room:useCard(sgs.CardUseStruct(dummy_0, cirno, sgs.SPlayerList()))
			end
		end
	end,
	priority = -1
}

chenn:addSkill(luashuangfeng)
chenn:addSkill(luachixiao)


sgs.LoadTranslationTable{
	["pay2"] = "百鸟朝凤", --注意这里每次要加逗号
	["marisa"] = "雾雨魔理沙",
	["marisaA"] = "雾雨魔理沙",
	["marisaB"] = "雾雨魔理沙",
	["marisaC"] = "雾雨魔理沙",
	["marisaD"] = "雾雨魔理沙",
	["marisaE"] = "雾雨魔理沙",
	["#marisa"]= "魔法使",
	["designer:marisa"] = "Paysage",
	["illustrator:marisaB"] = "lumo1121",
	["luaaoshu"] = "奥术",
	["luadanmu2"] = "请弃置至少一张的手牌。",
	["@aoshu"] = "你可使用一张牌（无视距离）",
	["luaaoshu2"] = "请选择要获得谁的牌",
	[":luaaoshu"] = "你可以跳过你的摸牌阶段，令一名其他角色摸一张牌并执行一个额外的此阶段。之后若其手牌数为全场最多，你获得其X张牌（X为其手牌数的一半，向上取整）",
	["luadanmu"] = "弹幕",
	[":luadanmu"] = "出牌阶段，你可以弃任意张手牌，视为你对攻击范围内一名手牌数与你相同的角色使用了一张【弹幕】，且其非锁定技直到其回合开始时无效。",

	["kaguya"] = "蓬莱山辉夜",
	["kaguyaA"] = "蓬莱山辉夜",
	["kaguyaB"] = "蓬莱山辉夜",
	["kaguyaC"] = "蓬莱山辉夜",
	["kaguyaD"] = "蓬莱山辉夜",
	["#kaguya"]= "月之公主",
	["designer:kaguya"] = "Paysage",
	["Luaxiuyue"] = "袖月",
	[":Luaxiuyue"] = "转化技：出牌阶段限一次，①：你可以流失一点体力；②：你可以增加一点体力。",
	["luashenbao"] = "神宝",
	[":luashenbao"] = "若你装备区没有防具，且体力为：1，你视为拥有技能“青囊”;2，你视为拥有“火裘”；3，你视为拥有“玉枝”；4，你视为拥有“永夜”；5，你视为拥有“龙玉”。",
	
	["fujiwara"] = "藤原妹红",
	["#fujiwara"]= "人类地狱火",	
	["#yanfeng"] = "焰风",
	["luayanfeng"] = "焰风",
	[":luayanfeng"] = "锁定技，你的【杀】均视为【火攻】。你使用【火攻】指定目标后，若你没有手牌，则其受到一点火焰伤害。",
	["luayanfeng1"] = "摸一张牌",
	["luayanfeng2"] = "减少一点体力上限",
	["luachongsheng"] = "重生",	
	[":luachongsheng"] = "摸牌阶段开始时，你可以回复体力至上限，并于此阶段额外摸一张牌。此后，你受到的所有伤害+1。 ",
	--[[["@luachongsheng"] = "你可以发动“重生”",
	["~luachongsheng"] = "选择X+1张红牌张牌（X为你的体力上限）→点击确定",	]]--
	["luahonghun"] = "红魂",
	["honghun"] = "红魂",
	[":luahonghun"] = "出牌阶段限一次，你可以将一张手牌当作【铁索连环】对你与一名其他角色使用。",
	
	["reimu"] = "博丽灵梦",
	["reimuA"] = "博丽灵梦",
	["reimuB"] = "博丽灵梦",
	["reimuC"] = "博丽灵梦",
	["reimuD"] = "博丽灵梦",
	["reimuE"] = "博丽灵梦",
	["reimuX"] = "博丽灵梦",
	["#reimu"]= "乐园的巫女",
	["designer:reimu"] = "Paysage",
	["luahakurei"] = "灵击",
	["hakurei"] = "灵击",
	["luahakurei1"] = "此【杀】不计入次数限制",
	["luahakurei2"] = "摸一张牌",
	[":luahakurei"] = "出牌阶段限一次，你可以将一张牌当作【杀】使用，并选择一项：1.此【杀】无距离限制；2.此【杀】不计入次数限制；3.摸一张牌。",
	["LuaFengyin"] = "封印",	
	[":LuaFengyin"] = "锁定技，你使用【杀】指定目标后，其不能打出或使用与此牌同花色的手牌，直到其回合结束阶段。",

	["akong"] = "灵乌路空",	
	["akongA"] = "灵乌路空",
	["akongB"] = "灵乌路空",
	["akongC"] = "灵乌路空",
	["akongD"] = "灵乌路空",
	["akongO"] = "灵乌路空",
	["#akong"]= "地底太阳",
	["designer:akong"] = "Paysage",
	["LuaYaoban"] = "耀斑",	
	[":LuaYaoban"] = "出牌阶段，你可以弃置三张同花色的手牌并选一项：对一名其他角色造成一点火焰伤害；或者横置任意名角色，并摸一张牌。",
	["LuaXinXing"] = "新星",	
	[":LuaXinXing"] = "摸牌阶段，你可以放弃摸牌，改为从牌堆顶亮出四张牌，你获得其中一种花色的全部牌，将其余的牌置入弃牌堆。",		
	["LuaHebao"] = "核爆",	
	[":LuaHebao"] = "觉醒技，若你手牌多于6张，你须失去两点体力上限并获得技能“天火”“聚变”。",
	["LuaHebaosdf"] = "核爆",
	[":LuaHebaosdf"] = "觉醒技，若你手牌多于6张，你须失去两点体力上限并获得技能“天火”“聚变”。\
	[<i><b>天火</b>：锁定技。你的普通【杀】均视为火【杀】。</i>]\
	[<i><b>聚变</b>：锁定技，你对一名角色造成火焰伤害后，你须摸一张牌。</i>]",

	["damage"] = "造成伤害",		
	["chain"] = "横置",

	["huashan"] = "茨木华扇",
	["#huashan"]= "茨华仙",
	["designer:huashan"] = "Paysage",
	["luayanxun"] = "言训",
	[":luayanxun"] = "每回合限一次，你对一名其他角色造成1点伤害后，你可以令其回复1点体力并获得其一张牌，然后摸一张牌。锁定技，你不是其他角色方块牌的合法目标。",
	["luahuaxian"] = "华仙",
	[":luahuaxian"] = "觉醒技，若你于本局游戏已使用过至少四种张装备牌，你增加一点体力上限并回复一点体力，将“言训”的“获得”改为“弃置”，且发动“言训”不再回复体力，然后获得“邪道”“鬼腕”。",
	["luaxiedao"] = "邪道",
	[":luaxiedao"] = "你造成过伤害的回合仅一次，你可以重置一个\"每回合限一次\"的技能。",
	["luaguiwan"] = "鬼腕",
	[":luaguiwan"] = "每回合限一次，你可以重铸一张装备牌，并对一名其他角色造成一点伤害。",

	["~LuaJianyan"] = "你可以防止此伤害，将场上的一张牌置于另一名角色相应的区域内。",

	["yugi"] = "星熊勇仪",	
	["#yugi"]= "怪力乱神",	
	["designer:yugi"] = "Paysage",
	["illustrator:yugi"] = "UGUME",
	["LuaGuishen"] = "鬼神",	
	[":LuaGuishen"] = "出牌阶段开始时，你可以使用一张无视距离的【杀】，若如此做，你摸一张牌，此【杀】不计入出牌阶段使用限制。",		
	["LuaBisha"] = "必杀",	
	[":LuaBisha"] = "你的回合限一次，当你使用牌指定一名其他角色为目标时，若其为本回合你第三次指定其为目标，你可以令其进入濒死判定。",
	
	["ringoseiran"] = "铃瑚&清兰",	
	["#ringoseiran"]= "狐鹰双兔",	
	["designer:ringoseiran"] = "Paysage",	
	["luayuetuan"] = "月团",	
	["luayuetuan2"] = "你可以令一名角色于此时执行一个额外的摸牌阶段",	
	[":luayuetuan"] = "群体锦囊以外，你每造成或受到一点伤害后，你可以弃一张牌，然后令一名角色摸一张牌。你于“月团”期间不能再发动“月团”。 ",
	["luayuechong"] = "月铳",	
	["~luayuechong"] = "请为你刚刚选择的牌指定一个合法目标，若不能使用则会自动弃置进入弃牌堆",	
	["@luayuechong"] = "你可以发动“月铳”",	
	[":luayuechong"] = "你的牌被弃置后，你可以展示牌堆顶的一张牌，然后无限制地对任意一名角色使用之。",
	
	--["@LuaJianyan"] = "你可以发动  箴言",	
	["mima"] = "魅魔",	
	["#mima"]= "法之左翼",	
	["designer:mima"] = "Paysage",		
	["lualianpo"] = "连破",
	["light"] = "光",
	["LuaYuanzu"] = "天仪",	
	[":LuaYuanzu"] = "你可以将一张红色手牌牌当【杀】或【闪】使用或打出，以此法使用或打出的牌置入你的判定区。",
	["LuaShanguang"] = "闪光",	
	[":LuaShanguang"] = "你使用【杀】对一名角色造成伤害时，你可以弃置你判定区的X张牌，将此伤害改为X。若X大于：0，你摸一张牌；1，你将其一张牌置于你的判定区；2，你获得“完杀”直到回合结束。3，你获得“连破”直到回合结束。",
	
	["momiji"] = "犬走椛",
	["momijiA"] = "犬走椛",
	["momijiB"] = "犬走椛",
	["momijiC"] = "犬走椛",
	["momijiD"] = "犬走椛",
	["#momiji"]= "哨戒天狗",	
	["designer:momiji"] = "Paysage",		
	["luashaojie"] = "哨戒",	
	[":luashaojie"] = "每轮开始时，你可以指定一名角色。此轮其受到伤害时，你防止之，改为你流失一点体力。 ",
	["lualangshi"] = "狼噬",
	["@lualangshi"] = "你可以发动 “狼噬”确认任意名角色的手牌。",
	["~lualangshi"] = "选择目标→确定",
	[":lualangshi"] = "准备阶段，你可以摸三张牌并弃X张牌，然后确认任意名角色的手牌（X为你体力值）。这回合你使用红色牌不能被响应，使用黑色牌无距离限制。 ",
						
	["accirno"] = "琪露诺",	
	["#accirno"]= "正义的剑士",	
	["designer:accirno"] = "Paysage",	
	["luaqijian"] = "七剑",	
	["sword"] = "剑",
	[":luaqijian"] = "分发起始手牌时，你将牌堆顶的七张牌置于武将牌上，称为“剑”；出牌阶段开始时，你可以弃置一张“剑”，使用一张武器牌，并于本回合获得“决战”或“无双”。",
	["luazhengyi"] = "正义",
	[":luazhengyi"] = "每阶段限一次，你可以将与你武器牌同花色的一张牌当任意基本牌或【无懈可击】使用或打出。",
	["luayingzi"] = "英姿",
	[":luayingzi"] = "摸牌阶段，你可以额外摸一张牌。",
	["luawushuang"] = "无双",
	[":luawushuang"] = "锁定技，当你使用【杀】指定一名角色为目标后，该角色需连续使用两张【闪】才能抵消；与你进行【决斗】的角色每次需连续打出两张【杀】。",
	
	["suika"] = "伊吹萃香",	
	["suikaA"] = "伊吹萃香",
	["suikaB"] = "伊吹萃香",
	["suikaC"] = "伊吹萃香",
	["suikaD"] = "伊吹萃香",
	["suikaE"] = "伊吹萃香",
	["#suika"]= "百鬼夜行",
	["illustrator:suika"] = "伊吹のつ",
	["illustrator:suika_1"] = "羽羽斩",
	["illustrator:suika_2"] = "柊暁生",
	["illustrator:suika_3"] = "あずまあや",
	["illustrator:suika_4"] = "チロタタ",
	["illustrator:suika_5"] = "鑽頭吉他",
	["designer:suika"] = "Paysage",
	["luajiuchi"] = "酒池",	
	[":luajiuchi"] = "你可以将一张黑桃手牌当【酒】使用。",		
	["luahuaifei"] = "坏废",	
	[":luahuaifei"] = "你使用【杀】指定目标后，其非锁定技无效直到回合结束。",
	["luasuiyue"] = "碎月",	
	["suiyue"] = "碎月",	
	[":luasuiyue"] = "出牌阶段限一次，你可以弃置你的所有手牌，视为你对攻击范围内的一名角色使用了一张【杀】。若此【杀】造成伤害，你摸X张牌（X为你弃置牌数）。",

	["chenn"] = "陈",
	["#chenn"]= "秉公任直",
	["designer:chenn"] = "Paysage",
	["luashuangfeng"] = "双锋",
	[":luashuangfeng"] = "你使用普通【杀】对一名角色结算后，你可以重铸一张手牌，视为你对其使用了一张属性【杀】。",
	["~luashuangfeng"] = "选择一张手牌→点击确定",
	["@luashuangfeng"] = "你可以发动“双锋”",
	["luachixiao"] = "赤霄",
	[":luachixiao"] = "锁定技，分发起始手牌后，你可以使用一张攻击范围2以上的红色武器牌。限定技，出牌阶段，你可以与一名其他角色交换座次，然后依座次,视为你对攻击范围内的各个角色使用了一张【杀】。",

}
return {extension_pay_b}