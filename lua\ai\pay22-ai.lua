
math.randomseed(tostring(os.time()):reverse():sub(1, 7)) --------设置时间种子

sgs.ai_skill_invoke.luabainian = function(self, data)
	local damage = data:toDamage()
	local target = damage.to
	if self:isFriend(target) then
		return true
	end
end
sgs.ai_choicemade_filter.skillInvoke["luabainian"] = function(self, player, promptlist)
	local fuji = self.room:findPlayerBySkillName("luabainian")
	if not fuji then return end
	if promptlist[#promptlist] == "yes" then
		sgs.updateIntention(player, fuji, -40)
	else
		sgs.updateIntention(player, fuji, 40)
	end
end

sgs.ai_aoe_value.luabainian = function(self, card, to, from, sj_num)
	if (card:isKindOf("ArcheryAttack") or card:isKindOf("SavageAssault")) then
		if to:getHp() > 1 and not to:hasFlag("luabainian") then
			return 30
		end
	end
	return 0
end
local function findGongxiCard(self)
	if self.player:isKongcheng() then return end
	local fcaerds = sgs.QList2Table(self.player:getCards("h"))
	self:sortByKeepValue(fcaerds)
	if self:needToThrowArmor() then return self.player:getArmor() end
	local function judge(card_0) --bug
		local x = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
		local bool_k = (self.player:getHp() <= 2 and self.player:getHandcardNum() <= 3 and x <= 1)
		if (card_0:isKindOf("Peach") or card_0:isKindOf("ExNihilo")) and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
			return false
		end
		if (card_0:isKindOf("Duel") or card_0:isKindOf("Indulgence")) and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
			return false
		end
		if card_0:isKindOf("Snatch") or card_0:isKindOf("Dismantlement") then
			local use = { isDummy = true }
			self:useCardSnatchOrDismantlement(card_0, use)
			if use.card then return false end
		end

		if (card_0:isKindOf("AOE") and self:getAoeValue(card_0) > 35) then return false end

		local bool_2 = card_0:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(card_0:getId()) == sgs.Player_PlaceEquip))
		local bool_3 = (card_0:isKindOf("EightDiagram") or card_0:isKindOf("RenwangShield") or card_0:isKindOf("Tengu"))
				and ((self.room:getCardPlace(card_0:getId()) == sgs.Player_PlaceEquip)
				or ((self.room:getCardPlace(card_0:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
		if (bool_2 or bool_3) then return false end
		if (not (card_0:isKindOf("Jink") and bool_k) and not (isCard("Peach", card_0, self.player) and self.player:isWounded()))
			and not (card_0:isKindOf("Analeptic") and self.player:getHp() == 1 and x < 2) then
			return true
		end
	end
	if judge(fcaerds[1]) then return fcaerds[1] end
	if self.player:getHp() > 2 and self.player:getDefensiveHorse() then return self.player:getDefensiveHorse() end

end
local luagongxi_skill = {}
luagongxi_skill.name = "luagongxi"
table.insert(sgs.ai_skills, luagongxi_skill)
luagongxi_skill.getTurnUseCard = function(self)
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("luachunhui") then return end
	end
	return sgs.Card_Parse("#luagongxi:.:")
end
sgs.ai_skill_use_func["#luagongxi"] = function(cardD, use, self)
	local card_0 = findGongxiCard(self)
	if card_0 then
		use.card = sgs.Card_Parse("#luagongxi:".. card_0:getId() ..":")
		if use.to then
			if #self.friends_noself > 0 then
				self:sort(self.friends_noself, "defense2")
				use.to:append(self.friends_noself[1])
			else
				use.to:append(self.player)
			end
			return
		end
	end
end

sgs.ai_use_priority.luagongxi = function(self)
	if #self.friends_noself > 0 then
		return 0.7
	else
		return 7
	end
end
sgs.ai_card_intention.luagongxi = -40

local function useChunhuiCard(self)
	local fcaerds = sgs.QList2Table(self.player:getCards("h"))
	local function judge(card_0, value)
		local x = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
		local bool_k = (self.player:getHp() <= 2 and self.player:getHandcardNum() <= 3 and x <= 1)
		if (card_0:isKindOf("Peach") or card_0:isKindOf("ExNihilo")) and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
			return false
		end
		if ((card_0:isKindOf("Duel") or card_0:isKindOf("Indulgence")) and value < 40) and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
			return false
		end
		if card_0:isKindOf("Snatch") or card_0:isKindOf("Dismantlement") and value < 25 then
			local use = { isDummy = true }
			self:useCardSnatchOrDismantlement(card_0, use)
			if use.card then return false end
		end

		if (card_0:isKindOf("AOE") and self:getAoeValue(card_0) > 35) then return false end

		for _, askill in sgs.qlist(self.player:getVisibleSkillList(true)) do
			local callback = sgs.ai_cardneed[askill:objectName()]
			if type(callback)=="function" and callback(self.player, card_0, self) and value < 45 then
				return false
			end
		end

		local bool_2 = card_0:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(card_0:getId()) == sgs.Player_PlaceEquip))
		local bool_3 = (card_0:isKindOf("EightDiagram") or card_0:isKindOf("RenwangShield") or card_0:isKindOf("Tengu"))
				and ((self.room:getCardPlace(card_0:getId()) == sgs.Player_PlaceEquip)
				or ((self.room:getCardPlace(card_0:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
		if value > 45 then bool_3 = false;bool_2 = false end
		if (bool_2 or bool_3) then return false end
		if (not (card_0:isKindOf("Jink") and bool_k and value < 55) and not (isCard("Peach", card_0, self.player) and self.player:isWounded() and value < 55))
				and not (card_0:isKindOf("Analeptic") and self.player:getHp() == 1 and x < 2 and value < 55) then
			return true
		end
	end
	local function ABCDEF(card)
		local SavageAssault = sgs.Sanguosha:cloneCard("savage_assault", sgs.Card_NoSuit, 0)
		local ArcheryAttack = sgs.Sanguosha:cloneCard("archery_attack", sgs.Card_NoSuit, 0)
		local GodSalvation = sgs.Sanguosha:cloneCard("god_salvation", sgs.Card_NoSuit, 0)
		local AmazingGrace = sgs.Sanguosha:cloneCard("amazing_grace", sgs.Card_NoSuit, 0)
		local IronChain = sgs.Sanguosha:cloneCard("iron_chain", sgs.Card_NoSuit, 0)
		SavageAssault:addSubcard(card)
		ArcheryAttack:addSubcard(card)
		GodSalvation:addSubcard(card)
		AmazingGrace:addSubcard(card)
		IronChain:addSubcard(card)
		local max_value = 0
		local max_card
		if not self.player:isCardLimited(SavageAssault, sgs.Card_MethodUse) and self:getAoeValue(SavageAssault) > max_value
				and judge(card, self:getAoeValue(SavageAssault)) then
			max_value = self:getAoeValue(SavageAssault)
			max_card = SavageAssault
		end
		if not self.player:isCardLimited(ArcheryAttack, sgs.Card_MethodUse) and self:getAoeValue(ArcheryAttack) > max_value
				and judge(card, self:getAoeValue(ArcheryAttack)) then
			max_value = self:getAoeValue(ArcheryAttack)
			max_card = ArcheryAttack
		end
		self.room:writeToConsole("chunhui ceshiS " .. max_value)
		if not self.player:isCardLimited(GodSalvation, sgs.Card_MethodUse) and self:godSalvationValue(GodSalvation)*2.5 > max_value
				and judge(card, self:godSalvationValue(GodSalvation)*2.5) then
			max_value = self:godSalvationValue(GodSalvation)*2.5
			max_card = GodSalvation
		end
		if not self.player:isCardLimited(AmazingGrace, sgs.Card_MethodUse) and self:getAmazingGraceValue(AmazingGrace)*6 > max_value
				and judge(card, self:getAmazingGraceValue(AmazingGrace)*6) then
			max_value = self:getAmazingGraceValue(AmazingGrace)*6
			max_card = AmazingGrace
		end
		if not self.player:isCardLimited(IronChain, sgs.Card_MethodUse) and max_value < 15 and judge(card, 0)
			and #self.enemies > 1 then
			local dummy_use = { isDummy = true , to = sgs.SPlayerList()}
			self:useTrickCard(IronChain, dummy_use)
			if dummy_use.card and dummy_use.to:length() > 0 then
				max_value = 15
				max_card = IronChain
			end
		end
		if max_value and max_card then self.room:writeToConsole("chunhui ceshiR " .. max_value) end
		return max_value, max_card
	end
--[[
	local compare_func = function(a, b)
		local v1 = ABCDEF(a)
		local v2 = ABCDEF(b)

		if v1 ~= v2 then
			return v1 > v2
		else
			return self:getUseValue(a) < self:getUseValue(b)
		end
	end

	table.sort(fcaerds, compare_func)]]--
	self:sortByUseValue(fcaerds, true)
	local toUse = fcaerds[1]
	local Max_value, Max_card = ABCDEF(toUse)
	if Max_card and (Max_value > 15) then
		self.room:writeToConsole("chunhui ceshi")
		return toUse, Max_card
	end
end
local luachunhui_skill = {}
luachunhui_skill.name = "luachunhui"
table.insert(sgs.ai_skills, luachunhui_skill)
luachunhui_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luachunhui") then return end
	if self.player:isKongcheng() then return end
	return sgs.Card_Parse("#luachunhui:.:")
end

sgs.ai_skill_use_func["#luachunhui"] = function(card, use, self)
	local toUse, Max_card = useChunhuiCard(self)
	if not toUse then return end
	use.card = sgs.Card_Parse("#luachunhui:".. toUse:getId() ..":")
	if use.to then
		use.to = sgs.SPlayerList()
		return
	end
end
sgs.ai_use_priority.luachunhui = 4.2
sgs.ai_skill_choice.luachunhui = function(self, choices)
	local cardX = self.room:getTag("luachunhuiTC"):toCard()
	local toUse, Max_card = useChunhuiCard(self)
	if toUse and Max_card then return Max_card:objectName() end
	return "savage_assault"
end

sgs.ai_need_damaged.luabainian = function(self, attacker, player)
	if attacker and player and self:isFriend(attacker, player) and not player:hasFlag("luabainian") then return true end
	return false
end

sgs.ai_skill_discard.luafenshui = function(self, discard_num, min_num, optional, include_equip)
	local target = self.room:getCurrent()
	local x = target:getLostHp()
	local handcard = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(handcard)
	if self:isEnemy(target) and self:isWeak(target) and not target:isNude() and target:isWounded() then
		return handcard[1]:getId()
	end
	if x > 2 then
		local p = target:getEquips():length()
		if target:hasArmorEffect("gale_shell") and p > 0 then p = p - 1 end
		p = p + target:getHandcardNum()
		if p > 2 or (self:isFriend(target) and target:isWounded()) then return handcard[1]:getId() end
	end
	if target:objectName() == self.player:objectName() and self:isWeak() and self.player:isWounded() then
		return handcard[1]:getId()
	end
	for _, friend in ipairs(self.friends_noself) do
		local x2 = self:getEnemyNumBySeat(target, friend, self.player, false, true)
		if self:isEnemy(target) then x2 = x2 - 1 end
		if self:isWeak(friend) and not self:getPlayerBySeat(self.player, target, friend)
			and (x2 < 1 or not friend:isKongcheng()) and friend:isWounded() then
			return {}
		end
	end
	if x == 2 then
		local p = target:getEquips():length()
		if target:hasArmorEffect("gale_shell") and p > 0 then p = p - 1 end
		p = p + target:getHandcardNum()
		if p >= 2 or (self:isFriend(target) and target:isWounded()) and not handcard[1]:isKindOf("ExNihilo") then
			if (handcard[1]:isKindOf("Analeptic") and self.player:getHp() == 1) or (handcard[1]:isKindOf("Peach") and self:isWeak()) then
				return {}
			end
			return handcard[1]:getId()
		end
	end

	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end

	local function Check_R(card)
		if card:isKindOf("TrickCard") then
			if card:isKindOf("IronChain") and not shuxin then return true end
			if card:isKindOf("Lightning") and not self:willUseLightning(card) then return true end
			if card:isKindOf("AOE") and self:getAoeValue(card) > 35 then return false end
			if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
			if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
			local dummy_use = {isDummy = true}
			self:useTrickCard(card, dummy_use)
			if not dummy_use.card then return true end
			return false
		end
		if card:isKindOf("EquipCard") then --or card:isKindOf("DefensiveHorse")
			if card:isKindOf("OffensiveHorse") then
				if self.player:getOffensiveHorse() then return true end
			end
			if card:isKindOf("DefensiveHorse") then
				if self.player:getDefensiveHorse() then return true end
			end
			if card:isKindOf("Armor") and self:needToThrowArmor() then return true end
			if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
				local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
						or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
				if not bool_3 then return true end
			end
			if card:isKindOf("Weapon") then
				local dummy_use = {isDummy = true}
				self:useEquipCard(card, dummy_use)
				if not dummy_use.card then return true end
				if dummy_use.card and self.player:getWeapon() then return true end
			end
		end
		if card:isKindOf("Analeptic") then return false end
		if card:isKindOf("Peach") then return false end
		local x0 = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
		if card:isKindOf("Jink") and x0 <= 1 then return false end
		return true
	end

	if x == 1 then
		local p = target:getEquips():length()
		if target:hasArmorEffect("gale_shell") and p > 0 then p = p - 1 end
		p = p + target:getHandcardNum()
		if p >= 1 or (self:isFriend(target) and target:isWounded()) then
			if Check_R(handcard[1]) then
				return handcard[1]:getId()
			end
		end
	end
	return {}
end

sgs.ai_skill_invoke.luachenti = true
local luachenti_skill = {}
luachenti_skill.name = "luachenti"
table.insert(sgs.ai_skills, luachenti_skill)
luachenti_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luachenti") then return end
	return sgs.Card_Parse("#luachenti:.:")
end

sgs.ai_skill_use_func["#luachenti"] = function(card, use, self)
	use.card = sgs.Card_Parse("#luachenti:.:")
	if use.to then
		for _, friend in ipairs(self.friends) do
			use.to:append(friend)
		end
		return
	end
end
sgs.ai_use_priority.luachenti = 15

sgs.pay_ai_card.Peach.luachenti = function(self, card, use, mustusepeach) 
	if self.player:hasSkill("luachenti") and self.player:getHandcardNum() == 2 and self:getOverflow() < 2 and not mustusepeach then return 2 end
end 


sgs.ai_skill_choice.luafenshui = function(self, choices, data)
	local target = data:toPlayer()
	if self:isFriend(target) then return "recover"; else return "loseHp" end
end

sgs.ai_skill_choice.luafenshui2 = function(self, choices, data)
	local target = data:toPlayer()
	if self:isFriend(target) then return "draw"; else return "discard" end
end

sgs.ai_choicemade_filter.skillChoice.luafenshui = function(self, player, promptlist)
	local target = self.room:getTag("luafenshuiTP"):toPlayer()
	if promptlist[#promptlist] == "recover" then
		sgs.updateIntention(player, target, -60)
	else
		sgs.updateIntention(player, target, 60)
	end
end

sgs.ai_choicemade_filter.skillChoice.luafenshui2 = function(self, player, promptlist)
	local target = self.room:getTag("luafenshuiTP"):toPlayer()
	if promptlist[#promptlist] == "draw" then
		sgs.updateIntention(player, target, -60)
	else
		sgs.updateIntention(player, target, 60)
	end
end

local luahongye_skill = {}
luahongye_skill.name = "luahongye"
table.insert(sgs.ai_skills, luahongye_skill)
luahongye_skill.getTurnUseCard = function(self)
	if not self.player:hasFlag("luahongye1") and self.player:getHandcardNum() == 3 then return sgs.Card_Parse("#luahongye:.:") end
	if not self.player:hasFlag("luahongye2") and self.player:getHandcardNum() == 2 then return sgs.Card_Parse("#luahongye:.:") end
end

sgs.ai_skill_use_func["#luahongye"] = function(card, use, self)
	if self.player:getHandcardNum() == 3 then
		for _, c in sgs.qlist(self.player:getHandcards()) do
			if c:isKindOf("BasicCard") and self.player:isCardLimited(c, sgs.Card_MethodUse) and not self.player:isCardLimited(c, sgs.Card_MethodDiscard) then
				use.card = sgs.Card_Parse("#luahongye:".. c:getId() ..":")
				if use.to then
					use.to:append(self.player)
					return
				end
			end
		end
		for _, c in sgs.qlist(self.player:getHandcards()) do
			if c:isKindOf("BasicCard") then
				if c:isKindOf("Slash") then
					if not self:slashIsAvailable(self.player, c) then
						use.card = sgs.Card_Parse("#luahongye:".. c:getId() ..":")
						if use.to then
							use.to:append(self.player)
							return
						end
					end
					local notu = false
					for _, c2 in sgs.qlist(self.player:getHandcards()) do
						if c2:isKindOf("Analeptic") then
							notu = true
						end
					end
					if self:canKillEnermyAtOnce(false, c) then
						notu = true
					end
					if not notu then
						use.card = sgs.Card_Parse("#luahongye:".. c:getId() ..":")
						if use.to then
							use.to:append(self.player)
							return
						end
					end
				end
			elseif c:isKindOf("Jink") then
				local cards = self:getTurnUse(true)
				if #cards > 1 then
					use.card = sgs.Card_Parse("#luahongye:".. c:getId() ..":")
					if use.to then
						use.to:append(self.player)
						return
					end
				end
				local sd = self:getCardsNum("Jink")
				if sd > 1.8 then
					use.card = sgs.Card_Parse("#luahongye:".. c:getId() ..":")
					if use.to then
						use.to:append(self.player)
						return
					end
				end
			end
		end
	elseif self.player:getHandcardNum() == 2 then
		local dismantlement = sgs.Sanguosha:cloneCard("dismantlement")
		dismantlement:setSkillName("luahongye")
		local dummy_use = { isDummy = false, to = sgs.SPlayerList() }
		self:useTrickCard(dismantlement, dummy_use)
		self.room:writeToConsole("luahongye test")
		if dummy_use.card and dummy_use.card:isKindOf("Dismantlement") and dummy_use.to and dummy_use.to:length() > 0 then
			self.room:writeToConsole("luahongye test" .. dummy_use.to:length())
			use.card = sgs.Card_Parse("#luahongye:.:")
			if use.to then
				use.to:append(dummy_use.to:at(0))
				return
			end
		end
	end
	return
end
sgs.ai_use_priority.luahongye = 15
sgs.ai_skill_invoke.luahongye = true
sgs.ai_skill_invoke.luasilian = function(self, data)
	local target = data:toDamage()
	target = target.to
	if self:isFriend(target) then return false end
	if target:getMark("@luasilian") > 0 then return false end
	return true
end

sgs.ai_skill_invoke.luaguihang = function(self, data)
	local dying = data:toDying()
	local _player = dying.who
	if self:isFriend(_player) then return false end
	if _player:getHp() < 0 then return false end
	if _player:getHandcardNum() == 1 and self:getKnownNum(_player) == _player:getHandcardNum() then
		for _, cx in sgs.qlist(_player:getHandcards()) do
			if cx:isKindOf("Peach") or cx:isKindOf("Analeptic") then
				for _, c2 in sgs.qlist(self.player:getHandcards()) do
					if c2:getNumber() <= cx:getNumber() then return true end
				end
			end
		end
	end
	local function Check_R(card)
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return end
		end
		if card:isKindOf("Peach") and not self:OverFlowPeach(card) then return false end
		if self:isWeak() and card:isKindOf("Jink") then return false end
		if card:isKindOf("ExNihilo") then return false end
		if (card:isKindOf("AOE") and self:getAoeValue(card) > 75) then return false end
		return true
	end
	for _, c in sgs.qlist(self.player:getHandcards()) do
		if Check_R(c) then return true end
	end
	return false
end

function sgs.ai_skill_pindian.luaguihang(minusecard, self, requestor, maxcard, mincard)
	if self.player:objectName() == requestor:objectName() then
		local req
		for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
			if p:hasFlag("luaguihang_Target") then
				req = p
				break
			end
		end
		local to_card = self:getMaxCard()
		if req:getHandcardNum() == 1 and self:getKnownNum(req) == req:getHandcardNum() then
			for _, cx in sgs.qlist(req:getHandcards()) do
				if cx:isKindOf("Peach") or cx:isKindOf("Analeptic") then
					for _, c2 in sgs.qlist(self.player:getHandcards()) do
						if c2:getNumber() <= cx:getNumber() then return c2 end
					end
				end
			end
		end
		local function Check_R(card)
			local nazrin = self.room:findPlayerBySkillName("luatanbao")
			if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
				if card:hasFlag("prelingbai") then return end
			end
			if card:isKindOf("Peach") and not self:OverFlowPeach(card) then return false end
			if self:isWeak() and card:isKindOf("Jink") then return false end
			if card:isKindOf("ExNihilo") then return false end
			if (card:isKindOf("AOE") and self:getAoeValue(card) > 75) then return false end
			return true
		end
		local x = req:getHandcardNum()
		x = (x * x)/30
		local k = math.random()
		for _, c2 in sgs.qlist(self.player:getHandcards()) do
			if self:getMaxCard(req) and c2:getNumber() > self:getMaxCard(req):getNumber() and Check_R(c2)
					and (self:getKnownNum(req) == req:getHandcardNum()) then return to_card end
		end
		if to_card:getNumber() > 10 and (k > (0.1 + x)) and Check_R(to_card) then return to_card end
		if to_card:getNumber() > 8 and (k > (0.25 + x)) and Check_R(to_card) then return to_card end
		if to_card:getNumber() > 6 and (k > (0.6 + x)) and Check_R(to_card) then return to_card end

		local cards = sgs.QList2Table(self.player:getHandcards())
		self:sortByKeepValue(cards)
		to_card = cards[1]
		if Check_R(to_card) then return to_card end
	end
end

sgs.ai_skill_invoke.luajuntuan = function(self, data)
	return #self.enemies > 1
end
sgs.ai_skill_playerchosen.luajuntuan2 = function(self, targets)
	local targetlist = sgs.QList2Table(targets)
	self:sort(targetlist, "defenseSlash")
	for _, target in ipairs(targetlist) do	--杀敌
		if self:isEnemy(target) and target:objectName() ~= self.player:objectName()
			and self:hasTrickEffective(sgs.Sanguosha:cloneCard("ArcheryAttack"), target, self.player) then return target end
	end
end
sgs.ai_skill_playerchosen.luahongyuan = function(self, targets)
	for _, target in sgs.qlist(targets) do
		if self:isFriend(target) then
			return target
		end
	end
end
sgs.ai_skill_playerchosen.luajuntuan = function(self, targets)
	local targetlist = sgs.QList2Table(targets)
	self:sort(targetlist, "defense")
	for _, target in ipairs(targetlist) do	--杀敌
		if self:isFriend(target) and target:objectName() ~= self.player:objectName() then return target end
	end
end

local function findShenquanCard(self)
	for _, cx in sgs.qlist(self.player:getHandcards()) do
		if cx:isKindOf("AOE") and #self.enemies > 1 then return cx end
	end
	for _, cx in sgs.qlist(self.player:getHandcards()) do
		if cx:isKindOf("Duel") and #self.enemies > 1 then return cx end
	end
	for _, cx in sgs.qlist(self.player:getHandcards()) do
		if cx:isKindOf("ExNihilo") and #self.friends > 1 then return cx end
	end
	for _, cx in sgs.qlist(self.player:getHandcards()) do
		if cx:isKindOf("Peach") and #self.friends > 1 then return cx end
	end
	for _, cx in sgs.qlist(self.player:getHandcards()) do
		if cx:isKindOf("Slash") and #self.enemies > 1 then return cx end
	end
	for _, cx in sgs.qlist(self.player:getHandcards()) do
		if cx:isKindOf("Snatch") and #self.enemies > 1 then return cx end
	end
	for _, cx in sgs.qlist(self.player:getHandcards()) do
		if cx:isKindOf("Dismantlement") and #self.enemies > 1 then return cx end
	end
end
sgs.ai_skill_use["@@luashenquan"] = function(self, prompt, method)
    local cardA = findShenquanCard(self)
    if cardA then
		local targets = sgs.SPlayerList()
		local function pCheck(to_select)
			return not self.player:isProhibited(to_select, cardA, to_select:getSiblings())
					and not (cardA:isKindOf("Peach") and not to_select:isWounded())
		end
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if pCheck(p) then targets:append(p) end
		end
		local Carddata2 = sgs.QVariant() -- ai用
		Carddata2:setValue(cardA)
		self.room:setTag("luajianjiTC", Carddata2)
		self.room:setPlayerFlag(self.player, "luajianjiQ")
		local target1 = sgs.ai_skill_playerchosen.luajianjic(self, targets)
		if not target1 then self.room:setPlayerFlag(self.player, "-luajianjiQ"); self.room:removeTag("luajianjiTC"); return "." end
		targets = sgs.SPlayerList()
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if pCheck(p) and p:objectName() ~= target1:objectName() then targets:append(p) end
		end
		local target2 = sgs.ai_skill_playerchosen.luajianjic(self, targets)
		if not target2 then self.room:setPlayerFlag(self.player, "-luajianjiQ"); self.room:removeTag("luajianjiTC"); return "." end
		self.room:removeTag("luajianjiTC")
		self.room:setPlayerFlag(self.player, "-luajianjiQ")
		return "#luashenquan:".. cardA:getId() .. ":->" .. target1:objectName() .. "+" .. target2:objectName()
	end
	return "."
end
sgs.ai_skill_invoke.luashenquan = function(self, data)
	if findShenquanCard(self) then return true end
	return false
end
sgs.ai_skill_discard.luashenquan = function(self, discard_num, min_num, optional, include_equip)
	local bool_1 = false
	if self.room:getCurrent():objectName() ~= self.player:objectName() then
		local x2 = self:getEnemyNumBySeat(self.room:getCurrent(), self.player, self.player, true, true)
		if x2 == 0 then
			bool_1 = true
		end
	end

	local function Check_R(card)
		local nyasama = self.room:findPlayerBySkillName("luawanbang")

		local suit
		if nyasama then
			if nyasama:getMark("@lianmengheart") > 0 then
				suit = sgs.Card_Heart
			elseif nyasama:getMark("@lianmengdiamond") > 0 then
				suit = sgs.Card_Diamond
			elseif nyasama:getMark("@lianmengclub") > 0 then
				suit = sgs.Card_Club
			elseif nyasama:getMark("@lianmengspade") > 0 then
				suit = sgs.Card_Spade
			end

			if suit and self:isFriend(nyasama) and card:getSuit() == suit then return true end
		end

		if card:isKindOf("TrickCard") then
			if card:isKindOf("quanxiang") or card:isKindOf("AmazingGrace") then return true end
			if card:isKindOf("SupplyShortage") then
				for _, enemy in ipairs(self.enemies) do
					if self:getSupplyShortageValue(enemy, self.enemies) > 3 then return false end
				end
				return true
			end
			if card:isKindOf("Lightning") or card:isKindOf("GirlChoosen") then return true end
			if card:isKindOf("FireAttack") or card:isKindOf("IronChain") then return true end
		end
		if card:isKindOf("EquipCard") then
			if card:isKindOf("OffensiveHorse") and (self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) then
				if self.player:getOffensiveHorse() then return true end
			end
			if card:isKindOf("DefensiveHorse") then
				return true
			end
			if card:isKindOf("Armor") and self:needToThrowArmor() then return true end
			if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
				local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
						or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
				if not bool_3 then return true end
			end
			if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip) then return false end
			if card:isKindOf("Weapon") then
				local dummy_use = {isDummy = true}
				self:useEquipCard(card, dummy_use)
				if not dummy_use.card then return true end
				if dummy_use.card and self.player:getWeapon() then return true end
			end
		end
		local x0 = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
		if card:isKindOf("Analeptic") then return true end
		if card:isKindOf("Jink") then
			if x0 <= 1 and not bool_1 then return false end
			return true
		end
		if card:isKindOf("Slash") and (self:getCardsNum("Slash") > 1 or self.player:hasWeapon("hakkero")) then return true end
		return false
	end

	local fcaerds = sgs.QList2Table(self.player:getCards("he"))
	self:sortByUseValue(fcaerds, true)
	for _, cardA in ipairs(fcaerds) do
		if Check_R(cardA) then return cardA:getId() end
	end
	return {}
end

sgs.ai_skill_invoke.luarudao = function(self, data)
	if self:canKillEnermyAtOnce() then return true end
    if sgs.ai_skill_discard.luashenquan(self) == {} then return false end
    if sgs.ai_skill_use["@@luashenquan"](self) ~= "." then
		for _, enemy in ipairs(self.enemies) do
			if self:isWeak(enemy) and self.player:inMyAttackRange(enemy) then return true end
		end
		if math.random() < 0.4 then return true end
	end
end

local luafuzhengr_skill = {}
luafuzhengr_skill.name = "luafuzhengr"
table.insert(sgs.ai_skills, luafuzhengr_skill)
luafuzhengr_skill.getTurnUseCard = function(self)
	local hasXL
	for _, p in sgs.qlist(self.room:getAllPlayers()) do
		if p:hasSkill("luafuzheng") and p:isAlive() then
			hasXL = p
		end
	end
	if self.player:hasUsed("#luafuzhengr") or (not hasXL) then return end
	if self:isEnemy(hasXL) then return end
	return sgs.Card_Parse("#luafuzhengr:.:")
end
sgs.ai_skill_use_func["#luafuzhengr"] = function(X, use, self)

	local cards = sgs.QList2Table(self.player:getCards("he"))
	if #cards == 0 then
		use.card = sgs.Card_Parse("#luafuzhengr:.:")
		if use.to then
			use.to = sgs.SPlayerList()
		end
	end
	--[[self:sortByKeepValue(cards)

	if cards[1]:isKindOf("Peach") and not self:OverFlowPeach(cards[1]) then return end
	if self:isWeak() and cards[1]:isKindOf("Jink") then return end
	if cards[1]:isKindOf("ExNihilo") then return end
	if (cards[1]:isKindOf("AOE") and self:getAoeValue(cards[1]) > 70) then return end
	local bool_3 = (cards[1]:isKindOf("EightDiagram") or cards[1]:isKindOf("RenwangShield"))
			and ((self.room:getCardPlace(cards[1]:getId()) == sgs.Player_PlaceEquip)
			or ((self.room:getCardPlace(cards[1]:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
	if bool_3 then return end
	if cards[1]:isKindOf("Wanbaochui") then return end]]--
	local mio = self.room:findPlayerBySkillName("luafuzheng")
	if self:isFriend(mio) or self.player:hasSkill("luajifeng") or (self.player:getMark("luashikong") < 2 and self.player:hasSkill("luashikong")) then
		use.card = sgs.Card_Parse("#luafuzhengr:.:")
		if use.to then
			use.to = sgs.SPlayerList()
		end
	end
	return
end
sgs.ai_skill_discard.luafuzhengr = function(self, discard_num, min_num, optional, include_equip)
	local handcards = sgs.QList2Table(self.player:getHandcards())
	self:sortByUseValue(handcards, true)
	if self.player:isNude() then return end
    if self:needToThrowArmor() then return self.player:getArmor():getEffectiveId() end
    if self.player:getDefensiveHorse() then
        for _, enemy in ipairs(self.enemies) do
            if enemy:distanceTo(self.player) <= 1 and not self:isWeak() then
                return self.player:getDefensiveHorse():getEffectiveId()
            end
        end
    end
	if self.player:getWeapon() then
		for _, acard in ipairs(handcards) do
			if acard:isKindOf("Weapon") then
				local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
				self:useEquipCard(acard, dummy_use)
				if dummy_use.card then
					return self.player:getWeapon():getEffectiveId()
				end
			end
		end
	end
	if #handcards > 0 then
		for _, acard in ipairs(handcards) do
			for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skill:objectName()]
				if not (type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](self.player, acard, self)) then
					return acard:getEffectiveId()
				end
			end
		end
		return handcards[1]:getEffectiveId()
	end
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)

	return cards[1]:getEffectiveId()
end
sgs.ai_use_priority.luafuzhengr = 8

sgs.ai_skill_playerchosen.luazhuni = function(self, targets)
	local friends = self.friends
	self:sort(friends, "defense2")
	for _, friend in ipairs(friends) do
		if targets:contains(friend) then
			return friend
		end
	end
end
sgs.ai_skill_invoke.luazhuni = function(self, data)
	if self.player:getMark("@luafuzheng") < 5 or self.player:getMark("luazhuni") > 0 then
		if self.player:getMark("@luafuzheng") < 2 then return false end
		local x = self.player:getMark("@luafuzheng")
		if x + #self.friends == 5 then return false end
		return true
	end
	local target = self:Mio()
	if target then return true end
	return false
end
sgs.ai_skill_choice.luazhuniK = function(self, choices)
	if self:Mio() then return "luazhuni2" end
	return "luazhuni1"
end
sgs.ai_skill_choice.luazhuni = function(self, choices)
	local y = self.player:getMark("@luafuzheng")
	local x = math.min(#self.friends + 1, y)
	return tostring(x)
end
sgs.ai_skill_playerchosen.luazhuni2 = function(self, targets)
	local target = self:Mio()
	if target then return target end
	return self.enemies[1]
end
sgs.ai_playerchosen_intention.luazhuni = -20
sgs.ai_playerchosen_intention.luazhuni2 = 80
sgs.ai_skill_cardask["luazhuni"] = function(self)
	local slashid = self:getCardId("Slash", self.player, nil , true)
	if not slashid then return "." end
	local target
	for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if player:hasFlag("SlashAssignee") then target = player break end
	end
	if self:isFriend(target) then return "." end
	return slashid or "."
end

sgs.ai_skill_invoke.luahejun = function(self, data)
	return #self.friends > 1
end
sgs.ai_skill_playerchosen.luahejun = function(self, targets)
	self:sort(self.friends, "defense2")
	for _, friend in ipairs(self.friends_noself) do
		if getCardsNum("Slash", friend, self.player) < 0.5 then
			return friend
		end
	end
	for _, friend in ipairs(self.friends_noself) do
		return friend
	end
end

sgs.ai_skill_invoke.luaqinjian = function(self, data)
	local use = data:toCardUse()
	local x = use.from
	return self:isFriend(x)
end
sgs.ai_choicemade_filter.skillInvoke["luaqinjian"] = function(self, player, promptlist)
	local target = self.room:getTag("luaqinjianTP"):toPlayer()
	if promptlist[#promptlist] == "yes" then
		sgs.updateIntention(player, target, -20)
	else
		sgs.updateIntention(player, target, 20)
	end
end



local luaxianglinr_skill = {}
luaxianglinr_skill.name = "luaxianglinr"
table.insert(sgs.ai_skills, luaxianglinr_skill)

luaxianglinr_skill.getTurnUseCard = function(self)
	local xp = self.room:findPlayerBySkillName("luaxianglin")
	if not xp then return end
	if not xp:isAlive() then return end
	if xp:isWounded() and self:isFriend(xp) then
		return sgs.Card_Parse("#luaxianglinr:.:")
	end
end
sgs.ai_skill_use_func["#luaxianglinr"] = function(X, use, self)
	for _, c in sgs.qlist(self.player:getHandcards()) do
		if c:isKindOf("EquipCard") then
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			self:useEquipCard(c, dummy_use)
			if dummy_use.card and self:getSameEquip(c) then
				use.card = sgs.Card_Parse("#luaxianglinr:".. self:getSameEquip(c):getEffectiveId() .. ":")
				if use.to then
					use.to = sgs.SPlayerList()
				end
			end
		end
	end

	if self:needToThrowArmor() then
		use.card = sgs.Card_Parse("#luaxianglinr:".. self.player:getArmor():getEffectiveId() .. ":")
		if use.to then
			use.to = sgs.SPlayerList()
		end
	end
	local xp = self.room:findPlayerBySkillName("luaxianglin")
	if self:isWeak(xp) then
		if self.player:getDefensiveHorse() then
			local could = true
			for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skill:objectName()]
				if type(callback) == "function" and callback(self.player, self.player:getDefensiveHorse(), self) then
					could = false
				end
			end
			if could then
				use.card = sgs.Card_Parse("#luaxianglinr:".. self.player:getDefensiveHorse():getEffectiveId() .. ":")
				if use.to then
					use.to = sgs.SPlayerList()
				end
			end
		end

		if self.player:getOffensiveHorse() then
			local could = true
			for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skill:objectName()]
				if type(callback) == "function" and callback(self.player, self.player:getOffensiveHorse(), self) then
					could = false
				end
			end
			if could then
				use.card = sgs.Card_Parse("#luaxianglinr:".. self.player:getOffensiveHorse():getEffectiveId() .. ":")
				if use.to then
					use.to = sgs.SPlayerList()
				end
			end
		end

		if self.player:getArmor() then
			local could = true
			for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skill:objectName()]
				if type(callback) == "function" and callback(self.player, self.player:getArmor(), self) then
					could = false
				end
			end
			if could then
				use.card = sgs.Card_Parse("#luaxianglinr:".. self.player:getArmor():getEffectiveId() .. ":")
				if use.to then
					use.to = sgs.SPlayerList()
				end
			end
		end

		if self.player:getWeapon() then
			local could = true
			for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skill:objectName()]
				if type(callback) == "function" and callback(self.player, self.player:getWeapon(), self) then
					could = false
				end
			end
			if could then
				use.card = sgs.Card_Parse("#luaxianglinr:".. self.player:getWeapon():getEffectiveId() .. ":")
				if use.to then
					use.to = sgs.SPlayerList()
				end
			end
		end
	end
end
sgs.ai_use_priority.luaxianglinr = 16
sgs.ai_card_intention.luaxianglinr = function(self, card, from, tos)
	local xp = self.room:findPlayerBySkillName("luaxianglin")
	sgs.updateIntention(from, xp, -40)
end

sgs.ai_skill_choice.luaxinlun = function(self, choices)  --self.room:getLord():getMark("@clock_time")
	local xo = self.room:getLord():getMark("@clock_time")
	if self.player:getMark("luaxinlun") >= 2 then
		if xo > 9 then return "luaxinlun2" end
		return "luaxinlun1"
	end
	if xo > 13 then return "luaxinlun2" end
	local a = 0
	local b = 0
	a = (90 - xo) / 2
	b = (xo - 90) / 2
	local acards = self:getTurnUse(true)
	for _,card in ipairs(acards) do
		if card:getNumber() == xo + 1 then a = a + 40 end
		if card:getNumber() == xo - 1 then b = b + 40 end
		if card:getNumber() == xo + 2 then a = a + 20 end
		if card:getNumber() == xo - 2 then b = b + 20 end
	end
	if self:RENKO(sgs.QList2Table(self.player:getCards("he")), xo + 1) then a = a + 40 end  --磨刀不误砍柴工  2021年1月20日17:03:52
	if self:RENKO(sgs.QList2Table(self.player:getCards("he")), xo - 1) then b = b + 40 end
	if a >= b then return "luaxinlun1" end
	return "luaxinlun2"
end
sgs.ai_skill_askforag.luaxinlun = function(self, card_ids)
	local cards = {}
	for _, card_id in ipairs(card_ids) do
		table.insert(cards, sgs.Sanguosha:getCard(card_id))
	end
	local function check_F(card)
		if card:isKindOf("Peach") and self.player:isWounded() then return false end
		if card:isKindOf("ExNihilo") and card:isAvailable(self.player) then return false end
		if card:isKindOf("Indulgence") then return false end
		if card:isKindOf("Wanbaochui") or card:isKindOf("Yanshi") then return false end
		if self:ThreeCheck(card) then return false end
		if card:isKindOf("Hui") and self:isWeak() then return false end
		 local bool_3 = (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu"))
				and ((self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
				or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
		if bool_3 then return false end
		if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
		return true
	end
	for _,card in ipairs(cards) do
		if not check_F(card) then
			return card:getId()
		end
	end
	for _,card in ipairs(cards) do
		if card:getNumber() == self.room:getLord():getMark("@clock_time") then
			return card:getId()
		end
	end
end
local luaxianlun_skill = {}
luaxianlun_skill.name = "luaxianlun"
luaxianlun_skill.getTurnUseCard = function(self)
	return sgs.Card_Parse("#luaxianlun:.:")
end
local function HowtoUse(self)
	if (self.player:getMark("drank") == 0) then
		local peach = sgs.Sanguosha:cloneCard("peach", sgs.Card_NoSuit, 0)
		local dummyuseW = { isDummy = true }
		self:useBasicCard(peach, dummyuseW)
		if dummyuseW.card then
			return "peach"
		end
	end

	if not self:slashIsAvailable() then return end
	local slash = {}
	slash[1] = "fire_slash"
	slash[2] = "thunder_slash"
	slash[3] = "slash"
	for i = 1, 3 do
		local basic2 = sgs.Sanguosha:cloneCard(slash[i], sgs.Card_NoSuit, 0)
		if self:YouMu() then
			local dummyuse = { isDummy = true }
			self:useBasicCard(basic2, dummyuse)
			if dummyuse.card then
				return slash[i]
			end
		end
	end
end
sgs.ai_skill_use_func["#luaxianlun"] = function(X, use, self)
	local xo = self.room:getLord():getMark("@clock_time")
	local cardS = self:RENKO(sgs.QList2Table(self.player:getCards("he")), xo)
	if not cardS then return end
	if #cardS == 0 then return end
	local userstring = HowtoUse(self)
	if not userstring then return end
	local taoluancard = sgs.Sanguosha:cloneCard(userstring, sgs.Card_SuitToBeDecided, -1)

	taoluancard:setSkillName("luaxianlun")
	for _,card in ipairs(cardS) do
		taoluancard:addSubcard(card)
	end

	local dummy_use = { isDummy = false , to = sgs.SPlayerList() }

	self:useBasicCard(taoluancard, dummy_use)
	if not dummy_use.card then return end
	if dummy_use.to and not dummy_use.to:isEmpty() then
		if dummy_use.to[1] then
			self.room:writeToConsole("sanae test target" .. dummy_use.to[1]:objectName())
		end
	end
	use.card = taoluancard
	if use.to then
		use.to = dummy_use.to
		return
	end
	return
end
sgs.ai_use_priority.luaxianlun = 8
sgs.ai_cardsview["luaxianlun"] = function(self, class_name, player)
	local xo = self.room:getLord():getMark("@clock_time")
	if self.player:hasSkill("luaxianlun") and player:objectName() == self.player:objectName() then
		local cardS = self:RENKO(sgs.QList2Table(self.player:getCards("he")), xo)
		if not cardS then return end
		if #cardS == 0 then return end
		local abc = string.lower(class_name)
		local slash = sgs.Sanguosha:cloneCard(abc)
		if not slash then return end
		if (not slash:isKindOf("BasicCard")) and not slash:isKindOf("Nullification") then return end
		local string = abc .. ":luaxianlun[to_be_decided:" .. tostring(xo) .. "]="
		local cardids = {}
		for _,c in ipairs(cardS) do	--yun
			table.insert(cardids, tostring(c:getEffectiveId()))
		end
		cardids = table.concat(cardids, "+")
		string = string .. cardids
		return string
	end
end
sgs.ai_cardneed.luaxinlun = function(to, card, self)
	if not self then return false end
	local xo = self.room:getLord():getMark("@clock_time")
	if to:hasSkill("luaxinlun") then
		if card:getNumber() == xo then return true end
	end
end

sgs.ai_skill_invoke.luafuze = true
sgs.ai_skill_cardask["@luafuze"] = function(self, data)
	if self.player:isNude() then return "." end
	local cards = sgs.QList2Table(self.player:getCards("he"))
	local function Point(cardA)
		local x = self:getKeepValue(cardA)
		local q = self.player:getMaxHp() * 2 - self.player:getHandcardNum()
		if cardA:isKindOf("ExNihilo") and q < 2 then
			return 1
		end
		if (cardA:isKindOf("Snatch") or cardA:isKindOf("AmazingGrace") or cardA:isKindOf("FaithCollection")) and q < 1 then
			return 0
		end
		return x
	end
	local function compare_func1(a, b)
		return Point(a) < Point(b)
	end
	table.sort(cards, compare_func1)
	return "$" ..  cards[1]:getEffectiveId()
end


























