---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON><PERSON>)
--- Created by Administrator.
--- DateTime: 2019/11/6 18:26
 
module("extensions.pay99",package.seeall)
extension = sgs.Package("pay99")


math.randomseed(tostring(os.time()):reverse():sub(1, 7))
hifu = sgs.General(extension,"hifu","qun",3,false,false,false)
hifuA = sgs.General(extension,"hifuA","qun",3,false,true,true)
hifuB = sgs.General(extension,"hifuB","qun",3,false,true,true)
hifuC = sgs.General(extension,"hifuC","qun",3,false,true,true)
hifuD = sgs.General(extension,"hifuD","qun",3,false,true,true)
hifuE = sgs.General(extension,"hifuE","qun",3,false,true,true)
hifuF = sgs.General(extension,"hifuF","qun",3,false,true,true)

zhengzhen = sgs.General(extension,"zhengzhen","luaxing",4,false,false,false)
sangetsusei = sgs.General(extension, "sangetsusei", "luaxing",3,false,false,false)
sangetsuseiA = sgs.General(extension, "sangetsuseiA", "luaxing",3,false,true,true)
sangetsuseiB = sgs.General(extension, "sangetsuseiB", "luaxing",3,false,true,true)
sangetsuseiC = sgs.General(extension, "sangetsuseiC", "luaxing",3,false,true,true)


jacket_label = sgs.General(extension, "jacket_label", "luaxi",4,false,false,false)

HappySkillAnjiang = sgs.General(extension, "HappySkillAnjiang", "god", 7, true, true, true)
SkillAnjiang = sgs.General(extension, "SkillAnjiang", "god", 7, true, true, true)
BaibanN = sgs.General(extension, "BaibanN", "god", 2, false, true, true)

local function ToDeath(ori_acq, general, player, room)
	local kill = true
	for i = 1, #ori_acq do
		if string.find(general, ori_acq[i]) then
			kill = false
		end
	end
	if kill then room:killPlayer(player) end
end
local function ChangeSkin(GeneralName, kplayer, room, modeCode)
	local NewGeneralName = GeneralName
	--modeCode 1表示正常局  2表示混沌局
				if GeneralName == "reimu" then
					local str = "reimu+reimuA+reimuB+reimuC+reimuD+reimuE+reimuX"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "yuka" then
					local str = "yuka+yukaA"
					if kplayer:getState() ~= "robot" and modeCode ~= 1 then
						str = "yuka+yukaA+yukaB"
					end 
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room) 
				elseif GeneralName == "yumemi" then
					local str = "yumemi+yumemiA+yumemiB+yumemiC"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "rh_flandre" then
					if (string.find(kplayer:screenName(), "Kevin")) or (string.find(kplayer:screenName(), "kevin")) then
						local str = "rh_flandreH+rh_flandreA+rh_flandreB+rh_flandreC+rh_flandreD+rh_flandreE+rh_flandreF+rh_flandreG"
						local general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					else
						local str = "rh_flandre+rh_flandreA+rh_flandreB+rh_flandreC+rh_flandreD+rh_flandreE+rh_flandreF+rh_flandreG"
						local general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					end
				elseif GeneralName == "hifu" then
					local str = "hifu+hifuA+hifuB+hifuC+hifuD+hifuE+hifuF"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "aya" then
					if (string.find(kplayer:screenName(), "诺多")) then
						local general = room:askForGeneral(kplayer, "aya+ayaA+ayaB+ayaC+ayaD+sp_aya")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
					elseif kplayer:getState() == "robot" then
						general = room:askForGeneral(kplayer, "aya+ayaA+ayaB")
					else
						local str = "aya+ayaA+ayaB+ayaC+ayaD+ayaE"
						local general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					end
				elseif GeneralName == "yuyuko" then
					local str = "yuyuko+yuyukoA+yuyukoB+yuyukoC+yuyukoD"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "cirno" then
					local general
					if kplayer:getState() == "robot" then
						general = room:askForGeneral(kplayer, "cirno+cirnoA+cirnoB+cirnoC+cirnoE")
					else
						if (string.find(kplayer:screenName(), "月半")) then
							general = room:askForGeneral(kplayer, "cirno+cirnoA+cirnoB+cirnoC+cirnoD+cirnoE")
						else
							general = room:askForGeneral(kplayer, "cirno+cirnoA+cirnoB+cirnoC+cirnoE")
						end
					end
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					if general == "accirno" then
						room:notifySkillInvoked(kplayer, "luaqijian")
						local card_ids = room:getNCards(7)
						kplayer:addToPile("sword", card_ids, false)
					end
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "rumia" then
					local general
					if kplayer:getState() == "robot" then
						general = room:askForGeneral(kplayer, "rumia+rumiaA+rumiaB")
					else
						local str = "rumia+rumiaA+rumiaB+ex_rumia"
						general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					end 
				elseif GeneralName == "qinxin" then
					local str = "qinxin+kokoroA+kokoroB+kokoroC+kokoroD"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "sanae" then
					local str = "sanae+sanaeA+sanaeB+sanaeC+sanaeD"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "patchouli" then
					local str = "patchouli+patchouliA+patchouliB+patchouliC+patchouliD"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "hiziri" then
					local str = "hiziri+hiziriA+hiziriB+hiziriC+hiziriD"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "akyuu" then
					local str = "akyuu+akyuuA+akyuuB+akyuuC+sp_akyuuB"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					if general == "sp_akyuuB" then
						local x = kplayer:getMaxHp()
						room:setPlayerProperty(kplayer, "hp", sgs.QVariant(x - 7)) 
					end 
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "marisa" then
					if kplayer:getState() == "robot" then
						local str = "marisa"
						local general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					else
						local str = "marisaA+marisaB+marisaC+marisaD+marisaE+sp_marisa"
						local general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					end
				elseif GeneralName == "shinki" then
					local general = room:askForGeneral(kplayer, "shinki")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "sp_rin" then
					local str = "sp_rin+sp_rinA+sp_rinB"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "iku" then
					local str = "iku+ikuA+ikuB"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "youmu" then
					local str = "youmu+youmuA+youmuB+youmuC"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "seiga" then
					local str = "seiga+seigaA+seigaB+seigaC"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "remilia" then
					local str = "remilia+remiliaA+remiliaB+remiliaC"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "sp_youmu" then
					local str = "sp_youmu+sp_youmuA+sp_youmuB"
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					room:writeToConsole("ChangeHero testAAAA")
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "eternity" then
					local str = "eternity+eternityA+eternityB+eternityC"
					if kplayer:getState() == "robot" then
						str = "eternity+eternityA+eternityB"
					end 
					if room:getAlivePlayers():length() > 6 then
						str = "eternity+eternityA+eternityB"
					end 
					local general = room:askForGeneral(kplayer, str)
					local tableX = str:split("+")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
					ToDeath(tableX, general, kplayer, room)
				elseif GeneralName == "hina" then
					local general = room:askForGeneral(kplayer, "hina+hinaA+hinaB")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "Rh_junko" then
					local general = room:askForGeneral(kplayer, "Rh_junko+Rh_junkoA+Rh_junkoB")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "kaguya" then
					local general = room:askForGeneral(kplayer, "kaguya+kaguyaA+kaguyaB+kaguyaC+kaguyaD")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "reisen" then
					local general = room:askForGeneral(kplayer, "reisen+reisenA+reisenB+reisenC+reisenD")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "toyosatomimi" then
					local general = room:askForGeneral(kplayer, "toyosatomimi+toyosatomimiA+toyosatomimiB+toyosatomimiC+toyosatomimiD+toyosatomimiE")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "parsee" then
					local general = room:askForGeneral(kplayer, "parseeA")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "nue" then
					local general = room:askForGeneral(kplayer, "nueA")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "sakuya" then
					local general = room:askForGeneral(kplayer, "sakuyaO+sakuyaA+sakuyaB+sakuyaC+sakuyaD")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "white" then
					if kplayer:getState() ~= "robot" then
						local str = "white+whiteA+whiteB+whiteC+whiteD"
						local general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					else
						local str = "white+whiteA+whiteC+whiteD"
						local general = room:askForGeneral(kplayer, str)
						local tableX = str:split("+")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
						ToDeath(tableX, general, kplayer, room)
					end
				elseif GeneralName == "sagume" then
					local general = room:askForGeneral(kplayer, "sagume+sagumeA+sagumeB+sagumeC")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "koakuma" then
					local general = room:askForGeneral(kplayer, "koakuma+koakumaA+koakumaB")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "akong" then
					local general = room:askForGeneral(kplayer, "akongO+akongA+akongB+akongC+akongD")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "daiyousei" then
					local general = room:askForGeneral(kplayer, "daiyousei+daiyouseiA+daiyouseiB+daiyouseiC+daiyouseiD")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "suika" then
					local general = room:askForGeneral(kplayer, "suika+suikaA+suikaB+suikaC+suikaD+suikaE")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "yukariex" then
					if (string.find(kplayer:screenName(), "魔理")) then
						local general = room:askForGeneral(kplayer, "yukariex+yukariexA+yukariexB+yukariexC+yukariexD")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
					else
						local general = room:askForGeneral(kplayer, "yukariex+yukariexA+yukariexB+yukariexC+yukariexD")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
						room:setPlayerMark(kplayer, "hasChanged", 1)
					end
				elseif GeneralName == "koishi" then
					local general = room:askForGeneral(kplayer, "koishi+koishiA+koishiB+koishiC")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "clownpiece" then
					local general = room:askForGeneral(kplayer, "clownpiece+clownpieceA+clownpieceB")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "alice" then
					local general = room:askForGeneral(kplayer, "alice+aliceA+aliceB+aliceC+aliceD+aliceE")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "futo" then
					local general = room:askForGeneral(kplayer, "futo+futoA+futoB+futoC")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "sangetsusei" then
					local general = room:askForGeneral(kplayer, "sangetsusei+sangetsuseiA+sangetsuseiB+sangetsuseiC")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "prismriver" then
					local general = room:askForGeneral(kplayer, "prismriver+prismriverA+prismriverB")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "kutaka" then
					local general = room:askForGeneral(kplayer, "kutaka+kutakaA+kutakaB+kutakaC")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "keiki" then
					local general = room:askForGeneral(kplayer, "keiki+keikiA+keikiB+keikiC")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerProperty(kplayer, "maxhp", sgs.QVariant(1))
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "momiji" then
					local general = room:askForGeneral(kplayer, "momiji+momijiA+momijiB+momijiC+momijiD")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "sumireko" then
					local general = room:askForGeneral(kplayer, "sumireko+sumirekoA+sumirekoB+sumirekoC+sumirekoD")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "joon" then
					if (string.find(kplayer:screenName(), "arisa")) then
						local general = room:askForGeneral(kplayer, "joon+yorigami+shion")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
					elseif kplayer:getState() ~= "robot" then
						local general = room:askForGeneral(kplayer, "joon+shion")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
					end
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "tewi" then
					if (string.find(kplayer:screenName(), "⑨")) then
						local general = room:askForGeneral(kplayer, "tewi+sp_tewi")
						if modeCode == 1 then 
							room:changeHero(kplayer, general, true, false, false, false) 
						end 
						NewGeneralName = general
					end
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "keine" then
					local general = room:askForGeneral(kplayer, "keine+keineA+keineB")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1)
				elseif GeneralName == "kitcho" then
					local general = room:askForGeneral(kplayer, "kitcho+kitchoA+kitchoB")
					if modeCode == 1 then 
						room:changeHero(kplayer, general, true, false, false, false) 
					end 
					NewGeneralName = general
					room:setPlayerMark(kplayer, "hasChanged", 1) 
				elseif GeneralName == "toone" then
                    local general = room:askForGeneral(kplayer, "toone+yukariY")
                    if modeCode == 1 then 
                        room:changeHero(kplayer, general, true, false, false, false) 
                    end 
                    NewGeneralName = general
                    room:setPlayerMark(kplayer, "hasChanged", 1)
				end 
	return NewGeneralName
end 


--[[
规则：游戏开始选将
内容：1、采用18选3的设定，座位按 1（主） 2（反） 3（忠） 4（反） 5（反） 6（忠） 设置
2、1号位先选1将ban2将，然后2号位选1将ban1将,4号位选1将，3号位选1将ban1将6号位选1将，5号位选1将；

nos_caocao, wanglang, prismriver, cenhun, kof_nos_guanyu, kof_nos_diaochan, caohong, zhangjunyi, wutugu, guanyinping, kof_huangzhong, quancong, sp_meili, wuyi, cuiyan,
 liubiao, nos_guanyu, zhoucang, xiahouyuan, fuwan, zhuzhi, as_fuhuanghou, heg_luxun, nos_zhangjiao, heg_caohong, kof_nos_daqiao, nazrin, guzhielai, liubei, zhangbao,
 maliang, shenzhouyu, shencaocao, nos_yuji, zangba, bf_zuoci, huangzhong, zumao, caizhaoji, mazhong, nue, yuejin, weiyan, bianhuanghou, zhangrang, st_huaxiong, nos_xuchu,
  jsp_machao, zhangliang, sunjian, st_gongsunzan, sunru, sunxiu, sunce, kof_nos_huatuo, nos_xiahoudun, yuanshu, zuoci, bug_caoren, mizhu, panfeng, nos_zhouyu, ol_zumao,
   neo_guanyu, yangxiu, yuanshao, ol_shixie, nos_caoren, sp_wenpin, masu, kof_zhenji, kof_menghuo, guojia, nos_xushu, wangyi, shixie, zhuhuan, zhugedan, nos_liru, yuanshao_po,
    kof_weiyan, ol_sunluyu, zhangyi, kof_zhurong, nos_lvbu, heqi, jiawenhe, taoqian, fuhuanghou, mayunlu, nos_zhoutai, diy_liuxie, ol_jiaxu, Rh_junko, kof_jiangwei, xushu,
    zhurong, super_yujin, ganning, nos_zhangfei, nos_luxun, jsp_sunshangxiang, ol_xuhuang, sundeng, yitianjian, xingcai, heg_sunce, pangde, lingtong, yujin, erqiao, wis_shuijing,
    st_yuanshu, xiahoushi, xiaoqiao, nos_zhaoyun, zhangchunhua, kof_nos_zhangliao, zhongshiji, sunluban, caifuren, nos_simayi, twyj_xiahouba, yanliangwenchou, ol_caozhen, sunqian,
     ol_xushu, liuqi, heg_dengai, ol_ii_caiwenji, yanbaihu, ol_zhoucang, shenguanyu, nos_ganning, gongsunzan, lingju, kanze, bgm_pangtong, zhaoxiang, kof_nos_huangyueying, jinxuandi,
      sp_guanyu, ol_quancong, heg_xusheng, nos_lvmeng, ol_machao, panglingming
]]--

--nue  "yoshika", 
local touhou = {"sakuya", "aya", "toyohime", "sanae", "seiga", "fujiwara", "daiyousei", "kosuzu", "doremi", "iku", "hina","chen","yukimai","shinmyoumaru","momiji",
                "shizuha", "rumia", "whiterock", "sp_youmu", "tokiko", "toziko", "sp_rin", "koishi", "lolice", "cirno", "kana", "kitcho", "sekibanki", "yugi", 
                "marisa", "akong", "parsee", "hatate", "tenshi", "komachi", "yamame" , "koakuma","jz_reimu","minamoto","elliy", "jacket_label", "satsuki", "seija",
                "medicine", "wriggle", "white", "ringoseiran", "hiziri", "sangetsusei", "rh_flandre", "toyosatomimi","nyasama","alice", "shikieiki", "jacket_label", --"sarielelis",
                "reimu", "remilia", "benben", "yuka", "keine", "kaguya", "suika", "akyuu", "meiling", "minoriko","keiki","reiko","prismriver","xiandina", "yukariex","nos_marisa",
                "futo", "tatara", "fujix", "mihana", "aunn", "qinxin", "mayumi", "patchouli", "sumireko", "joon","rinnosuke","sp_suwako","sagume","satori", "Yaezaki_An",
                "tewi" ,"shinki","nitori", "ichirin","rh_erin","Rh_junko","chiyuri","wakasagihime","toone", "sp_seiga","ran", "mugetsu","saki", "yumeko", "tenkai_tsurubami",  
                "clownpiece", "zhengzhen", "eternity", "reisen", "kutaka", "kanako", "mima", "sp_meili","yuyuko","yumemi","ellen", "mai_satono","syou","chimata","yorihime", "hifu",
                "renko","hecatia", "torisumi", "mike", "tsukasa", "eika", "megumu", "megumu", "sannyo", "magahara", "urumi", "taotei", "seoi", "nazrin", --"momoyo", 
				"takane", "Hypothia", "mamizou", "x_reimu", "mystia", "murasa", "momoyo" } -- 
local Lordss = { "mihana", "toyosatomimi", "sp_suwako", "remilia", "shinmyoumaru","hecatia", "yukariex", "tenkai_tsurubami",
                 "minamoto", "keiki", "kanako", "nyasama", "shinki","satori"} --,"sarielelis"

local function RemoveBanlist(acquired)
    local Banlist = {"diy_liuxie", "kof_nos_daqiao", "kof_jiangwei", "as_lingju", "sp_heg_zhouyu", "huangzhong", "nos_zhangfei", "shenzhugeliang","mazhong","sunhao","fuwan",
                     "kof_nos_zhangliao", "guanyinping", "pangde", "jiawenhe", "sp_wenpin", "ol_caocao", "heg_luxun","super_yujin", "shenzhugeliang", "shenlvbu","yanbaihu","ganning",
                     "zhangjunyi", "myun_lingju", "yujin", "nos_zhoutai", "wutugu", "ol_shixie", "ol_ii_caiwenji", "nos_zhangjiao", "zuoci", "kanze","guotufengji","jsp_zhaoyun",
                     "nos_lvmeng", "yanliangwenchou", "gongsunzan", "ol_sunluyu", "jinxuandi", "sunxiu", "xingcai", "ol_machao", "zhangchunhua", "guohuai", "shenguanyu","heqi",
                     "yuejin", "nos_yuji", "kof_nos_huatuo", "sunluban", "wangyi", "sunru", "ol_xushu", "nos_xiahoudun", "nos_ganning", "zhoucang", "nos_xuchu","ol_caozhen",
                    "wis_shuijing", "sunjian", "heg_lvbu", "xushu", "heg_sunce", "as_fuhuanghou", "sundeng", "ol_zhangbao", "zhangrang", "zhuzhi", "neo_guanyu","menghuo","simayi",
                    "sp_guanyu", "lingju", "kof_weiyan", "shenzhouyu", "wanglang", "liubei", "zhongshiji", "zumao", "cuiyan", "kof_nos_guanyu", "nos_caoren", "zangba", "kof_menghuo",
                     "sp_panfeng", "maliang", "heg_zhouyu", "nos_zhouyu", "nos_caocao", "zhaoxiang", "ol_caozhi", "yuanshao", "yitianjian", "nos_simayi","panfeng", "taoqian",
                     "kof_zhurong", "jsp_machao", "zhurong", "st_yuanshu", "sunqian", "bf_zuoci", "ol_zumao", "caocao", "bgm_pangtong", "erqiao", "kof_nos_diaochan", "mayunlu", "xiahoushi",
                      "guojia", "zhangliang", "st_gongsunzan", "shencaocao", "fuhuanghou", "liuqi", "cenhun", "ol_quancong", "kof_nos_huangyueying", "yuanshao_po", "xiahouyuan",
                     "masu", "yangxiu", "zhugedan", "caifuren", "guzhielai", "morino_hon", "jsp_sunshangxiang", "st_huaxiong", "kof_huangzhong", "ol_liubei", "kof_zhenji",
                     "liubiao", "sunce", "panglingming", "nos_lvbu", "quancong", "zhuhuan", "nos_zhaoyun", "mizhu", "heg_caohong", "twyj_xiahouba","xizhicai","lingtong","heg_sunce",
                     "yuanshu", "pr_shencaocao", "heg_xusheng", "heg_dengai", "nos_luxun", "nos_guanyu", "shixie", "caizhaoji", "zhangbao", "nos_xushu", "nos_liru", "weiyan",
                    "accirno"}
    local acquired2 = {}
    for _,general_name in pairs (acquired) do
        local ffff = false
        for _,general_name2 in pairs (Banlist) do
            if general_name2 == general_name then
                ffff = true
                break
            end
        end
        if not ffff then table.insert(acquired2, general_name) end
    end
    return acquired2
end
local function Remove(acquired, general0)
    --if general0 ~= "rrandom" then
        local acquired2 = {}
        for _,general_name in pairs (acquired) do
            if general_name ~= general0 then
                table.insert(acquired2, general_name)
            end
        end
        return acquired2
    --end
    --return acquired
end
local function adjusetHP(room, player)
    local name = player:getGeneralName()
    local x = player:getMaxHp()
    if name == "mihana" then
        room:setPlayerProperty(player, "hp", sgs.QVariant(x - 3))
    elseif name == "youmu" or name == "sp_youmu" or name == "kagerou" then
        room:setPlayerProperty(player, "hp", sgs.QVariant(x - 1))
    elseif name == "mayumi" then
        room:setPlayerProperty(player, "hp", sgs.QVariant(1))
    elseif name == "momoyo" then
        room:setPlayerProperty(player, "hp", sgs.QVariant(x - 2))
    elseif name == "keiki" or name == "keikiA" or name == "keikiB" or name == "keikiC" then
        room:setPlayerProperty(player, "maxhp", sgs.QVariant(1))
        room:setPlayerProperty(player, "hp", sgs.QVariant(1))
    end
end
local function adjusetHP2(room, player, Hp)
    local x = player:getMaxHp()
	local name = player:getGeneralName()
	room:writeToConsole("ChangeHero test243")
    if name == "mihana" then
        x = x - 3
    elseif name == "youmu" or name == "sp_youmu" or name == "kagerou" then
        x = x - 1
    elseif name == "mayumi" then
        x = 1
    elseif name == "momoyo" then
        x = x - 2
    elseif name == "keiki" or name == "keikiA" or name == "keikiB" or name == "keikiC" then
        x = x - 1
    end
	local RealHp = (x + Hp) / 2
	if (RealHp > math.floor(RealHp)) then
		player:drawCards(1)
	end 
	room:writeToConsole("ChangeHero test244")
	room:setPlayerProperty(player, "hp", sgs.QVariant(math.floor(RealHp)))
	room:writeToConsole("ChangeHero test245")
	if name == "kagerou" then 
		room:setPlayerProperty(player, "maxhp", sgs.QVariant(0))
	end 
end
local function getNSeats(room, player)
    local lord = room:getLord()
    if not player then room:writeToConsole(debug.traceback()) end
    if player:objectName() == lord:objectName() then
        return 1
    elseif player:objectName() == lord:getNextAlive():objectName() then
        return 2
    elseif player:objectName() == lord:getNextAlive():getNextAlive():objectName() then
        return 3
    elseif player:objectName() == lord:getNextAlive():getNextAlive():getNextAlive():objectName() then
        return 4
    elseif player:objectName() == lord:getNextAlive():getNextAlive():getNextAlive():getNextAlive():objectName() then
        return 5
    else
        return 6
    end
end

local function PAYchaoscheck(room, source)
		local Checks = {}
		table.insert(Checks, "jg_soul_simayi")
		local Role = source:getTag("ChaosRole"):toString()
		if Role == "rebel" then
			table.insert(Checks, "jg_soul_caozhen")
		elseif Role == "loyalist" then
			table.insert(Checks, "jg_soul_huangyueying")
		elseif Role == "renegade" then
			table.insert(Checks, "jg_soul_liubei")
		elseif Role == "civilian" then
			table.insert(Checks, "jg_soul_pangtong")
		elseif Role == "chaosX" then
			table.insert(Checks, "jg_soul_xiahouyuan")
		elseif Role == "cardinal" then
			table.insert(Checks, "jg_soul_zhanghe")
		end  
		local Hero = source:getTag("ChaosHero"):toString()
		table.insert(Checks, Hero)
		-- room:writeToConsole("chaoscheck test 1" .. Role)
		-- room:writeToConsole("chaoscheck test 2" .. Hero)
		-- room:writeToConsole("chaoscheck test X" .. #Checks)
		room:askForGeneral(source, table.concat(Checks,"+"))

end 
local function ThrowAliveName(room)
    local arc2 = touhou
    for _,p in sgs.qlist(room:getAllPlayers(true)) do
        local name = p:getGeneralName()
        for i = 1, #arc2 do
            if sgs.Sanguosha:translate(name) == sgs.Sanguosha:translate(arc2[i]) then
                arc2 = Remove(arc2, arc2[i])
                break
            end
        end

        name = p:getGeneral2Name()
        for i = 1, #arc2 do
            if sgs.Sanguosha:translate(name) == sgs.Sanguosha:translate(arc2[i]) then
                arc2 = Remove(arc2, arc2[i])
                break
            end
        end
    end
    return arc2
end
function ChangeRole(room, player, role, Clock)
    if role == player:getRole() then return end
    local abc = 0
    if player:getRole() == "lord" then
        abc = player:getMark("@clock_time")
        room:setPlayerMark(player, "@clock_time", 0)
    end
    room:setPlayerProperty(player, "role", sgs.QVariant(role))
    for _,p in sgs.qlist(room:getPlayers()) do
        room:notifyProperty(p, player, "role", player:getRole())
    end
    room:broadcastProperty(player, "role", player:getRole())
    if role == "lord" then
        room:setPlayerMark(player, "@clock_time", Clock)
        room:setPlayerMark(player, "ChooseJiangSeven", 1)
        room:setPlayerMark(player, "newtype", 1)
    end
    return abc
end
function ChangeHeroX(room, player, new_general) 
	if not new_general then room:writeToConsole(debug.traceback()) ; return end
	if new_general == "" then room:writeToConsole(debug.traceback()) ; return end
	new_general = ChangeSkin(new_general, player, room, 2)
	player:setTag("ChaosHero", sgs.QVariant(new_general))   
    if (player:getGeneral() ~= nil) then
		for _, skill in sgs.qlist(player:getSkillList()) do
			player:loseSkill(skill:objectName())
		end   
    end  
    room:setPlayerProperty(player, "general", sgs.QVariant(new_general))  
	local GeneralTure = sgs.Sanguosha:getGeneral(new_general)
    player:setGender(player:getGeneral():getGender())
    room:setPlayerProperty(player,"kingdom", sgs.QVariant(player:getGeneral():getKingdom())) 
    --for _, skill in sgs.qlist(player:getVisibleSkillList()) do
        --if (skill:isAttachedLordSkill()) then
            --player:loseAttachLordSkill(skill:objectName())
		--end
	--end 
	for _, skill in sgs.qlist(GeneralTure:getSkillList()) do 
		local shouldAdd = true
		if skill:isLordSkill() and not player:isLord() then shouldAdd = false end 
		for _, skill2 in sgs.qlist(GeneralTure:getVisibleSkillList()) do
			if skill2:objectName() == skill:objectName() and shouldAdd then 
				room:handleAcquireDetachSkills(player, skill:objectName()) 
				shouldAdd = false  
			end 
		end  
		if shouldAdd then 
			player:addSkill(skill:objectName())
		end   
	end   
	
	-- for _, skill in sgs.qlist(GeneralTure:getSkillList()) do  
		-- room:writeToConsole("ChangeHero AddSkill" .. skill:objectName())
		-- player:addSkill(skill:objectName())
		-- for _, skill2 in sgs.qlist(GeneralTure:getVisibleSkillList()) do
			-- if skill2:objectName() == skill:objectName() then room:attachSkillToPlayer(player, skill:objectName()) end 
		-- end    
	-- end   
	 
    room:filterCards(player, player:getCards("he"), true)
	 
		player:setMaxHp(player:getGeneralMaxHp()) 
 
    
    player:setHp(player:getMaxHp()) 
    room:broadcastProperty(player, "hp"); 
    room:broadcastProperty(player, "maxhp"); 
	
	for _, skill in sgs.qlist(GeneralTure:getSkillList()) do 
		local thread = room:getThread() 
		if not (skill:isLordSkill() and not player:isLord()) then 
            if (skill:inherits("TriggerSkill")) then
                local skillX = sgs.Sanguosha:getTriggerSkill(skill:objectName())
                if skillX then  
                    room:getThread():addTriggerSkill(skillX) 
                end
            end 
            if skill:getFrequency() == sgs.Skill_Limited and player:getMark("@" .. string.sub(skill:objectName(), 4)) == 0  then 
                room:setPlayerMark(player, "@" .. string.sub(skill:objectName(), 4), 1)
            end 
            thread:trigger(sgs.EventAcquireSkill, room, player, sgs.QVariant(skill:objectName()))
		end 
	end   
    room:resetAI(player)
end 
function checkCheating(room, player, general0, ori_acq) 
    local kill = true
    for i = 1, #ori_acq do
        if string.find(ori_acq[i], general0) then
            kill = false
        end
    end
    if kill and not string.find(player:screenName(), "aysage")  then room:killPlayer(player) end 
end 
function ChangeHero(room, player, general0, RandomAll, ori_acq, secend, modecode)
    local Asecend = secend
    if not Asecend then Asecend = false end
    if not ori_acq then room:writeToConsole(debug.traceback()) end
	checkCheating(room, player, general0, ori_acq) 
    local general1 = general0
    if general1 == "rrandom" then
		if #RandomAll > 0 then 
            --general1 = "satsuki"  --秘封活动已结束！  -- 冴月麟活动已开启！2024年9月28日09:26:55 --冴月麟活动已结束
			--general1 = "hifu"
			general1 = RandomAll[math.random(1,#RandomAll)]  --秘封活动搞一下
		else
			general1 = "sujiang"
		end 
    end 
	room:writeToConsole("ChangeHero test239")
    if (string.find(player:screenName(), "丽塔") or string.find(player:screenName(), "Rita"))
            and (general1 == "fujix" or general1 == "morino_hon" or general1 == "paysage")
        and room:askForSkillInvoke(player, "zhuanshu") then
        room:changeHero(player, "vollerei", true, true, Asecend, false)
    else
		--if general1 == "kagerou" then 
			--room:writeToConsole("ChangeHero test240")
			--room:changeHero(player, general1, false, true, Asecend, false)
		--else
			room:writeToConsole("ChangeHero test241" .. player:objectName() .. " " .. general1)
			if modecode and modecode == 1 then 
				room:changeHero(player, general1, true, false, Asecend, false)
			else
				room:writeToConsole("ChangeHero test241.P" .. player:objectName() .. " " .. general1)
				ChangeHeroX(player:getRoom(), player, general1) 
			end 
		--end 
    end
	room:writeToConsole("ChangeHero test2419" .. player:objectName() .. " " .. general1)
    if general1 == "alice" then
        local i = 0
        for _, cardX in sgs.list(player:getHandcards()) do
            if cardX:objectName() == "hongrai" or cardX:objectName() == "shanghai" then
                i = i + 1
            end
        end
        for _, cardX in sgs.list(player:getHandcards()) do
            if i > 1 then break end
            room:moveCardTo(cardX, player, sgs.Player_DrawPile)
            i = i + 1
            if i > 1 then break end
        end
        for j = 1,2 do
            local card_ids = room:getNCards(1)
            room:askForGuanxing(player, card_ids, sgs.Room_GuanxingDownOnly)
        end
        room:notifySkillInvoked(player, "luajunzhen")
        for _, p in sgs.qlist(room:getOtherPlayers(player)) do
            for _, Acard in sgs.list(p:getHandcards()) do
                if Acard:objectName() == "hongrai" or Acard:objectName() == "shanghai" then
                    room:obtainCard(player, Acard)
                    p:drawCards(1)
                end
            end
        end
        for _, id in sgs.qlist(room:getDrawPile()) do
            local Acard = sgs.Sanguosha:getCard(id)
            if Acard:objectName() == "hongrai" or Acard:objectName() == "shanghai" then
                local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
                dummy:addSubcard(id)
                room:obtainCard(player, dummy)
            end
        end
    end
	if general1 == "Yaezaki_An" then 
		local lord = room:getLord()
		anxbbb = {} 
		room:writeToConsole("ChangeHero test2420" .. player:objectName() .. " " .. general1)
		local ids = room:askForExchange(player, "luamuling", 99, 0, false, "mingren_put", true):getSubcards()
		local uaqiuwen = lord:getTag("luamuling"):toString() 
		uaqiuwen = uaqiuwen:split("|") 
		for _, id in ipairs(uaqiuwen) do
			table.insert(anxbbb, id)
		end 
		for _, id in sgs.qlist(ids) do
			table.insert(anxbbb, id)
		end  
		
		lord:setTag("luamuling", sgs.QVariant(table.concat(anxbbb, "|"))) 
		
		local dummy2 = sgs.Sanguosha:cloneCard("yuzhi", sgs.Card_NoSuit, 0)
		dummy2:addSubcards(ids) 
		local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_UNKNOWN, player:objectName(), name, nil)
		room:moveCardTo(dummy2, player, nil, sgs.Player_PlaceTable, reason, true)
		dummy2:deleteLater()
		
		local dummy3 = sgs.Sanguosha:cloneCard("yuzhi", sgs.Card_NoSuit, 0)
		dummy3:addSubcards(ids)
		local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_UNKNOWN, nil, name, nil)
		room:moveCardTo(dummy3, nil, nil, sgs.Player_PlaceUnknown, reason, true)
		dummy3:deleteLater()	
	end 
    if general1 == "shinmyoumaru" and player:isLord() then
        room:notifySkillInvoked(player, "luabaochui")
        for _, p in sgs.qlist(room:getOtherPlayers(player)) do
            for _, Acard in sgs.list(p:getHandcards()) do
                if Acard:isKindOf("Wanbaochui") then
                    room:obtainCard(player, Acard)
                    p:drawCards(1)
                end
            end
        end
        for _, id in sgs.qlist(room:getDrawPile()) do
            local Acard = sgs.Sanguosha:getCard(id)
            if Acard:isKindOf("Wanbaochui")  then
                local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
                dummy:addSubcard(id)
                room:obtainCard(player, dummy)
            end
        end
    end
end

ChooseJiangcard = sgs.CreateSkillCard{
    name = "ChooseJiang",
    filter = function(self, targets, to_select)
        return  to_select:objectName() == sgs.Self:objectName()
    end,
    on_use = function(self, room, source, targets)
    end
}

ChooseJiangVS = sgs.CreateViewAsSkill{
    name = "ChooseJiang",
    n = 0,
    view_filter = function()
        return false
    end,
    view_as = function()
        return ChooseJiangcard:clone()
    end,
    enabled_at_play = function()
        return false
    end,
    enabled_at_response = function(self, player, pattern)
        return pattern == "@@ChooseJiang"
    end,
}
local function deleteCard(room, player, cardName, self)
    local trickcard = sgs.IntList()
    for _, id in sgs.qlist(room:getDrawPile()) do
        local card = sgs.Sanguosha:getCard(id)
        if card:isKindOf(cardName) then
            trickcard:append(id)
        end
    end
    local dummy2 = sgs.Sanguosha:cloneCard("yuzhi", sgs.Card_NoSuit, 0)
    dummy2:addSubcards(trickcard)
    local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, player:objectName(), self:objectName(), nil)
    room:moveCardTo(dummy2, nil, nil,  sgs.Player_PlaceUnknown, reason, true)
    dummy2:deleteLater()
end 
local function deleteCard2(room, player, inta, intb)
    local trickcard = sgs.IntList()
    for _, id in sgs.qlist(room:getDrawPile()) do 
        if id >= inta and id <= intb then
            trickcard:append(id)
        end
    end
    local dummy2 = sgs.Sanguosha:cloneCard("yuzhi", sgs.Card_NoSuit, 0)
    dummy2:addSubcards(trickcard)
    local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, player:objectName(), "", nil)
    room:moveCardTo(dummy2, nil, nil,  sgs.Player_PlaceUnknown, reason, true)
    dummy2:deleteLater()
end 

ChooseJiang = sgs.CreateTriggerSkill{
    name = "ChooseJiang",
    global = true,
    priority = 99,
    view_as_skill = ChooseJiangVS,
    frequency = sgs.Skill_Compulsory,
    events = {sgs.TurnStart},
    on_trigger = function(self, event, playerQ, data)
        local room = playerQ:getRoom()
        local player = room:getLord()
        if event == sgs.TurnStart and player:getMark("hasChangedD") == 0 and playerQ:hasSkill("ChooseJiang") then

            --删除卡牌【雌雄双股剑】
            deleteCard(room, player, "DoubleSword", self)
            deleteCard(room, player, "Roukankenx", self)
            deleteCard(room, player, "Halberd", self)  
			 
            --[[
            
            for _, id in sgs.qlist(room:getDrawPile()) do
                local card = sgs.Sanguosha:getCard(id)
                if card:isKindOf("DoubleSword") then
                    local ids_A = sgs.IntList()
                    ids_A:append(id)
                    room:fillAG(ids_A)
                    room:getThread():delay()
                    room:getThread():delay()
                    room:clearAG()
                end
            end  ]]--

            local all = sgs.Sanguosha:getLimitedGeneralNames()
            
            player:drawCards(3)
            player:getNextAlive():drawCards(4)
            player:getNextAlive():getNextAlive():drawCards(4)
            player:getNextAlive():getNextAlive():getNextAlive():drawCards(4)
            player:getNextAlive():getNextAlive():getNextAlive():getNextAlive():drawCards(4)
            player:getNextAlive():getNextAlive():getNextAlive():getNextAlive():getNextAlive():drawCards(4)


            if player:getNextAlive():getRole() ~= "rebel" then
                room:setPlayerProperty(player:getNextAlive(), "role", sgs.QVariant("rebel"))
            end
            if player:getNextAlive():getNextAlive():getRole() ~= "loyalist" then
                room:setPlayerProperty(player:getNextAlive():getNextAlive(), "role", sgs.QVariant("loyalist"))
            end
            if player:getNextAlive():getNextAlive():getNextAlive():getRole() ~= "rebel" then
                room:setPlayerProperty(player:getNextAlive():getNextAlive():getNextAlive(), "role", sgs.QVariant("rebel"))
            end
            if player:getNextAlive():getNextAlive():getNextAlive():getNextAlive():getRole() ~= "rebel" then
                room:setPlayerProperty(player:getNextAlive():getNextAlive():getNextAlive():getNextAlive(), "role", sgs.QVariant("rebel"))
            end
            if player:getNextAlive():getNextAlive():getNextAlive():getNextAlive():getNextAlive():getRole() ~= "loyalist" then
                room:setPlayerProperty(player:getNextAlive():getNextAlive():getNextAlive():getNextAlive():getNextAlive(), "role", sgs.QVariant("loyalist"))
            end

            n = math.min(17, #all)
            all = RemoveBanlist(all)
            local undo = false
            for _,p in sgs.qlist(room:getAlivePlayers()) do
                if string.find(p:screenName(), "aysage") then
                    all = touhou
                    undo = true
                end
            end
            if not undo then
                for _,p in sgs.qlist(room:getAlivePlayers()) do
                    if p:getState() ~= "robot" then
                        local choice = room:askForChoice(p, "ChooseJiang", "touhou+quankuo")
                        if choice == "touhou" then
                            all = touhou
                        end
                        break
                    end
                end
            end
            local knowBan = {}
            local acquired = {}
            local ori_acq = {}
			local yorihime = {}
            local RandomAll = all
            repeat
                local rand = math.random(1,#all)
                room:writeToConsole(#all .. " xxxx " ..  all[#all] .. " ceshi  " .. rand)
                if not table.contains(acquired,all[rand]) then
                    table.insert(acquired,(all[rand]))
                    RandomAll = Remove(RandomAll, (all[rand]))
                end
            until #acquired == n
            table.insert(acquired, "rrandom") --秘封活动搞一下
            ori_acq = acquired
            player = player:getNextAlive():getNextAlive():getNextAlive():getNextAlive()
            room:askForUseCard(player, "@@ChooseJiang", "@ChooseJiang")
            local general_1
            room:setPlayerFlag(player, "luaFive")
            room:writeToConsole(" Seat" .. getNSeats(room, player) .. " start to ban general" .. getNSeats(room, player))
            if player:getState() ~= "robot" then
                general_1 = room:askForGeneral(player, table.concat(acquired,"+"))
                acquired = Remove(acquired, general_1)
            else
                general_1 = room:askForChoice(player, "ChooseJiang", table.concat(acquired,"+"))
                acquired = Remove(acquired, general_1)
            end
            room:setPlayerFlag(player, "-luaFive")

            player = room:getLord()
            local general0
            local general1
            local general2
            local general_2
            room:writeToConsole(" Seat" .. getNSeats(room, player) .. " start to choose general" .. getNSeats(room, player))
           -- room:getThread():delay(2000)
            room:askForUseCard(player, "@@ChooseJiang", "@ChooseJiang2")
            if player:getState() ~= "robot" then
                general0 = room:askForGeneral(player, table.concat(acquired,"+"))
                acquired = Remove(acquired, general0)
            else
                general0 = room:askForChoice(player, "ChooseJiang", table.concat(acquired,"+"))
                acquired = Remove(acquired, general0)
            end
            ChangeHero(room, player, general0, RandomAll, ori_acq, false, 1)
            adjusetHP(room, player)
            room:askForUseCard(player, "@@ChooseJiang", "@ChooseJiang")  -- "请在接下来展示的角色列表中选择一名角色禁用。",
            if player:getState() ~= "robot" then
                general1 = room:askForGeneral(player, table.concat(acquired,"+"))
                acquired = Remove(acquired, general1)
                general2 = room:askForGeneral(player, table.concat(acquired,"+"))
                acquired = Remove(acquired, general2)
                general_2 = room:askForGeneral(player, table.concat(acquired,"+"))
                acquired = Remove(acquired, general_2)
            else
                general1 = room:askForChoice(player, "ChooseJiang", table.concat(acquired,"+"))
                acquired = Remove(acquired, general1)
                general2 = room:askForChoice(player, "ChooseJiang", table.concat(acquired,"+"))
                acquired = Remove(acquired, general2)
                general_2 = room:askForChoice(player, "ChooseJiang", table.concat(acquired,"+"))
                acquired = Remove(acquired, general_2)
            end

            local general3
            local general4
            local general5
            local kplayer = player:getNextAlive()
            local kplayerF = kplayer:getNextAlive():getNextAlive()
            room:writeToConsole(" Seat" .. getNSeats(room, kplayer) .. " and "  .. getNSeats(room, kplayerF)  .. " start to choose general")
            room:getThread():delay(1000)
            if kplayer:getState() ~= "robot" then
                room:askForUseCard(kplayer, "@@ChooseJiang", "@ChooseJiang2")
                general3 = room:askForGeneral(kplayer, table.concat(acquired,"+"))
                acquired = Remove(acquired, general3)
            else
                general3 = room:askForChoice(kplayer, "ChooseJiang", table.concat(acquired,"+"))
                acquired = Remove(acquired, general3)
            end
            ChangeHero(room, kplayer, general3, RandomAll, ori_acq, false, 1)
            adjusetHP(room, kplayer)
            room:getThread():delay(1000)
            if kplayer:getState() ~= "robot" then
                room:askForUseCard(kplayer, "@@ChooseJiang", "@ChooseJiang")
                general5 = room:askForGeneral(kplayer, table.concat(acquired,"+"))
                acquired = Remove(acquired, general5)
            else
                general5 = room:askForChoice(kplayer, "ChooseJiang", table.concat(acquired,"+"))
                acquired = Remove(acquired, general5)
            end
            if kplayerF:getState() ~= "robot" then
                room:askForUseCard(kplayerF, "@@ChooseJiang", "@ChooseJiang2")
                general4 = room:askForGeneral(kplayerF, table.concat(acquired,"+"))
                acquired = Remove(acquired, general4)
            else
                general4 = room:askForChoice(kplayerF, "ChooseJiang", table.concat(acquired,"+"))
                acquired = Remove(acquired, general4)
            end
            ChangeHero(room, kplayerF, general4, RandomAll, ori_acq, false, 1)
            adjusetHP(room, kplayerF)

            local general6
            local general7
            local general8
            kplayer = kplayer:getNextAlive()
            kplayerF = kplayer:getNextAlive():getNextAlive():getNextAlive()
            room:writeToConsole(" Seat" .. getNSeats(room, kplayer) .. " and "  .. getNSeats(room, kplayerF)  .. " start to choose general")
            room:getThread():delay(1000)
            if kplayer:getState() ~= "robot" then
                room:askForUseCard(kplayer, "@@ChooseJiang", "@ChooseJiang2")
                general6 = room:askForGeneral(kplayer, table.concat(acquired,"+"))
                acquired = Remove(acquired, general6)
            else
                general6 = room:askForChoice(kplayer, "ChooseJiang", table.concat(acquired,"+"))
                acquired = Remove(acquired, general6)
            end
            ChangeHero(room, kplayer, general6, RandomAll, ori_acq, false, 1)
            adjusetHP(room, kplayer)
            room:getThread():delay(1000)
            if kplayer:getState() ~= "robot" then
                room:askForUseCard(kplayer, "@@ChooseJiang", "@ChooseJiang")
                general8 = room:askForGeneral(kplayer, table.concat(acquired,"+"))
                acquired = Remove(acquired, general8)
            else
                general8 = room:askForChoice(kplayer, "ChooseJiang", table.concat(acquired,"+"))
                acquired = Remove(acquired, general8)
            end

            if kplayerF:getState() ~= "robot" then
                room:askForUseCard(kplayerF, "@@ChooseJiang", "@ChooseJiang2")
                general7 = room:askForGeneral(kplayerF, table.concat(acquired,"+"))
                acquired = Remove(acquired, general7)
            else
                general7 = room:askForChoice(kplayerF, "ChooseJiang", table.concat(acquired,"+"))
                acquired = Remove(acquired, general7)
            end
            ChangeHero(room, kplayerF, general7, RandomAll, ori_acq, false, 1)
            adjusetHP(room, kplayerF)
			
            kplayer = kplayer:getNextAlive()

            local general9
            kplayer = kplayer:getNextAlive()
            room:writeToConsole(" Seat" .. getNSeats(room, kplayer) .. " start to choose general")
            room:getThread():delay(1000)
            if kplayer:getState() ~= "robot" then
                room:askForUseCard(kplayer, "@@ChooseJiang", "@ChooseJiang2")
                general9 = room:askForGeneral(kplayer, table.concat(acquired,"+"))
            else
                general9 = room:askForChoice(kplayer, "ChooseJiang", table.concat(acquired,"+"))
            end
            ChangeHero(room, kplayer, general9, RandomAll, ori_acq, false, 1)
            adjusetHP(room, kplayer)
            for _, p in sgs.qlist(room:getAlivePlayers()) do
                room:setPlayerMark(p, "hasChangedD", 1)
            end
            --room:setPlayerMark(room:getLord(), "@clock_time", room:getTag("TurnLengthCount"):toInt() + 1)
            table.insert(knowBan, general_1)
            table.insert(knowBan, general1)
            table.insert(knowBan, general2)
            table.insert(knowBan, general_2)
            table.insert(knowBan, general5)
            table.insert(knowBan, general8)
			local strBan = table.concat(knowBan,"+")
			player:getRoom():setTag("yorihime", sgs.QVariant(strBan))
            for _,p in sgs.qlist(room:getAlivePlayers()) do
                if string.find(p:screenName(), "aysage") then
                    room:askForGeneral(p, table.concat(knowBan,"+"))
                end
            end
			
        end
    end
}

DeleteCard = sgs.CreateTriggerSkill{
	name = "#DeleteCard", 
	global = true,
	priority = 100,
	events = {sgs.TurnStart},
	on_trigger = function(self, event, player, data, room) 
		for _, kp in sgs.qlist(room:getAlivePlayers()) do
			if kp:getMark("DeleteCard") == 0 then 
				deleteCard2(room, kp, 310, 351) 
			end
			room:setPlayerMark(kp, "DeleteCard", 1)
		end 
		return false
	end
}
HappySkillAnjiang:addSkill(ChooseJiang)
HappySkillAnjiang:addSkill(DeleteCard)

--[[
规则：游戏开始选将
内容：1、采用12选4的设定，座位按 1（主） 2（反） 3（反） 4（忠） 设置
2、1号位先选1将ban2将，然后2号位选1将ban1将,4号位选1将，3号位选1将ban1将6号位选1将，5号位选1将；
3、主公阵亡之后，由那个忠臣当主公
--]]
ChooseJiang22 = sgs.CreateTriggerSkill{
    name = "ChooseJiang22",
    global = true,
    priority = 99,
    frequency = sgs.Skill_Compulsory,
    events = {sgs.TurnStart, sgs.AskForPeaches},
    on_trigger = function(self, event, playerQ, data)
        local room = playerQ:getRoom()
        if event == sgs.TurnStart then

            local player = room:getLord()
            if event == sgs.TurnStart and player:getMark("hasChangedD") == 0 and playerQ:hasSkill("ChooseJiang22") then
                local all = sgs.Sanguosha:getLimitedGeneralNames()
                --删除卡牌【雌雄双股剑】
                deleteCard(room, player, "DoubleSword", self)
                deleteCard(room, player, "Roukankenx", self)
                deleteCard(room, player, "Halberd", self) 
                if player:getNextAlive():getRole() ~= "rebel" then
                    room:setPlayerProperty(player:getNextAlive(), "role", sgs.QVariant("rebel"))
                end
                if player:getNextAlive():getNextAlive():getRole() ~= "rebel" then
                    room:setPlayerProperty(player:getNextAlive():getNextAlive(), "role", sgs.QVariant("rebel"))
                end
                if player:getNextAlive():getNextAlive():getNextAlive():getRole() ~= "loyalist" then
                    room:setPlayerProperty(player:getNextAlive():getNextAlive():getNextAlive(), "role", sgs.QVariant("loyalist"))
                end

                player:drawCards(3)
                player:getNextAlive():drawCards(4)
                player:getNextAlive():getNextAlive():drawCards(4)
                player:getNextAlive():getNextAlive():getNextAlive():drawCards(5)

                n = math.min(12, #all)
                all = RemoveBanlist(all)
                for _,p in sgs.qlist(room:getAlivePlayers()) do
                    if p:getState() ~= "robot" then
                        --touhou
                        local choice = room:askForChoice(p, "ChooseJiang", "touhou+quankuo")
                        if choice == "touhou" then
                            all = touhou
                        end
                        break
                    end
                end

                local acquired = {}
                local ori_acq = {}
                local yorihime = {}
                local RandomAll = all
                repeat
                    local rand = math.random(1,#all)
                    room:writeToConsole(#all .. " xxxx " ..  all[#all] .. " ceshi  " .. rand)
                    if not table.contains(acquired,all[rand]) then
                        table.insert(acquired,(all[rand]))
                        RandomAll = Remove(RandomAll, (all[rand]))
                    end
                until #acquired == n
                table.insert(acquired, "rrandom") --秘封活动搞一下
                ori_acq = acquired

                player = room:getLord():getNextAlive():getNextAlive():getNextAlive()
                local general0
                local general1
                local general2
                room:writeToConsole(" Seat" .. getNSeats(room, player) .. " start to choose general" .. getNSeats(room, player))
                -- room:getThread():delay(2000)
                if player:getState() ~= "robot" then
                    general0 = room:askForGeneral(player, table.concat(acquired,"+"))
                    acquired = Remove(acquired, general0)
                else
                    general0 = room:askForChoice(player, "ChooseJiang", table.concat(acquired,"+"))
                    acquired = Remove(acquired, general0)
                end
                ChangeHero(room, player, general0, RandomAll, ori_acq, false, 1)
                adjusetHP(room, player)
                if player:getState() ~= "robot" then
                    general1 = room:askForGeneral(player, table.concat(acquired,"+"))
                    acquired = Remove(acquired, general1)
                    general2 = room:askForGeneral(player, table.concat(acquired,"+"))
                    acquired = Remove(acquired, general2)
                else
                    general1 = room:askForChoice(player, "ChooseJiang", table.concat(acquired,"+"))
                    acquired = Remove(acquired, general1)
                    general2 = room:askForChoice(player, "ChooseJiang", table.concat(acquired,"+"))
                    acquired = Remove(acquired, general2)
                end


                player = room:getLord():getNextAlive():getNextAlive()
                room:writeToConsole(" Seat" .. getNSeats(room, player) .. " start to choose general" .. getNSeats(room, player))
                -- room:getThread():delay(2000)
                if player:getState() ~= "robot" then
                    general0 = room:askForGeneral(player, table.concat(acquired,"+"))
                    acquired = Remove(acquired, general0)
                else
                    general0 = room:askForChoice(player, "ChooseJiang", table.concat(acquired,"+"))
                    acquired = Remove(acquired, general0)
                end
                if player:getState() ~= "robot" then
                    general1 = room:askForGeneral(player, table.concat(acquired,"+"))
                    acquired = Remove(acquired, general1)
                else
                    general1 = room:askForChoice(player, "ChooseJiang", table.concat(acquired,"+"))
                    acquired = Remove(acquired, general1)
                end
                ChangeHero(room, player, general0, RandomAll, ori_acq, false, 1)
                adjusetHP(room, player)

                player = room:getLord():getNextAlive()
                room:writeToConsole(" Seat" .. getNSeats(room, player) .. " start to choose general" .. getNSeats(room, player))
                -- room:getThread():delay(2000)
                if player:getState() ~= "robot" then
                    general0 = room:askForGeneral(player, table.concat(acquired,"+"))
                    acquired = Remove(acquired, general0)
                else
                    general0 = room:askForChoice(player, "ChooseJiang", table.concat(acquired,"+"))
                    acquired = Remove(acquired, general0)
                end
                ChangeHero(room, player, general0, RandomAll, ori_acq, false, 1)
                adjusetHP(room, player)

                player = room:getLord()
                room:writeToConsole(" Seat" .. getNSeats(room, player) .. " start to choose general" .. getNSeats(room, player))
                -- room:getThread():delay(2000)
                if player:getState() ~= "robot" then
                    general0 = room:askForGeneral(player, table.concat(acquired,"+"))
                    acquired = Remove(acquired, general0)
                else
                    general0 = room:askForChoice(player, "ChooseJiang", table.concat(acquired,"+"))
                    acquired = Remove(acquired, general0)
                end
                ChangeHero(room, player, general0, RandomAll, ori_acq, false, 1)
                adjusetHP(room, player)

                for _, p in sgs.qlist(room:getAlivePlayers()) do
                    room:setPlayerMark(p, "hasChangedD", 1)
                end
            end
        else
            local dying = data:toDying()
            if playerQ:hasSkill(self:objectName()) and dying.who:objectName() == playerQ:objectName() 
				and dying.who:getHp() <= 0 then
                local loyalist
                for _, p in sgs.qlist(room:getAllPlayers()) do
                    if p:getRole() == "loyalist" then
                        loyalist = p
                    end 
                end 
                if loyalist and loyalist:isAlive() and room:askForSkillInvoke(dying.who, "ChangeLord") then 
                    local xo = room:getTag("TurnLengthCount"):toInt() + 1
                    room:setPlayerProperty(playerQ, "role", sgs.QVariant("loyalist"))
                    room:setPlayerMark(playerQ, "@clock_time", 0)
                    room:setPlayerProperty(loyalist, "role", sgs.QVariant("lord"))
                    room:setPlayerMark(loyalist, "@clock_time", xo)
                end 
            end
            return false
        end
    end
}
HappySkillAnjiang:addSkill(ChooseJiang22)
--[[
规则：击杀奖惩
内容：1、击杀队友不会受到惩罚；
2、击杀队友、敌人不会得到奖励；
3、阵亡一方存活队友摸一张牌。
]]--
RewardAndPunish3 = sgs.CreateTriggerSkill{
    name = "#RewardAndPunish3",
    frequency = sgs.Skill_Compulsory,
    events = {sgs.BuryVictim},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        room:setTag("SkipNormalDeathProcess", sgs.QVariant(true))
        local friends = {}
        local alives = room:getAlivePlayers()
        local role = player:getRole()
        local death = data:toDeath()
        if death.who and death.who:getGeneralName() == "Yaezaki_An" then
            return false 
        end 
        --主公阵亡找忠臣
        if role == "lord" then
            for _,p in sgs.qlist(alives) do
                if p:getRole() == "loyalist" then
                    table.insert(friends, p)
                end
            end
            --忠臣阵亡找主公/忠臣
        elseif role == "loyalist" then
            for _,p in sgs.qlist(alives) do
                if p:getRole() == "lord" then
                    table.insert(friends, p)
                elseif p:getRole() == "loyalist" then --这里考虑了“焚心”等改变身份的技能的影响
                    table.insert(friends, p)
                end
            end
            --反贼阵亡找反贼
        elseif role == "rebel" then
            for _,p in sgs.qlist(alives) do
                if p:getRole() == "rebel" then
                    table.insert(friends, p)
                end
            end
        end
        --存活的队友摸一张牌
        if #friends > 0 then
            if room:getAllPlayers(true) == 4 then
                for _,friend in ipairs(friends) do
                    room:drawCards(friend, 2, "HappyRewardAndPunish3")
                end
            else
                for _,friend in ipairs(friends) do
                    room:drawCards(friend, 1, "HappyRewardAndPunish3")
                end
            end
        end
    end,
    can_trigger = function(self, target)
        return true
    end,
    priority = 2,
}
HappySkillAnjiang:addSkill(RewardAndPunish3)

--[[
规则：击杀奖惩2
内容：1、击杀队友不会受到惩罚；
2、击杀队友、敌人不会得到奖励；
]]--
RewardAndPunish6 = sgs.CreateTriggerSkill{
    name = "#RewardAndPunish6",
    frequency = sgs.Skill_Compulsory,
    events = {sgs.BuryVictim},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        room:setTag("SkipNormalDeathProcess", sgs.QVariant(true))
    end,
    can_trigger = function(self, target)
        return true
    end,
    priority = 2,
} 
HappySkillAnjiang:addSkill(RewardAndPunish6)
--====================Pay式军八====================--

ChooseJiangEight = sgs.CreateTriggerSkill{
    name = "ChooseJiangEight",
    global = true,
    priority = 99,
    view_as_skill = ChooseJiangVS,
    frequency = sgs.Skill_Compulsory,
    events = {sgs.TurnStart},
    on_trigger = function(self, event, playerQ, data)
        local room = playerQ:getRoom()
        local player = room:getLord()
        if event == sgs.TurnStart and player:getMark("hasChangedD") == 0 and playerQ:hasSkill("ChooseJiangEight") then
            local all = sgs.Sanguosha:getLimitedGeneralNames()
            --删除卡牌【雌雄双股剑】
            deleteCard(room, player, "DoubleSword", self)
            deleteCard(room, player, "Roukankenx", self)
            deleteCard(room, player, "Halberd", self) 

			room:getThread():delay(3500)
			for _, ppp in sgs.qlist(room:getAlivePlayers()) do
				ppp:drawCards(4) 
			end 

			--确认将池
            n = math.min(17, #all)
            all = RemoveBanlist(all)
			
            local undo = false
            for _,p in sgs.qlist(room:getAlivePlayers()) do
                if string.find(p:screenName(), "aysage") then
                    all = touhou
                    undo = true
                end
            end
            if not undo then
                for _,p in sgs.qlist(room:getAlivePlayers()) do
                    if p:getState() ~= "robot" then
                        local choice = room:askForChoice(p, "ChooseJiang", "touhou+quankuo")
                        if choice == "touhou" then
                            all = touhou
                        end
                        break
                    end
                end
            end

            local RandomAll = all
            local acquired = {}
            all = Remove(all, "wriggle")
            all = Remove(all, "sagume")
            all = Remove(all, "sp_seiga")
            all = Remove(all, "hecatia")
			all = Remove(all, "torisumi")	
			all = Remove(all, "eika")		
			all = Remove(all, "yorihime")	
			all = Remove(all, "Yaezaki_An")	

			local k = 3
            repeat
                local rand = math.random(1,#all)
                if not table.contains(acquired,all[rand]) then
                    table.insert(acquired,(all[rand]))
                    RandomAll = Remove(RandomAll, (all[rand]))
					if table.contains(Lordss,all[rand]) and k > 0 then
						k = k - 1
					end 
                end
            until #acquired == n - k
            room:writeToConsole("juuuun ba ceshiA " .. k)
			--确保至少有三主公
			if k > 0 then 
				repeat
					local rand = math.random(1,#Lordss)
					if not table.contains(acquired,Lordss[rand]) then
						table.insert(acquired,(Lordss[rand]))
						RandomAll = Remove(RandomAll, (Lordss[rand]))
					end
				until #acquired == n
			end

            room:writeToConsole("juuuun ba ceshi" .. #acquired)
            --[[
            for _,p in sgs.qlist(room:getAlivePlayers()) do
                generalx = room:askForGeneral(p, table.concat(acquired,"+"))
			end
]]--
            room:writeToConsole("juuuun ba ceshi2" .. #acquired)


            local lord = room:getLord()
            local nextP = lord
			--先发身份
            if room:getAlivePlayers():length() == 8 then
                local roles = {"rebel" , "rebel" , "rebel" , "rebel", "loyalist", "loyalist", "renegade"}
                for i = 1, 7 do
                    nextP = nextP:getNextAlive()
                    local rand = math.random(1, #roles)
                    local nextRole = roles[rand]
                    if nextP:getRole() ~= nextRole then
                        nextP:setRole(nextRole)
                        --room:setPlayerProperty(nextP, "role", sgs.QVariant(nextRole))
                        nextP:setShownRole(false)
                        room:notifyProperty(nextP, nextP, "role")
                    end
                    table.removeOne(roles, nextRole)
                end
            end

            if room:getAlivePlayers():length() == 6 then
                local roles2 = {"rebel" , "rebel" , "rebel" , "loyalist", "renegade"}
                for i = 1, 5 do
                    nextP = nextP:getNextAlive()
                    local rand = math.random(1, #roles2)
                    local nextRole = roles2[rand]
                    if nextP:getRole() ~= nextRole then
                        nextP:setRole(nextRole)
                        --room:setPlayerProperty(nextP, "role", sgs.QVariant(nextRole))
                        nextP:setShownRole(false)
                        room:notifyProperty(nextP, nextP, "role")
                    end
                    table.removeOne(roles2, nextRole)
                end
            end

			--选角色
			--一号位开始选
			local general10
            room:writeToConsole(" Seat" .. getNSeats(room, lord) .. " start to choose general" .. getNSeats(room, lord))
           -- room:getThread():delay(2000)
            room:askForUseCard(lord, "@@ChooseJiang", "@ChooseJiang2")
            if lord:getState() ~= "robot" then
                general10 = room:askForGeneral(lord, table.concat(acquired,"+"))
            else
                general10 = room:askForChoice(lord, "ChooseJiang", table.concat(acquired,"+"))
            end
			local acquiredX = acquired
            acquired = Remove(acquired, general10)
            room:writeToConsole(" Seat" .. getNSeats(room, lord) .. " results " .. general10)

			--八号位开始选
			local general0
            local general1
            local general2 
            room:writeToConsole(" Seat" .. getNSeats(room, nextP) .. " start to choose general" .. getNSeats(room, nextP))
           -- room:getThread():delay(2000)
            room:askForUseCard(nextP, "@@ChooseJiang", "@ChooseJiang2")
            if nextP:getState() ~= "robot" then
                general0 = room:askForGeneral(nextP, table.concat(acquired,"+"))
            else
                general0 = room:askForChoice(nextP, "ChooseJiang", table.concat(acquired,"+"))
            end
            ChangeHero(room, nextP, general0, RandomAll, acquired, false, 1)
            acquired = Remove(acquired, general0)
            adjusetHP(room, nextP)
			--八号位开始禁用
            room:askForUseCard(nextP, "@@ChooseJiang", "@ChooseJiang")  -- "请在接下来展示的角色列表中选择一名角色禁用。",
            if nextP:getState() ~= "robot" then
                general1 = room:askForGeneral(nextP, table.concat(acquired,"+"))
                acquired = Remove(acquired, general1)
                general2 = room:askForGeneral(nextP, table.concat(acquired,"+"))
                acquired = Remove(acquired, general2) 
            else
                general1 = room:askForChoice(nextP, "ChooseJiang", table.concat(acquired,"+"))
                acquired = Remove(acquired, general1)
                general2 = room:askForChoice(nextP, "ChooseJiang", table.concat(acquired,"+"))
                acquired = Remove(acquired, general2) 
            end
			
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:getNextAlive():objectName() == nextP:objectName() then 
					nextP = p
					break 
				end 
			end 
			
			--七号位开始选
			local general3
            local general4
            room:writeToConsole(" Seat" .. getNSeats(room, nextP) .. " start to choose general" .. getNSeats(room, nextP))
           -- room:getThread():delay(2000)
            room:askForUseCard(nextP, "@@ChooseJiang", "@ChooseJiang2")
            if nextP:getState() ~= "robot" then
                general3 = room:askForGeneral(nextP, table.concat(acquired,"+"))
            else
                general3 = room:askForChoice(nextP, "ChooseJiang", table.concat(acquired,"+"))
            end
            ChangeHero(room, nextP, general3, RandomAll, acquired, false, 1)
            acquired = Remove(acquired, general3)
            adjusetHP(room, nextP)
			
			--七号位开始禁用
            room:askForUseCard(nextP, "@@ChooseJiang", "@ChooseJiang")  -- "请在接下来展示的角色列表中选择一名角色禁用。",
            if nextP:getState() ~= "robot" then
                general4 = room:askForGeneral(nextP, table.concat(acquired,"+"))
                acquired = Remove(acquired, general4) 
            else
                general4 = room:askForChoice(nextP, "ChooseJiang", table.concat(acquired,"+"))
                acquired = Remove(acquired, general4) 
            end
			
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:getNextAlive():objectName() == nextP:objectName() then 
					nextP = p
					break 
				end 
			end 
			
			--六号位开始选
			local general5 
            room:writeToConsole(" Seat" .. getNSeats(room, nextP) .. " start to choose general" .. getNSeats(room, nextP))
           -- room:getThread():delay(2000)
            room:askForUseCard(nextP, "@@ChooseJiang", "@ChooseJiang2")
            if nextP:getState() ~= "robot" then
                general5 = room:askForGeneral(nextP, table.concat(acquired,"+"))
            else
                general5 = room:askForChoice(nextP, "ChooseJiang", table.concat(acquired,"+"))
            end
            ChangeHero(room, nextP, general5, RandomAll, acquired, false, 1)
            acquired = Remove(acquired, general5)
            adjusetHP(room, nextP)
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:getNextAlive():objectName() == nextP:objectName() then 
					nextP = p
					break 
				end 
			end 
			
			--五号位开始选
			local general6
            room:writeToConsole(" Seat" .. getNSeats(room, nextP) .. " start to choose general" .. getNSeats(room, nextP))
           -- room:getThread():delay(2000)
            room:askForUseCard(nextP, "@@ChooseJiang", "@ChooseJiang2")
            if nextP:getState() ~= "robot" then
                general6 = room:askForGeneral(nextP, table.concat(acquired,"+"))
            else
                general6 = room:askForChoice(nextP, "ChooseJiang", table.concat(acquired,"+"))
            end
            ChangeHero(room, nextP, general6, RandomAll, acquired, false, 1)
            acquired = Remove(acquired, general6)
            adjusetHP(room, nextP)
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:getNextAlive():objectName() == nextP:objectName() then 
					nextP = p
					break 
				end 
			end 
			
			--四号位开始选
			local general7
            room:writeToConsole(" Seat" .. getNSeats(room, nextP) .. " start to choose general" .. getNSeats(room, nextP))
           -- room:getThread():delay(2000)
            room:askForUseCard(nextP, "@@ChooseJiang", "@ChooseJiang2")
            if nextP:getState() ~= "robot" then
                general7 = room:askForGeneral(nextP, table.concat(acquired,"+"))
            else
                general7 = room:askForChoice(nextP, "ChooseJiang", table.concat(acquired,"+"))
            end
            ChangeHero(room, nextP, general7, RandomAll, acquired, false, 1)
            acquired = Remove(acquired, general7)
            adjusetHP(room, nextP)
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:getNextAlive():objectName() == nextP:objectName() then 
					nextP = p
					break 
				end 
			end 
			
			--三号位开始选
			local general8
            room:writeToConsole(" Seat" .. getNSeats(room, nextP) .. " start to choose general" .. getNSeats(room, nextP))
           -- room:getThread():delay(2000)
            room:askForUseCard(nextP, "@@ChooseJiang", "@ChooseJiang2")
            if nextP:getState() ~= "robot" then
                general8 = room:askForGeneral(nextP, table.concat(acquired,"+"))
            else
                general8 = room:askForChoice(nextP, "ChooseJiang", table.concat(acquired,"+"))
            end
            ChangeHero(room, nextP, general8, RandomAll, acquired, false, 1)
            acquired = Remove(acquired, general8)
            adjusetHP(room, nextP)
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:getNextAlive():objectName() == nextP:objectName() then 
					nextP = p
					break 
				end 
			end
            if room:getAlivePlayers():length() == 6 then
                for _, p in sgs.qlist(room:getAlivePlayers()) do
                    room:setPlayerMark(p, "hasChangedD", 1)
                end
                room:setPlayerMark(room:getLord(), "@clock_time", room:getTag("TurnLengthCount"):toInt() + 1)
                return false
            end
			--二号位开始选
			local general9
            room:writeToConsole(" Seat" .. getNSeats(room, nextP) .. " start to choose general" .. getNSeats(room, nextP))
           -- room:getThread():delay(2000)
            room:askForUseCard(nextP, "@@ChooseJiang", "@ChooseJiang2")
            if nextP:getState() ~= "robot" then
                general9 = room:askForGeneral(nextP, table.concat(acquired,"+"))
            else
                general9 = room:askForChoice(nextP, "ChooseJiang", table.concat(acquired,"+"))
            end
            ChangeHero(room, nextP, general9, RandomAll, acquired, false, 1)
            acquired = Remove(acquired, general9)
            adjusetHP(room, nextP)
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:getNextAlive():objectName() == nextP:objectName() then 
					nextP = p
					break 
				end 
			end 
			
            ChangeHero(room, lord, general10, RandomAll, acquiredX, false, 1)
            adjusetHP(room, lord)
            for _, p in sgs.qlist(room:getAlivePlayers()) do
                room:setPlayerMark(p, "hasChangedD", 1)
            end

            --room:setPlayerMark(room:getLord(), "@clock_time", room:getTag("TurnLengthCount"):toInt() + 1)
		end 
	end 
}

HappySkillAnjiang:addSkill(ChooseJiangEight)

--====================Pay式军八====================--
--[[
	1.只剩忠内主时，内杀死主直接胜利。
	2.只剩反内主时，内可以变身份为忠。
]]--
ChooseJiangEight2 = sgs.CreateTriggerSkill{
    name = "#ChooseJiangEight",
    global = true,
    priority = 99,
    frequency = sgs.Skill_Compulsory,
    events = {sgs.AskForPeachesDone},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
		for _, p in sgs.qlist(room:getAllPlayers(true)) do
			if p:hasSkill("ChooseJiangEight") then
                room:writeToConsole("gameover test 1")
				local lordCount = 0
				local loyalistCount = 0
				local renegadeCount = 0
				local rebelCount = 0
                local renegade
				for _, p2 in sgs.qlist(room:getAlivePlayers()) do
                    if p2:getHp() > 0 then
                        if p2:getRole() == "renegade" then
                            renegadeCount = renegadeCount + 1
                            renegade = p2
                        elseif p2:getRole() == "lord" then
                            lordCount = lordCount + 1
                        elseif p2:getRole() == "loyalist" then
                            loyalistCount = loyalistCount + 1
                        elseif p2:getRole() == "rebel" then
                            rebelCount = rebelCount + 1
                        end
                    end
				end
				if renegadeCount == 1 and lordCount == 0 and rebelCount == 0 then 
				    room:gameOver(renegade:objectName())
				elseif renegadeCount == 1 and lordCount == 1 and loyalistCount == 0 then  
					for _, p2 in sgs.qlist(room:getAlivePlayers()) do
						if p2:getHp() > 0 and p2:getRole() == "renegade" and room:askForSkillInvoke(p2, "change2loyalist", data) then
							room:setPlayerProperty(p2, "role", sgs.QVariant("loyalist"))
						end 
					end 
				end 
			end 
		end 
	end
}
HappySkillAnjiang:addSkill(ChooseJiangEight2)

--====================Pay式六人同将====================--
--规则如下
--说的道理
--====================Pay式六人同将====================--
ChooseJiangSame = sgs.CreateTriggerSkill{
    name = "ChooseJiangSame",
    global = true,
    priority = 99,
    frequency = sgs.Skill_Compulsory,
    events = {sgs.TurnStart, sgs.AskForPeachesDone},
    on_trigger = function(self, event, playerQ, data)
		if event == sgs.TurnStart then 
			local room = playerQ:getRoom()
			if playerQ:hasSkill(self:objectName()) and playerQ:getMark("hasChangedD") == 0 then
				local lord = room:getLord()
				local nextP = lord
				local xp = room:getAlivePlayers():length()
				--删除卡牌【雌雄双股剑】
				deleteCard(room, playerQ, "DoubleSword", self)
				deleteCard(room, playerQ, "Roukankenx", self)
				deleteCard(room, playerQ, "Halberd", self) 
				
				if room:getAlivePlayers():length() == xp then 
					room:setPlayerMark(lord, "@rebel", 3)
					room:setPlayerMark(lord, "@loyalist", 3)
					--发暗将   
					local all = sgs.Sanguosha:getLimitedGeneralNames() --torisumi  minamoto
					all = RemoveBanlist(all)  
					all = Remove(all, "sagume")
					all = Remove(all, "akyuu")
					all = Remove(all, "white")
					all = Remove(all, "aunn")
					all = Remove(all, "sp_seiga")
					all = Remove(all, "hecatia")	
					all = Remove(all, "yuyuko")		 	
					all = Remove(all, "yorihime")		 
					all = Remove(all, "magahara")		 
					all = Remove(all, "torisumi")	
					all = Remove(all, "mugetsu")
					all = Remove(all, "Yaezaki_An")
					all = Remove(all, "minamoto")
					all = Remove(all, "toyosatomimi")
					local undo = false
					local newTouhou = touhou 
					newTouhou = Remove(newTouhou, "sagume")
					newTouhou = Remove(newTouhou, "akyuu")
					newTouhou = Remove(newTouhou, "white")
					newTouhou = Remove(newTouhou, "aunn")
					newTouhou = Remove(newTouhou, "sp_seiga")
					newTouhou = Remove(newTouhou, "hecatia")	
					newTouhou = Remove(newTouhou, "yuyuko")  
					newTouhou = Remove(newTouhou, "yorihime")	 
					newTouhou = Remove(newTouhou, "magahara")	 
					newTouhou = Remove(newTouhou, "torisumi")	 
					newTouhou = Remove(newTouhou, "mugetsu")
					newTouhou = Remove(newTouhou, "Yaezaki_An")	
					newTouhou = Remove(newTouhou, "minamoto")
					newTouhou = Remove(newTouhou, "toyosatomimi")
									
					
					for _,p in sgs.qlist(room:getAlivePlayers()) do
						if string.find(p:screenName(), "aysage") then
							all = newTouhou
							undo = true
						end
					end
					if not undo then
						for _,p in sgs.qlist(room:getAlivePlayers()) do
							if p:getState() ~= "robot" then
								local choice = room:askForChoice(p, "ChooseJiang", "touhou+quankuo")
								if choice == "touhou" then
									all = newTouhou
								end
								break
							end
						end
					end
					
					local function getRandomGen(n, knowBanX) 
						local acquired = {}
						local ori_acq = {} 
						repeat
							local rand = math.random(1, #all) 
							if not table.contains(acquired, all[rand]) and not table.contains(knowBanX, all[rand]) then
								table.insert(acquired,(all[rand])) 
								table.insert(ori_acq,(all[rand])) 
							end
						until #acquired == n  
						return acquired, ori_acq
					end 
					knowBan = {} 
					local weights = {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1}
					local acquired, ori_acq = getRandomGen(12, knowBan)
	
					-- 按权重排序函数
					local function sort_by_weight() 
						for i = 1, #weights - 1 do
							for j = 1, #weights - i do
								if weights[j] < weights[j + 1] then 
									weights[j], weights[j + 1] = weights[j + 1], weights[j] 
									acquired[j], acquired[j + 1] = acquired[j + 1], acquired[j]
								end
							end
						end
					end 
					-- 更新权重并重新排序的函数
					local function choose_string(selected_string)
						for i = 1, #acquired do
							if acquired[i] == selected_string then
								weights[i] = weights[i] + 1
								break
							end
						end
						sort_by_weight()
					end

					local lordG 
					while true do
						if nextP:objectName() == lord:objectName() and lordG then break end 
						local general0 
						room:writeToConsole(" Seat" .. getNSeats(room, nextP) .. " start to choose general" .. getNSeats(room, nextP)) 
						room:askForUseCard(nextP, "@@ChooseJiang", "@ChooseJiang2")
						if nextP:getState() ~= "robot" then
							general0 = room:askForGeneral(nextP, table.concat(acquired,"+"))
						else
							general0 = room:askForChoice(nextP, "ChooseJiang", table.concat(acquired,"+"))
						end
						choose_string(general0)
						lordG = general0
						nextP = nextP:getNextAlive()
					end 
					nextP = lord
					acquired = {acquired[1], acquired[2]}
					local loyalistCount = 0
					local rebelCount = 0
					lordG = nil
					while true do
						if nextP:objectName() == lord:objectName() and lordG then break end 
						if loyalistCount < 2 and rebelCount < 3 then 
							room:askForUseCard(nextP, "@@ChooseJiang", "@ChooseJiang2")
							if nextP:getState() ~= "robot" then
								general0 = room:askForGeneral(nextP, table.concat(acquired,"+"))
							else
								general0 = room:askForChoice(nextP, "ChooseJiang", table.concat(acquired,"+"))
							end
							room:setPlayerMark(nextP, "hasChangedX", 1)
							ChangeHero(room, nextP, general0, {}, ori_acq)
							adjusetHP(room, nextP)
							if general0 == lordG and nextP:objectName() ~= lord:objectName() then 
								nextP:setRole("loyalist") 
								nextP:setShownRole(true)
								room:notifyProperty(nextP, nextP, "role")		
								room:setPlayerProperty(nextP, "role", sgs.QVariant("loyalist"))
								loyalistCount = loyalistCount + 1
							elseif nextP:objectName() == lord:objectName() then  
								lordG = general0
							else
								nextP:setRole("rebel") 
								nextP:setShownRole(true)
								room:notifyProperty(nextP, nextP, "role")	
								room:setPlayerProperty(nextP, "role", sgs.QVariant("rebel"))
								rebelCount = rebelCount + 1 
							end
						elseif rebelCount < 3 then
							if lordG == acquired[1] then general0 = acquired[2] else general0 = acquired[1] end 
							ChangeHero(room, nextP, general0, {}, ori_acq)
							adjusetHP(room, nextP)
							nextP:setRole("rebel") 
							nextP:setShownRole(true)
							room:notifyProperty(nextP, nextP, "role")	
							room:setPlayerProperty(nextP, "role", sgs.QVariant("rebel"))
							rebelCount = rebelCount + 1
						elseif loyalistCount < 2 then
							if lordG == acquired[1] then general0 = acquired[1] else general0 = acquired[2] end 
							ChangeHero(room, nextP, general0, {}, ori_acq)
							adjusetHP(room, nextP)
							nextP:setRole("loyalist") 
							nextP:setShownRole(true)
							room:notifyProperty(nextP, nextP, "role")	
							room:setPlayerProperty(nextP, "role", sgs.QVariant("loyalist"))
							loyalistCount = loyalistCount + 1 
						end 
						nextP = nextP:getNextAlive()
					end 
				end  
			end 
            for _, p in sgs.qlist(room:getAlivePlayers()) do
                room:setPlayerMark(p, "hasChangedD", 1)
            end
		else
			local room = playerQ:getRoom()
			local lord = room:getLord()
			local dying = data:toDying()
            if dying.who:objectName() == playerQ:objectName() 
				and dying.who:getHp() <= 0 then
				if (dying.who:getRole() == "loyalist" and lord:getMark("@loyalist") > 0)
					or (dying.who:getRole() == "rebel" and lord:getMark("@rebel") > 0) then
					if (dying.who:getRole() == "loyalist" and lord:getMark("@loyalist") > 0) then
						room:setPlayerMark(lord, "@loyalist", lord:getMark("@loyalist") - 1)
					else
						room:setPlayerMark(lord, "@rebel", lord:getMark("@rebel") - 1) 
					end 
					if lord:getMark("@rebel") == 0 then
						for _, p in sgs.qlist(room:getAlivePlayers()) do
							if p:getRole() == "rebel" then room:killPlayer(p) end 
						end 
					end 
					if lord:getMark("@loyalist") == 0 then
						room:killPlayer(lord)
					end 
					
					if room:askForSkillInvoke(dying.who, "fuhuo", data) then
						room:setPlayerProperty(dying.who, "hp", sgs.QVariant(dying.who:getMaxHp()))
						adjusetHP(room, dying.who)
					end
				end 
			end 
		end 
	end
}
HappySkillAnjiang:addSkill(ChooseJiangSame)



------------Pay 式混沌场
--本模式的问题：
--不能处理濒死时还未死的情况
--不能处理直接击杀、复活类技能
ChooseJiangChaos = sgs.CreateTriggerSkill{
    name = "ChooseJiangChaos",
    global = true,
    priority = 99,
    frequency = sgs.Skill_Compulsory,
    events = {sgs.TurnStart},
    on_trigger = function(self, event, playerQ, data)
        local room = playerQ:getRoom()
        if playerQ:hasSkill(self:objectName()) and playerQ:getMark("hasChangedD") == 0 then
			local lord = room:getLord()
            local nextP = lord
			local xp = room:getAlivePlayers():length()
            --删除卡牌【雌雄双股剑】
            deleteCard(room, playerQ, "DoubleSword", self)
            deleteCard(room, playerQ, "Roukankenx", self)
            deleteCard(room, playerQ, "Halberd", self) 
            if room:getAlivePlayers():length() == xp then
				nextP:setTag("ChaosRole", sgs.QVariant("lord"))  
				--先发身份
				math.randomseed(tostring(os.time()):reverse():sub(1, 7))
				
                local roles = {}
				local fixed = math.random(1, 20)
				local hasCiv = 0
				local haschaosX = false
				if fixed <= 5 then 
					table.insert(roles, "rebel")
					table.insert(roles, "loyalist")
				elseif (fixed >= 6 and fixed < 10) then 
					table.insert(roles, "civilian")
					hasCiv = hasCiv + 1	
					table.insert(roles, "renegade") 
				elseif (fixed >= 11 and fixed < 13) then 
					table.insert(roles, "chaosX")
					haschaosX = true	
					table.insert(roles, "renegade") 
				elseif (fixed >= 14 and fixed < 18) then 
					table.insert(roles, "rebel")
					table.insert(roles, "renegade") 
				elseif (fixed >= 18 and fixed <= 20) then 
					table.insert(roles, "loyalist")
					table.insert(roles, "renegade") 
				end 
				local hasCardinal = false
				for i = 1, xp - 3 do
					local rand = math.random(1, 90)
					room:writeToConsole("ChaosRole Numtest  " .. rand)
					if rand < 30 then
						table.insert(roles, "rebel")
					elseif (rand >= 30 and rand < 50) then
						table.insert(roles, "loyalist")
					elseif (rand >= 50 and rand < 58) then
						table.insert(roles, "renegade")
					elseif (rand >= 58 and rand < 68) then
						if hasCiv > 1 then 
							table.insert(roles, "rebel")
						else
							table.insert(roles, "civilian")
							hasCiv = hasCiv + 1	
						end 
					elseif (rand >= 68 and rand <= 78) then
						if haschaosX then 
							table.insert(roles, "renegade")
						else
							table.insert(roles, "chaosX")
							haschaosX = true	
						end  
					elseif (rand > 78 and rand <= 90) then
						if hasCardinal then 
							table.insert(roles, "rebel")
						else
							table.insert(roles, "cardinal")
							hasCardinal = true	
						end  
					-- elseif (rand > 90 and rand <= 100) then
						-- if hasCardinal then 
							-- table.insert(roles, "cardinal")
						-- else
							-- table.insert(roles, "rebel")
							-- haschaosX = true	
						-- end  
					end  
				end 
                for i = 1, xp - 1 do
                    nextP:drawCards(4)
                    nextP = nextP:getNextAlive()
                    local rand = math.random(1, #roles)
                    local nextRole = roles[rand] 
					nextP:setTag("ChaosRole", sgs.QVariant(nextRole))  
					room:setPlayerMark(nextP, "@unknown", 1)
                    nextP:setShownRole(false) 
                    table.removeOne(roles, nextRole)
					nextP:setTag("ChaosHero", sgs.QVariant("sujiang"))   
					PAYchaoscheck(room, nextP)
					room:writeToConsole("ChaosRole test " .. nextP:screenName() .. nextRole)
                end
                nextP:drawCards(4)
				
				--发暗将   
				local all = sgs.Sanguosha:getLimitedGeneralNames() 
				all = RemoveBanlist(all) 
				all = Remove(all, "wriggle")
				all = Remove(all, "sagume")
				all = Remove(all, "sp_seiga")
				all = Remove(all, "hecatia")	
				all = Remove(all, "yuyuko")		
				all = Remove(all, "torisumi")	
				all = Remove(all, "eika")			
				all = Remove(all, "yorihime")			
				all = Remove(all, "alice")		
				all = Remove(all, "magahara")		
                all = Remove(all, "Yaezaki_An")
				local undo = false
				local newTouhou = touhou
				newTouhou = Remove(newTouhou, "wriggle")
				newTouhou = Remove(newTouhou, "sagume")
				newTouhou = Remove(newTouhou, "sp_seiga")
				newTouhou = Remove(newTouhou, "hecatia")	
				newTouhou = Remove(newTouhou, "yuyuko") 
				newTouhou = Remove(newTouhou, "torisumi")	
				newTouhou = Remove(newTouhou, "eika")		
				newTouhou = Remove(newTouhou, "yorihime")	
								
				newTouhou = Remove(newTouhou, "alice")		
				newTouhou = Remove(newTouhou, "magahara")	 
				newTouhou = Remove(newTouhou, "Yaezaki_An")	
								
				table.insert(newTouhou, "kagerou")			
				
				for _,p in sgs.qlist(room:getAlivePlayers()) do
					if string.find(p:screenName(), "aysage") then
						all = newTouhou
						undo = true
					end
				end
				if not undo then
					for _,p in sgs.qlist(room:getAlivePlayers()) do
						if p:getState() ~= "robot" then
							local choice = room:askForChoice(p, "ChooseJiang", "touhou+quankuo")
							if choice == "touhou" then
								all = newTouhou
							end
							break
						end
					end
				end
				local function getRandomGen(n, knowBanX) 
					local acquired = {}
					local ori_acq = {} 
					repeat
						local rand = math.random(1, #all)
						room:writeToConsole(#all .. " xxxx " ..  all[#all] .. " ceshi  " .. rand)
						if not table.contains(acquired, all[rand]) and not table.contains(knowBanX, all[rand]) then
							table.insert(acquired,(all[rand])) 
							table.insert(ori_acq,(all[rand])) 
						end
					until #acquired == n 
					acquired[#acquired] = "rrandom" 
					room:writeToConsole(ori_acq[#ori_acq])
					return acquired, ori_acq
				end 
				knowBan = {}
				while true do
					local acquired, ori_acq = getRandomGen(6, knowBan)
					local general0 
					room:writeToConsole(" Seat" .. getNSeats(room, nextP) .. " start to choose general" .. getNSeats(room, nextP)) 
					room:askForUseCard(nextP, "@@ChooseJiang", "@ChooseJiang2")
					if nextP:getState() ~= "robot" then
						general0 = room:askForGeneral(nextP, table.concat(acquired,"+"))
					else
						general0 = room:askForChoice(nextP, "ChooseJiang", table.concat(acquired,"+"))
					end
					if general0 == "rrandom" then general0 = ori_acq[#ori_acq] end 
					if nextP:getRole() == "lord" then 
						room:setPlayerMark(nextP, "hasChangedX", 1)
						ChangeHero(room, nextP, general0, {}, ori_acq)
						adjusetHP(room, nextP)
					else
						checkCheating(room, nextP, general0, ori_acq) 
						nextP:setTag("ChaosHero", sgs.QVariant(general0)) 
					end 
					table.insert(knowBan, general0)
					 
					room:attachSkillToPlayer(nextP, "chaoscheck")
					if nextP:objectName() == room:getLord():objectName() then 
						break 
					end 
					PAYchaoscheck(room, nextP)
					for _, p in sgs.qlist(room:getAlivePlayers()) do
						if p:getNextAlive():objectName() == nextP:objectName() then 
							nextP = p
							break 
						end 
					end 
					
				end 
            end		
			
			for _, p in sgs.qlist(room:getAlivePlayers()) do
                room:setPlayerMark(p, "hasChangedD", 1)
            end
		end
	end
}
function dealMark(room, player, reCheck)
	local Role = "rebel"
	Role = player:getTag("ChaosRole"):toString()
	if player:getMark("@unknown") >= 1 then 
		room:setPlayerMark(player, "@unknown", 0)
		if Role == "rebel" then
			room:setPlayerMark(player, "@rebel", 1)
			room:setEmotion(player, "rebel")
		elseif Role == "loyalist" then
			room:setPlayerMark(player, "@loyalist", 1)
			room:setEmotion(player, "loyalist")
		elseif Role == "renegade" then
			room:setPlayerMark(player, "@renegade", 1)
			room:setEmotion(player, "renegade")
		elseif Role == "civilian" then
			room:setPlayerMark(player, "@civilian", 1)
			room:setEmotion(player, "civilian")
		elseif Role == "chaosX" then
			room:setPlayerMark(player, "@chaosX", 1)
			room:setEmotion(player, "chaosX")
		elseif Role == "cardinal" then
			room:setPlayerMark(player, "@cardinal", 1)
			room:setEmotion(player, "cardinal")
		end  		
	end 
	if reCheck then
		PAYchaoscheck(room, player)
	end 
	room:getThread():delay(1500)
	return Role
end 
local function ShowShowWay(room, player) 
	local Hero = player:getTag("ChaosHero"):toString()
	local HP = player:getHp()
	if player:getGeneralName() ~= Hero and room:askForSkillInvoke(player, "showHero") then
		local canDraw = true
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			if p:objectName() ~= player:objectName() and p:getGeneralName() == "sujiang" then 
				canDraw = false
			end 
		end 
		if canDraw then
			player:drawCards(2)
		end 
		local RandomAll = {}
		local ori_acq = {}
		table.insert(ori_acq, Hero) 
		ChangeHero(room, player, Hero, RandomAll, ori_acq) --ABCDEF
		room:writeToConsole("ChangeHero test242")
		adjusetHP2(room, player, HP)			
		local Hero2 = player:getTag("ChaosHeroS")
		if Hero2 and Hero2:toString() and Hero2:toString() ~= "" and room:askForSkillInvoke(player, "ChaosHeroS") then
			room:writeToConsole("ChaosHeroS " .. Hero2:toString())
			room:changeHero(player, Hero2:toString(), false, true, true, false)
		end 
		
	end
	-- if player:getMark("@unknown") >= 1 and room:askForSkillInvoke(player, "showRole") then
		-- local canDraw = true
		-- for _, p in sgs.qlist(room:getAllPlayers(true)) do
			-- if p:getRole() ~= "lord" and p:getMark("@unknown") == 0 then 
				-- canDraw = false
			-- end 
		-- end 
		-- if canDraw then
			-- player:drawCards(1)
		-- end 
		-- dealMark(room, player) 	
	-- end 
end 

local function PayWin(room, winPlayers)
	--把没赢的都杀光不就剩下的赢了
	-- 忽有狂徒夜磨刀，帝星飘摇荧惑高。 
	-- 翻天覆地从今始，杀人何须惜手劳。 
	-- 不礼不智不信人，大西王曰杀杀杀！  
	local playerlist = room:getAlivePlayers()			
	for _,p in sgs.qlist(playerlist) do
		local kill = true
		for i = 1, #winPlayers do
			if winPlayers[i] == p:objectName() then
				kill = false
			end 
		end 
		if kill and p:isAlive() and p:getRole() ~= "lord" then 
			room:killPlayer(p)
		end 
	end  
	for _,p in sgs.qlist(playerlist) do
		local kill = true
		for i = 1, #winPlayers do
			if winPlayers[i] == p:objectName() then
				kill = false
			end 
		end 
		if kill and p:isAlive() then 
			room:killPlayer(p)
		end 
	end  
end 
function CheckWin(room)  
	local lords = {}
	local lordisAlive = false
	local Lord
	for _,p in sgs.qlist(room:getAllPlayers(true)) do
		if p:getRole() == "lord" then
			Lord = p
		end 
	end 
	local loyalists = {}
	local renegades = {}
	local civilians = {}
	local rebels = {} 
	for _, p2 in sgs.qlist(room:getAlivePlayers()) do
        if p2:getHp() > 0 then
            if p2:getTag("ChaosRole"):toString() == "renegade" then
				table.insert(renegades, p2:objectName()) 
            elseif p2:getRole() == "lord" then
				lordisAlive = true 
				table.insert(lords, p2:objectName()) 
            elseif p2:getTag("ChaosRole"):toString() == "loyalist" then
				table.insert(lords, p2:objectName()) 
				table.insert(loyalists, p2:objectName()) 
            elseif p2:getTag("ChaosRole"):toString() == "rebel" then
				table.insert(rebels, p2:objectName()) 
            elseif p2:getTag("ChaosRole"):toString() == "civilian" then
				table.insert(civilians, p2:objectName())  
            elseif p2:getTag("ChaosRole"):toString() == "chaosX" then
				table.insert(renegades, p2:objectName())  
            elseif p2:getTag("ChaosRole"):toString() == "cardinal" then
				table.insert(rebels, p2:objectName())  
            end
        end
	end
	local function showAll(reason)
		room:writeToConsole(debug.traceback())
		local rebelWin = 1
		local lordWin = 2
		local renegadeWin = 3
		for _, player in sgs.qlist(room:getAlivePlayers()) do
			room:setPlayerMark(player, "@unknown", 0)
			local Role = player:getTag("ChaosRole"):toString()
			if Role == "rebel" then
				room:setPlayerMark(player, "@rebel", 1)
				player:setRole("rebel") 
				player:setShownRole(true)
				room:notifyProperty(player, player, "role")		
				room:setEmotion(player, "rebel")		
			elseif Role == "loyalist" then
				room:setPlayerMark(player, "@loyalist", 1)
				player:setRole("loyalist") 
				player:setShownRole(true)
				room:notifyProperty(player, player, "role")	
				room:setEmotion(player, "loyalist")			
			elseif Role == "renegade" then
				room:setPlayerMark(player, "@renegade", 1)
				player:setRole("renegade") 
				player:setShownRole(true)
				room:notifyProperty(player, player, "role")		
				room:setEmotion(player, "renegade")			
			elseif Role == "chaosX" then
				room:setPlayerMark(player, "@chaosX", 1)
				player:setRole("renegade") 
				player:setShownRole(true)
				room:notifyProperty(player, player, "role")		
				room:setEmotion(player, "chaosX")			
			elseif Role == "civilian" then
				room:setPlayerMark(player, "@civilian", 1)
				room:setEmotion(player, "civilian")			
				if reason == rebelWin then
					player:setRole("rebel") 
					player:setShownRole(true)
					room:notifyProperty(player, player, "role")		
				elseif reason == lordWin then
					player:setRole("loyalist") 
					player:setShownRole(true)
					room:notifyProperty(player, player, "role")		
				elseif reason == renegadeWin then
					player:setRole("renegade") 
					player:setShownRole(true)
					room:notifyProperty(player, player, "role")		 
				end 
			elseif Role == "cardinal" then
				room:setPlayerMark(player, "@cardinal", 1)
				room:setEmotion(player, "cardinal")		
				player:setRole("rebel") 
				player:setShownRole(true)
				room:notifyProperty(player, player, "role")			 
			end  			
		end 
		room:getThread():delay(2500)	
	end 
	if #renegades == 0 and #rebels == 0 then
		showAll(2)
		PayWin(room, lords)  
		return
	end 	 
	if not lordisAlive then
		room:writeToConsole("gameover test 1713")  
		if #rebels == 0 then
			if #renegades == 1 then 
				showAll(3)
				room:gameOver(renegades[1])
				return
			else
				local rp
				for _,p in sgs.qlist(room:getAllPlayers(true)) do
					if not p:isAlive() and p:getRole() == "rebel" then
						rp = p
						break
					end
				end
				if rp then --反贼胜利
					showAll(1)
					room:setPlayerProperty(rp, "maxhp", sgs.QVariant(1))
					room:setPlayerProperty(rp, "hp", sgs.QVariant(1))
					room:revivePlayer(rp, true)		
					table.insert(civilians, rp:objectName())
					PayWin(room, civilians)	
					return
				else
					showAll(2)
					room:setPlayerProperty(Lord, "maxhp", sgs.QVariant(1))
					room:setPlayerProperty(Lord, "hp", sgs.QVariant(1))
					--room:revivePlayer(Lord, true)		
					table.insert(lords, Lord:objectName())
					PayWin(room, lords)	
					return
				end 
			end 
		end 
		room:writeToConsole("gameover test 1745")  
		showAll()
	end 
end 
ChooseJiangChaos2 = sgs.CreateTriggerSkill{
    name = "#ChooseJiangChaos", 
    priority = 90,
    global = true,
    frequency = sgs.Skill_Compulsory,
    events = {sgs.AskForPeachesDone, sgs.EventPhaseStart, sgs.Damaged, sgs.Damage, sgs.TargetConfirmed, sgs.DamageInflicted, sgs.CardAsked, sgs.Dying,
		sgs.EventPhaseEnd, sgs.TurnStart, sgs.CardUsed, sgs.CardResponded, sgs.HpRecover},
    on_trigger = function(self, event, player, data)  --雪 舞 射命丸文
		local room = player:getRoom()
		local LLord = room:findPlayerBySkillName("ChooseJiangChaos")
		if not LLord then return false end 
		if event == sgs.AskForPeachesDone then 
			for _, p in sgs.qlist(room:getAllPlayers(true)) do
				if p:hasSkill("ChooseJiangChaos") then 
					local dying = data:toDying()
					if dying.who:objectName() == player:objectName() then
						if dying.who:getTag("ChaosRole") and dying.who:getTag("ChaosRole"):toString() ~= "" 
							and (dying.who:getHp() < 1 or not dying.who:isAlive()) then
							local RRRole = dealMark(room, dying.who)
							local general1 = dying.who:getTag("ChaosHero"):toString()
							if dying.damage and dying.damage.from and dying.damage.from:getTag("ChaosRole"):toString()
								and dying.who:objectName() ~= dying.damage.from:objectName() and dying.who:getRole() ~= "lord" then
								if dying.damage.from:getTag("ChaosRole"):toString() == "chaosX" then
									local Hp = dying.damage.from:getHp()
									dying.damage.from:setTag("ChaosRole", sgs.QVariant(RRRole))  
									dealMark(room, dying.damage.from)
									local Hero = dying.damage.from:getTag("ChaosHero"):toString()
									local RandomAll = {}
									local ori_acq = {}
									table.insert(ori_acq, Hero) 
									ChangeHero(room, dying.damage.from, Hero, RandomAll, ori_acq)

                                    local GeneralTure = sgs.Sanguosha:getGeneral(general1)
                                    for _, skill in sgs.qlist(GeneralTure:getSkillList()) do 
                                        local shouldAdd = true
                                        
                                        for _, skill2 in sgs.qlist(GeneralTure:getVisibleSkillList()) do
                                            if skill2:objectName() == skill:objectName() then 
                                                room:handleAcquireDetachSkills(dying.damage.from, skill:objectName()) 
                                                shouldAdd = false  
                                            end 
                                        end  
                                        if shouldAdd then 
                                            dying.damage.from:addSkill(skill:objectName())
                                        end   
                                    end   

									--room:changeHero(dying.damage.from, general1, false, true, true, false)
									adjusetHP2(room, dying.damage.from, Hp)										
								end 
							end 
							local Role = dying.who:getTag("ChaosRole"):toString()
							if Role == "civilian" or Role == "chaosX" then
								dying.who:setRole("renegade") 
								dying.who:setShownRole(true)
								room:notifyProperty(dying.who, dying.who, "role")	
								if dying.damage and dying.damage.from and dying.damage.from:isAlive() and dying.damage.from:objectName() ~= dying.who:objectName() then
									dying.damage.from:drawCards(2)
								end 
							elseif Role == "cardinal" then
								dying.who:setRole("rebel") 
								dying.who:setShownRole(true)
								room:notifyProperty(dying.who, dying.who, "role")								
							else
								dying.who:setRole(Role) 
								dying.who:setShownRole(true)
								room:notifyProperty(dying.who, dying.who, "role")
							end 
							CheckWin(room)
						end 
					end 
				end
			end
		elseif event == sgs.EventPhaseStart then 
			if room:getCurrent():objectName() == player:objectName() then 
				local Hero = player:getTag("ChaosHero"):toString()
				for _, p in sgs.qlist(room:getAllPlayers(true)) do
					if p:hasSkill("ChooseJiangChaos") then 
						if (player:getPhase() == sgs.Player_Start or player:getPhase() == sgs.Player_Finish) then 
							ShowShowWay(room, player)			 
						end 
						if player:getPhase() == sgs.Player_Judge and string.find(Hero, "sanae") and player:getGeneralName() == "sujiang"  then 
							ShowShowWay(room, player)	
						end 
						if player:getPhase() == sgs.Player_Discard and (Hero == "kosuzu" or Hero == "minamoto") and player:getGeneralName() == "sujiang"  then 
							ShowShowWay(room, player)	
						end 						
						if player:getPhase() == sgs.Player_Draw and (Hero == "fujiwara" or Hero == "akong") and player:getGeneralName() == "sujiang" then 
							ShowShowWay(room, player)	
						end 
					end 
				end  
				if player:getPhase() == sgs.Player_Play then 
					for _, aplayer in sgs.qlist(room:getAlivePlayers()) do
						local Hero = aplayer:getTag("ChaosHero"):toString()
						if (Hero == "tsukasa" or Hero == "chimata" or Hero == "minamoto" or Hero == "nitori") and aplayer:getGeneralName() == "sujiang" then 
							ShowShowWay(room, aplayer)	
						end 
					end 
				end 		
                if player:getPhase() == sgs.Player_Finish then
                    for _, aplayer in sgs.qlist(room:getAlivePlayers()) do
						local Hero = aplayer:getTag("ChaosHero"):toString()
                        if (Hero == "yukariex") and aplayer:getGeneralName() == "sujiang" then 
							ShowShowWay(room, aplayer)	
						end 
                    end 
                end 
			end 
		elseif event == sgs.EventPhaseEnd then  
			local Hero = player:getTag("ChaosHero"):toString()
			for _, p in sgs.qlist(room:getAllPlayers(true)) do
				if p:hasSkill("ChooseJiangChaos") then 
					if player:getPhase() == sgs.Player_Play then
						for _, aplayer in sgs.qlist(room:getAlivePlayers()) do
							local Hero = aplayer:getTag("ChaosHero"):toString()
							if Hero == "kutaka" and aplayer:getHandcardNum() == 2 and aplayer:getGeneralName() == "sujiang" then 
								ShowShowWay(room, aplayer)	
							end 
						end 						
					end
					if player:getPhase() == sgs.Player_Draw and player:getHp() == 1  then
						for _, aplayer in sgs.qlist(room:getAlivePlayers()) do
							local Hero = aplayer:getTag("ChaosHero"):toString()
							if Hero == "prismriver" and aplayer:getGeneralName() == "sujiang" then 
								ShowShowWay(room, aplayer)	
							end 
						end 						
					end 
					if player:getPhase() == sgs.Player_Finish then
						for _, aplayer in sgs.qlist(room:getAlivePlayers()) do
							local Hero = aplayer:getTag("ChaosHero"):toString()
							if Hero == "akyuu" and aplayer:getGeneralName() == "sujiang" then 
								ShowShowWay(room, aplayer)	
							end 
						end 						
					end 
				end
			end     
		elseif event == sgs.CardUsed then 
		    local use = data:toCardUse()
            if not use.from then return false end
            if use.card:isKindOf("SkillCard") then return false end
			if use.card and use.from:objectName() == player:objectName() then
				if use.card:getNumber() == room:getLord():getMark("@clock_time") then 
					local Hero = use.from:getTag("ChaosHero"):toString()
					if Hero == "renko" and use.from:getGeneralName() == "sujiang" then 
						ShowShowWay(room, use.from)	
					end 
				end
				if room:getCurrent():objectName() ~= player:objectName() then
					local Hero = use.from:getTag("ChaosHero"):toString()
					if Hero == "iku" and use.from:getGeneralName() == "sujiang" then 
						ShowShowWay(room, use.from)	
					end 				
				end 
			end 
			if use.card and use.card:isKindOf("EquipCard") then
				for _, aplayer in sgs.qlist(room:getAlivePlayers()) do
					local Hero = aplayer:getTag("ChaosHero"):toString()
					if Hero == "rinnosuke" and aplayer:getGeneralName() == "sujiang" then 
						ShowShowWay(room, aplayer)	
					end 
				end 			
			end 
		elseif event == sgs.CardResponded then
			local cd = data:toCardResponse().m_card
			if cd and player:getPhase() == sgs.Player_NotActive and not cd:isKindOf("SkillCard") then
				local Hero = player:getTag("ChaosHero"):toString()
				if Hero == "iku" and player:getGeneralName() == "sujiang" then 
					ShowShowWay(room, player)	
				end 				
			end 
		elseif event == sgs.TurnStart then 
			if room:getCurrent():isLord() and player:getMark("@extra_turn") == 0 and room:getCurrent():objectName() == player:objectName() then
				local cTargets = sgs.SPlayerList()  
				local cardinalA
				for _, aplayer in sgs.qlist(room:getAlivePlayers()) do
					if aplayer:getMark("transRebel") > 0 and aplayer:getRole() ~= "lord"  then 
						cTargets:append(aplayer)
						room:setPlayerMark(aplayer, "transRebel", 0)
					end 
					if aplayer:getTag("ChaosRole") and aplayer:getTag("ChaosRole"):toString() == "cardinal" then
						cardinalA = aplayer
					end 
				end 
				if not cTargets:isEmpty() and cardinalA and cardinalA:isAlive() then  
					local to = room:askForPlayerChosen(cardinalA, cTargets, "cardinal", "cardinal-invoke", true, false)
					if to and to:objectName() ~= cardinalA:objectName() then
						to:setTag("ChaosRole", sgs.QVariant("rebel"))  
						dealMark(room, to, true) 
						cardinalA:setTag("ChaosRole", sgs.QVariant("rebel"))  
					end 
				end
				for _, aplayer in sgs.qlist(room:getAlivePlayers()) do
					local Hero = aplayer:getTag("ChaosHero"):toString()
					if (Hero == "momiji" or Hero == "mayumi" or Hero == "benben") and aplayer:getGeneralName() == "sujiang"  then 
						ShowShowWay(room, aplayer)	
					end 
				end 			
				for _, kagerou in sgs.qlist(room:getAlivePlayers()) do
					local Hero = kagerou:getTag("ChaosHero"):toString()
					if Hero == "kagerou" then
						if kagerou:getRole() == "lord" then room:setPlayerProperty(kagerou, "hp", sgs.QVariant(3)) end 
						local bool = kagerou:getTag("ChaosHeroS") and kagerou:getTag("ChaosHeroS"):toString() ~= ""
						if not bool and room:askForSkillInvoke(kagerou, "luayinxi") then
							if kagerou:getRole() == "lord" then 
								local to = room:askForPlayerChosen(kagerou, room:getOtherPlayers(kagerou), "luayinxi", "luayinxi-invoke", false, true)
								if to then 
									local ABCDE = sgs.SPlayerList()
									ABCDE:append(to) 
									local votes = 0
									for _, people in sgs.qlist(room:getAlivePlayers()) do
										local toB = room:askForPlayerChosen(people, ABCDE, "luayinxi", "luayinxi-agree", true, true)
										if toB and toB:objectName() then
											votes = votes + 1
										else
											room:setEmotion(people, "yiyiyiyi")
										end 
									end 
									if votes > room:getAlivePlayers():length() / 2 then
										room:damage(sgs.DamageStruct("luayinxi", kagerou, to, 2, sgs.DamageStruct_Normal))
									end 
								end
							elseif kagerou:getTag("ChaosRole"):toString() == "rebel" or kagerou:getTag("ChaosRole"):toString() == "loyalist" then
								local to = room:askForPlayerChosen(kagerou, room:getOtherPlayers(kagerou), "luayinxi", "luayinxiY-invoke", false, false)
								if to then
									local generalP = room:askForGeneral(kagerou, "jg_soul_pangtong+jg_soul_liubei+jg_soul_caozhen+jg_soul_huangyueying+jg_soul_xiahouyuan+jg_soul_zhanghe")
									if to:getTag("ChaosRole"):toString() == generalP then
										local choices = {"check1", "check2", "check4"}
										local choice = room:askForChoice(kagerou, "luayinxi", table.concat(choices, "+"))
									else
										local choices = {"check1", "check3", "check4"}
										local choice = room:askForChoice(kagerou, "luayinxi", table.concat(choices, "+"))										
									end 
								end 
							else
								local newTouhou = touhou
								newTouhou = Remove(newTouhou, "wriggle")
								newTouhou = Remove(newTouhou, "sagume")
								newTouhou = Remove(newTouhou, "sp_seiga")
								newTouhou = Remove(newTouhou, "hecatia")	
								newTouhou = Remove(newTouhou, "yuyuko") 
								newTouhou = Remove(newTouhou, "torisumi")	
								newTouhou = Remove(newTouhou, "eika")		
								newTouhou = Remove(newTouhou, "yorihime")	
								
								newTouhou = Remove(newTouhou, "alice")		
								newTouhou = Remove(newTouhou, "magahara")	 
								 
								local acquired = {} 
								repeat
									local rand = math.random(1,#newTouhou) 
									if not table.contains(acquired,newTouhou[rand]) then
										table.insert(acquired,(newTouhou[rand])) 
									end
								until #acquired == 5					
								general_1 = room:askForGeneral(kagerou, table.concat(acquired,"+"))
								kagerou:setTag("ChaosHeroS", sgs.QVariant(general_1))  
							end 
						end 
					end 
				end 
			end 
		elseif event == sgs.Damaged then 
			local damage = data:toDamage()
			if damage and damage.to and damage.to:objectName() == player:objectName() then 
				local Hero = player:getTag("ChaosHero"):toString()
				if (string.find(Hero, "reisen") or string.find(Hero, "toyohime") or Hero == "ran" or Hero == "miyoi" or Hero == "tatara" or Hero == "toyosatomimi"
					or Hero == "ringoseiran" or Hero == "shinmyoumaru" or Hero == "tokiko" or Hero == "kana" or Hero == "cirno" or Hero == "tenkai_tsurubami")
					 and player:getGeneralName() == "sujiang" then 
					ShowShowWay(room, player)
				end 
				local xo = room:getLord():getMark("@clock_time")
				if damage and damage.from and damage.from:getTag("ChaosRole") and damage.from:getTag("ChaosRole"):toString() == "cardinal" and xo > 1 then
					damage.to:addMark("transRebel")
				end 
			end 
		elseif event == sgs.Damage then 
			local damage = data:toDamage()
			if damage and damage.from and damage.from:objectName() == player:objectName() then 		
				local Hero = player:getTag("ChaosHero"):toString()
				if (string.find(Hero, "reisen") or Hero == "ringoseiran" or Hero == "tenkai_tsurubami"
					or Hero == "shinmyoumaru" or Hero == "komachi" or Hero == "tatara") 
					and player:getGeneralName() == "sujiang" then 
					ShowShowWay(room, player)
				end 	 			
			end 
		elseif event == sgs.Dying then 
			local dying = data:toDying()
			local _player = dying.who		
			if dying.damage and _player and _player:getHp() < 1 then
				for _, aplayer in sgs.qlist(room:getAlivePlayers()) do
					local Hero = aplayer:getTag("ChaosHero"):toString()
					if Hero == "komachi" and _player:getHp() == 0 and aplayer:getGeneralName() == "sujiang" then 
						ShowShowWay(room, aplayer)	
					end  
					if (Hero == "keine" or Hero == "sarielelis") and aplayer:getGeneralName() == "sujiang" then 
						ShowShowWay(room, aplayer)	
					end 
				end 
				local killer = dying.damage.from
				if killer then 
					local Hero = killer:getTag("ChaosHero"):toString()
					if Hero == "momoyo" and killer:getGeneralName() == "sujiang" then 
						ShowShowWay(room, killer)
					end 
				end 
			end 
		elseif event == sgs.DamageInflicted then 
			local damage = data:toDamage()
			-- room:writeToConsole("medicine test1" .. player:screenName())  --这里player是受到伤害的角色
			-- room:writeToConsole("medicine test1" .. damage.from:screenName())
			if damage and damage.from then 		
				local Hero = damage.from:getTag("ChaosHero"):toString()
				--room:writeToConsole("medicine test2")
				if string.find(Hero, "medicine") then 
					ShowShowWay(room, damage.from)
					return false
				end
				if damage.damage >= damage.to:getHp() then 
					if damage.to:objectName() == player:objectName() then
						if Hero == "keiki" and damage.from:getGeneralName() == "sujiang" then 
							ShowShowWay(room, damage.from)
						end 
					end 
					if Hero == "kagerou" and damage.from:getGeneralName() == "sujiang" then 
						ShowShowWay(room, damage.from)
					end 
				end 
			end 
			if damage and damage.to then 	
				local Hero = damage.to:getTag("ChaosHero"):toString()
				if (Hero == "sp_rin" or Hero == "mai_satono") and damage.to:getGeneralName() == "sujiang" then 
					ShowShowWay(room, damage.from)
				end
			end 
		elseif event == sgs.TargetConfirmed then
		    -- if use.from and use.from:objectName() == player:objectName() then
				-- local Hero = player:getTag("ChaosHero"):toString()
				-- if use.card and use.card:isKindOf("Slash") then 
					-- if string.find(Hero, "joon") then 
						-- ShowShowWay(room, player)
					-- end 
				-- end 
			-- end 
			
			local use = data:toCardUse()
			for _, t in sgs.qlist(use.to) do
				if t and t:objectName() == player:objectName() then
					local Hero = player:getTag("ChaosHero"):toString()
					if use.to:length() == 1 and t:getHp() > 0 then
						if (Hero == "kitcho") and player:getGeneralName() == "sujiang" then 
							ShowShowWay(room, player)
						end 						
					end 
					if use.card and use.card:isKindOf("Slash") then 
						if (Hero == "joon"  or Hero == "shinki" or Hero == "mima") and player:getGeneralName() == "sujiang"  then 
							ShowShowWay(room, player)
						end 
						if Hero == "kanako" and player:getHandcardNum() - player:getMaxCards() > 0 and player:getGeneralName() == "sujiang"  then
							ShowShowWay(room, player)
						end 
					end 				
				end 
			end 
		elseif event == sgs.CardAsked then
			local Hero = player:getTag("ChaosHero"):toString() 
			if string.find(Hero, "kanako") then 			
				local pattern = data:toStringList()[1]
				if (pattern == "jink") then	
					if (Hero == "shinki" or Hero == "mima") and player:getGeneralName() == "sujiang" then 
						ShowShowWay(room, player)
					end 				
					if Hero == "kanako" and player:getHandcardNum() - player:getMaxCards() > 0 and player:getGeneralName() == "sujiang"  then
						ShowShowWay(room, player)
					end 
				end 
				if (pattern == "slash") then	
					if (Hero == "shinki" or Hero == "mima") and player:getGeneralName() == "sujiang" then 
						ShowShowWay(room, player)
					end 				
					if Hero == "kanako" and player:getHandcardNum() - player:getMaxCards() > 0 and player:getGeneralName() == "sujiang"  then
						ShowShowWay(room, player)
					end 
				end 
			end 
		end 
	end
}
luachaoscheckCard = sgs.CreateSkillCard{
	name = "chaoscheck" ,
	target_fixed = true ,
	will_throw = false,
	on_use = function(self, room, source, targets)
		local choice = room:askForChoice(source, self:objectName(), "checkHR+surrenderPays")
		if choice == "checkHR" then
			PAYchaoscheck(room, source)
		elseif choice == "surrenderPays" then
			if room:askForSkillInvoke(source, "surrenderPaysX") then
				room:loseHp(source, source:getHp() + 8)	
			end 
		else
			room:setEmotion(source, "flan1")
		end 
	end
}
chaoscheck = sgs.CreateZeroCardViewAsSkill{
	name = "chaoscheck&",
	view_as = function(self, cards)
		return luachaoscheckCard:clone()
	end,
	enabled_at_play = function(self, player)
		return true
	end
}
 
HappySkillAnjiang:addSkill(ChooseJiangChaos)
HappySkillAnjiang:addSkill(ChooseJiangChaos2)
HappySkillAnjiang:addSkill(chaoscheck)
luafanji2 = sgs.CreateTriggerSkill{
    name = "#luafanji",
    global = true,
    frequency = sgs.Skill_Compulsory,
    events = {sgs.ConfirmDamage},
    can_trigger = function(self, player)
        return player
    end,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if event == sgs.ConfirmDamage then
            local damage = data:toDamage()
            if damage.to and (damage.card and damage.card:isKindOf("Slash") and damage.from:getMark("@luafanji1") > 0) then
                damage.damage = damage.damage + 1
                data:setValue(damage)
                return false
            end
        end
    end
}

luafanji = sgs.CreateTriggerSkill{
    name = "luafanji",
    global = true,
    frequency = sgs.Skill_Compulsory,
    events = {sgs.EventPhaseStart, sgs.TargetConfirmed, sgs.EventPhaseChanging, sgs.DrawNCards},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if event == sgs.EventPhaseStart then
            local phase = player:getPhase()
            if phase == sgs.Player_Finish and player:objectName() == room:getCurrent():objectName() then
                if not player:hasFlag("fanjiFailed") then
                    for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
                        if room:askForSkillInvoke(p, self:objectName(), data) then
                            room:setPlayerMark(player, "@luafanji", 2)
                            room:setPlayerMark(player, "@luafanji0", 1)
                        end
                    end
                end
            elseif phase == sgs.Player_Start and player:objectName() == room:getCurrent():objectName() and player:getMark("@luafanji") > 0  then
                room:setPlayerMark(player, "@luafanji1", 1)
                room:setPlayerMark(player, "@damageup", player:getMark("@damageup") + 1)
            end
        elseif event == sgs.TargetConfirmed then
            local use = data:toCardUse()
            if not use.from then return end
			if use.card and use.card:isKindOf("SkillCard") then return end 
            if use.from:objectName() ~= player:objectName() or room:getCurrent():objectName() ~= player:objectName() then return end
            for _, p in sgs.qlist(use.to) do
                if p:objectName() ~= player:objectName() then
                    room:setPlayerFlag(player, "fanjiFailed")
                end
            end
        elseif event == sgs.EventPhaseChanging then
            local change = data:toPhaseChange()
            if change.to == sgs.Player_NotActive and player:objectName() == room:getCurrent():objectName() then
                if player:getMark("@luafanji") > 0 then 
                    player:loseMark("@luafanji1")
                    room:setPlayerMark(player, "@damageup", player:getMark("@damageup") - 1)
                end 
                player:loseMark("@luafanji")
            end
        elseif event == sgs.DrawNCards and player:objectName() == room:getCurrent():objectName() and player:getMark("@luafanji0") > 0 then
            local count = data:toInt()
            count = count + 1
            data:setValue(count)
            room:setPlayerMark(player, "@luafanji0", 0)
        end
    end
}

luagemingCard = sgs.CreateSkillCard{
    name = "luageming",
    mute = true,
    target_fixed = false,
    will_throw = true,
    filter = function(self, targets, to_select)
        return true
    end,
    feasible = function(self, targets)
        return true
    end,
    about_to_use = function(self, room, cardUse)
        local use = cardUse
        if not use.to:contains(use.from) then
            use.to:append(use.from)
        end
        room:removePlayerMark(use.from, "@geming")
        self:cardOnUse(room, use)
    end,
    on_use = function(self, room, source, targets)
        for _,p in ipairs(targets) do
            p:drawCards(1)
            if not p:hasSkill("luagemingg") then
                room:attachSkillToPlayer(p, "luagemingg")
            end
        end
    end
}
luagemingVS = sgs.CreateZeroCardViewAsSkill{
    name = "luageming",
    view_as = function(self, cards)
        return luagemingCard:clone()
    end,
    enabled_at_play = function(self, player)
        return player:getMark("@geming") >= 1
    end
}
luageming = sgs.CreateTriggerSkill{
    name = "luageming",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@geming",
    view_as_skill = luagemingVS,
    on_trigger = function()
    end
}
luageming2 = sgs.CreateTriggerSkill{
    name = "#luageming",
    global = true,
    frequency = sgs.Skill_Compulsory,
    events = {sgs.EventPhaseChanging},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if event == sgs.EventPhaseChanging then
            local change = data:toPhaseChange()
            if change.to == sgs.Player_Start and room:getCurrent():hasSkill("luageming") and room:getCurrent():getMark("@geming") == 0 then
                for  _, kplayer in sgs.qlist(room:getAlivePlayers()) do
                    room:setPlayerMark(kplayer, "gemingEnd", 1)
                end
            end
        end
    end
}

zhengzhen:addSkill(luafanji)
zhengzhen:addSkill(luafanji2)
zhengzhen:addSkill(luageming)
zhengzhen:addSkill(luageming2)

luatanmiCard = sgs.CreateSkillCard{
    name = "luatanmi",
    target_fixed = true,
    on_use = function(self, room, source, targets)

        if source:hasFlag("luatanmi") and source:getTag("luatanmi") and source:getTag("luatanmi"):toString() ~= "" then

            local uaqiuwen = source:getTag("luatanmi"):toString() --甚至提供及时查看功能，太nice了！
            uaqiuwen = uaqiuwen:split("|")
            local range_list = sgs.IntList()
            for _, id in ipairs(uaqiuwen) do
                range_list:append(id)
            end
            room:fillAG(range_list)
            room:getThread():delay(1500)
            room:clearAG()
        else
            source:removeTag("luatanmi")
            room:setPlayerMark(source, "luatanmi", 0)
            local card_ids = room:getNCards(5)
            local card_ids2 = {}
            room:fillAG(card_ids)
            room:getThread():delay(1500)
            for _,card_id in sgs.qlist(card_ids) do
                local range_list = sgs.IntList()
                range_list:append(card_id)
                table.insert(card_ids2, card_id)
                room:askForGuanxing(source, range_list, sgs.Room_GuanxingDownOnly)
            end
            room:clearAG()
            source:setTag("luatanmi", sgs.QVariant(table.concat(card_ids2, "|")))
            room:setPlayerFlag(source, "luatanmi")
            room:setPlayerMark(source, "luatanmi", card_ids:at(0))
        end
    end
}
luatanmi = sgs.CreateZeroCardViewAsSkill{
    name = "luatanmi",
    view_as = function(self, cards)
        return luatanmiCard:clone()
    end,
    enabled_at_play = function(self, player)
        if player:hasUsed("#luatanmi") then
            if player:getMark("luatanmi") > 0 then
                return true
            end
            return false
        end
        return true
    end
}

luatanmi3 = sgs.CreateTriggerSkill
{
    name = "#luatanmi2",
    events = {sgs.CardUsed, sgs.CardResponded},
    frequency = sgs.Skill_Frequent,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        local card
        if event == sgs.CardUsed then
            card = data:toCardUse().card
        else
			if data:toCardResponse().m_isUse then
				card = data:toCardResponse().m_card
			end 
        end
        if card and not card:isKindOf("SkillCard") then
            if player:getTag("luatanmi") and player:getTag("luatanmi"):toString() ~= "" then
                local uaqiuwen = player:getTag("luatanmi"):toString()
                uaqiuwen = uaqiuwen:split("|")
                if #uaqiuwen > 0 then
                    if card:getTypeId() == sgs.Sanguosha:getCard(uaqiuwen[1]):getTypeId() then
                        player:drawCards(1)
                        local new_qiu = {}
                        for _, id in ipairs(uaqiuwen) do
                            if id ~= uaqiuwen[1] then
                                table.insert(new_qiu, id)
                            end
                        end
                        if #new_qiu > 0 then
                            room:setPlayerMark(player, "luatanmi", new_qiu[1])
                            player:setTag("luatanmi", sgs.QVariant(table.concat(new_qiu, "|")))
                        else
                            room:setPlayerMark(player, "luatanmi", 0)
                            player:removeTag("luatanmi")
                        end
                    else
                        player:removeTag("luatanmi")
                        room:setPlayerMark(player, "luatanmi", 0)
                    end
                else
                    player:removeTag("luatanmi")
                    room:setPlayerMark(player, "luatanmi", 0)
                end
            end
        end
    end
}

luatanmi2 = sgs.CreateTargetModSkill{
    name = "#luatanmi",
    pattern = ".",
    distance_limit_func = function(self, player, card)
        if player:hasSkill("luatanmi") then
            if (player:hasFlag("luatanmi") and card:getTypeId() == sgs.Sanguosha:getCard(player:getMark("luatanmi")):getTypeId())
                    or player:getPhase() == sgs.Player_Draw then
                return 1000
            end
        else
            return 0
        end
    end
}

luatanmi4 = sgs.CreateTriggerSkill{
    name = "#luatanmi3" ,
    --global = true ,
    frequency = sgs.Skill_Compulsory ,
    events = {sgs.BeforeCardsMove} ,
    on_trigger = function(self, event, player, data)
        local move = data:toMoveOneTime()
        local room = player:getRoom()
        if room:getCurrent():objectName() ~= player:objectName() then return end
        if move.reason.m_skillName and move.reason.m_skillName == "luatanmi" then return end
        local _movefrom
        if move.from then
            for _, p in sgs.qlist(room:getAlivePlayers()) do
                if move.from:objectName() == p:objectName() then
                    _movefrom = p
                    break
                end
            end   --我去MoveOneTime的from居然是player不是splayer，还得枚举所有splayer获取下……
        end
        if not _movefrom then return end
        local basic = bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)
        if basic ~= sgs.CardMoveReason_S_REASON_DISCARD then return false end

        local tanmi = {}
        for _,card_idX in sgs.qlist(move.card_ids) do
            table.insert(tanmi, card_idX)
        end

        local i = 0
        for _, card_id in ipairs(tanmi) do
            move.card_ids:removeOne(card_id)
            move.from_places:removeAt(i)

            local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_DISCARD, _movefrom:objectName(), "luatanmi", "")
            room:moveCardTo(sgs.Sanguosha:getCard(card_id), _movefrom, nil, sgs.Player_DrawPile, reason, true)

            i = i + 1
        end
        data:setValue(move)
        return false
    end
}

luabijinCard = sgs.CreateSkillCard{
    name = "luabijin",
    filter = function(self, targets, to_select)
        return (#targets == 0)
    end,
    on_effect = function(self, effect)
        local room = effect.to:getRoom()
        if not room:askForDiscard(effect.to, self:objectName(), 1, 1, true, false) then
            room:damage(sgs.DamageStruct(self:objectName(), nil, effect.to))
        end
    end
}
luabijin = sgs.CreateViewAsSkill{
    name = "luabijin" ,
    n = 1 ,
    view_filter = function(self, selected, to_select)
        if #selected ~= 0 then return false end
        return (to_select:getSuit() == sgs.Card_Heart or to_select:getSuit() == sgs.Card_Spade) and (not sgs.Self:isJilei(to_select))
    end ,
    view_as = function(self, cards)
        if #cards ~= 1 then return nil end
        local LuaweiwoCard1 = luabijinCard:clone()
        LuaweiwoCard1:addSubcard(cards[1])
        return LuaweiwoCard1
    end ,
    enabled_at_play = function(self, player)
        return not player:hasUsed("#luabijin")
    end ,
}
--[[
luatanmi5 = sgs.CreateTriggerSkill{
    name = "#luatanmi5",
    frequency = sgs.Skill_Frequent,
    events = {sgs.DrawNCards},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if player:hasSkill("luatanmi") and room:askForSkillInvoke(player, "luatanmi") then
            local count = data:toInt() - 1
            data:setValue(count)
            room:setPlayerFlag(player, "luatanmi2")
        end
    end
}]]--

hifu:addSkill(luatanmi)
hifu:addSkill(luatanmi2)
hifu:addSkill(luatanmi3)
hifu:addSkill(luatanmi4)
hifu:addSkill(luabijin)

hifuA:addSkill(luatanmi)
hifuA:addSkill(luatanmi2)
hifuA:addSkill(luatanmi3)
hifuA:addSkill(luatanmi4)
hifuA:addSkill(luabijin)

hifuB:addSkill(luatanmi)
hifuB:addSkill(luatanmi2)
hifuB:addSkill(luatanmi3)
hifuB:addSkill(luatanmi4)
hifuB:addSkill(luabijin)

hifuC:addSkill(luatanmi)
hifuC:addSkill(luatanmi2)
hifuC:addSkill(luatanmi3)
hifuC:addSkill(luatanmi4)
hifuC:addSkill(luabijin)

hifuD:addSkill(luatanmi)
hifuD:addSkill(luatanmi2)
hifuD:addSkill(luatanmi3)
hifuD:addSkill(luatanmi4)
hifuD:addSkill(luabijin)

hifuE:addSkill(luatanmi)
hifuE:addSkill(luatanmi2)
hifuE:addSkill(luatanmi3)
hifuE:addSkill(luatanmi4)
hifuE:addSkill(luabijin)

hifuF:addSkill(luatanmi)
hifuF:addSkill(luatanmi2)
hifuF:addSkill(luatanmi3)
hifuF:addSkill(luatanmi4)
hifuF:addSkill(luabijin)

tongxinCard = sgs.CreateSkillCard{
    name = "luatongxin",
    target_fixed = false,
    will_throw = false,
    filter = function(self, targets, to_select)
        if #targets == 0 then
            if not to_select:isKongcheng() then
                return to_select:objectName() ~= sgs.Self:objectName()
            end
        end
        return false
    end,
    on_use = function(self, room, source, targets)
        local success = source:pindian(targets[1], "luatongxin", self)
        if success then
            if not targets[1]:isNude() then
                local to_throw = room:askForCardChosen(source, targets[1], "he", self:objectName(), false, sgs.Card_MethodDiscard)
                room:throwCard(sgs.Sanguosha:getCard(to_throw), targets[1], source)
            end
        else
            room:setPlayerFlag(targets[1], "luatongxin")
        end
    end
}
luatongxin = sgs.CreateViewAsSkill{
    name = "luatongxin",
    n = 1,
    view_filter = function(self, selected, to_select)
        return not to_select:isEquipped()
    end,
    view_as = function(self, cards)
        if #cards == 1 then
            local daheCard = tongxinCard:clone()
            daheCard:addSubcard(cards[1])
            return daheCard
        end
    end,
    enabled_at_play = function(self, player)
        if not player:hasUsed("#luatongxin") then
            return not player:isKongcheng()
        end
        return false
    end
}
luatongxin2 = sgs.CreateProhibitSkill{
    name = "#luatongxin",
    is_prohibited = function(self, from, to, card)
        return to:hasSkill("luatongxin") and from:getPhase() ~= sgs.Player_NotActive and from:hasFlag("luatongxin")
                and not card:isKindOf("SkillCard")
    end
}

luaxielislashcard = sgs.CreateSkillCard{
    name = "luaxielislash" ,
    target_fixed = true ,
    on_use = function(self, room, source, targets)
        local liufeng = room:findPlayerBySkillName("luaxieli")
        if (not liufeng) or (liufeng:getMark("luaxieli") == 0) then return end
        local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
        dummy:addSubcard(room:getDrawPile():first())
        if source:canSlash(liufeng, dummy, false) then
            dummy:setSkillName("luaxieli")
            room:useCard(sgs.CardUseStruct(dummy, source, liufeng), true)
        end
    end
}
canSlashsang = function(player)
    local sangetsusei
    for _, p in sgs.qlist(player:getSiblings()) do
        if p:isAlive() and p:hasSkill("luaxieli") and p:getMark("luaxieli") > 0 then
            sangetsusei = p
            break
        end
    end
    if not sangetsusei then return false end
    if player:getPhase() == sgs.Player_NotActive then return false end
    local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
    return slash:targetFilter(sgs.PlayerList(), sangetsusei, player)
end
luaxielislash = sgs.CreateViewAsSkill{
    name = "luaxielislash&" ,
    n = 0 ,
    view_as = function()
        return luaxielislashcard:clone()
    end ,
    enabled_at_play = function(self, player)
        return player and sgs.Slash_IsAvailable(player) and canSlashsang(player) and not player:hasUsed("#luaxielislash")
    end ,
    enabled_at_response = function(self, player, pattern)
        return player and (pattern == "slash") and (sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE)
                and canSlashsang(player) and not player:hasUsed("#luaxielislash")
    end ,
}

luaxieliCard = sgs.CreateSkillCard{
    name = "luaxieli" ,
    target_fixed = true ,
    on_use = function(self, room, source, targets)
        if source:isWounded() and source:getHandcardNum() >= source:getMaxCards() then
            room:recover(source, sgs.RecoverStruct(source, nil, 1))
        elseif not source:isWounded() and source:getHandcardNum() < source:getMaxCards() then
            source:drawCards(source:getMaxCards() - source:getHandcardNum(), self:objectName())
        else
            local choice = room:askForChoice(source, "luaxieli", "draw+recover")
            if choice == "draw" then
                source:drawCards(source:getMaxCards() - source:getHandcardNum(), self:objectName())
            else
                room:recover(source, sgs.RecoverStruct(source, nil, 1))
            end
        end
        room:setPlayerMark(source, "luaxieli", 1)
    end

}
luaxieli = sgs.CreateZeroCardViewAsSkill{
    name = "luaxieli",
    view_as = function(self, cards)
        return luaxieliCard:clone()
    end,
    enabled_at_play = function(self, player)
        return player:hasSkill("luaxieli") and (player:isWounded() or player:getHandcardNum() < player:getMaxCards()) and not player:hasUsed("#luaxieli")
    end,
}

luaxieli2 = sgs.CreateTriggerSkill{
    name = "#luaxieli" ,
    global = true,
    events = {sgs.EventPhaseStart, sgs.EventAcquireSkill, sgs.EventLoseSkill} ,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        local sangetsusei = room:findPlayerBySkillName("luaxieli")
        if not sangetsusei then return end
        if ((event == sgs.EventPhaseStart) and (sangetsusei and sangetsusei:isAlive() and sangetsusei:hasSkill(self:objectName())))
                or ((event == sgs.EventAcquireSkill) and (data:toString() == "luaxieli")) then
            if (event == sgs.EventPhaseStart) and player:objectName() == sangetsusei:objectName()
                and sangetsusei:getPhase() == sgs.Player_Start then room:setPlayerMark(sangetsusei, "luaxieli", 0) end
            for _, p in sgs.qlist(room:getOtherPlayers(player)) do
                if not p:hasSkill("luaxielislash") then
                    room:attachSkillToPlayer(p, "luaxielislash")
                end
            end
        elseif (event == sgs.EventLoseSkill) and (data:toString() == "luaxieli") then
            for _ , p in sgs.qlist(room:getOtherPlayers(player)) do
                if p:hasSkill("luaxielislash") then
                    room:detachSkillFromPlayer(p, "luaxielislash", true)
                end
            end
        end
        return false
    end,
    can_trigger = function(self, target)
        return target
    end
}

HappySkillAnjiang:addSkill(luaxielislash)
sangetsusei:addSkill(luaxieli)
sangetsusei:addSkill(luaxieli2)
sangetsusei:addSkill(luatongxin)
sangetsusei:addSkill(luatongxin2)

sangetsuseiA:addSkill(luaxieli)
sangetsuseiA:addSkill(luatongxin)
sangetsuseiA:addSkill(luatongxin2)

sangetsuseiB:addSkill(luaxieli)
sangetsuseiB:addSkill(luatongxin)
sangetsuseiB:addSkill(luatongxin2)

sangetsuseiC:addSkill(luaxieli)
sangetsuseiC:addSkill(luatongxin)
sangetsuseiC:addSkill(luatongxin2)

local function qiangxingshiyongYukari(card, room, Yukari, self)
	if not card:isKindOf("Jink") and not card:isKindOf("Nullification") and not card:isKindOf("sakura") and not card:isKindOf("Collateral") 
		and not (card:isKindOf("Slash") and not Yukari:canSlashWithoutCrossbow()) then 
		local dummy = card
		if Yukari:isCardLimited(dummy, sgs.Card_MethodUse) then  
			return false 
		end 
		local players = sgs.SPlayerList()
		for _, p in sgs.qlist(room:getAllPlayers()) do
			if not Yukari:isProhibited(p, dummy) and not Yukari:isCardLimited(card, sgs.Card_MethodUse)
                and p:hasFlag("luayaowuTarget") then 
				players:append(p)
			end 									
		end 
		
		local Carddata2 = sgs.QVariant() -- ai用
		Carddata2:setValue(dummy)
		room:setTag("luaqucaiTC", Carddata2)				
		local targets = sgs.SPlayerList()
		local target = 1
		if dummy:isKindOf("AOE") or dummy:isKindOf("AmazingGrace") or dummy:isKindOf("GodSalvation") then
			room:setPlayerFlag(Yukari, "qucaiAOE")
			while target and not players:isEmpty() do
				target = room:askForPlayerChosen(Yukari, players, "luayuanli", "luayuanliH", true, false) 
				if target then 
					players:removeOne(target)
					targets:append(target)
					room:setPlayerFlag(target, "qucaiAOEs")
					room:writeToConsole("Hello World! " .. target:getGeneralName())
				end 
			end 
			room:useCard(sgs.CardUseStruct(dummy, Yukari, sgs.SPlayerList()), true)
			for _, p in sgs.qlist(targets) do
				room:setPlayerFlag(p, "-qucaiAOEs") 
			end 
			room:setPlayerFlag(Yukari, "-qucaiAOE")			
			return true 
		elseif dummy:isKindOf("IronChain") then 
			if not players:isEmpty() then
				targets:append(players:at(0))
			end 
			room:useCard(sgs.CardUseStruct(dummy, Yukari, targets), true)
			return true  
		end 
		
		local target = room:askForPlayerChosen(Yukari, players, "luayuanli", "luayuanliH", true, false) 
		if not target then return false end 
		players:removeOne(target)
		targets:append(target)
		room:removeTag("luaqucaiTC")	
		room:useCard(sgs.CardUseStruct(dummy, Yukari, targets), true)
		return true 
	end  
	return false
end 

luayaowu = sgs.CreateTriggerSkill{
	name = "luayaowu",
    global = true,
    priority = -1,
	events = {sgs.CardUsed, sgs.TargetConfirmed},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
        local use = data:toCardUse()
        local card = data:toCardUse().card
		if event == sgs.CardUsed then
			if card and not card:isKindOf("SkillCard") and use.from:objectName() == player:objectName() then 
                local hc = true
                for _,p in sgs.qlist(room:getAlivePlayers()) do
                    if p:getHandcardNum() > player:getHandcardNum() then
                        hc = false
                    end
                end
                if hc and use.to:length() == 1 then 
                    for _, hongrai in sgs.qlist(room:findPlayersBySkillName("luayaowu")) do 
                        if player:objectName() ~= hongrai:objectName() and not hongrai:isKongcheng() then  
                            local target = use.to:at(0)
                            target:setFlags("luayaowuTarget")
                            local card = room:askForCard(hongrai, "TrickCard+^Nullification+^ExtraCollateral,BasicCard+^Jink,EquipCard|.|.|hand",
                             "@luayuanli", sgs.QVariant(), sgs.Card_MethodUse)
                            if card then
                                if not card:cardIsAvailable(hongrai) then 
                                else
                                    if hongrai:isCardLimited(card, sgs.Card_MethodUse) then 
                                    else
                                        local Carddata3 = sgs.QVariant() -- ai用
                                        Carddata3:setValue(card)
                                        room:setCardFlag(card, "luayaowuXS")
                                        hongrai:getRoom():setTag("luaqiuwenTC", Carddata3)
                                        if qiangxingshiyongYukari(card, room, hongrai, self) then
                                            hongrai:getRoom():removeTag("luaqiuwenTC") 
                                        end
                                        room:setCardFlag(card, "-luayaowuXS")
                                        hongrai:getRoom():removeTag("luaqiuwenTC")
                                    end
                                end  
                            end
                            target:setFlags("-luayaowuTarget")
                        end 
                    end 
                end 
			end 
        else
            if use.to:length() ~= 1 then return false end
            local target = use.to:at(0)
            if card and not card:isKindOf("SkillCard") and target:objectName() == player:objectName()
                and not card:isKindOf("EquipCard") then 
                local hc = true
                for _,p in sgs.qlist(room:getAlivePlayers()) do
                    if p:getHandcardNum() < player:getHandcardNum() then
                        hc = false
                    end
                end
                if hc and use.to:length() == 1 then 
                    for _, hongrai in sgs.qlist(room:findPlayersBySkillName("luayaowu")) do
                        if room:askForSkillInvoke(hongrai, "luayaowuX", data) then  
                            if player:getHp() < 0 then 
				                room:addPlayerMark(player, "luayaowuDelay")   
                            else 
                                local x = player:getMark("luayaowuDelay") + 1
                                for i = 1,x do  
                                    player:drawCards(1)
                                    if (hongrai:objectName() ~= player:objectName()) and not player:isKongcheng() and not hongrai:isKongcheng() then 
                                        local card1 = room:askForCard(player, ".|.|.|hand!", "@luayaowugive", sgs.QVariant(), sgs.Card_MethodNone)
                                        local card2 = room:askForCard(hongrai, ".|.|.|hand!", "@luayaowugive", sgs.QVariant(), sgs.Card_MethodNone)
                                        if card1 and card2 then
                                            room:obtainCard(player, card2, false)
                                            room:obtainCard(hongrai, card1, false)
                                        end 
                                    end 
                                end 
                            end 
                        end 
                    end
                end 
            end 
		end
	end,
}

lualuomu = sgs.CreateTriggerSkill{
	name = "lualuomu", 
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damaged},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.Damaged then
			local damage = data:toDamage()
			if damage.card == nil or not (damage.card:isKindOf("Slash") or damage.card:isKindOf("yuzhi")) or damage.to:isDead() then
				return false
			end
			for _, hongrai in sgs.qlist(room:getAllPlayers()) do
				if not hongrai or hongrai:isDead() or not hongrai:hasSkill(self:objectName()) then continue end
                if hongrai:faceUp() then 
                    local x = room:getCurrent():getHandcardNum()
                    local y = hongrai:getHandcardNum()
                    if y > x  then
                        room:askForDiscard(hongrai, "lualuomu", y - x, y - x, false, false, "@lualuomu")
                    end 
                    if y < x  then 
                        hongrai:drawCards(x - y)
						room:setPlayerProperty(player, "kingdom", sgs.QVariant(player:getKingdom()))
                        hongrai:turnOver()
                    end 
                end 
			end
		end
	end,
	can_trigger = function(self, target)
		return target ~= nil
	end
}
lualuomu2 = sgs.CreateTriggerSkill{
	name = "#lualuomu2",
	events = {sgs.TurnOver},
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data, room)
		if event == sgs.TurnOver then 
			if player:hasSkill("lualuomu") then 
				if player:getKingdom() == "luaxi" then 
					room:setPlayerProperty(player, "kingdom", sgs.QVariant("luacai"))
				else
					room:setPlayerProperty(player, "kingdom", sgs.QVariant("luaxi"))
				end
			end 
			return false 
		end
		return false
	end
}
jacket_label:addSkill(luayaowu)
jacket_label:addSkill(lualuomu)
jacket_label:addSkill(lualuomu2)

sgs.LoadTranslationTable {
    ["pay99"] = "两情相悦", --注意这里每次要加逗号
    ["hifu"] = "莲梅莉",
    ["hifuA"] = "莲梅莉",
    ["hifuB"] = "莲梅莉",
    ["hifuC"] = "莲梅莉",
    ["hifuD"] = "莲梅莉",
    ["hifuE"] = "莲梅莉",
    ["hifuF"] = "莲梅莉",
    ["#hifu"] = "秘封俱乐部",
    ["designer:hifu"] = "Paysage",
    ["luatanmi"] = "探秘",
    [":luatanmi"] = "出牌阶段限一次，你可以展示牌堆顶的5张牌并置于牌堆底。直到下次发动“探秘”前，"
        .. "只要你使用的牌依次与此5张牌类型相同，则无距离限制且摸一张牌。<b>锁定技</b>，你于回合内弃置（非重铸）的牌置于牌堆顶。",
    ["luabijin"] = "彼境",
    ["@luabijin"] = "弃置1张手牌或受到1点伤害",
    [":luabijin"] = "出牌阶段限一次，你可以弃置1张红桃或黑桃牌，令一名角色弃置1张手牌或受到1点伤害。",

    ["zhengzhen"] = "正针",
    ["#zhengzhen"] = "革命军",
    ["designer:zhengzhen"] = "Paysage",
    ["luafanji"] = "反击",
    [":luafanji"] = "一名角色的回合结束阶段，若其没有使用牌指定其他角色为目标，你可以令其下个摸牌摸牌数+1，且其下回合使用【杀】造成的伤害+1。",
    ["luageming"] = "革命",
    ["luagemingg"] = "革命",
    [":luageming"] = "限定技，出牌阶段，你可以令任意名角色摸一张牌，直到你的下个回合开始时，其仅一次可以将一张手牌当作【杀】使用（不计入次数限制）。",

    ["sangetsusei"] = "三月精",
    ["sangetsuseiA"] = "三月精",
    ["sangetsuseiB"] = "三月精",
    ["sangetsuseiC"] = "三月精",
    ["#sangetsusei"] = "日月星的塊宝",
    ["designer:sangetsusei"] = "Paysage",
    ["illustrator:sangetsusei"] = "茶葉",
    ["luatongxin"] = "同心",
    [":luatongxin"] = "出牌阶段限一次，你可以与一名其他角色拼点：若你赢，你弃置其一张牌；若你没赢，其于其下个回合内不能使用牌指定你为目标。",
    ["luaxieli"] = "协力",
    ["xieli"] = "协力",
    ["luaxielislash"] = "协力",
    [":luaxielislash"] = "你可以将牌堆顶的一张牌当作【杀】对三月精使用。",
    [":luaxieli"] = "出牌阶段限一次，你可以将手牌补至上限或回复1点体力。直到下个回合开始阶段，其他角色于其出牌阶段最多一次可以将牌堆顶的一张牌当作【杀】对你使用。",
    
    ["fuhuo"] = "复活",
    ["jacket_label"] = "蓬莱人形",
    ["#jacket_label"] = "新与旧的色彩",
    ["designer:jacket_label"] = "Paysage",
    ["illustrator:jacket_label"] = "石川スペアリブ",
    ["luayaowu"] = "邀舞",
    ["luayaowuX"] = "邀舞（摸牌效果）",
    ["@luayaowu"] = "你可以对该目标使用一张手牌。",
    ["@luayaowugive"] = "你需要给出一张手牌。",
    ["~luayaowu"] = "选择一张能对其使用的手牌→点击确定。",
    ["@luayaowu2"] = "你可以与目标各摸一张牌并交换一张手牌。",
    [":luayaowu"] = "手牌数最多的其他角色使用牌对唯一目标结算后，你可以对该目标使用一张手牌；"
        .. "手牌数最少的角色成为非装备牌的唯一目标后，其摸一张牌并与你交换一张手牌。",
    ["lualuomu"] = "落幕",  
    [":lualuomu"] = "锁定技，【杀】/【弹幕】造成伤害后，若你武将牌正面朝上，你须将手牌数调整至与当前回合角色相同；若因此你获得了牌，则你翻面。",
    ["@lualuomu"] = "因“落幕”的效果，你需要调整手牌数",

    ["touhou"] = "只玩东方",
    ["quankuo"] = "都拿上来吧",
    ["zhuanshu"] = "专属",
    ["ChooseJiang"] = "三三选将",
    ["choosejiang"] = "三三选将",
    ["ChooseJiangEight"] = "军八选将",
    ["choosejiangeight"] = "军八选将",
    ["@ChooseJiang"] = "请在接下来展示的角色列表中选择一名角色禁用。",
    ["@ChooseJiang2"] = "请在接下来展示的角色列表中选择你要使用的角色。",
    ["~ChooseJiang"] = "点击确定以继续。",
    ["~ChooseJiang2"] = "点击确定以继续。",
    ["HappySkillAnjiangB"] = "欢乐三威三",
	["change2loyalist"] = "内奸变忠",
    ["@ChooseJiang"] = "请在接下来展示的角色列表中选择一名角色禁用。",
	["chaoscheck"] = "菜单",  --
	["showHero"] = "亮将",
	["showRole"] = "亮身份",
	["showHeroS"] = "亮副将",
	["checkHR"] = "确认身份和武将",
	["surrenderPays"] = "将军走此小道",
	["surrenderPaysX"] = "投降离开",
	["Flandre"] = "发芙兰朵露表情包",
	["cardinal-invoke"] = "请指定一名角色变成反贼，之后你也会变成反贼<br/> <b>操作提示</b>: 选择一名其他角色→点击确定<br/>",
    ["ChooseJiangChaos"] = "混沌模式",
	["ChaosHeroS"] = "亮明副将",
	["ChangeLord"] = "交接主公"

	
	
}

