# QSanguosha-v2 开发文档中心

欢迎来到QSanguosha-v2的完整开发文档中心！这里包含了项目的详细介绍、界面美化指南和武将开发教程。

## 📚 文档目录

### 🎯 [项目介绍与分析](./项目介绍与分析.md)

**全面了解QSanguosha-v2项目**
- 项目概述与技术架构
- 核心功能模块详解
- 扩展包系统分析
- 网络架构与配置系统
- 开发特色与技术亮点
- 学习价值与项目价值

**适合人群**：
- 初次接触QSanguosha-v2的开发者
- 想要了解项目整体架构的技术人员
- 开源项目学习者
- 游戏开发爱好者

---

### 🎨 [界面美化完整指南](./界面美化完整指南.md)
**打造个性化的游戏界面**

#### 第一部分：游戏背景图片更换
- 通过配置文件快速更换背景
- 皮肤系统灵活配置
- 势力背景自动切换
- 自定义背景制作规范

#### 第二部分：按钮图案自定义
- 游戏内按钮图片替换
- 主菜单按钮QSS样式定制
- 完整的按钮状态系统
- 按钮制作规范与工具推荐

#### 第三部分：高级美化技巧
- 自动背景切换设置
- 创建自定义皮肤
- 完整主题制作
- 故障排除与调试

**适合人群**：
- 想要美化游戏界面的玩家
- UI设计爱好者
- 皮肤制作者
- 视觉效果优化人员

---

### ⚔️ [武将开发完整指南](./武将开发完整指南.md)

**从零开始创建自定义武将**

#### 第一部分：开发环境准备

- 文件结构与基本依赖
- 开发工具配置

#### 第二部分：武将创建步骤
- 扩展包创建
- 武将基本属性设置
- 技能类型详解（触发技、视为技、锁定技等）
- 常用技能模板
- 技能触发时机

#### 第三部分：翻译文件
- 翻译文件结构与规范
- 武将信息本地化
- 技能描述与提示信息
- 台词与动画配置

#### 第四部分：AI开发指南
- AI文件结构
- 技能发动判断逻辑
- 价值评估函数
- 武将强度评估

#### 第五部分：完整示例
- 赵云武将完整实现
- 包含代码、翻译、AI的完整示例
- 可直接运行的参考代码

#### 第六部分：高级技能实现
- 多选择技能
- 判定技能
- 距离技能
- 手牌上限技能

#### 第七部分：调试和测试
- 功能测试方法
- 性能优化技巧
- 兼容性测试

#### 第八部分：平衡性设计原则
- 强度控制
- 交互性设计
- 趣味性考虑

**适合人群**：
- 武将设计者
- Lua脚本开发者
- 游戏平衡性设计师
- QSanguosha-v2扩展包制作者

---

### 🏛️ [新势力添加指南](./新势力添加指南.md)
**创建全新的游戏势力**

#### 第一部分：势力系统分析
- 现有势力结构详解
- 势力配置文件分析
- 图片资源文件结构

#### 第二部分：添加新势力步骤
- 配置文件修改方法
- 图片资源准备规范
- 武将创建与势力关联
- 翻译文件设置

#### 第三部分：高级配置
- 势力背景设置
- 势力特殊规则
- 势力互动关系

#### 第四部分：实战示例
- 完整的"仙"势力添加示例
- 包含配置、图片、武将、翻译的完整流程

#### 第五部分：测试和调试
- 功能测试清单
- 常见问题排除
- 进阶技巧和社区分享

**适合人群**：
- 游戏内容创作者
- 势力设计师
- 主题包制作者
- 游戏扩展开发者

---

## 🚀 快速开始

### 新手推荐学习路径

1. **了解项目** → 阅读《项目介绍与分析》
2. **美化界面** → 跟随《界面美化完整指南》实践
3. **开发武将** → 学习《武将开发完整指南》
4. **创建势力** → 参考《新势力添加指南》

### 按需求选择文档

| 你想要... | 推荐文档 | 预计时间 |
|-----------|----------|----------|
| 了解项目整体情况 | 项目介绍与分析 | 30分钟 |
| 更换游戏背景 | 界面美化指南 - 第一部分 | 15分钟 |
| 自定义按钮样式 | 界面美化指南 - 第二部分 | 30分钟 |
| 制作完整主题 | 界面美化指南 - 完整阅读 | 2小时 |
| 创建简单武将 | 武将开发指南 - 前五部分 | 3小时 |
| 开发复杂武将 | 武将开发指南 - 完整阅读 | 6小时 |
| 制作AI逻辑 | 武将开发指南 - 第四部分 | 1小时 |
| 添加新势力 | 新势力添加指南 - 完整阅读 | 2小时 |
| 创建势力主题包 | 新势力添加指南 - 第六部分 | 4小时 |

---

## 🛠️ 开发工具推荐

### 代码编辑
- **Visual Studio Code**: 支持Lua语法高亮
- **Sublime Text**: 轻量级编辑器
- **Notepad++**: Windows平台简单编辑器

### 图像处理
- **Photoshop**: 专业图像处理
- **GIMP**: 免费开源替代
- **Paint.NET**: 轻量级图像编辑

### 调试工具
- **Lua调试器**: 脚本调试
- **Resource Hacker**: Windows资源编辑
- **Qt Creator**: 界面设计工具

---

## 📖 学习资源

### 官方资源
- QSanguosha官方网站
- GitHub开源仓库
- 官方论坛和社区

### 技术文档
- Lua编程语言官方文档
- Qt框架开发文档
- SQLite数据库文档

### 社区资源
- QSanguosha贴吧
- 相关QQ群和微信群
- 技术交流论坛

---

## 🤝 贡献指南

### 如何贡献
1. **发现问题**：在使用过程中发现文档错误或不足
2. **提出建议**：对文档内容或结构的改进建议
3. **分享经验**：分享你的开发经验和技巧
4. **完善示例**：提供更多实用的代码示例

### 贡献方式
- 提交Issue报告问题
- 提交Pull Request改进文档
- 在社区分享你的作品
- 帮助其他开发者解决问题

---

## 📞 获取帮助

### 常见问题
1. **Q**: 如何开始第一个武将开发？
   **A**: 建议先阅读《项目介绍与分析》了解基础，然后跟随《武将开发完整指南》的示例代码开始。

2. **Q**: 界面美化后游戏无法启动怎么办？
   **A**: 检查《界面美化完整指南》中的故障排除部分，通常是配置文件语法错误或图片路径问题。

3. **Q**: 武将技能不生效怎么调试？
   **A**: 参考《武将开发完整指南》第七部分的调试方法，添加调试信息定位问题。

### 技术支持

- 查看文档中的故障排除部分
- 在相关社区提问
- 参考官方示例代码
- 与其他开发者交流经验

---

## 📝 版权声明

本文档集合基于QSanguosha-v2开源项目编写，遵循开源协议。

- **文档作者**: QSanguosha开发团队
- **最后更新**: 2024年
- **文档版本**: v1.0
- **适用版本**: QSanguosha-v2-20190208

---

## 🎉 致谢

感谢QSanguosha-v2开源项目的所有贡献者，感谢社区中分享经验和技巧的开发者们，正是大家的努力让这个项目变得更加完善和易用。

希望这些文档能够帮助你在QSanguosha-v2的开发之路上走得更远！

---

**开始你的QSanguosha-v2开发之旅吧！** 🚀
