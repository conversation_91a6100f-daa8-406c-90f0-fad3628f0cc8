
extension_pay_g = sgs.Package("pay7")

ceshisd = sgs.General(extension_pay_g,"ceshisd","god",4,false,true,true)

kagerou = sgs.General(extension_pay_g,"kagerou","luaxing",0,false,true,false)

rh_flandre = sgs.General(extension_pay_g,"rh_flandre","luahong",1,false,false,false)
rh_flandreA = sgs.General(extension_pay_g,"rh_flandreA","luahong",1,false,true,true)
rh_flandreB = sgs.General(extension_pay_g,"rh_flandreB","luahong",1,false,true,true)
rh_flandreC = sgs.General(extension_pay_g,"rh_flandreC","luahong",1,false,true,true)
rh_flandreD = sgs.General(extension_pay_g,"rh_flandreD","luahong",1,false,true,true)
rh_flandreE = sgs.General(extension_pay_g,"rh_flandreE","luahong",1,false,true,true)
rh_flandreF = sgs.General(extension_pay_g,"rh_flandreF","luahong",1,false,true,true)
rh_flandreG = sgs.General(extension_pay_g,"rh_flandreG","luahong",1,false,true,true) 

mai_satono = sgs.General(extension_pay_g,"mai_satono","luacai",2,false,true,false)

rh_erin = sgs.General(extension_pay_g,"rh_erin","luayue",3, false, false, false)

sakuya = sgs.General(extension_pay_g,"sakuya","luahong",4,false,false,false)
sakuyaA = sgs.General(extension_pay_g,"sakuyaA","luahong",4,false,true,true)
sakuyaB = sgs.General(extension_pay_g,"sakuyaB","luahong",4,false,true,true)
sakuyaC = sgs.General(extension_pay_g,"sakuyaC","luahong",4,false,true,true)
sakuyaD = sgs.General(extension_pay_g,"sakuyaD","luahong",4,false,true,true)
sakuyaO = sgs.General(extension_pay_g,"sakuyaO","luahong",4,false,true,true)

mihana = sgs.General(extension_pay_g,"mihana$","god",5, false, false, false, 2)

taotei = sgs.General(extension_pay_g,"taotei","luadi",6,false,false,false)
Rh_junko = sgs.General(extension_pay_g,"Rh_junko","god",7,false,false,false)
Rh_junkoA = sgs.General(extension_pay_g,"Rh_junkoA","god",7,false,true,true)
Rh_junkoB = sgs.General(extension_pay_g,"Rh_junkoB","god",7,false,true,true)


sp_akyuu = sgs.General(extension_pay_g,"sp_akyuu","luadi",10,false,false,false,0) 
sp_akyuuB = sgs.General(extension_pay_g,"sp_akyuuB","luadi",10,false,true,true,3)


grani = sgs.General(extension_pay_g,"grani","qun",4,false,true,true)
skadi = sgs.General(extension_pay_g,"skadi$","qun",4,false,true,true)
okina = sgs.General(extension_pay_g,"okina$","god",5,false,true,true)


function dealMark(room, player, reCheck)
	local Role = "rebel"
	Role = player:getTag("ChaosRole"):toString()
	if player:getMark("@unknown") >= 1 then 
		room:setPlayerMark(player, "@unknown", 0)
		if Role == "rebel" then
			room:setPlayerMark(player, "@rebel", 1)
			room:setEmotion(player, "rebel")
		elseif Role == "loyalist" then
			room:setPlayerMark(player, "@loyalist", 1)
			room:setEmotion(player, "loyalist")
		elseif Role == "renegade" then
			room:setPlayerMark(player, "@renegade", 1)
			room:setEmotion(player, "renegade")
		elseif Role == "civilian" then
			room:setPlayerMark(player, "@civilian", 1)
			room:setEmotion(player, "civilian")
		elseif Role == "chaosX" then
			room:setPlayerMark(player, "@chaosX", 1)
			room:setEmotion(player, "chaosX")
		elseif Role == "cardinal" then
			room:setPlayerMark(player, "@cardinal", 1)
			room:setEmotion(player, "cardinal")
		end  		
	end 
	if reCheck then
		PAYchaoscheck(room, player)
	end 
	room:getThread():delay(1500)
	return Role
end 

function CheckWin(room)  
	local lords = {}
	local lordisAlive = false
	local Lord
	for _,p in sgs.qlist(room:getAllPlayers(true)) do
		if p:getRole() == "lord" then
			Lord = p
		end 
	end 
	local loyalists = {}
	local renegades = {}
	local civilians = {}
	local rebels = {} 
	for _, p2 in sgs.qlist(room:getAlivePlayers()) do
        if p2:getHp() > 0 then
            if p2:getTag("ChaosRole"):toString() == "renegade" then
				table.insert(renegades, p2:objectName()) 
            elseif p2:getRole() == "lord" then
				lordisAlive = true 
				table.insert(lords, p2:objectName()) 
            elseif p2:getTag("ChaosRole"):toString() == "loyalist" then
				table.insert(lords, p2:objectName()) 
				table.insert(loyalists, p2:objectName()) 
            elseif p2:getTag("ChaosRole"):toString() == "rebel" then
				table.insert(rebels, p2:objectName()) 
            elseif p2:getTag("ChaosRole"):toString() == "civilian" then
				table.insert(civilians, p2:objectName())  
            elseif p2:getTag("ChaosRole"):toString() == "chaosX" then
				table.insert(renegades, p2:objectName())  
            elseif p2:getTag("ChaosRole"):toString() == "cardinal" then
				table.insert(rebels, p2:objectName())  
            end
        end
	end
	local function showAll(reason)
		room:writeToConsole(debug.traceback())
		local rebelWin = 1
		local lordWin = 2
		local renegadeWin = 3
		for _, player in sgs.qlist(room:getAlivePlayers()) do
			room:setPlayerMark(player, "@unknown", 0)
			local Role = player:getTag("ChaosRole"):toString()
			if Role == "rebel" then
				room:setPlayerMark(player, "@rebel", 1)
				player:setRole("rebel") 
				player:setShownRole(true)
				room:notifyProperty(player, player, "role")		
				room:setEmotion(player, "rebel")		
			elseif Role == "loyalist" then
				room:setPlayerMark(player, "@loyalist", 1)
				player:setRole("loyalist") 
				player:setShownRole(true)
				room:notifyProperty(player, player, "role")	
				room:setEmotion(player, "loyalist")			
			elseif Role == "renegade" then
				room:setPlayerMark(player, "@renegade", 1)
				player:setRole("renegade") 
				player:setShownRole(true)
				room:notifyProperty(player, player, "role")		
				room:setEmotion(player, "renegade")			
			elseif Role == "chaosX" then
				room:setPlayerMark(player, "@chaosX", 1)
				player:setRole("renegade") 
				player:setShownRole(true)
				room:notifyProperty(player, player, "role")		
				room:setEmotion(player, "chaosX")			
			elseif Role == "civilian" then
				room:setPlayerMark(player, "@civilian", 1)
				room:setEmotion(player, "civilian")			
				if reason == rebelWin then
					player:setRole("rebel") 
					player:setShownRole(true)
					room:notifyProperty(player, player, "role")		
				elseif reason == lordWin then
					player:setRole("loyalist") 
					player:setShownRole(true)
					room:notifyProperty(player, player, "role")		
				elseif reason == renegadeWin then
					player:setRole("renegade") 
					player:setShownRole(true)
					room:notifyProperty(player, player, "role")		 
				end 
			elseif Role == "cardinal" then
				room:setPlayerMark(player, "@cardinal", 1)
				room:setEmotion(player, "cardinal")		
				player:setRole("rebel") 
				player:setShownRole(true)
				room:notifyProperty(player, player, "role")			 
			end  			
		end 
		room:getThread():delay(2500)	
	end 
	if #renegades == 0 and #rebels == 0 then
		showAll(2)
		PayWin(room, lords)  
		return
	end 	 
	if not lordisAlive then
		room:writeToConsole("gameover test 1713")  
		if #rebels == 0 then
			if #renegades == 1 then 
				showAll(3)
				room:gameOver(renegades[1])
				return
			else
				local rp
				for _,p in sgs.qlist(room:getAllPlayers(true)) do
					if not p:isAlive() and p:getRole() == "rebel" then
						rp = p
						break
					end
				end
				if rp then --反贼胜利
					showAll(1)
					room:setPlayerProperty(rp, "maxhp", sgs.QVariant(1))
					room:setPlayerProperty(rp, "hp", sgs.QVariant(1))
					room:revivePlayer(rp, true)		
					table.insert(civilians, rp:objectName())
					PayWin(room, civilians)	
					return
				else
					showAll(2)
					room:setPlayerProperty(Lord, "maxhp", sgs.QVariant(1))
					room:setPlayerProperty(Lord, "hp", sgs.QVariant(1))
					--room:revivePlayer(Lord, true)		
					table.insert(lords, Lord:objectName())
					PayWin(room, lords)	
					return
				end 
			end 
		end 
		room:writeToConsole("gameover test 1745")  
		showAll()
	end 
end 
local function isFriendQ(room, target, player)
	if player:getRole() == "rebel" then
		return target:getRole() == "rebel"
	end
	if player:getRole() == "loyalist" or player:getRole() == "lord" then
		return target:getRole() == "loyalist" or target:getRole() == "lord"
	end
end
LuaWeishi = sgs.CreateTriggerSkill{
	name = "LuaWeishi",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardUsed, sgs.CardResponded},
	on_trigger = function(self, event, player, data)
		if event == sgs.CardUsed then
			local use = data:toCardUse()
			local card = use.card
			if not card then return end 
			if card:isKindOf("SkillCard") then return end 
			local y = player:getLostHp()
			local x = player:getHp()
			local room = player:getRoom()
			if use.from:objectName() == player:objectName() and card:getNumber() and use.from:hasSkill(self:objectName()) then
				if card:getNumber() > (2*x) and (player:getHandcardNum() <= player:getMaxHp()) then
					if y == 0 then 
						room:setPlayerProperty(player, "maxhp", sgs.QVariant(player:getMaxHp() + 1))
					else
						local choice = room:askForChoice(player, "LuaWeishi", "hp+maxhp", data)
						if choice == "hp" then
							recover = sgs.RecoverStruct()
							recover.who = player
							room:recover(player, recover)
						else
							room:setPlayerProperty(player, "maxhp", sgs.QVariant(player:getMaxHp() + 1))
						end 					
					end 
				elseif card:getNumber() <= (2*x) and (player:getHandcardNum() <= player:getMaxHp()) then
					local function canLoseHp()
						for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
							if hecatiaX and isFriendQ(room, player, hecatiaX) and player:objectName() ~= hecatiaX:objectName()
									and player:getHp() == hecatiaX:getHp() then
								room:notifySkillInvoked(hecatiaX, "luayiti")
								return false
							end 
						end 
						for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
							if Erin and Erin:getKingdom() == player:getKingdom() then
								room:notifySkillInvoked(Erin, "luajiance")
								return false
							end 
						end 
						return true
					end 
					if canLoseHp() then room:loseHp(player) end 
				end
			end 
		end
		if event == sgs.CardResponded then
			local resp = data:toCardResponse()
			local card = resp.m_card
			
			local y = player:getLostHp()
			local x = player:getHp()
			local room = player:getRoom()
			local half = math.floor(y/2)			
			if card:getNumber() then
				if card:getNumber() > (2*x) and (player:getHandcardNum() <= player:getMaxHp()) then
					if y == 0 then 
						room:setPlayerProperty(player, "maxhp", sgs.QVariant(player:getMaxHp() + 1))
					else
						local choice = room:askForChoice(player, "LuaWeishi", "hp+maxhp", data)
						if choice == "hp" then
							recover = sgs.RecoverStruct()
							recover.who = player
							room:recover(player, recover)
						else
							room:setPlayerProperty(player, "maxhp", sgs.QVariant(player:getMaxHp() + 1))
						end 					
					end 
				elseif card:getNumber() <= (2*x) and (player:getHandcardNum() <= player:getMaxHp()) then
					local function canLoseHp()
						for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
							if hecatiaX and isFriendQ(room, player, hecatiaX) and player:objectName() ~= hecatiaX:objectName()
									and player:getHp() == hecatiaX:getHp() then
								room:notifySkillInvoked(hecatiaX, "luayiti")
								return false
							end 
						end 
						for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
							if Erin and Erin:getKingdom() == player:getKingdom() then
								room:notifySkillInvoked(Erin, "luajiance")
								return false
							end 
						end 
						return true
					end 
					if canLoseHp() then room:loseHp(player) end 
				end
			end
		end 
		return false
	end,
	can_trigger = function(self, target)
		if target then
			if target:isAlive() and target:hasSkill(self:objectName()) then
				return true
			end 
		end 
		return false
	end 
}
LuaChunguang = sgs.CreateTriggerSkill{
	name = "LuaChunguang",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.PreCardUsed} ,
	on_trigger = function(self, event, junko, data)
		if event == sgs.PreCardUsed then
			local function YouMuCheck(card, target)
				if card:isKindOf("Hui") or card:isKindOf("Ofuda") then
					return true
				elseif card:isKindOf("FaithCollection") then
					return not target:isNude()
				elseif card:isKindOf("Banquet") then
					return not target:containsTrick("banquet")
				end
			end
			local use = data:toCardUse()
			local card = use.card
			local y = junko:getLostHp()
			local x = junko:getHp()
			local room = junko:getRoom()
			local half = math.floor(y/2)
			if use.from:objectName() == junko:objectName() and (use.card:isNDTrick() or use.card:isKindOf("BasicCard")) and card:getNumber()
					and card:getNumber() <= (2*y) and use.from:hasSkill(self:objectName()) and card:getNumber() ~= 0 then
				if (sgs.Sanguosha:getCurrentCardUseReason() ~= sgs.CardUseStruct_CARD_USE_REASON_PLAY) then return false end
				local available_targets = sgs.SPlayerList()
				local choices = {}
				if (not use.card:isKindOf("AOE")) and (not use.card:isKindOf("GlobalEffect")) then
					room:setPlayerFlag(junko, "LuaChunguangExtraTarget")
					for _, p in sgs.qlist(room:getAlivePlayers()) do
						if (use.to:contains(p) or room:isProhibited(junko, p, use.card)) then continue end
						if (use.card:targetFixed()) then
							if (not use.card:isKindOf("Peach")) or (p:isWounded()) then
								available_targets:append(p)
							end
						else
							if (use.card:targetFilter(sgs.PlayerList(), p, junko)  or YouMuCheck(use.card, p)) then
								available_targets:append(p)
							end
						end
					end
					room:setPlayerFlag(junko, "-LuaChunguangExtraTarget")
				end
				if (use.to:length() > 1) then table.insert(choices, 1, "remove") end	
				if (not available_targets:isEmpty()) and (half > 0) then table.insert(choices, 1, "add") end
				if #choices == 0 then return false end
				local choice = room:askForChoice(junko, "LuaChunguang", table.concat(choices, "+"), data)
				if (choice == "add") then
					local extra
					if not use.card:isKindOf("Collateral") then
						local count = 1
						local Carddata2 = sgs.QVariant() -- ai用
						Carddata2:setValue(use.card)
						room:setTag("LuaChunguangTC", Carddata2)
						while (not available_targets:isEmpty()) and (count <= half) do
							extra = room:askForPlayerChosen(junko, available_targets, "LuaChunguang", "@Chunguang-add:::" .. use.card:objectName(), true)
							if not extra then break end
							--room:writeToConsole("纯狐测试"..extra:objectName())
							count = count + 1
							use.to:append(extra)
							local available_targets_0 = sgs.SPlayerList()
							for _, p in sgs.qlist(available_targets) do
								if p:objectName() ~= extra:objectName() then
									available_targets_0:append(p)
								end
							end 
							available_targets = available_targets_0
						end
						room:removeTag("LuaChunguangTC")
					end
					room:sortByActionOrder(use.to)
				elseif (choice == "remove") then 
					local count = 1
					local Carddata2 = sgs.QVariant() -- ai用
					Carddata2:setValue(card)
					room:setTag("LuaChunguangTC", Carddata2)
					while not use.to:isEmpty() and (count <= half) do
						local removed = room:askForPlayerChosen(junko, use.to, "LuaChunguang2", "@Chunguang-remove:::" .. use.card:objectName(), true)
						if not removed then break end
						use.to:removeOne(removed)
						count = count + 1
					end
				end
				room:loseMaxHp(junko,half)
				data:setValue(use)
				return false	
			end 
		end
	end
}
Rh_junko:addSkill(LuaWeishi)
Rh_junko:addSkill(LuaChunguang)

Rh_junkoA:addSkill(LuaWeishi)
Rh_junkoA:addSkill(LuaChunguang)

Rh_junkoB:addSkill(LuaWeishi)
Rh_junkoB:addSkill(LuaChunguang)


luazhuwu = sgs.CreateTriggerSkill{
	name = "luazhuwu" ,
	events = {sgs.EventPhaseStart, sgs.Damage } ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart then
			if player:getPhase() ~= sgs.Player_Start then
				return false
			end
		end
		if event == sgs.Damage then
			if player:objectName() ~= data:toDamage().from:objectName() then
				return false
			end
			local damage = data:toDamage()
			if damage.card and damage.card:isKindOf("AOE") then
				return false
			end
		end
		local olayer = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "luazhuwu", true, true)
		if olayer then
			local room2 = olayer:getRoom()
			local xd = olayer:getHp() + 1
			room:setPlayerProperty(olayer, "hp", sgs.QVariant(xd))
			local thread = olayer:getRoom():getThread()
			thread:trigger(sgs.HpChanged, olayer:getRoom(), olayer)
			room:setPlayerMark(olayer, "@zhuwu", 1)
			local playerdata = sgs.QVariant() -- ai用
			playerdata:setValue(player)
			room2:setTag("maisatono", playerdata)

		end
		--end
	end,
}

luazhuwu3 = sgs.CreateTriggerSkill{
	name = "#luazhuwu3",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageForseen},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.DamageForseen then
            local damage = data:toDamage()
			if damage.to and (damage.to:getMark("@zhuwu") > 0) then
				damage.damage = damage.damage + 1
				room:notifySkillInvoked(damage.to, "luazhuwu")
				damage.to:loseAllMarks("@zhuwu")
				data:setValue(damage)
				return false
			end 
		end 
	end 
}
local function qiangxingshiyongMai(card, room, Yukari, target)
	if not card:isKindOf("Jink") and not card:isKindOf("Nullification") and not card:isKindOf("sakura") and not card:isKindOf("Collateral")
		and not (card:isKindOf("Peach") and not target:isWounded()) then 
		local dummy = card
		if Yukari:isCardLimited(dummy, sgs.Card_MethodUse) then  
			return false 
		end 
		local players = sgs.SPlayerList()
		players:append(target)
		
		local Carddata2 = sgs.QVariant() -- ai用
		Carddata2:setValue(dummy)
		room:setTag("luaqucaiTC", Carddata2)		 
		if dummy:isKindOf("AOE") or dummy:isKindOf("AmazingGrace") or dummy:isKindOf("GodSalvation") then
			room:setPlayerFlag(Yukari, "qucaiAOE")
			room:setPlayerFlag(target, "qucaiAOEs")
			room:useCard(sgs.CardUseStruct(dummy, Yukari, sgs.SPlayerList()), true)
			room:setPlayerFlag(target, "-qucaiAOEs") 
			room:setPlayerFlag(Yukari, "-qucaiAOE")			
			return true 
		elseif dummy:isKindOf("IronChain") then  
			room:useCard(sgs.CardUseStruct(dummy, Yukari, players), true)
			return true  
		end 
		 
		room:removeTag("luaqucaiTC")	
		room:useCard(sgs.CardUseStruct(dummy, Yukari, players), true)
		return true 
	end  
	return false
end 
luakuangwang = sgs.CreateTriggerSkill{
	frequency = sgs.Skill_NotFrequent, --打钩发动            
	name = "luakuangwang",
	events = {sgs.DrawNCards},                 --摸牌阶段发动
	on_trigger = function(self, event, player, data)        
		local room = player:getRoom()                        
		if player:hasSkill("luakuangwang") and room:askForSkillInvoke(player, "luakuangwang") then
			local n = player:getHp()
			player:drawCards(n)
			room:setPlayerMark(player, "kuangwang", n)
			local targets = sgs.SPlayerList()
			local lord = room:getLord()
			local kp = lord
			targets:append(lord)
			for i = 1, room:getAlivePlayers():length() - 1 do
				kp = kp:getNextAlive()
				targets:append(kp)
			end
			n = n + 1
			while n > 0 do
				for _,p in sgs.qlist(targets) do
					local plist = sgs.SPlayerList()
					plist:append(p)
					local olayer = room:askForPlayerChosen(player, plist, self:objectName(), "luakuangwang2", true, false)
					if olayer then
						local _data = sgs.QVariant()
						_data:setValue(p)
						room:setPlayerMark(player, "luakuangwangai", n)
						local card_h = room:askForCard(player, ".", "@luakuangwang", _data, sgs.Card_MethodNone)
						if card_h then
							room:setPlayerMark(player, "luakuangwangai", 0) 
							local ap = sgs.SPlayerList()
							ap:append(p)
							qiangxingshiyongMai(card_h, room, player, olayer)
						end
					end
					n = n - 1
					if n <= 0 then break end
				end
			end 
			data:setValue(0)
			return false
       end
   end
}

luajiawei = sgs.CreateTriggerSkill{
	name = "luajiawei",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageForseen},
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local room = player:getRoom()
		if event == sgs.DamageForseen then
			if damage.to and (damage.to:hasSkill("luajiawei")) then
				if player:objectName() ~= damage.to:objectName() then return false end
				local zuiduo = true
				for _, ap in sgs.qlist(room:getOtherPlayers(player)) do
					if player:getHp() <= ap:getHp() then zuiduo = false end
				end
				if player:getHp() > room:getLord():getMark("@clock_time") and not zuiduo then
					damage.damage = damage.damage - 1
					room:notifySkillInvoked(damage.to, "luajiawei")
					data:setValue(damage)
				end
			end
			return false
		end
	end
}
luazhuwu2 = sgs.CreateTriggerSkill{
	name = "#luazhuwu2",
	global = true,
	priority = 10,
	events = {sgs.EventPhaseStart, sgs.HpRecover, sgs.HpChanged, sgs.Damage, sgs.EnterDying, sgs.MaxHpChanged },
	on_trigger = function(self, event, player, data, room)
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			local y = p:getHp() - p:getMaxHp()
			if y >= 0 then 
				room:setPlayerMark(p, "@canji", y) 
			else
				if p:getMark("@canji") > 0 then 
					room:setPlayerMark(p, "@canji", 0)
				end 
			end
		end
	end

}
mai_satono:addSkill(luazhuwu)
mai_satono:addSkill(luazhuwu2)
mai_satono:addSkill(luazhuwu3)
mai_satono:addSkill(luajiawei)
mai_satono:addSkill(luakuangwang)

luashiji = sgs.CreateTriggerSkill{
	name = "luashiji",
	global = true,
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.BeforeCardsMove, sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data)		
		local room = player:getRoom()
		if room:getTag("FirstRound"):toBool() then return end
		local function compID(cardids, id)		
			-- for _,ids in ipairs(cardids) do
				-- room:writeToConsole("咲夜测试" .. ids)
			-- end 
			for _,ids in ipairs(cardids) do
				
				if tonumber(ids) == id then return true end 
			end 
			return false 
		end 		
		local move = data:toMoveOneTime()
		if not (move.from_places:contains(sgs.Player_PlaceHand) or move.from_places:contains(sgs.Player_PlaceEquip)) then return false end 
		if not move.to_place == sgs.Player_DiscardPile then return false end 
		if player:getPhase() == sgs.Player_NotActive then return false end 
		if event == sgs.BeforeCardsMove then
			local ids = {}
			local card
			local i = 0	
			local pay = 0
			for _,id in sgs.qlist(move.card_ids) do
				card = sgs.Sanguosha:getCard(id)
				if ((move.from_places:at(i) == sgs.Player_PlaceHand) or (move.from_places:at(i) == sgs.Player_PlaceEquip))
					and (room:getCardOwner(id) and room:getCardOwner(id):objectName() == player:objectName())
						and ((move.to_place == sgs.Player_DiscardPile) or (move.to_place == sgs.Player_PlaceTable)) then
					if card then
						if pay == 0 then player:addMark("luashiji"); pay = pay + 1 end 		
						--room:writeToConsole("咲夜测试" .. id .. "  " .. player:objectName() .. "  " .. player:getMark("luashiji"))
						table.insert(ids,id)
					end
				end
				i = i + 1
			end
			player:getRoom():setTag("luashiji", sgs.QVariant(table.concat(ids, "+")))
		else
			if player:getMark("luashiji") == 1 and player:getRoom():getTag("luashiji") then
				for _, sakuya in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
					local room_0 = sakuya:getRoom() 
					
					local playerlist = sgs.SPlayerList()
					for _, p in sgs.qlist(room:getAlivePlayers()) do
						if p:getRoom():getTag("luashiji") and p:getMark("luashiji") == 1 then 							
							local ids = p:getRoom():getTag("luashiji"):toString()
							if ids == "" then return false end 
							ids = ids:split("+")
							local card
							local i = 0				
							for _,id in sgs.qlist(move.card_ids) do
								card = sgs.Sanguosha:getCard(id)
								if ((move.from_places:at(i) == sgs.Player_PlaceHand) or (move.from_places:at(i) == sgs.Player_PlaceEquip))
									and compID(ids, id) and p:getPhase() ~= sgs.Player_NotActive then
									if card then
										playerlist:append(p)		
									end
								end
								i = i + 1
							end										
						end 
					end 
					if playerlist:isEmpty() then return false end 
					local playerdata = sgs.QVariant() -- ai用
					playerdata:setValue(playerlist:at(0))
					room_0:writeToConsole("luashijip  " .. playerlist:at(0):objectName())
					sakuya:getRoom():setTag("luashijip", playerdata)	
					
					if (sakuya:getHp() > 0) and ((move.to_place == sgs.Player_DiscardPile) or (move.to_place == sgs.Player_PlaceTable)) and room_0:askForSkillInvoke(sakuya, self:objectName(), data) then
						sakuya:getRoom():removeTag("luashijip")	
						if playerlist:length() > 0 then 
							local Remilia = room:askForPlayerChosen(sakuya, playerlist, self:objectName(), "@luashiji")	
							if Remilia then 								
								local old_card_ids = {}
								for _,card_idX in sgs.qlist(move.card_ids) do	
									table.insert(old_card_ids, card_idX)
								end 			
								sakuya:getRoom():setTag("luashijia", sgs.QVariant(table.concat(old_card_ids, "+")))
								local ids = Remilia:getRoom():getTag("luashiji"):toString()
								ids = ids:split("+")
								local card
								local i = 0				
								local loose = false
								for _, id in ipairs(old_card_ids) do		
									card = sgs.Sanguosha:getCard(id)
									if ((move.from_places:at(i) == sgs.Player_PlaceHand) or (move.from_places:at(i) == sgs.Player_PlaceEquip))
										and compID(ids, id) then
										if card then
											loose = true
											room:obtainCard(Remilia, card, false)
											move.card_ids:removeOne(id)
											move.from_places:removeAt(i)											
										end
									end
									i = i + 1
								end
								local function canLoseHp()
									for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
										if hecatiaX and isFriendQ(room, sakuya, hecatiaX) and sakuya:objectName() ~= hecatiaX:objectName()
												and sakuya:getHp() == hecatiaX:getHp() then
											room:notifySkillInvoked(hecatiaX, "luayiti")
											return false
										end 
									end 
									for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
										if Erin and Erin:getKingdom() == sakuya:getKingdom() then
											room:notifySkillInvoked(Erin, "luajiance")
											return false
										end 
									end 
									return true
								end 
								if canLoseHp() then room:loseHp(sakuya) end
								sakuya:getRoom():removeTag("luashijia")									
							end 
						end 
					end 
					room:removeTag("luashijip")	
				end 
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					p:getRoom():removeTag("luashiji")
				end 	
				player:addMark("luashiji")
			end 
			
			data:setValue(move)
		end 
	end 
}

luashiji2 = sgs.CreateTriggerSkill{
	name = "#luashiji",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data)
		if data:toPhaseChange().to == sgs.Player_NotActive and player:getMark("luashiji") > 0 then
			local room = player:getRoom()
			player:setMark("luashiji", 0)
		end 
	end 
}

luaxiaosa = sgs.CreateTriggerSkill{
	name = "luaxiaosa" ,
	frequency = sgs.Skill_Frequent, 
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local change = data:toPhaseChange()
		if change.to == sgs.Player_Discard and room:askForSkillInvoke(player, self:objectName()) then
			local x = player:getHandcardNum()
			if x < 3 then 
				player:drawCards(3-x)
			elseif x > 3 then 
				room:askForDiscard(player, self:objectName(), x-3, x-3, false, true)
			end 
			player:skip(change.to)
		end
		return false
	end
}


luashijie = sgs.CreateTriggerSkill{
	name = "luashijie", 
	events = {sgs.HpChanged}, 
	frequency = sgs.Skill_Compulsory, 
	on_trigger = function(self, event, player, data, room)
		if event == sgs.HpChanged then
			if player:getHp() <= 1 then 
				if not player:hasSkill("luaxiaosa") then room:acquireSkill(player, "luaxiaosa") end 
				if not player:hasSkill("luaqishu") then room:acquireSkill(player, "luaqishu") end 
			else
				if player:hasSkill("luaxiaosa") then room:detachSkillFromPlayer(player, "luaxiaosa") end 
				if player:hasSkill("luaqishu") then room:detachSkillFromPlayer(player, "luaqishu") end 
				room:setPlayerMark(player, "@qishublack", 0)
				room:setPlayerMark(player, "@qishured", 0)
			end 
		end
	end, 
	can_trigger = function(self, target)
		return target and target:hasSkill("luashijie") 
	end
}

luaweixinsdf = sgs.CreateTriggerSkill{
	name = "luaweixinsdf",
	frequency = sgs.Skill_Limited,
	events = {sgs.GameStart},
	on_trigger = function()
	end
}

luaqishu = sgs.CreateViewAsSkill{
	name = "luaqishu" ,
	n = 1 ,
	view_filter = function(self, selected, to_select)
		if #selected > 0 then return false end
		local card = to_select
		local usereason = sgs.Sanguosha:getCurrentCardUseReason()
		if usereason == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			if (sgs.Self:getMark("@qishured") > 0) or ((sgs.Self:getMark("@qishured") == 0) and (sgs.Self:getMark("@qishublack") == 0)) then return card:isRed() end
			if (sgs.Self:getMark("@qishublack") > 0) then return card:isBlack() end
		elseif (usereason == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE) or (usereason == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE) then
			local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
			if pattern == "slash" and ((sgs.Self:getMark("@qishured") > 0) or ((sgs.Self:getMark("@qishured") == 0) and (sgs.Self:getMark("@qishublack") == 0))) then
				return card:isRed()
			elseif pattern == "jink" and ((sgs.Self:getMark("@qishured") > 0) or ((sgs.Self:getMark("@qishured") == 0) and (sgs.Self:getMark("@qishublack") == 0))) then
				return card:isRed() 
			elseif pattern == "nullification" and (sgs.Self:getMark("@qishublack") > 0) then
				return card:isBlack() 
			elseif string.find(pattern, "analeptic") and (sgs.Self:getMark("@qishublack") > 0) then
				return card:isBlack() 
			end
		else
			return false
		end
	end ,
	view_as = function(self, cards)
		if #cards ~= 1 then return nil end
		local originalCard = cards[1]
		local usereason = sgs.Sanguosha:getCurrentCardUseReason()
		if usereason == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			if (sgs.Self:getMark("@qishured") > 0) or ((sgs.Self:getMark("@qishured") == 0) and (sgs.Self:getMark("@qishublack") == 0)) then
				local slash = sgs.Sanguosha:cloneCard("slash", originalCard:getSuit(), originalCard:getNumber())
				slash:addSubcard(originalCard)
				slash:setSkillName(self:objectName())
				return slash
			elseif (sgs.Self:getMark("@qishublack") > 0) then
				local analeptic = sgs.Sanguosha:cloneCard("analeptic", originalCard:getSuit(), originalCard:getNumber())
				analeptic:addSubcard(originalCard)
				analeptic:setSkillName(self:objectName())
				return analeptic			
			end 
		elseif sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE then  
			local n = sgs.Self:getMark("luaqishu")
			if (not n) or n == 0 then n = 3 end 
			if (n == 3) and ((sgs.Self:getMark("@qishured") > 0) or ((sgs.Self:getMark("@qishured") == 0) and (sgs.Self:getMark("@qishublack") == 0))) then
				local slash = sgs.Sanguosha:cloneCard("slash", originalCard:getSuit(), originalCard:getNumber())
				slash:addSubcard(originalCard)
				slash:setSkillName(self:objectName())
				return slash
			elseif (n == 1) and ((sgs.Self:getMark("@qishured") > 0) or ((sgs.Self:getMark("@qishured") == 0) and (sgs.Self:getMark("@qishublack") == 0))) then
				local jink = sgs.Sanguosha:cloneCard("jink", originalCard:getSuit(), originalCard:getNumber())
				jink:addSubcard(originalCard)
				jink:setSkillName(self:objectName())
				return jink
			elseif (n == 2) and (sgs.Self:getMark("@qishublack") > 0) and sgs.Self:getHp() > 0 then
				local ncard = sgs.Sanguosha:cloneCard("nullification", originalCard:getSuit(), originalCard:getNumber())
				ncard:addSubcard(originalCard)
				ncard:setSkillName(self:objectName()) --sgs.Self:getMark("Luameiyin")
				return ncard	
			elseif (sgs.Self:getMark("@qishublack") > 0) then
				local ncard = sgs.Sanguosha:cloneCard("analeptic", originalCard:getSuit(), originalCard:getNumber())
				ncard:addSubcard(originalCard)
				ncard:setSkillName(self:objectName()) --sgs.Self:getMark("Luameiyin")
				return ncard					
			else
				return nil
			end			
		end
	end ,
	enabled_at_play = function(self, target)
		if (target:getMark("@qishured") > 0) or ((target:getMark("@qishured") == 0) and (target:getMark("@qishublack") == 0)) then return sgs.Slash_IsAvailable(target) end
		return true
	end,
	enabled_at_response = function(self, target, pattern)
		if sgs.Sanguosha:getCurrentCardUseReason() ~= sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE then return false end 
		if ((pattern == "nullification") or string.find(pattern, "analeptic"))
				and ((target:getMark("@qishured") > 0) or (((target:getMark("@qishured") == 0)) and (target:getMark("@qishublack") == 0))) then return false end
		if ((pattern == "slash") or (pattern == "jink")) and (target:getMark("@qishublack") > 0) then return false end
		return true 
	end,
	enabled_at_nullification = function(self, player)
		for _, card in sgs.qlist(player:getHandcards()) do
			if card:isBlack() then 
				local room = player:getRoom()
				room:setPlayerMark(player, "luaqishu", 2)
				return (player:getMark("@qishublack") > 0)
			end
		end
		return false
	end
}
luaqishu2 = sgs.CreateTriggerSkill{
	name = "#luaqishu",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardAsked},
	on_trigger = function(self,event,player,data)
		local room = player:getRoom()
		local pattern = data:toStringList()[1]
		if (pattern == "jink") then 
			room:setPlayerMark(player, "luaqishu", 1)
		elseif (pattern == "slash") then 
			room:setPlayerMark(player, "luaqishu", 3)
		elseif string.find(pattern, "analeptic") or string.find(pattern, "peach") then 
			room:writeToConsole("sakuya test" .. player:getGeneralName())
			room:setPlayerMark(player, "luaqishu", 4)
		end
	end,
}

qishuused = sgs.CreateTriggerSkill{
	name = "#qishuused" ,
	frequency = sgs.Skill_Compulsory ,
	events = {sgs.CardResponded, sgs.CardUsed} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if not player:hasSkill("luaqishu") then return false end 
		if event == sgs.CardResponded then
			local resp = data:toCardResponse()
			if (resp.m_card:getSkillName() == "luaqishu") then
				if (player:getMark("@qishured") > 0) or ((player:getMark("@qishured") == 0) and (player:getMark("@qishublack") == 0)) then
					room:setPlayerMark(player, "@qishured", 0)
					room:setPlayerMark(player, "@qishublack", 1)
				elseif (player:getMark("@qishublack") > 0) then
					room:setPlayerMark(player, "@qishured", 1)
					room:setPlayerMark(player, "@qishublack", 0)
				end 
			end
		else
			local use = data:toCardUse()
			if (use.from:objectName() == player:objectName()) and (use.card:getSkillName() == "luaqishu") then
				if (player:getMark("@qishured") > 0) or ((player:getMark("@qishured") == 0) and (player:getMark("@qishublack") == 0)) then
					room:setPlayerMark(player, "@qishured", 0)
					room:setPlayerMark(player, "@qishublack", 1)
				elseif (use.from:getMark("@qishublack") > 0) then
					room:setPlayerMark(player, "@qishured", 1)
					room:setPlayerMark(player, "@qishublack", 0)
				end 
			end
		end
		return false
	end
}


sakuya:addSkill(luashiji)
sakuya:addSkill(luaweixinsdf)
sakuya:addSkill(luashiji2)
sakuya:addSkill(qishuused)
sakuya:addSkill(luaqishu2)

sakuyaA:addSkill(luashiji)
sakuyaA:addSkill(luashijie)
sakuyaA:addSkill(luashiji2)
sakuyaA:addSkill(qishuused)
sakuyaA:addSkill(luaqishu2)

sakuyaB:addSkill(luashiji)
sakuyaB:addSkill(luashijie)
sakuyaB:addSkill(luashiji2)
sakuyaB:addSkill(qishuused)
sakuyaB:addSkill(luaqishu2)

sakuyaC:addSkill(luashiji)
sakuyaC:addSkill(luashijie)
sakuyaC:addSkill(luashiji2)
sakuyaC:addSkill(qishuused)
sakuyaC:addSkill(luaqishu2)

sakuyaD:addSkill(luashiji)
sakuyaD:addSkill(luashijie)
sakuyaD:addSkill(luashiji2)
sakuyaD:addSkill(qishuused)
sakuyaD:addSkill(luaqishu2)

sakuyaO:addSkill(luashiji)
sakuyaO:addSkill(luashijie)
sakuyaO:addSkill(luashiji2)
sakuyaO:addSkill(qishuused)
sakuyaO:addSkill(luaqishu2)

ceshisd:addSkill(luaqishu)
ceshisd:addSkill(luaxiaosa)


luaqijin = sgs.CreateTriggerSkill{
	name = "luaqijin",
	frequency = sgs.Skill_Frequent,
	events = {sgs.EventPhaseChanging},
	can_trigger = function(self, target)
		return target and target:hasSkill(self:objectName()) and target:isAlive() and target:isWounded()
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local change = data:toPhaseChange()
		if change.to == sgs.Player_Draw and room:askForSkillInvoke(player, "luaqijina") then
			room:recover(player, sgs.RecoverStruct(player, nil, player:getLostHp()))
			if room:askForSkillInvoke(player, "luaqijinb") then 
				room:loseHp(player)
				room:acquireSkill(player, "yingzi")
				room:acquireSkill(player, "mashu")
				local targets = sgs.SPlayerList()
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if player:canSlash(p, nil, false) then
						targets:append(p)
					end
				end				
				room:askForUseSlashTo(player, targets, "luaqijin", false)
			end 
		end 
		return false
	end
}
luaqijin2 = sgs.CreateTriggerSkill{
	name = "#luaqijin" ,
	events = {sgs.EventPhaseChanging} ,
	priority = 1,
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		if change.to ~= sgs.Player_NotActive then return false end
		local grani = player:getRoom():findPlayerBySkillName("luaqijin")
		local p = grani
		if not p then return false end 
		local room = p:getRoom()
		if p and p:hasSkill("yingzi") then room:detachSkillFromPlayer(p, "yingzi", false, true) end 
		if p and p:hasSkill("mashu") then room:detachSkillFromPlayer(p, "mashu", false, true) end 
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end
}



luahouhu = sgs.CreateTriggerSkill{
	name = "luahouhu",
	events = {sgs.Damage},
	global = true,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		local target = damage.to
		if (not damage.transfer) then
			for _, okina in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				local room_0 = okina:getRoom()
				if (player:getMark("forbid_houhu") == 0) and player:objectName() ~= okina:objectName() then 
					local playerdata = sgs.QVariant() -- ai用
					playerdata:setValue(player)
					okina:getRoom():setTag("luahouhup", playerdata)	
					if room_0:askForSkillInvoke(okina, self:objectName(), data) then 
						room_0:loseHp(okina)
						player:drawCards(1)
						player:turnOver()
					else
						if player:getMark("forbid_houhur") == 0 then 
							local choice = room_0:askForChoice(okina, "forbid_houhu","forbid_houhu:yes+forbid_houhu:no")
							if choice == "forbid_houhu:yes" then 
								room_0:setPlayerMark(player, "forbid_houhu", 1)
							else
								room_0:setPlayerMark(player, "forbid_houhur", 1)
							end 
						end 
					end
					okina:getRoom():removeTag("luahouhup")	
				end 
				return false
			end 
		end 
	end 
}

luahouhu3 = sgs.CreateTriggerSkill{
	name = "#luahouhu2" ,
	events = {sgs.EventPhaseChanging, sgs.PreCardUsed} ,
	can_trigger = function(self, target)
		return target ~= nil
	end ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseChanging then 
			local change = data:toPhaseChange()
			if change.to == sgs.Player_Start and player:hasSkill("luahouhu") then
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if not p:faceUp() then 
						if not p:getEquips():isEmpty() then
							local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
							for _, equip in sgs.qlist(p:getEquips()) do
								 dummy:addSubcard(equip:getEffectiveId())
							end	
							p:getRoom():obtainCard(p, dummy, false)
						end 
						local skills = p:getVisibleSkillList()
						if skills:length() > 0 then 
							local detachList = {}
							local gainList = {}
							for _,skill in sgs.qlist(skills) do
								if not skill:inherits("SPConvertSkill") and not skill:isAttachedLordSkill() then
									table.insert(detachList,"-"..skill:objectName())
									table.insert(gainList,skill:objectName())
								end
							end
							p:getRoom():handleAcquireDetachSkills(p, table.concat(detachList,"|"))
							if p:isAlive() then
								p:gainMark("@luahouhuq")
								p:getRoom():writeToConsole(p:objectName().."失去了以下技能"..table.concat(gainList,"|"))
								p:setTag("luahouhuq", sgs.QVariant(table.concat(gainList,"|")))
							end
						end 						
					end 
				end 
			elseif change.to == sgs.Player_NotActive and player:hasSkill("luahouhu") then
				for _, p in sgs.qlist(player:getRoom():getAlivePlayers()) do
					local gainList = p:getTag("luahouhuq"):toString()
					gainList = gainList:split("|")
					p:removeTag("luahouhuq")
					p:loseAllMarks("@luahouhuq")
					for i = 1,#gainList do 
						p:getRoom():writeToConsole(p:objectName().."得到了以下技能"..gainList[i])
						p:getRoom():acquireSkill(p, gainList[i])
					end 				
				end 
			end 		
		end 
	end 
}

miyiCard = sgs.CreateSkillCard{
	name = "luamiyi", 
	filter = function(self, targets, to_select)
		return (#targets <= 0) 
	end, 
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		local room2 = effect.to:getRoom()
		local xd = effect.to:getHp() + 1 
		room:setPlayerProperty(effect.to, "hp", sgs.QVariant(xd)) --
		effect.to:drawCards(1)
		room:setPlayerMark(effect.to, "zhuwu", 1)		
	end 
}
luamiyiVS = sgs.CreateViewAsSkill{
	name = "luamiyi", 
	n = 1, 
	view_filter = function(self, selected, to_select)
		return true
	end, 
	view_as = function(self, cards)
		if #cards ~= 1 then return nil end
		local card = miyiCard:clone()
		for _, cd in ipairs(cards) do
			card:addSubcard(cd)
		end
		return card
	end, 
	enabled_at_play = function()
		return false
	end, 
	enabled_at_response = function(self, player, pattern)
		return string.startsWith(pattern, "@@luamiyi")
	end
}

luamiyi = sgs.CreateTriggerSkill{
	name = "luamiyi", 
	view_as_skill = luamiyiVS, 
	events = {sgs.EventPhaseStart}, 
	on_trigger = function(self, event, player, data)
		if player:getPhase() == sgs.Player_Start then
			local room = player:getRoom()
			local r = room:getLord():getMark("@clock_time")
			r = r - math.floor(r/6)*6
			if player:getHp() == r then 
				
				local to = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "luamiyi1", true, true)
				if to then 
					local p = r - to:getHandcardNum() 
					if p < 0 then 
						p = 0 - p
						room:askForDiscard(to, self:objectName(), p, p, false, true)
					elseif p > 0 then 
						to:drawCards(p)
					end 										
				end 
			end 
			if player:getHandcardNum() == r then 
				room:askForUseCard(player, "@@luamiyi", "@luamiyi")
			
			end 
		end 
	end 
}

luamisheng = sgs.CreateTriggerSkill{
	name = "luamisheng$",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.BeforeCardsMove, sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:hasLordSkill(self:objectName()) then 
			if event == sgs.BeforeCardsMove then 
				local move = data:toMoveOneTime()
				local opq = room:getDiscardPile():length()		
				local rst = room:getDrawPile():length()		
				if move.from_places:contains(sgs.Player_DrawPile) and opq and opq > 0 then 
					local count = 0
					for _, id in sgs.qlist(move.card_ids) do
						if room:getCardPlace(id) == sgs.Player_DrawPile then
							count = count + 1
						end 
					end 
					if count > rst then 
						room:setPlayerFlag(player, "luamisheng")
					end 
				end 
			else
				local move = data:toMoveOneTime()
				local opq = room:getDiscardPile():length()		
				local rst = room:getDrawPile():length()		
				if move.from_places:contains(sgs.Player_DrawPile) then
					if player:hasFlag("luamisheng") then 
						if opq and opq > 0 then 
							room:recover(player, sgs.RecoverStruct(player))
							local y = 0
							for _,p in sgs.qlist(room:getAlivePlayers()) do
								if p:getRole() == "loyalist" and p:isAlive() then y = y + 1 end 
							end 			
							player:drawCards(2*y)
						end 
						room:setPlayerFlag(player, "-luamisheng")
					end 
				end 
			end 
		end 
	end 
	
}
okina:addSkill(luahouhu)
okina:addSkill(luahouhu3)
okina:addSkill(luamiyi)
okina:addSkill(luamisheng)
--[[
luafenxing2 = sgs.CreateMaxCardsSkill{
	name = "#luafenxing" ,
	fixed_func = function(self, target)
    	if target:hasSkill("luafenxing") then
			return 3
		end 	
		return -1
	end
}

luafenxing = sgs.CreateTriggerSkill{
	name = "luafenxing",
	global = true,
	priority = 10,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseStart, sgs.HpRecover, sgs.HpChanged, sgs.Damage, sgs.EnterDying },
	on_trigger = function(self, event, player, data, room)
		local flandre = room:findPlayerBySkillName("luafenxing")
		if not flandre then return false end
		if not flandre:isAlive() then
			return false
		end
		if flandre:getHp() < 1 then
			room:acquireSkill(flandre, "luafenxing5", false)
		else
			room:detachSkillFromPlayer(flandre, "luafenxing5")
		end
	end
}



luajingdan = sgs.CreateTriggerSkill{
	name = "luajingdan", 
	global = true, 
	events = {sgs.TargetSpecified}, 
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		local room = player:getRoom()
		if event == sgs.TargetSpecified and player:objectName() == use.from:objectName() and use.from:hasSkill(self:objectName()) and use.to:length() >= 1 and (use.card:isKindOf("Slash")) then
			local jink_table = sgs.QList2Table(player:getTag("Jink_" .. use.card:toString()):toIntList())
			local index = 1
			for _, t in sgs.qlist(use.to) do
				local _data = sgs.QVariant()
				_data:setValue(t)
				local _data2 = sgs.QVariant()
				_data2:setValue(use.card)
				if room:askForSkillInvoke(player, self:objectName(), _data) then
					player:drawCards(1)
					room:addPlayerMark(t, "@skill_invalidity")
					local card = room:askForCard(player, ".|.|.|.", "@luajingdan", _data2, sgs.Card_MethodNone)
					if card then
						room:throwCard(card, player, player)
						jink_table[index] = 0
					end 	
				end
				index = index + 1
			end 
			local jink_data = sgs.QVariant()
			jink_data:setValue(Table2IntList(jink_table))
			player:setTag("Jink_" .. use.card:toString(), jink_data)
		end
		return false
	end
}

luahuimieCard = sgs.CreateSkillCard{
	name = "luahuimie",
	will_throw = true,
	filter = function(self, targets, to_select)
		local danmu = sgs.Sanguosha:cloneCard("yuzhi", sgs.Card_NoSuit, 0)
		return sgs.Self:inMyAttackRange(to_select,  - sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_DistanceLimit, sgs.Self, danmu))
				and #targets < 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, sgs.Self, danmu)
	end,
	on_use = function(self, room, source, targets)
		local danmu = sgs.Sanguosha:cloneCard("yuzhi", sgs.Card_NoSuit, 0)
		local targets2 = sgs.SPlayerList()
		for _, p in ipairs(targets) do
			targets2:append(p)
		end
		room:useCard(sgs.CardUseStruct(danmu, source, targets2), true)
	end
}
luahuimieVS = sgs.CreateZeroCardViewAsSkill{
	name = "luahuimie",
	response_pattern = "@@luahuimie",
	view_as = function()
		return luahuimieCard:clone()
	end
}
luahuimie = sgs.CreateTriggerSkill{
	name = "luahuimie", 
	view_as_skill = luahuimieVS, 
	events = {sgs.EventPhaseStart, sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart and player:getPhase() == sgs.Player_Play and player:hasSkill(self:objectName()) then
			room:askForDiscard(player, self:objectName(), 1, 1, true, true)
		elseif event == sgs.CardsMoveOneTime then
			local move = data:toMoveOneTime()
			if move.from and move.from:objectName() == player:objectName() and player:hasSkill("luahuimie") and not move.card_ids:isEmpty() then
				local reason = move.reason.m_reason
				local reasonx = bit32.band(reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)
				local Yes = reasonx == sgs.CardMoveReason_S_REASON_DISCARD
				if Yes and not player:hasUsed("yuzhi") then
					if room:askForUseCard(player, "@@luahuimie", "@luahuimie") then

					end
				end
			end
		end
	end
}

rh_flandre:addSkill(luafenxing2)
rh_flandre:addSkill(luafenxing)
rh_flandre:addSkill(luajingdan)
rh_flandre:addSkill(luahuimie)

rh_flandreA:addSkill(luafenxing2)
rh_flandreA:addSkill(luafenxing)
rh_flandreA:addSkill(luajingdan)
rh_flandreA:addSkill(luahuimie)

rh_flandreB:addSkill(luafenxing2)
rh_flandreB:addSkill(luafenxing)
rh_flandreB:addSkill(luajingdan)
rh_flandreB:addSkill(luahuimie)

rh_flandreC:addSkill(luafenxing2)
rh_flandreC:addSkill(luafenxing)
rh_flandreC:addSkill(luajingdan)
rh_flandreC:addSkill(luahuimie)

rh_flandreD:addSkill(luafenxing2)
rh_flandreD:addSkill(luafenxing)
rh_flandreD:addSkill(luajingdan)
rh_flandreD:addSkill(luahuimie)

rh_flandreE:addSkill(luafenxing2)
rh_flandreE:addSkill(luafenxing)
rh_flandreE:addSkill(luajingdan)
rh_flandreE:addSkill(luahuimie)

rh_flandreF:addSkill(luafenxing2)
rh_flandreF:addSkill(luafenxing)
rh_flandreF:addSkill(luajingdan)
rh_flandreF:addSkill(luahuimie)

rh_flandreG:addSkill(luafenxing2)
rh_flandreG:addSkill(luafenxing)
rh_flandreG:addSkill(luajingdan)
rh_flandreG:addSkill(luahuimie)
]]--

luajingdan = sgs.CreateViewAsSkill{
	name = "luajingdan",
	n = 3,
	view_filter = function(self, selected, to_select)
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1) 
			for _, card in ipairs(selected) do
				slash:addSubcard(card)
			end 
			slash:deleteLater()
			if sgs.Self:getMark("luajingdanNL") > 0 then
				slash:setSkillName("luajingdan2")
			else
				slash:setSkillName("luajingdan")
			end 
			return slash:isAvailable(sgs.Self) and not to_select:isEquipped()
		end
		return not to_select:isEquipped()
	end,
	view_as = function(self, cards)
		if #cards ~= 3 then return nil end
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
		for _, cd in ipairs(cards) do
			slash:addSubcard(cd)
		end
		if sgs.Self:getMark("luajingdanNL") > 0 then
			slash:setSkillName("luajingdan2")
		else
			slash:setSkillName("luajingdan")
		end 
		return slash 
	end, 
	enabled_at_play = function(self, player)
		return player:getPile("&fenxing"):length() == 0
	end, 
	enabled_at_response = function(self, player, pattern)
		return pattern == "slash"
	end
}
luajingdan2 = sgs.CreateTriggerSkill{
    name = "#luajingdan2",
	global = true,
	priority = 10,
    events = {sgs.BeforeCardsMove, sgs.CardUsed, sgs.CardResponded, sgs.EventPhaseChanging},
    on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardUsed then 
			local use = data:toCardUse()
			local card = data:toCardUse().card
			if card and not card:isKindOf("SkillCard") and card:isKindOf("Slash") and (card:getSkillName() == "luajingdan" or card:getSkillName() == "luajingdan2")
				and use.from:objectName() == player:objectName() then 
				for _,id in sgs.qlist(card:getSubcards()) do 
					room:setPlayerMark(player, "luajingdanx" .. tostring(id), 1) --没有在回合结束时重置标记
				end 
				room:setPlayerMark(player, "luajingdanNL", player:getMark("luajingdanNL") + 1)
			end  
		elseif event == sgs.CardResponded then 

		elseif event == sgs.BeforeCardsMove then 
			local move = data:toMoveOneTime()
			local reason = move.reason.m_reason
			local reasonx = bit32.band(reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)
			local Yes = reasonx == sgs.CardMoveReason_S_REASON_USE or reasonx == sgs.CardMoveReason_S_REASON_RESPONSE
			if move.from_places:contains(sgs.Player_PlaceTable) and move.to_place == sgs.Player_DiscardPile and
				move.from and move.from:objectName() == player:objectName() and Yes then  
				local old_card = move.card_ids
				for i = old_card:length() - 1, 0, -1 do 
					local id = old_card:at(i)
					for j = 0, 500 do
						if player:getMark("luajingdanx" .. tostring(id)) > 0 and j == id then
							player:addToPile("&fenxing", id, false) 
							move.card_ids:removeAt(i)
							move.from_places:removeAt(i)
						end 
					end 
				end	
				for i = 0, 500 do
					room:setPlayerMark(player, "luajingdanx" .. tostring(i), 0)
				end 
				data:setValue(move)
			end
		elseif event == sgs.EventPhaseChanging then 
			local change = data:toPhaseChange()
			if change.to ~= sgs.Player_NotActive then return false end
			if room:getCurrent():objectName() == player:objectName() then
				for j = 0, 500 do
					room:setPlayerMark(player, "luajingdanx" .. tostring(j), 0) 
				end 
				room:setPlayerMark(player, "luajingdanNL", 0)
			end 
		end 
	end,		
}

luajinji2 = sgs.CreateTriggerSkill{
	name = "#luajinji" ,
	events = {sgs.EventPhaseChanging} ,
	--frequency = sgs.Skill_Frequent , 这句话源代码没有，但是我感觉应该加上，毕竟连破一点副作用都没有
	priority = 1,
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		if change.to ~= sgs.Player_NotActive then return false end
		local mima = player:getRoom():findPlayerBySkillName("luajinji")
		local p = mima
		if not p then return false end 
		local room = p:getRoom()
		if p and p:hasSkill("luadanmu") and p:hasFlag("TurnEndDetachDanMu") then 
			room:setPlayerFlag(player, "-TurnEndDetachDanMu")
			room:detachSkillFromPlayer(p, "luadanmu", false, true) 
		end  
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end
}

luajinjiCard = sgs.CreateSkillCard{
	name = "luajinji", 
	target_fixed = true,
	on_use = function(self, room, player, targets)
		local to_hand = sgs.IntList()
		for _, id in sgs.qlist(player:getPile("&fenxing")) do
			to_hand:append(id)
		end 

		local to_pile = sgs.IntList()
		local ii = 0
		if player:getHandcardNum() <= 3 then 
			for _, card in sgs.list(player:getHandcards()) do
				to_pile:append(card:getEffectiveId()) 
			end 
			ii = 1
		else 
			while ii < 3 do
				local card = room:askForCard(player, ".|.|.|hand", "@luajinji2", sgs.QVariant(), sgs.Card_MethodNone)
				if card then
					local cardid = card:getEffectiveId() 
					for _, id in sgs.qlist(to_pile) do
						if cardid == id then break end  
					end 
					to_pile:append(cardid) 
					ii = ii + 1
				end 
			end 
		end 

		if ii > 0 then 
			if not to_pile:isEmpty() then 
				player:addToPile("&fenxing", to_pile, false);
			end 		
			
			player:throwAllHandCards()
			if not to_hand:isEmpty() then 
				local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				dummy:addSubcards(to_hand)
				local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXCHANGE_FROM_PILE, "luajinji")
				room:obtainCard(player, dummy, reason, false)
			end  
			if not player:hasSkill("luadanmu") then room:acquireSkill(player, "luadanmu") end 
			room:setPlayerFlag(player, "TurnEndDetachDanMu")
		end 
		 
	end
}
luajinjiVS = sgs.CreateZeroCardViewAsSkill{
	name = "luajinji", 
	view_as = function(self, cards)
		return luajinjiCard:clone()
	end, 
	enabled_at_play = function(self, player)
		return player:getHandcardNum() > player:getPile("&fenxing"):length()
	end
}
luajinji = sgs.CreateTriggerSkill{
	name = "luajinji", 
	view_as_skill = luajinjiVS, 
	events = {sgs.EnterDying},
	on_trigger = function(self, event, player, data, room)
		if (player:getPile("&fenxing"):length() > player:getHandcardNum()) and room:askForSkillInvoke(player, self:objectName(), data) then  

			local to_hand = sgs.IntList()
			for _, id in sgs.qlist(player:getPile("&fenxing")) do
				to_hand:append(id)
			end 

			local to_pile = sgs.IntList()
			local ii = 0
			if player:getHandcardNum() <= 3 then 
				for _, card in sgs.list(player:getHandcards()) do
					to_pile:append(card:getEffectiveId()) 
				end 
				ii = 1
			else 
				while ii < 3 do
					local card = room:askForCard(effect.to, ".|.|.|hand", "@luajinji2", sgs.QVariant(), sgs.Card_MethodNone)
					if card then
						local cardid = card:getEffectiveId() 
						for _, id in sgs.qlist(to_pile) do
							if cardid == id then break end  
						end 
						to_pile:append(cardid) 
						ii = ii + 1
					end 
				end 
			end 

			if ii > 0 then 
				if not to_pile:isEmpty() then 
					player:addToPile("&fenxing", to_pile, false);
				end 		
				
				player:throwAllHandCards()
				if not to_hand:isEmpty() then 
					local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
					dummy:addSubcards(to_hand)
					local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXCHANGE_FROM_PILE, "luajinji")
					room:obtainCard(player, dummy, reason, false)
				end  
				if not player:hasSkill("luadanmu") then room:acquireSkill(player, "luadanmu") end 
				room:setPlayerFlag(player, "TurnEndDetachDanMu")
			end 
		end 
	end 
}

luajinguox = sgs.CreateFilterSkill{
	name = "luajinguox", 
	view_filter = function(self,to_select)
		local room = sgs.Sanguosha:currentRoom()
		local place = room:getCardPlace(to_select:getEffectiveId())
		return (place == sgs.Player_PlaceHand)
	end,
	view_as = function(self, originalCard)
		local slash = sgs.Sanguosha:cloneCard("peach", originalCard:getSuit(), originalCard:getNumber())
		slash:setSkillName(self:objectName())
		local card = sgs.Sanguosha:getWrappedCard(originalCard:getId())
		card:takeOver(slash)
		return card
	end
}

luajinguox2 = sgs.CreateTriggerSkill{
	name = "#luajinguox",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DrawNCards},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local count = 3
		data:setValue(count)
	end
}
luajingdan4 = sgs.CreateTargetModSkill{
	name = "#luajingdan3",
	pattern = "Slash",
	residue_func = function(self, player, card)
		if card:getSkillName() == "luajingdan" then
			return 1
		end 
		if card:getSkillName() == "luajingdan2" then
			return 2
		end 
		if player:getMark("luajingdanNL") > 0  then
			return player:getMark("luajingdanNL")
		end
	end,
    distance_limit_func = function(self, player, card)
        if card:getSkillName() == "luajingdan" or card:getSkillName() == "luajingdan2" then
            return 999
        else
            return 0
        end
    end
}

rh_flandre:addSkill(luajingdan)
rh_flandre:addSkill(luajingdan2)
rh_flandre:addSkill(luajingdan4)
rh_flandre:addSkill(luajinji)
rh_flandre:addSkill(luajinji2)
rh_flandre:addSkill(luajinguox)
rh_flandre:addSkill(luajinguox2)

rh_flandreA:addSkill(luajingdan)
rh_flandreA:addSkill(luajingdan2)
rh_flandreA:addSkill(luajingdan4)
rh_flandreA:addSkill(luajinji)
rh_flandreA:addSkill(luajinji2)
rh_flandreA:addSkill(luajinguox)
rh_flandreA:addSkill(luajinguox2)

rh_flandreB:addSkill(luajingdan)
rh_flandreB:addSkill(luajingdan2)
rh_flandreB:addSkill(luajingdan4)
rh_flandreB:addSkill(luajinji)
rh_flandreB:addSkill(luajinji2)
rh_flandreB:addSkill(luajinguox)
rh_flandreB:addSkill(luajinguox2)

rh_flandreC:addSkill(luajingdan)
rh_flandreC:addSkill(luajingdan2)
rh_flandreC:addSkill(luajingdan4)
rh_flandreC:addSkill(luajinji)
rh_flandreC:addSkill(luajinji2)
rh_flandreC:addSkill(luajinguox)
rh_flandreC:addSkill(luajinguox2)

rh_flandreD:addSkill(luajingdan)
rh_flandreD:addSkill(luajingdan2)
rh_flandreD:addSkill(luajingdan4)
rh_flandreD:addSkill(luajinji)
rh_flandreD:addSkill(luajinji2)
rh_flandreD:addSkill(luajinguox)
rh_flandreD:addSkill(luajinguox2)

rh_flandreE:addSkill(luajingdan)
rh_flandreE:addSkill(luajingdan2)
rh_flandreE:addSkill(luajingdan4)
rh_flandreE:addSkill(luajinji)
rh_flandreE:addSkill(luajinji2)
rh_flandreE:addSkill(luajinguox)
rh_flandreE:addSkill(luajinguox2)

rh_flandreF:addSkill(luajingdan)
rh_flandreF:addSkill(luajingdan2)
rh_flandreF:addSkill(luajingdan4)
rh_flandreF:addSkill(luajinji)
rh_flandreF:addSkill(luajinji2)
rh_flandreF:addSkill(luajinguox)
rh_flandreF:addSkill(luajinguox2)

rh_flandreG:addSkill(luajingdan)
rh_flandreG:addSkill(luajingdan2)
rh_flandreG:addSkill(luajingdan4)
rh_flandreG:addSkill(luajinji)
rh_flandreG:addSkill(luajinji2)
rh_flandreG:addSkill(luajinguox)
rh_flandreG:addSkill(luajinguox2)

lueyingCard = sgs.CreateSkillCard{
	name = "lualueying",
	filter = function(self, targets, to_select)
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		return (#targets <= 0) and sgs.Self:canSlash(to_select, slash, true)
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
		slash:addSubcards(self:getSubcards())
		slash:setSkillName("lualueying")
		room:useCard(sgs.CardUseStruct(slash, effect.from, effect.to))
		effect.to:drawCards(1)
	end
}
lualueying2 = sgs.CreateTriggerSkill{
	name = "#lualueying2" ,
	events = {sgs.TargetConfirmed} ,
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		if not use.from then return false end
		if (player:objectName() ~= use.from:objectName()) or (not use.card:isKindOf("Slash")) then return false end
		if use.card:getSkillName() ~= "lualueying" then return false end
		local room = player:getRoom()
		local jink_table = sgs.QList2Table(player:getTag("Jink_" .. use.card:toString()):toIntList())
		local index = 1
		for _, p in sgs.qlist(use.to) do
			local _data = sgs.QVariant()
			_data:setValue(p)
			jink_table[index] = 0
			index = index + 1
		end
		local jink_data = sgs.QVariant()
		jink_data:setValue(Table2IntList(jink_table))
		player:setTag("Jink_" .. use.card:toString(), jink_data)
		return false
	end
}
lualueyingVS = sgs.CreateViewAsSkill{
	name = "lualueying",
	n = 2,
	view_filter = function(self, selected, to_select)
		if #selected > 0 then
			if (to_select:objectName() == selected[1]:objectName()) or (to_select:isKindOf("Slash") and selected[1]:isKindOf("Slash")) then
				return true
			end
			return false
		end
		return true
	end,
	view_as = function(self, cards)
		if #cards ~= 2 then return nil end
		local card = lueyingCard:clone()
		for _, cd in ipairs(cards) do
			card:addSubcard(cd)
		end
		return card
	end,
	enabled_at_play = function()
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return string.startsWith(pattern, "@@lualueying")
	end
}
lualueying3 = sgs.CreateTriggerSkill{
	name = "#lualueying3",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.ConfirmDamage},
	can_trigger = function(self, player)
		return player
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.ConfirmDamage then
			local damage = data:toDamage()
			if damage.to and (damage.card and damage.card:isKindOf("Slash") and damage.card:getSkillName() == "lualueying") then
				damage.damage = damage.damage + 1
				data:setValue(damage)
				return false
			end
		end
	end
}

lualueying = sgs.CreateTriggerSkill{
	name = "lualueying",
	view_as_skill = lualueyingVS,
	events = {sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data, room)
		if player:getPhase() == sgs.Player_Play then
			if room:askForUseCard(player, "@@lualueying", "@lualueying") then
				return false
			end
		end
	end
}

langchaoCard = sgs.CreateSkillCard{
	name = "luelangchao",
	will_throw = true ,
	target_fixed = true ,
	on_use = function(self, room, source, targets)
		local count = 3
		for i = 1, count, 1 do
			local card_id = room:drawCard()
			local card = sgs.Sanguosha:getCard(card_id)
			local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_SHOW, source:objectName(), "", self:objectName(), "")
			room:moveCardTo(card, source, sgs.Player_PlaceTable, reason, true)
			room:getThread():delay()
			if not card:isKindOf("BasicCard") then
				room:throwCard(card_id, nil)
				room:setEmotion(source, "bad")
			else
				room:obtainCard(source, card_id)
				room:setEmotion(source, "good")
			end
		end
	end
}

luelangchao = sgs.CreateViewAsSkill{
	name = "luelangchao",
	n = 1,
	view_filter = function(self, selected, to_select)
		return not to_select:isKindOf("BasicCard") and to_select:isBlack()
	end,
	view_as = function(self, cards)
		if #cards ~= 1 then return nil end
		local card = langchaoCard:clone()
		for _, cd in ipairs(cards) do
			card:addSubcard(cd)
		end
		return card
	end,
    enabled_at_play = function(self, player)
        return not player:hasUsed("#luelangchao")
    end,
}

lueshenhai = sgs.CreateTriggerSkill{
	name = "lueshenhai$",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.ConfirmDamage},
	can_trigger = function(self, player)
		return player
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local function ifSlash(card)
			room:writeToConsole("skd测试")
			if card:getSuit() == sgs.Card_NoSuit then return true end
			for _, id in sgs.qlist(room:getDrawPile()) do
				local cardX = sgs.Sanguosha:getCard(id)
				if cardX:isBlack() == card:isBlack() then return false end
			end
			for _, id in sgs.qlist(room:getDiscardPile()) do
				local cardX = sgs.Sanguosha:getCard(id)
				if cardX:isBlack() == card:isBlack() then return false end
			end
			return true
		end
		if event == sgs.ConfirmDamage then
			local damage = data:toDamage()
            if not player:hasLordSkill("lueshenhai") then return false end
			if damage.to and (damage.card and damage.card:isKindOf("Slash") and ifSlash(damage.card)) and damage.to:hasSkill("luajiawei") then
				damage.damage = damage.damage + 1
				data:setValue(damage)
				return false
			end
		end
	end
}

skadi:addSkill(luelangchao)
skadi:addSkill(lualueying)
skadi:addSkill(lualueying2)
skadi:addSkill(lualueying3)
skadi:addSkill(lueshenhai)

lualvzhi = sgs.CreateTriggerSkill{
	name = "lualvzhi" ,
	events = {sgs.DamageCaused} ,
	frequency = sgs.Skill_Compulsory,
	global = true,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.DamageCaused then
			local damage = data:toDamage() 
			if damage.chain then return false end
			if damage.to and damage.to:hasSkill("lualvzhi") and damage.card and damage.card:isKindOf("Slash") then
				damage.damage = 0
				room:setPlayerProperty(damage.to, "maxhp", sgs.QVariant(damage.to:getMaxHp() - 1))
				if damage.to:getHp() <= 0 or damage.to:getMaxHp() <= 0 then room:killPlayer(damage.to) end
				data:setValue(damage)
			end
		end
		return false
	end
}


lualvzhi2 = sgs.CreateTriggerSkill
{
	name = "#lualvzhi",
	events = {sgs.CardUsed, sgs.CardResponded},
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardUsed then
			local card = data:toCardUse().card
			if card:isKindOf("BasicCard") and player:hasSkill("lualvzhi") and player:isWounded() then
				room:recover(player, sgs.RecoverStruct(player, nil, 1))

			end
		elseif event == sgs.CardResponded then
			local cd = data:toCardResponse().m_card
			if cd:isKindOf("BasicCard") and player:hasSkill("lualvzhi") and player:isWounded() then
				room:recover(player, sgs.RecoverStruct(player, nil, 1))
			end
		end
	end,
}

luashenjunCard = sgs.CreateSkillCard{
	name = "luashenjun",
	will_throw = true,
	filter = function(self, targets, to_select)
		local card = sgs.Sanguosha:getCard(self:getSubcards():at(0))
		return #targets < sgs.Self:getHp() - 1 and not sgs.Self:isProhibited(to_select, card, to_select:getSiblings())
			and not (card:isKindOf("Peach") and not to_select:isWounded())
	end,
	on_use = function(self, room, source, targets)
		local card0 = sgs.Sanguosha:getCard(self:getSubcards():at(0))
		local dummy = sgs.Sanguosha:cloneCard(card0:objectName(), sgs.Card_NoSuit, -1)
		if dummy:isKindOf("AOE") or dummy:isKindOf("AmazingGrace") or dummy:isKindOf("GodSalvation") then
			room:setPlayerFlag(source, "qucaiAOE")
			for _, p in ipairs(targets) do
				room:setPlayerFlag(p, "qucaiAOEs")
			end
			room:useCard(sgs.CardUseStruct(dummy, source, sgs.SPlayerList()))
			room:setPlayerFlag(source, "-qucaiAOE")
			for _, p in ipairs(targets) do
				room:setPlayerFlag(p, "-qucaiAOEs")
			end
			return false
		end
		local splist = sgs.SPlayerList()
		for _, p in ipairs(targets) do
			splist:append(p)
		end
		room:useCard(sgs.CardUseStruct(dummy, source, splist))
	end
}

luashenjun = sgs.CreateOneCardViewAsSkill{
	name = "luashenjun",
	view_filter = function(self, card)
		if card:isKindOf("TrickCard") and not card:isNDTrick() then
			if sgs.Self:getHp() ~= 2 then return false end
		end
		return not card:isKindOf("EquipCard") and not card:isKindOf("Jink") and not card:isKindOf("Nullification")
				and not sgs.Self:isCardLimited(card, sgs.Card_MethodUse)
	end,
	view_as = function(self, card)
		local qnc = luashenjunCard:clone()
		qnc:addSubcard(card)
		qnc:setSkillName(self:objectName())
		return qnc
	end,
	enabled_at_play = function(self, player)
		return player:canDiscard(player, "he") and not player:hasUsed("#luashenjun")
			and not player:isWounded()
	end,
}

 

luamingming = sgs.CreateTriggerSkill{
	name = "luamingming$",
	events = {sgs.EventPhaseChanging, sgs.EventPhaseSkipping},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseChanging and data:toPhaseChange().to == sgs.Player_NotActive and player:hasLordSkill(self:objectName()) then
			local splist = sgs.SPlayerList()
			for _, Dplayer in sgs.qlist(room:getAlivePlayers()) do
				if Dplayer:getKingdom() ~= "god" and not (Dplayer:hasSkill("shoucheng") and Dplayer:hasSkill("chouce") and Dplayer:hasSkill("buyi") and to:hasSkill("qingnang")) then
					splist:append(Dplayer)
				end
			end
			if not splist:isEmpty() then
				local to = room:askForPlayerChosen(player, splist, "luamingming", "luamingming2", true, true)
				if to then
					local choices = {}
					if not to:hasSkill("shoucheng") then table.insert(choices, "shoucheng") end
					if not to:hasSkill("chouce") then table.insert(choices, "chouce") end
					if not to:hasSkill("buyi") then table.insert(choices, "buyi") end
					if not to:hasSkill("luaqingnang") then table.insert(choices, "luaqingnang") end
					local choice = room:askForChoice(player, "luamingming", table.concat(choices, "+"))
					room:handleAcquireDetachSkills(to, choice)
					player:getRoom():setPlayerProperty(to, "kingdom", sgs.QVariant("god"))
				end
			end
		end
	end,
	can_trigger = function(self, target)
		return target and target:isAlive() and not target:isWounded()
	end
}

mihana:addSkill(lualvzhi)
mihana:addSkill(lualvzhi2)
mihana:addSkill(luashenjun) 
mihana:addSkill(luamingming)

local function Set(list)
	local set = {}
	for _, l in ipairs(list) do set[l] = true end
	return set
end

local function decideTrick(room, card_0, card_1, player, target1, target2)
	local type = {}
	local delay_trick = {}
	local sttrick = {}
	local mttrick = {}
	local patterns = {"snatch", "dismantlement", "ex_nihilo", "duel", "fire_attack", "amazing_grace", "savage_assault", "archery_attack", "god_salvation", "iron_chain", "supply_shortage", "lightning", "indulgence"}
	if not (Set(sgs.Sanguosha:getBanPackages()))["pay9"] then
		table.insert(patterns, 2, "banquet")
	end
	for _, cd in ipairs(patterns) do
		local card1 = sgs.Sanguosha:cloneCard(cd, card_0:getSuit(), card_0:getNumber())
		local card2 = sgs.Sanguosha:cloneCard(cd, card_1:getSuit(), card_1:getNumber())
		card1:addSubcard(card_0)
		card1:setSkillName("luajiance")

		card2:addSubcard(card_1)
		card2:setSkillName("luajiance")
		if (card1 and (card1:targetFilter(sgs.PlayerList(), target1, player) or card1:targetFixed()) and not player:isProhibited(target1, card1))
			and (card2 and card2:targetFilter(sgs.PlayerList(), target2, player or card2:targetFixed()) and not player:isProhibited(target2, card2)) then
			card1:deleteLater()
			card2:deleteLater()
			if card1:isAvailable(player) and card2:isAvailable(player) then
				if card1:isKindOf("DelayedTrick") then
					table.insert(delay_trick, cd)
				elseif (card1:isKindOf("SingleTargetTrick") and not card1:isKindOf("DelayedTrick")) then
					table.insert(sttrick, cd)
				else
					table.insert(mttrick, cd)
				end
			end
		end
	end
	if #sttrick ~= 0 then table.insert(type, "single_target_trick") end
	if #mttrick ~= 0 then table.insert(type, "multiple_target_trick") end
	if #sttrick + #mttrick == 0 then return false end
	local typechoice = ""
	if #type > 0 then
		typechoice = room:askForChoice(player, "luajiance", table.concat(type, "+"))
	end
	local choices = {}
	if typechoice == "delay_trick" then
		choices = delay_trick
	elseif typechoice == "single_target_trick" then
		choices = sttrick
	elseif typechoice == "multiple_target_trick" then
		choices = mttrick
	end
	local pattern_0
	if player:getState() ~= "robot" then
		pattern_0 = room:askForChoice(player, "luajianceX", table.concat(choices, "+"))
	else
		pattern_0 = room:askForChoice(player, "luajianceX", table.concat(patterns, "+"))
	end
	if pattern_0 then
		room:writeToConsole(pattern_0)
		local card_X = sgs.Sanguosha:cloneCard(pattern_0, sgs.Card_SuitToBeDecided, -1)
		if not (card_X:isKindOf("AOE")  or card_X:isKindOf("AmazingGrace") or card_X:isKindOf("GodSalvation")) then
			card_X:addSubcard(card_0)
			card_X:setSkillName("luajiance")
			room:useCard(sgs.CardUseStruct(card_X, player, target1))
			local card_Y = sgs.Sanguosha:cloneCard(pattern_0, sgs.Card_SuitToBeDecided, -1)
			card_Y:addSubcard(card_1)
			card_Y:setSkillName("luajiance")
			room:useCard(sgs.CardUseStruct(card_Y, player, target2))
		else
			local card_Z = sgs.Sanguosha:cloneCard(pattern_0, sgs.Card_SuitToBeDecided, -1)
			local targets = {target1, target2}
			card_Z:addSubcard(card_0)
			card_Z:addSubcard(card_1)
			card_Z:setSkillName("luajiance")
			room:setPlayerFlag(player, "jianceAOE")
			for _, p in ipairs(targets) do
				room:setPlayerFlag(p, "jianceAOEs")
			end
			room:useCard(sgs.CardUseStruct(card_Z, player, sgs.SPlayerList()))
			room:setPlayerFlag(player, "-jianceAOE")
			for _, p in ipairs(targets) do
				room:setPlayerFlag(p, "-jianceAOEs")
			end
		end
	end
end
luajianceCard = sgs.CreateSkillCard{
	name = "luajiance" ,
	target_fixed = false ,
	will_throw = false ,
	filter = function(self, targets, to_select)
		if #targets == 0 then
			return true
		elseif #targets == 1 then
			return to_select:objectName() ~= targets[1]:objectName()
		elseif #targets == 2 then
			return false
		end
	end ,
	feasible = function(self, targets)
		return #targets == 2
	end ,
	on_use = function(self, room, source, targets)
		if #targets < 2 then return end
		local to1 = sgs.Sanguosha:getCard(self:getSubcards():first())
		local to2 = sgs.Sanguosha:getCard(self:getSubcards():at(1))
		decideTrick(room, to1, to2, source, targets[1], targets[2])
	end
}
luajiance = sgs.CreateViewAsSkill{
	name = "luajiance",
	n = 2,
	view_filter = function(self, selected, to_select)
		if sgs.Self:isCardLimited(to_select, sgs.Card_MethodUse) then return false end
		if #selected == 0 then return true end
		if to_select:objectName() ~= selected[1]:objectName() then return false end
		return true
	end,
	view_as = function(self, cards)
		if #cards <= 1 then return end
		local card = luajianceCard:clone()
		for _, c in ipairs(cards) do
			card:addSubcard(c)
		end
		return card
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#luajiance") and not player:isKongcheng()
	end
}
luajiance2 = sgs.CreateFilterSkill{
	name = "#luajiance",
	view_filter = function(self,to_select)
		--if not sgs.Self:hasSkill("luajiance") then return false end
		return to_select:isKindOf("Slash") or to_select:isKindOf("Jink") or to_select:isKindOf("Analeptic") or to_select:isKindOf("Hui") or to_select:isKindOf("Ofuda")
	end,
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("peach", card:getSuit(), card:getNumber())
		slash:setSkillName("luajiance")
		local _card = sgs.Sanguosha:getWrappedCard(card:getId())
		_card:takeOver(slash)
		_card:setModified(true)
		return _card
	end
}
luajiance3 = sgs.CreateTriggerSkill{
	name = "#luajiance2",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.PreCardUsed},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.PreCardUsed then
			local use = data:toCardUse()
			local card = use.card
			if use.from:hasFlag("jianceAOE") then
				use.to = sgs.SPlayerList()
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if p:hasFlag("jianceAOEs") then use.to:append(p) end    
				end
			end
			data:setValue(use)
			return false
		end
	end,
	can_trigger = function(self, target)
		return target
	end,
}
luasushenCard = sgs.CreateSkillCard{
	name = "luasushen",
	filter = function(self, targets, to_select)
		return to_select:isWounded()
	end,
	on_effect = function(self, effect)
		local x = 4 - effect.to:getHp()
		x = math.min(effect.to:getLostHp(), x)
		effect.from:getRoom():recover(effect.to, sgs.RecoverStruct(effect.from, nil, x))
		effect.to:drawCards(x)
		effect.from:loseAllMarks("@sushen")
	end
}
luasushenVS = sgs.CreateZeroCardViewAsSkill{
	name = "luasushen",
	view_as = function(self, cards)
		return luasushenCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@sushen") >= 1
	end
}
luasushen = sgs.CreateTriggerSkill{
	name = "luasushen",
	frequency = sgs.Skill_Limited,
	events = {sgs.GameStart},
	limit_mark = "@sushen",
	view_as_skill = luasushenVS,
	on_trigger = function()
	end
}
rh_erin:addSkill(luajiance)
rh_erin:addSkill(luajiance2)
rh_erin:addSkill(luajiance3)
rh_erin:addSkill(luasushen)

luayinxi = sgs.CreateTriggerSkill{
    name = "luayinxi",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}

luawangyue = sgs.CreateTriggerSkill{
	name = "luawangyue",
	global = true,
    frequency = sgs.Skill_Limited,
    limit_mark = "@luawangyue",
	events = {sgs.DamageInflicted},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.DamageInflicted) then
			local kagerou = player:getRoom():findPlayerBySkillName("luawangyue")
			if not kagerou then return false end
			--if player:objectName() ~= kagerou:objectName() then return false end
			if not kagerou:isAlive() then return false end 
			local damage = data:toDamage()
			if damage.damage >= damage.to:getHp() then
				if damage.from and damage.from:objectName() == kagerou:objectName() then
					if kagerou:getMark("@luawangyue") > 0 and room:askForSkillInvoke(kagerou, "luawangyueA") then 
							local RRRole = dealMark(room, damage.to)
							local general1 = damage.to:getTag("ChaosHero"):toString()
							if damage and damage.from and damage.from:getTag("ChaosRole"):toString()
								and damage.to:objectName() ~= damage.from:objectName() and damage.to:getRole() ~= "lord" then
								if damage.from:getTag("ChaosRole"):toString() == "chaosX" then
									local Hp = damage.from:getHp()
									damage.from:setTag("ChaosRole", sgs.QVariant(RRRole))  
									dealMark(room, damage.from)
									local Hero = damage.from:getTag("ChaosHero"):toString()
									local RandomAll = {}
									local ori_acq = {}
									table.insert(ori_acq, Hero) 
									ChangeHero(room, damage.from, Hero, RandomAll, ori_acq)
									room:changeHero(damage.from, general1, false, true, true, false)
									adjusetHP2(room, damage.from, HP)										
								end 
							end 
							local Role = damage.to:getTag("ChaosRole"):toString()
							if Role == "civilian" or Role == "chaosX" then
								damage.to:setRole("renegade") 
								damage.to:setShownRole(true)
								room:notifyProperty(damage.to, damage.to, "role")	
								if damage and damage.from and damage.from:isAlive() and damage.from:objectName() ~= damage.to:objectName() then
									damage.from:drawCards(2)
								end 
							else
								damage.to:setRole(Role) 
								damage.to:setShownRole(true)
								room:notifyProperty(damage.to, damage.to, "role")
							end 
							CheckWin(room)			 
						room:killPlayer(damage.to, damage)
						room:removePlayerMark(kagerou, "@luawangyue")  
						return false
					end
					if kagerou:getMark("@luawangyue") > 0 and room:askForSkillInvoke(kagerou, "luawangyueB") then 
						room:broadcastSkillInvoke(self:objectName(), 2) 
						room:removePlayerMark(kagerou, "@luawangyue")   
						return true					
					end 
				else
					if kagerou:getMark("@luawangyue") > 0 and room:askForSkillInvoke(kagerou, "luawangyueB") then 
						room:broadcastSkillInvoke(self:objectName(), 2) 
						room:removePlayerMark(kagerou, "@luawangyue")   
						return true					
					end 					
				end 
			end 
		end
	end
}
kagerou:addSkill(luayinxi)
kagerou:addSkill(luawangyue)
 
luaxiongshouCard = sgs.CreateSkillCard{
	name = "luaxiongshou" ,
	will_throw = false,
	target_fixed = true, 
	on_use = function(self, room, source, targets)
		local id = self:getSubcards():at(0)
		source:addToPile("luaxiongshou", id, true)
		room:setPlayerMark(source, "luaxiongshouX", id + 1)
		for _, cardX in sgs.qlist(source:getHandcards()) do  
			room:clearCardFlag(cardX, source) 
		end 
		room:filterCards(source, source:getHandcards(), true)
		for _, cardX in sgs.qlist(source:getHandcards()) do 
			if cardX:isKindOf("BasicCard") then
				room:setCardFlag(cardX, "luaxiongshou" .. id) 
			end 
		end 
		room:filterCards(source, source:getHandcards(), true)
		 
	end
}
luaxiongshouVS = sgs.CreateViewAsSkill{
	name = "luaxiongshou", 
	n = 1,  
	view_filter = function(self, selected, to_select)
		return #selected < 1 and (not to_select:isKindOf("EquipCard"))
	end, 
	view_as = function(self, cards) 
		if #cards == 1 then 
			local card = luaxiongshouCard:clone()
			for _, cd in ipairs(cards) do
				card:addSubcard(cd)
			end	
			return card		
		end 
	end, 
	enabled_at_play = function(self, player)
		return false
	end,
	enabled_at_response = function(self, player, pattern)
        return pattern == "@@luaxiongshou"
	end,
}
luaxiongshou = sgs.CreateTriggerSkill{
	name = "luaxiongshou" , 
	global = false,
	view_as_skill = luaxiongshouVS,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseStart, sgs.DamageForseen, sgs.CardsMoveOneTime} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart and player:getPhase() == sgs.Player_Start then  
			if not player:isKongcheng() then 
				if room:askForUseCard(player, "@@luaxiongshou", "@luaxiongshou") then 	 
				else
					local acard = room:askForCard(player, "^EquipCard|.|.|.!", "@luaxiongshou", sgs.QVariant(), sgs.Card_MethodNone) 
					if acard then  
						if not acard:isKindOf("EquipCard") then 
							player:addToPile("luaxiongshou", acard:getEffectiveId(), true)
							room:setPlayerMark(player, "luaxiongshouX", acard:getEffectiveId() + 1)
						else
							return false 
						end 
					end 
				end 
				local choices = {"qianxin", "qinxue", "juyi", "baiyin", "fengliang", "zhiji", "wuji", "LuaHebao", "luaguitu", "luajiyuan"}
				local choice = room:askForChoice(player, "luaxiongshou", table.concat(choices, "+"))
				if not player:hasSkill(choice) then room:acquireSkill(player, choice) end
			end 
		elseif event == sgs.DamageForseen then  
			local damage = data:toDamage()
            if damage.to and (damage.to:hasSkill("luaxiongshou")) then
                if player:objectName() ~= damage.to:objectName() then return false end
                if damage.from and damage.from:getHp() == 1 then 
					for _, shikieki in sgs.qlist(room:findPlayersBySkillName("luashuojiao")) do 
						if shikieki and shikieki:isAlive() then return false end 
					end 
                    damage.damage = damage.damage + 1
                    room:notifySkillInvoked(damage.to, "luaxiongshou") 
                    data:setValue(damage)
                end
            end
		elseif event == sgs.CardsMoveOneTime then    
			local move = data:toMoveOneTime()
			if move.to and move.to:objectName() == player:objectName() and player:hasSkill("luaxiongshou") and not move.card_ids:isEmpty() then
				if move.to_place == sgs.Player_PlaceHand and player:getMark("luaxiongshouX") > 0 then		  
					for _, id in sgs.qlist(move.card_ids) do
						local cardX = sgs.Sanguosha:getCard(id)
						if room:getCardOwner(id):objectName() == player:objectName() and room:getCardPlace(id) == sgs.Player_PlaceHand
							and cardX:isKindOf("BasicCard") then
							local idX = player:getMark("luaxiongshouX") - 1
							room:setCardFlag(cardX, "luaxiongshou" .. idX) 
							room:filterCards(player, player:getHandcards(), true)
						end
					end				
				end 
			end 
		end
		return false
	end 
} 
luayuanlue = sgs.CreateFilterSkill{
	name = "luayuanlue", 
	view_filter = function(self,to_select)  
		return (to_select:isKindOf("BasicCard"))
	end,
	view_as = function(self, originalCard)
		for id = 0, 600 do
			if originalCard:hasFlag("luaxiongshou" .. id) then
				local acard = sgs.Sanguosha:getCard(id)
				if acard then 
					local slash = sgs.Sanguosha:cloneCard(acard:objectName(), acard:getSuit(), acard:getNumber())
					slash:setSkillName("luayuanlue")
					local card = sgs.Sanguosha:getWrappedCard(originalCard:getId())
					card:takeOver(slash)
					return card
				end 
			end 
		end 
		return originalCard
	end
}
luayuanlue2 = sgs.CreateProhibitSkill{
	name = "#luayuanlue" ,
	is_prohibited = function(self, from, to, card)
		return from:hasSkill("luayuanlue") and to:hasFlag("luayuanlueTargetED" .. card:objectName()) and to:hasFlag("luayuanlueTargetED") 
			and not card:isKindOf("SkillCard") and card:getSkillName() == "luayuanlue" 
	end
} 
luayuanlue4 = sgs.CreateTriggerSkill{
	name = "#luayuanlue2" ,
	global = true,
	events = {sgs.EventPhaseEnd, sgs.TargetConfirmed, sgs.CardUsed} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom() 
		if event == sgs.EventPhaseEnd then 
			for _, p in sgs.qlist(room:getAlivePlayers()) do 
				room:setPlayerFlag(p, "-luayuanlueTargetED")
			end
		elseif event == sgs.TargetConfirmed then  
			local use = data:toCardUse()
			if not use.from then return false end
			local room = use.from:getRoom()
			if (player:objectName() ~= use.from:objectName()) or (not player:hasSkill("luayuanlue")) then return false end
			if player:getPhase() ~= sgs.Player_Play then return false end
			if use.card and use.card:getSkillName() == "luayuanlue" then 
				for _, t in sgs.qlist(use.to) do
					room:setPlayerFlag(t, "luayuanlueTargetED")
					room:setPlayerFlag(t, "luayuanlueTargetED" .. use.card:objectName())
				end 
			end 
		elseif event == sgs.CardUsed then  
			local use = data:toCardUse()
			if not use.from then return false end
			local room = use.from:getRoom()
			if (player:objectName() ~= use.from:objectName()) or (not player:hasSkill("luayuanlue")) then return false end
			if use.card and use.card:getSkillName() == "luayuanlue" then 
				local choices = {"bear", "luaduhuo", "luamaoyou"} 
				local choice = room:askForChoice(use.from, "luayuanlue", table.concat(choices, "+"))
				if choice then
					use.from:gainMark("@" .. choice) 
				end 
			end  
		end 
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}
luayuanluexxCard = sgs.CreateSkillCard{
	name = "luayuanluexx",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select) 
		local card = sgs.Sanguosha:getCard(self:getSubcards():at(0))
		card:setSkillName("luayuanlue")
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		return card and card:targetFilter(qtargets, to_select, sgs.Self) and not sgs.Self:isProhibited(to_select, card, qtargets)
	end, 
	on_use = function(self, room, source, targets)
		local card = sgs.Sanguosha:getCard(self:getSubcards():at(0))
		card:setSkillName("luayuanlue")
		room:useCard(sgs.CardUseStruct(card, source, targets[1]))
	end,
}
luayuanluexx = sgs.CreateOneCardViewAsSkill{
	name = "luayuanluexx&",
	view_filter = function(self, card)
		for id = 0, 600 do
			if card:hasFlag("luaxiongshou" .. id) then
				return not card:isKindOf("EquipCard") and not card:isKindOf("Jink") and not card:isKindOf("Nullification")
					and not card:isKindOf("AOE") and not card:isKindOf("AmazingGrace") and not card:isKindOf("GodSalvation")
					and not card:isKindOf("Collateral")
			end
		end 
		return false
	end, 
	view_as = function(self, card) 
		local skill_card = luayuanluexxCard:clone()
		skill_card:setSkillName("luayuanlue")
		skill_card:addSubcard(card:getId())
		return skill_card
	end
}

taotei:addSkill(luaxiongshou)
taotei:addSkill(luayuanlue)
taotei:addSkill(luayuanlue2)
taotei:addSkill(luayuanlue4)
taotei:addSkill(luayuanluexx)

luazhuanshen = sgs.CreateTriggerSkill{
    name = "luazhuanshen",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
luabiyan = sgs.CreateTriggerSkill{
    name = "luabiyan",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
luahonglian = sgs.CreateTriggerSkill{
    name = "luahonglian",
    events = {sgs.GameStart},
    on_trigger = function()
		-- 这里我施展魔法，就可以不写代码实现这个技能效果
    end
}
sp_akyuu:addSkill(luazhuanshen)
sp_akyuu:addSkill(luabiyan)
sp_akyuu:addSkill(luahonglian)

luazhuanshenx = sgs.CreateTriggerSkill {
	name = "luazhuanshenx", 
	frequency = sgs.Skill_Compulsory, 
	events = { sgs.Damaged, sgs.EventPhaseChanging },
	on_trigger = function(self, event, player, data, room)
		if event == sgs.Damaged then 
			local damage = data:toDamage()
			if damage.damage == 0 then return false end  
			room:recover(player, sgs.RecoverStruct(player, nil, 3))
		else
			local change = data:toPhaseChange()
			if not player:isSkipped(change.to) and change.to == sgs.Player_Draw then
				while player:getHandcardNum() < player:getMaxCards() do 
					local judge = sgs.JudgeStruct()
					judge.pattern = "."
					judge.good = true
					judge.reason = self:objectName()
					judge.who = player 
					room:judge(judge)
				end 
				player:skip(change.to)
			end 
		end 
	end
}
luazhuanshenx2 = sgs.CreateTriggerSkill{
	name = "#luazhuanshenx",
	frequency = sgs.Skill_Compulsory,  
	events = {sgs.FinishJudge},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local judge = data:toJudge()
		local card = judge.card
		local card_data = sgs.QVariant()
		card_data:setValue(card)
		if room:getCardPlace(card:getEffectiveId()) == sgs.Player_PlaceJudge and judge.reason == "luazhuanshenx"
			and player:hasSkill("luazhuanshenx") then
			player:obtainCard(card)
		end
	end
}

luazhuanshenx3 = sgs.CreateTriggerSkill{
	name = "#luazhuanshenx3", 
	frequency = sgs.Skill_Compulsory,  
	priority = 10,
	events = {sgs.EventPhaseStart, sgs.HpRecover, sgs.HpChanged, sgs.Damage, sgs.EnterDying, sgs.MaxHpChanged },
	on_trigger = function(self, event, player, data, room)
		if player:hasSkill("luazhuanshenx") and not player:isWounded() then
			room:killPlayer(player)
		end 
	end

}

luahonglianX = sgs.CreateTriggerSkill{
	name = "#luahonglianX",
	frequency = sgs.Skill_Compulsory,  
	events = {sgs.StartJudge},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local judge = data:toJudge()  
		if judge.reason == "eight_diagram" and player:hasSkill("luahonglian") then
			judge.pattern = ".|club,diamond,heart"
			data:setValue(judge)
		end
	end
}

local function filterTarget(to_select)
	local canBeTarget = false
	for _, card in sgs.qlist(to_select:getHandcards()) do
		if not card:hasFlag("sp_huogongC") then 
			canBeTarget = true 
			break
		end 
	end 
	return canBeTarget
end
luabiyanxCard = sgs.CreateSkillCard{
	name = "luabiyanx", 
	target_fixed = true,
	on_use = function(self, room, source, targets)
		local choice = sgs.IntList() 
		for _, card in sgs.qlist(source:getCards("he")) do
			if (card:isRed() or (card:getSuit() == sgs.Card_Club and source:hasSkill("luahonglian")))
				and not card:isKindOf("BasicCard") then
				choice:append(card:getId())
			end 
		end
		local x = choice:length()
		if x == 0 then return false end 
		local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		dummy:addSubcards(choice)
		room:throwCard(dummy, source, source)

		for i = 1,x do
			if not source:isAlive() then break end 
			local dummy_0 = sgs.Sanguosha:cloneCard("sp_huogong", sgs.Card_NoSuit, 0)
			local available_targets = sgs.SPlayerList()
			for _, to_select in sgs.qlist(room:getAlivePlayers()) do
				if filterTarget(to_select) then
					available_targets:append(to_select)
				end
			end
			if available_targets:length() > 0 then
				local target = room:askForPlayerChosen(source, available_targets, self:objectName(), self:objectName(), true, false)
				if target then
					room:useCard(sgs.CardUseStruct(dummy_0, source, target))
				else
					dummy_0:deleteLater() 
				end 
			else
				dummy_0:deleteLater() 
			end 
		end 

		for _, to_select in sgs.qlist(room:getAlivePlayers()) do
			for _, card in sgs.qlist(to_select:getHandcards()) do 
				room:setCardFlag(card, "-sp_huogongC")
			end 
		end 
	end
}
luabiyanx = sgs.CreateZeroCardViewAsSkill{
	name = "luabiyanx",
	view_as = function(self, cards)
		return luabiyanxCard:clone()
	end,
	enabled_at_play = function(self, player)
		return true
	end,
}



sp_akyuuB:addSkill(luazhuanshenx)
sp_akyuuB:addSkill(luazhuanshenx2)
sp_akyuuB:addSkill(luazhuanshenx3)
sp_akyuuB:addSkill(luahonglian)
sp_akyuuB:addSkill(luahonglianX)
sp_akyuuB:addSkill(luabiyanx)


sgs.LoadTranslationTable{
	["pay7"] = "独树一帜", --注意这里每次要加逗号

	["Rh_junko"] = "纯狐",
	["Rh_junkoA"] = "纯狐",
	["Rh_junkoB"] = "纯狐",
	["#Rh_junko"]= "无名的存在",
	["designer:Rh_junko"] = "Paysage",
	["LuaWeishi"] = "威势",
	[":LuaWeishi"] = "锁定技，若你手牌数不大于体力上限，每当你使用一张牌后，若其点数大于2X，你须回复一点体力或者增加一点体力上限，否则你须流失一点体力。（X为你体力值）。",
	["LuaChunguang"] = "纯光",	
	[":LuaChunguang"] = "锁定技，你使用一张点数不大于2X的牌时，若合法，你须为此牌增加或减少X/2个目标，然后你失去X/2点体力上限（向下取整）（X为你已损失体力值）。",
	["hp"] = "回复一点体力",
	["maxhp"] = "增加一点体力上限",
	["add"] = "增加目标",
	["remove"] = "减少目标",
	["LuaChunguang2"] = "令此牌不能被响应",
	["@Chunguang-add"] = "因“纯光”的效果，你须为这张牌增加一个合法的目标。",
	["@Chunguang-remove"] = "因“纯光”的效果，你须为这张牌减少一个合法的目标。",

	["sakuya"] = "十六夜咲夜",	
	["sakuyaA"] = "十六夜咲夜",
	["sakuyaB"] = "十六夜咲夜",
	["sakuyaC"] = "十六夜咲夜",
	["sakuyaD"] = "十六夜咲夜",
	["sakuyaO"] = "十六夜咲夜",
	["#sakuya"] = "完美潇洒的从者",
	["designer:sakuya"] = "Paysage",	
	["luashiji"] = "时计",
	[":luashiji"] = "每当一名角色于其回合首次牌置于弃牌堆时，你可以流失一点体力值并令其获得之。",	
	["luashijie"] = "世界",	
	["luaweixinsdf"] = "世界",
	[":luaweixinsdf"] = "当你体力值不大于1时，你获得“潇洒”“奇术”。\
	[<i><b>潇洒</b>：弃牌阶段开始时，你可以将手牌补至3张跳过此阶段。</i>]\
	[<i><b>奇术</b>：转化技，①：你可以将一张红色牌当作【杀】【闪】使用；②：你可以将一张黑色牌当作【酒】【无懈可击】使用。</i>]",
	[":luashijie"] = "当你体力值不大于1时，你获得“潇洒”“奇术”。",
	["luaxiaosa"] = "潇洒",
	[":luaxiaosa"] = "弃牌阶段开始时，你可以将手牌补至3张跳过此阶段。",
	["luaqishu"] = "奇术",
	[":luaqishu"] = "转化技，①：你可以将一张红色牌当作【杀】【闪】使用；②：你可以将一张黑色牌当作【酒】【无懈可击】使用。",

	["mai_satono"] = "舞&里乃",
	["#mai_satono"]= "瘋狂的背景舞者",
	["designer:mai_satono"] = "Paysage",
	["illustrator:mai_satono"] = "型K式",
	["luazhuwu"] = "助舞",
	[":luazhuwu"] = "准备阶段，或是你因群体锦囊以外造成伤害后，你可以令一名角色增加一点体力，其下次受到的伤害+1（不可叠加）。",
	["luajiawei"] = "祭舞",
	[":luajiawei"] = "锁定技，每当你受到伤害时，若你体力值不为全场最多且大于轮数，则此伤害-1。",
	["luakuangwang"] = "台风",
	["luakuangwang2"] = "依座次选定角色（点击取消则直接跳过该角色）",
	["@luakuangwang"] = "对刚刚选定的角色使用一张牌（不能对其使用则无事发生）",
	[":luakuangwang"] = "摸牌阶段，你可以改为摸X张牌并依座次对X+1名角色使用一张牌。（X为你体力值）。",

	["luatanxi"] = "檀溪",
	[":luatanxi"] = "准备阶段，你可以展示一名其他角色的手牌，然后其需对你使用一张【杀】（无视距离），或你获得其中的【杀】。",
	["@luaqijinr"] = "请对梦子使用一张【杀】，否则其将获得你手牌中的全部【杀】",	
	["@luatanxi"] = "你可以发动“檀溪”",	
	["~luatanxi"] = "选择技能目标→点击确定",		

	["okina"] = "摩多罗隐岐奈",
	["#okina"]= "究极的绝对秘神",
	["designer:okina"] = "Paysage",
	["luahouhu"] = "后户",
	[":luahouhu"] = " 一名其他角色造成伤害后，你可以流失一点体力，令其摸一张牌并翻面。你的回合内，所有武将背面朝上的角色获得其所有装备并失去其所有技能。",		
	["luamiyi"] = "秘仪",
	["@luamiyi"] = "你可以发动“助舞”",
	["~luamiyi"] = "弃一张牌,选择“助舞”的对象。",
	[":luamiyi"] = "准备阶段，若你体力值为X，你可以令一名角色将手牌调整至X；若你手牌数为X，你可以弃一张牌并发动“助舞”（X为轮数对6取余）。",		
	["luamisheng"] = "秘神",
	["luamiyi1"] = "你可以令一名角色将手牌调整至X。",		
	[":luamisheng"] = "主公技，牌堆切洗时，你回复1点体力并摸2X张牌（X为存活忠臣数）。",
	["forbid_houhu"] = "是否对其关闭“后户”提示",
	["forbid_houhu:yes"] = "是，永久关闭（不可逆操作）",
	["forbid_houhu:no"] = "不，谢谢",

	["rh_flandre"] = "芙兰朵露",
	["rh_flandreA"] = "芙兰朵露",
	["rh_flandreB"] = "芙兰朵露",
	["rh_flandreC"] = "芙兰朵露",
	["rh_flandreD"] = "芙兰朵露",
	["rh_flandreE"] = "芙兰朵露",
	["rh_flandreF"] = "芙兰朵露",
	["rh_flandreG"] = "芙兰朵露",
	["#rh_flandre"]= "禁忌的波动",
	["designer:rh_flandre"] = "Paysage",
	["&fenxing"] = "分形",
	["luajingdan"] = "禁弹",
	["luajingdan2"] = "禁弹",
	[":luajingdan"] = "若你武将牌上没有牌，你可以将三张手牌当【杀】使用（无限制）。以此法使用的牌记作“分形”并置于武将牌上，且可以如手牌般使用或打出。（“分形”牌至多三张，多余须弃置）",
	["luajinji"] = "禁忌",
	[":luajinji"] = "出牌阶段若你手牌较多，或濒死时你“分形”牌较多，你可以交换这两份牌，并于本回合获得“弹幕”。 ",
	["@luajinji2"] = "请选择要进入“分形”牌的手牌（不要多次点选相同的牌）",
	["@luahuimie"] = "你可以发动“毁灭”",
	["~luahuimie"] = "选择你【弹幕】要指定的角色",
	["luajinguox"] = "禁果",
	[":luajinguox"] = "锁定技，你的手牌均视为【桃】，摸牌阶段你摸三张牌。 ",
	
	["skadi"] = "斯卡蒂",
	["#skadi"]= "深海悲歌",
	["designer:skadi"] = "Paysage",
	["lualueying"] = "掠影",
	[":lualueying"] = "出牌阶段结束时，你可以将两张同名牌当做【杀】对攻击范围内的一名角色使用之，此【杀】不可被闪避且伤害+1，然后其摸一张牌。",
	["luelangchao"] = "浪潮",
	[":luelangchao"] = "出牌阶段，你可以弃置一张黑色非基本牌，翻开牌堆顶的三张牌并获得其中的全部基本牌，将余下的置于弃牌堆。",		
	["lueshenhai"] = "深海",
	[":lueshenhai"] = "<b>主公技，锁定技</b>，所有角色使用【杀】造成伤害时，若牌堆及弃牌堆中没有与此牌颜色相同的牌，则此【杀】伤害+1。",

	["mihana"] = "萱野美华",
	["#mihana"]= "青空的公主",
	["designer:mihana"] = "Paysage",
	["lualvzhi"] = "绿荫",
	[":lualvzhi"] = "锁定技，你受到【杀】对你造成的伤害时，你须减少一点体力上限并防止此伤害。你使用或打出一张基本牌后，你回复一点体力。",
	["luashenjun"] = "神军",
	["shenjun"] = "神军",
	[":luashenjun"] = "出牌阶段限一次，若你未损失体力值，你可以弃置一张牌视为对至多X-1名角色使用之（X为你体力值）以此法使用的锦囊牌不能被【无懈可击】响应。",
	["luamingming"] = "命名",
	[":luamingming"] = "主公技，你的回合结束时，若你未损失体力值，你可以令一名非神势力角色获得“守成”“青囊”“筹策”“补益”中的一项，并变更势力为神势力。",
	["luamingming2"] = "请选择要获得技能的角色。",

	["rh_erin"] = "八意永琳",
	["#rh_erin"]= "月之贤者",
	["designer:rh_erin"] = "Paysage",
	["luajiance"] = "兼策",
	[":luajiance"] = "每回合限一次，你可以将两张同名牌当任意非延时类锦囊对两名角色使用（计算限制）。锁定技，你的基本牌均视为【桃】。",
	["luasushen"] = "苏生",
	[":luasushen"] = "限定技，你可以令一名角色回复体力至四，然后其每以此法回复一点体力，其摸一张牌。",
 
	["sp_akyuu"] = "稗田阿求",
	["#sp_akyuu"]= "彼岸红莲",
	["designer:sp_akyuu"] = "Paysage",
	["luazhuanshen"] = "转生",
	[":luazhuanshen"] = "锁定技，你始终跳过摸牌阶段，并判定直到手牌数达到上限为止，获得所有判定牌。锁定技，每当你进入濒死或受到伤害后，你回复3点体力。当你未受伤时计为阵亡。",
	["luabiyan"] = "笔焰",
	[":luabiyan"] = "你可以将你的所有红色非基本牌置于牌堆顶，视为你使用了等量【火攻】。（在此期间，所有角色不能展示相同手牌）",
	["luahonglian"] = "红莲",
	[":luahonglian"] = "锁定技，你的梅花牌计为红色牌。你弃置梅花牌的【火攻】伤害+1。",

	["sp_akyuuB"] = "稗田阿求",
	["#sp_akyuuB"]= "彼岸红莲",
	["designer:sp_akyuuB"] = "Paysage",
	["luazhuanshenx"] = "转生",
	[":luazhuanshenx"] = "锁定技，你始终跳过摸牌阶段，并判定直到手牌数达到上限为止，获得所有判定牌。每当你受到伤害后，你回复3点体力。当你未受伤时计为阵亡。",
	["luabiyanx"] = "笔焰",
	[":luabiyanx"] = "你可以弃置你的所有红色非基本牌，视为你使用了等量【火攻】。（在此期间，所有角色不能展示相同手牌）", 

	["taotei"] = "饕餮尤魔",
	["bear"] = "忍戒",
	["#taotei"]= "刚欲盟主",
	["designer:taotei"] = "Paysage",
	["luaxiongshou"] = "凶兽",
	[":luaxiongshou"] = "锁定技，准备阶段，你须将一张非装备牌置于武将牌上，获得一个觉醒技。锁定技，体力值为1的角色对你造成的伤害+1。",
	["@luaxiongshou"] = "你必须发动“凶兽”",
	["~luaxiongshou"] = "选择一张非装备牌", 
	["luayuanluexx"] = "远略",
	["luayuanlue"] = "远略",
	[":luayuanlue"] = "锁定技，你的基本牌均视为上次“凶兽”所放置的牌。出牌阶段，这些牌可以且仅能对每名角色使用一次，且结算后你获得一枚觉醒技相关的标记。",
	
	["kagerou"] = "今泉影狼",	 
	["#kagerou"] = "桌游大师",		
	["designer:kagerou"] = "Paysage",	
	["luayinxi"] = "影袭",
	[":luayinxi"] = "潜行技，每轮开始时，若你为：●主公，则你调整体力值至3，然后可以指定一名其他角色，发起一轮「你对其造成2点伤害」的投票。●忠臣/反贼，验证一名角色的身份是否为反贼。●其他，补发副将（五选一），然后失去此技能。",
	["luawangyue"] = "望月", 
	["luawangyueA"] = "望月（斩杀）", 
	["luawangyueB"] = "望月（保命）", 
	["luayinxi-invoke"] = "你可以指定一名角色并投票（你对其造成2点伤害），若超过半数的玩家同意则通过。<br/> <b>操作提示</b>: 选择一名角色→点击确定<br/>",
	["luayinxi-agree"] = "今泉影狼要对指定的这名角色造成2点伤害。<br/> <b>操作提示</b>: 点击确定表示支持！点击取消表示反对！<br/>",
	["luayinxiY-invoke"] = "请选择要猜测身份的角色",
	["check1"] = "你对其身份的猜测",
	["check2"] = "正确",
	["check3"] = "不正确",
	["check4"] = "我知道了", 
	[":luawangyue"] = "限定技，选择一项：①令受到你造成致命伤害的角色阵亡；②防止一名角色受到的致命伤害。"

}

return {extension_pay_g}