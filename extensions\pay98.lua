---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON>Lua)
--- Created by 2020.
--- DateTime: 2021/3/23 23:06
---
extension_pay_x = sgs.Package("pay98")
yorihime = sgs.General(extension_pay_x,"yorihime","luayue",8,false,true,false)
urumi = sgs.General(extension_pay_x,"urumi","luadi",5,false,false,false)
mamizou = sgs.General(extension_pay_x,"mamizou$","lualian",3,false,false,false)
murasa = sgs.General(extension_pay_x,"murasa","lualian",3,false,false,false)

luashenwei2 = sgs.CreateTriggerSkill{
	name = "#luashenwei",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageForseen},
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local room = player:getRoom()
		if event == sgs.DamageForseen then
			if damage.to and (damage.to:hasSkill("luashenwei")) and damage.card then    --"yuzhi"
				if (damage.card:isKindOf("Slash") and damage.card:isRed()) or damage.card:isKindOf("yuzhi") then
					damage.damage = damage.damage - 1
					room:notifySkillInvoked(damage.to, "luashenwei")
					data:setValue(damage)
				end
			end
			return false
		end
	end
}
luashenwei = sgs.CreateTriggerSkill{
	name = "luashenwei" ,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damaged, sgs.Damage} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if damage.damage == 0 then return false end
		if ((damage.card and (not damage.card:isKindOf("AOE"))) or not damage.card) and damage.by_user then
			local strBan = room:getLord():getRoom():getTag("yorihime")
			if strBan and strBan:toString() ~= "" then 
				strBan = strBan:toString()
				local general = room:askForGeneral(player, strBan)
				room:loseMaxHp(player, 1)
				
				strBan = strBan:split("+")
				local strBan2 = {}
				for _,general_name in pairs (strBan) do
					if general_name ~= general then
						table.insert(strBan2, general_name)
					end
				end					
				if #strBan2 > 0 then 
					strBan = table.concat(strBan2,"+")
					room:getLord():getRoom():setTag("yorihime", sgs.QVariant(strBan))	
				else
					room:getLord():getRoom():removeTag("yorihime")
				end 
						
				if general then
					local whatSheHad = player:getTag("luashenwei")
					if not whatSheHad or whatSheHad:toString() == "" then 
						player:setTag("luashenwei", sgs.QVariant(general))
					else 
						whatSheHad = whatSheHad:toString()
						whatSheHad = whatSheHad:split("+")
						table.insert(whatSheHad, general)
						whatSheHad = table.concat(whatSheHad,"+")
						player:setTag("luashenwei", sgs.QVariant(whatSheHad))
					end 
					whatSheHad = player:getTag("luashenwei")
					if whatSheHad and whatSheHad:toString() ~= "" and player:hasSkill("luajiangshen")
						and room:askForSkillInvoke(player, "luajiangshen") then 
						local xo = room:getLord():getMark("@clock_time") + 1
						room:setPlayerMark(player, "luajiangshen", xo)
						
						whatSheHad = whatSheHad:toString()
						local general2 = room:askForGeneral(player, whatSheHad)
						whatSheHad = whatSheHad:split("+")
						local whatSheHad2 = {}
						for _,general_name in pairs (whatSheHad) do
							if general_name ~= general2 then
								table.insert(whatSheHad2, general_name)
							end
						end		
						if #whatSheHad2 > 0 then 
							whatSheHad = table.concat(whatSheHad2,"+")
							player:setTag("luashenwei", sgs.QVariant(whatSheHad))	
						else
							player:removeTag("luashenwei")
						end 
						room:writeToConsole("luashenwei AItest")
						if not player:hasSkill("luawushuang") then room:acquireSkill(player, "luawushuang") end
						if not player:hasSkill("lualianpo") then room:acquireSkill(player, "lualianpo") end 
						local GeneralTure = sgs.Sanguosha:getGeneral(general2)
						local gainList = {}
						for _, skill2 in sgs.qlist(GeneralTure:getVisibleSkillList()) do
							if skill2:getFrequency() ~= sgs.Skill_Limited then
								table.insert(gainList, skill2:objectName())
							end 
						end 
						if #gainList > 0 then
							local removeSkill = player:getTag("luajiangshen")
							if removeSkill and removeSkill:toString() ~= "" then 
								removeSkill = removeSkill:toString()
							end
							for _, skill in sgs.qlist(player:getVisibleSkillList()) do
								if skill:objectName() == removeSkill then
									room:handleAcquireDetachSkills(player, "-" .. removeSkill)
									break
								end 
							end 
							player:removeTag("luajiangshen")
							
							local choice = room:askForChoice(player, "luajiangshen", table.concat(gainList, "+"))
							player:setTag("luajiangshen", sgs.QVariant(choice))
							room:acquireSkill(player, choice)
						end
					end 
				end 
			end  
		end
	end
}
luajiangshen = sgs.CreateTriggerSkill{
	name = "luajiangshen" ,
	global = true,
	events = {sgs.TurnStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local toziko = player:getRoom():findPlayerBySkillName("luajiangshen")
		if not toziko then return false end
		local xo = room:getLord():getMark("@clock_time") + 1
		local xo2 = toziko:getMark("luajiangshen")
		room:writeToConsole("luajiangshen test F2 " .. xo .. " " .. xo2)
		if toziko and xo2 > 0 and xo ~= xo2 and xo > 0 then
			room:writeToConsole("luajiangshen test")
			if toziko:hasSkill("luawushuang") then room:handleAcquireDetachSkills(toziko, "-luawushuang") end 
			if toziko:hasSkill("lualianpo") then room:handleAcquireDetachSkills(toziko, "-lualianpo") end 
			
			local removeSkill = toziko:getTag("luajiangshen")
			if removeSkill and removeSkill:toString() ~= "" then 
				removeSkill = removeSkill:toString()
			end
			for _, skill in sgs.qlist(toziko:getVisibleSkillList()) do
				if skill:objectName() == removeSkill then
					room:handleAcquireDetachSkills(toziko, "-" .. removeSkill)
					break
				end 
			end 
			toziko:removeTag("luajiangshen")
		end
	end
}
lualianpoCount2 = sgs.CreateTriggerSkill{
	name = "#lualianpo-count2" ,
	events = {sgs.Death, sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		if event == sgs.Death then
			local death = data:toDeath() 
			if death.who:objectName() ~= player:objectName() then return false end 
			local killer
			if death.damage then
				killer = death.damage.from
			else
				killer = nil
			end
			local current = player:getRoom():getCurrent()
			if killer and current and current:isAlive() and (current:getPhase() ~= sgs.Playr_NotActive) then 
				killer:addMark("lualianpo")
			end
		elseif player:getPhase() == sgs.Player_NotActive then
			for _, p in sgs.qlist(player:getRoom():getAlivePlayers()) do
				p:setMark("lualianpo", 0)
			end
		end
	end,
	can_trigger = function(self,target)
		return target
	end
}

lualianpoDo2 = sgs.CreateTriggerSkill{
	name = "#lualianpo-do2" ,
	events = {sgs.EventPhaseStart},
	priority = 1 ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:getTag("lualianpoInvoke") then
			local target = room:getTag("lualianpoInvoke"):toPlayer()
			room:removeTag("lualianpoInvoke")
			if target and target:isAlive() then 
				room:setPlayerMark(target, "@extra_turn", 1)
				target:gainAnExtraTurn()
				room:setPlayerMark(target, "@extra_turn", 0)
			end
		end
	end,
	can_trigger = function(self, target)
		return target and (target:getPhase() == sgs.Player_NotActive)
	end
}
yorihime:addSkill(luashenwei) 
yorihime:addSkill(luashenwei2) 
yorihime:addSkill(luajiangshen) 
yorihime:addSkill(lualianpoCount2) 
yorihime:addSkill(lualianpoDo2) 

luashuichanCard = sgs.CreateSkillCard{
	name = "luashuichan",
	will_throw = false,
	target_fixed = true,
	handling_method = sgs.Card_MethodNone,
	on_use = function(self, room, source, targets)
		local card_ids = sgs.IntList()
		local X = room:getAlivePlayers():length() - 2
		if X <= 0 then return false end 
		for i = 0, X - 1 do
			card_ids:append(room:getDiscardPile():at(i)) 
		end		
		room:fillAG(card_ids) 
		room:getThread():delay(1500)
		local move = sgs.CardsMoveStruct()
		move.from = nil
		move.from_place = sgs.Player_DiscardPile
		move.to = source
		move.to_place = sgs.Player_PlaceHand
		move.card_ids = card_ids 
		move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, "luashuichan")
		room:moveCardsAtomic(move, true)
		room:broadcastInvoke("clearAG")
		room:clearAG()	 
	end
}
luashuichanVS = sgs.CreateZeroCardViewAsSkill{
	name = "luashuichan",
	n = 0, 
	view_as = function(self, cards)
		return luashuichanCard:clone()
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#luashuichan") and player:getMark("luashuichan") > 0
	end  
}

luashuichan = sgs.CreateTriggerSkill {
	name = "luashuichan", 
	view_as_skill = luashuichanVS,
	events = { sgs.EventPhaseChanging, sgs.EventPhaseEnd, sgs.TurnStart },
	on_trigger = function(self, event, player, data, room)
		local room = player:getRoom()
		if event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()   
			if change.to == sgs.Player_Draw then 
				local X = room:getAlivePlayers():length() - 2
				if not player:isSkipped(change.to) and X >= 0 
					and room:askForSkillInvoke(player, "luashuichan2", data) then
					room:broadcastSkillInvoke(self:objectName())
					room:addPlayerMark(player, "luashuichan") 
					room:addPlayerMark(player, "@luashuichan1") 
					player:skip(change.to)  
				end		  
			end
		elseif event == sgs.EventPhaseEnd then
			if player:getPhase() == sgs.Player_Play then
				room:setPlayerMark(player, "luashuichan", 0) 
			end 
		elseif event == sgs.TurnStart then
			room:setPlayerMark(player, "@luashuichan1", 0)   --用addPlayerMark添加的标记，只能通过setPlayerMark消除掉
		end 
	end
}
luashuichan2 = sgs.CreateTriggerSkill{
	name = "#luashuichan" ,
	global = true ,
	frequency = sgs.Skill_Compulsory ,
	events = {sgs.TargetConfirmed} ,
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		if not use.from then return false end
		if not use.card then return false end
		if use.card:isKindOf("SkillCard") then return false end
		local room = use.from:getRoom()  
		if player:objectName() == use.from:objectName() then 
			for _, p in sgs.qlist(use.to) do
				if p:hasSkill("luashuichan") and p:objectName() ~= use.from:objectName() and p:getMark("@luashuichan1") > 0 then 
					room:damage(sgs.DamageStruct(self:objectName(), use.from, p, 1, sgs.DamageStruct_Normal))
				end 
			end 
		end 
	end
}
luaxieyin = sgs.CreateTriggerSkill{
    name = "luaxieyin", 
    events = {sgs.CardsMoveOneTime},
    on_trigger = function(self, event, player, data, room)
		local move = data:toMoveOneTime() 
		if move.to and move.to:objectName() == player:objectName() and move.to_place == sgs.Player_PlaceHand and not move.card_ids:isEmpty() then
			if player:getHandcardNum() == player:getHp() then 
				local to = room:askForPlayerChosen(player, room:getAlivePlayers(), "luaxieyin", "luaxieyin-invoke", true, true)
				if to then
					room:addPlayerMark(to, "@luaxieyin") 
				end 
				if player:getHandcardNum() == player:getEquips():length() * 2 then
					local discard_ids = room:getDrawPile()
					local i = 0
					for _, id in sgs.qlist(discard_ids) do
						local card = sgs.Sanguosha:getCard(id)
						if card:isKindOf("Hui") then
							local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
							dummy:addSubcard(card) 
							local to2 = room:askForPlayerChosen(player, room:getAlivePlayers(), "luaxieyin", "luaxieyin2-invoke", true, true)
							if to2 then
								to2:obtainCard(dummy)
							else
								dummy:deleteLater()
							end   
							i = 1 + i
						end
						if i > 0 then break end
					end					
				end 
			end
		elseif move.from and move.from:objectName() == player:objectName() and move.from_places:contains(sgs.Player_PlaceHand) and not move.card_ids:isEmpty() then
			if player:getHandcardNum() == player:getHp() then 
				local to = room:askForPlayerChosen(player, room:getAlivePlayers(), "luaxieyin", "luaxieyin-invoke", true, true)
				if to then
					room:addPlayerMark(to, "@luaxieyin") 
				end 	 
				if player:getHandcardNum() == player:getEquips():length() * 2 then
					local discard_ids = room:getDrawPile()
					local i = 0
					for _, id in sgs.qlist(discard_ids) do
						local card = sgs.Sanguosha:getCard(id)
						if card:isKindOf("Hui") then
							local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
							dummy:addSubcard(card) 
							local to2 = room:askForPlayerChosen(player, room:getAlivePlayers(), "luaxieyin", "luaxieyin2-invoke", true, true)
							if to2 then
								to2:obtainCard(dummy)
							else
								dummy:deleteLater()
							end   
							i = 1 + i
						end
						if i > 0 then break end
					end							
				end 				
			end
		end 
	end
}
luaxieyin2 = sgs.CreateTriggerSkill{
	name = "#luaxieyin" ,
	global = true ,
	frequency = sgs.Skill_Compulsory ,
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data) 
		local room = player:getRoom()
		if player:objectName() == room:getCurrent():objectName() then 
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				room:setPlayerMark(player, "@luaxieyin", 0) 
			end 
		end 
	end
}
urumi:addSkill(luashuichan)
urumi:addSkill(luashuichan2)
urumi:addSkill(luaxieyin)
urumi:addSkill(luaxieyin2)

luaqianbianCard = sgs.CreateSkillCard{
	name = "luaqianbian" , 
	will_throw = false,
	target_fixed = true, 
	on_use = function(self, room, source, targets)  
		local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		dummy:addSubcards(self:getSubcards())
		room:moveCardTo(dummy, source, sgs.Player_DrawPile)
		room:setPlayerMark(source, "luaqianbianUsecard", 1)
	end
}
luaqianbianVS = sgs.CreateViewAsSkill{
	name = "luaqianbian" ,
	n = 2 ,
	view_filter = function(self, selected, to_select)
		if #selected >= 2 then return false end  
		return true
	end ,
	view_as = function(self, cards)
		if #cards ~= 2 then return nil end
		local card = luaqianbianCard:clone()
		card:addSubcard(cards[1])
		card:addSubcard(cards[2])
		return card
	end ,
	enabled_at_play = function()
		return false
	end, 
	enabled_at_response = function(self, player, pattern)
		return string.startsWith(pattern, "@@luaqianbian")
	end
}
luaqianbian = sgs.CreateTriggerSkill{
	name = "luaqianbian" ,
	global = true ,
	view_as_skill = luaqianbianVS,  
	events = {sgs.Damage, sgs.EventPhaseEnd, sgs.TurnStart} ,
	on_trigger = function(self, event, mamizou, data)
		local room = mamizou:getRoom()
		if event == sgs.EventPhaseEnd then
			for _, mamizouX in sgs.qlist(room:findPlayersBySkillName("luaqianbian")) do 
				if mamizouX:hasFlag("luaqianbian") then 
					mamizouX:setFlags("-luaqianbian")
					if room:askForSkillInvoke(mamizouX, self:objectName(), data) then 
						mamizouX:drawCards(2) 
						if room:askForUseCard(mamizouX, "@@luaqianbian", "@luaqianbian") then 

						else
							if player:getCardCount(true) >= 2 then

							end 
						end 
					end 
				end 
			end 
		elseif event == sgs.Damage then
			local damage = data:toDamage()
			if damage.damage == 0 then return false end  
			if event == sgs.Damage and (not damage.from or damage.from:objectName() ~= mamizou:objectName()) then return false end 
			if mamizou:hasSkill("luaqianbian") then 
				if not mamizou:hasFlag("luaqianbian") then  
					mamizou:setFlags("luaqianbian")
				end
			end  
		elseif event == sgs.TurnStart then
			for _, mamizouX in sgs.qlist(room:findPlayersBySkillName("luaqianbian")) do 
				if mamizouX:getMark("luaqianbianUsecard") > 0 then
					room:setPlayerMark(mamizouX, "luaqianbianUsecard", 0) 
					while true do
						local strX = "BasicCard+^Jink"
						local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
						if (not slash:isAvailable(mamizouX) or mamizouX:hasUsed("Slash")) and mamizouX:getPhase() == sgs.Player_Play then strX = strX .. "+^Slash" end
						slash:deleteLater()
						if not mamizouX:isWounded() then strX = strX .. "+^Peach" end
						if not sgs.Analeptic_IsAvailable(mamizouX) then strX = strX .. "+^Analeptic" end 
						local X = room:askForUseCard(mamizouX, "TrickCard+^Nullification," .. strX .. ",EquipCard|.|.|hand", "@luaqianbianUse")
						if not X then break end
					end
				end 
			end  
		end
	end 
}
luawanhua = sgs.CreateTriggerSkill{
	name = "luawanhua",	
	events = {sgs.CardUsed, sgs.EventPhaseChanging}, 
	global = true,
	on_trigger = function(self, event, player, data, room)
		local card
		
		local function MoveToPlaceUnknown2DrawPile(id, source)
			local move = sgs.CardsMoveStruct()
			move.from = nil
			move.from_place = sgs.Player_PlaceUnknown
			move.to = nil
			move.to_place = sgs.Player_DrawPile
			move.card_ids = sgs.IntList()
			move.card_ids:append(id)
			move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_UNKNOWN, player:objectName())
			room:moveCardsAtomic(move, true)
			source:drawCards(1)  --算了我真的不想写了饶了我吧 2024年9月12日00:39:22
		end 
		local function removeCalled(tableX, mamizouX) 
			local newTable = {}
			for _, cardName in ipairs(tableX) do
				if mamizouX:getMark("luawanhuaA" .. cardName) == 0 then
					table.insert(newTable, cardName)
				end  
			end 
			return newTable
		end 
		if event == sgs.CardUsed then
			local use = data:toCardUse()
			local card = data:toCardUse().card
			if not use.from then return false end 
			if use.from:objectName() ~= player:objectName() or not use.from:hasSkill(self:objectName()) then return false end 
			if card:isKindOf("EquipCard") and use.from:getMark("luawanhuaEquip") == 0 then
				room:setPlayerMark(use.from, "luawanhuaEquip", 1)
			elseif card:isKindOf("BasicCard") and use.from:getMark("luawanhuaBasic") == 0  then
				room:setPlayerMark(use.from, "luawanhuaBasic", 1)
			elseif card:isKindOf("TrickCard") and use.from:getMark("luawanhuaTrick") == 0  then
				room:setPlayerMark(use.from, "luawanhuaTrick", 1)
			end 
		elseif event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				for _, mamizouX in sgs.qlist(room:findPlayersBySkillName("luaqianbian")) do 
					if mamizouX:getMark("luawanhuaEquip") > 0 and mamizouX:getMark("luawanhuaBasic") > 0 and mamizouX:getMark("luawanhuaTrick") > 0 then
						room:setPlayerMark(mamizouX, "luawanhuaEquip", 0)
						room:setPlayerMark(mamizouX, "luawanhuaBasic", 0)
						room:setPlayerMark(mamizouX, "luawanhuaTrick", 0)
						local targets = sgs.SPlayerList()
						for _, p in sgs.qlist(room:getAlivePlayers()) do
							if p:isKongcheng() then continue end
							targets:append(p)
						end 
						local target = room:askForPlayerChosen(mamizouX, targets, self:objectName(), self:objectName(), false, false)
						if target then 
							local to_throw = room:askForCardChosen(mamizouX, target, "h", self:objectName(), false, sgs.Card_MethodNone)

							local dummyYY = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
							dummyYY:addSubcard(to_throw)
							room:moveCardTo(dummyYY, mamizouX, sgs.Player_DrawPile)
 
							local move = sgs.CardsMoveStruct()
							move.from = nil
							move.from_place = sgs.Player_DrawPile
							move.to = nil
							move.to_place = sgs.Player_PlaceUnknown 
							move.card_ids = sgs.IntList()
							move.card_ids:append(to_throw)
							move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, mamizouX:objectName())
							room:moveCardsAtomic(move, true)   

							local choice1 = room:askForChoice(mamizouX, "luawanhua", "BasicCard+TrickCard+EquipCard")
							local Table1 = {310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324,
							 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346,
							 347, 348, 349, 350, 351}
							local Table2 = {"slash", "jink", "peach", "crossbow", "qinggang_sword", "blade", "spear", "axe", "kylin_bow",
								"eight_diagram", "zixing", "amazing_grace", "god_salvation", "savage_assault", "archery_attack", "duel", "ex_nihilo", "snatch",
								"dismantlement", "collateral", "nullification", "indulgence", "lightning", "ice_sword", "renwang_shield","guding_blade", "vine",
								"analeptic", "thunder_slash", "supply_shortage", "iron_chain", "silver_lion", "fire_attack", "fire_slash", "fan", "wooden_ox",
								"Ofuda", "Hui", "faith_collection", "banquet", "religion_battle", "wanbaochui" }
							 
							if choice1 == "BasicCard" then 
								local basicCards = {"slash","jink","analeptic","peach","Ofuda","Hui"}
								basicCards = removeCalled(basicCards, mamizouX) 
								local choice = room:askForChoice(mamizouX, self:objectName(), basicCards) 
								if choice then
									for ij = 1, #Table2 do
										if Table2[ij] == choice then
											room:setPlayerMark(mamizouX, "luawanhuaA" .. choice, 1)
											MoveToPlaceUnknown2DrawPile(Table1[ij], target)  
										end 
									end 
								end 
							elseif choice1 == "TrickCard" then    
								local type = {}
								table.insert(type, "delay_trick")
								table.insert(type, "single_target_trick") 
								table.insert(type, "multiple_target_trick") 
								local typechoice = room:askForChoice(mamizouX, self:objectName(), table.concat(type, "+"))
								local delay_trick = {"lightning", "indulgence", "supply_shortage", "banquet"}
								delay_trick = removeCalled(delay_trick, mamizouX) 
								local sttrick = {"duel", "ex_nihilo", "snatch", "dismantlement", "collateral", "nullification", "fire_attack", "faith_collection"}
								sttrick = removeCalled(sttrick, mamizouX) 
								local mttrick = {"amazing_grace", "god_salvation", "savage_assault", "archery_attack", "iron_chain", "religion_battle"}
								mttrick = removeCalled(mttrick, mamizouX) 
								local choices = {}
								if typechoice == "delay_trick" then
									choices = delay_trick
								elseif typechoice == "single_target_trick" then
									choices = sttrick
								elseif typechoice == "multiple_target_trick" then
									choices = mttrick
								end
								if #choices == 0 then return false end 
								local pattern_0 = room:askForChoice(mamizouX, self:objectName(), table.concat(choices, "+"))		
								if pattern_0 then 
									for ij = 1, #Table2 do
										if Table2[ij] == pattern_0 then
											room:setPlayerMark(mamizouX, "luawanhuaA" .. pattern_0, 1)
											MoveToPlaceUnknown2DrawPile(Table1[ij], target)  
										end 
									end 
								end 
							elseif choice1 == "EquipCard" then 
								local weapon = {"crossbow", "qinggang_sword", "blade", "spear", "axe", "kylin_bow", "ice_sword", "guding_blade", "fan"}
								weapon = removeCalled(weapon, mamizouX) 
								local armor = {"eight_diagram", "renwang_shield", "vine"} 
								armor = removeCalled(armor, mamizouX) 
								local treasure = {"wanbaochui", "wooden_ox"} 
								treasure = removeCalled(treasure, mamizouX) 
								local type = {}
								table.insert(type, "weapon")
								table.insert(type, "armor") 
								table.insert(type, "treasure") 
								local typechoice = room:askForChoice(mamizouX, self:objectName(), table.concat(type, "+")) 
								if typechoice == "weapon" then
									choices = weapon
								elseif typechoice == "armor" then
									choices = armor 
								elseif typechoice == "treasure" then
									choices = treasure 
								end
								local pattern_0 = room:askForChoice(mamizouX, self:objectName(), table.concat(choices, "+"))		
								if pattern_0 then 
									for ij = 1, #Table2 do
										if Table2[ij] == pattern_0 then
											room:setPlayerMark(mamizouX, "luawanhuaA" .. pattern_0, 1)
											MoveToPlaceUnknown2DrawPile(Table1[ij], target)  
										end 
									end 
								end  
							end 

						end 
					else
						room:setPlayerMark(mamizouX, "luawanhuaEquip", 0)
						room:setPlayerMark(mamizouX, "luawanhuaBasic", 0)
						room:setPlayerMark(mamizouX, "luawanhuaTrick", 0)
					end 
				end  
			end 
		end
	end 
}

luawanhua2 = sgs.CreateTriggerSkill{
	name = "#luawanhua2",
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.BeforeCardsMove, sgs.TargetConfirmed},
	on_trigger = function(self, event, player, data, room)
		local mamizou = room:findPlayerBySkillName("luawanhua")
		if not mamizou or not mamizou:isAlive() then return end 
		if event == sgs.BeforeCardsMove then 
			local move = data:toMoveOneTime()
			if not room:getTag("FirstRound"):toBool() and move.to and move.to:objectName() == player:objectName() 
				and move.reason.m_reason == sgs.CardMoveReason_S_REASON_DRAW and move.reason.m_skillName == "ex_nihilo" then
				if player:getMark("luawanhuaEngine") == 0 then
					mamizou:getRoom():sendCompulsoryTriggerLog(mamizou, "luawanhua")
					mamizou:getRoom():notifySkillInvoked(mamizou, "luawanhua")
					room:addPlayerMark(player, "luawanhuaEngine")
					move.card_ids = sgs.IntList() 
					data:setValue(move)
					player:drawCards(3, "ex_nihilo")
					room:removePlayerMark(player, "luawanhuaEngine") 
				else
					room:removePlayerMark(player, "luawanhuaEngine") 
				end 
			end
		else
			local use = data:toCardUse()
			if (player:objectName() ~= use.from:objectName()) or (not use.to) then return false end
			if (use.card:getSubcards() and (sgs.Sanguosha:getEngineCard(use.card:getEffectiveId()):objectName() ~= use.card:objectName())) 
				or (use.card:isVirtualCard()) then 
				room:addPlayerMark(player, "luawanhuaEngine") 
			end 
		end 
		return false
	end
}

mamizou:addSkill(luaqianbian)
mamizou:addSkill(luawanhua)
mamizou:addSkill(luawanhua2)
 
xuanwo_list = {} 
local function getCardList(intlist)
	local ids = sgs.CardList()
	for _, id in sgs.qlist(intlist) do
		ids:append(sgs.Sanguosha:getCard(id))
	end
	return ids
end
luaxuanwo = sgs.CreateTriggerSkill{
	name = "luaxuanwo", 
	global = true, 
	events = {sgs.TurnStart, sgs.EventPhaseChanging, sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data, room)
		local function getSuitCountInQlist(qListA, suit)
			local n = 0
			for _,id in sgs.qlist(qListA) do
				local card = sgs.Sanguosha:getCard(id)
				if card:getSuit() == suit then
					n = n + 1
				end 
			end 
			n = (n / 2) - 1
			n = math.max(n, 0)
			return math.ceil(n)
		end 
		if event == sgs.TurnStart then
			local kp = room:getCurrent() 
			local xo = room:getLord():getMark("@clock_time") + 1
			for _, murasa in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				local xo2 = murasa:getMark("luaxuanwoTurn")
				if not murasa:hasFlag("luaxuanwoPre") and murasa:objectName() ~= kp:objectName() and (xo ~= xo2 and xo > 0) then 
					local card = room:askForCard(murasa, ".|.|.|.", "@luaxuanwoGive", sgs.QVariant(), sgs.Card_MethodNone)
					if card then
						murasa:setFlags("luaxuanwoPre")
						room:setPlayerMark(murasa, "luaxuanwoTurn", xo)
						room:moveCardTo(card, kp, sgs.Player_PlaceHand, 
							sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_GIVE, murasa:objectName(), kp:objectName(), self:objectName(), ""))	
						
					end 
				end 
			end 
		elseif event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then 
				if (#xuanwo_list > 0) then  
					local ids = sgs.IntList()
					local jink_table = {}
					for _, id in ipairs(xuanwo_list) do
						if room:getCardPlace(id) == sgs.Player_DiscardPile then		
							for _, id2 in ipairs(xuanwo_list) do	
								if sgs.Sanguosha:getCard(id):getSuit() == sgs.Sanguosha:getCard(id2):getSuit() and id ~= id2 then 				
									ids:append(id)						
									table.insert(jink_table, tostring(id))	
									break
								end 
							end 
						end 
					end 
					for _, murasa in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do 
						if not ids:isEmpty() and murasa:hasFlag("luaxuanwoPre") then 	 
							murasa:setFlags("luaxuanwo")			 
							room:setTag("uaxuanwo", sgs.QVariant(table.concat(jink_table, "|")))
							
							room:fillAG(ids)
							local to_get = sgs.IntList()
							local to_throw = sgs.IntList()
							while not ids:isEmpty() do
								local card_id = room:askForAG(murasa, ids, false, "luaxuanwo")
								ids:removeOne(card_id)
								to_get:append(card_id)
								local card = sgs.Sanguosha:getCard(card_id)
								local suit = card:getSuit()
								room:takeAG(murasa, card_id, false)
								local _card_ids = ids
								if getSuitCountInQlist(_card_ids, suit) <= 0 then 
									for i = 0, 150 do--这一句不加的话 涉猎很多牌可能会bug，150可以改，数值越大，越精准，一般和你涉猎的牌数相等是没有bug的
										for _,id in sgs.qlist(_card_ids) do
											local c = sgs.Sanguosha:getCard(id)
											if c:getSuit() == suit then
												ids:removeOne(id)
												room:takeAG(nil, id, false)
												to_throw:append(id)
											end
										end
									end
								end 
							end
							--local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
							if not to_get:isEmpty() then
								--dummy:addSubcards(getCardList(to_get))
								murasa:drawCards(to_get:length())
							end
							--[[
							dummy:clearSubcards()
							if not to_throw:isEmpty() then
								dummy:addSubcards(getCardList(to_throw))
								local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_NATURAL_ENTER, player:objectName(), self:objectName(),"")
								room:throwCard(dummy, reason, nil)
							end]]--
							--dummy:deleteLater()
							room:clearAG()
		
							room:removeTag("uaxuanwo")
						end  
					end 
					
				end 
				for _, murasa in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
					room:setPlayerFlag(murasa, "-luaxuanwo")	
					room:setPlayerFlag(murasa, "-luaxuanwoPre")	
				end 
				xuanwo_list = {}
			end 
		elseif event == sgs.CardsMoveOneTime then	
			local move = data:toMoveOneTime()
			if move.to_place == sgs.Player_DiscardPile or (move.to_place == sgs.Player_PlaceTable and move.reason.m_reason ~= sgs.CardMoveReason_S_REASON_PREVIEW)
				and move.from_places:contains(sgs.Player_PlaceHand) then 
				local Akyuu = room:findPlayerBySkillName("luaxuanwo")
				if Akyuu and not Akyuu:hasFlag("luaxuanwo") then 
					for _, id in sgs.qlist(move.card_ids) do
						if not table.contains(xuanwo_list, id) then
							table.insert(xuanwo_list, id)
						end 
					end 
				end 
			end 
		end 
		return false
	end
}

chuanmaocard = sgs.CreateSkillCard{
	name = "luachuanmao",	
	filter = function(self, targets, to_select)
		return #targets == 0 and to_select:getHandcardNum() + sgs.Self:getHandcardNum() >= 3
			and to_select:objectName() ~= sgs.Self:objectName()
	end,	
	on_effect = function(self, effect)
		local room = effect.from:getRoom() 
		room:setPlayerMark(effect.to, "luachuanmaoEnd", 1) 
		effect.from:addMark("luachuanmaoEndP") 
		local cards_1 = room:askForExchange(effect.to, "luachuanmaoX", 3, 0, false, "@luachuanmaoEx", true)
		local x = 0
		if cards_1 then
			x = cards_1:getSubcards():length() 
		end 
		local cards_2 = sgs.IntList()
		local card_ids = sgs.IntList()
		if x < 3 then 
			if effect.from:getHandcardNum() < 3 - x then 
				room:setPlayerMark(effect.to, "luachuanmaoEnd", 0)
				if effect.from:getMark("luachuanmaoEndP") > 0 then 
					room:setPlayerMark(effect.from, "luachuanmaoEndP", effect.from:getMark("luachuanmaoEndP") - 1)
				end 
				return 
			end 
			local card2e = room:askForExchange(effect.from, self:objectName(), 3 - x, 3 - x, false, "@luachuanmaoEx", false)
			if card2e then 
				cards_2 = card2e:getSubcards()
				room:obtainCard(effect.to, card2e, true) 
				room:setPlayerMark(effect.to, "luachuanmaoEnd", cards_2:length() + 1)
			end 
		end 
		if cards_1 then
			for _, id in sgs.list(cards_1:getSubcards()) do
				card_ids:append(id)
			end  
		end 
		for _, id in sgs.list(cards_2) do
			card_ids:append(id)
		end   
		room:fillAG(card_ids)
		room:getThread():delay()
		room:getThread():delay()
		room:clearAG()
		
	end  
}

luachuanmaoVS = sgs.CreateViewAsSkill{
	name = "luachuanmao",	
	n = 0,	
	view_filter = function()
		return false
	end,	
	view_as = function()
		return chuanmaocard:clone()
	end,	
	enabled_at_play = function()
		return false
	end,
	enabled_at_response = function(self, player, pattern)
        return pattern == "@@luachuanmao"
	end,

}

luachuanmao = sgs.CreateTriggerSkill{
	name = "luachuanmao", 
	global = true, 
	view_as_skill = luachuanmaoVS, 
	events = {sgs.EventPhaseEnd, sgs.CardsMoveOneTime, sgs.EventPhaseChanging}, 
	on_trigger = function(self, event, player, data, room)
		if event == sgs.CardsMoveOneTime then	 
			local move = data:toMoveOneTime()
			--if room:getCurrent():getPhase() ~= sgs.Player_Finish then
				if move.to and move.to:objectName() == player:objectName() and player:hasSkill("luachuanmao") and not move.card_ids:isEmpty() then
					if (move.to_place == sgs.Player_PlaceHand or move.to_place == sgs.Player_PlaceEquip)
						and move.from_places:contains(sgs.Player_DrawPile) and move.reason.m_reason == sgs.CardMoveReason_S_REASON_DRAW 
						and not (move.from_places:contains(sgs.Player_PlaceHand) or move.from_places:contains(sgs.Player_PlaceEquip)) then		 
						player:addMark("luachuanmao", move.card_ids:length()) 

					end 
				end 
			--end 
		elseif event == sgs.EventPhaseEnd then
			for _, murasa in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				if murasa:getMark("luachuanmao") > 1 then 
					if room:askForUseCard(murasa, "@@luachuanmao", "@luachuanmao") then  

					end 
				end 
				room:setPlayerMark(murasa, "luachuanmao", 0)
			end 

		elseif event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then 
				for _, p1 in sgs.qlist(room:getAlivePlayers()) do
					if p1:getMark("luachuanmaoEnd") > 0 then 
						local y = p1:getMark("luachuanmaoEnd") - 1
						room:setPlayerMark(p1, "luachuanmaoEnd", 0)
						for _, p2 in sgs.qlist(room:getAlivePlayers()) do
							if p2:getMark("luachuanmaoEndP") > 0 then 
								local cardA = room:askForExchange(p1, self:objectName(), 3, 3, false, "@luachuanmaoEx2", true)
								if cardA then
									room:obtainCard(p2, cardA, true) 
								else
									if y > 0 then 
										room:loseHp(p1, y)	
									end  
								end 
							end
						end 
					end 
				end
				for _, p2 in sgs.qlist(room:getAlivePlayers()) do
					room:setPlayerMark(p2, "luachuanmaoEndP", 0)

				end 
			end 
		end 
	end
}
murasa:addSkill(luaxuanwo)
murasa:addSkill(luachuanmao)


sgs.LoadTranslationTable {
    ["pay98"] = "四喜临门", --注意这里每次要加逗号
    ["yorihime"] = "绵月依姬",
	["illustrator:yorihime"] = "baba",
    ["designer:yorihime"] = "Paysage/Marisa", 
    ["luashenwei"] = "神威",
    [":luashenwei"] = "锁定技，红【杀】与【弹幕】对你造成的伤害-1。群体锦囊以外，你造成或受到伤害时，你获得一张游戏开始时未被选择的武将牌，并失去一点体力上限。",
    ["luajiangshen"] = "降神",
    [":luajiangshen"] = "你因“神威”获得武将牌时，你可以弃置其中一张，本轮你获得“无双”“连破”以及该武将牌上限定技以外的一个技能（会替换之前依此法获得的技能）。",	
	
	["urumi"] = "牛崎润美",
	["illustrator:urumi"] = "ゾウノセ",
    ["#urumi"]= "牛鬼蛇神",
	["designer:urumi"] = "Paysage",
    ["luashuichan"] = "水产",
    ["luashuichan2"] = "水产(跳过摸牌阶段)",
    [":luashuichan"] = "你可以跳过摸牌阶段，并于下个出牌阶段限一次，你获得弃牌堆顶的X-2张牌（X为存活人数），直到下回合前，使用牌指定你为目标的其他角色对你造成一点伤害。",
	["luaxieyin-invoke"] = "你可以发动“携婴(手牌上限-1)”<br/> <b>操作提示</b>: 选择一名角色→点击确定<br/>",
	["luaxieyin2-invoke"] = "你可以发动“携婴(获得一张【秽】)”<br/> <b>操作提示</b>: 选择一名角色→点击确定<br/>",
    ["luaxieyin"] = "携婴",
    [":luaxieyin"] = "你手牌数发生变化后，若与体力值相等，你可以令一名角色下回合手牌上限-1。若再与装备数的两倍相等，你令其获得一张【秽】。",	
 
	["mamizou"] = "二岩貒藏",
	["illustrator:mamizou"] = "べらぼう",
    ["#mamizou"]= "佐渡之二岩",
	["designer:mamizou"] = "Paysage",
    ["luaqianbian"] = "千变",
	["~luaqianbian"] = "你可以发动“千变”",
	["@luaqianbian"] = "选择要置于牌堆顶的那些牌",
	["@luaqianbianUse"] = "请使用一张牌",

    [":luaqianbian"] = "你造成伤害的阶段结束时，你可以摸两张牌并将两张牌置于牌堆顶。若如此做，下个回合开始时，你可以使用任意张牌。",

    ["luawanhua"] = "万化",
    [":luawanhua"] = "你使用过三种不同类型的牌的回合结束时，你可以替换一名角色的一张手牌为任意牌（每牌名限一次，花色点数计为黑桃K）。锁定技，所有非转化【无中生有】摸牌数+1。",		
    ["luatuanshou"] = "猯首",
    [":luatuanshou"] = "主公技，准备阶段，你可以失去一项技能，然后获得一个主公技。",		
 
    ["murasa"] = "村纱水蜜",
    ["#murasa"]= "船缚灵",
	["illustrator:murasa"] = "baba",
    ["luaxuanwo"] = "旋涡",
    ["@luaxuanwoGive"] = "你可以交给当前回合角色一张牌来发动“旋涡”",

    [":luaxuanwo"] = "每轮限一次，一名其他角色的回合开始时，你可以交给其一张牌。本回合结束时，此期间从手牌置入弃牌堆的同花色牌每有两张，你摸一张牌。",
    ["luachuanmao"] = "船锚",
    ["luachuanmaoX"] = "船锚",
	["~luachuanmao"] = "你可以发动“船锚”",
	["@luachuanmao"] = "请选择一名其他角色",

    ["@luachuanmaoEx"] = "因“船锚”效果，你需展示至多3张手牌",
    ["@luachuanmaoEx2"] = "因“船锚”效果，你需交还3张手牌，否则要流失体力",
    [":luachuanmao"] = "你摸两张以上牌的阶段结束时，你可以与一名其他角色共展示三张手牌并令其获得之。"
		.. "本回合结束时，其需交还你三张手牌，否则流失X点体力（X为你交给其的手牌数）。",




}
return {extension_pay_x}