# SP稀神探女代码逐行解释

## 📋 概述

本文档将逐行解释SP稀神探女的实现代码，帮助理解每一行代码的具体含义和作用。

## 🎮 武将创建部分

### 扩展包创建

```lua
local sp_package = sgs.Package("sp_package")
```
**解释**：
- `sgs.Package()` - 创建一个新的扩展包
- `"sp_package"` - 扩展包的名称，用于识别这个扩展包
- `local sp_package` - 将创建的扩展包对象存储在变量中

### 武将定义

```lua
sp_uranomiya = sgs.General(sp_package, "sp_uranomiya", "god", 3, false)
```
**解释**：
- `sgs.General()` - 创建一个新武将的函数
- `sp_package` - 武将所属的扩展包
- `"sp_uranomiya"` - 武将的内部ID，程序用来识别这个武将
- `"god"` - 武将的势力，这里是"神"势力
- `3` - 武将的体力值，稀神探女有3点体力
- `false` - 武将的性别，false表示女性，true表示男性

## ⚔️ 诳言技能实现

### 诳言技能卡定义

```lua
local kuangyan_card = sgs.CreateSkillCard{
    name = "kuangyan_card",
    target_fixed = false,
    will_throw = false,
```
**解释**：
- `sgs.CreateSkillCard{}` - 创建一个技能卡，用于实现技能效果
- `name = "kuangyan_card"` - 技能卡的名称
- `target_fixed = false` - 表示这个技能卡需要选择目标，不是固定目标
- `will_throw = false` - 表示使用这个技能卡后不会被弃置

### 目标过滤函数

```lua
filter = function(self, targets, to_select)
    return #targets == 0 and to_select:objectName() ~= sgs.Self:objectName()
end,
```
**解释**：
- `filter` - 过滤函数，决定哪些角色可以成为技能的目标
- `#targets == 0` - 检查已选择的目标数量是否为0，确保只能选择一个目标
- `to_select:objectName()` - 获取当前考虑的角色的名称
- `sgs.Self:objectName()` - 获取使用技能的角色（自己）的名称
- `~=` - 不等于，确保不能选择自己作为目标

### 技能卡使用效果

```lua
on_use = function(self, room, source, targets)
    room:notifySkillInvoked(source, "kuangyan")
```
**解释**：
- `on_use` - 当技能卡被使用时执行的函数
- `room` - 游戏房间对象，包含所有游戏状态
- `source` - 使用技能的角色（稀神探女）
- `targets` - 技能的目标角色列表
- `room:notifySkillInvoked()` - 通知房间中的所有玩家，某个角色发动了某个技能

### 拼点执行

```lua
local success = source:pindian(targets[1], "kuangyan", nil)
```
**解释**：
- `source:pindian()` - 发起拼点的函数
- `targets[1]` - 拼点的对象，即技能的目标角色
- `"kuangyan"` - 拼点的技能名称，用于记录和显示
- `nil` - 不指定拼点使用的牌，让系统自动选择
- `success` - 拼点的结果，true表示稀神探女赢了，false表示输了

### 拼点成功后的处理

```lua
if success then
    local target_card = targets[1]:getTag("KuangyanPindianCard"):toCard()
```
**解释**：
- `if success then` - 如果拼点成功，执行以下代码
- `targets[1]:getTag()` - 从目标角色身上获取一个标记
- `"KuangyanPindianCard"` - 标记的名称，存储了拼点时使用的牌
- `:toCard()` - 将标记转换为卡牌对象

### 检查拼点牌有效性

```lua
if target_card and not target_card:isVirtualCard() then
```
**解释**：
- `target_card` - 检查拼点牌是否存在
- `not target_card:isVirtualCard()` - 检查这张牌不是虚拟卡牌
- 虚拟卡牌是程序生成的临时卡牌，不能用于某些操作

### 选择使用拼点牌的目标

```lua
local target = room:askForPlayerChosen(source, room:getAlivePlayers(), "kuangyan", 
    "@kuangyan-target:" .. targets[1]:objectName(), true)
```
**解释**：
- `room:askForPlayerChosen()` - 让玩家选择一个角色
- `source` - 进行选择的玩家（稀神探女）
- `room:getAlivePlayers()` - 所有存活的角色列表
- `"kuangyan"` - 技能名称
- `"@kuangyan-target:"` - 提示信息的前缀
- `targets[1]:objectName()` - 拼点对象的名称，用于显示提示
- `true` - 表示这个选择是可选的，可以取消

### 强制使用拼点牌

```lua
if target then
    local use = sgs.CardUseStruct()
    use.from = targets[1]
    use.to:append(target)
    use.card = target_card
    room:useCard(use)
```
**解释**：
- `sgs.CardUseStruct()` - 创建一个卡牌使用结构体
- `use.from = targets[1]` - 设置使用者为拼点的对象
- `use.to:append(target)` - 添加使用目标
- `use.card = target_card` - 设置要使用的卡牌
- `room:useCard(use)` - 执行卡牌使用

### 创建乐不思蜀牌

```lua
local indulgence = sgs.Sanguosha:cloneCard("indulgence", target_card:getSuit(), target_card:getNumber())
indulgence:addSubcard(target_card:getId())
indulgence:setSkillName("kuangyan")
```
**解释**：
- `sgs.Sanguosha:cloneCard()` - 复制一张卡牌
- `"indulgence"` - 要复制的卡牌类型，即【乐不思蜀】
- `target_card:getSuit()` - 获取拼点牌的花色
- `target_card:getNumber()` - 获取拼点牌的点数
- `indulgence:addSubcard()` - 将拼点牌作为子卡添加到乐不思蜀中
- `indulgence:setSkillName()` - 设置这张牌是由哪个技能产生的

### 对自己使用乐不思蜀

```lua
use.to.clear()
use.to:append(targets[1])
use.card = indulgence
room:useCard(use)
```
**解释**：
- `use.to.clear()` - 清空之前的目标列表
- `use.to:append(targets[1])` - 将拼点对象设为目标（对自己使用）
- `use.card = indulgence` - 设置要使用的卡牌为乐不思蜀
- `room:useCard(use)` - 执行卡牌使用

## 🎯 诳言视为技能

```lua
kuangyan = sgs.CreateZeroCardViewAsSkill{
    name = "kuangyan",
    view_as = function()
        return kuangyan_card:clone()
    end,
    enabled_at_play = function(self, player)
        return false
    end,
    enabled_at_response = function(self, player, pattern)
        return pattern == "@@kuangyan"
    end
}
```
**解释**：
- `sgs.CreateZeroCardViewAsSkill{}` - 创建一个不需要选择手牌的视为技能
- `name = "kuangyan"` - 技能名称
- `view_as` - 返回要视为使用的卡牌，这里是诳言技能卡
- `enabled_at_play` - 在出牌阶段是否可用，这里设为false
- `enabled_at_response` - 在响应阶段是否可用
- `pattern == "@@kuangyan"` - 只有在特定模式下才能使用

## 🔄 诳言触发技能

```lua
kuangyan_trigger = sgs.CreateTriggerSkill{
    name = "#kuangyan_trigger",
    events = {sgs.EventPhaseStart},
    view_as_skill = kuangyan,
    can_trigger = function(self, target)
        return target and target:isAlive() and target:getPhase() == sgs.Player_Play
    end,
```
**解释**：
- `sgs.CreateTriggerSkill{}` - 创建一个触发技能
- `name = "#kuangyan_trigger"` - 技能名称，#表示这是隐藏的辅助技能
- `events = {sgs.EventPhaseStart}` - 监听的事件，这里是阶段开始事件
- `view_as_skill = kuangyan` - 关联的视为技能
- `can_trigger` - 判断是否可以触发的函数
- `target:isAlive()` - 检查目标角色是否存活
- `target:getPhase() == sgs.Player_Play` - 检查是否是出牌阶段

### 触发效果

```lua
on_trigger = function(self, event, player, data, room)
    for _, p in sgs.qlist(room:getOtherPlayers(player)) do
        if p:hasSkill("kuangyan") and p:canPindian(player) then
            room:askForUseCard(p, "@@kuangyan", "@kuangyan-pindian:" .. player:objectName())
        end
    end
    return false
end,
global = true
```
**解释**：
- `for _, p in sgs.qlist(room:getOtherPlayers(player))` - 遍历除当前玩家外的所有其他玩家
- `p:hasSkill("kuangyan")` - 检查玩家是否拥有诳言技能
- `p:canPindian(player)` - 检查是否可以与当前玩家拼点
- `room:askForUseCard()` - 询问玩家是否要使用技能
- `"@@kuangyan"` - 技能使用模式
- `"@kuangyan-pindian:"` - 提示信息
- `return false` - 返回false表示不阻止其他技能的触发
- `global = true` - 表示这是全局技能，可以监听所有角色的事件

## 🏹 天矢技能实现

```lua
tianshi = sgs.CreateTriggerSkill{
    name = "tianshi",
    events = {sgs.Pindian, sgs.EventPhaseEnd},
    frequency = sgs.Skill_Limited,
    limit_mark = "@tianshi",
```
**解释**：
- `name = "tianshi"` - 技能名称
- `events = {sgs.Pindian, sgs.EventPhaseEnd}` - 监听两个事件：拼点和阶段结束
- `frequency = sgs.Skill_Limited` - 技能频率为限定技
- `limit_mark = "@tianshi"` - 限定技的标记名称

### 拼点事件处理

```lua
if event == sgs.Pindian then
    local pindian = data:toPindian()
    if pindian.from:hasSkill(self:objectName()) or pindian.to:hasSkill(self:objectName()) then
        if not room:getDiscardPile():isEmpty() then
            local card_id = room:getDiscardPile():first()
            room:moveCardTo(sgs.Sanguosha:getCard(card_id), nil, sgs.Player_DrawPile, false)
        end
    end
```
**解释**：
- `if event == sgs.Pindian` - 如果触发的事件是拼点
- `data:toPindian()` - 将事件数据转换为拼点数据结构
- `pindian.from` - 拼点的发起者
- `pindian.to` - 拼点的目标
- `hasSkill(self:objectName())` - 检查是否拥有天矢技能
- `room:getDiscardPile():isEmpty()` - 检查弃牌堆是否为空
- `room:getDiscardPile():first()` - 获取弃牌堆顶的牌
- `room:moveCardTo()` - 移动卡牌到指定位置
- `sgs.Player_DrawPile` - 目标位置是牌堆
- `false` - 移动到牌堆底部

### 限定技触发条件检查

```lua
elseif event == sgs.EventPhaseEnd and player:getPhase() == sgs.Player_Play then
    if player:getMark("damage_record_phase") == 0 and player:getMark("card_used_phase") == 0 then
```
**解释**：
- `event == sgs.EventPhaseEnd` - 如果是阶段结束事件
- `player:getPhase() == sgs.Player_Play` - 检查结束的是否是出牌阶段
- `player:getMark("damage_record_phase") == 0` - 检查本阶段是否没有造成伤害
- `player:getMark("card_used_phase") == 0` - 检查本阶段是否没有使用过牌

### 限定技执行

```lua
for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
    if p:getMark("@tianshi") > 0 and room:askForSkillInvoke(p, self:objectName(), data) then
        room:removePlayerMark(p, "@tianshi")
        room:notifySkillInvoked(p, self:objectName())
```
**解释**：
- `room:findPlayersBySkillName()` - 找到所有拥有天矢技能的角色
- `p:getMark("@tianshi") > 0` - 检查是否还有限定技标记
- `room:askForSkillInvoke()` - 询问是否发动技能
- `room:removePlayerMark()` - 移除限定技标记
- `room:notifySkillInvoked()` - 通知技能被发动

### 强制使用牌堆底的牌

```lua
local can_use = true
while can_use and not room:getDrawPile():isEmpty() do
    local card_id = room:getDrawPile():last()
    local card = sgs.Sanguosha:getCard(card_id)
    
    if not player:isCardLimited(card, card:getHandlingMethod()) then
        local use = sgs.CardUseStruct()
        use.from = player
        use.to:append(player)
        use.card = card
        
        if card:isKindOf("BasicCard") or card:isNDTrick() then
            if card:isAvailable(player) then
                room:useCard(use)
            else
                can_use = false
            end
        else
            can_use = false
        end
    else
        can_use = false
    end
end
```
**解释**：
- `can_use = true` - 设置循环控制变量
- `while can_use and not room:getDrawPile():isEmpty()` - 当可以使用且牌堆不为空时循环
- `room:getDrawPile():last()` - 获取牌堆底的牌
- `sgs.Sanguosha:getCard(card_id)` - 根据ID获取卡牌对象
- `player:isCardLimited()` - 检查角色是否被限制使用这张牌
- `card:getHandlingMethod()` - 获取卡牌的处理方式
- `use.from = player` - 设置使用者
- `use.to:append(player)` - 设置目标为自己
- `card:isKindOf("BasicCard")` - 检查是否是基本牌
- `card:isNDTrick()` - 检查是否是非延时锦囊牌
- `card:isAvailable(player)` - 检查角色是否可以使用这张牌
- `room:useCard(use)` - 执行卡牌使用
- `can_use = false` - 设置为false停止循环

## 🤖 AI代码解释

### 诳言AI使用逻辑

```lua
sgs.ai_skill_use["@@kuangyan"] = function(self, prompt, method)
    local target = self.player:getTag("KuangyanTarget"):toPlayer()
    if not target then return "." end
```
**解释**：
- `sgs.ai_skill_use["@@kuangyan"]` - 定义诳言技能的AI使用函数
- `self` - AI对象，包含AI的各种方法和数据
- `prompt` - 提示信息
- `method` - 使用方法
- `self.player:getTag("KuangyanTarget"):toPlayer()` - 获取拼点的目标角色
- `if not target then return "." end` - 如果没有目标，返回"."表示不使用技能

### AI判断逻辑

```lua
if self:isFriend(target) then return "." end

if self.player:getHandcardNum() <= 2 then return "." end

if target:getHandcardNum() == 0 then
    return "@kuangyan_card"
end
```
**解释**：
- `self:isFriend(target)` - 判断目标是否是友方角色
- `return "."` - 返回"."表示不使用技能
- `self.player:getHandcardNum()` - 获取AI角色的手牌数量
- `<= 2` - 如果手牌数量小于等于2张
- `target:getHandcardNum() == 0` - 如果目标没有手牌
- `return "@kuangyan_card"` - 返回技能卡名称表示使用技能

### AI拼点牌选择

```lua
local cards = self.player:getHandcards()
local max_card = self:getMaxCard()
if max_card and max_card:getNumber() >= 10 then
    return "@kuangyan_card"
end
```
**解释**：
- `self.player:getHandcards()` - 获取AI角色的所有手牌
- `self:getMaxCard()` - 获取手牌中点数最大的牌
- `max_card:getNumber()` - 获取卡牌的点数
- `>= 10` - 如果点数大于等于10
- 这表示AI认为有大牌时值得拼点

### 诳言目标选择AI

```lua
sgs.ai_skill_playerchosen.kuangyan = function(self, targets)
    local target = self.player:getTag("KuangyanTarget"):toPlayer()
    if not target then return nil end
```
**解释**：
- `sgs.ai_skill_playerchosen.kuangyan` - 定义诳言技能的目标选择AI
- `targets` - 可选择的目标列表
- `return nil` - 返回nil表示不选择任何目标

### AI目标分类

```lua
local enemies = {}
local friends = {}

for _, p in sgs.qlist(targets) do
    if self:isEnemy(p) then
        table.insert(enemies, p)
    elseif self:isFriend(p) then
        table.insert(friends, p)
    end
end
```
**解释**：
- `local enemies = {}` - 创建敌人列表
- `local friends = {}` - 创建友方列表
- `for _, p in sgs.qlist(targets)` - 遍历所有可选择的目标
- `self:isEnemy(p)` - 判断是否是敌人
- `table.insert(enemies, p)` - 将敌人添加到敌人列表
- `self:isFriend(p)` - 判断是否是友方

### AI目标选择策略

```lua
if #enemies > 0 then
    self:sort(enemies, "threat")
    return enemies[1]
end

return self.player
```
**解释**：
- `#enemies > 0` - 如果敌人列表不为空
- `self:sort(enemies, "threat")` - 按威胁程度排序敌人
- `return enemies[1]` - 返回威胁最大的敌人
- `return self.player` - 如果没有敌人，选择自己

### 天矢AI发动判断

```lua
sgs.ai_skill_invoke.tianshi = function(self, data)
    local target = data:toPlayer()

    if self:isEnemy(target) then
        return true
    end
```
**解释**：
- `sgs.ai_skill_invoke.tianshi` - 定义天矢技能的AI发动函数
- `data:toPlayer()` - 从事件数据中获取目标角色
- `self:isEnemy(target)` - 如果目标是敌人
- `return true` - 返回true表示发动技能

### AI友方保护逻辑

```lua
if self:isFriend(target) then
    if target:getHp() == 1 then
        return false
    end

    if target:getHandcardNum() >= 3 then
        return true
    end
end
```
**解释**：
- `self:isFriend(target)` - 如果目标是友方
- `target:getHp() == 1` - 如果友方只有1点体力
- `return false` - 不发动技能，保护友方
- `target:getHandcardNum() >= 3` - 如果友方手牌充足
- `return true` - 可以发动技能

## 📝 翻译文件解释

### 扩展包翻译

```lua
return {
    ["sp_package"] = "SP包",
    ["sp_uranomiya"] = "SP稀神探女",
    ["#sp_uranomiya"] = "天矢之巫女",
```
**解释**：
- `return {}` - 返回一个包含所有翻译的表
- `["sp_package"]` - 扩展包的内部名称
- `"SP包"` - 扩展包的中文显示名称
- `["sp_uranomiya"]` - 武将的内部名称
- `"SP稀神探女"` - 武将的中文显示名称
- `["#sp_uranomiya"]` - 武将称号的标识
- `"天矢之巫女"` - 武将的中文称号

### 武将信息翻译

```lua
["designer:sp_uranomiya"] = "QSanguosha-v2开发组",
["illustrator:sp_uranomiya"] = "东方Project",
["cv:sp_uranomiya"] = "无",
```
**解释**：
- `["designer:sp_uranomiya"]` - 武将设计者信息
- `"QSanguosha-v2开发组"` - 设计者的中文名称
- `["illustrator:sp_uranomiya"]` - 武将插画师信息
- `"东方Project"` - 插画来源
- `["cv:sp_uranomiya"]` - 武将配音演员信息
- `"无"` - 表示没有配音

### 技能名称翻译

```lua
["kuangyan"] = "诳言",
[":kuangyan"] = "其他角色的出牌阶段开始时，你可以与其拼点。若你赢，其对你指定的一名角色依此使用拼点牌，将余下的拼点牌当【乐不思蜀】对自身使用。",
```
**解释**：
- `["kuangyan"]` - 技能的内部名称
- `"诳言"` - 技能的中文显示名称
- `[":kuangyan"]` - 技能描述的标识，冒号表示这是技能描述
- 后面的长文本是技能的详细中文描述

### 技能提示翻译

```lua
["@kuangyan-pindian"] = "你可以发动"诳言"与 %src 拼点",
["@kuangyan-target"] = "请选择 %src 拼点牌的目标",
["~kuangyan"] = "选择一名角色→点击确定",
```
**解释**：
- `["@kuangyan-pindian"]` - 技能发动时的提示信息，@表示提示
- `%src` - 占位符，会被替换为实际的角色名称
- `["@kuangyan-target"]` - 选择目标时的提示信息
- `["~kuangyan"]` - 操作提示信息，~表示操作说明

### 技能台词翻译

```lua
["$kuangyan1"] = "哼哼，你上当了~",
["$kuangyan2"] = "我说的话，可不要全信哦~",
```
**解释**：
- `["$kuangyan1"]` - 技能发动时的台词1，$表示台词
- `["$kuangyan2"]` - 技能发动时的台词2
- 游戏会随机选择其中一句台词播放

### 死亡台词翻译

```lua
["~sp_uranomiya"] = "我...我的箭矢...怎么会...",
```
**解释**：
- `["~sp_uranomiya"]` - 武将阵亡时的台词，~加武将名表示死亡台词
- 当武将死亡时会播放这句台词

## 🔧 技能添加到武将

```lua
sp_uranomiya:addSkill(kuangyan)
sp_uranomiya:addSkill(kuangyan_trigger)
sp_uranomiya:addSkill(tianshi)
```
**解释**：
- `sp_uranomiya:addSkill()` - 将技能添加到武将的函数
- `kuangyan` - 诳言的视为技能
- `kuangyan_trigger` - 诳言的触发技能
- `tianshi` - 天矢技能
- 一个武将可以拥有多个技能

## 📦 扩展包完成

```lua
sp_package:addGeneral(sp_uranomiya)

return sp_package
```
**解释**：
- `sp_package:addGeneral()` - 将武将添加到扩展包
- `sp_uranomiya` - 要添加的武将
- `return sp_package` - 返回完成的扩展包，供游戏加载使用

## 💡 总结

这个代码实现了SP稀神探女的完整功能：

1. **武将定义** - 创建了3体力的神势力女性武将
2. **诳言技能** - 实现了拼点和强制使用机制
3. **天矢技能** - 实现了被动效果和限定技效果
4. **AI逻辑** - 提供了智能的技能使用判断
5. **翻译文件** - 提供了完整的中文本地化

每一行代码都有其特定的作用，共同构成了一个完整的武将实现。通过这些解释，您现在应该能够完全理解代码的工作原理了！
