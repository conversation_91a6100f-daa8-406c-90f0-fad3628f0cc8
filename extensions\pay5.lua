
extension_pay_e = sgs.Package("pay5")

remilia = sgs.General(extension_pay_e,"remilia$","luahong",4,false,false,false)
remiliaA = sgs.General(extension_pay_e,"remiliaA$","luahong",4,false,true,true)
remiliaB = sgs.General(extension_pay_e,"remiliaB$","luahong",4,false,true,true)
remiliaC = sgs.General(extension_pay_e,"remiliaC$","luahong",4,false,true,true) 

yuyuko = sgs.General(extension_pay_e,"yuyuko","luayao",3,false,false,false)
yuyukoA = sgs.General(extension_pay_e,"yuyukoA","luayao",3,false,true,true)
yuyukoB = sgs.General(extension_pay_e,"yuyukoB","luayao",3,false,true,true)
yuyukoC = sgs.General(extension_pay_e,"yuyukoC","luayao",3,false,true,true)
yuyukoD = sgs.General(extension_pay_e,"yuyukoD","luayao",3,false,true,true)
yuyukoE = sgs.General(extension_pay_e,"yuyukoE","luayao",3,false,true,true)

meiling = sgs.General(extension_pay_e,"meiling","luahong",4,false,false,false)

patchouli = sgs.General(extension_pay_e,"patchouli","luahong",3,false,false,false)
patchouliA = sgs.General(extension_pay_e,"patchouliA","luahong",3,false,true,true)
patchouliB = sgs.General(extension_pay_e,"patchouliB","luahong",3,false,true,true)
patchouliC = sgs.General(extension_pay_e,"patchouliC","luahong",3,false,true,true)
patchouliD = sgs.General(extension_pay_e,"patchouliD","luahong",3,false,true,true)

iku = sgs.General(extension_pay_e,"iku","luacai",3,false,false,false)
ikuA = sgs.General(extension_pay_e,"ikuA","luacai",3,false,true,true)
ikuB = sgs.General(extension_pay_e,"ikuB","luacai",3,false,true,true)

yukari = sgs.General(extension_pay_e,"yukari$","luayao",3,false,true,true) 

tenshi = sgs.General(extension_pay_e,"tenshi","luacai",3,false,false,false)
prismriver = sgs.General(extension_pay_e,"prismriver","luayao",3,false,false,false)
prismriverA = sgs.General(extension_pay_e,"prismriverA","luayao",3,false,true,true)
prismriverB = sgs.General(extension_pay_e,"prismriverB","luayao",3,false,true,true)

cirno = sgs.General(extension_pay_e,"cirno","luahong",4,false,false,false)
cirnoA = sgs.General(extension_pay_e,"cirnoA","luahong",4,false,true,true)
cirnoB = sgs.General(extension_pay_e,"cirnoB","luahong",4,false,true,true)
cirnoC = sgs.General(extension_pay_e,"cirnoC","luahong",4,false,true,true)
cirnoE = sgs.General(extension_pay_e,"cirnoE","luahong",4,false,true,true)

rumia = sgs.General(extension_pay_e,"rumia","luaxing",3,false,false,false)
rumiaA = sgs.General(extension_pay_e,"rumiaA","luaxing",3,false,true,true)
rumiaB = sgs.General(extension_pay_e,"rumiaB","luaxing",3,false,true,true) 

sp_youmu = sgs.General(extension_pay_e,"sp_youmu","luayao",4,false,false,false,3)
sp_youmuA = sgs.General(extension_pay_e,"sp_youmuA","luayao",4,false,true,true,3)
sp_youmuB = sgs.General(extension_pay_e,"sp_youmuB","luayao",4,false,true,true,3)
sp_youmuC = sgs.General(extension_pay_e,"sp_youmuC","luayao",4,false,true,true,3)

whiterock = sgs.General(extension_pay_e,"whiterock","luayao",4,false,false,false)

aya = sgs.General(extension_pay_e,"aya","luacai",4,false,false,false)
ayaA = sgs.General(extension_pay_e,"ayaA","luacai",4,false,true,true)
ayaB = sgs.General(extension_pay_e,"ayaB","luacai",4,false,true,true)
ayaC = sgs.General(extension_pay_e,"ayaC","luafeng",3,false,true,true)
ayaD = sgs.General(extension_pay_e,"ayaD","luafeng",3,false,true,true)
ayaE = sgs.General(extension_pay_e,"ayaE","luafeng",3,false,true,true)


shikieiki = sgs.General(extension_pay_e,"shikieiki","luadi",3,false,false,false)
tewi = sgs.General(extension_pay_e,"tewi","qun",4,false,false,false)


local function isFriendQ(room, target, player)
	if player:getRole() == "rebel" then
		return target:getRole() == "rebel"
	end
	if player:getRole() == "loyalist" or player:getRole() == "lord" then
		return target:getRole() == "loyalist" or target:getRole() == "lord"
	end
end
luashuojiaoCard = sgs.CreateSkillCard{
	name = "luashuojiao",
	filter = function(self, targets, to_select)
		if (#targets == 0) then
			local cardX = sgs.Sanguosha:getCard(self:getSubcards():first())   
			cardX:setSkillName("luashuojiao")
			if not sgs.Self:isCardLimited(cardX, sgs.Card_MethodUse) and cardX:targetFilter(targetsTable2QList(targets), to_select, sgs.Self)
				and not to_select:isProhibited(sgs.Self, cardX, sgs.Self:getSiblings()) 
				and (sgs.Slash_IsAvailable(sgs.Self) or not cardX:isKindOf("Slash"))
				and not (cardX:isKindOf("Peach") and not to_select:isWounded()) then
					for _,cardJ in sgs.qlist(to_select:getJudgingArea()) do
                        if cardJ and cardJ:isKindOf("Lightning") then
							return false
						end
					end 
				return true
			end 
		end 
		return false
	end, 
	on_effect = function(self, effect)
		local room = effect.to:getRoom()
		local cardX = sgs.Sanguosha:getCard(self:getSubcards():first())   
		cardX:setSkillName("luashuojiao") 
		if not effect.from:isCardLimited(cardX, sgs.Card_MethodUse) and not effect.from:isProhibited(effect.to, cardX) then
			if cardX:isKindOf("AOE") or cardX:isKindOf("AmazingGrace") or cardX:isKindOf("GodSalvation") then
				room:setPlayerFlag(effect.from, "qucaiAOE")
				room:setPlayerFlag(effect.to, "qucaiAOEs")
				cardX:setSkillName("luashuojiao")
				room:useCard(sgs.CardUseStruct(cardX, effect.from, sgs.SPlayerList()), true) 
				room:setPlayerFlag(effect.from, "-qucaiAOE")
				room:setPlayerFlag(effect.to, "-qucaiAOEs")
				 
			else
				cardX:setSkillName("luashuojiao")
				room:useCard(sgs.CardUseStruct(cardX, effect.from, effect.to), true) 
			end
            local lightning = sgs.Sanguosha:cloneCard("lightning", sgs.Card_SuitToBeDecided, -1)
			local thc = sgs.Sanguosha:getCard(room:getDrawPile():first())
            lightning:addSubcard(thc)
			local use_X = sgs.CardUseStruct()
			use_X.card = lightning
			use_X.from = effect.from
			use_X.to:append(effect.to)
			room:useCard(use_X, true)
		end		
	end
}
luashuojiao = sgs.CreateOneCardViewAsSkill{
	name = "luashuojiao",
	view_filter = function(self, card) 
    	return card:isBlack() and not card:isKindOf("Collateral")
	end,
	view_as = function(self,card)
		local skillcard = luashuojiaoCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self,player) 
		return not player:isKongcheng()  
	end
}
luashuojiao2 = sgs.CreateFilterSkill{
    name = "#luashuojiao2",
    view_filter = function(self,to_select)
        local room = sgs.Sanguosha:currentRoom()
        local place = room:getCardPlace(to_select:getEffectiveId())
        return (to_select:isKindOf("Peach"))
    end,
    view_as = function(self, card)
        local slash = sgs.Sanguosha:cloneCard(card:objectName(), sgs.Card_Spade, card:getNumber())
        slash:setSkillName(self:objectName())
        local _card = sgs.Sanguosha:getWrappedCard(card:getId())
        _card:takeOver(slash)
        _card:setModified(true)
        return _card
    end
}
luashuojiao3 = sgs.CreateTriggerSkill{
	name = "#luashuojiao3",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageForseen},
	priority = 9,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()		
		if event == sgs.DamageForseen then
            local damage = data:toDamage()
			if damage.to and damage.nature == sgs.DamageStruct_Thunder then
				for _, shikieki in sgs.qlist(room:findPlayersBySkillName("luashuojiao")) do
					damage.damage = 1 	
					data:setValue(damage) 
					return false 
				end 
			end 
		end 
	end 
}

luashenpani2 = sgs.CreateTriggerSkill{
	name = "#luashenpani",
	global = true, 
	events = {sgs.FinishJudge},
	frequency = sgs.Skill_Compulsory,
	priority = 9,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local judge = data:toJudge()
		local card = judge.card
		local card_data = sgs.QVariant()
		card_data:setValue(card)
		for _, shikieki in sgs.qlist(room:findPlayersBySkillName("luashenpani")) do
			if card:getSuit() == sgs.Card_Heart and room:getCardPlace(card:getEffectiveId()) == sgs.Player_PlaceJudge then
				shikieki:obtainCard(card)
			end
		end 
		return false
	end 
}

luashenpani = sgs.CreateTriggerSkill{
	name = "luashenpani" ,
	events = {sgs.AskForRetrial},
	global = true,
	limit_mark = "@luashenpani", 
	frequency = sgs.Skill_Limited,
	on_trigger = function(self, event, player, data)
		if event == sgs.AskForRetrial then 
			local room = player:getRoom()
			local judge = data:toJudge() 
			if not judge.who then return end 
			for _, shikieki in sgs.qlist(room:findPlayersBySkillName("luashenpani")) do
				if shikieki:getMark("@luashenpani") > 0 and player:objectName() == shikieki:objectName() then
					local card = room:askForCard(shikieki,  ".|.|.|.", "@luashenpani", data, sgs.Card_MethodNone, judge.who, true)
					if card then			 
						room:setPlayerMark(shikieki, "@luashenpani", 0)
						room:broadcastSkillInvoke(self:objectName())
						room:retrial(card, player, judge, self:objectName(), true)	 
					end
				end 
			end
		end		
	end
}
--[[    
	enum Phase { RoundStart, Start, Judge, Draw, Play, Discard, Finish, NotActive, PhaseNone };
    enum Place {
        PlaceHand, PlaceEquip, PlaceDelayedTrick, PlaceJudge,
        PlaceSpecial, DiscardPile, DrawPile, PlaceTable, PlaceUnknown,
        PlaceWuGu, DrawPileBottom
    };
]]--
lualunhuii = sgs.CreateTriggerSkill{
	name = "lualunhuii" ,
	global = true,
	limit_mark = "@lualunhuii", 
	frequency = sgs.Skill_Limited,
	events = {sgs.CardsMoveOneTime } ,
	on_trigger = function(self, event, player, data) 
		local room = player:getRoom()
		local move = data:toMoveOneTime()
		if move.card_ids:length() == 1 and move.from_places:at(0) == sgs.Player_PlaceDelayedTrick and move.from 
			and sgs.Sanguosha:getCard(move.card_ids:at(0)):objectName() == "lightning" then
			for _, shikieki in sgs.qlist(room:findPlayersBySkillName("lualunhuii")) do
				if shikieki:getMark("@lualunhuii") > 0 and shikieki:objectName() == player:objectName() then
				    local _movefrom
					for _, p in sgs.qlist(room:getAlivePlayers()) do
						if move.from:objectName() == p:objectName() then
							_movefrom = p
							break
						end
					end
					if _movefrom then 
						local fromPlayer = sgs.QVariant() -- ai用
						fromPlayer:setValue(_movefrom)
						shikieki:setTag("lualunhuiiF" .. tostring(move.card_ids:at(0)), fromPlayer) 
					end 
				end
			end  
		end
		if move.card_ids:length() == 1 and move.to_place == sgs.Player_PlaceDelayedTrick and move.to 
			and move.from_places:at(0) == sgs.Player_PlaceTable and sgs.Sanguosha:getCard(move.card_ids:at(0)):objectName() == "lightning" then
			for _, shikieki in sgs.qlist(room:findPlayersBySkillName("lualunhuii")) do 
				if shikieki:getMark("@lualunhuii") > 0 and shikieki:objectName() == player:objectName()
					and shikieki:getTag("lualunhuiiF" .. tostring(move.card_ids:at(0))) and shikieki:getTag("lualunhuiiF" .. tostring(move.card_ids:at(0))):toPlayer() then
					local _movefrom = shikieki:getTag("lualunhuiiF" .. tostring(move.card_ids:at(0))):toPlayer()
					shikieki:removeTag("lualunhuiiF" .. tostring(move.card_ids:at(0)))
					local qtargets = sgs.SPlayerList()
					if move.to:getSeat() > _movefrom:getSeat() then
						for _, p in sgs.qlist(room:getAlivePlayers()) do
							if p:getSeat() > _movefrom:getSeat() and p:getSeat() < move.to:getSeat() and p:objectName() ~= shikieki:objectName() then
								qtargets:append(p)
							end 
						end  
					else 
						for _, p in sgs.qlist(room:getAlivePlayers()) do
							if p:getSeat() < move.to:getSeat() and p:objectName() ~= shikieki:objectName() then
								qtargets:append(p)
							end 
						end  		
						for _, p in sgs.qlist(room:getAlivePlayers()) do
							if p:getSeat() > _movefrom:getSeat() and p:objectName() ~= shikieki:objectName() then
								qtargets:append(p)
							end 
						end  	 						
					end 
					if qtargets and qtargets:length() > 0 and shikieki:getMark("@lualunhuii") > 0 and room:askForSkillInvoke(shikieki, self:objectName()) then
						room:setPlayerMark(shikieki, "@lualunhuii", 0)
						for _, p in sgs.qlist(qtargets) do
							room:damage(sgs.DamageStruct(self:objectName(), nil, p, 1, sgs.DamageStruct_Thunder))
						end 
					end 
				end
			end  			
			
		end 
	end
}
shikieiki:addSkill(luashuojiao)
shikieiki:addSkill(luashuojiao2)
shikieiki:addSkill(luashuojiao3)
shikieiki:addSkill(luashenpani)
shikieiki:addSkill(luashenpani2)
shikieiki:addSkill(lualunhuii)


Table2IntList = function(theTable)
	local result = sgs.IntList()
	for i = 1, #theTable, 1 do
		result:append(theTable[i])
	end
	return result
end

Luashenqiang5 = sgs.CreateTriggerSkill{
	name = "#Luashenqiang5" ,
	events = {sgs.PreCardUsed} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local use = data:toCardUse()
		if not use.from then return false end
		if (not player:hasSkill("Luashenqiang2")) or (not use.card:isKindOf("Slash")) then return false end
		local index = 1
		for _, p in sgs.qlist(use.to) do
			local _data = sgs.QVariant()
			_data:setValue(p)
			local Carddata2 = sgs.QVariant() -- ai用
			Carddata2:setValue(use.card)
			if (not p:isKongcheng()) and player:askForSkillInvoke("Luashenqiang2", _data) then
				local card = room:askForCard(p, ".|.|.|hand!", "@Luashenqiang2", Carddata2, sgs.Card_MethodDiscard)
				if (not card) or (card:getNumber() < use.card:getNumber()) then
					room:setPlayerFlag(player, "shenqiangq|" .. p:objectName())
					if (not card) or (not card:isVirtualCard()) then
						local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_Heart, use.card:getNumber())
						if use.card:isKindOf("FireSlash") then
							slash = sgs.Sanguosha:cloneCard("fire_slash", sgs.Card_Heart, use.card:getNumber())
						elseif use.card:isKindOf("ThunderSlash") then
							slash = sgs.Sanguosha:cloneCard("thunder_slash", sgs.Card_Heart, use.card:getNumber())
						end
						local drank = use.card:hasFlag("drank")
						if drank then
							room:setCardFlag(slash, "drank")
							slash:setTag("drank", sgs.QVariant(1))
						end
						slash:addSubcard(use.card)
						use.card = slash
						data:setValue(use)
					end
				end
			end
			index = index + 1
		end
		return false
	end
}
Luashenqiang2 = sgs.CreateTriggerSkill{
	name = "Luashenqiang2" ,
	events = {sgs.TargetConfirmed} ,
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		if not use.from then return false end
		if (player:objectName() ~= use.from:objectName()) or (not use.card:isKindOf("Slash")) then return false end
		if not use.card then return false end
		local room = player:getRoom()
		local jink_table = sgs.QList2Table(player:getTag("Jink_" .. use.card:toString()):toIntList())
		local index = 1
		for _, p in sgs.qlist(use.to) do
			if player:hasFlag("shenqiangq|" .. p:objectName()) then
				jink_table[index] = 0
			end
			index = index + 1
		end
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			if player:hasFlag("shenqiangq|" .. p:objectName()) then
				room:setPlayerFlag(player, "-shenqiangq|" .. p:objectName())
			end
		end
		local jink_data = sgs.QVariant()
		jink_data:setValue(Table2IntList(jink_table))
		player:setTag("Jink_" .. use.card:toString(), jink_data)
		return false
	end
}

Luahongzhuan = sgs.CreateTriggerSkill{
	name = "Luahongzhuan",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damage, sgs.PreDamageDone},
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local room = player:getRoom()
		if (event == sgs.PreDamageDone) and damage.from and damage.from:hasSkill(self:objectName()) and damage.from:isAlive() and damage.card then
			local remilia = damage.from
			local boolean = damage.card:isKindOf("Slash") and damage.card:isRed()
			remilia:setTag("Luahongzhuan", sgs.QVariant(boolean))
		elseif (event == sgs.Damage) and player:hasSkill(self:objectName()) and player:isAlive() then
			local invoke = player:getTag("Luahongzhuan"):toBool()
			player:setTag("Luahongzhuan", sgs.QVariant(false))
			if invoke and player:isWounded() then
				local recover = sgs.RecoverStruct()
				recover.who = player
				recover.recover = 1
				room:recover(player, recover)
			end
		end
		return false
	end,
	can_trigger = function(self, target)
		return target
	end
}
LuahongzhuanMod = sgs.CreateTargetModSkill{
	name = "#LuahongzhuanMod" ,
	distance_limit_func = function(self, player, card)
		if player:hasSkill("Luahongzhuan") and card:isKindOf("Slash") and card:isRed() then 
			return 1000
		else
			return 0
		end
	end,
	residue_func = function(self, from, card, to)
		if from:hasSkill("Luahongzhuan") and card:isKindOf("Slash") and card:isRed() then
			return 1000
		else
			return 0
		end
	end
}
remilia:addSkill(Luashenqiang2)
remilia:addSkill(Luashenqiang5)
remilia:addSkill(Luahongzhuan)
remilia:addSkill(LuahongzhuanMod)

remiliaA:addSkill(Luashenqiang2)
remiliaA:addSkill(Luashenqiang5)
remiliaA:addSkill(Luahongzhuan)
remiliaA:addSkill(LuahongzhuanMod)

remiliaB:addSkill(Luashenqiang2)
remiliaB:addSkill(Luashenqiang5)
remiliaB:addSkill(Luahongzhuan)
remiliaB:addSkill(LuahongzhuanMod)

remiliaC:addSkill(Luashenqiang2)
remiliaC:addSkill(Luashenqiang5)
remiliaC:addSkill(Luahongzhuan)
remiliaC:addSkill(LuahongzhuanMod)
 

youquCard = sgs.CreateSkillCard{
	name = "luayouqu",
	filter = function(self, targets, to_select)
		return (#targets == 0)
	end,
	on_effect = function(self, effect)
		local room = effect.to:getRoom()
		local to_use_S = {}
		local to_use = {"fire_slash", "thunder_slash", "slash", "peach", "analeptic", "ofuda", "hui"}
		for _, str in ipairs(to_use) do
			local card = sgs.Sanguosha:cloneCard(str)
			local boolR = true
			local x = sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_Residue, effect.to, card) + 1
			if card:isKindOf("Slash") and x - effect.to:usedTimes("Slash") < 1
				and effect.to:getPhase() == sgs.Player_Play then boolR = false end
			if not effect.to:isCardLimited(card, sgs.Card_MethodUse) and boolR then table.insert(to_use_S, str) end

		end
		local str_X = room:askForChoice(effect.to, "luayouqu", table.concat(to_use_S, "+"))
		local card1 = room:askForCard(effect.to, ".|.|.|hand", "@luayouqu", sgs.QVariant(str_X), sgs.Card_MethodNone)
		if not card1 then return false end
		if effect.to:isCardLimited(card1, sgs.Card_MethodUse) then return false end
		local card = sgs.Sanguosha:cloneCard(str_X)
		local targets_list = sgs.SPlayerList()
		for _, target in sgs.qlist(room:getAllPlayers()) do
			if ((card:isKindOf("Slash") and effect.to:canSlash(target, card, true))
				or ((card:targetFixed() and effect.to:objectName() == target:objectName()))
				or (card:isKindOf("Ofuda") and effect.to:inMyAttackRange(target, - sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_DistanceLimit, effect.to, card))))
				and not effect.to:isProhibited(target, card) and not (card:isKindOf("Peach") and not target:isWounded())
				and card:isAvailable(effect.to) then
				targets_list:append(target)
			end
		end
		card:setSkillName(self:objectName())
		card:addSubcard(card1)
		local X = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, effect.to, card)
		local to_target = sgs.SPlayerList()
		while not targets_list:isEmpty() do
			local Carddata2 = sgs.QVariant() -- ai用
			Carddata2:setValue(card)
			effect.to:setTag("luayouquTC", Carddata2)
			local target_X = room:askForPlayerChosen(effect.to, targets_list, "luayouqu", "luayouquX", true)
			effect.to:removeTag("luayouquTC")
			if not target_X then break end
			to_target:append(target_X)
			X = X - 1
			if X <= 0 then break end
		end
		if not to_target:isEmpty() then
			room:useCard(sgs.CardUseStruct(card, effect.to, to_target), true)
			if card:isKindOf("Slash") and effect.to:getPhase() == sgs.Player_Play then room:addPlayerHistory(effect.to, "Slash", 1) end
			if not effect.from:isAlive() then return false end
			if card1:objectName() == str_X or (card1:isKindOf("Slash") and card:isKindOf("Slash")) then
				local choice = room:askForChoice(effect.from, "luayouqu2", "luayouqu+draw")
				if choice == "luayouqu" then
					room:addPlayerHistory(effect.from, "#luayouqu", 0)
				else
					effect.from:drawCards(1)
				end
			end
		end
	end
}
  
luayouqu = sgs.CreateZeroCardViewAsSkill{
	name = "luayouqu",
	view_as = function(self, cards)
		return youquCard:clone()
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#luayouqu")
	end,
}
luafanhun = sgs.CreateTriggerSkill{
	name = "luafanhun",
	global = true,
	events = {sgs.Dying, sgs.DamageInflicted},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.Dying) then
			local yuyuko = player:getRoom():findPlayerBySkillName("luafanhun")
			if not yuyuko then return false end
			if player:objectName() ~= yuyuko:objectName() then return false end
			if not yuyuko:isAlive() then return false end
			if yuyuko:getMark("Forbid_fanhun") > 0 then return false end
			local dying = data:toDying()
			local _player = dying.who
			if yuyuko:askForSkillInvoke(self:objectName(), data) then
				room:setPlayerMark(yuyuko, "Forbid_fanhun", 1)
				room:setPlayerProperty(_player, "hp", sgs.QVariant(1))
				room:filterCards(_player, _player:getCards("h"), true)
				local ar = _player:getMark("@luafanhun") - 2
				if ar > 0 then _player:drawCards(ar) end
				local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				local Use --
				local strX = "BasicCard+^Jink"
				if (not slash:isAvailable(_player) or _player:hasUsed("Slash")) and _player:getPhase() == sgs.Player_Play then strX = strX .. "+^Slash" end
				if not _player:isWounded() then strX = strX .. "+^Peach" end
				if _player:getMark("drank") > 0 then strX = strX .. "+^Analeptic" end
				room:writeToConsole("gohei test1  " .. strX)
				room:setPlayerFlag(_player, "xianji")
				for i = 1, ar + 4 do
					local X = room:askForUseCard(_player, "TrickCard+^Nullification," .. strX .. ",EquipCard|.|.|hand", "@luafanhun")
					if not X then break end
				end
				room:setPlayerFlag(_player, "-xianji")
				room:killPlayer(_player, dying.damage)
				room:setPlayerMark(yuyuko, "Forbid_fanhun", 0)
				room:damage(sgs.DamageStruct(self:objectName(), yuyuko, yuyuko, 2, sgs.DamageStruct_Normal))
			end
		elseif event == sgs.DamageInflicted then
			local yuyuko = player:getRoom():findPlayerBySkillName("luafanhun")
			if not yuyuko then return false end
			if not yuyuko:isAlive() then return false end
			local damage = data:toDamage()
			if not damage.from then return false end
			if not damage.from:isAlive() then return false end
			room:setPlayerMark(damage.from, "@luafanhun", damage.from:getMark("@luafanhun") + damage.damage)
		end
	end
}
yuyuko:addSkill(luayouqu)
yuyuko:addSkill(luafanhun)

yuyukoA:addSkill(luayouqu)
yuyukoA:addSkill(luafanhun)

yuyukoB:addSkill(luayouqu)
yuyukoB:addSkill(luafanhun)

yuyukoC:addSkill(luayouqu)
yuyukoC:addSkill(luafanhun)

yuyukoD:addSkill(luayouqu)
yuyukoD:addSkill(luafanhun)

yuyukoE:addSkill(luayouqu)
yuyukoE:addSkill(luafanhun)

LuaTaiji = sgs.CreateTriggerSkill{
	name = "LuaTaiji",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		if player:getPhase() == sgs.Player_Start then
			local room = player:getRoom()
			local handcard_num = player:getHandcardNum()
			local x = player:getHp()
			if not ((handcard_num > x) and player:getLostHp() <=0) and player:askForSkillInvoke(self:objectName()) then
				player:throwAllHandCards()
				if handcard_num == x then 
					player:drawCards(handcard_num)
				end 
				if handcard_num > x then 
					recover = sgs.RecoverStruct()
					recover.who = player
					room:recover(player, recover)
				end 
				if handcard_num < x then 
					local targets_list = sgs.SPlayerList()
					for _, target in sgs.qlist(room:getAllPlayers()) do
						if player:canSlash(target, nil, false) then
							targets_list:append(target)
						end
					end
					local target_0 = room:askForPlayerChosen(player, targets_list, self:objectName(), "LuaTaiji1", true, true)
					if target_0 then
						local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
						slash:setSkillName("LuaTaiji")
						room:useCard(sgs.CardUseStruct(slash, player, target_0))
					end
				end 
			end
		end
	end
}
lualongxiCard = sgs.CreateSkillCard{
	name = "lualongxi",
	target_fixed = true,
	will_throw = true,
	on_use = function(self, room, player, targets)
		
		local x = player:getHp()
		local handcard_num = player:getHandcardNum()
		x = x - handcard_num
		if player:isAlive() and (x > 0) then
			room:drawCards(player, x)
		end
		local function canLoseHp()
			for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
				if hecatiaX and isFriendQ(room, player, hecatiaX) and player:objectName() ~= hecatiaX:objectName()
						and player:getHp() == hecatiaX:getHp() then
					room:notifySkillInvoked(hecatiaX, "luayiti")
					return false
				end 
			end 
			for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
				if Erin and Erin:getKingdom() == player:getKingdom() then
					room:notifySkillInvoked(Erin, "luajiance")
					return false
				end 
			end 
			return true
		end 
		if canLoseHp() then room:loseHp(player, 1) end 
	end
}
lualongxi = sgs.CreateViewAsSkill{
	name = "lualongxi",
	n = 0,
	view_as = function(self, cards)
		return lualongxiCard:clone()
	end,
}


meiling:addSkill(lualongxi)
meiling:addSkill(LuaTaiji)

local function Set(list)
	local set = {}
	for _, l in ipairs(list) do set[l] = true end
	return set
end

Lualianji = sgs.CreateTriggerSkill{
	name = "Lualianji",
	--view_as_skill = LualianjiVS,
	events = {sgs.TargetConfirmed} ,
	frequency = sgs.Skill_NotFrequent,
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		local room = player:getRoom()
		if not use.card then return false end 
		if not use.from then return false end
		if (player:objectName() ~= use.from:objectName()) or (not use.to) or (use.card:isVirtualCard()) or (use.card:getTypeId() ~= sgs.Card_TypeTrick) then return false end
		if (use.card:getSubcards() and (sgs.Sanguosha:getEngineCard(use.card:getEffectiveId()):objectName() ~= use.card:objectName())) then return false end 
		local peh = sgs.Sanguosha:cloneCard("peach")
		--if (use.card:getSubcards() and (use.card:getSubcards():length() ~= 0) and (use.card:getSubcards():length() > 1)) then return false end 
		-- local A = use.card
		-- local B = use.card:getId()
		-- local C = use.card:getEffectiveId()
		-- local D = sgs.Sanguosha:getCard(use.card:getSubcards():at(0))
		-- local E = peh:getId()
		-- local F = peh:getEffectiveId()
		-- local Y = room:getCardPlace(peh:getEffectiveId())
		
		-- local H = sgs.Sanguosha:getEngineCard(use.card:getEffectiveId())
		-- local I = H:getId()
		-- local J = H:getEffectiveId()		
		-- room:writeToConsole("use.card 牌名是" .. A:objectName())
		-- room:writeToConsole("use.card getId是" .. B)
		-- room:writeToConsole("use.card getEffectiveId是" .. C)
		-- room:writeToConsole("use.card:getSubcards():at(0) 牌名是" .. D:objectName())
		-- room:writeToConsole("use.card:getSubcards():at(0) getId是" .. E)
		-- room:writeToConsole("use.card:getSubcards():at(0) getEffectiveId是" .. F)
		-- if Y == sgs.Player_PlaceEquip then room:writeToConsole("sgs.Player_PlaceEquip") end 
		-- if Y == sgs.Player_PlaceHand then room:writeToConsole("sgs.Player_PlaceHand") end 
		-- if Y == sgs.Player_PlaceJudge then room:writeToConsole("sgs.Player_PlaceJudge") end 
		-- room:writeToConsole("sgs.Sanguosha:getEngineCard(use.card:getEffectiveId()) 牌名是" .. H:objectName())
		-- room:writeToConsole("sgs.Sanguosha:getEngineCard(use.card:getEffectiveId()) getId是" .. I)
		-- room:writeToConsole("sgs.Sanguosha:getEngineCard(use.card:getEffectiveId()) getEffectiveId是" .. J)
		
		--if (use.card:getSubcards() and (use.card:getSubcards():length() ~= 0) and (sgs.Sanguosha:getCard(use.card:getSubcards():at(0)):getEffectiveId() ~= use.card:getEffectiveId())) then return false end 
		if use.to:length() ~= 1 then return false end 
		local playerdata = sgs.QVariant() -- ai用
		playerdata:setValue(use.to:at(0))
		room:setTag("LualianjiTarget", playerdata)
		local Carddata2 = sgs.QVariant() -- ai用
		Carddata2:setValue(use.card)
		room:setTag("LualianjiTargetC2", Carddata2)
		local pattern = use.card:getSuitString()
		local data_for_ai = sgs.QVariant(pattern)
		pattern = string.format(".|%s|.|.", pattern)
		local card_0 = room:askForCard(player, pattern, "@Lualianji", data_for_ai, sgs.NonTrigger, player)
		if card_0 then
			local Carddata = sgs.QVariant() -- ai用
			Carddata:setValue(card_0)
			room:setTag("LualianjiTargetC", Carddata)
			local type = {}
			local delay_trick = {}
			local sttrick = {}
			local mttrick = {}
			local patterns = {"snatch", "dismantlement", "collateral", "ex_nihilo", "duel", "fire_attack", "amazing_grace",
							  "savage_assault", "archery_attack", "god_salvation", "iron_chain", "supply_shortage", "lightning", "indulgence"}
			if not (Set(sgs.Sanguosha:getBanPackages()))["pay9"] then
				table.insert(patterns, 2, "banquet")
				table.insert(patterns, 2, "need_maribel")
				table.insert(patterns, 2, "faith_collection")
				table.insert(patterns, 2, "girl_choosen")
			end
			for _, cd in ipairs(patterns) do
				local card = sgs.Sanguosha:cloneCard(cd, card_0:getSuit(), card_0:getNumber())
				card:addSubcard(card_0)
				card:setSkillName("Lualianji")				
				local qtargets = sgs.PlayerList()
				if card and card:targetFilter(qtargets, use.to:at(0), player) and not player:isProhibited(use.to:at(0), card, qtargets) then	
					qtargets:append(use.to:at(0))
					card:deleteLater()
					if card:isAvailable(player) and (cd ~= use.card:objectName()) and card:targetsFeasible(qtargets, player) then
						if card:isKindOf("DelayedTrick") then
							table.insert(delay_trick, cd)
						elseif (card:isKindOf("SingleTargetTrick") and not card:isKindOf("DelayedTrick")) then
							table.insert(sttrick, cd)
						else
							table.insert(mttrick, cd)
						end
					end
				end
			end
			if #delay_trick ~= 0 then table.insert(type, "delay_trick") end
			if #sttrick ~= 0 then table.insert(type, "single_target_trick") end
			if #mttrick ~= 0 then table.insert(type, "multiple_target_trick") end
			local typechoice = ""
			if #type > 0 then
				typechoice = room:askForChoice(player, "Lualianji", table.concat(type, "+"))
			end		
			local choices = {}
			if typechoice == "delay_trick" then
				choices = delay_trick
			elseif typechoice == "single_target_trick" then
				choices = sttrick
			elseif typechoice == "multiple_target_trick" then
				choices = mttrick
			end
			local pattern_0 = room:askForChoice(player, "LualianjiX", table.concat(choices, "+"))		
			if pattern_0 then 
				local card_X = sgs.Sanguosha:cloneCard(pattern_0, sgs.Card_NoSuit, 0)
				card_X:addSubcard(card_0)
				card_X:setSkillName("Lualianji")
				if card_X:isKindOf("AOE") or card_X:isKindOf("AmazingGrace") or card_X:isKindOf("GodSalvation") then
					room:setPlayerFlag(player, "qucaiAOE")
					room:setPlayerFlag(use.to:at(0), "qucaiAOEs")
					card_X:setSkillName("Lualianji")
					room:useCard(sgs.CardUseStruct(card_X, player, sgs.SPlayerList()))
					room:setPlayerFlag(player, "-qucaiAOE")
					room:setPlayerFlag(use.to:at(0), "-qucaiAOEs")
				else
					local targets_list = sgs.SPlayerList()
					targets_list:append(use.to:at(0)) 
					room:useCard(sgs.CardUseStruct(card_X, player, targets_list)) 

				end
				if not use.to:at(0):isAlive() then return true end  --非常漂亮!
			end 
			room:removeTag("LualianjiTargetC")
		end
		room:removeTag("LualianjiTarget")
		room:removeTag("LualianjiTargetC2")
		return false
	end
}
LuaxianzheCard = sgs.CreateSkillCard{
	name = "Luaxianzhe",
	target_fixed = true,
	will_throw = false,
	on_use = function(self, room, source, targets)
		source:turnOver()
		source:drawCards(1)
		local discard_ids = room:getDiscardPile()
		local trickcard = sgs.IntList()
		local basiccard = sgs.IntList()
		for _, id in sgs.qlist(discard_ids) do 
			local card = sgs.Sanguosha:getCard(id)
			if card:isKindOf("TrickCard") then 
				trickcard:append(id)
			elseif (card:isKindOf("BasicCard") and not card:isKindOf("Peach")) then 
				basiccard:append(id)
			end 
		end 
		if trickcard:length() > 0 and basiccard:length() > 0 then 
			room:fillAG(trickcard, source)
			local card_id = room:askForAG(source, trickcard, false, "LuaxianzheT")
			room:clearAG()
			room:obtainCard(source, card_id)
			local Carddata2 = sgs.QVariant() -- ai用
			Carddata2:setValue(sgs.Sanguosha:getCard(card_id))
			room:setTag("LuaxianzheTC", Carddata2)
			
			room:fillAG(basiccard, source)
			local card_id2 = room:askForAG(source, basiccard, false, "LuaxianzheB")			
			room:clearAG()		
			room:obtainCard(source, card_id2)	
			local yiji_cards = sgs.IntList()
			yiji_cards:append(card_id)
			yiji_cards:append(card_id2)
			room:askForYiji(source, yiji_cards, self:objectName(), true, false, true, 1, room:getAlivePlayers())
			room:removeTag("LuaxianzheTC")
			room:removeTag("LuaxianzheT")
		end 
	end
}
Luaxianzhe = sgs.CreateViewAsSkill{
	name = "Luaxianzhe",
	n = 0,
	view_as = function(self, cards)
		if #cards == 0 then
			local xianzheCard = LuaxianzheCard:clone()
			return xianzheCard
		end
	end,
	enabled_at_play = function(self, player)
		if not player:hasUsed("#Luaxianzhe") then
			return true
		end
		return false
	end
}
patchouli:addSkill(Lualianji)
patchouli:addSkill(Luaxianzhe)

patchouliA:addSkill(Lualianji)
patchouliA:addSkill(Luaxianzhe)

patchouliB:addSkill(Lualianji)
patchouliB:addSkill(Luaxianzhe)

patchouliC:addSkill(Lualianji)
patchouliC:addSkill(Luaxianzhe)

patchouliD:addSkill(Lualianji)
patchouliD:addSkill(Luaxianzhe)
 



Luayuyi = sgs.CreateTriggerSkill{
	name = "Luayuyi",
	events = {sgs.CardUsed, sgs.CardResponded},
    frequency = sgs.Skill_Frequent,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardUsed then 
			local card = data:toCardUse().card 
			if card and player:getPhase() == sgs.Player_NotActive and not card:isKindOf("SkillCard") and room:askForSkillInvoke(player, self:objectName()) then
				--room:broadcastSkillInvoke("yshanzhan")
				player:drawCards(2)   
			end 
        elseif event == sgs.CardResponded then
			local cd = data:toCardResponse().m_card
			if cd and player:getPhase() == sgs.Player_NotActive and not cd:isKindOf("SkillCard") and room:askForSkillInvoke(player, self:objectName()) then
				--room:broadcastSkillInvoke("yshanzhan")
				player:drawCards(2) 
			end
		end
	end,
}

Lualeidian = sgs.CreateTriggerSkill{
	name = "Lualeidian",
	frequency = sgs.Skill_NotFrequent, 
	events = {sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data)
		local move = data:toMoveOneTime()
		local room = player:getRoom()
		if room:getTag("FirstRound"):toBool() then return end 
		if (move.to) and (move.to_place == sgs.Player_PlaceHand) and (move.to:objectName() == player:objectName()) and move.card_ids and (move.card_ids:length() > 1)
			and player:hasSkill("Lualeidian") and room:askForDiscard(player, self:objectName(), 1, 1, true, false, "@Lualeidian2") then
			if player:getHandcardNum() == player:getMaxCards() then 
				local dongzhuo = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "@Lualeidian", false)
				if dongzhuo then 
					room:damage(sgs.DamageStruct(self:objectName(), player, dongzhuo, 1, sgs.DamageStruct_Thunder))
				end 
			end 			
		end 
	end 
}

iku:addSkill(Luayuyi)
iku:addSkill(Lualeidian)

ikuA:addSkill(Luayuyi)
ikuA:addSkill(Lualeidian)

ikuB:addSkill(Luayuyi)
ikuB:addSkill(Lualeidian)
--[[
luapaoying2 = sgs.CreateTriggerSkill{
	name = "#luapaoying" ,
	global = true,
	events = {sgs.Pindian},
	on_trigger = function(self, event, player, data)
		local pindian = data:toPindian()
		local room = player:getRoom()
		if pindian.reason == "luapaoying" then
			--room:writeToConsole("luapaoying test" .. pindian.from:objectName() .. " " .. pindian.to:objectName())
			if pindian.from_card:getNumber() > pindian.to_card:getNumber() then
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if p:objectName() == pindian.from:objectName() then
						p:gainMark("@luapaoying")
					end
				end
				--room:setPlayerMark(pindian.from, "@luapaoying", 1)
			elseif pindian.from_card:getNumber() < pindian.to_card:getNumber() then
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if p:objectName() == pindian.to:objectName() then
						p:gainMark("@luapaoying")
					end
				end
			end
		end
		return false
	end,
	priority = -1
}]]--

luapaoying = sgs.CreateTriggerSkill{
	name = "luapaoying" ,
	events = {sgs.EventPhaseChanging} ,
	priority = 1,
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		local room = player:getRoom()
		if change.to ~= sgs.Player_Start then return false end
		if player:isKongcheng() then return false end

		local Reimus = sgs.SPlayerList() --老结界厨了
		for _, p in sgs.qlist(room:getAllPlayers()) do
			if not p:isKongcheng() and p:objectName() ~= player:objectName() then
				Reimus:append(p)
			end
		end
		local to = room:askForPlayerChosen(player, Reimus, "luapaoying", "luapaoying", true, true)

		if to then
			local _data = sgs.QVariant()
			_data:setValue(to)
			room:setTag("luapaoyingTP", _data)
			local success = player:pindian(to, "luapaoying", nil)
			player:drawCards(1)
			to:drawCards(1)
			if success then
				room:detachSkillFromPlayer(to, "luajingjie3")
				local suit = room:askForChoice(player, "luajingjie1", "heart+diamond+club+spade")
				local num = room:askForChoice(player, "luajingjie2", "1+2+3+4+5+6+7+8+9+10+11+12+13")
				local name = room:askForChoice(player, "luajingjie3", "slash+jink+analeptic+peach+ofuda+hui")
				local cardid = room:askForCardChosen(player, to, "h", "luajingjie", true)
				if not cardid or not name or not num or not suit then return false end
				local ids_A = sgs.IntList()
				ids_A:append(cardid)
				room:fillAG(ids_A)
				room:getThread():delay(1000)
				room:clearAG()
				local log = sgs.LogMessage()
				log.type = "#YUKARI"
				log.to:append(to)
				log.arg = name
				log.arg2 = suit
				room:sendLog(log)

				local card = sgs.Sanguosha:getCard(cardid)
				room:setCardFlag(card, "luajingjie")
				room:setCardFlag(card, "luajingjie|" .. num .. "|" .. suit .. "|" .. name)
				room:acquireSkill(to, "luajingjie3", false)
			end

			room:removeTag("luapaoyingTP")
		end
	end
}
luapaoying3 = sgs.CreateTriggerSkill{
	name = "#luapaoying2",
	global = true,
	priority = 10,
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data, room)
		local change = data:toPhaseChange()
		if change.to ~= sgs.Player_Start then return false end
		if event == sgs.EventPhaseChanging then
			if player:objectName() == room:getLord():objectName() and player:getMark("@extra_turn") == 0
					and room:getCurrent():objectName() == player:objectName() then
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					room:setPlayerMark(p, "@luapaoying", 0)
				end
			end
		end
	end
}
luapaoying4 = sgs.CreateTriggerSkill{
	name = "#luapaoying3",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardUsed},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Play then
			local use = data:toCardUse()
			local card = use.card
			if event == sgs.CardUsed and use.from and player:objectName() == use.from:objectName() then
				if card:isKindOf("LuaSkillCard") and player:getMark("@luapaoying") > 0 then
					for _, skill in sgs.qlist(player:getVisibleSkillList()) do
						if skill:objectName() == card:objectName() and string.find(skill:getDescription(), sgs.Sanguosha:translate("luapaoying2")) then
							room:addPlayerHistory(player, "#" .. card:objectName(), 0)
							room:setPlayerMark(player, "@luapaoying", 0)
						end
					end
				end
			end
		end
	end
}

yemuCard = sgs.CreateSkillCard{
	name = "luayemu",
	filter = function(self, targets, to_select)
		return not to_select:isAllNude() and not sgs.Self:isAllNude()
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		local card = room:askForCard(effect.to, ".|.|.|.!", "@yemu", sgs.QVariant(), self:objectName())
		room:throwCard(card, effect.to, effect.to)
		local function canLoseHp()
			for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
				if hecatiaX and isFriendQ(room, effect.to, hecatiaX) and effect.to:objectName() ~= hecatiaX:objectName()
						and effect.to:getHp() == hecatiaX:getHp() then
					room:notifySkillInvoked(hecatiaX, "luayiti")
					return false
				end 
			end 
			for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
				if Erin and Erin:getKingdom() == effect.to:getKingdom() then
					room:notifySkillInvoked(Erin, "luajiance")
					return false
				end 
			end 
			return true
		end 
		if effect.to:isLastHandCard(card) then
			room:loseHp(effect.to)
		end
	end
}
yemuVS = sgs.CreateZeroCardViewAsSkill{
	name = "luayemu",
	view_as = function()
		return yemuCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@yemu") > 0
	end
}
luayemu = sgs.CreateTriggerSkill{
	name = "luayemu$",
	frequency = sgs.Skill_Limited,
	view_as_skill = yemuVS,
	events = {sgs.GameStart},
	limit_mark = "@yemu",
	on_trigger = function()
	end
}


local function string2suit(suit)
	if suit == "spade" then
		return sgs.Card_Spade
	elseif suit == "heart" then
		return sgs.Card_Heart
	elseif suit == "club" then
		return sgs.Card_Club
	elseif suit == "diamond" then
		return sgs.Card_Diamond
	end
end
luajingjieCard = sgs.CreateSkillCard{
	name = "luajingjie",
	handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		return #targets == 0  
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		effect.to:drawCards(1)
		--
		if effect.to:objectName() == effect.from:objectName() then
			room:addPlayerHistory(effect.from, "#luajingjie", 0)
		else
			room:setPlayerMark(effect.to, "@luapaoying", 1)
		end
	end
}
luajingjie = sgs.CreateOneCardViewAsSkill{
	name = "luajingjie",
	filter_pattern = ".|heart,spade|.|.",
	view_as = function(self,card)
		local skillcard = luajingjieCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#luajingjie")
	end
}

yukari:addSkill(luapaoying4)
yukari:addSkill(luapaoying3)
yukari:addSkill(luapaoying)
yukari:addSkill(luajingjie)  
LuaFeixiangCard = sgs.CreateSkillCard{
	name = "LuaFeixiang",
	filter = function(self, targets, to_select)
		if #targets == 0 then
			return not to_select:isNude()
		end
		return false
	end,
	on_effect = function(self, effect)
		local tenshi = effect.from
		local room = tenshi:getRoom()
		local id = room:askForCardChosen(tenshi, effect.to, "he", self:objectName(), false, sgs.Card_MethodNone) 
		tenshi:addToPile("qizhi2", id)
	end 
} 


LuaFeixiangVS = sgs.CreateZeroCardViewAsSkill{
	name = "LuaFeixiang",
	response_pattern = "@@LuaFeixiang",
	view_as = function(self, cards)
		return LuaFeixiangCard:clone()
	end
}

LuaFeixiang = sgs.CreateTriggerSkill{
	name = "LuaFeixiang" ,
	events = {sgs.EventPhaseStart} ,
	frequency = sgs.Skill_NotFrequent,
	view_as_skill = LuaFeixiangVS,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart and ((player:getPhase() == sgs.Player_Start) or (player:getPhase() == sgs.Player_Finish)) then 
			if room:askForUseCard(player,  "@@LuaFeixiang", "@LuaFeixiang") then 
				
			end
		end 
	end 
}

LuaFeixiang2 = sgs.CreateTriggerSkill{
	name = "#LuaFeixiang" ,
	events = {sgs.Damage} ,
	frequency = sgs.Skill_Compulsory,
	global = true,
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local target = damage.to
		if damage.damage == 0 then return false end
		if target and (not target:getPile("qizhi2"):isEmpty()) and target:hasSkill("LuaFeixiang") then
			local qizhi = target:getPile("qizhi2")
			local room = target:getRoom()
			local dummy = sgs.Sanguosha:cloneCard("jink")
			if not qizhi:isEmpty() then
				room:fillAG(qizhi, player)
				local card_id = room:askForAG(player, qizhi, false, "LuaFeixiang")
				if not card_id then card_id = qizhi:at(0) end
				local card = sgs.Sanguosha:getCard(card_id)
				if not card then return false end
				dummy:addSubcard(card)
				player:getRoom():getCurrent():obtainCard(dummy)
				qizhi:removeOne(card_id)
				room:clearAG(player)
				target:drawCards(1)
				local dummy2 = sgs.Sanguosha:cloneCard("jink")
				dummy2:addSubcards(qizhi)
				room:throwCard(dummy2, target)
			end

		end
	end 
}
function TSMark(player, bool)
	local room = player:getRoom()
	if not bool then
		local x = player:getMark("LuaJiaosiD") + 1
		room:setPlayerMark(player, "LuaJiaosiD", x)
	else
		local x = player:getMark("LuaJiaosiS") + 1
		room:setPlayerMark(player, "LuaJiaosiS", x)
	end
	--room:writeToConsole("LuaJiaosiZ个数".. x)
end
local function TSSuit(pile, card)
	for _,id in sgs.qlist(pile) do
		if sgs.Sanguosha:getCard(id):getSuit() == card:getSuit() then return true end
	end
	return false
end
LuaJiaosiCard = sgs.CreateSkillCard{
	name = "LuaJiaosi",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select)
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE then return true end 
		if to_select:objectName() == sgs.Self:objectName() then return false end
		if #targets ~= 0 then return false end
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		local duel = sgs.Sanguosha:cloneCard("duel", card:getSuit(), card:getNumber())
		local can_duel = true
		if sgs.Self:isProhibited(to_select, duel, to_select:getSiblings()) then can_duel = false end
		local function TSATKR()
			if sgs.Self:getWeapon() and self:getSubcards():contains(sgs.Self:getWeapon():getId()) then
				local weapon = sgs.Self:getWeapon():getRealCard():toWeapon()
				local distance_fix = weapon:getRange() - 1
				if sgs.Self:getOffensiveHorse() and self:getSubcards():contains(sgs.Self:getOffensiveHorse():getId()) then
					distance_fix = distance_fix + 1
				end
				return sgs.Self:distanceTo(to_select, distance_fix) <= sgs.Self:getAttackRange()
			elseif sgs.Self:getOffensiveHorse() and self:getSubcards():contains(sgs.Self:getOffensiveHorse():getId()) then
				return sgs.Self:distanceTo(to_select, 1) <= sgs.Self:getAttackRange()
			end 
			return sgs.Self:inMyAttackRange(to_select)
		end 
		return TSATKR() or can_duel
	end,
	on_validate = function(self, carduse)
		local source = carduse.from
		local target = carduse.to:first()
		local room = source:getRoom()
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		local slash_va = true
		local duel_va = true
		if carduse.from:getMark("LuaJiaosiD") > 0 then duel_va = false end
		if carduse.from:getMark("LuaJiaosiS") > 0 then slash_va = false end
		if not duel_va and not slash_va then return end
		local function TSATKR(to_select)
			if source:getWeapon() and self:getSubcards():contains(source:getWeapon():getId()) then
				local weapon = source:getWeapon():getRealCard():toWeapon()
				local distance_fix = weapon:getRange() - 1
				if source:getOffensiveHorse() and self:getSubcards():contains(source:getOffensiveHorse():getId()) then
					distance_fix = distance_fix + 1
				end
				return source:distanceTo(to_select, distance_fix) <= source:getAttackRange()
			elseif source:getOffensiveHorse() and self:getSubcards():contains(source:getOffensiveHorse():getId()) then
				return source:distanceTo(to_select, 1) <= source:getAttackRange()
			end 
			return source:inMyAttackRange(to_select)
		end 
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
    		local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
        	slash:addSubcard(card:getEffectiveId())
        	slash:deleteLater()
        	if (not slash:isAvailable(source)) or (not source:canSlash(target, nil, false) or (not TSATKR(target))) then slash_va = false end
		end 
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
    		local duel = sgs.Sanguosha:cloneCard("duel", card:getSuit(), card:getNumber())
        	duel:addSubcard(card:getEffectiveId())
        	duel:deleteLater()
        	if (not duel:isAvailable(source)) or source:isProhibited(target, duel) then duel_va = false end 	
		end 
		if (not slash_va) and duel_va then
			local duel = sgs.Sanguosha:cloneCard("duel", card:getSuit(), card:getNumber())
			duel:setSkillName(self:objectName())
			duel:addSubcard(card:getEffectiveId())
			TSMark(source)
			return duel			
		elseif (not duel_va) and slash_va then 
			local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
			slash:setSkillName(self:objectName())
			slash:addSubcard(card:getEffectiveId())
			TSMark(source)
			return slash
		elseif duel_va and slash_va then 
			local pdata ,cdata= sgs.QVariant() ,sgs.QVariant()
			pdata:setValue(target)
			cdata:setValue(card)
			room:setTag("LuaJiaosi_user",pdata)
			room:setTag("LuaJiaosi_card",cdata)
			local choice = room:askForChoice(source, "LuaJiaosi", "slash+duel")

			if choice == "slash" then 
				local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
				slash:setSkillName(self:objectName())
				slash:addSubcard(card:getEffectiveId())
				TSMark(source)
				return slash
			else
				local duel = sgs.Sanguosha:cloneCard("duel", card:getSuit(), card:getNumber())
				duel:setSkillName(self:objectName())
				duel:addSubcard(card:getEffectiveId())		
				TSMark(source)
				return duel	
			end 
			room:removeTag("LuaJiaosi_user")
			room:removeTag("LuaJiaosi_card")
		end
	end,
	on_validate_in_response = function(self, tenshi)
		local room = tenshi:getRoom()
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		if not TSSuit(tenshi:getPile("qizhi2"), card) then return nil end
		local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		slash:addSubcard(card:getEffectiveId())
		TSMark(tenshi)
		return slash
	end,
}
LuaJiaosi = sgs.CreateViewAsSkill{
	name = "LuaJiaosi" ,
	n = 1,
	view_filter = function(self, selected, to_select)
		if not TSSuit(sgs.Self:getPile("qizhi2"), to_select) then return false end
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
    		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
        	slash:addSubcard(to_select:getEffectiveId())
        	slash:deleteLater()
        	if (not slash:isAvailable(sgs.Self)) or sgs.Self:getMark("LuaJiaosiS") > 0 then
				if sgs.Self:getMark("LuaJiaosiD") > 0 then return false end
				if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
					local duel = sgs.Sanguosha:cloneCard("duel", sgs.Card_SuitToBeDecided, -1)
					duel:addSubcard(to_select:getEffectiveId())
					duel:deleteLater()	
					if not duel:isAvailable(sgs.Self) then return false end 
				end 
			end 
    	end
		return (#selected == 0) and (not sgs.Self:isJilei(to_select))
	end ,
	view_as = function(self, cards)
		if #cards ~= 1 then return nil end
		if not TSSuit(sgs.Self:getPile("qizhi2"), cards[1]) then return nil end
		local card = LuaJiaosiCard:clone()
		card:addSubcard(cards[1])
		return card
	end ,
	enabled_at_play = function(self, player)
		if (not player:getPile("qizhi2")) or (player:getPile("qizhi2"):isEmpty()) then return false end 
		return (player:getMark("LuaJiaosiD") < 1) or (player:getMark("LuaJiaosiS") < 1)
	end,
	enabled_at_response = function(self, player, pattern)
		if (not player:getPile("qizhi2")) or (player:getPile("qizhi2"):isEmpty()) then return false end 
		return (player:getMark("LuaJiaosiD") < 1) or (player:getMark("LuaJiaosiS") < 1)
	end
}

LuaJiaosi2 = sgs.CreateTriggerSkill{
	name = "#LuaJiaosi2" ,
	global = true,
	events = {sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Start and player:objectName() == room:getCurrent():objectName() then
			local toziko = player:getRoom():findPlayerBySkillName("LuaJiaosi")
			if toziko then
				room:setPlayerMark(player, "LuaJiaosiD", 0)
				room:setPlayerMark(player, "LuaJiaosiS", 0)
				room:setPlayerMark(player, "LuaJiaosiAI", 0)
			end
		end
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end ,
	priority = 1
}

tenshi:addSkill(LuaFeixiang2)
tenshi:addSkill(LuaFeixiang)
tenshi:addSkill(LuaJiaosi)
tenshi:addSkill(LuaJiaosi2)

luaxuqu = sgs.CreateTriggerSkill{
	name = "luaxuqu" ,
	global = true,
	events = {sgs.Death, sgs.EventPhaseEnd} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.Death then
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if p:hasFlag("luaxuqu") then
					room:setPlayerFlag(p, "-luaxuqu")
				end
			end
		else
			if player:getPhase() == sgs.Player_Draw and player:objectName() == room:getCurrent():objectName()
				and player:getHp() == 1 then
				for _, ap in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
					local _data = sgs.QVariant()
					_data:setValue(player)
					if room:askForSkillInvoke(ap, "luaxuqu", _data) then
						room:setPlayerFlag(ap, "luaxuqu")
						local thread = room:getThread()
						local old_phase = ap:getPhase()
						ap:setPhase(sgs.Player_Play)
						room:broadcastProperty(ap, "phase")
						if not thread:trigger(sgs.EventPhaseStart, room, ap) then
							thread:trigger(sgs.EventPhaseProceeding, room, ap)
						end

						thread:trigger(sgs.EventPhaseEnd, room, ap)
						if ap:hasFlag("luaxuqu") then
							local function canLoseHp()
								for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
									if hecatiaX and isFriendQ(room, ap, hecatiaX) and ap:objectName() ~= hecatiaX:objectName()
											and ap:getHp() == hecatiaX:getHp() then
										room:notifySkillInvoked(hecatiaX, "luayiti")
										return false
									end 
								end 
								for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
									if Erin and Erin:getKingdom() == ap:getKingdom() then
										room:notifySkillInvoked(Erin, "luajiance")
										return false
									end 
								end 
								return true
							end 
							if canLoseHp() then room:loseHp(ap) end 
							room:setPlayerFlag(ap, "-luaxuqu")
						end
						ap:setPhase(old_phase)
						room:broadcastProperty(ap, "phase")
					end
				end
			end
		end
	end
}

luaduzou = sgs.CreateTriggerSkill{
	name = "luaduzou",
	events = {sgs.TargetConfirmed} ,
	frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			if not use.card then return false end
			if not use.from then return false end
			if (player:objectName() ~= use.from:objectName()) or (not use.to) or not (use.card:isRed()) then return false end
			if use.to:length() ~= 1 then return false end
			if use.card:isKindOf("SkillCard") then return false end
			local _data = sgs.QVariant()
			_data:setValue(use.to:at(0))
			local choice = room:askForChoice(use.from, "luaduzou", "Draw+Discard", _data)
			if choice == "Draw" then
				use.to:at(0):drawCards(1)
			elseif not use.to:at(0):isNude() then
				room:askForDiscard(use.to:at(0), self:objectName(), 1, 1, false, true)
			end
		end
	end
}

luahezouCard = sgs.CreateSkillCard{
	name = "luahezou" ,
	will_throw = false ,
	target_fixed = true ,
	on_use = function(self, room, source, targets)
		room:detachSkillFromPlayer(source, "luahezour")
		for _, id in sgs.qlist(self:getSubcards()) do
			local card = sgs.Sanguosha:getCard(id)
			room:setCardFlag(card, "luahezou")
		end

		room:fillAG(self:getSubcards())
		room:getThread():delay(1000)
		room:clearAG()

		room:acquireSkill(source, "luahezour", false)
	end
}
luahezou = sgs.CreateViewAsSkill{
	name = "luahezou",
	n = 3,
	view_filter = function(self, selected, to_select)
		return #selected <= 3 and not to_select:isKindOf("yuzhi") and not to_select:isEquipped()
	end,
	view_as = function(self, cards)
		if #cards == 3 then
			local card = luahezouCard:clone()
			for _, cd in ipairs(cards) do
				card:addSubcard(cd)
			end
			return card
		end
	end,
	enabled_at_play = function(self, player)
		return not player:isKongcheng()
	end
}

prismriver:addSkill(luaxuqu)
prismriver:addSkill(luaduzou)
prismriver:addSkill(luahezou)

prismriverA:addSkill(luaxuqu)
prismriverA:addSkill(luaduzou)
prismriverA:addSkill(luahezou)

prismriverB:addSkill(luaxuqu)
prismriverB:addSkill(luaduzou)
prismriverB:addSkill(luahezou)

luajuezhancard = sgs.CreateSkillCard{
	name = "luajuezhan" ,
	filter = function(self, targets, to_select)
		if #targets ~= 0 then return false end
		local duel = sgs.Sanguosha:cloneCard("duel", sgs.Card_NoSuit, 0)
		if to_select:isProhibited(sgs.Self, duel, sgs.Self:getSiblings()) then return false end
		if to_select:isCardLimited(duel, sgs.Card_MethodUse) then return false end
		return to_select:objectName() ~= sgs.Self:objectName()
	end ,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		effect.from:drawCards(1)
		local duel = sgs.Sanguosha:cloneCard("duel", sgs.Card_NoSuit, 0)
		duel:setSkillName(self:objectName())
		if (not effect.to:isCardLimited(duel, sgs.Card_MethodUse)) and (not effect.to:isProhibited(effect.from, duel)) then
			room:useCard(sgs.CardUseStruct(duel, effect.to, effect.from))
		end		
	end ,
}

luajuezhan = sgs.CreateViewAsSkill{
	name = "luajuezhan" ,
	n = 0 ,
	view_filter = function(self, selected, to_select)
		return true
	end ,
	view_as = function(self, cards)
		local card = luajuezhancard:clone()
		return card
	end ,
	enabled_at_play = function(self, player)
		return not player:hasFlag("luajuezhani") and player:usedTimes("#luajuezhan") < 2
	end
}


luabingpu = sgs.CreateTriggerSkill{
	name = "luabingpu" ,
	events = {sgs.Damaged} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if damage.damage == 0 then return false end
		if damage.from and damage.from:isAlive() and not damage.from:isNude() then 
			local playerdata = sgs.QVariant() -- ai用
			playerdata:setValue(damage.from)
			room:setTag("luabingpuTarget", playerdata)
				if room:askForSkillInvoke(player, self:objectName())  then	
					local to_give = room:askForCard(damage.from, ".", "@luabingpua", data, self:objectName())
					if to_give then 
						room:throwCard(to_give, damage.from, damage.from)	
						local Carddata2 = sgs.QVariant() -- ai用
						Carddata2:setValue(to_give)
						room:setTag("luabingpuC2", Carddata2)						
					elseif not damage.from:isNude() then 
						local handcards = damage.from:getHandcards()
						local allcards = {}
						if damage.from:canDiscard(damage.from, "h") then
							for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
								table.insert(allcards, c)
							end
						end 
						if damage.from:canDiscard(damage.from, "e") then
							local equips = damage.from:getEquips()
							for _,c in sgs.qlist(equips) do
								table.insert(allcards, c)
							end						
						end 
						if #allcards > 0 then 
							local r = math.random(1, #allcards)
							local card = allcards[r]
							room:throwCard(card, damage.from, damage.from)		
							local Carddata2 = sgs.QVariant() -- ai用
							Carddata2:setValue(card)
							room:setTag("luabingpuC2", Carddata2)									
						end 
					end 

					local to_give2 = room:askForCard(player, ".", "@luabingpub", data, self:objectName())
					if to_give2 then 
						room:throwCard(to_give2, player, player)			
					end 	

					if to_give and to_give2 then 
						if (to_give:isRed() and to_give2:isBlack()) or (to_give:isBlack() and to_give2:isRed())
							and (player:getMark("@baka") == 0) then 
							player:gainMark("@baka")
						end 
					end 
				end
			room:removeTag("luabingpuC2")
			room:removeTag("luabingpuTarget")
		end 
	end ,
	can_trigger = function(self,target)
		if target and target:isAlive() then			
			return target:hasSkill("luabingpu")
		end
		return false
	end,
}

luabingpu2 = sgs.CreateTriggerSkill{
	name = "#luabingpu",
	global = true,
	events = {sgs.EventPhaseChanging},
	can_trigger = function(self, target)
		return target and target:isAlive() 
	end,
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		local room = player:getRoom()
		if change.to == sgs.Player_NotActive then
			for _, p in sgs.qlist(room:findPlayersBySkillName("luabingpu")) do	
				while p:isWounded() and p:getMark("@baka") > 0 do
					room:recover(p, sgs.RecoverStruct(p))
					p:loseMark("@baka")
				end 
			end 
		end 
	end ,
}

cirno:addSkill(luajuezhan)
cirno:addSkill(luabingpu)
cirno:addSkill(luabingpu2)

cirnoA:addSkill(luajuezhan)
cirnoA:addSkill(luabingpu)
cirnoA:addSkill(luabingpu2)

cirnoB:addSkill(luajuezhan)
cirnoB:addSkill(luabingpu)
cirnoB:addSkill(luabingpu2)

cirnoC:addSkill(luajuezhan)
cirnoC:addSkill(luabingpu)
cirnoC:addSkill(luabingpu2)

cirnoE:addSkill(luajuezhan)
cirnoE:addSkill(luabingpu)
cirnoE:addSkill(luabingpu2)

luayexiao3 = sgs.CreateTriggerSkill{
	name = "#luayexiao3",
	global = true,
	priority = 1,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart and player:getMark("luayexiaoK") == 0 then
			room:setPlayerMark(player, "rumia2", 1)
			room:setPlayerMark(player, "luayexiaoK", 1)
		end
	end
}

luayexiao2 = sgs.CreateTriggerSkill{
	name = "#luayexiao" ,
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseStart}, 
	on_trigger = function(self, event, player, data, room)
		if player:getPhase() == sgs.Player_Start and player:hasSkill("luayexiao") then
			if player:getMark("rumia2") > 0 then 

				local targets = sgs.SPlayerList()
				for _, target in sgs.qlist(room:getAlivePlayers()) do
					if not target:isWounded() then 
						targets:append(target)
					end 
				end 	
				if room:askForSkillInvoke(player, "luayexiao", data) then
						if (not targets:isEmpty()) then 
						local target = room:askForPlayerChosen(player, targets, "luayexiao", "luayexiaoX", true, true)		
						if target then
							room:damage(sgs.DamageStruct("luayexiao", player, target))
						else
							if player:getGeneralName() == "rumia_boss" then
								player:drawCards(1)
							else
								room:askForDiscard(player, self:objectName(), 1, 1, false, true)
							end
						end 
					else
						room:askForDiscard(player, self:objectName(), 1, 1, false, true)
					end 
					room:setPlayerMark(player, "rumia2", 0)
					room:setPlayerMark(player, "rumia1", 1)
				end 
			end 
		end 
	end 
}


yexiaoCard = sgs.CreateSkillCard{
	name = "luayexiao", 
	filter = function(self, targets, to_select)		
		return (#targets == 0) and to_select:getHandcardNum() == 0
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		effect.from:getRoom():damage(sgs.DamageStruct("luayexiao", effect.from, effect.to))
		room:setPlayerMark(effect.from, "rumia1", 0)
		room:setPlayerMark(effect.from, "rumia2", 1)
	end
}
luayexiao = sgs.CreateZeroCardViewAsSkill{
	name = "luayexiao",
	view_as = function(self, cards)
		return yexiaoCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("rumia1") > 0
	end,
}

luayexiao5 = sgs.CreateZeroCardViewAsSkill{
	name = "luayexiao5",
	view_as = function(self, cards)
		return yexiaoCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("rumia1") > 0
	end,
}

luayexiao4 = sgs.CreateTriggerSkill{
	name = "#luayexiao4" ,
	priority = 100,
	events = {sgs.AskForPeachesDone}, --sgs.QuitDying事件没有Lua接口，用此代替。
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local dying = data:toDying()
		if dying.damage and ((dying.damage:getReason() == "luayexiao") or (dying.damage:getReason() == "luayexiao5")) then
			local from = dying.damage.from
			if from and from:isAlive() and from:isWounded() then
				room:recover(from, sgs.RecoverStruct(from))
			end
		end
	end,
	can_trigger = function(self, target)
		return target
	end
}

yueshiCard = sgs.CreateSkillCard{
	name = "luayueshi",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select)
		if #targets == 0 then
			if not to_select:isKongcheng() then
				return to_select:objectName() ~= sgs.Self:objectName()
			end
		end
		return false
	end,
	on_use = function(self, room, source, targets)
		local success = source:pindian(targets[1], "luayueshi", self)
		if success then 
			local book = false
			local qargets = sgs.SPlayerList()
			for _, target in sgs.qlist(room:getAlivePlayers()) do
				if target:isWounded() then 
					qargets:append(target)
				end 
			end 				
			if not qargets:isEmpty() then 
				local target = room:askForPlayerChosen(source, qargets, "luayueshi1", "luayueshi1", true, true)		
				if target then 
					room:addPlayerMark(target, "yueshi1") 
					room:recover(target, sgs.RecoverStruct(source))
					book = true
				end 
			end 
			if not book then 
				qargets = sgs.SPlayerList()
				for _, target in sgs.qlist(room:getAlivePlayers()) do
					if not target:isNude() then 
						qargets:append(target)
					end 
				end 	
				if not qargets:isEmpty() then 
					local target = room:askForPlayerChosen(source, qargets, "luayueshi2", "luayueshi2", false, true)
					room:addPlayerMark(target, "yueshi2") 
					local id = room:askForCardChosen(source, target, "he", "luayueshi", false, sgs.Card_MethodDiscard)
					room:throwCard(id, target, source)
					--target:drawCards(1)
				end 
			end 
		end 
	end
}
luayueshi = sgs.CreateViewAsSkill{
	name = "luayueshi",
	n = 1,
	view_filter = function(self, selected, to_select)
		return not to_select:isEquipped()
	end,
	view_as = function(self, cards)
		if #cards == 1 then
			local daheCard = yueshiCard:clone()
			daheCard:addSubcard(cards[1])
			return daheCard
		end
	end,
	enabled_at_play = function(self, player)
		if not player:hasUsed("#luayueshi") then
			return not player:isKongcheng()
		end
		return false
	end
}

luayueshiX = sgs.CreateViewAsSkill{
	name = "luayueshiX",
	n = 1,
	view_filter = function(self, selected, to_select)
		return not to_select:isEquipped()
	end,
	view_as = function(self, cards)
		if #cards == 1 then
			local daheCard = yueshiCard:clone()
			daheCard:addSubcard(cards[1])
			return daheCard
		end
	end,
	enabled_at_play = function(self, player)
		if not player:hasUsed("#luayueshi") then
			return not player:isKongcheng()
		end
		return false
	end
}


luayueshi2 = sgs.CreateTriggerSkill{
	name = "#luayueshi",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.ConfirmDamage},
	can_trigger = function(self, player)
		return player 
	end,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()		
		if event == sgs.ConfirmDamage then
            local damage = data:toDamage()
			--room:writeToConsole("露米娅测试" .. damage.to:objectName())
			if damage.to and damage.to:getMark("yueshi1") > 0 then
				room:writeToConsole("rumia test")
				damage.damage = damage.damage + 1
				data:setValue(damage)
				room:broadcastSkillInvoke("luayueshi", 1)
				room:setPlayerMark(damage.to, "yueshi1", 0)
				return false
			end
		end
	end
}
luayueshi3 = sgs.CreateTriggerSkill{
	name = "#luayueshi2" ,
	global = true,
	events = {sgs.CardsMoveOneTime} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.CardsMoveOneTime) then
			local move = data:toMoveOneTime()
			if move.to and move.to:getMark("yueshi2") > 0 and (move.to_place == sgs.Player_PlaceHand or move.to_place == sgs.Player_PlaceEquip) then
				local _moveto
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if move.to:objectName() == p:objectName() then
						_moveto = p
						break
					end
				end 
				room:setPlayerMark(_moveto, "yueshi2", 0)
				_moveto:drawCards(1)
				
			end 
		end 
	end 
}
luayueshi4 = sgs.CreateTriggerSkill{
	name = "#luayueshi3",
	events = {sgs.Pindian},
	on_trigger = function(self, event, player, data)
		local pindian = data:toPindian()
		if player:hasSkill("luayueshiX") then
			player:obtainCard(pindian.to_card)
		end
		return false
	end,
}

luaxiaoan = sgs.CreateTriggerSkill{
	name = "luaxiaoan",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damage},
	on_trigger = function(self, event, player, data, room)
		local damage = data:toDamage()
		local _data = sgs.QVariant()
		_data:setValue(damage.to)
		if damage.to:isAlive() and room:askForSkillInvoke(player, "luaxiaoan", _data) then
			if not room:askForCard(player, ".|black", "@luaxiaoan", data, sgs.Card_MethodDiscard) then
				room:loseMaxHp(damage.to)
			end
		end
	end 
}

rumia:addSkill(luayexiao3)
rumia:addSkill(luayexiao2)
rumia:addSkill(luayexiao)
rumia:addSkill(luayexiao4)
rumia:addSkill(luayueshi)
rumia:addSkill(luayueshi2)
rumia:addSkill(luayueshi3)

rumiaA:addSkill(luayexiao3)
rumiaA:addSkill(luayexiao2)
rumiaA:addSkill(luayexiao)
rumiaA:addSkill(luayexiao4)
rumiaA:addSkill(luayueshi)
rumiaA:addSkill(luayueshi2)
rumiaA:addSkill(luayueshi3)

rumiaB:addSkill(luayexiao3)
rumiaB:addSkill(luayexiao2)
rumiaB:addSkill(luayexiao)
rumiaB:addSkill(luayexiao4)
rumiaB:addSkill(luayueshi)
rumiaB:addSkill(luayueshi2)
rumiaB:addSkill(luayueshi3)
 
--[[
jianji_list = {}
luajianjistart = sgs.CreateTriggerSkill{
	name = "#luajianjistart",
    priority = 10,
    frequency = sgs.Skill_Compulsory,
    events = {sgs.EventPhaseStart},
	on_trigger = function(self,event,player,data)
        local room = player:getRoom()
        if event == sgs.EventPhaseStart and player:getMark("luajianjistart") == 0 then
            if player:hasSkill("luajianji") then
                room:setPlayerProperty(player, "maxhp", sgs.QVariant(player:getMaxHp() + 1))
                room:setPlayerMark(player, "luajianjistart", 1)
            end
        end
		return false
	end
}

luajianji2 = sgs.CreateTriggerSkill{   
	frequency = sgs.Skill_NotFrequent, --打钩发动            
	name = "#luajianji2",            
	events = {sgs.DrawNCards},                 --摸牌阶段发动
	on_trigger = function(self, event, player, data)        
		local room = player:getRoom()                        
		if player:hasSkill("luajianji") and room:askForSkillInvoke(player, "luajianji") then
			local choice1 = {0, 1, 2, 3, 4}		
			local choice = room:askForChoice(player, "luajianji",table.concat(choice1,"+"))	
			local x = tonumber(choice)
			local n = 4 - x
			data:setValue(n)       
			if x == 0 then room:setPlayerCardLimitation(player, "use", ".", false) end 
			room:setPlayerFlag(player, "jianji")
			room:setPlayerMark(player, "jianji", x)	
       end
   end
}

function payRIGHT(self, player)
	if player and player:isAlive() and player:hasSkill(self:objectName()) then return true else return false end
end
luajianji = sgs.CreateTriggerSkill{
	name = "luajianji", 
	global = true,
	frequency = sgs.Skill_Compulsory, 
	events = {sgs.CardUsed, sgs.CardResponded, sgs.EventPhaseEnd, sgs.CardsMoveOneTime}, 
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Play and (event == sgs.CardUsed or event == sgs.CardResponded) then
			local card
			if event == sgs.CardUsed then
				card = data:toCardUse().card
			else
				local response = data:toCardResponse()
				if response.m_isUse then
					card = response.m_card
				end
			end
			if card and card:getHandlingMethod() == sgs.Card_MethodUse and player:getMark("jianji") > 0 then
				room:removePlayerMark(player, "jianji")
				if player:getMark("jianji") == 0 then
					room:setPlayerCardLimitation(player, "use", ".", true)
				end
			end
		elseif event == sgs.CardsMoveOneTime then			
			local move = data:toMoveOneTime()
			if not move.from then return false end
			
			if move.from:objectName() ~= player:objectName() then return false end 
			jianji_list = {}
			if not move.from:hasFlag("jianji") then return false end 
			for _,id in sgs.qlist(move.card_ids) do
				if bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) == sgs.CardMoveReason_S_REASON_DISCARD then
					table.insert(jianji_list, id)
				end
			end
		elseif event == sgs.EventPhaseEnd then
			if (player:getPhase() == sgs.Player_Play or player:getPhase() == sgs.Player_Discard) and payRIGHT(self, player) then
				room:removePlayerCardLimitation(player, "use", ".")
				room:removePlayerCardLimitation(player, "use", ".$1")
				room:setPlayerMark(player, "jianji", 0)
			end 
			if player:getPhase() == sgs.Player_Discard then
				if #jianji_list > 0 and player:hasFlag("jianji") and payRIGHT(self, player)
					and player:isWounded() then
					room:recover(player, sgs.RecoverStruct(player))
				end			
			end 
		end
		return false 
	end
}


luajianji6 = sgs.CreateTriggerSkill{
	name = "#luajianji6",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.PreCardUsed} ,
	on_trigger = function(self, event, youmu, data)
		if event == sgs.PreCardUsed then
			local function YouMuCheck(card, target)
				if card:isKindOf("Hui") or card:isKindOf("Ofuda") then
					return true
				elseif card:isKindOf("FaithCollection") then
					return not target:isNude() 
				elseif card:isKindOf("Banquet") then
					return not target:containsTrick("banquet")
				end
			end
			local use = data:toCardUse()
			local card = use.card
			local room = youmu:getRoom()

			if use.from:objectName() == youmu:objectName() and (use.card:isNDTrick() or use.card:isKindOf("BasicCard")) and use.from:hasSkill("luajianji")
				and use.from:hasFlag("jianji") and use.from:getPhase() == sgs.Player_Play then	
				if (sgs.Sanguosha:getCurrentCardUseReason() ~= sgs.CardUseStruct_CARD_USE_REASON_PLAY) then return false end
				local available_targets = sgs.SPlayerList()
				if (not use.card:isKindOf("AOE")) and (not use.card:isKindOf("GlobalEffect")) then
					--room:setPlayerFlag(youmu, "luajianjiExtraTarget")
					for _, p in sgs.qlist(room:getAlivePlayers()) do
						if (use.to:contains(p) or room:isProhibited(youmu, p, use.card)) then continue end
						if (use.card:targetFixed()) then
							if (not use.card:isKindOf("Peach")) or (p:isWounded()) then
								available_targets:append(p)
							end
						else
							if (use.card:targetFilter(sgs.PlayerList(), p, youmu)  or YouMuCheck(use.card, p)) then
								available_targets:append(p)
							end
						end
					end
					--room:setPlayerFlag(youmu, "-luajianjiExtraTarget")
				end
				local extra
				if not use.card:isKindOf("Collateral") then	
				
					local Carddata2 = sgs.QVariant() -- ai用
					Carddata2:setValue(use.card)
					room:setTag("luajianjiTC", Carddata2)				
					
					extra = room:askForPlayerChosen(youmu, available_targets, "luajianjic", "luajianjic", true, true)
					room:removeTag("luajianjiTC")
					if extra then 
						use.to:append(extra)
					end 
				end 
				room:sortByActionOrder(use.to)		
				data:setValue(use)
				return false	
			end 
		end 
		return false	
	end
}]]--
--sp_youmu:addSkill(luajianjistart)

luajianjiVS = sgs.CreateViewAsSkill{
	name = "luajianji",
	n = 1,
	view_filter = function(self, selected, to_select)
		if to_select:isRed() and sgs.Self:getMark("luajjSlash") > 0 then
            local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
            slash:addSubcard(to_select:getEffectiveId())
			slash:deleteLater()
			return slash:isAvailable(sgs.Self) and not sgs.Self:isJilei(to_select)
		elseif to_select:isBlack() and to_select:isKindOf("Slash") and sgs.Self:getMark("luajjanaleptic") > 0 then
			return sgs.Analeptic_IsAvailable(sgs.Self) and not sgs.Self:isJilei(to_select)
		elseif to_select:isBlack() and to_select:isKindOf("Slash") then
			return not sgs.Self:isJilei(to_select)
        end 
	end ,
	view_as = function(self, cards)
		if #cards > 0 then 
			local card = cards[1]
			local new_card
			if card:isRed() and sgs.Self:getMark("luajjSlash") > 0 then
				new_card = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, 0)
			end
			if card:isBlack() and card:isKindOf("Slash") and sgs.Self:getMark("luajjanaleptic") > 0 then
				new_card = sgs.Sanguosha:cloneCard("analeptic", sgs.Card_SuitToBeDecided, 0)
			end
			if card:isBlack() and card:isKindOf("Slash") and sgs.Self:getMark("luajjduel") > 0 then
				new_card = sgs.Sanguosha:cloneCard("duel", sgs.Card_SuitToBeDecided, 0)
			end
			if card:isBlack() and card:isKindOf("Slash") and sgs.Self:getMark("luajjdismantlement") > 0 then
				new_card = sgs.Sanguosha:cloneCard("dismantlement", sgs.Card_SuitToBeDecided, 0)
			end
			if card:isBlack() and card:isKindOf("Slash") and sgs.Self:getMark("luajjclearmind") > 0 then
				new_card = sgs.Sanguosha:cloneCard("clearmind", sgs.Card_SuitToBeDecided, 0)
			end
			if card:isBlack() and card:isKindOf("Slash") and sgs.Self:getMark("luajjcollateral") > 0 then
				new_card = sgs.Sanguosha:cloneCard("collateral", sgs.Card_SuitToBeDecided, 0)
			end
			if new_card then
				new_card:setSkillName(self:objectName())
				new_card:addSubcard(card)
			end
			return new_card
		end 
	end,
	enabled_at_play = function(self, player)
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luajianji"
	end
}
luajianji = sgs.CreateTriggerSkill{
	name = "luajianji" ,
	view_as_skill = luajianjiVS,
	events = {sgs.CardFinished} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.CardFinished then
			local use = data:toCardUse()
			if not use.from then return end  
			if player:hasSkill("luajianji") and player:objectName() == use.from:objectName() and player:isAlive() then 
				room:removePlayerMark(player, "luajjSlash") 
				room:removePlayerMark(player, "luajjanaleptic") 
				room:removePlayerMark(player, "luajjduel") 
				room:removePlayerMark(player, "luajjdismantlement") 
				room:removePlayerMark(player, "luajjclearmind") 
				room:removePlayerMark(player, "luajjcollateral") 
				if use.card and use.card:isKindOf("Slash") and use.card:isBlack() then
					room:addPlayerMark(player, "luajjSlash")
					room:askForUseCard(player,  "@@luajianji", "@luajianji")
				elseif use.card and use.card:isKindOf("Slash") and use.card:isRed()
					and room:askForSkillInvoke(player, self:objectName(), data) then  
					local choice = room:askForChoice(player, "luajianji", "analeptic+duel+dismantlement+collateral+clearmind")
					room:addPlayerMark(player, "luajj" .. choice)
					room:askForUseCard(player,  "@@luajianji", "@luajianji")
				else
					return false 
				end   
				local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				for _, equip in sgs.qlist(player:getEquips()) do 
					if equip:isKindOf("Weapon") and room:askForSkillInvoke(player, "luajianji2", data) then
						dummy:addSubcard(equip:getEffectiveId())
					end 
				end					
				room:obtainCard(player, dummy) 
			end
		end
		return false
	end,
	can_trigger = function(self, target)
		return target
	end
}

luayishancard = sgs.CreateSkillCard{
	name = "luayishan",	
	handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		return #targets == 0 and to_select:objectName() ~= sgs.Self:objectName()
	end,
	on_use = function(self, room, source, targets) 
		local cardX = sgs.Sanguosha:getCard(self:getSubcards():first())
		room:useCard(sgs.CardUseStruct(cardX, source, source))

		room:doAnimate(1, source:objectName(), targets[1]:objectName())
		room:getThread():delay(750)

		room:swapSeat(source, targets[1])  
		room:removePlayerMark(source, "@yishan") 

		local lord = room:getLord()
		local target = room:getLord()
		while target do
			if source:inMyAttackRange(target) and target and target:isAlive() and source:isAlive() then
				room:doAnimate(1, source:objectName(), target:objectName())
				local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				dummy:addSubcard(room:getDrawPile():first())
				if source:canSlash(target, dummy, false) then
					dummy:setSkillName("luayishan")
					room:useCard(sgs.CardUseStruct(dummy, source, target), true)
				end	
			end 
			target = target:getNextAlive()
			if target:objectName() == lord:objectName() then break end 
		end 
	end
}
luayishanVS = sgs.CreateViewAsSkill{
	name = "luayishan",
	n = 1,
	view_filter = function(self, selected, to_select)
		return to_select:isKindOf("Weapon") and not to_select:isEquipped()
	end,
	view_as = function(self, cards)
		if #cards > 0 then 
			local card_1 = luayishancard:clone()
			for _, c in ipairs(cards) do
				card_1:addSubcard(c)
			end
			return card_1
		end 
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@yishan") > 0
	end,
}
luayishan = sgs.CreateTriggerSkill{
	name = "luayishan",
	frequency = sgs.Skill_Limited,
	limit_mark = "@yishan",
	view_as_skill = luayishanVS,
	events = {sgs.BeforeCardsMove, sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.BeforeCardsMove then

		end
	end 
}
 
sp_youmu:addSkill(luajianji)
sp_youmu:addSkill(luayishan)

sp_youmuA:addSkill(luajianji)
sp_youmuA:addSkill(luayishan) 

sp_youmuB:addSkill(luajianji)
sp_youmuB:addSkill(luayishan) 

sp_youmuC:addSkill(luajianji)
sp_youmuC:addSkill(luayishan) 

luajifeng2 = sgs.CreateTriggerSkill{
	name = "#luajifeng2" ,
	--global = true ,
	frequency = sgs.Skill_NotFrequent ,
	events = {sgs.BeforeCardsMove, sgs.EventPhaseStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart then
			if player:getPhase() == sgs.Player_Start and player:hasSkill("luajifeng") and room:askForUseCard(player,  "@@luajifeng", "@luajifeng") then
				return false
			end
			return false
		end
		if player:getPile("fong"):length() > 0 then return false end
		local move = data:toMoveOneTime()
		if not move.from then return false end 

		
		local aicard = {}
		local j = 0
		local bool = false
		for _,card_id in sgs.qlist(move.card_ids) do
			if room:getCardOwner(card_id) and room:getCardOwner(card_id):objectName() == move.from:objectName()
				and room:getCardOwner(card_id):objectName() == player:objectName() then
				local place = move.from_places:at(j)
				if place == sgs.Player_PlaceHand or place == sgs.Player_PlaceEquip then
					bool = true 
					table.insert(aicard, card_id)
				end
			end
			j = j + 1
		end
		
		local _movefrom
		for _, p in sgs.qlist(room:getAlivePlayers()) do
			if move.from:objectName() == p:objectName() then
				_movefrom = p
				break
			end 
		end 
		local reason = move.reason
		local basic = bit32.band(reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)		
		if _movefrom and _movefrom:objectName() == player:objectName() and _movefrom:hasSkill("luajifeng") and bool
			and ((basic == sgs.CardMoveReason_S_REASON_DISCARD) or (basic == sgs.CardMoveReason_S_REASON_GOTCARD)) then				
			local room0 = player:getRoom()
			for _, p in sgs.qlist(room0:findPlayersBySkillName("luajifeng")) do

				local duel = sgs.Sanguosha:cloneCard("duel", sgs.Card_NoSuit, 0)
				duel:addSubcards(player:getHandcards())
				if player:getEquips() then duel:addSubcards(player:getEquips()) end
				local i = 0				
				if _movefrom:askForSkillInvoke("luajifeng", sgs.QVariant(table.concat(aicard, "+"))) then
					room:setPlayerFlag(player, "DontDiscard")
					local old_card_ids = {}
					for _,card_idX in sgs.qlist(move.card_ids) do	
						table.insert(old_card_ids, card_idX)
					end 
					for _, card_idY in ipairs(old_card_ids) do					
						if (duel:getSubcards():contains(card_idY)) then
							move.card_ids:removeOne(card_idY)
							move.from_places:removeAt(i)
						else
							i = i + 1
						end
					end
					player:addToPile("fong", duel:getSubcards())
					room:setPlayerFlag(player, "-DontDiscard")
				end
			end
			data:setValue(move)
		end 	
		return false
	end,
}

luajifengcard = sgs.CreateSkillCard{
	name = "luajifeng",
	target_fixed = false,
	filter = function(self, targets, to_select)
		return (#targets < 1) and ((to_select:getEquips() and to_select:getEquips():length() > 0) or (to_select:getJudgingArea() and to_select:getJudgingArea():length() > 0))
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		local fong = effect.from:getPile("fong")
		local dummy = sgs.Sanguosha:cloneCard("jink")
		if not fong:isEmpty() then
			dummy:addSubcards(fong)				
		end	
		effect.from:obtainCard(dummy)
		local id = room:askForCardChosen(effect.from, effect.to, "ej", self:objectName(), false, sgs.Card_MethodDiscard)
		room:throwCard(id, effect.to, effect.from)			
	end 
}
	
luajifeng = sgs.CreateZeroCardViewAsSkill{
	name = "luajifeng",	
	view_as = function()
		return luajifengcard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:isKongcheng() and player:getPile("fong"):length() > 0
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luajifeng" and player:getPile("fong"):length() > 0
	end
}

luaqucai = sgs.CreateTriggerSkill {
	name = "luaqucai", 
	events = {sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:isKongcheng() then return end
		if player:getPhase() == sgs.Player_Start and not player:isKongcheng() and room:askForSkillInvoke(player, self:objectName()) then
			local card = room:askForCard(player, ".|.|.|hand!", "@luaqucai", sgs.QVariant(), sgs.Card_MethodNone)
			if not card then return end
			room:throwCard(card, player, player)
			if not card:isKindOf("Jink") and not card:isKindOf("Nullification") and not card:isKindOf("sakura") and not card:isKindOf("Collateral") 
				and not card:isKindOf("EquipCard") and not card:isKindOf("DelayedTrick") then 
				local dummy = sgs.Sanguosha:cloneCard(card:objectName(), sgs.Card_NoSuit, 0)
				if player:isCardLimited(dummy, sgs.Card_MethodUse) then return false end 
				local players = sgs.SPlayerList()
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if not player:isProhibited(p, dummy) then 
						players:append(p)
					end 									
				end 
				
				local Carddata2 = sgs.QVariant() -- ai用
				Carddata2:setValue(dummy)
				room:setTag("luaqucaiTC", Carddata2)				
				local target = room:askForPlayerChosen(player, players, "luaqucai", "luaqucaiH") 
				room:removeTag("luaqucaiTC")	
				
				if dummy:isKindOf("AOE") or dummy:isKindOf("AmazingGrace") or dummy:isKindOf("GodSalvation") then
					room:setPlayerFlag(player, "qucaiAOE")
					room:setPlayerFlag(target, "qucaiAOEs")
					room:useCard(sgs.CardUseStruct(dummy, player, sgs.SPlayerList()))
					if room:getDiscardPile() and room:getDiscardPile():length() > 0 then 
						local card0 = room:getDiscardPile():at(0)
						card0 = sgs.Sanguosha:getCard(card0)
						local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, player:objectName(), nil, "luaqucai", nil)
						room:moveCardTo(card0, nil, nil, sgs.Player_DrawPile, reason, true)							
					end 
					room:setPlayerFlag(player, "-qucaiAOE")
					room:setPlayerFlag(target, "-qucaiAOEs")			
					return false 
				end 
				room:useCard(sgs.CardUseStruct(dummy, player, target))
				if room:getDiscardPile() and room:getDiscardPile():length() > 0 then 
					local card0 = room:getDiscardPile():at(0)
					card0 = sgs.Sanguosha:getCard(card0)
					local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, player:objectName(), nil, "luaqucai", nil)
					room:moveCardTo(card0, nil, nil, sgs.Player_DrawPile, reason, true)							
				end 				
				return false 
			end 
		end
	end
}
 
luaqucai2 = sgs.CreateTriggerSkill{
	name = "#luaqucai",
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.PreCardUsed},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.PreCardUsed then
			local use = data:toCardUse()
			local card = use.card 
			if use.from and use.from:objectName() == player:objectName() and use.from:hasFlag("qucaiAOE") then 
				use.to = sgs.SPlayerList()
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if p:hasFlag("qucaiAOEs") then use.to:append(p) end
				end
			end
			data:setValue(use)
			return false
		end
	end 
}

luajizouCard = sgs.CreateSkillCard{
	name = "luajizou" ,
	target_fixed = true,
	on_use = function(self, room, source, targets)
		local cardid = source:getMark("luajizoum") - 1
		local card = sgs.Sanguosha:getCard(cardid)

		if not card:isKindOf("Jink") and not card:isKindOf("Nullification") and not card:isKindOf("sakura") and not card:isKindOf("Collateral")  
			and not card:isKindOf("EquipCard") and not card:isKindOf("DelayedTrick") then 
			local dummy = sgs.Sanguosha:cloneCard(card:objectName(), sgs.Card_NoSuit, 0)
			if source:isCardLimited(dummy, sgs.Card_MethodUse) then return false end 
			local players = sgs.SPlayerList()
			local qtargets = sgs.PlayerList()
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if not source:isProhibited(p, dummy) 
					and not ((card:isKindOf("FireAttack") or card:isKindOf("quanxiang")) and p:isKongcheng())
					and not ((card:isKindOf("Snatch") or card:isKindOf("Dismantlement")) and p:isAllNude())
					and not (card:isKindOf("Peach") and not p:isWounded())
					and not (card:targetFixed() and p:objectName() ~= source:objectName()) then 
					players:append(p)
				end 									
			end 
			
			if dummy:isKindOf("AOE") or dummy:isKindOf("AmazingGrace") or dummy:isKindOf("GodSalvation") then 
				room:useCard(sgs.CardUseStruct(dummy, source, sgs.SPlayerList())) 
				if not source:isNude() then room:askForDiscard(source, self:objectName(), 1, 1, false, true, "luajizou") end 
				return false 
			end  
			if players:isEmpty() then return false end
			local Carddata2 = sgs.QVariant() -- ai用
			Carddata2:setValue(dummy)
			room:setTag("luaqucaiTC", Carddata2)				
			local target = room:askForPlayerChosen(source, players, "luajizou", "luajizouH") 
			room:removeTag("luaqucaiTC")	

			room:useCard(sgs.CardUseStruct(dummy, source, target)) 
			if not source:isNude() then room:askForDiscard(source, self:objectName(), 1, 1, false, true, "luajizou") end 
			return false 
		end 
	end
}
luajizouVS = sgs.CreateZeroCardViewAsSkill{
	name = "luajizou" ,
	view_as = function(self, cards)
		return luajizouCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("luajizoum") > 0 and not player:isNude()
	end, 
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luajizou" and player:getMark("luajizoum") > 0
	end
}
luajizou = sgs.CreateTriggerSkill{
	name = "luajizou" , 
	view_as_skill = luajizouVS,
	events = {sgs.EventPhaseStart, sgs.CardsMoveOneTime} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseStart and player:getPhase() == sgs.Player_Start then  
			if not player:isNude() and player:getMark("luajizoum") > 0 and room:askForSkillInvoke(player, self:objectName(), data) then  
				room:askForUseCard(player, "@@luajizou", "@luajizou") 
			end 
			return false 
		end
		return false
	end, 
}
luajizou3 = sgs.CreateTriggerSkill{
	name = "#luajizou2" ,
	priority = 2 ,
	events = {sgs.CardsMoveOneTime},   
	on_trigger = function(self, event, player, data)
		local move = data:toMoveOneTime()
		local room = player:getRoom() 
		if move.from and move.from:objectName() == player:objectName() and player:hasSkill("luajizou") and not move.card_ids:isEmpty() then
			room:writeToConsole("Hello World! 2024-11-13 21:59:39")
			if move.to_place == sgs.Player_DiscardPile or move.to_place == sgs.Player_PlaceTable then		
				if not (move.from_places:contains(sgs.Player_PlaceHand) or move.from_places:contains(sgs.Player_PlaceEquip)) then return false end  
				for _, id in sgs.qlist(move.card_ids) do   
					if room:getCardPlace(id) == sgs.Player_DiscardPile or room:getCardPlace(id) == sgs.Player_PlaceTable then
						room:setPlayerMark(player, "luajizoum", id + 1)
						local card = sgs.Sanguosha:getCard(id)
						room:writeToConsole("Hello World!" .. card:objectName())
						if card:getSuit() == sgs.Card_Spade and not player:hasFlag("luajizouSpade") then 
							room:setPlayerFlag(player, "luajizouSpade") 
						elseif card:getSuit() == sgs.Card_Club and not player:hasFlag("luajizouClub") then
							room:setPlayerFlag(player, "luajizouClub")  
						elseif card:getSuit() == sgs.Card_Diamond and not player:hasFlag("luajizouDiamond") then
							room:setPlayerFlag(player, "luajizouDiamond")  
						elseif card:getSuit() == sgs.Card_Heart and not player:hasFlag("luajizouHeart") then
							room:setPlayerFlag(player, "luajizouHeart")  
						end 
						if player:hasFlag("luajizouSpade") and player:hasFlag("luajizouClub") 
							and player:hasFlag("luajizouDiamond") and player:hasFlag("luajizouHeart") then 
							room:setPlayerMark(player, "@luajizoux", 1) 
						end 
					end 
				end				
			end 
		end  
	end, 
}

luajizou2 = sgs.CreateTriggerSkill{
	name = "#luajizou" ,
	events = {sgs.EventPhaseChanging, sgs.EventPhaseStart},  
	global = true, 
	on_trigger = function(self, event, player, data)
		if event == sgs.EventPhaseChanging then 
			local room = player:getRoom()
			local change = data:toPhaseChange()
			if change.to ~= sgs.Player_NotActive then return false end
			local shensimayi = player:getRoom():findPlayerBySkillName("luajizou")
			if (not shensimayi) or (shensimayi:getMark("@luajizoux") <= 0) then return false end
			local n = shensimayi:getMark("@luajizoux")
			room:setPlayerMark(shensimayi, "@luajizoux", 0) 
			if shensimayi:hasFlag("luajizouSpade") then 
				room:setPlayerFlag(shensimayi, "-luajizouSpade")
			elseif shensimayi:hasFlag("luajizouClub") then
				room:setPlayerFlag(shensimayi, "-luajizouClub") 
			elseif shensimayi:hasFlag("luajizouDiamond") then
				room:setPlayerFlag(shensimayi, "-luajizouDiamond") 
			elseif shensimayi:hasFlag("luajizouHeart") then
				room:setPlayerFlag(shensimayi, "-luajizouHeart") 
			end 
			if not shensimayi:askForSkillInvoke("luajizoux") then return false end 
			local playerdata = sgs.QVariant()
			playerdata:setValue(shensimayi)
			shensimayi:getRoom():setTag("luajizouExtraTurn", playerdata)
			return false
		else 
			local room = player:getRoom()
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if room:getTag("luajizouExtraTurn") then
					local target = room:getTag("luajizouExtraTurn"):toPlayer()
					room:removeTag("luajizouExtraTurn")
					if target and target:isAlive() then 
						room:setPlayerMark(target, "@extra_turn", 1) 
						target:gainAnExtraTurn()
						room:setPlayerMark(target, "@extra_turn", 0)
					end
				end
			end  
		end 
	end  
}  
  

aya:addSkill(luajifeng2)
aya:addSkill(luajifeng)
aya:addSkill(luaqucai)
aya:addSkill(luaqucai2)

ayaA:addSkill(luajifeng2)
ayaA:addSkill(luajifeng)
ayaA:addSkill(luaqucai)
ayaA:addSkill(luaqucai2)
  
ayaB:addSkill(luajifeng2)
ayaB:addSkill(luajifeng)
ayaB:addSkill(luaqucai)
ayaB:addSkill(luaqucai2)
  
ayaC:addSkill(luajizou) 
ayaC:addSkill(luajizou2) 
ayaC:addSkill(luajizou3) 
   
ayaD:addSkill(luajizou) 
ayaD:addSkill(luajizou2) 
ayaD:addSkill(luajizou3) 
  
ayaE:addSkill(luajizou) 
ayaE:addSkill(luajizou2) 
ayaE:addSkill(luajizou3) 

luaxingyunCard = sgs.CreateSkillCard{
	name = "luaxingyun",
	filter = function(self, targets, to_select)
		return (#targets < 1) and not to_select:isNude()
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		local card_1 = room:askForCard(effect.to, ".|.|.|.!", "@luaxingyun", sgs.QVariant(), sgs.Card_MethodDiscard)
		local card_2 = room:askForCard(effect.from, ".|.|.|.!", "@luaxingyun2", sgs.QVariant(), sgs.Card_MethodDiscard)
		if not card_1 then return end 
		if not card_2 then return end 
		local ids = sgs.IntList()
		ids:append(card_1:getId())
		ids:append(card_2:getId())
	    local move = sgs.CardsMoveStruct()
	    move.card_ids = ids
	    move.to = nil
	    move.to_place = sgs.Player_DiscardPile
	    move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_DISCARD, effect.from:objectName(), self:objectName(), "")
	    room:moveCardsAtomic(move, true)		
		
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		local targets_list = sgs.SPlayerList()
		for _, target in sgs.qlist(room:getAllPlayers()) do
			if effect.to:canSlash(target, slash, false) then
				targets_list:append(target)
			end
		end
		room:setPlayerFlag(effect.to, "xingyun")
		local Carddata2 = sgs.QVariant() -- ai用
		Carddata2:setValue(card_1)
		room:setTag("luaxingyun", Carddata2)		
		local target_0 = room:askForPlayerChosen(effect.from, targets_list, self:objectName(), "luaxingyun2", false, true)
		room:removeTag("luaxingyun")
		if target_0 then
			local slash2 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			slash2:setSkillName("luaxingyun")
			room:useCard(sgs.CardUseStruct(slash2, effect.to, target_0))
		end		
		
		if effect.to:hasFlag("xingyun") then 
			local ids2 = sgs.IntList()
			ids2:append(card_1:getId())
			ids2:append(card_2:getId())
			local move2 = sgs.CardsMoveStruct()
			move2.card_ids = ids2
			move2.to = effect.from
			move2.to_place = sgs.Player_PlaceHand
			move2.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, effect.from:objectName(), self:objectName(), "")
			room:moveCardsAtomic(move2, true)
		else
			local ids2 = sgs.IntList()
			ids2:append(card_1:getId())
			ids2:append(card_2:getId())
			local move2 = sgs.CardsMoveStruct()
			move2.card_ids = ids2
			move2.to = effect.to
			move2.to_place = sgs.Player_PlaceHand
			move2.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, effect.from:objectName(), self:objectName(), "")
			room:moveCardsAtomic(move2, true)
		end 
	end
}
luaxingyun = sgs.CreateZeroCardViewAsSkill{
	name = "luaxingyun",
	
	view_as = function()
		return luaxingyunCard:clone()
	end,

	enabled_at_play = function(self, player)
		return (not player:hasUsed("#luaxingyun")) and not player:isNude()
	end
}

luaxingyun2 = sgs.CreateTriggerSkill{
	name = "#luaxingyun" ,
	global = true,
	events = {sgs.PreDamageDone} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.PreDamageDone then 
            local damage = data:toDamage()
            if damage.card and damage.card:isKindOf("Slash") and damage.card:getSkillName() == "luaxingyun" and damage.from then 
				room:setPlayerFlag(damage.from, "-xingyun")
            end 
        end 
        return false
	end,
	can_trigger = function(self, target)
		return target
	end
}
tewi:addSkill(luaxingyun)
tewi:addSkill(luaxingyun2)

honghuanCard = sgs.CreateSkillCard{
	name = "Luahonghuan",
	will_throw = false,
	target_fixed = true,
	on_use = function(self, room, source, targets)
		room:detachSkillFromPlayer(source, "luayewang3")
		for _,card in sgs.qlist(source:getHandcards()) do
			if card:getId() == self:getSubcards():at(0) then
				room:setCardFlag(card, "luayewang")
			end
		end
		room:acquireSkill(source, "luayewang3", false)

	end
}
Luahonghuan = sgs.CreateOneCardViewAsSkill{
	name = "Luahonghuan$",
	filter_pattern = ".",
	view_as = function(self, card)
		local skill_card = honghuanCard:clone()
		skill_card:addSubcard(card)
		skill_card:setSkillName(self:objectName())
		return skill_card
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#Luahonghuan")
	end
	
}
--[[
Luahonghuan2 = sgs.CreateTriggerSkill{
	name = "#Luahonghuan2" ,
	events = {sgs.GameStart} ,
	on_trigger = function(self, event, player, data)
		if player:hasLordSkill("Luahonghuan") then player:gainMark("@honghuan", 1) end
	end
}

Luahonghuan3 = sgs.CreateTriggerSkill{
	name = "#Luahonghuan3",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damage, sgs.PreDamageDone, sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local room = player:getRoom()
		if (event == sgs.PreDamageDone) and damage.from and (damage.from:getMark("@scarlet") > 0) and damage.from:isAlive() and damage.card then
			local remilia = damage.from
			local boolean = damage.card:isKindOf("Slash")
			remilia:setTag("Luahongzhuan2", sgs.QVariant(boolean))
		elseif (event == sgs.Damage) and (player:getMark("@scarlet") > 0) and player:isAlive() then
			local invoke = player:getTag("Luahongzhuan2"):toBool()
			player:setTag("Luahongzhuan2", sgs.QVariant(false))
			if invoke and player:isWounded() then
				local recover = sgs.RecoverStruct()
				recover.who = player
				recover.recover = damage.damage
				room:recover(player, recover)
			end
		elseif event == sgs.EventPhaseStart and player:getPhase() == sgs.Player_RoundStart and player:hasLordSkill("Luahonghuan") then 
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				p:loseAllMarks("@scarlet")
			end 
		end
		return false
	end,
	can_trigger = function(self, target)
		return target
	end
}
LuahonghuanMod = sgs.CreateTargetModSkill{
	name = "#LuahonghuanMod" ,
	distance_limit_func = function(self, player, card)
		if (player:getMark("@scarlet") > 0) and card:isKindOf("Slash") then 
			return 1000
		else
			return 0
		end
	end
}
Luahonghuan4 = sgs.CreateProhibitSkill{
	name = "#Luahonghuan4" ,
	is_prohibited = function(self, from, to, card)
		local remilia
		for _,p in sgs.qlist(from:getAliveSiblings()) do
			if p:hasSkill("Luahonghuan") and p:isAlive() then remilia = p end 
		end 			
		if remilia and (remilia:getMark("@scarlet") > 0) and (from:getMark("@scarlet") == 0) then 
			return card:isKindOf("Slash") and (from:distanceTo(to) > 1) and not string.find(string.lower(card:getSkillName()),"nosguhuo")--特别注意旧蛊惑
		end 
	end
}

Luahonghuan5 = sgs.CreateTriggerSkill{
	name = "#Luahonghuan5",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Death, sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		if player:isAlive() then
			local room = player:getRoom()
			local y = 0
			for _,p in sgs.qlist(room:getAlivePlayers()) do
				if p:getRole() == "loyalist" and p:isAlive() then y = y + 1 end
			end
			room:setPlayerMark(player, "vampire", y)
		end
	end
}
]]--

luayewangCard = sgs.CreateSkillCard{
	name = "luayewang",
	target_fixed = true,
	on_use = function(self, room, source, targets)
		room:detachSkillFromPlayer(source, "luayewang3")
		for _,card in sgs.qlist(source:getHandcards()) do
			room:setCardFlag(card, "luayewang")
		end
		room:acquireSkill(source, "luayewang3", false)

		source:drawCards(1)
	end
}
luayewang = sgs.CreateZeroCardViewAsSkill{
	name = "luayewang",
	view_as = function(self, cards)
		return luayewangCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:hasSkill("luayewang") and not player:hasUsed("#luayewang")
	end,
}


remilia:addSkill(Luahonghuan)
--[[
remilia:addSkill(Luahonghuan2)
remilia:addSkill(Luahonghuan3)
remilia:addSkill(LuahonghuanMod)
remilia:addSkill(Luahonghuan4)
remilia:addSkill(Luahonghuan5)
]]--
remiliaA:addSkill(Luahonghuan)
--[[
remiliaA:addSkill(Luahonghuan2)
remiliaA:addSkill(Luahonghuan3)
remiliaA:addSkill(LuahonghuanMod)
remiliaA:addSkill(Luahonghuan4)
remiliaA:addSkill(Luahonghuan5)
]]--
remiliaB:addSkill(Luahonghuan)
--[[
remiliaB:addSkill(Luahonghuan2)
remiliaB:addSkill(Luahonghuan3)
remiliaB:addSkill(LuahonghuanMod)
remiliaB:addSkill(Luahonghuan4)
remiliaB:addSkill(Luahonghuan5)
]]--
remiliaC:addSkill(Luahonghuan)
--[[
remiliaC:addSkill(Luahonghuan2)
remiliaC:addSkill(Luahonghuan3)
remiliaC:addSkill(LuahonghuanMod)
remiliaC:addSkill(Luahonghuan4)
remiliaC:addSkill(Luahonghuan5)
]]-- 
lualindong = sgs.CreateTriggerSkill{
	name = "lualindong",
	frequency = sgs.Skill_Frequent,
	events = {sgs.DrawNCards, sgs.EventPhaseEnd, sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.DrawNCards then
			if player:getMark("lualindongA") == 0 and player:getMark("lualindong") == 0 then
				local choice = room:askForChoice(player, "lualindong", "notAct+DrawA+DrawB")
				if choice == "DrawA" then
					local count = data:toInt() + 1
					data:setValue(count)
					room:setPlayerMark(player, "lualindong", 1)
					player:addMark("@lindongs", 1)
				elseif choice == "DrawB" then
					local count = data:toInt() - 1
					data:setValue(count)
					local Carddata2 = sgs.QVariant() -- ai用
					Carddata2:setValue(sgs.Sanguosha:getCard(room:getDrawPile():first()))
					player:setTag("lualindongTC", Carddata2)
					room:setPlayerMark(player, "lualindongA", 1)
				end
			end
		elseif event == sgs.EventPhaseEnd then
			if player:getPhase() == sgs.Player_Draw and player:getMark("lualindongA") > 0 then
				local card1 = player:getTag("lualindongTC"):toCard()
				
				room:setPlayerFlag(player, "luaaoshuNull")
				local card2 = room:askForUseCard(player, "TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand", "@lualindong") 
				room:setPlayerFlag(player, "-luaaoshuNull")
				local targets = sgs.SPlayerList()
				for  _, kplayer in sgs.qlist(room:getAlivePlayers()) do
					if not kplayer:isNude() then targets:append(kplayer) end
				end
				if targets:isEmpty() then return false end
				local Carddata2 = sgs.QVariant() -- ai用
				Carddata2:setValue(card2)
				player:setTag("lualindongTC2", Carddata2)
				room:writeToConsole(player:getTag("lualindongTC"):toCard():objectName())
				local target = room:askForPlayerChosen(player, targets, "lualindong", "lualindongA", false, false)
				if target then
					local cardid = room:askForCardChosen(player, target, "hej", self:objectName(), false, sgs.Card_MethodDiscard)
					player:removeTag("lualindongTC2")
					player:removeTag("lualindongTC")
					local card3 = sgs.Sanguosha:getCard(cardid)
					room:throwCard(card3, target, player)
					if card2 and card1:getTypeId() ~= card2:getTypeId() and card2:getTypeId() ~= card3:getTypeId() then
						local target2 = room:askForPlayerChosen(player, room:getAlivePlayers(), "lualindong2", "lualindongB", false, false)
						local function canLoseHp()
							for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
								if hecatiaX and isFriendQ(room, target2, hecatiaX) and target2:objectName() ~= hecatiaX:objectName()
										and target2:getHp() == hecatiaX:getHp() then
									room:notifySkillInvoked(hecatiaX, "luayiti")
									return false
								end 
							end 
							for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
								if Erin and Erin:getKingdom() == target2:getKingdom() then
									room:notifySkillInvoked(Erin, "luajiance")
									return false
								end 
							end 
							return true
						end 
						if canLoseHp() then room:loseHp(target2) end 
					end
				end
				player:removeTag("lualindongTC2")
				player:removeTag("lualindongTC")
			end
		elseif event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_Play and player:objectName() == room:getCurrent():objectName()
				and (player:getMark("lualindongA") > 0 or player:getMark("lualindong") > 0)  then
				player:skip(sgs.Player_Play)
				room:setPlayerMark(player, "lualindongA", 0)
				room:setPlayerMark(player, "lualindong", 0)
			end
		end
	end
}
whiterock:addSkill(lualindong)
sgs.LoadTranslationTable{
	["pay5"] = "求闻史纪", --注意这里每次要加逗号
	["remilia"] = "蕾米莉亚",
	["remilia_boss"] = "蕾米莉亚",
	["remiliaA"] = "蕾米莉亚",
	["remiliaB"] = "蕾米莉亚",
	["remiliaC"] = "蕾米莉亚",
	["#remilia"]= "绯色恶魔",
	["Luashenqiang2"] = "命运",
	["@Luashenqiang2"] = "请弃置一张手牌，若此牌点数不大于此【杀】，则你不能使用【闪】响应此【杀】，且此【杀】花色改为红桃。",
	[":Luashenqiang2"] = "当你使用【杀】指定目标前，其必须弃置一张手牌，若此牌点数不大于此【杀】，则其不能使用【闪】响应此【杀】，且此【杀】花色改为红桃。",
	["Luahongzhuan"] = "神枪",
	[":Luahongzhuan"] = "锁定技，你使用红色【杀】无限制，且若对一名其他角色造成伤害后，你回复1点体力。",
	["Luahonghuan"] = "夜王",
	[":Luahonghuan"] = "主公技，出牌阶段限一次，你可以展示你的一张手牌。以此法展示的红色牌视为【杀】，黑色牌视为【闪】。",
	["luayewang"] = "夜王",
	["luayewang3"] = "夜王",
	[":luayewang"] = "出牌阶段限一次，你可以展示你的全部手牌，然后摸一张牌。直到下次发动“夜王”前，以此法展示的红色牌均视为【杀】，黑色牌均视为【闪】。",

	["yuyuko"] = "西行寺幽幽子",
	["yuyukoA"] = "西行寺幽幽子",
	["yuyukoB"] = "西行寺幽幽子",
	["yuyukoC"] = "西行寺幽幽子",
	["yuyukoD"] = "西行寺幽幽子",
	["yuyukoE"] = "西行寺幽幽子",
	["#yuyuko"]= "华胥的亡灵",
	["luayouqu"] = "幽蝶",
	["youqu"] = "幽蝶",
	[":luayouqu"] = "出牌阶段限一次，你可以令一名角色将一张手牌当作任意基本牌使用，若此牌与当作牌牌名相同，则你重置此技能或摸一张牌。",
	["@luayouqu"] = "选择要使用的牌,现在不用决定目标。",
	["luayouquX"] = "选择此基本牌的目标",
	["luafanhun"] = "返魂",
	["@luafanhun"] = "你可以使用一张牌",
	[":luafanhun"] = "一名角色濒死时，你可以令其体力值改为1，摸X-2张牌并使用至多X+2张牌（X为其本局造成过的伤害数）。若如此做，其死亡，你对自己造成两点伤害。你于“返魂”期间不能再发动“返魂”。",
	
	["meiling"] = "红美铃",
	["#meiling"]= "紅魔館門番",		
	["lualongxi"] = "龙息",
	[":lualongxi"] = "出牌阶段，你可以将手牌补至与体力值相同，然后失去一点体力。",
	["LuaTaiji"] = "太极",	
	[":LuaTaiji"] = "回合开始阶段，你可以弃置所有手牌（没有则不弃）。若弃置数大于X，你回复一点体力；若弃置数等于X，你摸等量的牌；若弃置数小于X，你视为对一名其他角色使用了一张【杀】（X为你当前体力值）。",
	["LuaTaiji1"] = "视为对一名其他角色使用了一张【杀】",	

	["patchouli"] = "帕秋莉·诺蕾姬",
	["patchouliA"] = "帕秋莉·诺蕾姬",
	["patchouliB"] = "帕秋莉·诺蕾姬",
	["patchouliC"] = "帕秋莉·诺蕾姬",
	["patchouliD"] = "帕秋莉·诺蕾姬",
	["#patchouli"]= "不动的大图书馆",
	["Lualianji"] = "连计",
	[":Lualianji"] = "当你使用非转化锦囊指定唯一角色为目标时，你可以将与其同花色的一张牌当作与其不同名的锦囊对该角色使用（计算限制）。",
	["@Lualianji"] = "你可以发动“连计”",
	["Luaxianzhe"] = "贤者",	
	["xianzhe"] = "贤者",	
	[":Luaxianzhe"] = "出牌阶段限一次，你可以翻面，摸一张牌并获得弃牌堆中的一张锦囊牌与非【桃】基本牌，并将其中一张交给一名角色。",
	
	["iku"] = "永江衣玖",	
	["ikuA"] = "永江衣玖",
	["ikuB"] = "永江衣玖",
	["#iku"] = "美丽的绯之衣",
	["designer:iku"] = "Paysage",	
	["Luayuyi"] = "羽衣",
	[":Luayuyi"] = "你于回合外每使用或打出一张牌后，你可以摸两张牌。",
	["Lualeidian"] = "雷电",	
	["@Lualeidian2"] = "你可以弃置一张手牌，弃置完之后若你的手牌数等于手牌上限，你可以对一名角色造成一点雷电伤害。",
	["@Lualeidian"] = "请选择要电击的那个角色",
	[":Lualeidian"] = "每当你获得一张以上的牌后，你可以弃置一张手牌，若此时你的手牌数等于手牌上限，你可以对一名角色造成一点雷电伤害。",
	
	["yukari"] = "八云紫",	--
	["yukariA"] = "八云紫",	
	["yukariB"] = "八云紫",	
	["#yukari"] = "神隐的主犯",		
	["designer:yukari"] = "Paysage",
	["luajingjie"] = "境界",
	["luajingjie3"] = "境界",
	[":luajingjie"] = "出牌阶段限一次，你可以重铸一张红桃牌或黑桃牌并指定一名角色，此轮，该角色记述中包含“出牌阶段限一次”的技能可以额外使用一次。",
	["luayemu"] = "夜幕",
	[":luayemu"] = "主公技，限定技，你可以令任意名角色弃置一张牌，以此法失去最后手牌的角色流失一点体力。",
	["luapaoying"] = "泡影",
	["luapaoying2"] = "出牌阶段限一次",
	["luapaoying3"] = "出牌阶段",
	[":luapaoying"] = "准备阶段，你可以与一名其他角色拼点并各摸一张牌。若你赢，则你声明一个花色、点数、基本牌名，然后确认并展示其一张手牌，将此牌改为你声明的牌。",

	
	["tenshi"] = "比那名居天子",	
	["#tenshi"] = "有顶天之女",		
	["designer:tenshi"] = "Paysage",	--
	["illustrator:tenshi"] = "君と子音",	
	["LuaFeixiang"] = "绯想",	
	[":LuaFeixiang"] = "回合开始与结束阶段时，你可以将一名角色的一张牌置于你的武将牌上，称为“气质”。每当你受到伤害后，当前回合角色获得一张“气质”，你摸一张牌，然后将余下的“气质”置于弃牌堆。",
	["LuaJiaosi"] = "骄肆",		
	["@LuaFeixiang"] = "你可以发动“绯想”",
	["~LuaFeixiang"] = "选择要拆的角色→点击确定",	
	["qizhi2"] = "气质",
	[":LuaJiaosi"] = "每回合各限一次，你可以将与一张“气质”同花色的牌当作【决斗】或【杀】使用和打出。",
	
	["prismriver"] = "虹川三姐妹",	
	["prismriverA"] = "虹川三姐妹",
	["prismriverB"] = "虹川三姐妹",
	["#prismriver"] = "亡灵乐团",
	["designer:prismriver"] = "Paysage",	
	["illustrator:prismriver"] = "ふーぷ",
	["illustrator:prismriver_1"] = "ke-ta",
	["illustrator:prismriver_2"] = "Kirero",
	["Draw"] = "摸牌",
	["Discard"] = "弃牌",
	["luaxuqu"] = "序曲",
	[":luaxuqu"] = "一名体力值为一的角色的摸牌阶段结束时，你可以执行一个额外的出牌阶段。且若于此阶段期间未有任何角色阵亡，你流失一点体力。",
	["luaduzou"] = "独奏",
	[":luaduzou"] = "你使用红色牌指定唯一角色为目标后，你可以令其摸一张牌或弃一张牌。",
	["luahezou"] = "合奏",
	[":luahezou"] = "你可以展示三张【弹幕】以外的手牌，然后将这些牌改为【弹幕】。 ",
	
	["cirno"] = "琪露诺",	
	["cirnoA"] = "琪露诺",
	["cirnoB"] = "琪露诺",
	["cirnoC"] = "琪露诺",
	["cirnoE"] = "琪露诺",
	["#cirno"] = "湖上的冰精",
	["designer:cirno"] = "Paysage",
	["luajuezhan"] = "决战",	
	[":luajuezhan"] = "出牌阶段限两次，若你本回合未造成过伤害，你可以摸一张牌，视为一名其他角色对你使用了一张【决斗】。",
	["luabingpu"] = "冰瀑",		
	["@luabingpua"] = "请弃置一张牌（必须），且若之后⑨弃置了和你颜色不同的牌，则其将于此回合结束时回复1点体力。",		
	["@luabingpub"] = "请弃置一张手牌（必须），且若你弃置了和之前伤害来源颜色不同的牌，则你将于此回合结束时回复1点体力。",		
	["luabingpu"] = "冰瀑",		
	[":luabingpu"] = "每当你受到1点伤害后，你可以令伤害来源弃置一张牌，然后你弃置一张手牌。若以此法弃置的两张牌颜色不同，你于此回合结束时回复1点体力。（不可叠加）",		

	["rumia"] = "露米娅",	
	["rumia_boss"] = "露米娅",
	["rumiaA"] = "露米娅",
	["rumiaB"] = "露米娅",
	["#rumia"] = "宵暗的妖怪",
	["designer:rumia"] = "Paysage",	
	["luayexiao"] = "夜枭",	
	["luayexiao5"] = "夜枭",
	[":luayexiao"] = "转化技，①：准备阶段，你可以弃一张牌，或对一名未受伤的角色造成一点伤害；②：出牌阶段，你可以对没有手牌的一名角色造成一点伤害。若以此法令一名角色濒死，你回复一点体力。",
	[":luayexiao5"] = "转化技，①：准备阶段，你可以弃一张牌，或对一名未受伤的角色造成一点伤害；②：出牌阶段，你可以对没有手牌的一名角色造成一点伤害。若以此法令一名角色濒死，你回复一点体力。",
	["luayexiaoX"] = "请选择要吃的角色。请注意，若你不想造成伤害，请点击取消，之后会再要求你弃牌。",
	["luayueshi"] = "月蚀",		
	["yueshi"] = "月蚀",		
	["luayueshiX"] = "月蚀",
	["luayueshi1"] = "请选择要回复体力的那名角色。请注意，若你想选择该技能的选项2（弃置一名角色一张牌），请点击取消。",
	["luayueshi2"] = "请选择一名角色，弃置该角色的一张牌。",
	[":luayueshi"] = "出牌阶段限一次，你可以和一名角色拼点。若你赢，你可以选择一项：1.令一名角色回复一点体力，其受到的下次伤害+1；2.弃置一名角色一张牌，其下次获得牌时摸一张牌。",
	[":luayueshiX"] = "出牌阶段限一次，你可以和一名角色拼点。若你赢，你可以选择一项：1.令一名角色回复一点体力，其受到的下次伤害+1；2.弃置一名角色一张牌，其下次获得牌时摸一张牌。",
	["luaxiaoan"] = "宵暗",
	["@luaxiaoan"] = "请弃置一张黑色手牌，否则减少一点体力上限",
	[":luaxiaoan"] = "你对一名角色造成伤害后，你可令其弃置一张黑色手牌，否则其减少一点体力上限。",

	["sp_youmu"] = "魂魄妖梦",
	["sp_youmuA"] = "魂魄妖梦",
	["sp_youmuB"] = "魂魄妖梦",
	["sp_youmuC"] = "魂魄妖梦",
	["#sp_youmu"] = "苍天的庭师",
	["designer:sp_youmu"] = "Paysage",	
	["luajianji"] = "连斩", 
	["luajianji2"] = "收回武器", 
	[":luajianji"] = "当你使用【杀】结算后，若此【杀】颜色为：黑，你可以将一张红色牌当【杀】使用；红，你可以将一张黑色【杀】当【酒】/【决斗】/【过河拆桥】/【借刀杀人】/【明镜止水】使用。然后可以收回你武器区的牌。",
	["@luajianji"] = "你可以发动“连斩”",
	["~luajianji"] = "选择一张手牌当成特定的牌来使用。",
	["luayishan"] = "一闪", 
	[":luayishan"] = "限定技，你可以使用一张武器牌，与一名其他角色交换座次，然后依座次对攻击范围内的所有角色：将牌堆顶的牌当【杀】对其使用。",
	
	

	["aya"] = "射命丸文",
	["ayaA"] = "射命丸文",
	["ayaB"] = "射命丸文",
	["ayaC"] = "射命丸文",
	["ayaD"] = "射命丸文",
	["ayaE"] = "射命丸文",
	["#aya"] = "傳統的幻想記者",
	["designer:aya"] = "Paysage",	
	["luajifeng"] = "疾风",
	["@luajifeng"] = "你可以发动“疾风”",
	["~luajifeng"] = "选择要被你弃置牌的角色→点击确定",
	[":luajifeng"] = "你的牌被弃置或获得时，若你没有“风”，你可以将所有牌置于武将牌上，称为“风”。准备阶段开始，或是没有手牌的出牌阶段，你可以获得所有“风”并弃置场上一张牌。",
	["luaqucai"] = "取材",
	["luaqucaiH"] = "请为为“取材”发动而弃置的那张牌指定一个正确的使用目标。",
	["@luaqucai"] = "请弃置一张牌以来发动“取材”",
	["fong"] = "风",
	[":luaqucai"] = "准备阶段结束时，你可以弃置一张手牌并视为对一名角色使用其同名牌，然后将弃牌堆顶的牌置于牌堆顶。",
	
	["luajizou"] = "疾走",
	["luajizoux"] = "疾走（执行额外回合）",
	["luajizouH"] = "请为「上一次你置于弃牌堆的那张非装备牌」指定一个正确的使用目标。",
	[":luajizou"] = "准备阶段或出牌阶段，你可以视为使用了上一次你置于弃牌堆的一张非装备牌，然后你必须弃置一张牌。" ..
		"锁定技，若同一回合你置于弃牌堆的牌包含四种花色，则此回合结束后，你执行一个额外的回合。",
	["luabaqu-invoke"] = "你可以以“八衢”对其中一名角色使用你刚刚摸上来的牌<br/> <b>操作提示</b>: 选择一名角色→点击确定<br/>", 


	["shikieiki"] = "四季映姬",
	["#shikieiki"] = "乐园的裁判长",		
	["designer:shikieiki"] = "Paysage",	
	["luashuojiao"] = "言训",
	[":luashuojiao"] = "你可以对一名角色使用一张黑色牌，并须将牌堆顶的牌当作【闪电】对其使用。锁定技，你的【桃】的花色改为黑桃，所有角色受到的雷电伤害固定为1。",
	["luashenpani"] = "審判", 
	[":luashenpani"] = "锁定技，红桃判定牌生效后，你获得之。限定技，判定牌生效前，你可以用一张牌替换之。", 
	["@luashenpani"] = "你可以用一张牌来改判",
	["lualunhuii"] = "轮回",
	[":lualunhuii"] = "限定技，【闪电】移动后，你可以令扫过的所有角色（除你外）受到1点雷电伤害。", 

	["tewi"] = "因幡帝",
	["#tewi"] = "因幡的白兔",		
	["designer:tewi"] = "Paysage",	
	["luaxingyun"] = "幸运",
	["xingyun"] = "幸运",
	["luaxingyun2"] = "请选择【杀】要指定的角色。若此【杀】未造成伤害，则你获得因此弃置的两张牌，否则其获得这两张牌。",
	["@luaxingyun"] = "请弃置一张牌，然后你将视为对天为指定的一名色使用了一张【杀】。若此【杀】未造成伤害，则其获得因此弃置的两张牌，否则你获得这两张牌。",
	["@luaxingyun2"] = "请弃置一张牌，然后其将视为对你指定的一名色使用了一张【杀】。若此【杀】未造成伤害，则你获得因此弃置的两张牌，否则其获得这两张牌。",
	[":luaxingyun"] = "出牌阶段限一次，你可以选择一名角色，你与其各弃置一张牌，然后视为其对其以外你选择的一名角色使用了一张【杀】。若此【杀】未造成伤害，则你获得因此弃置的两张牌，否则其获得这两张牌。",

	["whiterock"] = "蕾蒂·霍瓦特洛克",
	["#whiterock"] = "冬的遗忘之物",
	["designer:whiterock"] = "Paysage",
	["lualindong"] = "凛冬",
	["notAct"] = "不发动",
	["DrawA"] = "莫多莫多",
	["DrawB"] = "少摸一张",
	["lualindongA"] = "请选择要弃置谁的牌",
	["lualindongB"] = "请选择要流失体力的角色",
	[":lualindong"] = "摸牌阶段开始时，你可以跳过下个出牌阶段，然后选择一项：①手牌上限+1且此阶段摸三张牌；②少摸一张牌，使用一张牌并弃置一名角色区域里的一张牌。若因“凛冬”涉及的三张牌种类各不相同，你可以展示之，并令一名角色流失一点体力。",

	["#YUKARI"] = "八云紫 展示了%to 的这张牌，并将其改写为了 %arg2 %arg ",
}
return {extension_pay_e}