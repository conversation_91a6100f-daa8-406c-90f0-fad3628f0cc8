---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON><PERSON>)
--- Created by Administrator.
--- DateTime: 2020/7/28 20:43
---

extension_pay_yuki = sgs.Package("pay100")
xiandinb = sgs.General(extension_pay_yuki,"xiandinb","god",2,false,true,false)
yorigami = sgs.General(extension_pay_yuki,"yorigami","god",2,false,true,true)

xiandinc = sgs.General(extension_pay_yuki,"xiandinc","luayao",4,false,true,false)
yukariC = sgs.General(extension_pay_yuki,"yukariC","luayao",4,false,true,true)

xiandind = sgs.General(extension_pay_yuki,"xiandind","luaxing",4,false,true,false)
cirnoD = sgs.General(extension_pay_yuki,"cirnoD","luaxing",4,false,true,true)
rh_flandreH = sgs.General(extension_pay_yuki,"rh_flandre<PERSON>","luahong",3,false,true,true)
sp_tewi = sgs.General(extension_pay_yuki,"sp_tewi","luayue",3,false,true,false)
shion = sgs.General(extension_pay_yuki,"shion","god",5,false,true,false)
sp_marisa = sgs.General(extension_pay_yuki,"sp_marisa","luacai",3,false,true,true)
marisaA = sgs.General(extension_pay_yuki,"marisaA","luacai",3,false,true,true)
marisaB = sgs.General(extension_pay_yuki,"marisaB","luacai",3,false,true,true)
marisaC = sgs.General(extension_pay_yuki,"marisaC","luacai",3,false,true,true)
marisaD = sgs.General(extension_pay_yuki,"marisaD","luacai",3,false,true,true)
marisaE = sgs.General(extension_pay_yuki,"marisaE","luacai",3,false,true,true)
RITA = true
if sgs.GetConfig("UserName", ""):match("Rita") then RITA = false end
if sgs.GetConfig("UserName", ""):match("丽塔") then RITA = false end
vollerei = sgs.General(extension_pay_yuki,"vollerei","qun", 4,false,true, RITA)

xiandine = sgs.General(extension_pay_yuki,"xiandine","luayue",4,false,true,false)
xiandinf = sgs.General(extension_pay_yuki,"xiandinf","luacai",4,false,true,false) 
xiandinh = sgs.General(extension_pay_yuki,"xiandinh","qun",3,false,true,false)
xiandini = sgs.General(extension_pay_yuki,"xiandini","luahong",3,false,true,false)
xiandinj = sgs.General(extension_pay_yuki,"xiandinj","luayue",3,false,true,false)

whiteB = sgs.General(extension_pay_yuki,"whiteB","luaxing",3,false,true,false)
yukariY = sgs.General(extension_pay_yuki,"yukariY","luacai",3,false,false,false)
x_reimu = sgs.General(extension_pay_yuki,"x_reimu","god",3,false,true,false)

xiandinja = sgs.CreateTriggerSkill{
    name = "xiandinja",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
xiandinjb = sgs.CreateTriggerSkill{
    name = "xiandinjb",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
xiandinj:addSkill(xiandinja)
xiandinj:addSkill(xiandinjb)

xiandinia = sgs.CreateTriggerSkill{
    name = "xiandinia",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
xiandinib = sgs.CreateTriggerSkill{
    name = "xiandinib",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
xiandini:addSkill(xiandinia)
xiandini:addSkill(xiandinib)

xiandinha = sgs.CreateTriggerSkill{
    name = "xiandinha",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
xiandinhb = sgs.CreateTriggerSkill{
    name = "xiandinhb",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
xiandinh:addSkill(xiandinha)
xiandinh:addSkill(xiandinhb)

xiandinea = sgs.CreateTriggerSkill{
    name = "xiandinea",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
xiandineb = sgs.CreateTriggerSkill{
    name = "xiandineb",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
xiandine:addSkill(xiandinea)
xiandine:addSkill(xiandineb)

xiandinfa = sgs.CreateTriggerSkill{
    name = "xiandinfa",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
xiandinfb = sgs.CreateTriggerSkill{
    name = "xiandinfb",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
xiandinf:addSkill(xiandinfa)
xiandinf:addSkill(xiandinfb)
 

luamingan = sgs.CreateTriggerSkill{
    name = "luamingan",
    events = {sgs.PreCardUsed} ,
    on_trigger = function(self, event, Seele, data)
        if event == sgs.PreCardUsed then
            local function YouMuCheck(card, target)
                if card:isKindOf("Hui") or card:isKindOf("Ofuda") then
                    return true
                elseif card:isKindOf("FaithCollection") then
                    return not target:isNude()
                elseif card:isKindOf("Banquet") then
                    return not target:containsTrick("banquet")
                end
            end
            local use = data:toCardUse()
            local card = use.card
            local room = Seele:getRoom()
            if use.from:objectName() == Seele:objectName() and (use.card:isNDTrick() or use.card:isKindOf("BasicCard")) and use.from:hasSkill("luamingan")
                    and use.card:getSuit() == sgs.Card_Heart and use.from:getMark("@danzhongu") > 0 then
                if (sgs.Sanguosha:getCurrentCardUseReason() ~= sgs.CardUseStruct_CARD_USE_REASON_PLAY) then return false end
                local available_targets = sgs.SPlayerList()
                if (not use.card:isKindOf("AOE")) and (not use.card:isKindOf("GlobalEffect")) then
                    for _, p in sgs.qlist(room:getAlivePlayers()) do
                        if (use.to:contains(p) or room:isProhibited(Seele, p, use.card)) then continue end
                        if (use.card:targetFixed()) then
                            if (not use.card:isKindOf("Peach")) or (p:isWounded()) then
                                available_targets:append(p)
                            end
                        else
                            if (use.card:targetFilter(sgs.PlayerList(), p, Seele) or YouMuCheck(use.card, p)) then
                                available_targets:append(p)
                            end
                        end
                    end
                end --<img src='image/system/flan/1.jpg' width="75"  />   <p> 333333 </p> --    "<p style=\"margin:3px 2px;\">%1</p>"
                local extra
                if not use.card:isKindOf("Collateral") then
                    local Carddata2 = sgs.QVariant() -- ai用
                    Carddata2:setValue(use.card)
                    room:setTag("luajianjiTC", Carddata2)

                    extra = room:askForPlayerChosen(Seele, available_targets, "luajianjic", "luajianjic", true, true)
                    room:removeTag("luajianjiTC")
                    if extra then
                        use.to:append(extra)
                    end
                end
                room:sortByActionOrder(use.to)
                data:setValue(use)
                return false
            else
                if use.from:objectName() == Seele:objectName() and (use.card:isNDTrick() or use.card:isKindOf("BasicCard")) and use.from:hasSkill("luamingan")
                        and use.card:getSuit() == sgs.Card_Spade and use.from:getMark("@danzhongd") > 0 and use.to and use.to:length() > 1 then
                    local extra
                    if not use.card:isKindOf("Collateral") then
                        local R = sgs.SPlayerList()

                        local Carddata2 = sgs.QVariant() -- ai用
                        Carddata2:setValue(use.card)
                        room:setTag("luajianjiTC", Carddata2)

                        extra = room:askForPlayerChosen(Seele, use.to, "luajianjic", "luajianjic", false, false)
                        room:removeTag("luajianjiTC")
                        if extra then
                            R:append(extra)
                        end
                        use.to = R
                    end
                    room:sortByActionOrder(use.to)
                    data:setValue(use)
                    return false
                end
            end
        end
        return false
    end
}
luadanzhong = sgs.CreateTriggerSkill{
    name = "luadanzhong" ,
    events = {sgs.TargetConfirmed},
    frequency = sgs.Skill_Frequent,
    on_trigger = function(self, event, Seele, data, room)
        local use = data:toCardUse()
        if use.from:hasFlag("luadanzhong") then return false end
        if use.from:getMark("@danzhongu") > 0 and use.to and use.to:length() > 1 and use.card and not use.card:isKindOf("Peach")
                and use.from:objectName() == Seele:objectName() then
            local extra = room:askForPlayerChosen(Seele, use.to, "luadanzhong", "luadanzhong", true, true)
            if extra then
                Seele:getRoom():damage(sgs.DamageStruct("luadanzhong", Seele, extra))
                use.from:loseMark("@danzhongu")
                use.from:gainMark("@danzhongd")
                room:setPlayerFlag(use.from, "luadanzhong")
            end
        elseif use.from:getMark("@danzhongd") > 0 and use.to and use.to:length() == 1 and use.card
                and use.from:objectName() == Seele:objectName() then
            local R = sgs.SPlayerList()
            for _,p in sgs.qlist(room:getAlivePlayers()) do
                if not use.to:contains(p) and p:getHp() > 1 then
                    R:append(p)
                end
            end
            if not R:isEmpty() then
                local extra = room:askForPlayerChosen(Seele, R, "luadanzhong", "luadanzhong", true, true)
                if extra then
                    Seele:getRoom():damage(sgs.DamageStruct("luadanzhong", Seele, extra))
                    use.from:loseMark("@danzhongd")
                    use.from:gainMark("@danzhongu")
                    room:setPlayerFlag(use.from, "luadanzhong")
                end
            end
        end
        return false
    end
}
luadanzhong2 = sgs.CreateTriggerSkill{
    name = "#luadanzhong",
    global = true,
    events = {sgs.EventPhaseStart},
    on_trigger = function(self, event, player, data, room)
        if event == sgs.EventPhaseStart then
            for _, p2 in sgs.qlist(room:findPlayersBySkillName("luadanzhong")) do
                if p2:getMark("@danzhongu") == 0 and p2:getMark("@danzhongd") == 0 then
                    p2:gainMark("@danzhongu")
                    break
                end
            end
        end
    end
}
luadanzhong3 = sgs.CreateTriggerSkill{
    name = "#luadanzhong2" ,
    global = true,
    events = {sgs.EventPhaseStart} ,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if player:getPhase() == sgs.Player_Start and player:objectName() == room:getCurrent():objectName() then
            local toziko = player:getRoom():findPlayerBySkillName("luadanzhong")
            if toziko then
                if toziko:hasFlag("luadanzhong") then room:setPlayerFlag(toziko, "-luadanzhong") end
            end
        end
        return false
    end ,
    can_trigger = function(self, target)
        return target
    end ,
    priority = 1
}
vollerei:addSkill(luamingan)
vollerei:addSkill(luadanzhong)
vollerei:addSkill(luadanzhong2)
vollerei:addSkill(luadanzhong3)
--[[
luabuxin = sgs.CreateTriggerSkill{
    name = "luabuxin",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
luazaiesp = sgs.CreateTriggerSkill{
    name = "luazaiesp",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
shion:addSkill(luabuxin)
shion:addSkill(luazaiesp)
]]--
xiandinba = sgs.CreateTriggerSkill{
    name = "xiandinba",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
xiandinbb = sgs.CreateTriggerSkill{
    name = "xiandinbb",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
xiandinb:addSkill(xiandinba)
xiandinb:addSkill(xiandinbb)

xiandinda = sgs.CreateTriggerSkill{
    name = "xiandinda",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
xiandind:addSkill(xiandinda)

xiandinca = sgs.CreateTriggerSkill{
    name = "xiandinca",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
xiandincb = sgs.CreateTriggerSkill{
    name = "xiandincb",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
xiandinc:addSkill(xiandinca)
xiandinc:addSkill(xiandincb)

luapaomo = sgs.CreateTriggerSkill{
    name = "luapaomo",
    events = {sgs.DrawNCards},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if room:askForSkillInvoke(player,self:objectName(),data) then
            local count = room:getAlivePlayers():length()
            player:gainMark("@zhai", count)
            data:setValue(count)
        end
    end
}
luacaihuosp = sgs.CreateTriggerSkill{
    name = "luacaihuosp",
    events = {sgs.TargetConfirmed},
    global = true,
    on_trigger = function(self, event, player, data)
        local use = data:toCardUse()
        local room = player:getRoom()
        if not use.from then return false end
        if use.from:objectName() ~= player:objectName() then return false end
        if use.card:isKindOf("SkillCard") then return false end
        if use.to then
            local toOthers = true
            for _, p in sgs.qlist(use.to) do
                if p:hasSkill("luacaihuosp") then
                    toOthers = false
                    if use.from:objectName() ~= p:objectName() and room:askForSkillInvoke(p, self:objectName(), data) then
                        p:gainMark("@zhai")
                        use.to = sgs.SPlayerList()
                        data:setValue(use)
                        return false
                    end
                end
            end
            if not use.from:isAlive() then return end
            if toOthers or use.to:length() > 1 then
                if use.from:hasSkill("luacaihuosp") then
                    local _data = sgs.QVariant()
                    _data:setValue(use.from)
                    for _, p in sgs.qlist(use.to) do
                        if use.from:objectName() ~= p:objectName() and use.from:getMark("@zhai") > 0
                                and room:askForSkillInvoke(p, "luacaihuosp2", _data) then
                            use.from:loseMark("@zhai")
                            p:drawCards(1)
                        end
                    end
                end
                return false
            end
        end
    end
}
luacaihuosp2 = sgs.CreateTriggerSkill{
    name = "#luacaihuosp",
    frequency = sgs.Skill_Compulsory,
    events = {sgs.EventPhaseStart},
    on_trigger = function(self,event,player,data)
        local phase = player:getPhase()
        local room = player:getRoom()
        if phase == sgs.Player_Finish then
            if player:hasSkill("luacaihuosp") and player:getMark("@zhai") >= room:getAlivePlayers():length() then
                room:sendCompulsoryTriggerLog(player, "luacaihuosp")
                player:loseAllMarks("@zhai")
                room:loseMaxHp(player)
            end
        end
        return false
    end
}
yorigami:addSkill(luapaomo)
yorigami:addSkill(luacaihuosp)
yorigami:addSkill(luacaihuosp2)


luamouchengCard = sgs.CreateSkillCard{
    name = "luamoucheng",
    will_throw = false,
    filter = function(self, targets, to_select)
        return #targets == 0
    end,
    on_effect = function(self, effect)
        local room = effect.to:getRoom()
        effect.to:obtainCard(self)
        local target = room:askForPlayerChosen(effect.from, room:getOtherPlayers(effect.to), self:objectName(), "luamouchengX")
        if not target then return false end
        if not target:isAlive() then return false end
        local slash = sgs.Sanguosha:cloneCard("slash")
        slash:setSkillName("luamoucheng")
        if room:useCard(sgs.CardUseStruct(slash, effect.to, target)) then
            if not target then return false end
            if not target:isAlive() then return false end
            if not effect.from then return false end
            if not effect.from:isAlive() then return false end 
            if room:askForSkillInvoke(target, "luamoucheng2") then
				local slash2 = sgs.Sanguosha:cloneCard("slash")
                room:useCard(sgs.CardUseStruct(slash2, target, effect.from))
            end
        end
    end
}
luamoucheng = sgs.CreateOneCardViewAsSkill{
    name = "luamoucheng",
    filter_pattern = ".|.|.|.",
    view_as = function(self,card)
        local skillcard = luamouchengCard:clone()
        skillcard:addSubcard(card)
        return skillcard
    end,
    enabled_at_play = function(self,player)
        return not player:hasUsed("#luamoucheng")
    end,
}
luawangliang = sgs.CreateTriggerSkill{
    name = "luawangliang" ,
    frequency = sgs.Skill_Compulsory,
    global = true, --究极重要！全场技能必备，花了5个小时买一场教训！
    events = {sgs.EventPhaseChanging, sgs.Damaged} ,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if event == sgs.EventPhaseChanging then
            local change = data:toPhaseChange()
            if change.to == sgs.Player_NotActive then
                for _, yukari in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
                    local players = sgs.SPlayerList()
                    for _, p in sgs.qlist(room:getAlivePlayers()) do
                        if p:getMark("damageYukari") > 0 and not p:isNude() then
                            room:setPlayerMark(p, "damageYukari", 0)
                            players:append(p)
                        end
                    end
                    if players:length() == 0 then return false end
                    local extra = room:askForPlayerChosen(yukari, players, "luawangliang", "luawangliang", true, true)
                    if extra and extra:isAlive() and not extra:isNude() then
                        local id = room:askForCardChosen(yukari, extra, "he", self:objectName(), false)
                        local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
                        dummy_0:addSubcard(id)
                        yukari:obtainCard(dummy_0)
                    end
                end
            end
        else
            local damage = data:toDamage()
            if damage.damage == 0 then return false end
            if damage.from and damage.from:isAlive() then
                if damage.to and damage.to:isAlive() then
                    if damage.to:hasSkill(self:objectName()) then
                        room:setPlayerMark(damage.from, "damageYukari", 1)
                    end
                end
            end
        end
    end
}
yukariC:addSkill(luamoucheng)
yukariC:addSkill(luawangliang)

luasuanshuCard = sgs.CreateSkillCard{
    name = "luasuanshu",
    filter = function(self, targets, to_select)
        return (#targets <= 0) and to_select:getHp() > 0
    end,
    on_effect = function(self, effect)
        local room = effect.from:getRoom()
        local dummy = sgs.Sanguosha:cloneCard("yuzhi", sgs.Card_NoSuit, 0)
        for _, p in sgs.qlist(room:getAlivePlayers()) do
            if not p:hasSkill("luasuanshu3") then room:acquireSkill(p, "luasuanshu3", false) end
            room:setPlayerMark(p, "@skill_invalidity", p:getMark("@skill_invalidity") + 1)
        end
        room:setPlayerFlag(effect.from, "luasuanshu")
        room:useCard(sgs.CardUseStruct(dummy, effect.from, effect.to))
        if effect.from:hasFlag("luasuanshu") then
            room:setPlayerFlag(effect.from, "-luasuanshu")
            for _, p in sgs.qlist(room:getAlivePlayers()) do
                if p:hasSkill("luasuanshu3") then room:detachSkillFromPlayer(p, "luasuanshu3") end
                if p:getMark("@skill_invalidity") > 0 then
                    room:setPlayerMark(p, "@skill_invalidity", p:getMark("@skill_invalidity") - 1)
                end
            end
        end
    end
}
luasuanshu = sgs.CreateZeroCardViewAsSkill{
    name = "luasuanshu",
    view_as = function(self, cards)
        return luasuanshuCard:clone()
    end,
    enabled_at_play = function(self, player)
        return not player:hasUsed("#luasuanshu")
    end,
}

luasuanshu2 = sgs.CreateTriggerSkill {
    name = "#luasuanshu",
    events = { sgs.CardUsed, sgs.CardResponded },
    global = true,
    frequency = sgs.Skill_Compulsory,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if event == sgs.CardUsed then
            local use = data:toCardUse()
            if not use.from then return false end
            if use.card:isKindOf("SkillCard") then return false end
            local cirnoD
            for _, p in sgs.qlist(room:getAlivePlayers()) do
                if p:hasFlag("luasuanshu") then cirnoD = p;break end
            end
            if not cirnoD then return false end
            if not cirnoD:isAlive() then return false end
            if not cirnoD:hasFlag("luasuanshu") then return false end
            if use.card and use.card:isKindOf("Nullification") then
                use.from:drawCards(1)
            end
            local xy = use.from:getMark("jiujiujiu")
            room:setPlayerMark(use.from, "jiujiujiu", use.card:getNumber())
            if xy + use.card:getNumber() == 9 and use.card:getNumber() ~= 9 then
                use.from:drawCards(2)
                room:setPlayerFlag(cirnoD, "-luasuanshu")
                for _, p in sgs.qlist(room:getAlivePlayers()) do
                    if p:hasSkill("luasuanshu3") then room:detachSkillFromPlayer(p, "luasuanshu3") end
                    if p:getMark("@skill_invalidity") > 0 then
                        room:setPlayerMark(p, "@skill_invalidity", p:getMark("@skill_invalidity") - 1)
                    end
                end
            end
        end
    end
}
cirnoD:addSkill(luasuanshu)
cirnoD:addSkill(luasuanshu2)

luafenxingsp = sgs.CreateTriggerSkill{
    name = "luafenxingsp" ,
    events = {sgs.EventPhaseStart, sgs.DrawNCards},
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if event == sgs.EventPhaseStart then
            if player:getPhase() == sgs.Player_Start then
                local card1 = room:askForCard(player, ".", "@luafenxingspA", sgs.QVariant(), sgs.Card_MethodNone)
                if card1 then
                    local ids = sgs.IntList()
                    ids:append(card1:getEffectiveId())
                    room:fillAG(ids)
                    room:getThread():delay(1500)
                    room:clearAG()
                    room:setCardFlag(card1, "visible")
                    local discard_ids = room:getDrawPile()
                    local i = 0
                    for _, id in sgs.qlist(discard_ids) do
                        local card = sgs.Sanguosha:getCard(id)
                        if card:getNumber() == card1:getNumber() then
                            local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
                            dummy:addSubcard(card)
                            player:obtainCard(card)
                            i = 1 + i
                        end
                        if i > 2 then break end
                    end
                    room:setPlayerFlag(player, "luafenxingsp")
                end
            end
        else
            if player:hasFlag("luafenxingsp") then
                local count = data:toInt() - 1
                count = math.max(0, count)
                data:setValue(count)
                room:setPlayerFlag(player, "-luafenxingsp")
            end
        end
        return false
    end
}
luafenxingsp2 = sgs.CreateTriggerSkill{
    name = "#luafenxingsp" ,
    frequency = sgs.Skill_Compulsory,
    global = true, --究极重要！全场技能必备，花了5个小时买一场教训！
    events = {sgs.EventPhaseChanging} ,
    on_trigger = function(self, event, player, data)
        if event == sgs.EventPhaseChanging then
            local room = player:getRoom()
            local change = data:toPhaseChange()
            if change.to == sgs.Player_NotActive then
                for _, p in sgs.qlist(room:findPlayersBySkillName("luafenxingsp")) do
                    room:setPlayerFlag(p, "-luafenxingsp")
                end
            end
        end
    end
}
luajinguo2 = sgs.CreateTriggerSkill{
    name = "#luajinguo",
    global = true,
    events = {sgs.CardsMoveOneTime},
    on_trigger = function(self, event, player, data, room)
        local move = data:toMoveOneTime()
        if move.to and move.to:isAlive() and not move.card_ids:isEmpty() then
            if move.to:getPhase() ~= sgs.Player_Draw then
                for _,card_idX in sgs.qlist(move.card_ids) do
                    local card = sgs.Sanguosha:getCard(card_idX)
                    room:setCardFlag(card, "luajinguo")
                end
            end
        end
    end
}
luajinguo = sgs.CreateProhibitSkill{
    name = "luajinguo",
    is_prohibited = function(self, from, to, card)
        return card:hasFlag("luajinguo") and to:hasSkill("luajinguo") and not card:isKindOf("SkillCard")
    end
}
rh_flandreH:addSkill(luafenxingsp)
rh_flandreH:addSkill(luafenxingsp2)
rh_flandreH:addSkill(luajinguo2)
rh_flandreH:addSkill(luajinguo)

luabuxinCard = sgs.CreateSkillCard{
    name = "luabuxin",
    target_fixed = true,
    on_use = function(self, room, source, targets)
        room:acquireSkill(source, "luabuxin4", false)
        local card_id = self:getSubcards():first()
        local card = sgs.Sanguosha:getEngineCard(card_id)
        room:setPlayerMark(source, "luabuxin" .. card:objectName(), 1)
        source:drawCards(1)
        room:filterCards(source, source:getCards("h"), true)
    end
}
luabuxin = sgs.CreateOneCardViewAsSkill{
    name = "luabuxin",
    view_filter = function(self, card)
        return not sgs.Self:isJilei(card)
    end,
    view_as = function(self,card)
        local skillcard = luabuxinCard:clone()
        skillcard:addSubcard(card)
        return skillcard
    end,
    enabled_at_play = function(self,player)
        return true
    end
}
luabuxin2 = sgs.CreateTriggerSkill{
    name = "#luabuxin" ,
    frequency = sgs.Skill_Compulsory,
    global = true, --究极重要！全场技能必备，花了5个小时买一场教训！
    events = {sgs.EventPhaseChanging, sgs.CardsMoveOneTime} ,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if event == sgs.EventPhaseChanging then
            local change = data:toPhaseChange()
            for _, p in sgs.qlist(room:findPlayersBySkillName("luabuxin")) do
                room:setPlayerCardLimitation(p, "discard", "Hui", false)
            end
        end
    end
}
luazaiesp2 = sgs.CreateTriggerSkill{
    name = "#luazaiesp",
    frequency = sgs.Skill_Compulsory,
    global = true,
    events = {sgs.PreCardUsed} ,
    on_trigger = function(self, event, youmu, data)
        if event == sgs.PreCardUsed then
            local use = data:toCardUse()
            local card = use.card
            local room = youmu:getRoom()
            if card:isKindOf("Hui") then
                for _, p in sgs.qlist(room:getAlivePlayers()) do
                    if not use.to:contains(p) and p:getMark("@luazaiesp") > 0 then use.to:append(p) end
                end
            end
            room:sortByActionOrder(use.to)
            data:setValue(use)
            return false
        end
    end
}
luazaiesp = sgs.CreateTriggerSkill{
    name = "luazaiesp",
    events = {sgs.EventPhaseEnd},
    on_trigger = function(self, event, player, data, room)
        if event == sgs.EventPhaseEnd then
            if player:getPhase() == sgs.Player_Discard then
                if player:getHandcardNum() > player:getMaxCards() then
                    local extra = room:askForPlayerChosen(player, room:getAlivePlayers(), "luazaiesp", "luazaiesp", true)
                    if extra then
                        room:addPlayerMark(extra, "@luazaiesp")
                    end
                end
            end
        end
    end
}
shion:addSkill(luabuxin)
shion:addSkill(luabuxin2)
shion:addSkill(luazaiesp)
shion:addSkill(luazaiesp2)


jiaotuCard = sgs.CreateSkillCard{
    name = "luajiaotu",
    filter = function(self, targets, to_select)
        if (#targets ~= 0) then return false end
        return true
    end ,
    on_effect = function(self, effect)
        local source = effect.to
        local room = source:getRoom()
        local choice = room:askForChoice(source, self:objectName(), "Slash+Jink+Analeptic+Peach+Ofuda+Hui")

        local discard_ids = room:getDiscardPile()
        local trickcard = sgs.IntList()
        for _, id in sgs.qlist(discard_ids) do
            local card = sgs.Sanguosha:getCard(id)
            if card:isKindOf(choice) then
                trickcard:append(id)
            end
        end
        discard_ids = room:getDrawPile()
        for _, id in sgs.qlist(discard_ids) do
            local card = sgs.Sanguosha:getCard(id)
            if card:isKindOf(choice) then
                trickcard:append(id)
            end
        end
        if trickcard:length() > 0 then
            room:fillAG(trickcard, source)
            local card_id = room:askForAG(source, trickcard, false, "luajiaotu")
            local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
            dummy_0:addSubcard(card_id)
            room:clearAG()
            source:obtainCard(dummy_0)
            local card = sgs.Sanguosha:getCard(card_id)
            room:setCardFlag(card, "luajiaotu")
        end
    end
}
luajiaotu = sgs.CreateOneCardViewAsSkill{
    name = "luajiaotu",
    filter_pattern = ".|.|.|.",
    view_as = function(self, card)
        local aaa = jiaotuCard:clone()
        aaa:addSubcard(card)
        return aaa
    end,
    enabled_at_play = function(self, player)
        return not player:hasUsed("#luajiaotu")
    end
}
luajiaotu2 = sgs.CreateTriggerSkill{
    name = "#luajiaotu",
    global = true,
    events = {sgs.TargetSpecifying},
    on_trigger = function(self, event, player, data, room)
        local use = data:toCardUse()
        if use.card:hasFlag("luajiaotu") and room:askForSkillInvoke(use.from, self:objectName(), data) then
            use.to = sgs.SPlayerList()
            local extra = room:askForPlayerChosen(use.from, room:getAllPlayers(), "luajiaotu", "luajiaotu", true, true)
            use.to:append(extra)
            room:sortByActionOrder(use.to)
            data:setValue(use)
        end
    end
}
luasanku = sgs.CreateTriggerSkill{
    name = "luasanku" ,
    frequency = sgs.Skill_NotFrequent ,
    events = {sgs.BeforeCardsMove, sgs.Dying} ,
    on_trigger = function(self, event, player, data)
        if event == sgs.BeforeCardsMove then
            local room = player:getRoom()
            local move = data:toMoveOneTime()
            if not move.from then return false end
            local old_card_ids = {}
            for _,card_idX in sgs.qlist(move.card_ids) do
                table.insert(old_card_ids, card_idX)
            end
            local aicard = sgs.IntList()
            local j = 0
            local bool = false
            for _,card_id in sgs.qlist(move.card_ids) do
                if room:getCardOwner(card_id) and room:getCardOwner(card_id):objectName() == move.from:objectName()
                        and room:getCardOwner(card_id):objectName() == player:objectName() then
                    local place = move.from_places:at(j)
                    if place == sgs.Player_PlaceHand or place == sgs.Player_PlaceEquip then
                        local canAppend = true
                        for _,card in sgs.qlist(player:getCards("j")) do
                            local trueCard = sgs.Sanguosha:getCard(card:getSubcards():first())
                            room:writeToConsole("tewi test  ff "  .. trueCard:objectName())
                            if sgs.Sanguosha:getCard(card_id):getTypeId() == trueCard:getTypeId() then
                                canAppend = false
                            end
                        end
                        if canAppend then
                            bool = true
                            aicard:append(card_id)
                        end
                    end
                end
                j = j + 1
            end

            local _movefrom
            for _, p in sgs.qlist(room:getAlivePlayers()) do
                if move.from:objectName() == p:objectName() then
                    _movefrom = p
                    break
                end
            end
            local reason = move.reason
            local basic = bit32.band(reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)
            if _movefrom and _movefrom:objectName() == player:objectName() and _movefrom:hasSkill("luasanku") and bool
                    and (basic == sgs.CardMoveReason_S_REASON_DISCARD) and aicard:length() > 0 then
                room:fillAG(aicard, player)
                local card_id = room:askForAG(player, aicard, false, "luasanku")
                if card_id then
                    local i = 0
                    for _, card_idY in ipairs(old_card_ids) do
                        if card_idY == card_id then
                            move.card_ids:removeOne(card_idY)
                            move.from_places:removeAt(i)
                        else
                            i = i + 1
                        end
                    end
                    local light = sgs.Sanguosha:cloneCard("luamin", sgs.Card_NoSuit, 0)
                    room:clearAG()
                    light:addSubcard(card_id)
                    local use_X = sgs.CardUseStruct()
                    use_X.card = light
                    use_X.from = player
                    use_X.to:append(player)
                    room:useCard(use_X, false)
                    data:setValue(move)
                    return false
                end
            end
        elseif event == sgs.Dying then
            local dying = data:toDying()
            local room = player:getRoom()
            if dying.who:getSeat() == player:getSeat() then return false end
            local count = player:getJudgingArea():length()
            if count > 0 and room:askForSkillInvoke(player, "luasanku") then
                local hcardid = sgs.IntList()
                for _, card in sgs.qlist(player:getHandcards()) do
                    hcardid:append(card:getEffectiveId())
                end
                local ecardid = sgs.IntList()
                for _, card in sgs.qlist(player:getEquips()) do
                    ecardid:append(card:getEffectiveId())
                end
                local dummyX = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
                dummyX:addSubcards(player:getJudgingArea())
                room:throwCard(dummyX, player)
                room:recover(player, sgs.RecoverStruct(player, nil, count))

                local move = sgs.CardsMoveStruct()
                move.from = player
                move.from_place = sgs.Player_PlaceHand
                move.to = nil
                move.to_place = sgs.Player_DiscardPile
                move.card_ids = hcardid
                move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, player:objectName())
                room:moveCardsAtomic(move, true)

                move = sgs.CardsMoveStruct()
                move.from = player
                move.from_place = sgs.Player_PlaceEquip
                move.to = nil
                move.to_place = sgs.Player_DiscardPile
                move.card_ids = ecardid
                move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, player:objectName())
                room:moveCardsAtomic(move, true)
            end
        end
    end
}
sp_tewi:addSkill(luajiaotu)
sp_tewi:addSkill(luajiaotu2)
sp_tewi:addSkill(luasanku)

luazhuxing = sgs.CreateTriggerSkill{
    name = "luazhuxing" ,
    frequency = sgs.Skill_Compulsory,
    events = {sgs.CardsMoveOneTime},
    on_trigger = function(self, event, player, data, room)
        local move = data:toMoveOneTime()
        if not move.from then return false end
        if move.from:isKongcheng() then return false end
        if not move.from:hasSkill(self:objectName()) then return false end
        if move.from_places:contains(sgs.Player_PlaceHand) then
            local plist = sgs.SPlayerList()
            for _,p in sgs.qlist(room:getAlivePlayers()) do
                if p:getHandcardNum() == player:getHandcardNum() then
                    plist:append(p)
                end
            end
            local target = room:askForPlayerChosen(player, plist, "luadanmu", "luadanmu", true, true)
            if target then
                local dummy = sgs.Sanguosha:cloneCard("yuzhi", sgs.Card_NoSuit, 0)
                room:useCard(sgs.CardUseStruct(dummy, player, target))

                local thc = room:askForCard(player, ".|.|.|hand!", "@luazhuxing", data, sgs.Card_MethodNone)
                local light = sgs.Sanguosha:cloneCard("light", thc:getSuit(), thc:getNumber())
                light:addSubcard(thc)
                room:writeToConsole(light:objectName())
                local use_X = sgs.CardUseStruct()
                use_X.card = light
                use_X.from = player
                use_X.to:append(player)
                room:useCard(use_X, false)
            end
        end
    end
}
luaqianyiCard = sgs.CreateSkillCard{
    name = "luaqianyi",
    will_throw = false,
    target_fixed = true,
    on_use = function(self, room, source, targets)
        if source:getMark("@luaqianyi1") == 0 then
            source:drawCards(1)
            room:handleAcquireDetachSkills(source, "LuaShanguang")
            for _, skill in sgs.qlist(source:getVisibleSkillList(true)) do
                if skill:objectName() ~= "LuaShanguang" and skill:objectName() ~= "luaqianyi" then
                    room:detachSkillFromPlayer(source, skill:objectName())
                end
            end
            room:setPlayerMark(source, "@luaqianyi1", 1)
            room:setPlayerMark(source, "@luaqianyi2", 0)
        else
            room:handleAcquireDetachSkills(source, "luazhuxing")
            room:askForDiscard(source, self:objectName(), 1, 1, false, true)
            for _, skill in sgs.qlist(source:getVisibleSkillList(true)) do
                if skill:objectName() ~= "luazhuxing" and skill:objectName() ~= "luaqianyi" then
                    room:detachSkillFromPlayer(source, skill:objectName())
                end
            end
            room:setPlayerMark(source, "@luaqianyi2", 1)
            room:setPlayerMark(source, "@luaqianyi1", 0)
        end
    end
}
luaqianyi = sgs.CreateZeroCardViewAsSkill{
    name = "luaqianyi",
    view_as = function(self, cards)
        return luaqianyiCard:clone()
    end,
    enabled_at_play = function(self, player)
        return not player:hasUsed("#luaqianyi")
    end,
}
sp_marisa:addSkill(luazhuxing)
sp_marisa:addSkill(luaqianyi)
 
marisaA:addSkill(luazhuxing)
marisaA:addSkill(luaqianyi)

marisaB:addSkill(luazhuxing)
marisaB:addSkill(luaqianyi)

marisaC:addSkill(luazhuxing)
marisaC:addSkill(luaqianyi)

marisaD:addSkill(luazhuxing)
marisaD:addSkill(luaqianyi)

marisaE:addSkill(luazhuxing)
marisaE:addSkill(luaqianyi)

luapanjuey = sgs.CreateTriggerSkill{
	name = "luapanjuey",  
	global = true,
	events = {sgs.EnterDying, sgs.BuryVictim, sgs.HpRecover},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EnterDying then
            local room = player:getRoom()
            local dying = data:toDying()
            local damage = dying.damage
            if dying.who:objectName() == player:objectName() then
                for _, lilyW in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
                    local target = room:askForPlayerChosen(lilyW, room:getOtherPlayers(player), self:objectName(), "luapanjueyY", true, true) 
                    if target then 
						room:setPlayerMark(target, "@luapanjuey", 1)
						room:setPlayerMark(target, "luapanjuex", 1)
                        if not target:hasSkill("luaxinsheng") then room:acquireSkill(target, "luaxinsheng") end  
                        if not target:hasSkill("luachunlei") then room:acquireSkill(target, "luachunlei") end
                        
						room:filterCards(target, target:getCards("h"), true)
                        lilyW:setTag("PayVictim", sgs.QVariant(dying.who:objectName())) 
                    end 
                end 
            end 
        elseif event == sgs.BuryVictim then
            local death = data:toDeath() 
            for _, lilyW in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
                local objName = lilyW:getTag("PayVictim")
                if objName and objName:toString() and objName:toString() ~= "" then 
                    if death.who:objectName() == player:objectName() and objName:toString() == death.who:objectName() then 
                        for _, p in sgs.qlist(room:getAlivePlayers()) do 
                            if p:getMark("@luapanjuey") > 0 then
                                room:setPlayerMark(p, "@luapanjuey", 0)
                                lilyW:removeTag("PayVictim")
                                room:loseHp(p, 1)	
                            end 
                        end
                    end 
                end 
            end 
        else
            if player:getHp() <= 0 then return false end 
            for _, lilyW in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
                local objName = lilyW:getTag("PayVictim")
                if objName and objName:toString() and objName:toString() ~= "" then 
                    if objName:toString() == player:objectName() then 
                        for _, p in sgs.qlist(room:getAlivePlayers()) do 
                            if p:getMark("@luapanjuey") > 0 then
                                room:setPlayerMark(p, "@luapanjuey", 0)
                                lilyW:removeTag("PayVictim") 	
                            end 
                        end
                    end 
                end
            end  
		end
	end
}
luapanjuey2 = sgs.CreateTriggerSkill{
	name = "#luapanjuey" ,
	global = true,
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		if event == sgs.EventPhaseChanging then
			local room = player:getRoom()
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then 
                for _, p in sgs.qlist(room:getAlivePlayers()) do 
                    if p:getMark("luapanjuex") > 0 then
                        if p:hasSkill("luaxinsheng") then room:detachSkillFromPlayer(p, "luaxinsheng") end 
                        if p:hasSkill("luachunlei") then room:detachSkillFromPlayer(p, "luachunlei") end
						room:filterCards(p, p:getCards("h"), true)
						room:setPlayerMark(p, "luapanjuex", 0) 
                    end 
                end 
			end
		end
	end
}

luaguyuzCard = sgs.CreateSkillCard{
	name = "luaguyuz",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select)
		if to_select:objectName() == sgs.Self:objectName() then return false end 
		return not to_select:isKongcheng()
	end,
	on_use = function(self, room, source, targets)
        local function canLoseHp(playerXX)
			for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
				if hecatiaX and isFriendQ(room, playerXX, hecatiaX) and playerXX:objectName() ~= hecatiaX:objectName()
						and playerXX:getHp() == hecatiaX:getHp() then
					room:notifySkillInvoked(hecatiaX, "luayiti")
					return false
				end 
			end 
			for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
				if Erin and Erin:getKingdom() == playerXX:getKingdom() then
					room:notifySkillInvoked(Erin, "luajiance")
					return false
				end 
			end 
			return true
		end  

        local cards = {}
        local preLoseHpPlayer = {}
        local count = 0
        local card_x = room:askForCard(source, ".|.|.|hand!", "@luaguyuz", sgs.QVariant(), sgs.Card_MethodNone)
        if card_x:getSuit() == sgs.Card_Spade then
            count = count + 1
        else
            table.insert(preLoseHpPlayer, source) 
        end 
        table.insert(cards, card_x)
		for _, target in ipairs(targets) do
            card_x = room:askForCard(target, ".|.|.|hand!", "@luaguyuz", sgs.QVariant(), sgs.Card_MethodNone)
            if card_x:getSuit() == sgs.Card_Spade then
                count = count + 1
            else
                table.insert(preLoseHpPlayer, target) 
            end 
            table.insert(cards, card_x)
        end  

        
		room:showCard(source, cards[1]:getEffectiveId())
        local ii = 2
		for _, target in ipairs(targets) do
            room:getThread():delay(1500)
            room:showCard(target, cards[ii]:getEffectiveId())
            ii = ii + 1 
        end 

        if count > (#cards / 2) then
            for _, targetX in ipairs(preLoseHpPlayer) do
                if canLoseHp(targetX) then
                    room:loseHp(targetX, 1)	 
                end 
            end  
        else
			local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
            for _, card_xx in ipairs(cards) do
                if card_xx:getSuit() == sgs.Card_Spade then
                    dummy:addSubcard(card_xx:getEffectiveId())
                end 
            end 
            room:obtainCard(source, dummy, true) 
        end  
    end  
}

luaguyuz = sgs.CreateZeroCardViewAsSkill{
	name = "luaguyuz" ,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#duliang") and not player:isKongcheng()
	end ,
	view_as = function(self, cards)
		return luaguyuzCard:clone()
	end,
}


whiteB:addSkill(luapanjuey)
whiteB:addSkill(luapanjuey2)
whiteB:addSkill(luaguyuz) 


local function qiangxingshiyongYukari(card, room, Yukari, self)
	if not card:isKindOf("Jink") and not card:isKindOf("Nullification") and not card:isKindOf("sakura") and not card:isKindOf("Collateral") 
		and not (card:isKindOf("Slash") and not Yukari:canSlashWithoutCrossbow()) then 
		local dummy = card
		if Yukari:isCardLimited(dummy, sgs.Card_MethodUse) then  
			return false 
		end 
		local players = sgs.SPlayerList()
		for _, p in sgs.qlist(room:getAllPlayers()) do
			if not Yukari:isProhibited(p, dummy) and p:hasFlag("luayuanli") then 
				players:append(p)
			end 									
		end 
		
		local Carddata2 = sgs.QVariant() -- ai用
		Carddata2:setValue(dummy)
		room:setTag("luaqucaiTC", Carddata2)				
		local targets = sgs.SPlayerList()
		local target = 1
		if dummy:isKindOf("AOE") or dummy:isKindOf("AmazingGrace") or dummy:isKindOf("GodSalvation") then
			room:setPlayerFlag(Yukari, "qucaiAOE")
			while target and not players:isEmpty() do
				target = room:askForPlayerChosen(Yukari, players, "luayuanli", "luayuanliH", true, false) 
				if target then 
					players:removeOne(target)
					targets:append(target)
					room:setPlayerFlag(target, "qucaiAOEs")
					room:writeToConsole("Hello World! " .. target:getGeneralName())
				end 
			end 
			room:useCard(sgs.CardUseStruct(dummy, Yukari, sgs.SPlayerList()), true)
			for _, p in sgs.qlist(targets) do
				room:setPlayerFlag(p, "-qucaiAOEs") 
			end 
			room:setPlayerFlag(Yukari, "-qucaiAOE")			
			return true 
		elseif dummy:isKindOf("IronChain") then 
			while target and not players:isEmpty() do
				target = room:askForPlayerChosen(Yukari, players, "luayuanli", "luayuanliH", true, false) 
				if target then 
					players:removeOne(target)
					targets:append(target)
					room:setPlayerFlag(target, "qucaiAOEs")
					break
				end 
			end 
			room:useCard(sgs.CardUseStruct(dummy, Yukari, targets), true)
			return true  
		end 
		
		local target = room:askForPlayerChosen(Yukari, players, "luayuanli", "luayuanliH", true, false) 
		if not target then return false end 
		players:removeOne(target)
		targets:append(target)
		room:removeTag("luaqucaiTC")	
		room:useCard(sgs.CardUseStruct(dummy, Yukari, targets), true)
		return true 
	end  
	return false
end 
yuanliCard = sgs.CreateSkillCard{
	name = "luayuanli", 
	will_throw = false,
	handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		if self:getSubcards():isEmpty() then 
			return true
		else --启动结月缘计算器！
			--if #targets ~= self:getSubcards():length() then return true end 
			local sum = 0
			for _,p in pairs(targets) do
				sum = sum + p:getHp()
			end			
			for _, id in sgs.qlist(self:getSubcards()) do
				sum = sum - sgs.Sanguosha:getCard(id):getNumber()
			end			
			if sum == 0 then return false end 
			return true
		end
	end, 
	on_use = function(self, room, Yukari, targets) 
		if #targets == 0 then return end 
		local count = 0
		local sumHp = 0
		local aaa = sgs.SPlayerList()
		for _,sp in pairs(targets) do
			sumHp = sumHp + sp:getHp()
			aaa:append(sp)
			room:setPlayerFlag(sp, "luayuanli") 
		end		
		local num = 0
		while Yukari:getHandcardNum() > 0 do
			local _data = sgs.QVariant()
            room:setPlayerFlag(Yukari, "luaaoshuNull")
			local card = room:askForCard(Yukari, "TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand", "@luayuanli", _data, sgs.Card_MethodNone) 
            room:setPlayerFlag(Yukari, "-luaaoshuNull")
            if card then
				if not card:cardIsAvailable(Yukari) then 
				else
					if Yukari:isCardLimited(card, sgs.Card_MethodUse) then 
					else
						local Carddata3 = sgs.QVariant() -- ai用
						Carddata3:setValue(card)
						Yukari:getRoom():setTag("luaqiuwenTC", Carddata3)
						if qiangxingshiyongYukari(card, room, Yukari, self) then
							num = num + card:getNumber()
							count = count + 1
							Yukari:getRoom():removeTag("luaqiuwenTC")
						end
						Yukari:getRoom():removeTag("luaqiuwenTC")
					end
				end 
			else
				break
			end
		end 
		if count == 0 then return end 
		if sumHp == num then
			local target = room:askForPlayerChosen(Yukari, aaa, self:objectName(), "luayuanli2-invoke", true, true)
			if target then 
				local choice = room:askForChoice(Yukari, self:objectName(), "luayuanli1+luayuanli2")
				if choice == "luayuanli1" then
					target:drawCards(count, "luayuanli")
                    room:setPlayerFlag(target, "luaaoshuNull")
					room:askForUseCard(target, "TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand", "@RB2", -1, sgs.Card_MethodUse, false)
					room:askForUseCard(target, "TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand", "@RB2", -1, sgs.Card_MethodUse, false)
                    room:setPlayerFlag(target, "-luaaoshuNull")
				else 
					if room:askForDiscard(target, self:objectName(), 2, 2, false, true, "luayuanliG") then 
						room:recover(target, sgs.RecoverStruct(Yukari, nil, count))
					end 
				end 
			end 
		end 
		for _,sp in pairs(targets) do
			room:setPlayerFlag(sp, "-luayuanli") 
		end		
	end
}
luayuanli = sgs.CreateViewAsSkill{
	name = "luayuanli",
	n = 999,
	view_filter = function(self, selected, to_select) 
		return not to_select:isKindOf("Jink") and not to_select:isKindOf("Nullification") 
			and not to_select:isKindOf("sakura") and not to_select:isKindOf("Collateral")
	end, 
	view_as = function(self, cards)
		local card = yuanliCard:clone()
		for _, acard in ipairs(cards) do
			card:addSubcard(acard)
		end		
		return card
	end, 
	enabled_at_play = function(self, player)
		return not player:hasUsed("#luayuanli")
	end
}

luashengyue = sgs.CreateTriggerSkill{
	name = "luashengyue",
	events = {sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data, room)
		local change = data:toPhaseChange()
		if not player:isSkipped(change.to) and (change.to == sgs.Player_Draw) and room:askForSkillInvoke(player, self:objectName()) then
			local xo = room:getLord():getMark("@clock_time") + 1
			room:setPlayerMark(player, "luashengyue", xo)
			room:broadcastSkillInvoke(self:objectName())
			room:addPlayerMark(player, self:objectName().."engine")
			if player:getMark(self:objectName().."engine") > 0 then
				player:skip(change.to)
				if not player:hasSkill("luaqiyin") then room:acquireSkill(player, "luaqiyin") end
				if not player:hasSkill("luabiyue") then room:acquireSkill(player, "luabiyue") end 
				room:removePlayerMark(player, self:objectName().."engine")
			end
		end
	end
}

luashengyue2 = sgs.CreateTriggerSkill{
	name = "#luashengyue" ,
	global = true,
	events = {sgs.TurnStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local toziko = player:getRoom():findPlayerBySkillName("luashengyue")
		if not toziko then return false end
		local xo = room:getLord():getMark("@clock_time") + 1
		local xo2 = toziko:getMark("luashengyue")
		room:writeToConsole("luashengyue test F2 " .. xo .. " " .. xo2)
		if toziko and xo2 > 0 and xo ~= xo2 and xo > 0 then
			room:writeToConsole("luashengyue test")
			if toziko:hasSkill("luaqiyin") then room:handleAcquireDetachSkills(toziko, "-luaqiyin") end 
			if toziko:hasSkill("luabiyue") then room:handleAcquireDetachSkills(toziko, "-luabiyue") end 
		end
	end
}
luaqiyin3 = sgs.CreateTriggerSkill{
	name = "#luaqiyin2",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data, room)
		local move = data:toMoveOneTime()
		if move.from and move.from:objectName() == player:objectName() and player:hasSkill("luaqiyin") and not move.card_ids:isEmpty()
				and room:getCurrent():getPhase() == sgs.Player_Play then
			if bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) == sgs.CardMoveReason_S_REASON_DISCARD then
				local invoke = false
				local i = 0
				for _,id in sgs.qlist(move.card_ids) do
					card = sgs.Sanguosha:getCard(id)
					if move.from_places:at(i) == sgs.Player_PlaceHand
							or move.from_places:at(i) == sgs.Player_PlaceEquip then
						if card and card:getNumber() > 7 then
							player:drawCards(1)
						end
					end
					i = i + 1
				end
			end
		end
		return false
	end
}

yukariY:addSkill(luayuanli) 
yukariY:addSkill(luashengyue)
yukariY:addSkill(luashengyue2) 
yukariY:addSkill(luaqiyin3)

local function executeRandomEffect(room, use)
    -- 定义效果和对应的概率
    local effects = {
        {effect = "thunder_slash", probability = 0.2},
        {effect = "fire_slash", probability = 0.1},
        {effect = "slash_black", probability = 0.4},
        {effect = "slash_red", probability = 0.2},
        {effect = "killer", probability = 0.1},
    }

    -- 生成一个随机数 [0, 1)
    local rand = math.random()

    -- 累计概率
    local cumulative = 0
    for _, entry in ipairs(effects) do
        cumulative = cumulative + entry.probability
        if rand < cumulative then
            -- 执行对应的效果
            room:setEmotion(use.from, entry.effect)
            break
        end
    end
end

luazhisi = sgs.CreateTriggerSkill {
	name = "luazhisi",
	events = { sgs.PreCardUsed, sgs.TargetConfirmed }, 
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		local room = player:getRoom()
		if event == sgs.PreCardUsed and use.from and player:objectName() == use.from:objectName() and use.from:hasSkill(self:objectName())
				and use.to:length() == 1 and use.card and not use.card:isKindOf("SkillCard") then
            
            if not use.card:isKindOf("Slash") then  
                executeRandomEffect(room, use)
            end 

			for _, to in sgs.qlist(use.to) do 
                
                local Carddata2 = sgs.QVariant() -- ai用
                Carddata2:setValue(use.card)
                to:getRoom():setTag("luazhisiTC", Carddata2) 
                to:getRoom():setPlayerFlag(player, "luazhisiSource") 

                room:askForDiscard(to, self:objectName(), 1, 99, true, false, self:objectName())
                to:getRoom():setPlayerFlag(player, "-luazhisiSource") 
                to:getRoom():removeTag("luazhisiTC")
			end
        elseif event == sgs.TargetConfirmed and use.from and player:objectName() == use.from:objectName() and use.from:hasSkill(self:objectName())
            and use.to:length() == 1 and use.card and use.card:isKindOf("Slash") then
            for _, to in sgs.qlist(use.to) do     
                if room:askForSkillInvoke(player, self:objectName(), data) then 
					room:showAllCards(to)
                    local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0) 
					for _, card in sgs.list(to:getHandcards()) do
                        if card:isKindOf("Peach") or card:isKindOf("Jink") then
                            dummy:addSubcard(card)
                        end 
                    end 
                    if dummy:getSubcards():length() > 0 then 
                        use.card:setTag("drank", sgs.QVariant(dummy:getSubcards():length() - 1))
                        room:throwCard(dummy, to, to)
                    else 
                        dummy:deleteLater()
                        use.to = sgs.SPlayerList()
                        data:setValue(use)
                        return true
                    end 
                end  
            end 
		end
	end
}

luaciyuanCard = sgs.CreateSkillCard{
	name = "luaciyuan",
	will_throw = false, 	
    filter = function(self, targets, to_select)		
		return (#targets == 0)  
	end,
	on_use = function(self, room, source, targets) 
        local moves = sgs.CardsMoveList()
        local result2 = self:getSubcards()
        room:setPlayerMark(source, "@luaciyuan", 0) 
        local move = sgs.CardsMoveStruct(result2, targets[1], sgs.Player_PlaceHand, 
            sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_SWAP, source:objectName(), targets[1]:objectName(), self:objectName(), ""))
        moves:append(move) 
        local dummy_2 
        if not targets[1]:isKongcheng() then 
            dummy_2 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
            local result = sgs.IntList()
            for _, cardX in sgs.list(targets[1]:getHandcards()) do
                result:append(cardX:getEffectiveId())
                dummy_2:addSubcard(cardX)
            end 
            local move2 = sgs.CardsMoveStruct(result, source, sgs.Player_PlaceHand, 
                sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_SWAP, targets[1]:objectName(), source:objectName(), self:objectName(), ""))
            moves:append(move2)
        end 
        room:moveCardsAtomic(moves, false)
           

	end
}

luaciyuanVS = sgs.CreateViewAsSkill{
	name = "luaciyuan" ,
	n = 3 ,
	view_filter = function(self, cards, to_select)
		return not to_select:isEquipped() 
	end ,
	view_as = function(self, cards)
		if #cards ~= 3 then return nil end
		local skillcard = luaciyuanCard:clone()
        for _,card in ipairs(cards) do
            skillcard:addSubcard(card)
        end
		return skillcard
	end ,
	enabled_at_play = function(self, player)
		return player:getMark("@luaciyuan") > 0 
	end
}

luaciyuan = sgs.CreateTriggerSkill{
	name = "luaciyuan",  
	frequency = sgs.Skill_Limited, 
	limit_mark = "@luaciyuan", 
	view_as_skill = luaciyuanVS, 
	events = {sgs.CardUsed, sgs.HpRecover, sgs.DamageCaused},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom() 
		if event == sgs.CardUsed then
			local use = data:toCardUse()
            if use.from and player:objectName() == use.from:objectName() and use.from:hasSkill(self:objectName()) 
                and use.to:length() >= 1 and use.card and use.card:isKindOf("Weapon") then
                room:setPlayerMark(player, "@damageup", player:getMark("@damageup") + 1)
                room:setPlayerMark(player, "ciyuan", player:getMark("ciyuan") + 1)
                local xo = room:getLord():getMark("@clock_time") + 1
                room:setPlayerMark(player, "luaciyuanT", xo)
            end 
        elseif event == sgs.HpRecover then 
            room:setPlayerMark(player, "@damageup", player:getMark("@damageup") + 1)
            room:setPlayerMark(player, "ciyuan", player:getMark("ciyuan") + 1)
            local xo = room:getLord():getMark("@clock_time") + 1
            room:setPlayerMark(player, "luaciyuanT", xo)
        elseif event == sgs.DamageCaused then 
            local damage = data:toDamage()
            local y = player:getMark("ciyuan")  
            damage.damage = damage.damage + player:getMark("ciyuan")
            data:setValue(damage)
            room:setPlayerMark(player, "@damageup", math.max(player:getMark("@damageup") - y, 0))
            room:setPlayerMark(player, "ciyuan", 0)
        end 
	end
}
luaciyuan2 = sgs.CreateTriggerSkill{
	name = "#luaciyuan" ,
	global = true,
	events = {sgs.TurnStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom() 
        for _, x_reimu in sgs.qlist(room:findPlayersBySkillName("luaciyuan")) do 
            local xo = room:getLord():getMark("@clock_time") + 1
            local xo2 = x_reimu:getMark("luaciyuanT") 
            if xo2 > 0 and xo ~= xo2 and xo > 0 then  
                local y = x_reimu:getMark("ciyuan")  
                room:setPlayerMark(x_reimu, "@damageup", math.max(x_reimu:getMark("@damageup") - y, 0))
                room:setPlayerMark(x_reimu, "ciyuan", 0) 
            end
        end 
	end
}
x_reimu:addSkill(luazhisi)
x_reimu:addSkill(luaciyuan)
x_reimu:addSkill(luaciyuan2)



sgs.LoadTranslationTable {
    ["pay100"] = "踏雪寻梅", --注意这里每次要加逗号
    ["sp_rinN"] = "火焰猫燐",
    ["marisaN"] = "雾雨魔理沙",

    ["xiandinb"] = "依神姐妹",
    ["#xiandinb"] = "最凶最恶的双子",
    ["designer:xiandinb"] = "まりさ",
    ["illustrator:xiandinb"] = "ひそな",
    ["xiandinba"] = "泡沫",
    [":xiandinba"] = "摸牌阶段时，你可以改为摸与当前存活角色相同数量的牌并获得等量的“债”标记。",
    ["xiandinbb"] = "财祸",
    [":xiandinbb"] = "其他角色使用牌指定你为目标后，你可以增加一枚" ..
            "“债”令此牌无效。你使用牌指定其他角色为目标后，其可以令你弃置一枚“债”然后摸一张牌。锁定技，结束阶段，若你“债”标记数不少于存活人数，你失去一点体力上限并弃置所有“债”。",

    ["xiandinc"] = "八云紫",
    ["#xiandinc"] = "神隐的主犯",
    ["designer:xiandinc"] = "偷心大盗魔理沙",
    ["illustrator:xiandinc"] = "菊月",
    ["xiandinca"] = "谋逞",
    [":xiandinca"] = "出牌阶段限一次，你可以交给一名角色一张牌，令其对你指定的一名角色视为使用【杀】。此【杀】结算完毕后，该【杀】的目标可视为对你使用一张【杀】。",
    ["xiandincb"] = "魍魉",
    [":xiandincb"] = "一名角色回合结束时，你可获得本回合内对你造成过伤害的角色的一张牌。",

    ["yorigami"] = "依神姐妹",
    ["#yorigami"] = "最凶最恶的双子",
    ["designer:yorigami"] = "まりさ",
    ["luapaomo"] = "泡沫",
    [":luapaomo"] = "摸牌阶段时，你可以改为摸与当前存活角色相同数量的牌并获得等量的“债”标记。",
    ["luacaihuosp"] = "财祸",
    ["luacaihuosp2"] = "财祸（令依神姐妹弃置标记）",
    [":luacaihuosp"] = "其他角色使用牌指定你为目标后，你可以增加一枚" ..
            "“债”令此牌无效。你使用牌指定其他角色为目标后，其可以令你弃置一枚“债”然后摸一张牌。锁定技，结束阶段，若你“债”标记数不少于存活人数，你失去一点体力上限并弃置所有“债”。",

    ["yukariC"] = "八云紫",
    ["#yukariC"] = "神隐的主犯",
    ["designer:yukariC"] = "偷心大盗魔理沙",
    ["illustrator:yukariC"] = "菊月",
    ["luamoucheng"] = "谋逞",
    ["luamouchengX"] = "令其对你指定的一名角色视为使用【杀】",
    ["luamoucheng2"] = "视为对八云紫使用一张【杀】",
    [":luamoucheng"] = "出牌阶段限一次，你可以交给一名角色一张牌，令其对你指定的一名角色视为使用【杀】。此【杀】结算完毕后，该【杀】的目标可视为对你使用一张【杀】。",
    ["luawangliang"] = "魍魉",
    [":luawangliang"] = "一名角色回合结束时，你可获得本回合内对你造成过伤害的角色的一张牌。",

    ["shion"] = "依神紫苑",
    ["#shion"] = "粗衣粝食的贫穷神",
    ["designer:shion"] = "Paysage",
    ["illustrator:shion"] = "シンノバ",
    ["luabuxin"] = "不幸",
    ["luabuxin4"] = "不幸",
    [":luabuxin"] = "你可以弃一张牌并摸一张牌。锁定技，你手牌中与因“不幸”弃置牌所同名的牌均视为【秽】。你不能弃置【秽】。",
    ["luazaiesp"] = "灾厄",
    [":luazaiesp"] = "弃牌阶段结束时，若你手牌数大于上限，则你可以指定一名角色。此后所有角色使用【秽】时，其锁定成为此牌的额外目标。",

    ["xiandind"] = "琪露诺",
    ["#xiandind"] = "妖精教授",
    ["designer:xiandind"] = "月半",
    ["illustrator:xiandind"] = "cuteg",
    ["xiandinda"] = "算术",
    [":xiandinda"] = "出牌阶段限一次，你可以视为对一名其他角色使用【弹幕】，直到此牌结算完之前，以下适用：①所有角色的手牌视为【无懈可击】，"  ..
    "一名角色使用【无懈可击】后摸一张牌。②所有角色的非锁定技失效。③若一名角色使用的牌的点数与其本回合使用的上一张牌的点数合计为9，其摸两张牌，然后①②③失效。",

    ["cirnoD"] = "琪露诺",
    ["#cirnoD"] = "妖精教授",
    ["designer:cirnoD"] = "月半",
    ["illustrator:cirnoD"] = "cuteg",
    ["luasuanshu"] = "算术",
    [":luasuanshu"] = "出牌阶段限一次，你可以视为对一名其他角色使用【弹幕】，直到此牌结算完之前，以下适用：①所有角色的手牌视为【无懈可击】，"  ..
            "一名角色使用【无懈可击】后摸一张牌。②所有角色的非锁定技失效。③若一名角色使用的牌的点数与其本回合使用的上一张牌的点数合计为9，其摸两张牌，然后①②③失效。",

    ["xiandine"] = "蓬莱山辉夜",
    ["#xiandine"] = "永远的公主",
    ["designer:xiandine"] = "Paysage",
    ["illustrator:xiandine"] = "ジョンディー",
    ["xiandinea"] = "难题",
    [":xiandinea"] = "锁定技，若你装备区没有武器，且体力值与手牌数相等，则你使用牌无距离限制，且红桃手牌均视为火【杀】；方块手牌均视为【南蛮入侵】；梅花手牌均视为【无懈可击】；黑桃手牌视为你上一张使用的一张非装备牌。",
    ["xiandineb"] = "袖月",
    [":xiandineb"] = "转化技：出牌阶段限一次，①：你可以流失一点体力；②：你可以增加一点体力。",

    ["xiandinf"] = "上白泽慧音",
    ["#xiandinf"] = "史学家",
    ["designer:xiandinf"] = "Paysage",
    ["illustrator:xiandinf"] = "lumo1121",
    ["xiandinfa"] = "授业",
    [":xiandinfa"] = "每轮开始时，你可以将一张基本牌或单目标锦囊牌置于武将牌上，之后所有角色于其出牌阶段限一次，其可以将一张牌当作此牌使用。",
    ["xiandinfb"] = "头槌",
    [":xiandinfb"] = "发动“授业”的这轮结束时，你可以选择某区域内没有牌的至多X名角色，将你一张牌置于其对应区域内，并对其造成一点伤害（X为此轮未以“授业”使用牌的角色数）。",

 

    ["xiandinh"] = "射命丸文",
    ["#xiandinh"] = "风神少女",
    ["designer:xiandinh"] = "诺多战士",
    ["illustrator:xiandinh"] = "Neko",
    ["xiandinha"] = "急速",
    [":xiandinha"] = "一名角色的回合结束时，若本回合没有任何角色的牌置入弃牌堆，你可以判定，若为锦囊牌，你执行一个额外回合。",
    ["xiandinhb"] = "风行",
    [":xiandinhb"] = "每轮限一次，你的牌被弃置时，你可以将之全部置于牌堆顶，然后从场上选与这些牌中有相同花色的一张牌，移动之。",

    ["xiandini"] = "芙兰朵露",
    ["#xiandini"] = "禁忌的波动",
    ["designer:xiandini"] = "kevin",
    ["illustrator:xiandinh"] = "ルヒカ",
    ["xiandinia"] = "分形",
    [":xiandinia"] = "准备阶段，你可以展示一张手牌，获得牌堆顶与其点数相同的牌（至多三张）。若如此做，你于本回合的摸牌阶段少摸一张牌。",
    ["xiandinib"] = "禁果",
    [":xiandinib"] = "你不能成为一名角色的于摸牌阶段外获得的牌的目标。",

    ["rh_flandreH"] = "芙兰朵露",
    ["#rh_flandreH"] = "禁忌的波动",
    ["designer:rh_flandreH"] = "kevin",
    ["illustrator:rh_flandreH"] = "ルヒカ",
    ["luafenxingsp"] = "分形",
    ["@luafenxingsp"] = "你可以展示一张牌来发动“分形”",
    [":luafenxingsp"] = "准备阶段，你可以展示一张手牌，获得牌堆顶与其点数相同的牌（至多三张）。若如此做，你于本回合的摸牌阶段少摸一张牌。",
    ["luajinguo"] = "禁果",
    [":luajinguo"] = "你不能成为一名角色的于摸牌阶段外获得的牌的目标。",

    ["xiandinj"] = "因幡帝",
    ["#xiandinj"] = "狡猾的兔妖",
    ["designer:xiandinj"] = "星际玩家盲人⑨、Paysage",
    ["illustrator:xiandinj"] = "るなむん",
    ["xiandinja"] = "狡兔",
    ["luamin"] = "罠",
    [":xiandinja"] = "出牌阶段限一次，你可以弃一张牌，令一名角色获得一张任意基本牌（不公开）。此牌可以无视距离和次数限制对任意角色使用。",
    ["xiandinjb"] = "三窟",
    [":xiandinjb"] = "你的牌被弃置时，你可以将其中一张与你判定区牌种类均不同的牌置于判定区。当你濒死时，你可以弃置判定区的所有牌并回复等量体力，然后将你所有牌置于弃牌堆。",

    ["sp_tewi"] = "因幡帝",
    ["#sp_tewi"] = "狡猾的兔妖",
    ["designer:sp_tewi"] = "星际玩家盲人⑨、Paysage",
    ["illustrator:sp_tewi"] = "るなむん",
    ["luajiaotu"] = "狡兔",
    [":luajiaotu"] = "出牌阶段限一次，你可以弃一张牌，令一名角色获得一张任意基本牌（不公开）。此牌可以无视距离和次数限制对任意角色使用。",
    ["luasanku"] = "三窟",
    [":luasanku"] = "你的牌被弃置时，你可以将其中一张与你判定区牌种类均不同的牌置于判定区。当你濒死时，你可以弃置判定区的所有牌并回复等量体力，然后将你所有牌置于弃牌堆。",
 
    ["sp_marisa"] = "雾雨魔理沙",
    ["#sp_marisa"] = "无名的魔术师",
    ["designer:sp_marisa"] = "Paysage",
    ["illustrator:sp_marisa"] = "ファルケン",
    ["luazhuxing"] = "逐星",
    [":luazhuxing"] = "每当你失去或获得手牌后，视为你对与你手牌数相同的一名角色使用了一张【弹幕】，并将一张手牌置于判定区。",
    ["luaqianyi"] = "千仪",
    [":luaqianyi"] = "转化技，出牌阶段限一次，①：你可以摸一张牌，获得“闪光”并失去其他技能。②：你可以获得“逐星”，弃一张牌并失去其他技能。",

    ["xiandinl"] = "莉莉黑",
    ["#xiandinl"] = "妖精审判长",
    ["designer:xiandinl"] = "Paysage",
    ["illustrator:xiandinl"] = "北辰小北",
    ["xiandinla"] = "春雷",
    [":xiandinla"] = "锁定技，你的黑桃手牌均视为【桃】。",
    ["xiandinlb"] = "判决",
    [":xiandinlb"] = "一名角色濒死时，你可以令另一名角色于本回合获得【心声】【春雷】。若该濒死角色阵亡，你指定的角色流失一点体力。",
    ["xiandinlc"] = "谷雨",
    [":xiandinlc"] = "出牌阶段限一次，你可以与任意名其他角色各展示一张手牌。若以此法展示的黑桃牌较多，未展示黑桃牌的角色流失一点体力，否则你获得这些黑桃牌。",

    ["whiteB"] = "莉莉黑",
    ["#whiteB"] = "妖精审判长",
    ["designer:whiteB"] = "Paysage",
    ["illustrator:whiteB"] = "北辰小北",
    ["luachunlei"] = "春雷",
    [":luachunlei"] = "锁定技，你的黑桃手牌均视为【桃】。",
    ["luapanjueyY"] = "你可以发动“判决”<br/> <b>操作提示</b>: 选择一名角色→点击确定<br/>", 
    ["luapanjuey"] = "判决",
    [":luapanjuey"] = "一名角色濒死时，你可以令另一名角色于本回合获得【心声】【春雷】。若该濒死角色阵亡，你指定的角色流失一点体力。",
    ["luaguyuz"] = "谷雨",
    ["@luaguyuz"] = "因莉莉黑的技能，你需要展示一张手牌。",
    [":luaguyuz"] = "出牌阶段限一次，你可以与任意名其他角色各展示一张手牌。若以此法展示的黑桃牌较多，未展示黑桃牌的角色流失一点体力，否则你获得这些黑桃牌。",

    ["x_reimu"] = "祸灵梦",
    ["#x_reimu"] = "神最上",
    ["designer:x_reimu"] = "Paysage",
    ["illustrator:x_reimu"] = "1402",
    ["luazhisi"] = "直死",
    ["luazhisiX"] = "因 祸灵梦 “直死” 技能效果，你可以弃置任意数量的牌（可以不弃）",
    [":luazhisi"] = "你使用牌指定唯一目标前，其可以弃置任意张手牌。你使用【杀】指定唯一目标后，你可以展示其所有手牌：若有【闪】/【桃】，其全部弃置之，并将此【杀】伤害改为所弃置牌的数量；否则此【杀】无效。",
    ["luaciyuan"] = "次元",
    [":luaciyuan"] = "你使用武器牌或回复体力后，本轮你下一次造成的伤害+1。限定技，你可以用三张手牌交换一名角色的全部手牌。", 

	["yukariY"] = "结月缘",
	["#yukariY"] = "几望之月",
	["designer:yukariY"] = "Paysage", 
	["luayuanli"] = "缘理",
	["@luayuanli"] = "请决定你要使用的一张牌（现在不必指定目标）",
	["luayuanliH"] = "请决定你要使用的牌的目标。对于多目标锦囊，你需要重复此步骤来指定多个目标。",
	[":luayuanli"] = "出牌阶段限一次，你可以指定任意名角色并使用X张牌（目标可以并只能从这些角色中选）。" ..
		"若这些牌合计点数与其合计体力值相等，则你可以令其中一名角色执行一项：①摸X张牌，使用至多两张牌；②弃两张牌，回复至多X点体力。",
	
	["luayuanli2-invoke"] = "你可以发动“缘理”<br/> <b>操作提示</b>: 选择一名角色来继续执行效果→点击确定<br/>",	
	["luayuanliG"] = "因结月缘的技能效果，你必须弃置牌",
	["luayuanli1"] = "摸X张牌，使用至多两张牌",
	["luayuanli2"] = "弃两张牌，回复至多X点体力",

	["luashengyue"] = "声月",
	[":luashengyue"] = "你可以跳过摸牌阶段，并获得【七音】【闭月】直到本轮结束。",
}

return {extension_pay_yuki}