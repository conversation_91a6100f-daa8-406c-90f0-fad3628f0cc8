---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by Administrator.
--- DateTime: 2020/8/24 18:42
---
module("extensions.pay14",package.seeall)
extension = sgs.Package("pay14")

miyoi = sgs.General(extension,"miyoi","luacai",3,false,true,false)
nos_marisa = sgs.General(extension,"nos_marisa","luaxi",3,false,false,false)
xiandina = sgs.General(extension,"xiandina","luacai",3,false,true,true)

luamiyoia = sgs.CreateTriggerSkill{
    name = "luamiyoia",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
luamiyoib = sgs.CreateTriggerSkill{
    name = "luamiyoib",
    frequency = sgs.Skill_Limited,
    events = {sgs.GameStart},
    limit_mark = "@weixin",
    on_trigger = function()
    end
}
miyoi:addSkill(luamiyoib)
miyoi:addSkill(luamiyoia)


liangxiaoCard = sgs.CreateSkillCard{
    name = "lualiangxiao",
    will_throw = false,
    filter = function(self, targets, to_select)
        return (#targets == 0)
    end,
    on_effect = function(self, effect)
        local room = effect.to:getRoom()
        room:detachSkillFromPlayer(effect.to, "lualiangxiao3")
        effect.to:obtainCard(self)
        local card = self:getSubcards():at(0)
        card = sgs.Sanguosha:getCard(card)
        effect.to:getRoom():setCardFlag(card, "lualiangxiao")
        room:acquireSkill(effect.to, "lualiangxiao3", false)
    end
}
lualiangxiaoVS = sgs.CreateOneCardViewAsSkill{
    name = "lualiangxiao",
    filter_pattern = "BasicCard",
    view_as = function(self,card)
        local skillcard = liangxiaoCard:clone()
        skillcard:addSubcard(card)
        return skillcard
    end,
    enabled_at_play = function(self,player)
        return not player:isKongcheng()
    end,
}

lualiangxiao = sgs.CreateTriggerSkill{
    name = "lualiangxiao",
    global = true,
    view_as_skill = lualiangxiaoVS,
    events = {sgs.CardFinished},
    on_trigger = function(self, event, player, data, room)
        if event == sgs.CardFinished then
            local use = data:toCardUse()
            local xo = room:getLord():getMark("@clock_time") + 1
            if use.card:isKindOf("Analeptic") and use.from and use.from:objectName() == player:objectName() and use.from:getMark("lualiangxiaoA") ~= xo then
                room:setPlayerMark(player, "lualiangxiaoA", xo)
                for _, miyoi in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
                    local _data = sgs.QVariant()
                    _data:setValue(player)
                    if use.from:objectName() ~= miyoi:objectName() and room:askForSkillInvoke(miyoi, "lualiangxiao", _data) then
                        miyoi:drawCards(1)

                        if player and player:isAlive() then
                            local room_0 = player:getRoom()
                            local thread = room_0:getThread()
                            local old_phase = player:getPhase()

                            player:setPhase(sgs.Player_Play)

                            room_0:broadcastProperty(player, "phase")
                            if not thread:trigger(sgs.EventPhaseStart, room_0, player) then
                                thread:trigger(sgs.EventPhaseProceeding, room_0, player)
                            end

                            thread:trigger(sgs.EventPhaseEnd, room_0, player)
                            player:setPhase(old_phase)
                            room_0:broadcastProperty(player, "phase")
                        end
                    end
                end
            end
        end
    end
}

luajieyou2 = sgs.CreateMasochismSkill{
    name = "#luajieyou",
    global = true,
    on_damaged = function(self,player, damage)
        local room = player:getRoom()
        if not player:hasSkill("luajieyou") and not player:hasFlag("luajieyouX") then return false end
        if player:hasSkill("luajieyou") then
            if damage.damage == 0 then return false end
            if not damage.from then return false end
            room:setPlayerFlag(player, "luajieyou")
            room:setPlayerFlag(damage.from, "luajieyouX")
        end
        if player:hasFlag("luajieyouX") then
            if damage.damage == 0 then return false end
            local miyoi = player:getRoom():findPlayerBySkillName("luajieyou")
            for i = 1, damage.damage, 1 do
                room:recover(miyoi, sgs.RecoverStruct(miyoi, nil, 1))
            end
        end
    end,
}
luajieyou = sgs.CreateTriggerSkill{
    name = "luajieyou" ,
    events = {sgs.EventPhaseChanging} ,
    priority = 1,
	global = true,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        local change = data:toPhaseChange()
        if change.to == sgs.Player_NotActive then
            for _,p in sgs.qlist(room:getAlivePlayers()) do
                room:setPlayerFlag(p, "-luajieyouX")
            end
        end
        local miyoi = player:getRoom():findPlayerBySkillName("luajieyou")
        if not miyoi then return false end
        if not miyoi:hasFlag("luajieyou") then return false end
        --if miyoi:askForSkillInvoke("luajieyou") then
            local targetX = room:askForPlayerChosen(miyoi, room:getOtherPlayers(miyoi), "luajieyou", "luajieyou", true)
            if targetX and targetX:isAlive() then
                room:setPlayerFlag(targetX, "luajieyouABC")
				room:setPlayerFlag(targetX, "luaaoshuNull")
                room:askForUseCard(targetX, "TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand", "@luajieyou")
                room:setPlayerFlag(targetX, "-luajieyouABC")
				room:setPlayerFlag(targetX, "-luaaoshuNull")
            end
        room:setPlayerFlag(miyoi, "-luajieyou") 
    end
}
xiandina:addSkill(lualiangxiao)
xiandina:addSkill(luajieyou)
xiandina:addSkill(luajieyou2)

luayanling = sgs.CreateTriggerSkill{
	name = "luayanling" ,
    global = true,
	events = {sgs.PreCardUsed, sgs.CardEffected, sgs.BeforeCardsMove} ,
	on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if event == sgs.PreCardUsed then
            local use = data:toCardUse()
            for _, marisa in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do 
                if use.card and not use.card:isKindOf("EquipCard") and use.from and use.from:objectName() == player:objectName() and not use.card:isKindOf("SkillCard") 
                    and use.from:getHandcardNum() == marisa:getHandcardNum() and use.from:objectName() ~= marisa:objectName() 
                    and not use.card:isKindOf("Jink") and not use.card:isKindOf("Nullification") and not use.card:isKindOf("Collateral") 
                    and use.to:length() == 1 and not use.from:hasFlag(self:objectName()) then 

                        local ids = sgs.IntList()
						ids:append(use.card:getEffectiveId())
						room:fillAG(ids, marisa)
						room:getThread():delay(1500)
						room:clearAG() 
						room:setCardFlag(use.card, "visible") 

                    local card = room:askForCard(marisa, "TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand", "@luayanling", sgs.QVariant(), sgs.Card_MethodResponse)
                    if card then 
                        local log = sgs.LogMessage()
                        log.type = "$luayanling"
                        log.from = marisa
                        log.card_str = card:toString()
                        log.arg = self:objectName()
                        room:sendLog(log)
                        room:notifySkillInvoked(marisa, self:objectName())  
                        room:setCardFlag(use.card, "luayanling")
                        marisa:setFlags(self:objectName() .. "Obtain")
                        use.from:setFlags(self:objectName())
                        room:useCard(sgs.CardUseStruct(card, use.from, use.to), true)
                        use.from:setFlags("-" .. self:objectName())  
                        return false
                    end  
                end 
            end 
        elseif event == sgs.CardEffected then
            local effect = data:toCardEffect()
            if effect.card and effect.card:hasFlag(self:objectName()) then 
                room:writeToConsole("Hello World! luayanling")
                return true
            end 
        else 
			local move = data:toMoveOneTime() 
            for _, marisa in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
                if marisa:hasFlag(self:objectName() .. "Obtain") then 
                    for _, id in sgs.qlist(move.card_ids) do 
                        local card = sgs.Sanguosha:getCard(id)
                        if card:hasFlag(self:objectName()) then 
                            move.to = marisa
                            move.to_place = sgs.Player_PlaceHand
                            marisa:setFlags("-" .. self:objectName() .. "Obtain")
                            break
                        end
                    end
                end 
            end 
            data:setValue(move)
        end 
		return false
	end
}

luaaoshuxxCard = sgs.CreateSkillCard{
	name = "luaaoshuxx" , 
	target_fixed = true,
    on_validate_in_response = function(self, user)
        local room = user:getRoom()
        room:setPlayerMark(user, "luaaoshuxxR", 1)
        local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
        room:writeToConsole("luaaoshuxx pattern " .. pattern .. "  end")
        if not pattern or pattern == "" then 
            pattern = "Nullification"
        end 
        if pattern == "TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand" and not user:hasFlag("luaaoshuNull") then
            pattern = "Nullification" 
        end 
        room:writeToConsole("luaaoshuxx pattern2 " .. pattern .. "  end")
        local source = user
        for i = 0, 500 do
            if source:hasFlag(self:objectName() .. tostring(i)) then
                source:setFlags("-" .. self:objectName() .. tostring(i)) 
            end 
        end 
        local judge = sgs.JudgeStruct()
        judge.pattern = "."
        judge.good = true
        judge.reason = self:objectName()
        judge.who = source
        room:judge(judge)
        local card = judge.card
        local cardX = room:askForCard(source, pattern, "luaaoshuxxRes", sgs.QVariant(), sgs.Card_MethodNone)
        if cardX and not source:isJilei(cardX) then 
            if cardX:getSuit() == card:getSuit() and room:askForSkillInvoke(user, self:objectName(), sgs.QVariant()) then
                local dummy = sgs.Sanguosha:cloneCard("yuzhi", sgs.Card_SuitToBeDecided, -1)
                dummy:addSubcard(cardX) 
                local targets = sgs.SPlayerList()
                for _, p in sgs.qlist(room:getAlivePlayers()) do
                    if not source:isProhibited(p, dummy) and source:inMyAttackRange(p) then
                        targets:append(p)
                    end 
                end
                local tartgetB = room:askForPlayerChosen(user, targets, self:objectName(), "luaaoshuxxPlayer", true, false)
                if tartgetB then 
                    room:useCard(sgs.CardUseStruct(dummy, source, tartgetB))
                end  
            end 
            return cardX	
        end  
        return nil
	end, 
}
luaaoshuxxVS = sgs.CreateZeroCardViewAsSkill{
	name = "luaaoshuxx",
	view_as = function(self, cards)
		return luaaoshuxxCard:clone()
	end,
	enabled_at_play = function(self, player)
		return false
	end, 
	enabled_at_response = function(self, player, pattern) 
        local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
        if not pattern or pattern == "" then return false end 
        --if pattern == "Nullification" then return false end 
        --if pattern == "nullification" then return false end 
        if string.find(pattern, "@ChooseJiang") then return false end 
		return player:getPhase() == sgs.Player_NotActive and player:getMark("luaaoshuxxR") == 0
	end,
    enabled_at_nullification = function(self, player)
        for _, card in sgs.list(player:getHandcards()) do
            if card:isKindOf("Nullification") and not player:isJilei(card) then
                return player:getPhase() == sgs.Player_NotActive and player:getMark("luaaoshuxxR") == 0 
            end 
        end 
        return false 
	end, 
}

luaaoshuxx = sgs.CreateTriggerSkill{
	name = "luaaoshuxx" ,
	global = true,
	view_as_skill = luaaoshuxxVS, 
	events = {sgs.TurnStart} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom() 
		for _, toziko in sgs.qlist(room:findPlayersBySkillName("luaaoshuxx")) do 
			if toziko and toziko:getMark("luaaoshuxxR") == 1 then 
                room:setPlayerMark(toziko, "luaaoshuxxR", 0)
			end 	
		end 
		return false
	end , 
	priority = 1
} 

nos_marisa:addSkill(luayanling)
nos_marisa:addSkill(luaaoshuxx)



sgs.LoadTranslationTable {
    ["pay14"] = "赣鄱荟萃", --注意这里每次要加逗号
    ["miyoi"] = "奥野田美宵",
    ["#miyoi"]= "梦幻酒家的店花",
    ["xiandina"] = "奥野田美宵",
    ["#xiandina"]= "梦幻酒家的店花",
    ["illustrator:xiandina"] = "月小鹅",
    ["designer:xiandina"] = "Paysage",
    ["illustrator:miyoi"] = "月小鹅",
    ["designer:miyoi"] = "Paysage",

    ["luajieyou"] = "解忧",
    [":luajieyou"] = "你受到伤害的阶段结束时，你可以令一名其他角色使用一张牌，本回合内对你造成过伤害的角色每受到一点伤害，你回复一点体力。",
    ["lualiangxiao"] = "良宵",
    ["lualiangxiao3"] = "良宵",
    [":lualiangxiao"] = "出牌阶段，你可以将一张基本牌交给一名角色，以此法交出的牌改为【酒】。一名其他角色于每轮首次使用【酒】后，你可以摸一张牌并令其执行一个额外的出牌阶段。",

    ["luamiyoia"] = "解忧",
    ["@luajieyou"] = "你可以使用一张牌",
    [":luamiyoia"] = "你受到伤害的阶段结束时，你可以令一名其他角色使用一张牌，本回合内对你造成过伤害的角色每受到一点伤害，你回复一点体力。",
    ["luamiyoib"] = "良宵",
    [":luamiyoib"] = "出牌阶段，你可以将一张基本牌交给一名角色，以此法交出的牌改为【酒】。一名其他角色于每轮首次使用【酒】后，你可以摸一张牌并令其执行一个额外的出牌阶段。",

    ["nos_marisa"] = "雾雨魔理沙",
    ["#nos_marisa"]= "非凡的魔法使", 
    ["illustrator:nos_marisa"] = "米诺",
    ["designer:nos_marisa"] = "Marisa",  
    ["luayanling"] = "言灵", 
    ["@luayanling"] = "你可以打出一张牌替换这张将要被使用的牌", 
    ["luaaoshuxxRes"] = "请用正确的牌来响应",
    ["$luayanling"] = "因 %from “%arg”的效果，原本使用的卡牌被改写为了 %card 。",
    [":luayanling"] = "与你手牌数相同的其他角色使用非装备牌仅指定一名角色为目标时，你可以打出一张牌替换之，且此牌除你之外不能被响应。",
    ["luaaoshuxxPlayer"] = "请选择【弹幕】要指定的目标",
    ["luaaoshuxx"] = "奥术",
    [":luaaoshuxx"] = "每回合限一次，当你于回合外需要打出或使用牌时，你可以判定，若判定牌与你响应的牌花色相同，你可以将此判定牌当作【弹幕】使用。",

}













