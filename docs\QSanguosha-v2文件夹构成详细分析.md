# QSanguosha-v2 项目文件夹构成分析

## 📋 目录概览

QSanguosha-v2是一个基于Qt和Lua的开源三国杀游戏项目，具有完整的模块化架构和丰富的扩展能力。

### 🏗️ 项目根目录结构

```
QSanguosha-v2/
├── 🎮 核心游戏系统
│   ├── lua/                    # Lua脚本引擎
│   ├── extensions/             # 扩展包系统
│   ├── scenarios/              # 游戏场景模式
│   └── etc/                    # 游戏配置文件
│
├── 🌐 多语言与本地化
│   └── lang/                   # 多语言支持系统
│
├── 🎨 视觉与媒体资源
│   ├── image/                  # 图片资源库
│   ├── audio/                  # 音频资源库
│   ├── font/                   # 字体文件
│   ├── full/                   # 武将全身立绘
│   ├── skins/                  # 界面皮肤主题
│   └── qss/                    # 样式表文件
│
├── 🔧 技术框架支持
│   ├── bearer/                 # Qt网络插件
│   ├── iconengines/            # Qt图标引擎
│   ├── imageformats/           # Qt图像格式
│   ├── platforms/              # Qt平台适配
│   ├── sqldrivers/             # 数据库驱动
│   ├── translations/           # Qt翻译文件
│   └── qml1tooling/            # QML调试工具
│
├── 🛠️ 开发与扩展
│   ├── docs/                   # 项目文档
│   ├── doc/                    # 原始文档
│   ├── diy/                    # DIY自定义
│   ├── ui-script/              # UI脚本
│   ├── listserver/             # 服务器支持
│   └── dmp/                    # 调试转储
│
└── 📦 程序文件
    ├── QSanguosha.exe          # 主程序
    ├── config.ini              # 主配置
    └── Qt*.dll                 # 运行库
```

### 📊 目录分类统计

| 分类 | 目录数 | 主要功能 | 重要性 |
|------|--------|----------|--------|
| **核心游戏系统** | 4个 | 游戏逻辑、扩展、场景、配置 | ⭐⭐⭐⭐⭐ |
| **多语言本地化** | 1个 | 翻译、本地化支持 | ⭐⭐⭐⭐ |
| **视觉媒体资源** | 6个 | 图片、音频、字体、皮肤 | ⭐⭐⭐⭐⭐ |
| **技术框架支持** | 7个 | Qt插件、平台适配 | ⭐⭐⭐ |
| **开发与扩展** | 6个 | 文档、开发工具、自定义 | ⭐⭐⭐ |
| **程序文件** | 若干 | 可执行文件、配置、库 | ⭐⭐⭐⭐⭐ |

## 🎮 核心游戏系统

### 1. lua/ - Lua脚本引擎 ⭐⭐⭐⭐⭐

QSanguosha-v2的核心逻辑引擎，负责游戏规则、AI逻辑和配置管理。

```
lua/
├── 🤖 ai/                     # AI智能系统 (50+ AI脚本)
│   ├── standard-ai.lua        # 标准包AI逻辑
│   ├── maneuvering-ai.lua     # 军争包AI逻辑
│   ├── pay*-ai.lua           # 付费扩展AI逻辑
│   ├── smart-ai.lua          # 智能AI核心
│   └── ...                   # 其他扩展包AI
│
├── 📚 lib/                    # Lua核心库
│   ├── json.lua              # JSON数据处理
│   ├── middleclass.lua       # 面向对象编程
│   └── sqlite3.lua           # 数据库接口
│
├── ⚙️ 核心配置文件
│   ├── config.lua            # 游戏主配置
│   ├── sanguosha.lua         # 游戏启动脚本
│   ├── sgs_ex.lua            # 扩展函数库
│   └── utilities.lua         # 工具函数集
```

**🔧 核心功能**：
- **AI决策系统**：智能对手的决策逻辑
- **游戏规则引擎**：三国杀核心规则实现
- **扩展包管理**：动态加载各种扩展包
- **配置系统**：游戏参数和包名管理

### 2. extensions/ - 扩展包系统 ⭐⭐⭐⭐⭐

游戏内容的核心，包含所有武将、卡牌和特殊模式的定义。

```
extensions/
├── 💰 付费扩展包系列 (20+ 扩展包)
│   ├── pay.lua               # 基础付费包
│   ├── pay12.lua             # 十二周年包
│   ├── pay99.lua             # 特殊纪念包
│   └── pay*.lua              # 其他付费扩展
│
├── 🎯 特殊模式扩展
│   ├── extra.lua             # 额外扩展内容
│   ├── hulaoguan.lua         # 虎牢关模式
│   └── ...                   # 其他特殊模式
│
├── 🔒 隐藏内容
│   └── hidden/               # 隐藏扩展包
│
└── 📝 源代码
    └── src/                  # 扩展包源码
```

**🎲 主要内容**：
- **武将系统**：300+ 武将角色定义
- **技能实现**：1000+ 武将技能逻辑
- **卡牌系统**：自定义卡牌的完整实现
- **游戏模式**：特殊玩法和规则变体

### 3. scenarios/ - 游戏场景系统 ⭐⭐⭐⭐

各种特殊游戏模式和剧情关卡的定义。

```
scenarios/
├── 🏛️ 经典模式
│   ├── basara.html           # 暗将模式
│   ├── hegemony.html         # 国战模式
│   └── bossmode.html         # Boss挑战模式
│
├── ⚔️ 历史战役
│   ├── fancheng.html         # 樊城之战
│   ├── guandu.html           # 官渡之战
│   └── hulaopass.html        # 虎牢关之战
│
└── 👥 多人模式
    ├── couple.html           # 夫妻模式
    └── ...                   # 其他多人玩法
```

**🎪 模式特色**：
- **剧情重现**：经典历史战役
- **特殊规则**：独特的游戏机制
- **平衡调整**：针对性的平衡设计

### 4. etc/ - 游戏配置系统 ⭐⭐⭐

游戏平衡性和角色配置的核心文件。

```
etc/
├── 🎯 模式配置
│   ├── 1v1-priority.txt      # 1v1模式优先级
│   ├── 3v3-priority.txt      # 3v3模式优先级
│   └── customScenes/         # 自定义场景
│
├── 👑 身份配置
│   ├── loyalist.txt          # 忠臣配置
│   ├── rebel.txt             # 反贼配置
│   ├── renegade.txt          # 内奸配置
│   └── double-generals.txt   # 双将配置
```

**⚖️ 平衡系统**：
- **武将强度**：各模式下的武将平衡
- **身份分配**：不同身份的配置规则
- **模式适配**：特殊模式的参数调整

## 🌐 多语言与本地化

### lang/ - 多语言支持系统 ⭐⭐⭐⭐

完整的游戏本地化框架，支持多语言和地区适配。

```
lang/
└── zh_CN/                    # 简体中文本地化
    ├── 📦 Package/           # 扩展包翻译 (25+ 翻译文件)
    │   ├── StandardPackage.lua       # 标准包翻译
    │   ├── ManeuveringPackage.lua    # 军争包翻译
    │   ├── NostalgiaPackage.lua      # 朝花夕拾包翻译
    │   ├── FirePackage.lua           # 火包翻译
    │   ├── WindPackage.lua           # 风包翻译
    │   └── ...                       # 其他扩展包翻译
    │
    ├── 🎵 Audio/             # 音频本地化
    │   ├── StandardGeneralPackageLines.lua  # 标准武将台词
    │   ├── FirePackageLines.lua             # 火包武将台词
    │   └── ...                              # 其他音频翻译
    │
    ├── 📝 核心翻译文件
    │   ├── Common.lua        # 通用翻译
    │   ├── skill.lua         # 技能描述翻译
    │   ├── BasaraMode.lua    # 暗将模式翻译
    │   └── ...               # 其他模式翻译
```

**🌍 本地化特色**：
- **完整翻译**：游戏内所有文本的中文化
- **音频配置**：武将台词和音效的本地化
- **文化适配**：符合中文用户习惯的表达
- **扩展性强**：易于添加其他语言支持

## 🎨 视觉与媒体资源

### 1. image/ - 图片资源库 ⭐⭐⭐⭐⭐

游戏的完整视觉资源系统，包含所有图片素材。

```
image/
├── 🃏 卡牌图片系统
│   ├── card/                 # 标准卡牌图片 (100+ 卡牌)
│   ├── big-card/             # 大尺寸卡牌图片
│   └── equips/               # 装备卡牌图片
│
├── 👤 武将图片系统
│   └── generals/             # 武将图片库
│       ├── big/              # 大武将头像
│       ├── card/             # 武将卡牌图
│       ├── small/            # 小武将头像
│       └── double-general/   # 双将模式图片
│
├── 🎭 皮肤与主题
│   ├── fullskin/             # 全皮肤资源
│   ├── heroskin/             # 英雄皮肤系统
│   └── compact/              # 紧凑模式资源
│
├── 🏛️ 界面与系统
│   ├── system/               # 系统界面元素
│   ├── kingdom/              # 势力标识图标
│   ├── mark/                 # 游戏状态标记 (200+ 标记)
│   └── gender/               # 性别标识
│
├── 🎬 动画与特效
│   ├── animate/              # 动画资源
│   └── ...                   # 其他特效资源
```

**🖼️ 资源特色**：
- **多分辨率支持**：适配不同屏幕尺寸
- **丰富的武将形象**：每个武将多套图片
- **完整的卡牌视觉**：所有卡牌的精美图片
- **状态标记系统**：200+ 游戏状态图标

### 2. audio/ - 音频资源库 ⭐⭐⭐⭐

完整的游戏音频系统，提供沉浸式的听觉体验。

```
audio/
├── 🎵 游戏音效
│   ├── card/                 # 卡牌使用音效
│   ├── equip/                # 装备音效
│   ├── skill/                # 技能发动音效
│   └── system/               # 系统提示音效
│
├── 🎤 武将语音
│   ├── 武将台词/              # 各武将的专属台词
│   ├── 技能语音/              # 技能发动语音
│   └── 死亡台词/              # 武将阵亡语音
│
└── 🎼 背景音乐
    ├── 主界面音乐/            # 菜单背景音乐
    ├── 游戏音乐/              # 对战背景音乐
    └── 特殊模式音乐/          # 特殊场景音乐
```

**🔊 音频特色**：
- **丰富的音效库**：覆盖游戏所有操作
- **武将专属语音**：每个武将独特的台词
- **氛围音乐**：营造游戏氛围的背景音乐

### 3. font/ - 字体系统 ⭐⭐⭐

支持中文显示的完整字体系统。

```
font/
├── DroidSansFallback.ttf    # 默认字体 (支持中文)
├── simkai.ttf               # 楷体 (传统风格)
├── simli.ttf                # 隶书 (古典风格)
└── simsun.ttf               # 宋体 (标准字体)
```

**📝 字体特色**：
- **中文支持**：完整的中文字符集
- **多种风格**：适应不同界面需求
- **清晰显示**：优化的字体渲染

### 4. full/ - 武将全身立绘 ⭐⭐⭐⭐

高质量的武将全身立绘资源，提供更丰富的视觉体验。

```
full/
├── 🎨 标准武将立绘
│   ├── 曹操.png              # 各武将的全身立绘
│   ├── 刘备.png              # 高分辨率精美图片
│   ├── 孙权.png              # 完整的人物形象
│   └── ...                   # 300+ 武将立绘
│
├── 🌟 皮肤变体
│   ├── 武将名A.png           # 武将皮肤A版本
│   ├── 武将名B.png           # 武将皮肤B版本
│   └── ...                   # 多套皮肤选择
│
└── 🎭 特殊版本
    ├── SP武将立绘/           # SP版本武将
    ├── 神武将立绘/           # 神势力武将
    └── 限定立绘/             # 限定版本立绘
```

**🖼️ 立绘特色**：
- **高质量图片**：精美的武将全身图
- **多套皮肤**：同一武将的不同风格
- **完整收录**：覆盖所有主要武将

### 5. skins/ - 界面皮肤系统 ⭐⭐⭐⭐

游戏界面的主题和布局配置系统。

```
skins/
├── 🎨 皮肤配置文件
│   ├── defaultSkin.*.json        # 默认皮肤主题
│   ├── compactSkin.*.json        # 紧凑皮肤主题
│   ├── fulldefaultSkin.*.json    # 全屏默认主题
│   └── fullcompactSkin.*.json    # 全屏紧凑主题
│
├── 📋 皮肤管理
│   └── skinList.json             # 皮肤列表配置
│
└── 🎯 主题特色
    ├── 布局配置              # 界面元素位置
    ├── 颜色方案              # 主题色彩搭配
    ├── 动画效果              # 界面动画设置
    └── 字体配置              # 文字显示样式
```

**🎨 皮肤特色**：
- **多套主题**：不同风格的界面设计
- **自适应布局**：支持不同分辨率
- **个性化定制**：可配置的界面元素

### 6. qss/ - 样式表系统 ⭐⭐⭐

Qt样式表文件，控制界面的视觉样式。

```
qss/
├── 🎨 主题样式表
│   ├── default.qss           # 默认主题样式
│   ├── compact.qss           # 紧凑主题样式
│   └── custom.qss            # 自定义样式
│
└── 🎯 样式组件
    ├── 按钮样式              # 按钮的视觉效果
    ├── 对话框样式            # 弹窗的外观设计
    ├── 列表样式              # 列表控件的样式
    └── 输入框样式            # 文本输入的样式
```

**🎨 样式特色**：
- **统一设计**：保持界面风格一致
- **可定制性**：支持样式修改
- **响应式设计**：适应不同界面状态

## 🔧 技术框架支持

### Qt框架插件系统 ⭐⭐⭐

QSanguosha-v2基于Qt框架，需要各种插件支持跨平台运行。

```
Qt插件系统/
├── 🌐 网络支持
│   └── bearer/               # 网络承载插件
│       ├── qgenericbearer.dll   # 通用网络支持
│       ├── qnativewifibearer.dll # WiFi网络支持
│       └── ...                   # 其他网络插件
│
├── 🖼️ 图像处理
│   ├── iconengines/          # 图标引擎
│   │   ├── qsvgicon.dll      # SVG图标支持
│   │   └── ...               # 其他图标引擎
│   │
│   └── imageformats/         # 图像格式支持
│       ├── qjpeg.dll         # JPEG格式支持
│       ├── qpng.dll          # PNG格式支持
│       ├── qgif.dll          # GIF格式支持
│       └── ...               # 其他图像格式
│
├── 🖥️ 平台适配
│   └── platforms/            # 平台插件
│       ├── qwindows.dll      # Windows平台支持
│       ├── qminimal.dll      # 最小化平台支持
│       └── ...               # 其他平台插件
│
├── 🗄️ 数据库支持
│   └── sqldrivers/           # 数据库驱动
│       ├── qsqlite.dll       # SQLite数据库支持
│       └── ...               # 其他数据库驱动
│
├── 🛠️ 开发工具
│   ├── qml1tooling/          # QML调试工具
│   └── translations/         # Qt框架翻译
│
└── 📚 运行库
    ├── Qt5Core.dll           # Qt核心库
    ├── Qt5Gui.dll            # Qt图形界面库
    ├── Qt5Network.dll        # Qt网络库
    ├── Qt5Widgets.dll        # Qt控件库
    └── ...                   # 其他Qt库文件
```

**🔧 技术特色**：
- **跨平台支持**：Windows、Linux、Mac兼容
- **模块化设计**：按需加载功能插件
- **完整生态**：Qt框架的完整支持

## 🛠️ 开发与扩展

### 1. docs/ - 项目文档系统 ⭐⭐⭐⭐

项目的完整文档库，包含开发指南和分析报告。

```
docs/
├── 📚 开发指南
│   ├── QSanguosha-v2卡牌制作完整指南.md    # 卡牌制作教程
│   ├── QSanguosha-v2文件夹构成详细分析.md  # 项目结构分析
│   └── ...                                # 其他开发文档
│
├── 📊 数据分析
│   ├── 卡牌构成统计分析.md                # 卡牌数据统计
│   ├── 武将技能分析.md                    # 技能系统分析
│   └── ...                                # 其他分析报告
│
└── 🔧 技术文档
    ├── API参考文档.md                     # 接口文档
    ├── 扩展包开发指南.md                  # 扩展开发
    └── ...                                # 其他技术文档
```

### 2. diy/ - DIY自定义系统 ⭐⭐⭐

用户自定义内容的支持系统。

```
diy/
├── 🏛️ 势力标识
│   ├── wei-*.png             # 魏势力相关资源
│   ├── shu-*.png             # 蜀势力相关资源
│   ├── wu-*.png              # 吴势力相关资源
│   ├── qun-*.png             # 群势力相关资源
│   └── god-*.png             # 神势力相关资源
│
├── 🎨 自定义资源
│   ├── mask.png              # 遮罩文件
│   ├── 自定义武将图片/        # 用户添加的武将
│   ├── 自定义卡牌图片/        # 用户添加的卡牌
│   └── ...                   # 其他自定义内容
│
└── 🔧 DIY工具
    ├── 资源模板/              # DIY制作模板
    ├── 配置文件/              # DIY配置
    └── 说明文档/              # DIY制作指南
```

### 3. listserver/ - 服务器支持系统 ⭐⭐⭐

游戏联机功能的服务器端支持。

```
listserver/
├── 🌐 Node.js版本
│   ├── nodejs/               # Node.js服务器实现
│   │   ├── server.js         # 服务器主程序
│   │   ├── package.json      # 依赖配置
│   │   └── ...               # 其他服务器文件
│
├── ☁️ SAE版本
│   ├── sae/                  # 新浪云服务器版本
│   │   ├── index.php         # PHP服务器实现
│   │   ├── config.yaml       # SAE配置文件
│   │   └── ...               # 其他SAE文件
│
└── 📖 文档
    ├── README                # 服务器部署说明
    ├── API文档               # 服务器接口文档
    └── 部署指南               # 服务器搭建教程
```

### 4. 其他开发支持目录

```
开发支持/
├── 🐛 调试支持
│   ├── dmp/                  # 崩溃转储文件
│   │   ├── crash_*.dmp       # 程序崩溃记录
│   │   └── debug_info/       # 调试信息
│
├── 📝 原始文档
│   ├── doc/                  # 原始开发文档
│   │   ├── 设计文档/          # 游戏设计文档
│   │   ├── 技术规范/          # 技术标准文档
│   │   └── 历史记录/          # 开发历史记录
│
└── 🖥️ UI开发
    └── ui-script/            # UI脚本文件
        ├── qml文件/          # QML界面脚本
        ├── js脚本/           # JavaScript逻辑
        └── 样式文件/         # 界面样式定义
```

**🔧 开发特色**：
- **完整文档**：详细的开发和使用文档
- **DIY支持**：用户自定义内容的完整支持
- **服务器端**：多平台的服务器解决方案
- **调试工具**：完善的错误诊断和调试支持

## 📦 程序文件

### 核心可执行文件 ⭐⭐⭐⭐⭐

```
程序文件/
├── 🎮 主程序
│   └── QSanguosha.exe        # 游戏主程序
│
├── ⚙️ 配置文件
│   ├── config.ini            # 主配置文件
│   ├── settings.conf         # 用户设置
│   └── ...                   # 其他配置文件
│
├── 📚 Qt运行库 (20+ DLL文件)
│   ├── Qt5Core.dll           # Qt核心库
│   ├── Qt5Gui.dll            # Qt图形界面库
│   ├── Qt5Network.dll        # Qt网络库
│   ├── Qt5Widgets.dll        # Qt控件库
│   ├── Qt5Multimedia.dll     # Qt多媒体库
│   └── ...                   # 其他Qt库
│
└── 🔧 系统库
    ├── msvcp*.dll            # Visual C++运行库
    ├── vcruntime*.dll        # Visual C++运行时
    └── ...                   # 其他系统依赖
```

## 📊 项目规模统计

### 目录数量分布

| 分类 | 目录数 | 文件数估算 | 存储空间 | 重要程度 |
|------|--------|------------|----------|----------|
| **核心游戏系统** | 4个 | 500+ | 50MB | ⭐⭐⭐⭐⭐ |
| **多语言本地化** | 1个 | 100+ | 5MB | ⭐⭐⭐⭐ |
| **视觉媒体资源** | 6个 | 2000+ | 500MB | ⭐⭐⭐⭐⭐ |
| **技术框架支持** | 7个 | 100+ | 100MB | ⭐⭐⭐ |
| **开发与扩展** | 6个 | 200+ | 20MB | ⭐⭐⭐ |
| **程序文件** | - | 50+ | 50MB | ⭐⭐⭐⭐⭐ |
| **总计** | **24个** | **3000+** | **725MB** | - |

### 内容规模统计

| 内容类型 | 数量 | 说明 |
|----------|------|------|
| **武将角色** | 300+ | 包含所有扩展包武将 |
| **武将技能** | 1000+ | 各种技能效果实现 |
| **卡牌种类** | 200+ | 基本牌、锦囊牌、装备牌 |
| **AI脚本** | 50+ | 各扩展包的AI逻辑 |
| **翻译文件** | 100+ | 完整的中文本地化 |
| **图片资源** | 2000+ | 武将、卡牌、界面图片 |
| **音频文件** | 500+ | 音效、语音、背景音乐 |
| **扩展包** | 30+ | 各种游戏内容扩展 |

## 🏗️ 架构设计特点

### 1. 🎯 模块化设计
- **功能分离**：每个模块职责明确，相互独立
- **松耦合**：模块间依赖关系清晰，易于维护
- **可扩展**：新功能可以独立添加，不影响现有系统

### 2. 🌍 国际化架构
- **多语言支持**：完整的本地化框架
- **文化适配**：考虑不同地区的文化差异
- **易于扩展**：添加新语言支持简单便捷

### 3. 🎨 资源管理系统
- **分层组织**：按功能和类型分层管理资源
- **多分辨率**：适配不同屏幕和设备
- **按需加载**：优化内存使用和加载速度

### 4. 🔧 开发者友好
- **文档完整**：详细的开发文档和API说明
- **工具支持**：完善的开发和调试工具
- **社区友好**：支持用户自定义和扩展

### 5. 🚀 性能优化
- **资源优化**：图片、音频资源的合理压缩
- **代码优化**：Lua脚本的高效执行
- **内存管理**：合理的资源加载和释放策略

## 🎯 总结

QSanguosha-v2项目展现了优秀的软件架构设计：

### ✅ 优势特点
1. **架构清晰** - 模块化设计，职责分明
2. **扩展性强** - 支持插件化扩展，易于添加新内容
3. **资源丰富** - 完整的多媒体资源库
4. **技术先进** - 基于Qt框架，跨平台支持
5. **文档完善** - 详细的开发文档和用户指南
6. **社区友好** - 支持DIY和自定义内容

### 🎮 项目价值
- **游戏完整性** - 提供完整的三国杀游戏体验
- **技术示范** - 展示了优秀的游戏架构设计
- **开源贡献** - 为开源游戏开发提供参考
- **学习价值** - 适合学习游戏开发和Qt编程

### 🚀 发展潜力
- **持续更新** - 模块化设计便于持续改进
- **社区发展** - 开放的扩展系统促进社区贡献
- **技术演进** - 良好的架构支持技术升级
- **平台扩展** - 跨平台特性支持多平台发布

QSanguosha-v2不仅是一个优秀的三国杀游戏实现，更是一个展示现代软件工程实践的典型案例，其清晰的架构设计和丰富的功能实现为游戏开发者提供了宝贵的参考价值。
