# SP稀神探女正确实现 - 仅诳言技能

## 📋 技能设计

**诳言**：其他角色的出牌阶段开始时，你可以与其拼点。若你赢，其对你指定的一名角色依此使用拼点牌，将余下的拼点牌当【乐不思蜀】对自身使用。

## 🔧 正确实现代码

### extensions/sp_uranomiya_correct.lua

```lua
-- SP稀神探女正确实现 - 仅诳言技能
module("extensions.sp_uranomiya_correct", package.seeall)
extension = sgs.Package("sp_uranomiya_package")

-- 创建SP稀神探女武将
sp_uranomiya = sgs.General(extension, "sp_uranomiya", "god", 3, false)

-- 诳言技能卡：与其他角色拼点
kuangyan_card = sgs.CreateSkillCard{
    name = "kuangyan",
    target_fixed = false,
    will_throw = false,
    filter = function(self, targets, to_select)
        return #targets == 0 and to_select:objectName() ~= sgs.Self:objectName()
    end,
    on_use = function(self, room, source, targets)
        room:notifySkillInvoked(source, "kuangyan")
        -- 执行拼点，结果处理在Pindian事件中进行
        local success = source:pindian(targets[1], "kuangyan", nil)
        -- 拼点结果的处理在kuangyan_pindian技能中完成
    end
}

-- 诳言视为技能：零牌技能，响应@@kuangyan时可发动
kuangyan = sgs.CreateZeroCardViewAsSkill{
    name = "kuangyan",
    view_as = function()
        return kuangyan_card:clone()
    end,
    enabled_at_play = function(self, player)
        return false
    end,
    enabled_at_response = function(self, player, pattern)
        return pattern == "@@kuangyan"
    end
}

-- 诳言触发技：其他角色出牌阶段开始时触发
kuangyan_trigger = sgs.CreateTriggerSkill{
    name = "#kuangyan_trigger",
    events = {sgs.EventPhaseStart},
    view_as_skill = kuangyan,
    can_trigger = function(self, target)
        return target and target:isAlive() and target:getPhase() == sgs.Player_Play
    end,
    on_trigger = function(self, event, player, data, room)
        -- 其他角色的出牌阶段开始时
        for _, p in sgs.qlist(room:getOtherPlayers(player)) do
            if p:hasSkill("kuangyan") and p:canPindian(player) then
                room:askForUseCard(p, "@@kuangyan", "@kuangyan-pindian:" .. player:objectName())
            end
        end
        return false
    end,
    global = true
}

-- 诳言拼点处理技能：使用Pindian事件获取拼点结果
kuangyan_pindian = sgs.CreateTriggerSkill{
    name = "#kuangyan_pindian",
    events = {sgs.Pindian},
    can_trigger = function(self, target)
        return target ~= nil
    end,
    on_trigger = function(self, event, player, data, room)
        local pindian = data:toPindian()
        
        -- 检查是否是诳言技能的拼点
        if pindian.reason == "kuangyan" then
            local uranomiya = pindian.from  -- 稀神探女
            local target = pindian.to       -- 拼点对象
            
            -- 检查稀神探女是否拼点成功
            if pindian.from_number > pindian.to_number then
                room:sendLog("#KuangyanSuccess", uranomiya, "kuangyan", sgs.QVariant(), target:objectName())
                
                -- 获取拼点牌
                local uranomiya_card = pindian.from_card
                local target_card = pindian.to_card
                
                -- 让稀神探女选择拼点牌的使用目标
                local chosen_target = room:askForPlayerChosen(uranomiya, room:getAlivePlayers(),
                    "kuangyan", "@kuangyan-choose:" .. target:objectName(), false)
                
                if chosen_target then
                    local cards_to_process = {}
                    
                    -- 收集要处理的拼点牌
                    if uranomiya_card and not uranomiya_card:isVirtualCard() then
                        table.insert(cards_to_process, uranomiya_card)
                    end
                    if target_card and not target_card:isVirtualCard() then
                        table.insert(cards_to_process, target_card)
                    end
                    
                    local remaining_cards = {}
                    
                    -- 依次使用拼点牌
                    for _, card in ipairs(cards_to_process) do
                        local can_use = false
                        
                        -- 检查卡牌是否可以使用
                        if card:isAvailable(target) and not target:isCardLimited(card, card:getHandlingMethod()) then
                            local use = sgs.CardUseStruct()
                            use.from = target
                            use.card = card
                            
                            -- 根据卡牌类型设置目标
                            if card:isKindOf("Slash") then
                                -- 杀：需要目标
                                if chosen_target:objectName() ~= target:objectName() or card:targetFilter(sgs.PlayerList(), chosen_target, target) then
                                    use.to:append(chosen_target)
                                    room:useCard(use, false)
                                    can_use = true
                                end
                            elseif card:isKindOf("Peach") then
                                -- 桃：对指定目标使用
                                use.to:append(chosen_target)
                                room:useCard(use, false)
                                can_use = true
                            elseif card:isKindOf("Analeptic") then
                                -- 酒：对指定目标使用
                                use.to:append(chosen_target)
                                room:useCard(use, false)
                                can_use = true
                            elseif card:isKindOf("EquipCard") then
                                -- 装备牌：装备到目标身上
                                use.to:append(chosen_target)
                                room:useCard(use, false)
                                can_use = true
                            elseif card:isKindOf("TrickCard") then
                                -- 锦囊牌处理
                                if card:isKindOf("DelayedTrick") then
                                    -- 延时锦囊
                                    if card:targetFilter(sgs.PlayerList(), chosen_target, target) then
                                        use.to:append(chosen_target)
                                        room:useCard(use, false)
                                        can_use = true
                                    end
                                elseif card:isKindOf("AOE") then
                                    -- 群体锦囊（万箭齐发、南蛮入侵等）
                                    room:useCard(use, false)
                                    can_use = true
                                elseif card:targetFixed() then
                                    -- 无目标锦囊（无中生有等）
                                    room:useCard(use, false)
                                    can_use = true
                                else
                                    -- 单体锦囊
                                    if card:targetFilter(sgs.PlayerList(), chosen_target, target) then
                                        use.to:append(chosen_target)
                                        room:useCard(use, false)
                                        can_use = true
                                    end
                                end
                            end
                        end
                        
                        -- 不能使用的牌加入剩余列表
                        if not can_use then
                            table.insert(remaining_cards, card)
                        end
                    end
                    
                    -- 将剩余的拼点牌当【乐不思蜀】对拼点对象自身使用
                    for _, remaining_card in ipairs(remaining_cards) do
                        local indulgence = sgs.Sanguosha:cloneCard("indulgence", remaining_card:getSuit(), remaining_card:getNumber())
                        indulgence:addSubcard(remaining_card:getId())
                        indulgence:setSkillName("kuangyan")
                        
                        -- 检查是否可以对目标使用乐不思蜀
                        if indulgence:isAvailable(target) and indulgence:targetFilter(sgs.PlayerList(), target, target) then
                            local indulgence_use = sgs.CardUseStruct()
                            indulgence_use.from = target
                            indulgence_use.to:append(target)
                            indulgence_use.card = indulgence
                            room:useCard(indulgence_use, false)
                        end
                    end
                end
            else
                -- 拼点失败
                room:sendLog("#KuangyanFailed", uranomiya, "kuangyan", sgs.QVariant(), target:objectName())
            end
        end
        return false
    end,
    global = true
}

-- 添加技能到武将
sp_uranomiya:addSkill(kuangyan)
sp_uranomiya:addSkill(kuangyan_trigger)
sp_uranomiya:addSkill(kuangyan_pindian)

-- 翻译表
sgs.LoadTranslationTable{
    ["sp_uranomiya_package"] = "SP稀神探女包",
    ["sp_uranomiya"] = "SP稀神探女",
    ["#sp_uranomiya"] = "天矢之巫女",
    ["designer:sp_uranomiya"] = "QSanguosha-v2开发组",
    ["illustrator:sp_uranomiya"] = "东方Project",
    ["cv:sp_uranomiya"] = "无",
    
    -- 诳言技能
    ["kuangyan"] = "诳言",
    [":kuangyan"] = "其他角色的出牌阶段开始时，你可以与其拼点。若你赢，其对你指定的一名角色依此使用拼点牌，将余下的拼点牌当【乐不思蜀】对自身使用。",
    ["@kuangyan-pindian"] = "你可以发动"诳言"与 %src 拼点",
    ["@kuangyan-choose"] = "诳言：请选择 %src 拼点牌的使用目标",
    ["~kuangyan"] = "选择一名角色→点击确定",
    ["$kuangyan1"] = "哼哼，你上当了~",
    ["$kuangyan2"] = "我说的话，可不要全信哦~",
    
    -- 日志信息
    ["#KuangyanSuccess"] = "%from 的"%arg"拼点成功，对 %dest 发动效果",
    ["#KuangyanFailed"] = "%from 的"%arg"拼点失败",
    
    -- 死亡台词
    ["~sp_uranomiya"] = "我...我的箭矢...怎么会...",
}
```

## 🔍 关键技术点解析

### 1. 使用Pindian事件获取拼点结果

```lua
kuangyan_pindian = sgs.CreateTriggerSkill{
    name = "#kuangyan_pindian",
    events = {sgs.Pindian},  -- 关键：监听Pindian事件
    -- ...
    on_trigger = function(self, event, player, data, room)
        local pindian = data:toPindian()
        
        -- 检查是否是诳言技能的拼点
        if pindian.reason == "kuangyan" then
            -- 获取拼点信息
            local uranomiya = pindian.from      -- 稀神探女
            local target = pindian.to           -- 拼点对象
            local uranomiya_card = pindian.from_card  -- 稀神探女的拼点牌
            local target_card = pindian.to_card       -- 对方的拼点牌
            
            -- 检查拼点结果
            if pindian.from_number > pindian.to_number then
                -- 拼点成功，执行技能效果
            end
        end
    end
}
```

### 2. 技能架构设计

```
诳言技能完整架构：
├── kuangyan_card        # 技能卡：执行拼点
├── kuangyan            # 视为技能：响应@@kuangyan
├── kuangyan_trigger    # 触发技能：监听出牌阶段开始
└── kuangyan_pindian    # 隐藏技能：处理Pindian事件 ⭐新增⭐
```

### 3. 拼点信息获取

通过Pindian事件可以获取到：
- `pindian.from` - 拼点发起者（稀神探女）
- `pindian.to` - 拼点目标
- `pindian.from_card` - 稀神探女的拼点牌
- `pindian.to_card` - 目标的拼点牌
- `pindian.from_number` - 稀神探女拼点牌的点数
- `pindian.to_number` - 目标拼点牌的点数
- `pindian.reason` - 拼点的原因（"kuangyan"）

### 4. 技能执行流程

```
1. 其他角色出牌阶段开始 (kuangyan_trigger)
   ↓
2. 询问稀神探女是否发动诳言
   ↓
3. 选择拼点目标，执行拼点 (kuangyan_card)
   ↓
4. Pindian事件触发 (kuangyan_pindian)
   ↓
5. 检查拼点结果，如果成功：
   ├── 选择拼点牌的使用目标
   ├── 依次使用能使用的拼点牌
   └── 剩余牌当乐不思蜀对拼点对象使用
```

## ✅ 修复的关键问题

1. **正确的事件监听**：使用Pindian事件而不是PindianVerifying
2. **准确的拼点信息获取**：直接从pindian数据结构获取所有信息
3. **清晰的技能分离**：拼点执行和结果处理分离到不同技能
4. **完整的卡牌类型处理**：覆盖所有可能的卡牌类型
5. **安全的错误处理**：添加各种有效性检查

这个实现严格按照您的指导，使用Pindian事件来正确获取拼点结果和相关信息，确保技能的准确性和稳定性。
