--hahaha

math.randomseed(tostring(os.time()):reverse():sub(1, 7)) --------设置时间种子


-- player:hasFlag("hasUsedSlash") 是否使用了杀
Pay = require "paysage" --加载价值模组 

sgs.ai_skill_cardask["@lualubiao"] = function(self, data, pattern, target)
	local card_type = data:toString()
	local cards = self.player:getHandcards()
	cards = sgs.QList2Table(cards)
	self:sortByUseValue(cards)
	if not target then 
		target = self.room:findPlayerBySkillName("lualubiao")
	end
	local bool0 = self:hasCrossbowEffect(target) and target:getHandcardNum() > 4
	if not target then return end
	if target:hasFlag("hasUsedSlash") then self.room:writeToConsole("player" .. target:objectName() .. "hasSlashed") end
	local bool1 = (target:hasFlag("hasUsedSlash") and not bool0) or (target:getHandcardNum() == 1 and self.room:getTag("LualubiaoAI") and self.room:getTag("LualubiaoAI"):toInt() == 0)
		or not target:inMyAttackRange(self.player) --表示要牌玩家不能杀我
	local slash = sgs.Sanguosha:cloneCard("slash")	
	local bool2 = (not bool1) and self:hasHeavySlashDamage(target, slash, self.player)
	local bool3 = self:getEnemyNumBySeat(target, self.player, self.player, false, true) >= 3 or (not bool1 and self:getEnemyNumBySeat(target, self.player, self.player, false, true) >= 2) --表示预计还有多少人要杀我
	if target:hasWeapon("GudingBlade") and not bool1 and self.player:getHandcardNum() <= 1 and target:inMyAttackRange(self.player) then 
		return "."
	end 
	local count = 0
	local count2 = 0
	if (self.player:getHandcardNum() < (self.player:getMaxCards() - 1)) then 		
		for _, c in ipairs(cards) do
			if c:isKindOf(card_type) then 
				count = count + 1
			end 
		end		
		if count < 3 and not (card_type == "BasicCard" and bool2) then return "." end 
	end 
	if card_type == "BasicCard" then 
		if target:inMyAttackRange(self.player) and self:slashIsEffective(slash, self.player, target) then
			if bool0 and self:getCardsNum("Peach") <= 2 then	--yun
				return "." --打死不给，反正都是被打死）
			end
			if self:getCardsNum("Jink") < 1 then	--yun and not isCard("Analeptic", card, self.player)
				if self.player:getHp() > 1 and count <= 2 and self:getCardsNum("Peach") <= 1 then 
					return "." --打死不给，反正都是被打死）
				end 			
			elseif self:getCardsNum("Jink") == 1 then 
				if self:getCardsNum("Peach") == 0 and not (self:getCardsNum("Analeptic") > 0 and (self.player:getHp() < 3)) and count <= 2 then return "." end
			end			
		end
		self.room:writeToConsole("kana test")
		for _, c in ipairs(cards) do
			if c:isKindOf(card_type) then
				if (not isCard("Peach", c, target) and not (isCard("Analeptic", c, target) and target:inMyAttackRange(self.player) and not bool1)) or (count2 > 0) then 					
					return "$" .. c:getEffectiveId()
				else
					count2 = count2 + 1
				end 
			end
		end 
	end 
	if card_type == "TrickCard" then 
		if count == 1 then 
			if self.player:getHandcardNum() >= (self.player:getMaxCards() - 1) then
				for _, c in ipairs(cards) do
					if c:isKindOf(card_type) and c:isKindOf("AOE") and self:getAoeValue(c) < 0 then
						return "$" .. c:getEffectiveId()
					end
				end
			end 
			return "."
		else
			for _, c in ipairs(cards) do
				if c:isKindOf(card_type) and not isCard("ExNihilo", c, target) and not isCard("Indulgence", c, target) then
					return "$" .. c:getEffectiveId()
				end
			end			
		end 		
	end 
	if card_type == "EquipCard" then 
		for _, c in ipairs(cards) do
			if c:isKindOf(card_type) then
				return "$" .. c:getEffectiveId()
			end
		end
	end 
	return "."
end


sgs.ai_card_intention.Lualubiao = 40

sgs.ai_card_intention.LuaBian = 200


local LuaBianVS_skill={} -- 初始化 LuaBianVS_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 LuaBianVS_skill 只是为了出于习惯
LuaBianVS_skill.name="LuaBian" -- 设置 name
table.insert(sgs.ai_skills, LuaBianVS_skill) -- 把这个表加入到 sgs.ai_skills 中

LuaBianVS_skill.getTurnUseCard = function(self)  --还没有调整牌序的问题
	self.room:writeToConsole("小町测试2")
	local enermy = self.enemies
	local room = self.room
	local lord = room:getLord()
	local clock_num = self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt()
	local value_1 = 0
	local value_2 = 0
	local value_3 = 0
	local value_4 = 0
	local value_5 = 0
	for _, to in ipairs(enermy) do
		if to:hasSkill("jijiu") and to then
			local RedCard = Pay.GetAllRCardsByPlayer(to)
			if #RedCard > 0 then return end 
		end
		if to:getHandcardNum() > 4 then return end 
		if to:getHandcardNum() == 4 then value_1 = value_1 + 1 end 
		if to:getHandcardNum() >= 3 then value_2 = value_2 + 1 end 
		if value_1 > 1 then return end 
		if value_2 > 1 and (self.player:getMaxHp() == 1) then return end 
		if to:hasSkills("buqu|nosbuqu") then value_3 = value_3 + 1 end 
		if (to:hasSkill("niepan") and to:getMark("@nirvana") > 0) and (self.player:getMaxHp() == 1) then value_4 = value_4 + 1 end 
	end
	for _, to in ipairs(enermy) do
		if to:isWounded() and (to:getHp() < clock_num) and not to:isLord() then 
			if (self.player:getMaxHp() > 1) or (not self.player:isLord()) then 
				value_5 = value_5 + 1 
			end 
		end 
	end 
	if value_5 > 0 and value_5 > value_3 and value_5 > value_4 and not self.player:hasUsed("#LuaBian") then 
		return sgs.Card_Parse("#LuaBian:.:")
	end 
	return
end 

sgs.ai_skill_use_func["#LuaBian"] = function(card, use, self)	
	local enermy = self.enemies
	local clock_num = self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt()
	self:sort(enermy, "defense")
	for _, to in ipairs(enermy) do
		if (not to:hasSkills("buqu|nosbuqu")) and (not ((to:hasSkill("niepan") and to:getMark("@nirvana") > 0) and (self.player:getMaxHp() == 1))) and not to:isLord()
			and (to:getHp() < clock_num)  then 
			use.card = sgs.Card_Parse("#LuaBian:.:") 
			if use.to then use.to:append(to) end
			return -- 在正确设置 use.card 和 use.to 后，务必 return		
		end 
	end 
	
end 
	-- for _,p in sgs.qlist(self.room:getOtherPlayers(current)) do
		-- local card = sgs.Sanguosha:cloneCard("snatch")
		-- if current:isCardLimited(card, sgs.Card_MethodUse, true)then break end
		-- if self.room:isProhibited(current, p, card) or current:distanceTo(p)>1 then continue end
		-- if self:isFriend(p) and (p:containsTrick("indulgence") or p:containsTrick("supply_shortage")) and not p:containsTrick("YanxiaoCard")then
		-- elseif self:isEnemy(p) and not p:isNude()then
		-- else continue end
		-- --return 239--顺
		-- return min_jiaozhao_cards + 9
	-- end
sgs.ai_skill_askforag.Luahuaimeng = function(self, card_ids) --
	for i, card_id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(card_id)
		if card:isKindOf("GodSalvation") and not (self.player:isCardLimited(card, sgs.Card_MethodUse, true) or not card:isAvailable(self.player)) then 
			if self:getAoeValue(card) > 0 then return card_id end  
		end 
	end 
	for i, card_id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(card_id)
		if card:isKindOf("AOE") and not (self.player:isCardLimited(card, sgs.Card_MethodUse, true) or not card:isAvailable(self.player)) then 
			if self:getAoeValue(card) > 30 then return card_id end  
		end 
	end 
	for i, card_id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(card_id)		
		if card:isKindOf("Snatch") or card:isKindOf("Dismantlement") then
			if self.player:isCardLimited(card, sgs.Card_MethodUse, true) then break end
			for _,p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
				if self.room:isProhibited(self.player, p, card) or (self.player:distanceTo(p) > 1 and card:isKindOf("Snatch")) then continue end
				if self:isFriend(p) and (p:containsTrick("indulgence") or p:containsTrick("supply_shortage")) and not p:containsTrick("YanxiaoCard") then return card_id end 
			end 
		end 
	end 
	for i, card_id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(card_id)		
		if card:isKindOf("ExNihilo") then		
			if self.player:isCardLimited(card, sgs.Card_MethodUse, true) then break end
			return card_id 
		end 
	end 
	for i, card_id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(card_id)		
		if card:isKindOf("ExNihilo") then		
			if self.player:isCardLimited(card, sgs.Card_MethodUse, true) then break end
			return card_id 
		end 
	end 
	for i, card_id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(card_id)
		if card:isKindOf("AOE") and not (self.player:isCardLimited(card, sgs.Card_MethodUse, true) or not card:isAvailable(self.player)) then 
			if self:getAoeValue(card) > 0 then return card_id end  
		end 
	end 
	for i, card_id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(card_id)		
		if card:isKindOf("Snatch") or card:isKindOf("Dismantlement") then
			if self.player:isCardLimited(card, sgs.Card_MethodUse, true) then break end
			for _,p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
				if self.room:isProhibited(self.player, p, card) or (self.player:distanceTo(p) > 1 and card:isKindOf("Snatch")) then continue end
				if self:isEnemy(p) and not p:isNude() then return card_id end 
			end 
		end 
	end 
	for i, card_id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(card_id)
		for _,p in ipairs(self.enemies) do
			if self:isWeak(p) and self:hasTrickEffective(card, p, self.player) and self:damageIsEffective(p, sgs.DamageStruct_Normal, self.player) then
				return card_id
			end
		end	
	end 
	local abc = {}
	for i, card_id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(card_id)
		if not self.player:isCardLimited(card, sgs.Card_MethodUse, true) then table.insert(abc, card) end 
	end 
	self:sortByUseValue(abc)
	return abc[1]:getId()
end

local Lualubiao_skill={} -- 初始化 LuaBianVS_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 LuaBianVS_skill 只是为了出于习惯
Lualubiao_skill.name="Lualubiao" -- 设置 name
table.insert(sgs.ai_skills, Lualubiao_skill) -- 把这个表加入到 sgs.ai_skills 中

Lualubiao_skill.getTurnUseCard = function(self)  
	if self.player:hasUsed("#Lualubiao") then return false end 
	if #self.enemies == 0 then return false end 
	return sgs.Card_Parse("#Lualubiao:.:")
end 
sgs.ai_skill_use_func["#Lualubiao"] = function(card, use, self)
	local handcards = self.player:getHandcards()
	local enemy2 = self.enemies
	local enemy3 = self.enemies
	self:sort(enemy2, "defense")
	if #enemy2 == 0 or #enemy3 == 0 then return end 
	local allcards = {}
	local allcards_0 = {}
	local allcards_1 = {}
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子 local x = to:getMaxCards() - to:getHandcardNum()
		table.insert(allcards, c)
	end
	for _, c in ipairs(allcards) do	
		if c:isKindOf("TrickCard") then table.insert(allcards_1, c) end 
		if c:isKindOf("EquipCard") then table.insert(allcards_0, c) end 
	end 
	for _, to in ipairs(enemy2) do			
		if #allcards_1 > 0 and ((to:getHandcardNum() == 4 and math.random() > 0.5) or to:getHandcardNum() > 4) then 
			local card = allcards_1[math.random(1, #allcards_1)]
			use.card = sgs.Card_Parse("#Lualubiao:" .. card:getEffectiveId() ..":") 
			if use.to then use.to:append(to) end
			return
		end 
	end 
	for _, to in ipairs(enemy2) do	
		local x = to:getMaxCards() - to:getHandcardNum()	
		if #allcards_0 > 0 and to:getEquips() and (x < to:getEquips():length()) and (to:getEquips():length() > 1) then 
			local card = allcards_0[math.random(1, #allcards_0)]
			use.card = sgs.Card_Parse("#Lualubiao:" .. card:getEffectiveId() ..":") 
			if use.to then use.to:append(to) end
			return
		end 
	end 
	for _, to in ipairs(enemy2) do			
		if #allcards_0 > 0 then 
			for _, equip in sgs.qlist(to:getEquips()) do
				if (self:evaluateArmor(card, to) <= 4.5) then 
					local card = allcards_0[math.random(1, #allcards_0)]
					use.card = sgs.Card_Parse("#Lualubiao:" .. card:getEffectiveId() ..":") 
					if use.to then use.to:append(to) end
					return	
				end 
			end 
		end 
	end 
	self:sort(enemy3, "handcard2")
	if (enemy3[1]:getHandcardNum() >= 4 and math.random() > 0.4) or (enemy3[1]:getHandcardNum() >= 5) then enemy2 = enemy3 end
	for _, to in ipairs(enemy2) do			
		local card = allcards[math.random(1, #allcards)]
		use.card = sgs.Card_Parse("#Lualubiao:" .. card:getEffectiveId() ..":") 
		if use.to then use.to:append(to) end
		return
	end 	
end 
sgs.ai_use_priority.Lualubiao = 10

sgs.ai_playerchosen_intention["luaniluanz"] = function(self, from, to)
	local playerf = self.room:getCurrent()
	local x = playerf:getMark("@luatianshii")
	if x > 1 then
		sgs.updateIntention(from, to, 30)
	end 
end

sgs.ai_choicemade_filter.skillChoice.luatianshi = function(self, player, promptlist)
	local choice = promptlist[#promptlist]
	local target = self.room:getTag("luatianshkTarget")
	if (not target) or (not target:toPlayer()) or (not target:toPlayer():objectName()) then return end  
	target = target:toPlayer()
	if choice == "luatianshk" then 
		if not self:findLeijiTarget(target, 50, self.player) then sgs.updateIntention(player, target, 30) end 
	else
		local mur 
		for _, to in sgs.qlist(self.room:getAllPlayers()) do
			if to:hasFlag("tianshira" .. target:objectName()) then 
				mur = to
			end 
		end 	
		if not self:findLeijiTarget(mur, 50, self.player) then sgs.updateIntention(player, mur, 30) end 
	end
end 

sgs.ai_skill_use["@@luaniluan"] = function(self, prompt)  --没有主动触发雷击的手段/控顶时没有考虑一个良好的【杀】指定对象 
	local playerf = self.room:getCurrent()

	local next_player --爷爷能控顶，明白吗？
	next_player = playerf:getNextAlive()
	if not next_player:faceUp() then next_player = next_player:getNextAlive() end
	self.room:writeToConsole("探女测试X" .. next_player:getGeneralName())

	local x = playerf:getMark("@luatianshii")
	local canTS = (self.player:getMark("luatianshit") ~= self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt()) and self.player:hasSkill("luatianshi")
	local toUse 
	local toUses = {}
	local jink = sgs.Sanguosha:cloneCard("jink")
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(cards)
	for _, card in ipairs(cards) do
		if self:getUseValue(card) < self:getUseValue(jink) then table.insert(toUses, card) end
		if self:isFriend(next_player) and card:isKindOf("Peach") then table.insert(toUses, card) end
	end 
	if #toUses > 0 then toUse = toUses[1] end 
	if not toUse then 
		local jinjs = self:getCards("Jink")
		if (#jinjs > 1) or ((#jinjs == 1) and not self.player:isWounded()) then
			self:sortByKeepValue(jinjs)
			toUse = jinjs[1]
		end 
	end 
	if not toUse then return "." end 
	local xa = #self.friends
	local xb = #self.enemies
	local xc = self.room:getAlivePlayers():length() - xa - xb	
	
	local enr 
	local enemy2 = self.enemies
	self:sort(enemy2, "defenseSlash")
	for _, to in ipairs(enemy2) do	
		if self:YouMu2(to, true, playerf) then enr = to; break end
	end 
	if #enemy2 == 0 then return "." end 


	--if self:isFriend(playerf) then --指挥队友杀明敌
	if not toUse:isKindOf("Slash") then
		for _, toUsex in ipairs(toUses) do
			if toUsex:isKindOf("Slash") and canTS then toUse = toUsex; break end
		end
	end
	if (enr and (enr:getHp() <= 1)) or (x > 4 and canTS) or (toUse:isKindOf("Slash") and canTS) then
		if toUse:isKindOf("Slash") then self.room:setPlayerFlag(self.player, "niluanSOperator") end
		return "#luaniluan:".. toUse:getId() ..":"
	end
	if self:isEnemy(playerf) and ((x == 1) or (x > 4) or (toUse:isKindOf("Slash") and canTS)) then

		if (xb >= 2) or (xc > 0) then
			if enr and (toUse:isKindOf("Slash")) then
				if toUse:isKindOf("Slash") then self.room:setPlayerFlag(self.player, "niluanSOperator") end
				return "#luaniluan:".. toUse:getId() ..":"
			end
		end
	end
	if next_player:isLord() then if toUse:isKindOf("Slash") then self.room:setPlayerFlag(self.player, "niluanSOperator") end;return "#luaniluan:".. toUse:getId() ..":" end
	local judge
	if self:isFriend(next_player) and next_player:getJudgingArea() and (next_player:getJudgingArea():length() > 0)
		and (not next_player:hasSkill("guanxing")) and (self:getFinalRetrial(next_player) == 0) and (not next_player:containsTrick("YanxiaoCard"))
		and ((xb >= 2) or (xc > 0) or (self:getCardsNum("Jink") >= 1)) then
			judge = sgs.QList2Table(next_player:getJudgingArea())
			judge = sgs.reverse(judge)
			if judge[1]:isKindOf("SupplyShortage") then
				
				local toUse2 
				local exh = sgs.Sanguosha:cloneCard("ex_nihilo")
				local exhv = self:getUseValue(exh)
				local peh = sgs.Sanguosha:cloneCard("peach")
				local pev = self:getKeepValue(peh, true, true)
				for _, card in ipairs(cards) do
					if (self:getUseValue(card) <= exhv) and (self:getKeepValue(card) < pev) and card:getSuit() == sgs.Card_Club then toUse2 = toUsex; break end
				end 
				if toUse2 then 
					if toUse2:isKindOf("Slash") then self.player:setFlags("niluanSOperator") end 
					self.player:setFlags("luaniluanOperator")
					return "#luaniluan:".. toUse2:getId() ..":" 
				end 
			elseif judge[1]:isKindOf("Indulgence") then 
				local toUse2 
				local exh = sgs.Sanguosha:cloneCard("ex_nihilo")
				local exhv = self:getUseValue(exh)
				local peh = sgs.Sanguosha:cloneCard("peach")
				local pev = self:getKeepValue(peh, true, true)
				for _, card in ipairs(cards) do
					if (self:getUseValue(card) <= exhv) and (self:getKeepValue(card) < pev) and card:getSuit() == sgs.Card_Heart then toUse2 = toUsex; break end
				end 
				if toUse2 then
					if toUse2:isKindOf("Slash") then self.player:setFlags("niluanSOperator") end 
					self.player:setFlags("luaniluanOperator")
					return "#luaniluan:".. toUse2:getId() ..":" 
				end 			
			end 
		elseif self:isEnemy(next_player) and next_player:getJudgingArea() and (next_player:getJudgingArea():length() > 0)
			and not next_player:hasSkill("guanxing") and (self:getFinalRetrial(next_player) == 0) and (not next_player:containsTrick("YanxiaoCard")) then 
			judge = sgs.QList2Table(next_player:getJudgingArea())
			judge = sgs.reverse(judge)
			if judge[1]:isKindOf("Lightning") then
				local toUse2 
				for _, card in ipairs(cards) do
					if (self:getUseValue(card) <= exh) and (self:getKeepValue(card) < pev) 
						and card:getSuit() == sgs.Card_Spade and card:getNumber() >= 2 and card:getNumber() <= 9 then toUse2 = toUsex; break end 
				end 
				if toUse2 then 
					if toUse2:isKindOf("Slash") then self.player:setFlags("niluanSOperator") end 
					self.player:setFlags("luaniluanOperator")
					return "#luaniluan:".. toUse2:getId() ..":" 
				end 					
			end 
			
		end 
	--end 
	
	
end 


sgs.ai_skill_playerchosen.luaniluan = function(self, targets)
	local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
	slash:setSkillName("luaniluan")
				
	local enemy2 = self.enemies
	self:sort(enemy2, "defenseSlash")
	for _, to in ipairs(enemy2) do	
		if not (self:hasEightDiagramEffect(to) and (self.player:hasFlag("luaniluanOperator") or self.player:hasFlag("niluanSOperator"))) then 
			if targets:contains(to) and self:slashIsEffective(slash, to, self.room:getCurrent()) then return to end 
		end 
	end 
	
	for _, to in ipairs(enemy2) do	
		if targets:contains(to) and self:slashIsEffective(slash, to, self.room:getCurrent())  then return to end 
	end 
	
	local players = sgs.QList2Table(self.room:getAllPlayers())
	self:sort(players, "defense")
	for _, to in ipairs(players) do	
		if targets:contains(to) and not self:isFriend(to) and self:slashIsEffective(slash, to, self.room:getCurrent())  then return to end 
	end 
	
	local friend = self.friends
	self:sort(self.friends, "defenseSlash")
	for _, to in ipairs(friend) do	
		if targets:contains(to) then return to end 
	end 
end 

sgs.ai_skill_use["@@luatianshi"] = function(self, prompt) 
	local playerf = self.room:getCurrent()
	local x = playerf:getMark("@luatianshii")
	if self.player:getMark("luatianshit") == self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt() then return "." end
	local xo = self.room:getLord():getMark("@clock_time")
	local froms = sgs.SPlayerList()
	for _, from in sgs.qlist(self.room:getAllPlayers()) do
		if from:hasFlag("luatianshira") then
			froms:append(from)
		end
	end

	for _, from in sgs.qlist(froms) do
		if self.player:hasFlag("niluanSOperator") and (x ~= 3) and not ((x > 3) and self:isFriend(from))
			and not ((x < 3) and self:isFriend(playerf)) then return "#luatianshi:.:"  end
	end

	for _, from in sgs.qlist(froms) do
		if (x >= 4) and self:isEnemy(from) then return "#luatianshi:.:"  end
	end 
	if (x <= 1) and self:isEnemy(playerf) then return "#luatianshi:.:"  end
	return "."
end 

sgs.ai_skill_playerchosen.luatianshi = function(self, targets)
	local playerf = self.room:getCurrent()
	local x = playerf:getMark("@luatianshii")
	local froms = sgs.SPlayerList()
	local tos = sgs.SPlayerList()
	local acf = false
	for _, from in sgs.qlist(self.room:getAllPlayers()) do	
		if from:hasFlag("luatianshira") then
			froms:append(from)
		end
	end 

	for _, to in sgs.qlist(self.room:getAllPlayers()) do	
		for _, from in sgs.qlist(froms) do	
			if to:hasFlag("tianshira" .. from:objectName()) then
				tos:append(to)
			end
		end 
	end

	for _, to in ipairs(tos) do
		if self:isEnemy(to) then acf = true; break end 
	end  
	
	if x > 4 then 
		froms = sgs.QList2Table(froms)
		self:sort(froms, "defense")
		return froms[1]
	elseif acf then 
		tos = sgs.QList2Table(tos)
		self:sort(tos, "defense")
		return tos[1]	
	else
		froms = sgs.QList2Table(froms)
		self:sort(froms, "defense")
		return froms[1]		
	end 	
end

sgs.ai_skill_choice.luatianshi = function(self, choices)
	--local items = choices:split("+")

	local playerf = self.room:getCurrent()
	local x = playerf:getMark("@luatianshii")
	local target = self.room:getTag("luatianshkTarget"):toPlayer()
	local tos = sgs.SPlayerList()
	for _, to in sgs.qlist(self.room:getAllPlayers()) do	
		if to:hasFlag("tianshira" .. target:objectName()) then
			tos:append(to)
		end
	end 
	if self:isEnemy(target) and self:isEnemy(tos:at(0)) then 
		local enemy2 = self.enemies
		self:sort(enemy2, "defense")
		for _, tr in ipairs(enemy2) do	
			if tr:objectName() == target:objectName() then return "luatianshk" end 
			if tr:objectName() == tos:at(0):objectName() then return "luatianshj" end 
		end 
	elseif self:isEnemy(target) then return "luatianshk" 
	elseif self:isEnemy(tos:at(0)) then return "luatianshj" 
	end 
end 

local function baquX(i,player) 
	local q = player:getTag("Luabaqu_".. tostring(i)) 
	local room = player:getRoom()
	
	if not q then return false end 
	if not q:toPlayer() then return false end 
	return q:toPlayer() 
end 

local Luabaqu_skill = {}
Luabaqu_skill.name = "Luabaqu"
table.insert(sgs.ai_skills, Luabaqu_skill)

local function findbaqucard(self, cardo, bool) --cardo是本回合预计要使用的牌,bool表示就算把本回合要用的牌开了也要开掉
	local i = #self.enemies
	local j = #self.friends_noself
	local k = self.room:getAlivePlayers():length() - i - j - 1
	
	
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByUseValue(cards, true)
	local tousea = {} --是本回合已用过花色的牌且不在预计本回合要使用的牌之内
	local touseb = {} --是本回合已用过花色的牌
	local function notuseful(card)
		if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield")) and self:isWeak() then return false end 
		if card:isKindOf("Weapon") then return false end 
		if card:isKindOf("TrickCard") then 
			if card:isKindOf("GodSalvation") or card:isKindOf("AmazingGrace") then return false end 
			if card:isKindOf("AOE") and ((j > 0) or (k > 0)) then return false end --大哥，这么好的牌别浪费
			if card:isKindOf("IronChain") then 
				for _, friend in ipairs(self.friends) do
					if friend:hasSkill(sgs.shuxin_skill) then return false end 
				end 				
			else
				if card:isKindOf("Duel") then return false end 
				local dummy_use = {isDummy = true}
				self:useTrickCard(card, dummy_use)
				if dummy_use.card then return false end 				
			end 
		end 
		if card:isKindOf("Peach") then return false end 
		if card:isKindOf("Slash") and table.contains(cardo, card) then return false end
		local sakura = self:getCards("sakura")	
		local rp = self:getCardsNum("Jink") + #sakura
		if card:isKindOf("Jink") and rp == 1 then return false end
		if card:isKindOf("Analeptic") and self:getCardsNum("Analeptic") == 1 then return false end
		if card:isKindOf("sakura") then return false end 
		return true
	end 
	if self.player:hasFlag("Luafengmi_Spade") then 
		for _,c in ipairs(cards) do	--yun
			if c:getSuit() == sgs.Card_Spade and notuseful(c) then 
				if not table.contains(cardo, c) then table.insert(tousea, c) end 
				table.insert(touseb, c)
			end 
		end 
	end 
	if self.player:hasFlag("Luafengmi_Diamond") then 
		for _,c in ipairs(cards) do	
			if c:getSuit() == sgs.Card_Diamond and notuseful(c) then 
				if not table.contains(cardo, c) then table.insert(tousea, c) end 
				table.insert(touseb, c)
			end 
		end 
	end 
	if self.player:hasFlag("Luafengmi_Club") then 
		for _,c in ipairs(cards) do	
			if c:getSuit() == sgs.Card_Club and notuseful(c) then 
				if not table.contains(cardo, c) then table.insert(tousea, c) end 
				table.insert(touseb, c)
			end 
		end 
	end 
	if self.player:hasFlag("Luafengmi_Heart") then 
		for _,c in ipairs(cards) do	
			if c:getSuit() == sgs.Card_Heart and notuseful(c) then 
				if not table.contains(cardo, c) then table.insert(tousea, c) end 
				table.insert(touseb, c)
			end 
		end 
	end 
	if #tousea > 0 then return tousea[1] end 
	if #touseb > 0 then return touseb[1] end 
	for _,c in ipairs(cards) do	
		if notuseful(c) then 
			if not table.contains(cardo, c) then return c end 
		end 
	end 	
	if bool then 
		for _,c in ipairs(cards) do	
			if not table.contains(cardo, c) then return c end 
		end 	
	end 
	return
end 
Luabaqu_skill.getTurnUseCard = function(self)
	
	if not self.player:hasSkill("Luafengmi") then return end 
	if self.player:isNude() or (self.player:getHandcardNum() >= 8) then return end 
	local cards = self:getTurnUse(true)
	
	local arta = findbaqucard(self, cards, true) 
	local artb = findbaqucard(self, cards, false) 
	
	local death = self:canKillEnermyAtOnce(cards)
	local ppg = #cards
	for _, acard in sgs.qlist(self.player:getHandcards()) do
		if acard:isKindOf("Analeptic") and sgs.Analeptic_IsAvailable(self.player, acard) and not table.contains(cards, acard) then table.insert(cards, acard) end 
		if acard:isKindOf("Lightning") and sgs.Analeptic_IsAvailable(self.player, acard) and not table.contains(cards, acard) then table.insert(cards, acard) end 
		--if acard:isKindOf("FireAttack") and sgs.Analeptic_IsAvailable(self.player, acard) and not table.contains(cards, acard) then table.insert(cards, acard) end 商榷
	end 
	local toslash = false 
	for _,c in ipairs(cards) do	
		if c:isKindOf("Slash") then toslash = true end 
	end 

	local toaoe = false 
	for _,c in ipairs(cards) do	
		if c:isKindOf("GodSalvation") or c:isKindOf("AmazingGrace") then toaoe = true end 
		if c:isKindOf("AOE") and (self:getAoeValue(c) > 35) then toaoe = true end --大哥，这么好的牌别浪费
	end 
	
	-- self:sortByDynamicUsePriority(cards)
	-- for _,c in ipairs(cards) do	
		-- self.room:writeToConsole("射命丸文测试" .. c:objectName())
	-- end 
	
	local count1 = 0
	local count2 = 0
	local count3 = 0
	local count4 = 0
	
	local count5 = 0
	local count6 = 0
	local count7 = 0
	local count8 = 0
	
	for _,c in ipairs(cards) do	
		if c:getSuit() == sgs.Card_Heart and count1 == 0 then 
			count1 = count1 + 1
		end 	
		if c:getSuit() == sgs.Card_Diamond and count2 == 0 then 
			count2 = count2 + 1
		end 
		if c:getSuit() == sgs.Card_Club and count3 == 0 then 
			count3 = count3 + 1
		end 
		if c:getSuit() == sgs.Card_Spade and count4 == 0 then 
			count4 = count4 + 1
		end 		
	end 
	
	if self.player:hasFlag("Luafengmi_Heart") then count5 = count5 + 1 end 
	if self.player:hasFlag("Luafengmi_Diamond") then count6 = count6 + 1 end 
	if self.player:hasFlag("Luafengmi_Club") then count7 = count7 + 1 end 
	if self.player:hasFlag("Luafengmi_Spade") then count8 = count8 + 1 end 
	
	local function pay(a ,b)
		if a == 0 and b == 0 then return 0 end 
		return 1
	end 
	
	local count = pay(count1 ,count5) + pay(count2 ,count6) + pay(count3 ,count7) + pay(count4 ,count8)
	local countx = count5 + count6 + count7 + count8
	local county = count1 + count2 + count3 + count4
	
	if count == 4 then return end 
	if toaoe then return end 
	if death then return end 
	local targets = sgs.SPlayerList()
	if not baquX(1, self.player) then return end 
	if (self.player:getMark("drank") > 0) and toslash then return end 
	
	if countx == 4 and (self:getOverflow() > 0) and artb then return sgs.Card_Parse("#Luabaqu:.:") end 
	if ppg == 0 and (((count > 2)) or self:getOverflow() > 0) then return sgs.Card_Parse("#Luabaqu:.:") end 
	if not arta then return end 
	if count < 3 and not artb then return end 
	if countx == 0 and (self.player:getHandcardNum() <= 6) then 
		if ((((self.player:getHandcardNum() == 3) and math.random() > 0.65)) or self.player:getHandcardNum() < 3) then return end 
		return sgs.Card_Parse("#Luabaqu:.:")
	end 
	if countx == 1 then 
		if count == 3 then return sgs.Card_Parse("#Luabaqu:.:") end 
		if (((self.player:getHandcardNum() == 5) and math.random() > 0.65) or self.player:getHandcardNum() > 6) and ppg ~= 0 then return end 	
		return sgs.Card_Parse("#Luabaqu:.:")
	end 
	
	return sgs.Card_Parse("#Luabaqu:.:")
end
sgs.ai_skill_use_func["#Luabaqu"] = function(cardS, use, self)
	local cardw = sgs.QList2Table(self.player:getHandcards())
	self:sortByUseValue(cardw, true)
	local cards = self:getTurnUse(true)
	local card = findbaqucard(self, cards, false)
	if not card then card = findbaqucard(self, cards, true) end 
	if not card then card = cardw[1] end 
	
	use.card = sgs.Card_Parse("#Luabaqu:" .. card:getEffectiveId()..":")
	if use.to then use.to:append(self.player); return end
end 

sgs.ai_use_priority.Luabaqu = 15

sgs.ai_skill_invoke.Luabaqu = function(self, data)
	local targets = sgs.SPlayerList()
	if baquX(1, self.player) then 
		local i = 1
		while baquX(i, self.player) do
			local target = baquX(i, self.player) 
			if target then 
				targets:append(target)
			end 
			i = i + 1
		end		
	end 
	
	local iff = false
	local iffi = false
	local ife = false
	local ifei = false
	for _, p in sgs.qlist(targets) do
		if self:isFriend(p) then
			if p:isChained() then iffi = true end  
			iff = true
		end 
		if self:isEnemy(p) then 
			if not p:isChained() then ifei = true end  
			ife = true
		end 
	end 
	
	local cardX = self.room:getTag("LuabaquTC"):toCard()
	local tof = cardX:isKindOf("Peach") or cardX:isKindOf("Analeptic") or cardX:isKindOf("ExNihilo") or cardX:isKindOf("AmazingGrace")
		or cardX:isKindOf("GodSalvation") or cardX:isKindOf("GodSalvation") or (iffi and cardX:isKindOf("ExNihilo")) or (cardX:isKindOf("EquipCard") and not cardX:isKindOf("GaleShell"))

		
	if (tof and iff) then return true end 
	if (not tof) and ife then return true end 
	return false 
end 

sgs.ai_skill_invoke.Luafengmi = function(self, data)
	return true 
end 

sgs.ai_skill_playerchosen.Luabaqu = function(self, targetS)
	local targets = sgs.SPlayerList()
	if baquX(1, self.player) then 
		local i = 1
		while baquX(i, self.player) do
			local target = baquX(i, self.player) 
			if target then 
				targets:append(target)
			end 
			i = i + 1
		end		
	end 
	
	local iffi = false
	for _, p in sgs.qlist(targets) do
		if self:isFriend(p) then
			if p:isChained() then iffi = true end  
		end 
	end 	
	local cardX = self.room:getTag("LuabaquTC"):toCard()
	local tof = cardX:isKindOf("Peach") or cardX:isKindOf("Analeptic") or cardX:isKindOf("ExNihilo") or cardX:isKindOf("AmazingGrace")
		or cardX:isKindOf("GodSalvation") or cardX:isKindOf("GodSalvation") or (iffi and cardX:isKindOf("IronChain")) or (cardX:isKindOf("EquipCard") and not cardX:isKindOf("GaleShell"))
	if tof then 
		self:sort(self.friends, "defense")
		if cardX:isKindOf("IronChain") then 
			for _, friend in ipairs(self.friends_noself) do
				if friend:isChained() then return friend end 
			end 	
		end 
		for _, friend in ipairs(self.friends_noself) do
			if targets:contains(friend) then return friend end 
		end 
	else
		local alives = sgs.QList2Table(self.room:getAllPlayers())
		self:sort(alives, "defense")
		if cardX:isKindOf("IronChain") then 
			for _, p in ipairs(alives) do
				if not p:isChained() then return p end 
			end 	
		end 
		for _, p in ipairs(alives) do
			if targets:contains(p) then return p end 
		end 
	end 
end
sgs.ai_skill_turnUse["Luafengmi"] = function(self, turnUse)
	if self.player:hasSkill("Luafengmi") then
		local i = #self.enemies
		local j = #self.friends_noself
		local k = self.room:getAlivePlayers():length() - i - j - 1
		local aya_turnUse = {} --射命丸文的最后调整
		for _, card in ipairs(turnUse) do
			if not (card:isKindOf("GodSalvation") or card:isKindOf("AmazingGrace"))
					and not (card:isKindOf("AOE") and ((j > 0) or (k > 0))) then
				table.insert(aya_turnUse, card)
			end
		end
		if ayaadjust(self, aya_turnUse) then
			turnUse = aya_turnUse
		end
		for _ ,c in sgs.qlist(self.player:getHandcards()) do
			if ((c:isKindOf("GodSalvation") or c:isKindOf("AmazingGrace")) or (c:isKindOf("AOE") and ((j > 0) or (k > 0)))) and ayaadjust(self, turnUse, true) then
				if not table.contains(turnUse, c) then table.insert(turnUse, c)	end
			end
		end
		return turnUse
	end
end



local function useWhatCard(self, subcards)
	local function AddSubcards(card)
		for _, card_0 in ipairs(subcards) do
			card:addSubcard(card_0:getId())
		end
	end
	local slash = {}
	slash[1] = "fire_slash"
	slash[2] = "thunder_slash"
	slash[3] = "slash"

	local aoe = {}
	aoe[1] = "archery_attack"
	aoe[2] = "savage_assault"


	for i = 1, 2 do
		local trick2 = sgs.Sanguosha:cloneCard(aoe[i], sgs.Card_SuitToBeDecided, 10)
		AddSubcards(trick2)
		if self:getAoeValue(trick2, self.player) >= 80 then
			return aoe[i]
		end
	end

	local trick9 = sgs.Sanguosha:cloneCard("god_salvation", sgs.Card_SuitToBeDecided, 10)
	AddSubcards(trick9)
	local value_o = self:godSalvationValue(trick9)
	self.room:writeToConsole(value_o)
	if (value_o > 25) then
		return "god_salvation"
	end

	local basic1 = sgs.Sanguosha:cloneCard("peach", sgs.Card_SuitToBeDecided, 10)
	AddSubcards(basic1)
	if (self.player:getLostHp() > 1) then
		local dummyuse = { isDummy = true }
		self:useBasicCard(basic1, dummyuse)
		if dummyuse.card then
			return "peach"
		end
	end

	local trick = sgs.Sanguosha:cloneCard("duel", sgs.Card_SuitToBeDecided, 10)
	AddSubcards(trick)
	if self:ceshi(trick, false, true) then return "duel" end

	local trick8 = sgs.Sanguosha:cloneCard("ex_nihilo", sgs.Card_SuitToBeDecided, 10)
	AddSubcards(trick8)
	if trick8:isAvailable(self.player) then return "ex_nihilo" end

	local trick90 = sgs.Sanguosha:cloneCard("god_salvation", sgs.Card_SuitToBeDecided, 10)
	AddSubcards(trick90)
	local value_oo = self:godSalvationValue(trick90)
	self.room:writeToConsole(value_oo)
	if (value_oo > 15) then
		return "god_salvation"
	end

	local basic11 = sgs.Sanguosha:cloneCard("peach", sgs.Card_SuitToBeDecided, 10)
	AddSubcards(basic11)
	if (self.player:getLostHp() == 1) then
		local dummyuse = { isDummy = true }
		self:useBasicCard(basic11, dummyuse)
		if dummyuse.card then
			return "peach"
		end
	end

	local trick2 = sgs.Sanguosha:cloneCard("snatch", sgs.Card_SuitToBeDecided, 10)
	AddSubcards(trick2)
	if self:ceshi(trick2, true) then return "snatch" end

	for i = 1, 2 do
		local trick20 = sgs.Sanguosha:cloneCard(aoe[i], sgs.Card_SuitToBeDecided, 10)
		AddSubcards(trick20)
		if self:getAoeValue(trick20, self.player) >= 60 then
			return aoe[i]
		end
	end

	for i = 1, 3 do
		local basic2 = sgs.Sanguosha:cloneCard(slash[i], sgs.Card_SuitToBeDecided, 10)
		AddSubcards(basic2)
		if self:YouMu() then
			local dummyuse = { isDummy = true }
			self:useBasicCard(basic2, dummyuse)
			if dummyuse.card then
				return slash[i]
			end
		end
	end

	local trick3 = sgs.Sanguosha:cloneCard("dismantlement", sgs.Card_SuitToBeDecided, 10)   --
	AddSubcards(trick3)
	if self:ceshi(trick3, true) then return "dismantlement" end

	local trick4 = sgs.Sanguosha:cloneCard("snatch", sgs.Card_SuitToBeDecided, 10)
	AddSubcards(trick4)
	if self:ceshi(trick4) then return "snatch" end

	local trick5 = sgs.Sanguosha:cloneCard("duel", sgs.Card_SuitToBeDecided, 10)
	AddSubcards(trick5)
	if self:ceshi(trick5) then return "duel" end

	local trick6 = sgs.Sanguosha:cloneCard("indulgence", sgs.Card_SuitToBeDecided, 10)
	AddSubcards(trick6)
	if self:ceshi(trick6, false, false, true) and #subcards == 1 then return "indulgence" end

	for i = 1, 2 do
		local basic2 = sgs.Sanguosha:cloneCard(slash[i], sgs.Card_SuitToBeDecided, 10)
		AddSubcards(basic2)
		local dummyuse = { isDummy = true, to = sgs.SPlayerList() }
		self:useBasicCard(basic2, dummyuse)
		if dummyuse.card and not dummyuse.to:isEmpty() then
			for _, p in sgs.qlist(dummyuse.to) do
				if self:isGoodChainTarget(p, self.player, nil, nil, dummyuse.card) then
					return slash[i]
				end
			end
		end
	end

	for i = 1, 3 do
		local basic2 = sgs.Sanguosha:cloneCard(slash[i], sgs.Card_SuitToBeDecided, 10)
		AddSubcards(basic2)
		local dummyuse = { isDummy = true, to = sgs.SPlayerList() }
		self:useBasicCard(basic2, dummyuse)
		if dummyuse.card and not dummyuse.to:isEmpty() then
			for _, p in sgs.qlist(dummyuse.to) do
				if self:hasHeavySlashDamage(self.player, damage.card, p) then
					return slash[i]
				end
			end
		end
	end

	local trick7 = sgs.Sanguosha:cloneCard("supply_shortage", sgs.Card_SuitToBeDecided, 10)
	AddSubcards(trick7)
	if self:ceshi(trick7) and #subcards == 1 then return "supply_shortage" end

	for i = 1, 2 do
		local trick23 = sgs.Sanguosha:cloneCard(aoe[i], sgs.Card_SuitToBeDecided, 10)
		AddSubcards(trick23)
		if self:getAoeValue(trick23, self.player) >= 40 then
			return aoe[i]
		end
	end

	local basic12 = sgs.Sanguosha:cloneCard("peach", sgs.Card_SuitToBeDecided, 10)
	AddSubcards(basic12)
	if (self.player:getLostHp() > 1) then
		local dummyuse = { isDummy = true }
		self:useBasicCard(basic12, dummyuse)
		if dummyuse.card then
			return "peach"
		end
	end

	local trick88 = sgs.Sanguosha:cloneCard("ex_nihilo", sgs.Card_SuitToBeDecided, 10)
	AddSubcards(trick88)
	if trick88:isAvailable(self.player) and (self.player:getHandcardNum() <= self.player:getMaxCards()) then return "ex_nihilo" end

	for _, ecard in ipairs(self:getTurnUse(true)) do
		if ecard:isKindOf("Slash") then
			return "analeptic"
		end
	end

	return "ex_nihilo"
end

local luashizi_skill = {}
luashizi_skill.name = "luashizi"
table.insert(sgs.ai_skills, luashizi_skill)
luashizi_skill.getTurnUseCard = function(self)
	if self.player:isNude() then return end
	if self.player:hasFlag("luashizix") then return end
	self.room:writeToConsole("Yumemi test X3")
	return sgs.Card_Parse("#luashizi:.:")
end

sgs.ai_skill_use_func["#luashizi"] = function(card, use, self)
	local toUse = self:Jiaoshou(true)
	--self.room:writeToConsole("Yumemi test X8" .. #toUse)
	if (not toUse) or (#toUse == 0) then return end
	self.room:writeToConsole("Yumemi test X9")
	if self.player:isNude() then return end
	local userstring = useWhatCard(self, toUse)
	local taoluancard = sgs.Sanguosha:cloneCard(userstring, sgs.Card_NoSuit, 10)

	taoluancard:setSkillName("luashizi")

	for _, card_0 in ipairs(toUse) do
		taoluancard:addSubcard(card_0:getId())
	end

	local dummy_use = { isDummy = false , to = sgs.SPlayerList() }

	if taoluancard:getTypeId() == sgs.Card_TypeBasic then
		self:useBasicCard(taoluancard, dummy_use)
	else
		assert(taoluancard)
		--self.room:writeToConsole("早苗测试")
		self:useTrickCard(taoluancard, dummy_use)
	end
	if userstring == "jink" and userstring == "nullification" then return false end
	self.room:writeToConsole("早苗测试A")
	if not dummy_use.card then return end
	--self.room:writeToConsole("早苗测试B")
	self.room:writeToConsole("早苗测试B" .. userstring)
	if dummy_use.to and not dummy_use.to:isEmpty() then
		if dummy_use.to[1] then
			self.room:writeToConsole("早苗测试目标" .. dummy_use.to[1]:objectName())
		end
	end
	use.card = taoluancard
	if use.to then
		use.to = dummy_use.to
		if math.random() < 0.009 then self.player:speak("我都玩了⑨年了，技术不比你强？") end
		return
	end
	return
end

sgs.ai_cardsview["luashizi"] = function(self, class_name, player)
	local classname2objectname = {
		["Slash"] = "slash", ["Jink"] = "jink",
		["Peach"] = "peach", ["Analeptic"] = "analeptic",
		["Nullification"] = "nullification",
		["FireSlash"] = "fire_slash", ["ThunderSlash"] = "thunder_slash"
	}
	local name = classname2objectname[class_name]
	if not name then return end
	if player:hasSkill("luashizi") then
		local no_have = true
		local cards = self:Jiaoshou()
		if not cards then return end
		if #cards == 0 then return end
		for _,c in ipairs(cards) do	--yun
			if c:isKindOf(class_name) then
				no_have = false
				break
			end
		end
		if (not no_have) and (not (#cards < 3 and self.player:getMark("chaofan") > 0 and self.player:getMark("chaofan") < 11)) then return end
		if class_name == "Peach" and player:getMark("Global_PreventPeach") > 0 then return end
		if class_name ~= "Peach" and class_name ~= "Analeptic" then
			local discard_ids = self.room:getDiscardPile()
			if discard_ids:isEmpty() then return end
			if player:getMark("luachaofanA") == 0 then return end
			local x = sgs.Sanguosha:getCard(discard_ids:at(0)):getNumber()
			if x <= 10 then return end
		end
		local string = name .. ":luashizi[to_be_decided:10]="
		local cardids = {}
		for _,c in ipairs(cards) do	--yun
			table.insert(cardids, tostring(c:getEffectiveId()))
		end
		cardids = table.concat(cardids, "+")
		string = string .. cardids
		return string
	end
end

sgs.ai_use_priority.luashizi = 38

sgs.ai_skill_discard.luachaofan = function(self, discard_num, optional, include_equip)
	local if_shizi = self:Jiaoshou()
	local function Dont_discard_shizi(CardB)
		if if_shizi then
			for _,c in ipairs(if_shizi) do	--yun
				if c:getEffectiveId() == CardB:getEffectiveId() then
					return true
				end
			end
		end
	end
	local function Check_R(card)
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return false end
		end
		if Dont_discard_shizi(card) then return false end
		if card:isKindOf("Wanbaochui") then return false end
		if card:isKindOf("Hui") then return false end
		if card:isKindOf("Crossbow") and #self:getCards("Slash") > 1 then return false end
		if card:getNumber() >= 10 then return false end
		if if_shizi or #self:getTurnUse(true) > 1 then
			if card:getNumber() < 3 then return true end
			if card:isKindOf("Peach") and not self:OverFlowPeach(card) then return false end
			if card:isKindOf("ExNihilo") or card:isKindOf("Duel") then return false end
			if card:isKindOf("Lightning") and self:willUseLightning(card) then return false end
			if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
			if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
			if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
			if card:isKindOf("Indulgence") then return false end
			return true
		end
		return false
	end
	local cards = sgs.QList2Table(self.player:getCards("he"))
	local function compare_func2(a, b)
		return a:getNumber() < b:getNumber()
	end
	table.sort(cards, compare_func2)
	for _, card in ipairs(cards) do
		if Check_R(card) then self.room:writeToConsole("jiaoo shou test X " .. card:getEffectiveId());return card:getEffectiveId() end
	end
	return {}
end

local luasishux_skill = {}
luasishux_skill.name = "luasishux"
table.insert(sgs.ai_skills, luasishux_skill)

luasishux_skill.getTurnUseCard = function(self)
	if self.player:isKongcheng() then return end
	if self.player:hasUsed("#luasishux") then return end
	return sgs.Card_Parse("#luasishux:.:")
end

sgs.ai_skill_use_func["#luasishux"] = function(cardQ, use, self)

	local cards = sgs.QList2Table(self.player:getHandcards())
	local x = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")

	if #self.enemies > 0 then
		self:sort(self.enemies, "hp")
		for _, enemy in ipairs(self.enemies) do
			if enemy:getHp() == 1 and self:damageIsEffective(enemy, sgs.DamageStruct_Normal, self.player) then
				for _, card in ipairs(cards) do
					if not ((card:isKindOf("Peach") or card:isKindOf("Analeptic")) and self:isWeak() and x < 2)
						and card:isKindOf("BasicCard") then
						use.card = sgs.Card_Parse("#luasishux:" .. card:getEffectiveId()..":")
						if use.to then
							use.to:append(enemy)
							return
						end
					end
				end
			end
		end
	end
	for _, card in ipairs(cards) do
		if card:isKindOf("EquipCard") then
			use.card = sgs.Card_Parse("#luasishux:" .. card:getEffectiveId()..":")
			if use.to then
				use.to:append(self.player)
				return
			end
		end
	end
end

sgs.ai_use_priority.luasishux = 9
sgs.ai_skill_choice.luasishux = function(self, choices)
	local shinki = self.room:findPlayerBySkillName("luatianzhao")
	if self.player:isWounded() then return "Peach" end
	if self:getCardsNum("Jink") < 1 then return "Jink" end
	if (self:getCardsNum("Analeptic") < 1 and self:getCardsNum("Slash") >= 1) or (shinki and self:isFriend(shinki)) then return "Analeptic" end
	return "Slash"
end

local luajinshu_skill = {}
luajinshu_skill.name = "luajinshu"
table.insert(sgs.ai_skills, luajinshu_skill)

luajinshu_skill.getTurnUseCard = function(self)
	if self.player:usedTimes("#luajinshu") > 2 then return end
	if self.player:getMark("@jinshu") < self.room:getAlivePlayers():length() - 1 then return end

	local SavageAssault = sgs.Sanguosha:cloneCard("savage_assault", sgs.Card_NoSuit, 0)
	local ArcheryAttack = sgs.Sanguosha:cloneCard("archery_attack", sgs.Card_NoSuit, 0)
	local GodSalvation = sgs.Sanguosha:cloneCard("god_salvation", sgs.Card_NoSuit, 0)
	local AmazingGrace = sgs.Sanguosha:cloneCard("amazing_grace", sgs.Card_NoSuit, 0)
	local value_1 = self:getAoeValue(SavageAssault)
	local value_2 = self:getAoeValue(ArcheryAttack)
	local value_3 = self:godSalvationValue(GodSalvation)*2.5
	local value_4 = self:getAmazingGraceValue(AmazingGrace)*6

	if value_1 < 15 and value_2 < 15 and value_3 < 15 and value_4 < 15 then return end
	return sgs.Card_Parse("#luajinshu:.:")
end

sgs.ai_skill_use_func["#luajinshu"] = function(cardQ, use, self)
	use.card = sgs.Card_Parse("#luajinshu:.:")
	if use.to then
		use.to = sgs.SPlayerList()
		return
	end
end

sgs.ai_skill_choice.luajinshu = function(self, choices)
	local SavageAssault = sgs.Sanguosha:cloneCard("savage_assault", sgs.Card_NoSuit, 0)
	local ArcheryAttack = sgs.Sanguosha:cloneCard("archery_attack", sgs.Card_NoSuit, 0)
	local GodSalvation = sgs.Sanguosha:cloneCard("god_salvation", sgs.Card_NoSuit, 0)
	local AmazingGrace = sgs.Sanguosha:cloneCard("amazing_grace", sgs.Card_NoSuit, 0)
	local value_1 = self:getAoeValue(SavageAssault)
	local value_2 = self:getAoeValue(ArcheryAttack)
	local value_3 = self:godSalvationValue(GodSalvation)*2.5
	local value_4 = self:getAmazingGraceValue(AmazingGrace)*6

	if value_1 >= value_2 and value_1 > value_3 and value_1 > value_4 then return "savage_assault" end
	if value_2 > value_1 and value_2 > value_3 and value_2 > value_4 then return "archery_attack" end
	if value_3 > value_2 and value_3 > value_1 and value_3 > value_4 then return "god_salvation" end
	if value_4 > value_2 and value_4 > value_1 and value_4 > value_3 then return "amazing_grace" end

end

sgs.ai_use_priority.luajinshu = 5

local function findfenghuaCard(self)
	if self.player:getCardCount(true) < 2 then return nil end
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)

	local function Check_R(card)
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return false end
		end
		if self.player:containsTrick("gainb") then return true end
		if card:isKindOf("Peach") and not (card:isKindOf("Analeptic") and self.player:getHp() == 1) then return false end
		if card:isKindOf("TrickCard") then
			if card:isKindOf("Lightning") and not self:willUseLightning(card) then return true end
			if card:isKindOf("AOE") and self:getAoeValue(card) > 75 then return false end
			if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
			if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 3 then return false end
			if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
			if card:isKindOf("Indulgence") then
				local dummy_use = {isDummy = true}
				self:useTrickCard(card, dummy_use)
				if not dummy_use.card then return true end
				return false
			end
			return true
		end
		if card:isKindOf("EquipCard") then
			if card:isKindOf("Armor") and self:needToThrowArmor() then return true end
			if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
				local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
						or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
				if not bool_3 then return true end
			end
			return false
		end
		return true
	end
	for _,acard in ipairs(cards) do
		if Check_R(acard) then
			for _,bcard in ipairs(cards) do
				if bcard:getSuit() == acard:getSuit() and bcard:getEffectiveId() ~= acard:getEffectiveId() and Check_R(bcard) then
					local toUse = {}
					table.insert(toUse, bcard:getEffectiveId())
					table.insert(toUse, acard:getEffectiveId())
					return toUse
				end
			end
		end
	end
	return
end
sgs.ai_skill_invoke.luaxueyue = function(self, data)
	local shikieiki = self.room:findPlayerBySkillName("luapanjue")
	if shikieiki and not self:isFriend(shikieiki) then return false end

	local x = self:getOverflow()
	if self.player:getPhase() == sgs.Player_Judge or self.player:getPhase() == sgs.Player_Draw then
		x = x + 2
	end
	if self.player:getPhase() == sgs.Player_Discard and x >= 0 then return false end
	local count = 0
	for _, c in sgs.qlist(self.player:getHandcards()) do
		if c:getSuit() == sgs.Card_Spade then
			count = count + 1
		end
	end
	for _, c in sgs.qlist(self.player:getHandcards()) do
		if c:getSuit() == sgs.Card_Club then
			count = count + 1
		end
	end
	for _, c in sgs.qlist(self.player:getHandcards()) do
		if c:getSuit() == sgs.Card_Diamond then
			count = count + 1
		end
	end
	for _, c in sgs.qlist(self.player:getHandcards()) do
		if c:getSuit() == sgs.Card_Heart then
			count = count + 1
		end
	end
	if count <= 1 then return true end
	local hp = self.player:getHp() + self:getCardsNum("Peach")
	if hp == 1 and self:getCardsNum("Analeptic") > 0.9 then hp = hp + 1 end

	if hp == 1 and count == 2 and math.random() > 0.8 then return true end
	if hp == 2 and count == 2 and math.random() > 0.5 then
		if self.player:getState() == "robot" then
			if math.random() > 0.7 then self:speak("为成大业者，这点儿牺牲算不得什么！") end
		end
		return true
	end
	if hp == 3 and count == 2 and x <= 2 and not findfenghuaCard(self) then return true end

	if hp <= 2 and count == 3 then return false end
	if hp > 2 and count == 3 and x <= 2 and not findfenghuaCard(self) then return true end

	if hp > 3 and x <= 2 and not findfenghuaCard(self) then return true end
	return false
end

local luafenghua_skill = {}
luafenghua_skill.name = "luafenghua"
table.insert(sgs.ai_skills, luafenghua_skill)
luafenghua_skill.getTurnUseCard = function(self)
	if self.player:getCardCount(true) < 3 then return nil end
	if self.player:getMark("luafenghua") ~= 0 then return nil end
	if #self.friends_noself == 0 then return nil end
	return sgs.Card_Parse("#luafenghua:.:")
end
sgs.ai_skill_use_func["#luafenghua"] = function(cardD, use, self)
	local toUse = findfenghuaCard(self)
	local friends = self.friends_noself
	self:sort(friends, "defense")
	if toUse then
		use.card = sgs.Card_Parse("#luafenghua:".. table.concat(toUse, "+")  ..":")
		if use.to then
			use.to:append(friends[1])
			return
		end
	end
end
sgs.ai_use_priority.luafenghua = 5.3
sgs.ai_skill_choice.luafenghua = function(self, choices, data)
	local target = data:toPlayer()
	if target:getMaxHp() == 1 or self:isWeak(target) then return "maxhp" end
	if self:hasSkills(sgs.need_maxhp_skill, target) then return "maxhp" end
	return "extraturn"
end
sgs.ai_skill_invoke.luatiandu = true

local function findSKtarget(self, card)
	if card:isKindOf("EquipCard") then
		local qlistA = sgs.IntList()
		qlistA:append(card:getId())
		local target = self:Nitori(qlistA)
		if target then return target end
		return self.player
	elseif card:isKindOf("Slash") then
		local target = self:Kitcho(card, true, true)
		if target then return target end
		return self.player
	elseif card:isKindOf("Dismantlement") or card:isKindOf("Snatch") then
		local target = self:findPlayerToDiscard("hej", #self.friends_noself > 0)
		if target then
			for _, friend in ipairs(self.friends) do
				if not friend:isCardLimited(card, sgs.Card_MethodUse) and not self.room:isProhibited(friend, target, card)
					and not friend:isCardLimited(card, sgs.Card_MethodUse) and not self.room:isProhibited(friend, friend, card)
						and (not card:isKindOf("Dismantlement") or friend:distanceTo(target) <= 1) then
					return friend
				end
			end
		end
	elseif card:isKindOf("Peach") then
		self:sort(self.friends, "defense")
		for _, friend in ipairs(self.friends) do
			if friend:isWounded() and not friend:isCardLimited(card, sgs.Card_MethodUse)
					and not self.room:isProhibited(friend, friend, card) then
				return friend
			end
		end
		return self.player
	elseif card:isKindOf("DelayedTrick") or card:isKindOf("AOE") or card:isKindOf("AmazingGrace") or card:isKindOf("GodSalvation") then
		return self.player
	elseif card:isKindOf("FireAttack") then
		self:sort(self.friends, "handcard2")
		for _, friend in ipairs(self.friends) do
			if not friend:isCardLimited(card, sgs.Card_MethodUse)
					and not self.room:isProhibited(friend, friend, card) then
				return friend
			end
		end
	elseif card:isKindOf("quanxiang") then
		self:sort(self.friends, "handcard2")
		for _, friend in ipairs(self.friends) do
			if not friend:isCardLimited(card, sgs.Card_MethodUse)
					and not self.room:isProhibited(friend, friend, card) then
				for _, enemy in ipairs(self.enemies) do
					if friend:inMyAttackRange(enemy) and not self.room:isProhibited(friend, enemy, card) then
						return friend
					end
				end
			end
		end
	elseif card:isKindOf("Hui") then
		self:sort(self.friends, "defense")
		for _, enemy in ipairs(self.enemies) do
			return enemy
		end
	end
end
local luazhujiao_skill = {}
luazhujiao_skill.name = "luazhujiao"
table.insert(sgs.ai_skills, luazhujiao_skill)
luazhujiao_skill.getTurnUseCard = function(self)
	if self.player:getMaxCards() == 0 then return end
	if self.player:getMaxCards() == 1 and self.player:getHandcardNum() > 2 then return end
	if self.player:getMaxCards() > 0 then return sgs.Card_Parse("#luazhujiao:.:") end
end

sgs.ai_skill_use_func["#luazhujiao"] = function(cardD, use, self)
	local bool_0 = false
	for _, friend in ipairs(self.friends) do
		if friend:isWounded() then bool_0 = true end
	end

	local function Check1(card)
		if not self.player:isCardLimited(card, sgs.Card_MethodDiscard) and card:getNumber() == 10 and bool_0 then return true end
		if card:isKindOf("Peach") and not self:OverFlowPeach(card) then return false end
		if self:isWeak() and card:isKindOf("Jink") then return false end
		if self:isWeak() and card:isKindOf("Analeptic") then return false end
		if card:isKindOf("ExNihilo") then return false end
		return not self.player:isCardLimited(card, sgs.Card_MethodDiscard)
	end
	local function Check2(card)
		if card:isKindOf("Peach") then return false end
		if self:getCardsNum("Jink") < 2 and card:isKindOf("Jink") then return false end
		if card:isKindOf("Analeptic") then return false end
		if card:isKindOf("ExNihilo") or card:isKindOf("Duel") then return false end
		if card:isKindOf("Hui") then return false end
		if card:isKindOf("Nullification") then return false end
		return not self.player:isCardLimited(card, sgs.Card_MethodDiscard)
	end
	if self.player:getMark("@luazhujiao") > 0 then
		if self.player:getMark("luashikong") < 2 then
			local card_0 = self:Chiyuri()
			if card_0 then
				local handcards = sgs.QList2Table(self.player:getHandcards())
				self:sortByKeepValue(handcards)
				if #handcards > 1 then
					for _,card in ipairs(handcards) do
						if card:getEffectiveId() ~= card_0:getEffectiveId() and bool_0 and card:getNumber() + card_0:getNumber() == 10 then
							use.card = sgs.Card_Parse("#luazhujiao:".. card_0:getEffectiveId() .. "+" .. card:getEffectiveId() ..":")
							if use.to then
								use.to = sgs.SPlayerList()
								return
							end
						end
					end
					for _,card in ipairs(handcards) do
						if card:getEffectiveId() ~= card_0:getEffectiveId() and Check1(card) then
							use.card = sgs.Card_Parse("#luazhujiao:".. card_0:getEffectiveId() .. "+" .. card:getEffectiveId() ..":")
							if use.to then
								use.to = sgs.SPlayerList()
								return
							end
						end
					end
				end
			else
				sgs.ai_use_priority.luazhujiao = 0
				local handcards = sgs.QList2Table(self.player:getHandcards())
				self:sortByKeepValue(handcards)
				if bool_0 and not (self:getOverflow() == 2 and self.player:getMark("luashikong") == 1) then
					for _,card in ipairs(handcards) do
						if Check2(card) then
							for _,card2 in ipairs(handcards) do
								if card:getEffectiveId() ~= card2:getEffectiveId() and card:getNumber() + card2:getNumber() == 10 then
									use.card = sgs.Card_Parse("#luazhujiao:".. card:getEffectiveId() .. "+" .. card2:getEffectiveId() ..":")
									if use.to then
										use.to = sgs.SPlayerList()
										return
									end
								end
							end
						end
					end
				end
				if #handcards > 1 and not (self:getOverflow() == 2 and self.player:getMark("luashikong") == 1) then
					if Check2(handcards[1]) and Check2(handcards[2]) then
						use.card = sgs.Card_Parse("#luazhujiao:".. handcards[1]:getEffectiveId() .. "+" .. handcards[2]:getEffectiveId() ..":")
						if use.to then
							use.to = sgs.SPlayerList()
							return
						end
					end
				end
			end
		end
	else
		local card_0 = self:Chiyuri()
		if card_0 then
			local target = findSKtarget(self, card_0)
			if target and (self:getOverflow() < 2 or self.player:containsTrick("gainb")) then
				use.card = sgs.Card_Parse("#luazhujiao:".. card_0:getEffectiveId()  ..":")
				if use.to then
					use.to = sgs.SPlayerList()
					return
				end
			end
		else
			sgs.ai_use_priority.luazhujiao = 0
			local handcards = sgs.QList2Table(self.player:getHandcards())
			self:sortByKeepValue(handcards)
			if #handcards > 0 then
				for _,card in ipairs(handcards) do
					if Check1(card) or self.player:containsTrick("gainb") then
						if self:getOverflow() >= 1 and self.player:getMark("luashikong") == 1 then break end
						use.card = sgs.Card_Parse("#luazhujiao:".. card:getEffectiveId()  ..":")
						if use.to then
							use.to = sgs.SPlayerList()
							return
						end
					end
				end
			end
		end
	end
end

sgs.ai_use_priority.luazhujiao = 14

sgs.ai_skill_playerchosen.luashikong = function(self, targets)
	local cards = self.player:getTag("luashikongK"):toString()
	cards = cards:split("+")
	for _, id in ipairs(cards) do
		local card = sgs.Sanguosha:getCard(tonumber(id))
		local target = findSKtarget(self, card)
		if findSKtarget(self, card) then return target end
	end
	return nil
end

sgs.ai_playerchosen_intention.luashikong = function(self, from, to)
	local cards = self.room:getTag("luashikongK"):toString()
	cards = cards:split("+")
	for _, id in ipairs(cards) do
		local card = sgs.Sanguosha:getCard(tonumber(id))
		if card:isKindOf("Hui") then return end
	end
	sgs.updateIntention(from, to , -20)
end


sgs.ai_skill_askforag.luashikong = function(self, card_ids)
	local cards = {}
	for i, card_id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(card_id)
		table.insert(cards, card)
	end
	self:sortByUseValue(cards)
	for _, card in ipairs(cards) do
		if not card:isKindOf("Analeptic") and not card:isKindOf("Jink") and not card:isKindOf("Nullification") then
			local dummy_use = {isDummy = true}
			local type = card:getTypeId()
			self["use" .. sgs.ai_type_name[type + 1] .. "Card"](self, card, dummy_use)
			if dummy_use.card then
				return card:getId()
			end
		end
	end
	for _, card in ipairs(cards) do
		if card:isKindOf("Slash") then --强制输出
			local playerX = self:Skadi(card, nil, self.player, true)
			if playerX then
				return card:getId()
			end
		end
	end
end

sgs.ai_skill_use["@@luashikong"] = function(self, prompt)-- 义舍技能卡的使用函数
	local card = self.player:getMark("shikong") - 1
	self.room:writeToConsole("seiran test X" .. card)
	card = sgs.Sanguosha:getCard(card)

	local dummy_use = { isDummy = true , to = sgs.SPlayerList() }

	if card:getTypeId() == sgs.Card_TypeBasic then

		self:useBasicCard(card, dummy_use)
	else
		self:useTrickCard(card, dummy_use)
	end

	if dummy_use.to and not dummy_use.to:isEmpty() then
		local targets = {}
		for _, p in sgs.qlist(dummy_use.to) do
			table.insert(targets, p:objectName())
		end
		return "#luashikong:.:->" .. table.concat(targets, "+")
	end
	if card:isKindOf("Slash") then --强制输出
		local playerX = self:Skadi(card, nil, self.player, true)
		if playerX then
			return "#luashikong:.:->" .. playerX:objectName()
		end
	end
end



local function ceYan(self, card)
	local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
	slash:addSubcard(card:getEffectiveId())
	slash:setSkillName("luaboli")
	slash:deleteLater()
	return true
end
local function findhakreicard(self, cardsA)
	local fin_target
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	local red_card
	self:sortByUseValue(cards, true)

	local x = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
	local bool_k = (self.player:getHp() <= 2 and self.player:getHandcardNum() <= 3 and x <= 1)

	local function CheckH(target)
		local useAll = false
		if target:getHp() == 1 and not target:hasArmorEffect("eight_diagram") and self:isWeak(target)
				and getCardsNum("Jink", target, self.player) + getCardsNum("Peach", target, self.player) + getCardsNum("Analeptic", target, self.player) == 0 then
			useAll = true
		end
		return useAll
	end

	local function caonima(slash)
		local boolQ = false
		if (self.player:getHandcardNum() > 2 or self:getOverflow() > 0) then
			for _, card in ipairs(cardsA) do
				if card:getEffectiveId() == slash:getEffectiveId() then boolQ = true end
			end
		end
		if not boolQ then return true end
		return false
	end
	local function Check_F(card_0, target)
		if (card_0:isKindOf("Peach") or card_0:isKindOf("ExNihilo")) and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
			return false
		end
		if card_0:isKindOf("Duel") and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
			return false
		end
		if card_0:isKindOf("Indulgence") and not CheckH(target) then
			for _, enemy in ipairs(self.enemies) do
				if self:getIndulgenceValue(enemy) > 3 then return false end
			end
		end
		if not card_0:isKindOf("Dismantlement") and self:ThreeCheck(card_0) then return false end

		if (card_0:isKindOf("AOE") and self:getAoeValue(card_0) > 35) then return false end
		if card_0:isKindOf("Lightning") and not self:willUseLightning(card_0) then return true end
		local bool_2 = card_0:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(card_0:getId()) == sgs.Player_PlaceEquip))
		local bool_3 = (card_0:isKindOf("EightDiagram") or card_0:isKindOf("RenwangShield") or card_0:isKindOf("Tengu"))
				and ((self.room:getCardPlace(card_0:getId()) == sgs.Player_PlaceEquip)
				or ((self.room:getCardPlace(card_0:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
		if (bool_2 or bool_3) and not CheckH(target) then return false end
		if card_0:isKindOf("Slash") and caonima(card_0) then return false end
		if (not (card_0:isKindOf("Jink") and bool_k) and not (isCard("Peach", card_0, self.player) and self.player:isWounded())) or CheckH(target) then
			return true
		end
	end

	local function getTarget(card)
		local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
		slash:setSkillName("luaboli")
		slash:addSubcard(card:getEffectiveId())
		for _, enemy in ipairs(self.enemies) do
			if self.player:canSlash(enemy, slash, false) and self:slashIsEffective(slash, enemy, self.player)
					and not self:slashProhibit(slash, enemy) and not (enemy:hasSkill("anxian") and not enemy:isKongcheng()) then
				local count = 0
				local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
				for _, cc in sgs.qlist(enemy:getHandcards()) do
					if (cc:hasFlag("visible") or cc:hasFlag(flag)) and (cc:isKindOf("Hui")) and card:getSuit() == cc:getSuit() then
						count = count - 2
					end
				end

				for _, cc in sgs.qlist(enemy:getHandcards()) do
					if (cc:hasFlag("visible") or cc:hasFlag(flag)) and (cc:isKindOf("Peach") or cc:isKindOf("ExNihilo") or cc:isKindOf("Indulgence")) and card:getSuit() == cc:getSuit() then
						count = count + 1
					end
				end

				for _, cc in sgs.qlist(enemy:getHandcards()) do
					if (cc:hasFlag("visible") or cc:hasFlag(flag)) and card:getSuit() == cc:getSuit() then
						count = count + 1
					end
				end

				if count > 1 then return enemy end
			end
		end
		for _, enemy in ipairs(self.enemies) do
			if self.player:canSlash(enemy, slash, false) and self:slashIsEffective(slash, enemy, self.player)
					and not self:slashProhibit(slash, enemy) and not (enemy:hasSkill("anxian") and not enemy:isKongcheng()) then return enemy
			end
		end
	end


	if not red_card then
		if self:needToThrowArmor() and ceYan(self, self.player:getArmor()) then
			local target = getTarget(self.player:getArmor())
			if target then
				red_card = self.player:getArmor()
				fin_target = target
			end
		end
	end



	if not red_card then
		for _, card in ipairs(cards) do
			if card:getSuit() == sgs.Card_Diamond and ceYan(self, card) then
				if not (card:isKindOf("Jink") and bool_k) and not (isCard("Peach", card, self.player) and self.player:isWounded())
						and not (card:isKindOf("Slash") and caonima(card)) then
					local target = getTarget(card)
					if target then
						red_card = self.player:getArmor()
						fin_target = target
					end
				end
			end
		end
	end

	if not red_card then
		for _, card in ipairs(cards) do
			if ceYan(self, card) then
				local target = getTarget(card)
				if target and Check_F(card, target) then
					red_card = card
					fin_target = target
					break
				end
			end
		end
	end
	return red_card, fin_target
end
local luaboli_skill = {}
luaboli_skill.name = "luaboli"
table.insert(sgs.ai_skills, luaboli_skill)
luaboli_skill.getTurnUseCard = function(self, inclusive)
	if self.player:hasFlag("luaboli_used") then return end
	return sgs.Card_Parse("#luaboli:.:")
end

sgs.ai_skill_use_func["#luaboli"] = function(card, use, self)
	local cardsA = self:getTurnUse(true)
	local red_card, target = findhakreicard(self, cardsA)
	if not red_card then return end


	local slash = sgs.Sanguosha:cloneCard("slash", red_card:getSuit(), red_card:getNumber())
	slash:setSkillName("luaboli")
	slash:addSubcard(red_card:getEffectiveId())
	local dummy_use = { isDummy = true , to = sgs.SPlayerList() , distance = 99}
	self:useBasicCard(slash, dummy_use)
	if dummy_use.card and not dummy_use.card:isKindOf("Slash") then
		use.card = dummy_use.card
		if use.to then
			use.to = dummy_use.to
		end
	end


	use.card = sgs.Card_Parse("#luaboli:" .. red_card:getId() ..":")
	if use.to then
		if red_card:getSuit() == sgs.Card_Diamond and math.random() < 0.2 then self.player:speak("妖怪治退！") end
		use.to:append(target)
		return
	end
end

sgs.ai_use_priority.luaboli = 4.38

sgs.ai_skill_choice.luaquxie = function(self, choices, data)
	local target = data:toPlayer()
	local card_0 = self.room:getTag("luaquxieTC"):toCard()

	local function Check_R(card)
		if card:isKindOf("Peach") then return true end
		if card:isKindOf("Jink") then return true end
		if card:isKindOf("ExNihilo") then return true end
		if card:isKindOf("Analeptic") and self:isWeak(target) then return true end
		if card:isKindOf("Indulgence") or card:isKindOf("Snatch") then return true end
		return false
	end

	for _, card in sgs.list(target:getHandcards()) do
		if Check_R(card) and card_0:getSuit() == card:getSuit() then
			return "luaquxie2"
		end
	end

	local count = 0
	for _, card in sgs.list(target:getHandcards()) do
		if card_0:getSuit() == card:getSuit() then
			count = count + 1
		end
	end
	if count > 1 then return "luaquxie2" end
	return "luaquxie1"
end
sgs.ai_skill_invoke.luaquxie = sgs.ai_skill_invoke.liegong  --如何决定技能是否发动的一个实例



sgs.ai_skill_playerchosen.luaxiongshi = function(self, targets)
	local target = sgs.ai_skill_playerchosen.luajianjic(self, targets)
	return target
end


local function usenizhouCard(self)
	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end
	local function Check_R(card)
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return false end
		end
		if card:isKindOf("TrickCard") then
			if card:isKindOf("Nullification") then return true end
			if card:isKindOf("IronChain") and not shuxin then return true end
			if card:isKindOf("Dismantlement") or card:isKindOf("NeedMaribel") then return true end
			if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
			if card:isKindOf("Lightning") and not self:willUseLightning(card) then return true end
			if card:isKindOf("AOE") and self:getAoeValue(card) > 35 then return false end
			if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
			if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
			local dummy_use = {isDummy = true}
			self:useTrickCard(card, dummy_use)
			if not dummy_use.card then return true end
			return false
		end
		if card:isKindOf("EquipCard") then --or card:isKindOf("DefensiveHorse")
			if card:isKindOf("OffensiveHorse") then
				return true
			end
			if card:isKindOf("DefensiveHorse") then
				return true
			end
			if card:isKindOf("Armor") and self:needToThrowArmor() then return true end
			if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
				local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
						or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
				if not bool_3 then return true end
			end
			if card:isKindOf("Weapon") then
				local dummy_use = {isDummy = true}
				self:useEquipCard(card, dummy_use)
				if not dummy_use.card then return true end
				if dummy_use.card and self.player:getWeapon() then return true end
			end
		end
		if card:isKindOf("Slash") or card:isKindOf("Ofuda") then return true end
		if card:isKindOf("Hui") then return true end
		if card:isKindOf("Jink") and self:getCardsNum("Jink") > 1 then return true end
	end
	for _, card in sgs.qlist(self.player:getCards("he")) do
		if Check_R(card) then return card end
	end
end
sgs.ai_skill_use["@@luanizhou"] = function(self, prompt)
	local cards = self:getTurnUse(true)
	local count = 0
	for _, ecard in ipairs(cards) do
		if (ecard:isKindOf("SingleTargetTrick") and not ecard:isKindOf("DelayedTrick"))
			or ecard:isKindOf("Slash") then count = count + 1 end
	end
	if #self.friends_noself < 1 then return "." end
	if #self.friends_noself == 1 then
		if self.friends_noself[1]:getHandcardNum() < 2 then
			return "."
		end
		if count < 1 then return "." end
	end
	local toUse = usenizhouCard(self)
	if toUse then
		self.room:writeToConsole("sagume test" .. toUse:getId())
		return "#luanizhou:".. toUse:getId() .. ":"
	end
end

sgs.ai_skill_choice.luanizhou = function(self, choices)
	local cards = self:getTurnUse(true)
	local count = 0
	for _, ecard in ipairs(cards) do
		if (ecard:isKindOf("SingleTargetTrick") and not ecard:isKindOf("DelayedTrick"))
				or ecard:isKindOf("BasicCard") then count = count + 1 end
	end
	for _, ecard in ipairs(cards) do
		if ecard:isKindOf("AOE") then
			if count + self.room:getAlivePlayers():length() - 1 > 2 then
				return 3
			end
		end
	end
	if #self.friends_noself == 1 then
		if math.random() < 0.2 then return "3" end
		local x = self.friends_noself[1]:getHandcardNum() - getCardsNum("Jink", self.friends_noself[1], self.player)
		if x >= 2.5 then return "3" end
		if #cards == 1 then return "1" end
		return "2"
	else
		local y = math.random(#self.friends_noself)
		local x = self.friends_noself[y]:getHandcardNum() - getCardsNum("Jink", self.friends_noself[y], self.player)
		if math.random() < 0.3 then return "3" end
		if math.random() < 0.5 then return "1" end
		return "2"
	end
end

sgs.ai_skill_choice.luanizhou2 = function(self, choices)
	local sp = self.room:getCurrent():getMark("@luatianshii")
	if sp > 2 and not self:needToThrowArmor() then return "damage" end
	if sp > 3 and self:needToThrowArmor() then return "damage" end
	return "discard"
end

sgs.ai_skill_invoke.luashehuo = function(self, data)
	return true
end
sgs.ai_skill_choice.luanizhou2 = function(self, choices)
	return "luashehuo2"
end

sgs.ai_skill_invoke.luashuangshen = function(self, data)
	return true
end

sgs.ai_skill_playerchosen.luashushi = function(self, targets)
	local targetlist=sgs.QList2Table(targets)
	local acard = sgs.Sanguosha:cloneCard("yuzhi")
	for _, target in ipairs(targetlist) do
		if self:isEnemy(target) and not self.room:isProhibited(self.player, target, acard)
			and self:damageIsEffective(self.player, nil, target) and not self:needToLoseHp(target, self.player, false, false)
				and not self:getDamagedEffects(target, self.player, false) then
			return target
		end
	end
	for _, target in ipairs(targetlist) do
		if self:isEnemy(target) and not self.room:isProhibited(self.player, target, acard)
			and self:damageIsEffective(self.player, nil, target) then
			return target
		end
	end
end


local function GetjulianCard(self, cardX, shouyi)
	if not shouyi then shouyi = 1 end
	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end
	local jink_table = {}
	for _, id in ipairs(julian_list) do
		if self.room:getCardPlace(id) == sgs.Player_DiscardPile and not sgs.Sanguosha:getCard(id):isKindOf("Hui")
				and sgs.Sanguosha:getCard(id):getSuit() == sgs.Card_Club then
			table.insert(jink_table, tostring(id))
		end
	end
	local function Check_R(card)
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return false end
		end
		if cardX:getId() == card:getId() then return false end
		local yuka = self.room:findPlayerBySkillName("luahuapu")
		if yuka and self:isFriend(yuka) then
			if card:getSuit() == sgs.Card_Club then return true end
		end
		if #jink_table > 0 and card:getSuit() == sgs.Card_Club then return true end
		if #jink_table > 1 then return true end
		if card:isKindOf("TrickCard") then
			if card:isKindOf("IronChain") and not shuxin then return true end
			if card:isKindOf("Lightning") and not self:willUseLightning(card) then return true end
			if card:isKindOf("AOE") and self:getAoeValue(card) > 35 and shouyi < 2 then return false end
			if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 and shouyi < 2 then return false end
			if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 and shouyi < 2 then return false end
			if self:ThreeCheck(card) then return false end
			local dummy_use = {isDummy = true}
			self:useTrickCard(card, dummy_use)
			if not dummy_use.card then return true end
			return false
		end
		if card:isKindOf("EquipCard") then --or card:isKindOf("DefensiveHorse")
			if card:isKindOf("OffensiveHorse") then
				return true
			end
			if card:isKindOf("DefensiveHorse") then
				if shouyi == 2 then return true end
				if self.player:getDefensiveHorse() then return true end
			end
			if card:isKindOf("Armor") and self:needToThrowArmor() then return true end
			if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
				local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
						or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
				if not bool_3 then return true end
			end
			if card:isKindOf("Weapon") then
				local dummy_use = {isDummy = true}
				self:useEquipCard(card, dummy_use)
				if dummy_use.card then return false end
				if self.player:getWeapon() then return true end
			end
			if card:isKindOf("Treasure") then return false end
		end
		if card:isKindOf("Analeptic") and shouyi < 2 then return false end
		if card:isKindOf("Peach") then return false end
		local x0 = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
		if card:isKindOf("Jink") and x0 <= 1 and shouyi < 2 then return false end
		return true
	end
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	for _, card in ipairs(cards) do
		if Check_R(card) then return card end
	end
end
local luajulian_skill = {}
luajulian_skill.name = "luajulian"
table.insert(sgs.ai_skills, luajulian_skill)
luajulian_skill.getTurnUseCard = function(self, inclusive)
	return sgs.Card_Parse("#luajulian:.:")
end
sgs.ai_skill_use_func["#luajulian"] = function(X, use, self)
	local acards = self:getTurnUse(true)
	for _, card in ipairs(acards) do
		if card:isKindOf("Slash") then
			local dummy_use = { isDummy = true, extra_target = 99, to = sgs.SPlayerList(), distance = 99 }
			self:useBasicCard(card, dummy_use)
			if dummy_use.card then
				if (dummy_use.to:length() > 1 + self.player:getMark("@luajulian1"))
						or (self.player:getMark("drank") > 0 and not self:YouMu2(dummy_use.to:at(0), true)) then
					local toUse = GetjulianCard(self, card)
					if toUse then
						if (self.player:getMark("drank") > 0 and not self:YouMu2(dummy_use.to:at(0), true)) then
							self.room:setPlayerFlag(self.player, "luajulianE")
						end
						use.card = sgs.Card_Parse("#luajulian:".. toUse:getId() ..":")
						if use.to then
							use.to = sgs.SPlayerList()
							return
						end
					end
				end
			end
		elseif card:isKindOf("Duel") then
			local dummy_use = { isDummy = true, extra_target = 1, to = sgs.SPlayerList() }
			self:useTrickCard(card, dummy_use)
			if dummy_use.card then
				if dummy_use.card then
					if (dummy_use.to:length() > 1 + self.player:getMark("@luajulian1")) then
						local toUse = GetjulianCard(self, card, 2)
						if toUse then
							use.card = sgs.Card_Parse("#luajulian:".. toUse:getId() ..":")
							if use.to then
								use.to = sgs.SPlayerList()
								return
							end
						end
					end
				end
			end
		elseif card:isKindOf("Snatch") or card:isKindOf("Dismantlement") or card:isKindOf("FaithCollection") then
			local snatch = sgs.Sanguosha:cloneCard("Dismantlement")
			local dummy_use = { isDummy = true, extra_target = 1, to = sgs.SPlayerList() }
			self:useTrickCard(snatch, dummy_use)
			if dummy_use.card then
				if (dummy_use.to:length() > 1 + self.player:getMark("@luajulian1")) then
					local toUse = GetjulianCard(self, card)
					if toUse then
						use.card = sgs.Card_Parse("#luajulian:".. toUse:getId() ..":")
						if use.to then
							use.to = sgs.SPlayerList()
							return
						end
					end
				end
			end
		elseif card:isKindOf("Peach") then
			local count = 0
			for _, friend in ipairs(self.friends_noself) do
				if friend:isWounded() and not friend:hasSkill("luasanaey") then
					count = count + 1
				end
			end
			if count >= 1 + self.player:getMark("@luajulian1") then
				local toUse = GetjulianCard(self, card, 2)
				if toUse then
					use.card = sgs.Card_Parse("#luajulian:".. toUse:getId() ..":")
					if use.to then
						use.to = sgs.SPlayerList()
						return
					end
				end
			end
		elseif card:isKindOf("ExNihilo") then
			local count = 0
			for _, friend in ipairs(self.friends_noself) do
				if not self.room:isProhibited(self.player, friend, card) then
					count = count + 1
				end
			end
			if count >= 1 + self.player:getMark("@luajulian1") then
				local toUse = GetjulianCard(self, card, 2)
				if toUse then
					use.card = sgs.Card_Parse("#luajulian:".. toUse:getId() ..":")
					if use.to then
						use.to = sgs.SPlayerList()
						return
					end
				end
			end
		elseif card:isKindOf("Hui") then
			local count = 0
			for _, enemy in ipairs(self.enemies) do
				if not self.player:isCardLimited(card, sgs.Card_MethodUse) and not self.player:isProhibited(enemy, card)
						and self:damageIsEffective(enemy, nil, enemy) then
					count = count + 1
				end
			end
			if count >= 1 + self.player:getMark("@luajulian1") then
				local toUse = GetjulianCard(self, card, 2)
				if toUse then
					use.card = sgs.Card_Parse("#luajulian:".. toUse:getId() ..":")
					if use.to then
						use.to = sgs.SPlayerList()
						return
					end
				end
			end
		end
	end
end
sgs.ai_use_priority.luajulian = 21
sgs.ai_cardneed.luajulian = function(to, card, self)
	return card:getSuit() == sgs.Card_Club
end
sgs.ai_skill_choice.luajulian = function(self, choices, data)
	local jink_table = {}
	for _, id in ipairs(julian_list) do
		if self.room:getCardPlace(id) == sgs.Player_DiscardPile and not sgs.Sanguosha:getCard(id):isKindOf("Hui")
				and sgs.Sanguosha:getCard(id):getSuit() == sgs.Card_Club then
			table.insert(jink_table, tostring(id))
		end
	end
	if #jink_table > 1 and self.player:getMark("@luajulian4") == 0 then
		self.room:writeToConsole("luajulian test X" .. #jink_table)
		return "luajulian4"
	end
	if self.player:hasFlag("luajulianE") then
		if self.player:getMark("drank") > 0 and self.player:getMark("@luajulian3") == 0 then
			return "luajulian3"
		end
		self.room:setPlayerFlag(self.player, "-luajulianE")
	end
	return "luajulian1"
end
sgs.ai_skill_playerchosen.luajulian = function(self, targets)
	local yuka = self.room:findPlayerBySkillName("luahuapu")
	if yuka and self:isFriend(yuka) then
		return yuka
	end
	return self.player
end
