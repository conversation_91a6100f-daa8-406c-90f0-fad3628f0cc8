
math.randomseed(tostring(os.time()):reverse():sub(1, 7)) --------设置时间种子

sgs.ai_skill_invoke.luahuizhen = function(self, data)
	local target = data:toPlayer()
	if self:isFriend(target) and self:isWeak(target) then
		local count = 0
		for _, enemy in ipairs(self.enemies) do
			if getCardsNum("Slash", enemy, self.player) > 0.9 or
					(self.room:getCurrent():hasSkill("luahuizhen") or self.room:getCurrent():getPhase() == sgs.Player_Finish) then
				count = count + 1
			end
		end
		if count > 1 then return false end
	end
	if not self:slashProhibit(sgs.Sanguosha:cloneCard("slash"), target) then return true end
end
sgs.ai_skill_cardask["@luahuizhen"] = function(self, data, pattern, X)
	local target = data:toPlayer()
	self.room:writeToConsole("luahuizhen AItest")
	local shinmyoumaru = self.room:findPlayerBySkillName("luahuizhen")
	local function CouldUse(card)
		if self.player:getMark("@lualongyan") >= 1 and self:isWeak() then
			return false
		end
		if self.room:isProhibited(self.player, target, card) or self.player:isCardLimited(card, sgs.Card_MethodUse) then
			return false
		end
		return true
	end
	if self:isFriend(target) then
		for _, acard in sgs.qlist(self.player:getHandcards()) do
			if acard:isKindOf("Peach") and target:isWounded() and CouldUse(acard) and self:isFriend(shinmyoumaru) then return "$" .. acard:getEffectiveId() end
		end
		for _, acard in sgs.qlist(self.player:getHandcards()) do
			if acard:isKindOf("ExNihilo") and self:getOverflow(target) < 0 and self:isFriend(shinmyoumaru)
					and CouldUse(acard) and self:hasTrickEffective(acard, target, self.player) then return "$" .. acard:getEffectiveId() end
		end
		for _, acard in sgs.qlist(self.player:getHandcards()) do
			if (acard:isKindOf("Dismantlement") or acard:isKindOf("Snatch") or acard:isKindOf("FaithCollection")) and CouldUse(acard)
				and (target:containsTrick("indulgence") or target:containsTrick("supply_shortage") or self:needToThrowArmor(target))
				and not target:containsTrick("YanxiaoCard") and self:hasTrickEffective(acard, target, self.player) and self:isFriend(shinmyoumaru)
			then return "$" .. acard:getEffectiveId() end
		end
		for _, acard in sgs.qlist(self.player:getHandcards()) do
			if acard:isKindOf("NeedMaribel") and not target:containsTrick("need_maribel") and CouldUse(acard) and self:isFriend(shinmyoumaru) then return "$" .. acard:getEffectiveId() end
		end
		for _, acard in sgs.qlist(self.player:getHandcards()) do
			if acard:isKindOf("Banquet") and not target:containsTrick("banquet") and CouldUse(acard) then return "$" .. acard:getEffectiveId() end
		end
		for _, acard in sgs.qlist(self.player:getHandcards()) do
			if acard:isKindOf("EquipCard") and not self:getSameEquip(acard, target) and CouldUse(acard) and self:isFriend(shinmyoumaru) then return "$" .. acard:getEffectiveId() end
		end
		for _, acard in sgs.qlist(self.player:getHandcards()) do
			if acard:isKindOf("IronChain") and target:isChained() and CouldUse(acard) and self:isFriend(shinmyoumaru) then return "$" .. acard:getEffectiveId() end
		end
	else
		for _, acard in sgs.qlist(self.player:getHandcards()) do
			if (acard:isKindOf("Hui")) and CouldUse(acard) then
				return "$" .. acard:getEffectiveId()
			end
		end
		for _, acard in sgs.qlist(self.player:getHandcards()) do
			if (acard:isKindOf("Duel") or acard:isKindOf("ArcheryAttack") or acard:isKindOf("SavageAssault") or acard:isKindOf("FireAttack") or acard:isKindOf("yuzhi")) and CouldUse(acard)
					and self:hasTrickEffective(acard, target, self.player) and CouldUse(acard) and self:isFriend(shinmyoumaru) then
				return "$" .. acard:getEffectiveId()
			end
		end
		for _, acard in sgs.qlist(self.player:getHandcards()) do
			if acard:isKindOf("Indulgence") and CouldUse(acard) and not target:containsTrick("indulgence") and self:hasTrickEffective(acard, target, self.player) and CouldUse(acard) then
				return "$" .. acard:getEffectiveId()
			end
		end
		for _, acard in sgs.qlist(self.player:getHandcards()) do
			if acard:isKindOf("SupplyShortage") and CouldUse(acard) and not target:containsTrick("supply_shortage")
					and self:hasTrickEffective(acard, target, self.player) and CouldUse(acard) and self:isFriend(shinmyoumaru) then
				return "$" .. acard:getEffectiveId()
			end
		end
		for _, acard in sgs.qlist(self.player:getHandcards()) do
			if acard:isKindOf("Slash") and self:slashIsEffective(acard, target, self.player) and CouldUse(acard)
					and (self:isFriend(shinmyoumaru) or self:YouMu2(target, true)) then
				return "$" .. acard:getEffectiveId()
			end
		end
		for _, acard in sgs.qlist(self.player:getHandcards()) do
			if acard:isKindOf("IronChain") and not target:isChained() and CouldUse(acard) and self:isFriend(shinmyoumaru) then return "$" .. acard:getEffectiveId() end
		end
	end
	return "."
end
local luabaochui_skill = {}
luabaochui_skill.name = "luabaochui"
table.insert(sgs.ai_skills, luabaochui_skill)
luabaochui_skill.getTurnUseCard = function(self)
	if self.player:getHandcardNum() < 2 then return end
	if not self.player:getTreasure() then
		local discard_ids = self.room:getDiscardPile()
		local trickcard = sgs.IntList()
		for _, id in sgs.qlist(discard_ids) do
			local card = sgs.Sanguosha:getCard(id)
			if card:isKindOf("Wanbaochui") then
				trickcard:append(id)
			end
		end
		if trickcard:length() > 0 then
			return sgs.Card_Parse("#luabaochui:.:")
		end
	end
end
sgs.ai_skill_use_func["#luabaochui"] = function(cardD, use, self)
	local friends = self.friends_noself
	for _, friend in ipairs(friends) do
		if friend:getHandcardNum() == 1 then
			use.card = sgs.Card_Parse("#luabaochui:.:")
			if use.to then
				use.to:append(friend)
			end
			return
		end
	end
end
local function findyequtarget(self)
	self.room:writeToConsole("luayequ test 5" .. self.player:objectName())
    local friends = self.friends_noself
	for _, friend in ipairs(friends) do
		if friend and friend:getLostHp() == 1 and self.player:getHandcardNum() > (friend:getHp() - 2) then
			return self.player
		end
	end
	if self:isWeak() then return self.player end 
    self:sort(friends, "defense")
    for _, friend in ipairs(friends) do
        if self:Miko(friend) then return friend end
    end
    for _, friend in ipairs(friends) do
        if friend:getMaxHp() == 3 then return friend end
    end
    if #friends > 0 and not self:isWeak(friends[1]) then return friends[1] end
    return self.player
end
local luaxiaodian_skill = {}
luaxiaodian_skill.name = "luaxiaodian"
table.insert(sgs.ai_skills, luaxiaodian_skill)
luaxiaodian_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luaxiaodian") then return end 
	local friends = self.friends
	for _, friend in ipairs(friends) do
		if friend:isWounded() then return sgs.Card_Parse("#luaxiaodian:.:") end
	end
end 


sgs.ai_skill_use_func["#luaxiaodian"] = function(cardD, use, self)
    local cards = sgs.QList2Table(self.player:getCards("h"))
    self:sortByUseValue(cards, true)
    local toUse = {}
    for _,card in ipairs(cards) do
        if card:isKindOf("Indulgence") then
            local insert = true
            for _, enemy in ipairs(self.enemies) do
                if self:getIndulgenceValue(enemy) > 4 then insert = false end
            end
            if insert then table.insert(toUse, card) end
        end

        if (not card:isKindOf("ExNihilo") and not card:isKindOf("Peach") and not card:isKindOf("Indulgence")
                and not (card:isKindOf("AOE") and self:getAoeValue(card) > 50)) or self.player:containsTrick("gainb") then
            table.insert(toUse, card)
        end

    end
    if #toUse >= self.player:getHp() and self.player:getLostHp() == 1 then
        local toUse2 = {}
        for _,card in ipairs(toUse) do
            table.insert(toUse2, card:getEffectiveId())
            if #toUse2 == self.player:getHp() then break end
        end
        use.card = sgs.Card_Parse("#luaxiaodian:" .. table.concat(toUse2, "+") .. ":")
        if use.to then
            use.to:append(self.player)
        end
        return
    end
    local target = findyequtarget(self)
    if target and target:getLostHp() == 1 and #toUse >= target:getHp() then
        local toUse2 = {}
        for _,card in ipairs(toUse) do
            table.insert(toUse2, card:getEffectiveId())
            if #toUse2 == target:getHp() then break end
        end
        use.card = sgs.Card_Parse("#luaxiaodian:" .. table.concat(toUse2, "+") .. ":")
        if use.to then
            use.to:append(target)
        end
        return
    end

	local friends = self.friends
	self:sort(friends, "defense")
	for _, friend in ipairs(friends) do
		if friend and friend:getLostHp() == 1 and #toUse >= friend:getHp() then
			local toUse2 = {}
			for _,card in ipairs(toUse) do
				table.insert(toUse2, card:getEffectiveId())
				if #toUse2 == friend:getHp() then break end
			end
			use.card = sgs.Card_Parse("#luaxiaodian:" .. table.concat(toUse2, "+") .. ":")
			if use.to then
				use.to:append(friend)
			end
			return
		end
	end

    self:sort(friends, "hp")
    for _, friend in ipairs(friends) do
        if #toUse >= friend:getHp() then
            local toUse2 = {}
            for _,card in ipairs(toUse) do
                table.insert(toUse2, card:getEffectiveId())
                if #toUse2 == friend:getHp() then break end
            end
            use.card = sgs.Card_Parse("#luaxiaodian:" .. table.concat(toUse2, "+") .. ":")
            if use.to then
                use.to:append(friend)
            end
            return
        end
    end

end
sgs.ai_card_intention.luaxiaodian = -80
sgs.ai_playerchosen_intention.luayequ = -80
sgs.ai_use_priority.luaxiaodian = 10
sgs.ai_skill_invoke.luayequ = function(self, data)
    local mystia = self.room:findPlayerBySkillName("luayequ")
	self.room:writeToConsole("luayequ test 4" .. self.player:objectName())
    if mystia and self:isFriend(mystia) then return true end
    return false
end
sgs.ai_skill_playerchosen.luayequ = function(self, targets)
    local kp = findyequtarget(self)
    if kp and targets:contains(kp) then
        return kp
    end
end

local function findbestjinlunTarget(self)
	local function shouyi(playerX)
		local true_handcard_num = playerX:getHandcardNum()
		if playerX:containsTrick("gainb") then true_handcard_num = 0 end
		if self:isFriend(playerX) then
			local w = 3 - true_handcard_num
			if playerX:containsTrick("gaina") then w = w - 2 end
			w = w - math.floor(getCardsNum("Peach", playerX, self.player))
			w = w - math.floor(getCardsNum("Hui", playerX, self.player))
			if w > 0 then
				if playerX:hasSkills("luasanaex|luacaihuo|luajunying|luasuiyue|luaboli|luayouqu|Luaxianzhe|Luazonghuo|luajiangsui" ..
						"luashikong|luayequ|luashizhu|luafenghua|lualiuzhi|luawangshi|LuaFeixiang|LuaLeishi|luaxieli|luakuangxiang")
						or (playerX:hasSkill("LuaTaiji") and playerX:getHp() > 2)
						or (playerX:hasSkills("luashaojie|luashiji") and playerX:getHp() < 3)
						or (playerX:hasSkill("luayigong") and playerX:getEquips():length() > 1) then
					w = w + 1
				end
			end
			if self:isWeak(playerX) then
				w = w - math.floor(getCardsNum("Analeptic", playerX, self.player))
			end
			w = w - 0.5*math.floor(getCardsNum("Jink", playerX, self.player))
			w = w - 0.5*math.floor(getCardsNum("Analeptic", playerX, self.player))
			return w
		else
			local w = true_handcard_num - 4
			if playerX:containsTrick("gaina") then w = w + 2 end
			if w > 0 then
				if playerX:hasSkills("luasanaex|luacaihuo|luajunying|luasuiyue|luaboli|luayouqu|Luaxianzhe|Luazonghuo|luajiangsui" ..
						"luashikong|luayequ|luashizhu|luafenghua|lualiuzhi|luawangshi|LuaFeixiang|LuaLeishi|luaxieli|luakuangxiang")
						or (playerX:hasSkill("LuaTaiji") and playerX:getHp() > 2)
						or (playerX:hasSkills("luashaojie|luashiji") and playerX:getHp() < 3)
						or (playerX:hasSkill("luayigong") and playerX:getEquips():length() > 1) then
					w = w - 1
				end
			end
			return w
		end
	end
	local func = function(a, b)
		return shouyi(a) > shouyi(b)
	end
	local toList = sgs.QList2Table(self.room:getAlivePlayers())
	table.sort(toList, func)
	if shouyi(toList[1]) > 0 then return toList[1] end
	return nil
end
sgs.ai_skill_invoke.luajinlun = true
sgs.ai_skill_playerchosen.luajinlun = function(self, targets)
	local kp = findbestjinlunTarget(self)
	if kp and targets:contains(kp) then
        self.room:writeToConsole("tokiko test F")
		return kp
	end
end
sgs.ai_playerchosen_intention.luajinlun = function(self, from, to)
	local k = (to:getHandcardNum() - 3) * 10
	sgs.updateIntention(from, to , k)
end 
local luajieao_skill = {}
luajieao_skill.name = "luajieao"
table.insert(sgs.ai_skills, luajieao_skill)
local function findjunluncard(self)
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(cards)
	local shinki = self.room:findPlayerBySkillName("luatianzhao")
	for _, card in ipairs(cards) do
		if card:isKindOf("Jink") and not (self:isWeak() and self:getCardsNum("Jink") < 2) then
			return card
		elseif card:isKindOf("Peach") and self:OverFlowPeach(card) and not self:getHandcardNum() == 1 then
			return card
		elseif card:isKindOf("Analeptic") and not (self:isWeak() and (self:getCardsNum("Peach") + self:getCardsNum("Jink") < 2)) then
			if (not shinki) or self:isEnemy(shinki) then return card end
		else
			return card
		end
	end
end
luajieao_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luajieao") then return end
	if self.player:isKongcheng() then return end
	local card = findjunluncard(self)
	if not card then return end
	local q = self:getMaxCard(self.player):getNumber()
	if q <= 10 and self:isWeak() then return end
	if self.player:getHandcardNum() <= 4 then self.room:writeToConsole("朱鹭子测试2"); return sgs.Card_Parse("#luajieao:.:") end
	if #self.enemies == 0 then return end

	if not findbestjinlunTarget(self) and self:getCardsNum("Jink") < 1 then return end
	if not self.player:hasSkill("luajinlun") then return end

	return sgs.Card_Parse("#luajieao:.:")
end

sgs.ai_skill_use_func["#luajieao"] = function(card, use, self)
	local q = self:getMaxCard(self.player):getNumber()
	local p = 0
	local enemies = self.enemies
	self:sort(enemies, "defense")
	local card_0 = findjunluncard(self)
	if not card_0 then return end
	for _, enemy in ipairs(enemies) do
		local boolean = not self:damageIsEffective(enemy, nil, self.player) and #enemies > 1  --无敌且敌人数大于1就先不考虑他
		local cards = sgs.QList2Table(enemy:getHandcards())
		local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
		if not enemy:isKongcheng() and not enemy:hasSkills("luajiejie|Luashenqiang2|Luayuelong|luayueni|luasilian")
			and not enemy:hasWeapon("pundarika") and not boolean then
			for _, cc in ipairs(cards) do
				if not (cc:hasFlag("visible") or cc:hasFlag(flag)) then
					p = 0
					break
				end
				if cc:getNumber() > p then p = cc:getNumber() end
			end
			if q > p then
				use.card = sgs.Card_Parse("#luajieao:".. card_0:getId() ..":")
				if use.to then
					use.to:append(enemy)
				end
				return
			end
		end
	end
	for _, enemy in ipairs(enemies) do
		local boolean = not self:damageIsEffective(enemy, nil, self.player) and #enemies > 1
		local cards = sgs.QList2Table(enemy:getHandcards())
		local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
		if not enemy:isKongcheng() and not enemy:hasWeapon("pundarika") and not boolean then
			for _, cc in ipairs(cards) do
				if not (cc:hasFlag("visible") or cc:hasFlag(flag)) then
					p = 0
					break
				end
				if cc:getNumber() > p then p = cc:getNumber() end
			end
			if q > p then
				use.card = sgs.Card_Parse("#luajieao:".. card_0:getId() ..":")
				if use.to then
					use.to:append(enemy)
				end
				return
			end
		end
	end
	if #enemies > 0 then
		for _, enemy in ipairs(enemies) do
			if not enemy:isKongcheng() and not enemy:hasSkills("luajiejie|Luashenqiang2|Luayuelong|luayueni|luasilian")
					and not enemy:hasWeapon("pundarika") then
				use.card = sgs.Card_Parse("#luajieao:".. card_0:getId() ..":")
				if use.to then
					use.to:append(enemy)
				end
				return
			end
		end
	end
	if #enemies > 0 then return end
	local toList = sgs.QList2Table(self.room:getAlivePlayers())
	self:sort(toList, "defense")
	for _, P in ipairs(toList) do
		if not self:isFriend(P) and not P:isKongcheng() then
			use.card = sgs.Card_Parse("#luajieao:".. card_0:getId() ..":")
			if use.to then
				use.to:append(P)
			end
			return
		end
	end
end
sgs.ai_use_priority.luajieao = 4.2
sgs.ai_card_intention.luajieao = 40

local function lushouBasic(self, slash, try)
	local allcards = sgs.QList2Table(self.player:getCards("he"))
	local reimu = self.room:findPlayerBySkillName("luahakurei")
	local weak = false
	for _, friend in ipairs(self.friends) do
		if self:isWeak(friend) then weak = true; break end
	end
	self:sortByUseValue(allcards, true)
	for _, card in ipairs(allcards) do
		if card:isKindOf("Slash") and self.player:getHandcardNum() > 2 then return card end
		if card:isKindOf("Slash") and try then return card end
		if card:isKindOf("Slash") and card:hasFlag("drank") then
			return card
		end
	end
	for _, card in ipairs(allcards) do
		if card:isKindOf("Jink") and self.player:getHp() > 2
			and not (reimu and reimu:inMyAttackRange(self.player)) then return card end
		if card:isKindOf("Jink") and self:getCardsNum("Jink") > 1 then return card end
		if card:isKindOf("Jink") and self:getCardsNum("Peach") > 0 and self.player:getHp() > 1 then return card end
		if card:isKindOf("Jink") and try then return card end
		if slash and slash:hasFlag("drank") then
			return card
		end
	end
	for _, card in ipairs(allcards) do
		if card:isKindOf("Analeptic") then
			if slash and slash:hasFlag("drank") then
				return card
			end
		end
	end
	for _, card in ipairs(allcards) do
		if card:isKindOf("Peach") then
			if slash and slash:hasFlag("drank") then
				return card
			end
		end
		if card:isKindOf("Peach") and self:OverFlowPeach(card) and not weak then return card end
		if card:isKindOf("Peach") and try then return card end
	end
end

sgs.ai_skill_cardask["lualushouA"] = function(self, data)
	local use = data:toCardUse()
	local enemy = use.to:at(0)
	if self:YouMu2(enemy, true) then return "." end
	local slash = use.card
	local card = lushouBasic(self, slash)
	if not card then return "." end
	return "$" .. card:getEffectiveId()
end

sgs.ai_skill_cardask["lualushouB"] = function(self, data)
	local allcards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByUseValue(allcards, true)
	local slash = sgs.Sanguosha:cloneCard("slash")
	local dummy_use = { isDummy = true, extra_target = 1, to = sgs.SPlayerList() }
	self:useBasicCard(slash, dummy_use)
	if not dummy_use.card then return "." end
	if not dummy_use.card:isKindOf("Slash") then return "." end
	if not dummy_use.to then return "." end
	if dummy_use.to:isEmpty() or dummy_use.to:length() < 2 then return "." end
	for _, card in ipairs(allcards) do
		if card:isKindOf("TrickCard") and ((not card:isKindOf("ExNihilo") and not card:isKindOf("Duel")) or self.player:containsTrick("gainb")) then
			return "$" .. card:getEffectiveId()
		end
	end
end
sgs.ai_skill_playerchosen.lualushou = sgs.ai_skill_playerchosen.slash_extra_targets
local function lushouE(self)
	if not self.player:isWounded() then return end
	if self:needToThrowArmor() and self.player:getLostHp() > 1 and not self.player:isCardLimited(self.player:getArmor(), sgs.Card_MethodDiscard) then
		return self.player:getArmor()
	end
	for _, card in sgs.qlist(self.player:getCards("he")) do
		if card:isKindOf("OffensiveHorse") then
			return card
		end
	end
	for _, card in sgs.qlist(self.player:getCards("he")) do
		if card:isKindOf("DefensiveHorse") then
			return card
		end
	end
	for _, card in sgs.qlist(self.player:getCards("he")) do
		if card:isKindOf("Armor") and (not self:isWeak())
				and (not (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield")) or lushouBasic(self)) then
			return card
		end
	end
	if self:needToThrowArmor() and not self.player:isCardLimited(self.player:getArmor(), sgs.Card_MethodDiscard) then
		return self.player:getArmor()
	end
	for _, card in sgs.qlist(self.player:getCards("he")) do
		if card:isKindOf("Weapon") and lushouBasic(self) then
			return card
		end
	end
end
sgs.ai_skill_cardask["lualushouC"] = function(self)
	if not self.player:isWounded() then return "." end
	local card = lushouE(self)
	if not card then return "." end
	return "$" .. card:getEffectiveId()
end

local luayanguang_skill = {}
luayanguang_skill.name = "luayanguang"
table.insert(sgs.ai_skills, luayanguang_skill)
luayanguang_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luayanguang") then return end
	return sgs.Card_Parse("#luayanguang:.:")
end

local function findYanguangTarget(self)
	local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
	slash:setSkillName("luayanguang")
	local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
	self:useCardSlash(slash, dummy_use)
	if dummy_use.card and not dummy_use.to:isEmpty() then
		if lushouE(self) and lushouBasic(self) then self.room:writeToConsole("赤蛮奇ai测试");return dummy_use.to:at(0) end
		for _, p in sgs.qlist(dummy_use.to) do
			if self:YouMu2(p, true) then
				return p
			end
		end
	end
end
sgs.ai_skill_use_func["#luayanguang"] = function(card, use, self)
	local target = findYanguangTarget(self)
	if target then
		use.card = sgs.Card_Parse("#luayanguang:.:")
		if use.to then
			self.room:writeToConsole("赤蛮奇ai测试2")
			use.to:append(target)
		end
		return
	end
end

sgs.ai_use_priority.luayanguang = 3.2

sgs.ai_skill_playerchosen.luawangyue = function(self, targets)
	local dismantlement = sgs.Sanguosha:cloneCard("dismantlement")
	local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
	self:useTrickCard(dismantlement, dummy_use)
	if dummy_use.card and dummy_use.to and (dummy_use.to:length() > 0) then
		return dummy_use.to:at(0)
	end

	for _, enemy in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		local equips = enemy:getEquips()
		if not enemy:isNude() and not self:doNotDiscard(enemy, "he") and not self:isFriend(enemy) then
			local cardchosen
			if not equips:isEmpty() and not self:doNotDiscard(enemy, "e") then
				cardchosen = self:getCardRandomly(enemy, "e")
			else
				cardchosen = self:getCardRandomly(enemy, "h") end
			if cardchosen then
				return enemy
			end
		end
	end
end

sgs.ai_skill_playerchosen.luayinxi = function(self, targets)
	local enemies = self.enemies
	local slashes = self:getCards("Slash")
	local function Check(enemy)
		if #slashes == 0 then
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			if self.player:canSlash(enemy, slash, true) and self:slashIsEffective(slash, enemy, self.player)
					and not self:slashProhibit(slash, enemy) and not (enemy:hasSkill("anxian") and not enemy:isKongcheng()) then return true
			end
		else
			for _, slashX in ipairs(slashes) do
				if self.player:canSlash(enemy, slashX, true) and self:slashIsEffective(slashX, enemy, self.player)
						and not self:slashProhibit(slashX, enemy) and not (enemy:hasSkill("anxian") and not enemy:isKongcheng()) then return true
				end
			end
		end
		return false
	end
	self:sort(enemies, "defenseSlash")
	self.room:writeToConsole("kagerou test")
	for _, enemy in ipairs(enemies) do
		if Check(enemy) then
			return enemy
		end
	end

	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if not self:isFriend(p) then
			return p
		end
	end
end

sgs.ai_skill_cardask["luayinxiF"] = function(self, data)
	local par = sgs.ai_skill_cardask["luaqijinr"]
	if par == "." then return "." end
end


local luabagu_skill = {}
luabagu_skill.name = "luabagu"
table.insert(sgs.ai_skills, luabagu_skill)
luabagu_skill.getTurnUseCard = function(self)
	local count = 2*self.player:getEquips():length() - self.player:usedTimes("#luabagu")
	if count <= 0 then return end
	return sgs.Card_Parse("#luabagu:.:")
end
sgs.ai_skill_use_func["#luabagu"] = function(cardF, use, self)
	local countX = 2*self.player:getEquips():length() - self.player:usedTimes("#luabagu")
	local count = 0
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:isKindOf("Slash") and not self.player:isCardLimited(card, sgs.Card_MethodDiscard) then
			count = count + 1
		end
	end
	local hp = self.player:getHp() + self:getCardsNum("Peach")
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:isKindOf("Slash") and not self.player:isCardLimited(card, sgs.Card_MethodDiscard) then
			for _, enemy in ipairs(self.enemies) do
				if self:damageIsEffective(enemy, sgs.DamageStruct_Thunder, self.player) and self:isWeak(enemy)
						and not (self:getDamagedEffects(enemy, self.player) or self:needToLoseHp(enemy, self.player, nil, false)) then
					use.card = sgs.Card_Parse("#luabagu:".. card:getId() ..":" )
					if use.to then
						use.to:append(enemy)
						return
					end
				end
			end
		end
	end

	for _, card in sgs.list(self.player:getHandcards()) do
		if card:isKindOf("Slash") and not self.player:isCardLimited(card, sgs.Card_MethodDiscard) then
			if ((hp >= 4) or (hp == 3 and count > 1) or (hp == 1 and self:getCardsNum("Analeptic") >= 1 and count > 1))
				and countX > 1 and self:getOverflow() < 2 then
				use.card = sgs.Card_Parse("#luabagu:".. card:getId() ..":" )
				if use.to then
					use.to:append(self.player)
					return
				end
			end
		end
	end
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:isKindOf("Slash") and not self.player:isCardLimited(card, sgs.Card_MethodDiscard) then
			for _, enemy in ipairs(self.enemies) do
				if self:damageIsEffective(enemy, sgs.DamageStruct_Thunder, self.player)
					and not (self:getDamagedEffects(enemy, self.player) or self:needToLoseHp(enemy, self.player, nil, false)) then
					use.card = sgs.Card_Parse("#luabagu:".. card:getId() ..":" )
					if use.to then
						use.to:append(enemy)
						return
					end
				end
			end
		end
	end
end

sgs.ai_use_priority.luabagu = 12
sgs.ai_card_intention.luabagu = 60

local function QipaiDuiCheck(self, test)
	if self.player:getHandcardNum() > 6 then return end

	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end

	local x1 = self.player:getMaxCards() - (self.player:getHandcardNum() - #self:getTurnUse(true))
	local bool_o = false
	for _, friend in ipairs(self.friends) do
		if self:isWeak(friend) then
			for _, friend2 in ipairs(self.friends) do
				if friend2:objectName() ~= friend:objectName() then
					bool_o  = true
				end
			end
		end
	end
	local function Check_R(cardX)
		if cardX:isKindOf("ExNihilo") or (cardX:isKindOf("AOE") and self:getAoeValue(cardX) > 45) then return true end
		if cardX:isKindOf("Analeptic") or cardX:isKindOf("Duel") then return true end
		if cardX:isKindOf("EightDiagram") or cardX:isKindOf("RenwangShield") then return true end
		if cardX:isKindOf("GodSalvation") and self:godSalvationValue(cardX) >= 10 then return true end
		if not cardX:isKindOf("Dismantlement") and self:ThreeCheck(cardX) then return true end
		for _, friend in ipairs(self.friends) do
			for _, skill in sgs.qlist(friend:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skill:objectName()]
				if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](friend, cardX, self) then
					return true
				end
			end
		end
		return false
	end
	local function Check_R2(cardX)
		if cardX:isKindOf("ExNihilo") or (cardX:isKindOf("AOE") and self:getAoeValue(cardX) > 45) then return true end
		if cardX:isKindOf("Analeptic") or cardX:isKindOf("Duel") then return true end
		if cardX:isKindOf("GodSalvation") and self:godSalvationValue(cardX) >= 10 then return true end
		if cardX:isKindOf("Snatch") or cardX:isKindOf("Indulgence") then return true end
		if cardX:isKindOf("IronChain") and shuxin then return true end
		if cardX:isKindOf("Lightning") and self:willUseLightning(cardX) then return true end
		if cardX:isKindOf("EightDiagram") or cardX:isKindOf("RenwangShield") then return true end
		if cardX:isKindOf("EquipCard") then
			local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
			if (self.room:getCardPlace(cardX:getId()) == sgs.Player_PlaceHand) and rinnosuke
					and rinnosuke:isAlive() and self:isFriend(rinnosuke) then return true end
		end
		for _, friend in ipairs(self.friends) do
			for _, skill in sgs.qlist(friend:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skill:objectName()]
				if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](friend, cardX, self) then
					return true
				end
			end
		end
		return false
	end
	local function GetCardsA(card_1)
		local trickcard = sgs.IntList()
		for i = 0, 4 do
			local card_0 = sgs.Sanguosha:getCard(self.room:getDiscardPile():at(i))
			if card_0:getId() ~= card_1:getId() then
				trickcard:append(card_0:getId())
			end
		end
		return trickcard
	end
	if (self:isWeak() and #self.friends_noself == 0) or bool_o then
		for i = 4, 0, -1 do
			local card_0 = sgs.Sanguosha:getCard(self.room:getDiscardPile():at(i))
			if card_0:isKindOf("Peach") then
				local count = 1
				local count2 = count
				local dafen = 1
				if Check_R2(card_0) then dafen = dafen + 1 end
				local card_ids = GetCardsA(card_0)
				for _, card_id in sgs.qlist(card_ids) do
					local card_1 = sgs.Sanguosha:getCard(card_id)
					if card_1:getSuit() == card_0:getSuit() then
						count = count + 1
						dafen = dafen + 1
						if Check_R2(card_1) then dafen = dafen + 1 end
					end
				end
				if #self.friends_noself > 0 then count2 = count2 - 1 end
				sgs.ai_use_priority.luashizhu = 6
				if dafen > 3 then return i end
			end
		end
	end
	for i = 4, 0, -1 do
		local card_0 = sgs.Sanguosha:getCard(self.room:getDiscardPile():at(i))
		local count = 1
		local count2 = count
		local dafen = 1
		if Check_R2(card_0) then dafen = dafen + 1 end
		local card_ids = GetCardsA(card_0)
		for _, card_id in sgs.qlist(card_ids) do
			local card_1 = sgs.Sanguosha:getCard(card_id)
			if card_1:getSuit() == card_0:getSuit() then
				count = count + 1
				dafen = dafen + 1
				if Check_R2(card_1) then dafen = dafen + 1 end
			end
		end
		if #self.friends_noself > 0 then count2 = count2 - 1 end
		sgs.ai_use_priority.luashizhu = 8
		if x1 >= count2 - 3 and dafen > 4 then
			if test then self.room:writeToConsole("shizhu AItest A" .. i) end
			return i
		end
	end

	for i = 4, 0, -1 do
		local card_0 = sgs.Sanguosha:getCard(self.room:getDiscardPile():at(i))
		local count = 1
		local count2 = count
		local dafen = 1
		if Check_R2(card_0) then dafen = dafen + 1 end
		local card_ids = GetCardsA(card_0)
		for _, card_id in sgs.qlist(card_ids) do
			local card_1 = sgs.Sanguosha:getCard(card_id)
			if card_1:getSuit() == card_0:getSuit() then
				count = count + 1
				dafen = dafen + 1
				if Check_R2(card_1) then dafen = dafen + 1 end
			end
		end
		if #self.friends_noself > 0 then count2 = count2 - 1 end
		sgs.ai_use_priority.luashizhu = 4
		if x1 >= count2 - 1 and dafen > 2 then
			if test then self.room:writeToConsole("shizhu AItest B " .. i) end
			return i
		end
	end

	for i = 4, 0, -1 do
		local card_0 = sgs.Sanguosha:getCard(self.room:getDiscardPile():at(i))
		local count = 1
		local count2 = count
		local dafen = 1
		if Check_R2(card_0) then dafen = dafen + 1 end
		local card_ids = GetCardsA(card_0)
		for _, card_id in sgs.qlist(card_ids) do
			local card_1 = sgs.Sanguosha:getCard(card_id)
			if card_1:getSuit() == card_0:getSuit() then
				count = count + 1
				dafen = dafen + 1
				if Check_R2(card_1) then dafen = dafen + 1 end
			end
		end
		if #self.friends_noself > 0 then count2 = count2 - 1 end
		sgs.ai_use_priority.luashizhu = 0
		if x1 >= count2 - 1 and dafen > 1 then
			if test then self.room:writeToConsole("shizhu AItest C" .. i) end
			return i
		end
	end

	for i = 4, 0, -1 do
		local card_0 = sgs.Sanguosha:getCard(self.room:getDiscardPile():at(i))
		local count = 1
		local count2 = count
		local dafen = 1
		if Check_R2(card_0) then dafen = dafen + 1 end
		local card_ids = GetCardsA(card_0)
		for _, card_id in sgs.qlist(card_ids) do
			local card_1 = sgs.Sanguosha:getCard(card_id)
			if card_1:getSuit() == card_0:getSuit() then
				count = count + 1
				dafen = dafen + 1
				if Check_R2(card_1) then dafen = dafen + 1 end
			end
		end
		if #self.friends_noself > 0 then count2 = count2 - 1 end
		sgs.ai_use_priority.luashizhu = 0
		if x1 >= count2 - 1 and dafen > 0 then
			if test then self.room:writeToConsole("shizhu AItest D" .. i) end
			return i
		end
	end
end
local luashizhu_skill = {}
luashizhu_skill.name = "luashizhu"
table.insert(sgs.ai_skills, luashizhu_skill)
luashizhu_skill.getTurnUseCard = function(self)
	if self.player:getMark("@shizhu") == 0 then return end
	local sagume = self.room:findPlayerBySkillName("luanizhou")
	if sagume and sagume:isAlive() and self:isFriend(sagume) then
		local sp = self.room:getCurrent():getMark("@luatianshii")
		local x = sagume:getMark("luanizhou")
		if sp == x then
			sgs.ai_use_priority.luashizhu = 10
		else
			sgs.ai_use_priority.luashizhu = 0
		end
	end
	local discard_ids = self.room:getDiscardPile()
	if discard_ids:length() == 0 then return end
	local x = self.room:getAlivePlayers():length() - #self.friends - #self.enemies
	if x > 1 then return end
	local y = QipaiDuiCheck(self)
	if not y then return end
	return sgs.Card_Parse("#luashizhu:.:")
end
sgs.ai_skill_use_func["#luashizhu"] = function(cardF, use, self)
	use.card = sgs.Card_Parse("#luashizhu:.:")
	if use.to then use.to = sgs.SPlayerList() end
	return
end
sgs.ai_skill_choice.luashizhu = function(self, player, promptlist)
	local q = QipaiDuiCheck(self, true)
	self.room:writeToConsole("shizhu AItest X" .. q)
	if not q then self.room:writeToConsole("shizhu test bug"); q = 4 end
	return tostring(q + 1)
end

sgs.ai_use_priority.luashizhu = 8

sgs.ai_skill_cardask["@luarenyu"] = function(self, data, pattern, target)
	local cards = {}
	for _, c in sgs.qlist(self.player:getHandcards()) do
		if self.player:getMark("luashizhu" .. c:getId()) > 0 then
			table.insert(cards, c)
		end
	end
	if #cards > 0 then
		self.room:writeToConsole("luarenyu ai test")
		self:sortByUseValue(cards)
		for _, card in ipairs(cards) do
			for _, friend in ipairs(self.friends) do
				for _, skill in sgs.qlist(friend:getVisibleSkillList(true)) do
					local callback = sgs.ai_cardneed[skill:objectName()]
					if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](friend, card, self) then
						return "$" .. card:getEffectiveId()
					end
				end
			end
		end
		for _, card in ipairs(cards) do
			if card:isKindOf("EquipCard") then
				for _, friend in ipairs(self.friends) do
					if friend:hasSkills(sgs.need_equip_skill) then
						return "$" .. card:getEffectiveId()
					end
				end
			end
		end
		for _, card in ipairs(cards) do
			if card:isKindOf("Peach") then
				for _, friend in ipairs(self.friends_noself) do
					if self:isWeak(friend) then
						return "$" .. card:getEffectiveId()
					end
				end
			end
		end
		for _, card in ipairs(cards) do
			if card:isKindOf("Analeptic") then
				for _, friend in ipairs(self.friends_noself) do
					if self:isWeak(friend) and friend:getHp() == 1 then
						return "$" .. card:getEffectiveId()
					end
				end
			end
		end
		return "$" .. cards[1]:getEffectiveId()
	end

	local cards2 = sgs.QList2Table(self.player:getHandcards())
	self:sortByUseValue(cards2)

	for _, card in ipairs(cards2) do
		for _, friend in ipairs(self.friends) do
			for _, skill in sgs.qlist(friend:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skill:objectName()]
				if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](friend, card, self) then
					return "$" .. card:getEffectiveId()
				end
			end
		end
	end
	for _, card in ipairs(cards2) do
		if card:isKindOf("EquipCard") then
			for _, friend in ipairs(self.friends) do
				if friend:hasSkills(sgs.need_equip_skill) then
					return "$" .. card:getEffectiveId()
				end
			end
		end
	end
	for _, card in ipairs(cards2) do
		if card:isKindOf("Peach") then
			for _, friend in ipairs(self.friends_noself) do
				if self:isWeak(friend) then
					return "$" .. card:getEffectiveId()
				end
			end
		end
	end
	for _, card in ipairs(cards2) do
		if card:isKindOf("Analeptic") then
			for _, friend in ipairs(self.friends_noself) do
				if self:isWeak(friend) and friend:getHp() == 1 then
					return "$" .. card:getEffectiveId()
				end
			end
		end
	end
	return "$" .. cards2[1]:getEffectiveId()
end


sgs.ai_skill_playerchosen.luarenyu = function(self, targets)
	local cardX = self.room:getTag("luarenyuTC"):toCard()
	self.room:writeToConsole("renyu test " .. cardX:getId())
	if self.player:getMark("luashizhu" .. cardX:getId()) > 0 then
		self.room:writeToConsole("renyu testY " .. cardX:getId())
	end
		for _, friend in ipairs(self.friends_noself) do
			for _, skill in sgs.qlist(friend:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skill:objectName()]
				if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](friend, cardX, self) then
					return friend
				end
			end
		end

	if cardX:isKindOf("EquipCard") then
		for _, friend in ipairs(self.friends_noself) do
			if friend:hasSkills(sgs.need_equip_skill) then
				return friend
			end
		end
	end

	if cardX:isKindOf("Peach") then
		for _, friend in ipairs(self.friends_noself) do
			if self:isWeak(friend) then
				return friend
			end
		end
	end

	if cardX:isKindOf("Analeptic") then
		for _, friend in ipairs(self.friends_noself) do
			if self:isWeak(friend) and friend:getHp() == 1 then
				return friend
			end
		end
	end

	for _, friend in ipairs(self.friends_noself) do
		if friend:hasSkills(sgs.Active_cardneed_skill) then
			return friend
		end
	end

	self:sort(self.friends, "defense")
	self:sort(self.friends_noself, "defense")
	if self.player:getMark("luashizhu" .. cardX:getId()) > 0 and #self.friends_noself > 0 then
		return self.friends_noself[1]
	end
	return self.friends[1]
end


sgs.ai_playerchosen_intention.luarenyu = -80

sgs.ai_skill_playerchosen.luashizhu = function(self, targets)
    local targetlist = sgs.QList2Table(targets)
    for _, target in ipairs(targetlist) do
        if target:objectName() == self.player:objectName() then return target end
    end
    for _, target in ipairs(targetlist) do
        if self:isFriend(target) then return target end
    end
end

local function findnizhuanTarget(self)
	local slash = sgs.Sanguosha:cloneCard("slash")
	local function Point(target, target2)
		if not self:isEnemy(target) then return 0 end
		if target:hasSkill("luacuiruo") then return 0 end
		local count = 0.75
		if target:getHandcardNum() == 1 and self.player:inMyAttackRange(target) then
			for _, friend in ipairs(self.friends) do
				if self:Skadi2(slash, target, friend, true) then
					count = count + 1
					if friend:objectName() == self.player:objectName() then
						if self:getCardsNum("Slash") == 0 then count = count - 1 end
					end
				end
			end
		end
		for _, friend in ipairs(self.friends) do
			if self:Skadi2(slash, target, friend, true) then
				count = count + 1
				if friend:objectName() == self.player:objectName() then
					if self:getCardsNum("Slash") == 0 then count = count - 1 end
				end
			end
		end
		if target:hasSkills(sgs.need_slash_skill) or target:hasSkill("Luazonghuo") then count = count + 0.5 end
		if target:getWeapon() and target:getWeapon():isKindOf("Crossbow") then count = count + target:getHandcardNum()*0.25 end
		return count
	end
	local function Point2(target)
		local count = 0
		for _, ap in sgs.qlist(self.room:getAlivePlayers()) do
			if target:distanceTo(ap) == 1 then
				count = Point(ap, target)
			end
		end
		if self:isEnemy(target) and self.player:inMyAttackRange(target) and target:isKongcheng() then
			for _, friend in ipairs(self.friends) do
				if self:Skadi2(slash, target, friend, true) then
					count = count + 1
					if friend:objectName() == self.player:objectName() then
						if self:getCardsNum("Slash") == 0 then count = count - 1 end
					end
				end
			end
		end
		if self:isFriend(target) then
			count = count + 1
			if target:isKongcheng() and self.player:inMyAttackRange(target) then count = count - 0.75 end
			if self:isWeak(target) then count = count + 0.5 end
		end
        if self.player:getHandcardNum() == 1 then count = count - 1 end
        if self.player:getHandcardNum() == 2 then
            local cardsA = self:getTurnUse(true)
            if #cardsA == 0 then
                count = count + 1
            else
                count = count - 0.5
            end
        end
		return count
	end
	local compare_func = function(a, b)
		return Point2(a) > Point2(b)
	end
	local players = sgs.QList2Table(self.room:getAlivePlayers())
	table.sort(players, compare_func)
	if Point2(players[1]) > 1.25 then return players[1] end
end
local luanizhuan_skill = {}
luanizhuan_skill.name = "luanizhuan"
table.insert(sgs.ai_skills, luanizhuan_skill)
luanizhuan_skill.getTurnUseCard = function(self)
	local target = findnizhuanTarget(self)
	if target and self.player:usedTimes("#luanizhuan") < 3 then return sgs.Card_Parse("#luanizhuan:.:") end
end
sgs.ai_skill_use_func["#luanizhuan"] = function(X, use, self)
	local slash = sgs.Sanguosha:cloneCard("slash")
	local target = findnizhuanTarget(self)
	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end
	local function pofang(targetX)
		local invoke = false
		for _, friend in ipairs(self.friends) do
			if self:Skadi2(slash, targetX, friend, true) then
				invoke = true
			end
		end
		return targetX:isKongcheng() and self:isEnemy(targetX) and invoke
	end
	if self:isEnemy(target) then
		local cards = self.player:getCards("he")
		cards = sgs.QList2Table(cards)
		self:sortByUseValue(cards,true)
		local function Check_R(card)
			if card:isKindOf("TrickCard") then
				if card:isKindOf("Nullification") then return true end
				if card:isKindOf("IronChain") and not shuxin then return true end
				if card:isKindOf("Dismantlement") or card:isKindOf("NeedMaribel") then return true end
				if card:isKindOf("Lightning") and not self:willUseLightning(card) then return true end
				if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
				if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
				if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
				local dummy_use = {isDummy = true}
				self:useTrickCard(card, dummy_use)
				if not dummy_use.card then return true end
				return false
			end
			if card:isKindOf("EquipCard") then --or card:isKindOf("DefensiveHorse")
				if card:isKindOf("OffensiveHorse") and card:objectName() ~= "hongrai" then
					return true
				end
				if card:isKindOf("DefensiveHorse") and card:objectName() ~= "shanghai" then
					return true
				end
				local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
				if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) and rinnosuke
						and rinnosuke:isAlive() and self:isFriend(rinnosuke) then return false end
				if card:isKindOf("Armor") and self:needToThrowArmor() then return true end
				if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
					local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
							or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
					if not bool_3 then return true end
				end
				if card:isKindOf("Weapon") then
					local dummy_use = {isDummy = true}
					self:useEquipCard(card, dummy_use)
					if not dummy_use.card then return true end
					if dummy_use.card and self.player:getWeapon() then return true end
				end
			end
			if card:isKindOf("Jink") or card:isKindOf("Ofuda") and pofang(target) then return true end
			if card:isKindOf("Slash") and self:getCardsNum("Slash") > 1 then return true end
			if card:isKindOf("Hui") then return true end
			return false
		end
		for _, card in ipairs(cards) do
			if Check_R(card) then
				use.card = sgs.Card_Parse("#luanizhuan:" .. card:getEffectiveId()..":")
				if use.to then use.to:append(target) end
				return
			end
		end
	else
		local cards = self.player:getCards("he")
		cards = sgs.QList2Table(cards)
		self:sortByUseValue(cards)
		for _, card in ipairs(cards) do
			if not card:isKindOf("Wanbaochui") then
				use.card = sgs.Card_Parse("#luanizhuan:" .. card:getEffectiveId()..":")
				if use.to then use.to:append(target) end
				return
			end
		end
	end
end
sgs.ai_use_priority.luanizhuan = 6.5
sgs.ai_playerchosen_intention.luanizhuan = 20
sgs.ai_skill_playerchosen.luanizhuan = function(self, targets)
    local targetlist = sgs.QList2Table(targets)
    for _, target in ipairs(targetlist) do	--杀敌
        if self:isEnemy(target) then
            return target
        end
    end
end
sgs.ai_skill_playerchosen.luafanze = function(self, targets)
	local targetlist = sgs.QList2Table(targets)
	self:sort(targetlist, "handcard2")
    self.room:writeToConsole("fanze testX")
	for _, target in ipairs(targetlist) do	--杀敌
		if self:isEnemy(target) then
			for _, c in sgs.qlist(target:getHandcards()) do
				local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), target:objectName())
				if (c:hasFlag("visible") or c:hasFlag(flag)) then
					if c:isKindOf("ExNihilo") or c:isKindOf("Duel") or c:isKindOf("SavageAssault") or c:isKindOf("EquipCard")
							or c:isKindOf("Dismantlement") or c:isKindOf("Analeptic") or c:isKindOf("Indulgence") or c:isKindOf("SupplyShortage")
							or c:isKindOf("ArcheryAttack") or c:isKindOf("Banquet") or c:isKindOf("Wanbaochui") or c:isKindOf("Hui") then
						return target
					end
				end
			end
			if getCardsNum("Peach", target, self.player) > 0.5 then
				for _, friend in ipairs(self.friends) do
					if friend:isWounded() then
						return target
					end
				end
			end
		end
	end
	for _, target in ipairs(targetlist) do	--杀敌
		if self:isEnemy(target) and not target:hasSkill("luacuiruo") then
			if getCardsNum("Slash", target, self.player) > 0.5 then
				return target
			end
		end
	end
    for _, target in ipairs(targetlist) do	--杀敌
        if self:isEnemy(target) and target:getHandcardNum() > 1 then
            return target
        end
    end
    for _, target in ipairs(targetlist) do	--杀敌
        if self:isFriend(target) and not target:hasSkill("luacuiruo") then
            if getCardsNum("Slash", target, self.player) > 0.5 then
                return target
            end
        end
    end
end

sgs.ai_skill_askforag.luafanze = function(self, card_ids)
	local target = self.room:getTag("luafanzeTP"):toPlayer()
	if not self:isFriend(target) then
		for _, card_id in ipairs(card_ids) do
			local c = sgs.Sanguosha:getCard(card_id)
			if c:isKindOf("ExNihilo") or c:isKindOf("Duel") or c:isKindOf("SavageAssault")  or c:isKindOf("Indulgence") or c:isKindOf("SupplyShortage")
					or c:isKindOf("ArcheryAttack") or c:isKindOf("Banquet") or c:isKindOf("Wanbaochui") or c:isKindOf("Hui") then
				return card_id
			end
		end
	end
    if not self:isFriend(target) then
        for _, card_id in ipairs(card_ids) do
            local c = sgs.Sanguosha:getCard(card_id)
            if c:isKindOf("ExNihilo") or c:isKindOf("Duel") or c:isKindOf("SavageAssault") or c:isKindOf("EquipCard")
                    or c:isKindOf("Dismantlement") or c:isKindOf("Analeptic") or c:isKindOf("Indulgence") or c:isKindOf("SupplyShortage")
                    or c:isKindOf("ArcheryAttack") or c:isKindOf("Banquet") or c:isKindOf("Wanbaochui") or c:isKindOf("Hui") then
                return card_id
            end
        end
    end
	for _, card_id in ipairs(card_ids) do
		local c = sgs.Sanguosha:getCard(card_id)
		if c:isKindOf("Peach") or c:isKindOf("GodSalvation") then
			for _, friend in ipairs(self.friends) do
				if friend:isWounded() then
					return card_id
				end
			end
		end
	end
	for _, card_id in ipairs(card_ids) do
		local c = sgs.Sanguosha:getCard(card_id)
		if c:isKindOf("Slash") then
			return card_id
		end
	end
	if not self:isFriend(target) then
		for _, card_id in ipairs(card_ids) do
			local c = sgs.Sanguosha:getCard(card_id)
			if c:isKindOf("NeedMaribel") or c:isKindOf("Snatch") or c:isKindOf("Ofuda") then
				return card_id
			end
		end
	end
	return -1
end

sgs.ai_skill_playerchosen.luafanze2 = function(self, targets)
    local cardX = self.room:getTag("luafanzeTC"):toCard()
    local target = self.room:getTag("luafanzeTP"):toPlayer()
    targets = sgs.QList2Table(targets)
    local dummy_use_a = { isDummy = true , to = sgs.SPlayerList() }
    local function ABF(Rplayer)
        for _, friend in ipairs(targets) do
            if friend:objectName() == Rplayer:objectName() then return true end
        end
        return false
    end
    if cardX:isKindOf("Peach") or cardX:isKindOf("GodSalvation") then
        self:sort(self.friends_noself, "defense")
        for _, friend in ipairs(self.friends_noself) do
            if friend:isWounded() and (not (friend:hasSkill("luaminghe")) or friend:getLostHp() > 1)
                    and ABF(friend) then
                return friend
            end
        end
    elseif cardX:isKindOf("ExNihilo") then
        self:sort(self.friends_noself, "defense")
        for _, friend in ipairs(self.friends_noself) do
            if ABF(friend) then
                return friend
            end
        end
    elseif cardX:isKindOf("Slash") then
        return self:Skadi(cardX, nil, target, false)
    elseif cardX:isKindOf("Hui") or cardX:isKindOf("Duel")
            or cardX:isKindOf("SavageAssault") or cardX:isKindOf("ArcheryAttack") then
        self:sort(self.enemies, "defense")
        for _, enemy in ipairs(self.enemies) do
            if ABF(enemy) then return enemy end
        end
    end
    if cardX:isKindOf("EquipCard") then
        self:sort(self.friends_noself, "defense")
        for _, friend in ipairs(self.friends_noself) do
            if ABF(friend) and not self:getSameEquip(cardX, friend) then
                return friend
            end
        end
        for _, friend in ipairs(self.friends_noself) do
            if ABF(friend) then
                return friend
            end
        end
    end
    if cardX:isKindOf("BasicCard") then
        self:useBasicCard(cardX, dummy_use_a)
        if not dummy_use_a.to:isEmpty() then
            for _, p in sgs.qlist(dummy_use_a.to) do
                if ABF(p) then return p end
            end
        end
    elseif cardX:isKindOf("TrickCard") then
        if (cardX:isKindOf("Snatch") or cardX:isKindOf("Dismantlement") or cardX:isKindOf("FaithCollection")) and self.player:hasSkill("luaxiongshi") then
            for _, enemy in ipairs(self.enemies) do
                if self:hasTrickEffective(cardX, enemy) and not enemy:isNude() and ABF(enemy) then
                    return enemy
                end
            end
        else
            self:useTrickCard(cardX, dummy_use_a)
            if not dummy_use_a.to:isEmpty() then
                for _, p in sgs.qlist(dummy_use_a.to) do
                    if ABF(p) then self.room:writeToConsole("spyoumu Trick Card Test " .. p:objectName()) ; return p end
                end
            end
        end
    end
    self.room:writeToConsole("spyoumu Trick Card Test! " .. cardX:objectName())
    return nil
end



