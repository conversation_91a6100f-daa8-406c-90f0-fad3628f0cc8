---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by <PERSON>sage.
--- DateTime: 2021/7/18 16:53
---

local luazhaocai_skill = {}
luazhaocai_skill.name = "luazhaocai"
table.insert(sgs.ai_skills, luazhaocai_skill)
luazhaocai_skill.getTurnUseCard = function(self)
    --if self.player:hasUsed("#lualiangxiao") then return end
    return sgs.Card_Parse("#luazhaocai:.:")
end
sgs.ai_skill_use_func["#luazhaocai"] = function(X, use, self)

end
sgs.ai_skill_use["@@luayinke"]=function(self,prompt)
    local function Acheck(playerX, card_0)
        for _, askill in sgs.qlist(playerX:getVisibleSkillList(true)) do
            local callback = sgs.ai_cardneed[askill:objectName()]
            if type(callback)=="function" and callback(playerX, card_0, self) then
                return true
            end
        end
        return false
    end
    for  _, target in sgs.qlist(self.room:getAlivePlayers()) do
        if target:getHandcardNum() == 1 then
            for _, card in sgs.list(target:getHandcards()) do
                local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), target:objectName())
                if card:hasFlag("visible") or card:hasFlag(flag) then
                    for _, cardX in sgs.qlist(self.player:getHandcards()) do
                        if cardX:getSuit() ~= card:getSuit() then
                            return "#luayinke:".. cardX:getId() ..":->" .. target:objectName()
                        end
                    end
                end
            end
        end
    end
    local enermy = self.enemies
    self:sort(enermy, "handcard")
    for _, enemy in ipairs(self.enemies) do
        for _, card in sgs.list(enemy:getHandcards()) do
            local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
            if card:hasFlag("visible") or card:hasFlag(flag) then
                for _, cardX in sgs.qlist(self.player:getHandcards()) do
                    if cardX:getSuit() ~= card:getSuit() then
                        return "#luayinke:".. cardX:getId() ..":->" .. enemy:objectName()
                    end
                end
            end
        end
    end
end
sgs.ai_skill_cardask["@luayinkegive"] = function(self, data)
    local handcards = self.player:getCards("h")
    handcards = sgs.QList2Table(handcards)
    self:sortByKeepValue(handcards)
    return "$" .. handcards[1]:getEffectiveId()
end

sgs.ai_skill_cardask["@lualicai2"] = function(self, data)
    local handcards = self.player:getCards("h")
    handcards = sgs.QList2Table(handcards)
    self:sortByKeepValue(handcards)
    return "$" .. handcards[1]:getEffectiveId()
end
sgs.ai_damage_effect["luaguansha"] = function(self, to, nature, from)
    if to:hasSkill("luaguansha") then
         local current = self.room:getCurrent()
        if to:getMark("luaguansha") == 0 and to:getHandcardNum() < current:getHandcardNum() then return true end
    end
end
sgs.ai_skill_use["@@lualicai"] = function(self, prompt, method)
    local handcards = self.player:getCards("h")
    handcards = sgs.QList2Table(handcards)
    self:sortByKeepValue(handcards)
    if (handcards[1]:isKindOf("Peach") or handcards[1]:isKindOf("Analeptic"))
        and self.player:getHp() == 1 then
        local sp_seiga = self.room:findPlayerBySkillName("luajianglin")
        if not sp_seiga then return end
        if not sp_seiga:isAlive() then return end
    end
    local target = self.room:getCurrent()
    if self:isFriend(target) then
        if (target:getHandcardNum() >= self.player:getHandcardNum() - 1)
            or #self.friends_noself == 2 then
            return "#lualicai:".. handcards[1]:getId() .. ":"
        end
    end
end
sgs.ai_skill_invoke.luahonggguang = function(self, data)
	local Megumu = self.room:findPlayerBySkillName("luahonggguang")
	if Megumu and self:isFriend(Megumu) then
		return true
	end 
	return false
end 

sgs.ai_skill_invoke.luashichang = function(self, data)

	local Chimata = self.room:findPlayerBySkillName("luahonghuan")
	--调试信息
	self.room:writeToConsole("----start Skill----")
	self.room:writeToConsole("Alive Players = ")
	self.room:writeToConsole(self.room:getAlivePlayers():length())
	
	self.room:writeToConsole("Cur Seat = ")--当前座位，最大为存活人数
	local target = self.room:getCurrent()
	self.room:writeToConsole(target:getSeat())
	
	self.room:writeToConsole("Self Seat = ")--自己的座位，会变化
	self.room:writeToConsole(self.player:getSeat())
	--找出所有的好位置：前3个人至少两个是友军
	local srcseats = {}--友军的位置
	local tarseats = {}--好位置
	
	for _,i in ipairs(self.friends) do--整理友军位置
		self.room:writeToConsole("Friend seat=")
		self.room:writeToConsole(i:getSeat())
		table.insert(srcseats, i:getSeat())
		--local j = self.room:getFront(i,Chimata)--找出位置靠前的玩家，可能之后用得到
	end
	table.sort(srcseats)
	self.room:writeToConsole("srcseats = ")
	self.room:writeToConsole(table.concat(srcseats))
	
	if next(srcseats) ~= nil then
		local num = #srcseats--友军人数
		local p = self.room:alivePlayerCount()--存活人数
		self.room:writeToConsole("Friend num =")
		self.room:writeToConsole(num)
		if num == 1 then--当只有自己时，锁定为自己
			self.room:writeToConsole(srcseats[1])
			table.insert(tarseats, srcseats[1])
		else--存在友军时
			for i=1,num-1 do--检测友军距离，不包括首尾相连，>=-2意味着距离不超过2，中间最多有1个敌人
				if srcseats[i] - srcseats[i+1] >= -2 then
					self.room:writeToConsole(srcseats[i])
					table.insert(tarseats,srcseats[i])
				end
			end
			if srcseats[num] - p - srcseats[1] >= -2 then--检测首尾相连的友军距离
				self.room:writeToConsole(srcseats[num])
				table.insert(tarseats,srcseats[num])
			end
		end
		table.sort(tarseats)
		self.room:writeToConsole("target seats = ")
		self.room:writeToConsole(table.concat(tarseats))
	end
	
	
	if next(tarseats) ~= nil then--虹环有几张
		local cardnum = 0--虹环数量
		local p = self.room:alivePlayerCount()--存活人数
		local n = target:getSeat()--当前位置
		--tarseats 目标位置
		if next(sgs.QList2Table(Chimata:getPile("luahonghuan"))) ~= nil then
			cardnum = Chimata:getPile("luahonghuan"):length()
		end
		self.room:writeToConsole("Honghuan = ")
		self.room:writeToConsole(cardnum)
		local delta = p-cardnum--剩余回合
		self.room:writeToConsole("Last Turn = ")
		self.room:writeToConsole(delta)
		local result = {}
		for i=1,#tarseats do
			self.room:writeToConsole("Tar Seats = ")
			self.room:writeToConsole(tarseats[i])
			if tarseats[i] == n + delta - 1 or tarseats[i] + p == n + delta - 1 then--位置有一个是正确的那就全开
				self.room:writeToConsole("answer check")
				self.room:writeToConsole(n + delta - 1)
				self.room:writeToConsole(n + delta - p - 1)
				return true
			end
			if not self:isFriend(self.room:getCurrent()) and delta == 1 then--可能没有意义，也许对额外回合有用
				return false
			end
			if self:isFriend(self.room:getCurrent()) then--通过跳过友军来延后发动
				return false
			end
		end
	end
	-- 当前位置，目标位置，虹环数量，最大人数
	-- 最大人数-虹环数量=剩余回合	6-5=1
	-- 当前位置+剩余回合=目标位置  5+1=6 true
	--not self:isFriend(self.room:getCurrent()) and
	return true
end
sgs.ai_skill_choice.luashichang = function(self, choices, data)
	if not self.room:getCurrent():isNude() then
	self.room:writeToConsole("----choseTarget----")
		return "luashichang2"
	else
	self.room:writeToConsole("----choseSelf----")
		return "luashichang1"
	end  
end 
sgs.ai_skill_discard.luashichang = function(self, discard_num, optional, include_equip)
	self.room:writeToConsole("---discard self---")
	local Chimata = self.room:findPlayerBySkillName("luahonghuan")
	local to_discard = {}
	local cards = Chimata:getCards("he")
	local cards = sgs.QList2Table(cards)
	self:sortByUseValue(cards, true)
	for _,card in ipairs(cards) do
		if card:getSuit() == sgs.Card_Diamond then
			table.insert(to_discard, card:getId())
			break
		end
	end
	if next(to_discard) == nil then
		for _,card in ipairs(cards) do
			table.insert(to_discard, card:getId())
			break
		end
	end
	self.room:writeToConsole("----to discard----")
	self.room:writeToConsole(table.concat(to_discard))
	return to_discard
end
sgs.ai_skill_cardchosen.luashichang = function(self, discard_num, optional, include_equip)
	self.room:writeToConsole("---target---")
	
	local to_discard = {}
	local cards = self.room:getCurrent():getCards("he")
	local cards = sgs.QList2Table(cards)
	local dangerousCard = self:getDangerousCard(self.room:getCurrent())
	
	if self:isFriend(self.room:getCurrent()) then
		self.room:writeToConsole("----discard friend----")
		self:sortByUseValue(cards, true)
		for _,card in ipairs(cards) do
			if card:getSuit() == sgs.Card_Diamond then
				table.insert(to_discard, card:getId())
				break
			end
		end
		if next(to_discard) == nil then
			for _,card in ipairs(cards) do
				table.insert(to_discard, card:getId())
				break
			end
		end
	else
		self.room:writeToConsole("----discard enemy----")
		self:sortByUseValue(cards, false)
		for _,card in ipairs(cards) do
			if dangerousCard and dangerousCard:getId() == card:getId() then
				table.insert(to_discard, card:getId())
				break
			end
		end
		if next(to_discard) == nil then
			for _,card in ipairs(cards) do
				if card:getSuit() ~= sgs.Card_Diamond then
					table.insert(to_discard, card:getId())
					break
				end
			end
		end
		if next(to_discard) == nil then
			for _,card in ipairs(cards) do
				table.insert(to_discard, card:getId())
				break
			end
		end
	end
	self.room:writeToConsole("----to discard----")
	self.room:writeToConsole(table.concat(to_discard))
	return to_discard
end 