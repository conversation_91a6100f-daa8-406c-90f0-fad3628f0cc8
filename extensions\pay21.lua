module("extensions.pay21", package.seeall)
extension = sgs.Package("pay21")


 
shinmyoumaru = sgs.General(extension, "shinmyoumaru$", "luaxing", 4, false, false, false)
seija = sgs.General(extension, "seija", "luaxing", 3, false, false, false)
mystia = sgs.General(extension, "mystia", "luaxing", 3, false, false, false)
tokiko = sgs.General(extension,"tokiko","luaxing",3,false,false,false)
sekibanki = sgs.General(extension,"sekibanki","luaxing",4,false,false,false)
reiko = sgs.General(extension,"reiko","luaxing",4,false,false,false)
wakasagihime = sgs.General(extension,"wakasagihime","luaxing",3,false,false,false)

local function isFriendQ(room, target, player)
	if player:getRole() == "rebel" then
		return target:getRole() == "rebel"
	end
	if player:getRole() == "loyalist" or player:getRole() == "lord" then
		return target:getRole() == "loyalist" or target:getRole() == "lord"
	end
end
-- luawangyue = sgs.CreateTriggerSkill{
	-- name = "luawangyue",
	-- frequency = sgs.Skill_Limited,
	-- global = true,
	-- limit_mark = "@wangyue",
	-- events = {sgs.EventPhaseStart},
	-- on_trigger = function(self, event, player, data, room)
		-- for _, kagerou in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
			-- if kagerou:getMark("@wangyue") > 0 then
				-- room:setPlayerProperty(kagerou, "hp", sgs.QVariant(kagerou:getHp() + 1))
				-- if kagerou:getHp() >= kagerou:getMaxHp() then
					-- local x = kagerou:getHandcardNum()
					-- if kagerou:getMaxCards() <= x then
						-- kagerou:drawCards(1)
					-- end
					-- if kagerou:getEquips():length() >= x then
						-- local players = sgs.SPlayerList()
						-- for _, p in sgs.qlist(room:getAlivePlayers()) do
							-- if not p:isAllNude() then
								-- players:append(p)
							-- end
						-- end
						-- if not players:isEmpty() then
							-- local target1 = room:askForPlayerChosen(kagerou, players, self:objectName(), "luawangyue2")
							-- if target1:hasEquip() or target1:getJudgingArea():length() > 0 or not target1:isKongcheng() then
								-- local card_id = room:askForCardChosen(kagerou, target1, "hej", self:objectName())
								-- local card = sgs.Sanguosha:getCard(card_id)
								-- local place = room:getCardPlace(card_id)
								-- local equip_index = -1
								-- if place == sgs.Player_PlaceEquip then
									-- local equip = card:getRealCard():toEquipCard()
									-- equip_index = equip:location()
								-- end
								-- local tos = sgs.SPlayerList()
								-- local list = room:getAlivePlayers()
								-- for _,p in sgs.qlist(list) do
									-- if equip_index ~= -1 then
										-- if not p:getEquip(equip_index) then
											-- tos:append(p)
										-- end
									-- elseif place == sgs.Player_PlaceJudge then
										-- if not kagerou:isProhibited(p, card) and not p:containsTrick(card:objectName()) then
											-- tos:append(p)
										-- end
									-- elseif p:objectName() ~= target1:objectName() then
										-- tos:append(p)
									-- end
								-- end
								-- local tag = sgs.QVariant()
								-- tag:setValue(target1)
								-- room:setTag("wangyueTarget", tag)
								-- local to = room:askForPlayerChosen(kagerou, tos, "luawangyue3")
								-- if to then
									-- local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, kagerou:objectName(), self:objectName(), "")
									-- room:moveCardTo(card, target1, to, place, reason)
								-- end
								-- room:removeTag("wangyueTarget")
							-- end
						-- end
					-- end
				-- end
				-- kagerou:loseAllMarks("@wangyue")
			-- end
		-- end
	-- end
-- }
-- luawangyue2 = sgs.CreateTriggerSkill{
	-- name = "#luawangyue",
	-- events = {sgs.Damage},
	-- on_trigger = function(self, event, kagerou, data)
		-- local room = kagerou:getRoom()
		-- local damage = data:toDamage()
		-- if damage.from and damage.from:isAlive() and damage.from:objectName() == kagerou:objectName() then
			-- if kagerou:getMark("@wangyue") <= 0 then
				-- kagerou:gainMark("@wangyue")
			-- end
		-- end
	-- end
-- }

-- luayinxi2 = sgs.CreateDistanceSkill{
	-- name = "#luayinxi",
	-- correct_func = function(self, from, to)
		-- if from:hasSkill("luayinxi") then
			-- return -1
		-- end
	-- end,
-- }
-- luayinxi_list = {}
-- luayinxi = sgs.CreateTriggerSkill{
	-- name = "luayinxi",
	-- events = {sgs.EventPhaseChanging, sgs.EventPhaseEnd, sgs.CardsMoveOneTime, sgs.DamageCaused, sgs.TargetConfirmed},
	-- global = true,
	-- frequency = sgs.Skill_Compulsory,
	-- on_trigger = function(self, event, player, data, room)
		-- if event == sgs.EventPhaseChanging then
			-- if data:toPhaseChange().to == sgs.Player_NotActive and player:hasSkill("luayinxi") and room:getCurrent():objectName() == player:objectName() then
				-- room:setPlayerFlag(player, "luayinxiR")
				-- local target = room:askForPlayerChosen(player, room:getAlivePlayers(), self:objectName(), "luayinxi-invoke")
				-- if target then
					-- room:setPlayerMark(target, "luayinxi", 1)
				-- end
			-- end
		-- elseif event == sgs.CardsMoveOneTime then
			-- if player:objectName() == room:getCurrent():objectName() and player:getMark("luayinxi") > 0 then
				-- local move = data:toMoveOneTime()
				-- if not move.from then return false end
				-- if move.from:objectName() ~= player:objectName() then return false end
				-- luayinxi_list = {}
				-- for _,id in sgs.qlist(move.card_ids) do
					-- if bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) == sgs.CardMoveReason_S_REASON_DISCARD then
						-- table.insert(luayinxi_list, id)
					-- end
				-- end
			-- end
		-- elseif event == sgs.EventPhaseEnd then
			-- if player:getPhase() == sgs.Player_Discard and player:getMark("luayinxi") > 0 and player:objectName() == room:getCurrent():objectName() then
				-- for _,id in pairs(luayinxi_list) do
					-- if sgs.Sanguosha:getCard(id):isKindOf("Slash") then
						-- return false
					-- end
				-- end
				-- for _, kagerou in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
					-- room:setPlayerFlag(kagerou, "slashbizhong")

					-- room:setPlayerFlag(kagerou, "slashTargetFix")
					-- room:setPlayerFlag(kagerou, "slashNoDistanceLimit")
					-- room:setPlayerFlag(kagerou, "slashTargetFixToOne")
					-- room:setPlayerFlag(player, "SlashAssignee")

					-- if room:askForUseSlashTo(kagerou, player, "luayinxiF", false) then
						-- room:setPlayerFlag(kagerou, "-slashbizhong")
						-- room:setPlayerFlag(kagerou, "-slashTargetFix")
						-- room:setPlayerFlag(kagerou, "-slashNoDistanceLimit")
						-- room:setPlayerFlag(kagerou, "-slashTargetFixToOne")
						-- room:setPlayerFlag(player, "-SlashAssignee")
					-- end
					-- room:setPlayerFlag(kagerou, "-slashbizhong")
					-- room:setPlayerFlag(kagerou, "-slashTargetFix")
					-- room:setPlayerFlag(kagerou, "-slashNoDistanceLimit")
					-- room:setPlayerFlag(kagerou, "-slashTargetFixToOne")
					-- room:setPlayerFlag(player, "-SlashAssignee")
				-- end
				-- room:setPlayerMark(player, "luayinxi", 0)
			-- end
		-- elseif event == sgs.TargetConfirmed then
			-- local use = data:toCardUse()
			-- if not use.from then return false end
			-- if (player:objectName() ~= use.from:objectName()) or (not use.card:isKindOf("Slash")) then return false end
			-- local jink_table = sgs.QList2Table(player:getTag("Jink_" .. use.card:toString()):toIntList())
			-- local index = 1
			-- for _, p in sgs.qlist(use.to) do
				-- if player:hasFlag("slashbizhong") then
					-- local _data = sgs.QVariant()
					-- _data:setValue(p)
					-- jink_table[index] = 0
				-- end
				-- index = index + 1
			-- end
			-- local jink_data = sgs.QVariant()
			-- jink_data:setValue(Table2IntList(jink_table))
			-- player:setTag("Jink_" .. use.card:toString(), jink_data)
			-- return false
		-- end
		-- return false
	-- end
-- }
-- kagerou:addSkill(luawangyue)
-- kagerou:addSkill(luawangyue2)
-- kagerou:addSkill(luayinxi)
-- kagerou:addSkill(luayinxi2)
luahuizhen = sgs.CreateTriggerSkill{
	name = "luahuizhen",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.Damage, sgs.Damaged},
	on_trigger = function(self, event, player, data)
		if event == sgs.Damage then
			local room = player:getRoom()
			local damage = data:toDamage()
			if damage.from and damage.from:hasSkill("luahuizhen") and damage.from:objectName() == player:objectName() and not player:hasFlag("luahuizhen") then
				local _data = sgs.QVariant()
				_data:setValue(damage.to)
				if not damage.to:isAlive() then return false end
				if room:askForSkillInvoke(player, "luahuizhen", _data) then
					room:setPlayerFlag(player,"luahuizhen")
					for _,p in sgs.qlist(room:getAlivePlayers()) do
						if not damage.to:isAlive() then return false end 
						room:setPlayerFlag(p, "luaaoshuNull")
						local card = room:askForCard(p, "TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand", "@luahuizhen", _data, sgs.Card_MethodNone)
						room:setPlayerFlag(p, "-luaaoshuNull")
						if card then
							if not p:isCardLimited(card, sgs.Card_MethodUse) and not p:isProhibited(damage.to, card) then
								if card:isKindOf("AOE") or card:isKindOf("AmazingGrace") or card:isKindOf("GodSalvation") then
									room:setPlayerFlag(p, "qucaiAOE")
									room:setPlayerFlag(damage.to, "qucaiAOEs")
									card:setSkillName("luahuizhen")
									room:useCard(sgs.CardUseStruct(card, p, sgs.SPlayerList()))
									player:drawCards(1)
									room:setPlayerFlag(p, "-qucaiAOE")
									room:setPlayerFlag(damage.to, "-qucaiAOEs")
								else
									card:setSkillName("luahuizhen")
									room:useCard(sgs.CardUseStruct(card, p, damage.to))
									player:drawCards(1)
								end
							end
						end
					end
					return true
				end
			end
		elseif event == sgs.Damaged then
			local room = player:getRoom()
			local damage = data:toDamage()
			if damage.from and damage.to:hasSkill("luahuizhen") and damage.to:objectName() == player:objectName() and not player:hasFlag("luahuizhen") then
				local _data = sgs.QVariant()
				_data:setValue(damage.to)
				if room:askForSkillInvoke(player, "luahuizhen", _data) then
					room:setPlayerFlag(player,"luahuizhen")
					for _,p in sgs.qlist(room:getAlivePlayers()) do
						if not damage.to:isAlive() then return false end
						room:setPlayerFlag(p, "luaaoshuNull")
						local card = room:askForCard(p, "TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand", "@luahuizhen", _data, sgs.Card_MethodNone)
						room:setPlayerFlag(p, "-luaaoshuNull")
						if card then
							if not p:isCardLimited(card, sgs.Card_MethodUse) and not p:isProhibited(damage.to, card) then
								if card:isKindOf("AOE") or card:isKindOf("AmazingGrace") or card:isKindOf("GodSalvation") then
									room:setPlayerFlag(p, "qucaiAOE")
									room:setPlayerFlag(damage.to, "qucaiAOEs")
									card:setSkillName("luahuizhen")
									room:useCard(sgs.CardUseStruct(card, p, sgs.SPlayerList()))
									player:drawCards(1)
									room:setPlayerFlag(p, "-qucaiAOE")
									room:setPlayerFlag(damage.to, "-qucaiAOEs")
									return false
								end
								card:setSkillName("luahuizhen")
								room:useCard(sgs.CardUseStruct(card, p, damage.to))
								player:drawCards(1)
							end
						end
					end
				end
			end
		end
	end
}
luahuizhen3 = sgs.CreateTriggerSkill{
    name = "#luahuizhen2",
    global = true,
    events = {sgs.EventPhaseStart, sgs.EventPhaseEnd, sgs.Death, sgs.CardsMoveOneTime},
    on_trigger = function(self, event, player, data, room)
        local shinmyoumaru = room:findPlayerBySkillName("luahuizhen")
		if not shinmyoumaru then return false end
		if not shinmyoumaru:isAlive() then
			for _, p in sgs.qlist(room:getAllPlayers()) do
				room:detachSkillFromPlayer(p, "luahuizhen2")
			end
			return false
		end
		if event == sgs.EventPhaseStart then
			if room:getCurrent():hasSkill("luahuizhen") or room:getCurrent():getPhase() == sgs.Player_Finish then
				if room:getCurrent():objectName() == player:objectName() and room:getCurrent():getPhase() ~= sgs.Player_NotActive then
					for _, p2 in sgs.qlist(room:findPlayersBySkillName("luahuizhen")) do
						for _, p in sgs.qlist(room:getAllPlayers()) do
							if not p:hasSkill("luahuizhen2") and p:getHandcardNum() == 1 then room:acquireSkill(p, "luahuizhen2", false) end
						end
					end
				end
			end
		elseif event == sgs.EventPhaseEnd then
			if room:getCurrent():getPhase() == sgs.Player_Finish then
				for _, p in sgs.qlist(room:getAllPlayers()) do
					room:detachSkillFromPlayer(p, "luahuizhen2")
				end
				local toziko = player:getRoom():findPlayerBySkillName("luahuizhen")
				if toziko and toziko:hasFlag("luahuizhen") then
					room:setPlayerFlag(toziko, "-luahuizhen")
				end
			end
		elseif event == sgs.Death then
			local death = data:toDeath()
			if death.who:hasSkill("luahuizhen") then
				for _, p in sgs.qlist(room:getAllPlayers()) do
					room:detachSkillFromPlayer(p, "luahuizhen2")
				end
			end
		else
			local move = data:toMoveOneTime()
			if room:getCurrent():hasSkill("luahuizhen") or room:getCurrent():getPhase() == sgs.Player_Finish then
				if move.to and move.to:objectName() == player:objectName() and move.to_place == sgs.Player_PlaceHand and not move.card_ids:isEmpty() then
					if player:getHandcardNum() == 1 then
						if not player:hasSkill("luahuizhen2") then room:acquireSkill(player, "luahuizhen2", false);room:writeToConsole("huiXXX test y3") end
					else
						room:detachSkillFromPlayer(player, "luahuizhen2")
					end
				elseif move.from and move.from:objectName() == player:objectName() and move.from_places:contains(sgs.Player_PlaceHand) and not move.card_ids:isEmpty() then
					if player:getHandcardNum() == 1 then
						if not player:hasSkill("luahuizhen2") then room:acquireSkill(player, "luahuizhen2", false) end
					else
						room:detachSkillFromPlayer(player, "luahuizhen2")
					end
				end
			else
				room:detachSkillFromPlayer(player, "luahuizhen2")
			end
			return false
		end
    end
}

luabaochui2 = sgs.CreateTriggerSkill{
	name = "#luabaochui",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DrawInitialCards},
	on_trigger = function(self, triggerEvent, player, data)
		local room = player:getRoom()
		if triggerEvent == sgs.DrawInitialCards then
			if player:hasLordSkill("luabaochui") then
				room:notifySkillInvoked(player, "luabaochui")
				for _, Acard in sgs.list(player:getHandcards()) do
					if Acard:isKindOf("Wanbaochui") then
						player:drawCards(1)
					end
				end
				for _, id in sgs.qlist(room:getDrawPile()) do
					local Acard = sgs.Sanguosha:getCard(id)
					if Acard:isKindOf("Wanbaochui")  then
						local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
						dummy:addSubcard(id)
						room:obtainCard(player, dummy)
					end
				end
			end
		end 
		return false
	end
}

luabaochuiCard = sgs.CreateSkillCard{
	name = "luabaochui",
	will_throw = false,
	--handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		if sgs.Self:getHandcardNum() <= to_select:getHandcardNum() then return end
		return to_select:getHandcardNum() == 1
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		local playerA = effect.from
		local playerB = effect.to
		local moveA = sgs.CardsMoveStruct()
		moveA.card_ids = playerA:handCards()
		moveA.to = playerB
		moveA.to_place = sgs.Player_PlaceHand
		local moveB = sgs.CardsMoveStruct()
		moveB.card_ids = playerB:handCards()
		moveB.to = playerA
		moveB.to_place = sgs.Player_PlaceHand
		room:moveCardsAtomic(moveA, false)
		room:moveCardsAtomic(moveB, false)

		local discard_ids = room:getDiscardPile()
		local trickcard = sgs.IntList()
		for _, id in sgs.qlist(discard_ids) do
			local card = sgs.Sanguosha:getCard(id)
			if card:isKindOf("Wanbaochui") then
				trickcard:append(id)
			end
		end
		if trickcard:length() > 0 then
			room:fillAG(trickcard, playerA)
			local card_id = room:askForAG(playerA, trickcard, false, "luashenju")
			local card_r = sgs.Sanguosha:getCard(card_id)
			local card_po = card_r:objectName()
				local dummy_0 = sgs.Sanguosha:cloneCard(card_po, sgs.Card_NoSuit, 0)
				dummy_0:addSubcard(card_id)
				room:useCard(sgs.CardUseStruct(dummy_0, playerA, sgs.SPlayerList()))
			room:clearAG()
		end
	end
}
luabaochui = sgs.CreateZeroCardViewAsSkill{
	name = "luabaochui$",
	view_as = function(self, cards)
		return luabaochuiCard:clone()
	end,
	enabled_at_play = function(self, player)
		return true
	end,
}

shinmyoumaru:addSkill(luahuizhen)
shinmyoumaru:addSkill(luahuizhen3)
shinmyoumaru:addSkill(luabaochui2)
shinmyoumaru:addSkill(luabaochui)
local function targetsTable2QListPay(thetable)
	local theqlist = sgs.SPlayerList()
	for _, p in ipairs(thetable) do
		theqlist:append(p)
	end 
	return theqlist
end
luanigongCard = sgs.CreateSkillCard{
    name = "luanigong",
    will_throw = false,
    handling_method = sgs.Card_MethodNone,
    filter = function(self, targets, to_select)
        if sgs.Self:objectName() == to_select:objectName() then 
            return false
        end
        if  to_select:isKongcheng() then
            return false
        end
		return true
    end,
    on_use = function(self, room, source, targets)
		local targetsA = targets
        while true do
			local target = room:askForPlayerChosen(source, targetsTable2QListPay(targetsA), "luanigong", "luanigongX", false, false)
			if target then  
				local targetsB = {}
				for _,player in ipairs(targetsA) do
					if player:objectName() ~= target:objectName() then
						table.insert(targetsB, player)
					end
				end
				targetsA = targetsB
				
				if not source:isKongcheng() and not target:isKongcheng() then
					local success = source:pindian(target, "luanigong") 
				end 
				if #targetsA == 0 then break end
			else
				break
			end 
		end   
    end,
}

luanigongVS = sgs.CreateZeroCardViewAsSkill{
    name = "luanigong",
    view_as = function(self, cards)
        return luanigongCard:clone()
    end,
    enabled_at_play = function(self, player)
        return false
    end,
    enabled_at_response = function(self, player, pattern)
        return pattern == "@@luanigong"
    end,
}

luanigong = sgs.CreateTriggerSkill{
    name = "luanigong" ,
    events = {sgs.EventPhaseStart, sgs.Pindian},
    view_as_skill = luanigongVS,
    frequency = sgs.Skill_NotFrequent,
    on_trigger = function(self, event, player, data, room)
        if event == sgs.EventPhaseStart then
            if player:getPhase() == sgs.Player_Finish and room:askForUseCard(player, "@@luanigong", "@luanigong") then
                
            end
		elseif event == sgs.Pindian then
			local pindian = data:toPindian()
			if pindian.reason == "luanigong" then 
				local winner
				local loser
				local cardX 
				if pindian.from_number > pindian.to_number then
					winner = pindian.from
					loser = pindian.to  
					cardX = pindian.from_card
				elseif pindian.from_number < pindian.to_number then
					winner = pindian.to
					loser = pindian.from 
					cardX = pindian.to_card
				end  
				if not winner then return false end  
				local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
				dummy:addSubcard(cardX)
				local targets_list = sgs.SPlayerList() 
				for _, target in sgs.qlist(room:getAllPlayers()) do
					if winner:canSlash(target, dummy, true) then
						targets_list:append(target)
					end
				end 
				if not targets_list:isEmpty() then 
					--local targetNumber = 1
					--targetNumber = targetNumber + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, winner, dummy)
					local slashTarget = room:askForPlayerChosen(winner, targets_list, self:objectName(), "luanigongY", false, false)

					if slashTarget then 
						room:useCard(sgs.CardUseStruct(dummy, winner, slashTarget))
					else
						room:useCard(sgs.CardUseStruct(dummy, winner, targets_list:first())) 
					end 
				else
					dummy:deleteLater()
				end 
			end 
        end
    end
}
luafanze = sgs.CreateTriggerSkill{
	name = "luafanze",
	global = true,
	events = {sgs.TurnStart, sgs.TargetConfirmed},
	--frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data, room) 
        if event == sgs.TurnStart then
			if room:getCurrent():isLord() and player:getMark("@extra_turn") == 0 and room:getCurrent():objectName() == player:objectName() then  
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					room:setPlayerMark(p, "@luafanze", 0) 
				end 
				
				for _, seija in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do  
					local target = room:askForPlayerChosen(seija, room:getAlivePlayers(), self:objectName(), "luafanzeA", true, true)
					if target and target:isAlive() then 
						room:setPlayerMark(target, "@luafanze", 1)  
					end
				end 
			end
		else
			local use = data:toCardUse()
			if use.card and use.from and use.to and (use.card:isKindOf("Slash") or use.card:isKindOf("Duel")) 
				and use.from:objectName() == player:objectName() and use.to:length() == 1 and use.to:first():getMark("@luafanze") > 0 then
				local count = 0
				local targetA = use.to:first()
				if targetA:getHp() == 1 then count = count + 1 end
				if targetA:getHandcardNum() == 1 then count = count + 1 end
				if targetA:getEquips() and targetA:getEquips():length() == 1 then count = count + 1 end
				if count > 0 then
					targetA:drawCards(count)
					if targetA:getHandcardNum() >= use.from:getHandcardNum() then
						use.to = sgs.SPlayerList()
						--use.to:append(use.from)
						--use.from = targetA
						room:getLord():setFlags("luafanzeSlash")
						room:useCard(sgs.CardUseStruct(use.card, targetA, use.from), true)
						room:getLord():setFlags("-luafanzeSlash")

						data:setValue(use) 
						return true
					end 
				end 
			end 
		end 
	end
} 
seija:addSkill(luanigong)
seija:addSkill(luafanze)


luajieaoCard = sgs.CreateSkillCard{
	name = "luajieao",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select)
		if #targets == 0 then
			if not to_select:isKongcheng() then
				return to_select:objectName() ~= sgs.Self:objectName()
			end
		end
		return false
	end,
	on_use = function(self, room, source, targets)
		local success = source:pindian(targets[1], "luajieao", self)
		if success then
			room:damage(sgs.DamageStruct(self:objectName(), source, targets[1], 1, sgs.DamageStruct_Normal))
		else
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_Diamond, 0)
			slash:setSkillName("luajieao")
			room:useCard(sgs.CardUseStruct(slash, targets[1], source))
		end
	end
}
luajieao = sgs.CreateViewAsSkill{
	name = "luajieao",
	n = 1,
	view_filter = function(self, selected, to_select)
		return not to_select:isEquipped()
	end,
	view_as = function(self, cards)
		if #cards == 1 then
			local daheCard = luajieaoCard:clone()
			daheCard:addSubcard(cards[1])
			return daheCard
		end
	end,
	enabled_at_play = function(self, player)
		if not player:hasUsed("#luajieao") then
			return not player:isKongcheng()
		end
		return false
	end
}

luajinlun = sgs.CreateTriggerSkill{
	name = "luajinlun",
	frequency = sgs.Skill_Frequent,
	events = {sgs.Damaged},
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		if damage.damage == 0 then return false end
		player:setMark("luajinlun", 1)

	end,
}
luajinlun2 = sgs.CreateTriggerSkill{
	name = "#luajinlun" ,
	events = {sgs.EventPhaseChanging} ,
	global = true,
	--frequency = sgs.Skill_Frequent , 这句话源代码没有，但是我感觉应该加上，毕竟连破一点副作用都没有
	priority = 1,
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		if change.to ~= sgs.Player_NotActive then return false end
		local shensimayi = player:getRoom():findPlayerBySkillName("luajinlun")
		if (not shensimayi) or (shensimayi:getMark("luajinlun") <= 0) then return false end
		shensimayi:setMark("luajinlun",0)
		if not shensimayi:askForSkillInvoke("luajinlun") then return false end
		player:getRoom():writeToConsole("朱鹭子测试F")
		local p = shensimayi
		local playerdata = sgs.QVariant()
		playerdata:setValue(p)
		player:getRoom():setTag("luajinlunInvoke", playerdata)
		return false
	end ,
	can_trigger = function(self, target)
		return target
	end
}
luajinlunDo = sgs.CreateTriggerSkill{
	name = "#luajinlun-do" ,
	events = {sgs.EventPhaseStart},
	global = true,
	priority = 1 ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if room:getTag("luajinlunInvoke") then
			local target = room:getTag("luajinlunInvoke"):toPlayer()
			room:removeTag("luajinlunInvoke")
			if target and target:isAlive() then
				room:writeToConsole("朱鹭子测试F2")
				local p = room:askForPlayerChosen(target, room:getAlivePlayers(), "luajinlun", "luajinlun", true, true)
				if p then
					local jink = sgs.Sanguosha:cloneCard("jink")
					jink:addSubcards(p:getHandcards())
					room:throwCard(jink, p, p)
					if p:isAlive() then
						room:setPlayerMark(p, "@extra_turn", 1)
						p:gainAnExtraTurn()
						room:setPlayerMark(p, "@extra_turn", 0)
					end
				end
			end
			return false
		end
		return false
	end,
	can_trigger = function(self, target)
		return target and (target:getPhase() == sgs.Player_NotActive)
	end
}

tokiko:addSkill(luajieao)
tokiko:addSkill(luajinlun)
tokiko:addSkill(luajinlun2)
tokiko:addSkill(luajinlunDo)



yanguangCard = sgs.CreateSkillCard{
	name = "luayanguang",
	filter = function(self, targets, to_select)
		local targets_list = sgs.PlayerList()
		for _, target in ipairs(targets) do
			targets_list:append(target)
		end
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		slash:setSkillName("luayanguang")
		slash:deleteLater()
		return (#targets < 1) and slash:targetFilter(targets_list, to_select, sgs.Self) and sgs.Self:canSlash(to_select, false)
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		local function canLoseHp()
			for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
				if hecatiaX and isFriendQ(room, effect.from, hecatiaX) and effect.from:objectName() ~= hecatiaX:objectName()
						and effect.from:getHp() == hecatiaX:getHp() then
					room:notifySkillInvoked(hecatiaX, "luayiti")
					return false
				end 
			end 
			for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
				if Erin and Erin:getKingdom() == effect.from:getKingdom() then
					room:notifySkillInvoked(Erin, "luajiance")
					return false
				end 
			end 
			return true
		end 
		if canLoseHp() then room:loseHp(effect.from) end 
		
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		slash:setSkillName("luayanguang")
		effect.from:getRoom():useCard(sgs.CardUseStruct(slash, effect.from, effect.to))
	end
}

luayanguang = sgs.CreateZeroCardViewAsSkill{
	name = "luayanguang",
	view_as = function()
		local suiyuecard = yanguangCard:clone()
		suiyuecard:setSkillName("luayanguang")
		return suiyuecard
	end,
	enabled_at_play = function(self, player)
		return (not player:hasUsed("#luayanguang")) and player:getHp() > 0
	end
}


lualushou = sgs.CreateTriggerSkill{
	name = "lualushou" ,
	events = {sgs.PreCardUsed, sgs.TargetConfirmed, sgs.Damage, sgs.PreDamageDone} ,
	on_trigger = function(self, event, player, data)
		if event == sgs.PreCardUsed then
			local use = data:toCardUse()
			local room = player:getRoom()
			if use.from:objectName() == player:objectName() and (use.card:isKindOf("Slash")) and use.from:hasSkill("lualushou")
					and room:askForCard(player, "TrickCard", "lualushouB", data, sgs.Card_MethodDiscard) then
				player:drawCards(1)
				--if (sgs.Sanguosha:getCurrentCardUseReason() ~= sgs.CardUseStruct_CARD_USE_REASON_PLAY) then return false end
				local available_targets = sgs.SPlayerList()
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if (use.to:contains(p) or room:isProhibited(player, p, use.card)) then continue end
					if (use.card:targetFilter(sgs.PlayerList(), p, player)) then
						available_targets:append(p)
					end
				end
				local extra = nil
				local Carddata2 = sgs.QVariant() -- ai用
				Carddata2:setValue(use.card)
				room:writeToConsole("赤蛮奇测试" .. player:getGeneralName())
				extra = room:askForPlayerChosen(player, available_targets, "lualushou", "lualushou", true, true)
				if extra then
					use.to:append(extra)
				end
				room:sortByActionOrder(use.to)
				data:setValue(use)
				return false
			end
		elseif event == sgs.TargetConfirmed then
			local use = data:toCardUse()
			if not use.from then return false end
			if (player:objectName() ~= use.from:objectName()) or (not use.card:isKindOf("Slash")) then return false end
			local room = player:getRoom()
			local jink_table = sgs.QList2Table(player:getTag("Jink_" .. use.card:toString()):toIntList())
			local index = 1
			if room:askForCard(player, "EquipCard", "lualushouC", data, sgs.Card_MethodDiscard) then room:setPlayerFlag(player, "lualushou") end
			if room:askForCard(player, "BasicCard", "lualushouA", data, sgs.Card_MethodDiscard) and not use.to:isEmpty() then
				for _, p in sgs.qlist(use.to) do
					local _data = sgs.QVariant()
					_data:setValue(p)
					jink_table[index] = 0
					index = index + 1
				end
			end
			local jink_data = sgs.QVariant()
			jink_data:setValue(Table2IntList(jink_table))
			player:setTag("Jink_" .. use.card:toString(), jink_data)
			return false
		end
	end
}

lualushou2 = sgs.CreateTriggerSkill{
	name = "#lualushou",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damage, sgs.PreDamageDone},
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		local room = player:getRoom()
		if (event == sgs.PreDamageDone) and damage.from and damage.from:hasSkill("lualushou") and damage.from:isAlive() then
			local weiyan = damage.from
			weiyan:setTag("invokeLuaKuanggu", sgs.QVariant(weiyan:hasFlag("lualushou")))
		elseif (event == sgs.Damage) and player:hasSkill("lualushou") and player:isAlive() then
			local invoke = player:getTag("invokeLuaKuanggu"):toBool()
			player:setTag("invokeLuaKuanggu", sgs.QVariant(false))
			if invoke and player:isWounded() then
				local recover = sgs.RecoverStruct()
				recover.who = player
				recover.recover = damage.damage
				room:recover(player, recover)
			end
            room:setPlayerFlag(player, "-lualushou")
		end
		return false
	end
}
sekibanki:addSkill(lualushou)
sekibanki:addSkill(lualushou2)
sekibanki:addSkill(luayanguang)

lualeimin = sgs.CreateFilterSkill{
	name = "lualeimin",
	view_filter = function(self,to_select)
		return to_select:isKindOf("Slash") 
	end,
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("thunder_slash", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		local _card = sgs.Sanguosha:getWrappedCard(card:getId())
		_card:takeOver(slash)
		_card:setModified(true)
		return _card
	end
}


lualeimin2 = sgs.CreateTriggerSkill{
	name = "#lualeimin",
	frequency = sgs.Skill_Frequent,
	events = {sgs.Damaged},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		local x = damage.damage
		if damage.damage == 0 then return false end
		if damage.nature == sgs.DamageStruct_Thunder and damage.to:objectName() == player:objectName() and player:hasSkill("lualeimin") then
			for i = 1, x, 1 do
				player:drawCards(3)
			end
		end

	end
}

luabaguCard = sgs.CreateSkillCard{
	name = "luabagu",
	filter = function(self, targets, to_select)
		return (#targets == 0)
	end,
	on_effect = function(self, effect)
		effect.from:getRoom():damage(sgs.DamageStruct("luabagu", effect.from, effect.to, 1, sgs.DamageStruct_Thunder))
	end
}
luabagu = sgs.CreateOneCardViewAsSkill{
	name = "luabagu",
	view_filter = function(self, to_select)
		return to_select:isKindOf("ThunderSlash") 
	end, 
	view_as = function(self, card)
		local skillcard = luabaguCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self, player)
		return player:usedTimes("#luabagu") < 2*player:getEquips():length()
	end,
}

reiko:addSkill(lualeimin)
reiko:addSkill(lualeimin2)
reiko:addSkill(luabagu)
luashizhucard = sgs.CreateSkillCard{
	name = "luashizhu",
	target_fixed = true,
	handling_method = sgs.Card_MethodNone,
	on_use = function(self, room, source, targets)
		local names = {"1", "2", "3", "4", "5"}
		local choice = room:askForChoice(source, "luashizhu", table.concat(names, "+"))

		choice = math.min(tonumber(choice), room:getDiscardPile():length())
		local card_ids = sgs.IntList()
		if room:getDiscardPile():isEmpty() then source:loseMark("@shizhu"); return false end
		room:writeToConsole("shizhu testK " .. choice)
		for i = 0, choice - 1 do
			card_ids:append(room:getDiscardPile():at(i))
			if room:getDiscardPile():length() < i + 1 then break end
		end
			room:fillAG(card_ids)
			local card_0 = sgs.Sanguosha:getCard(card_ids:at(card_ids:length() - 1))
			local move = sgs.CardsMoveStruct()
			move.from = nil
			move.from_place = sgs.Player_DiscardPile
			move.to = source
			move.to_place = sgs.Player_PlaceHand
			move.card_ids = sgs.IntList()
			for _, id in sgs.qlist(card_ids) do
				local card = sgs.Sanguosha:getCard(id)
				room:writeToConsole("test X " .. choice)
				if card:getSuit() == card_0:getSuit() then
					move.card_ids:append(id)
					room:setPlayerMark(source, "luashizhu" .. card:getId(), 1)
				end
			end
			move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, "luashizhu")
			room:moveCardsAtomic(move, true)
			room:broadcastInvoke("clearAG")
			room:clearAG()

		source:loseMark("@shizhu")
	end
}
luashizhuVS = sgs.CreateViewAsSkill{
	name = "luashizhu",
	n = 0,
	view_filter = function()
		return false
	end,
	view_as = function()
		return luashizhucard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@shizhu") > 0
	end,
}
luashizhu = sgs.CreateTriggerSkill{
	name = "luashizhu",
	frequency = sgs.Skill_Limited,
	limit_mark = "@shizhu",
	view_as_skill = luashizhuVS,
	events = {sgs.BeforeCardsMove, sgs.EventPhaseEnd},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.BeforeCardsMove then
			local move = data:toMoveOneTime()
			if room:getTag("FirstRound"):toBool() then return end
			if bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) == sgs.CardMoveReason_S_REASON_USE
				or bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) == sgs.CardMoveReason_S_REASON_DISCARD
				or bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) == sgs.CardMoveReason_S_REASON_RESPONSE then
				if move.from and move.from:objectName() == player:objectName()
						and (move.from_places:contains(sgs.Player_PlaceEquip) or move.from_places:contains(sgs.Player_PlaceHand)) then
					for _, id in sgs.qlist(move.card_ids) do
						if room:getCardOwner(id):objectName() == move.from:objectName() and (room:getCardPlace(id) == sgs.Player_PlaceEquip or room:getCardPlace(id) == sgs.Player_PlaceHand)
								and player:getMark("luashizhu" .. id) > 0 then
							room:setPlayerFlag(player, "luashizhu")
							return false
						end
					end
				end
			end
		else
			if player:getPhase() == sgs.Player_Finish then
				local invoke = false
				if not player:hasFlag("luashizhu") then

					for i = 0 ,500 do
						if player:getMark("luashizhu" .. i) > 0 then
							room:setPlayerMark(player, "luashizhu" .. i, 0)
							invoke = true
						end
					end
				else
					local function canLoseHp()
						for _, hecatiaX in sgs.qlist(room:findPlayersBySkillName("luayiti")) do 
							if hecatiaX and isFriendQ(room, player, hecatiaX) and player:objectName() ~= hecatiaX:objectName()
									and player:getHp() == hecatiaX:getHp() then
								room:notifySkillInvoked(hecatiaX, "luayiti")
								return false
							end 
						end 
						for _, Erin in sgs.qlist(room:findPlayersBySkillName("luajiance")) do 
							if Erin and Erin:getKingdom() == player:getKingdom() then
								room:notifySkillInvoked(Erin, "luajiance")
								return false
							end 
						end 
						return true
					end 
					if canLoseHp() then room:loseHp(player, 1) end 
				end
				if invoke then
					local kageroues = sgs.SPlayerList()
					for _,p in sgs.qlist(room:getAlivePlayers()) do
						for _, skill in sgs.qlist(p:getVisibleSkillList()) do
							if skill:getFrequency() == sgs.Skill_Limited and p:getMark("@" .. string.sub(skill:objectName(), 4)) == 0
								and (not skill:isLordSkill() or p:hasLordSkill(skill:objectName())) then
								kageroues:append(p)
							end
						end
					end
					if not kageroues:isEmpty() then
						local kagerou = room:askForPlayerChosen(player, kageroues, "luashizhu", "@luashizhu", true, true)
						if kagerou then
							local choices = {}
							for _, skill in sgs.qlist(kagerou:getVisibleSkillList()) do
								if skill:getFrequency() == sgs.Skill_Limited and kagerou:getMark("@" .. string.sub(skill:objectName(), 4)) == 0
										and (not skill:isLordSkill() or kagerou:hasLordSkill(skill:objectName())) then
									table.insert(choices, skill:objectName())
								end
							end
							local choice = room:askForChoice(player, "luashizhu", table.concat(choices, "+"))

							for _, skill in sgs.qlist(kagerou:getVisibleSkillList()) do
								if skill:objectName() == choice then
									room:writeToConsole("ABCDE" .. "@" .. string.sub(skill:objectName(), 4))
									room:setPlayerMark(kagerou, "@" .. string.sub(skill:objectName(), 4), 1)
								end
							end
						end
					end
				end
			end
		end
	end
}
luarenyu = sgs.CreateTriggerSkill{
	name = "luarenyu",
	global = true,
	events = {sgs.MarkChanged},
	on_trigger = function(self, event, player, data, room)
		local mark = data:toMark().name
		if not player:hasSkill("luarenyu") then return end
		for _,kagerou in sgs.qlist(room:getAlivePlayers()) do
			for _, skill in sgs.qlist(kagerou:getVisibleSkillList()) do
				if skill:getFrequency() == sgs.Skill_Limited and mark == "@" .. string.sub(skill:objectName(), 4)
					and kagerou:getMark("@" .. string.sub(skill:objectName(), 4)) == 0
						and (not skill:isLordSkill() or kagerou:hasLordSkill(skill:objectName())) then
					room:writeToConsole(mark)
					for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
						local card_h = room:askForCard(p2, ".", "@luarenyu", data, sgs.Card_MethodNone)
						if card_h then
							local Carddata2 = sgs.QVariant() -- ai用
							Carddata2:setValue(card_h)
							p2:getRoom():setTag("luarenyuTC", Carddata2)
							local tp = p2:getRoom():askForPlayerChosen(p2, room:getAlivePlayers(), "luarenyu", "@luarenyu2", true, true)
							p2:getRoom():removeTag("luarenyuTC")
							if tp then
								tp:obtainCard(card_h)
								room:setPlayerFlag(tp, "luarenyux")
								local Carddata3 = sgs.QVariant() -- ai用
								Carddata3:setValue(card_h)
								tp:getRoom():setTag("luaqiuwenTC", Carddata3)
								card = room:askForUseCard(tp, card_h:toString(), "@luarenyu3", -1, sgs.Card_MethodUse, true)
								tp:getRoom():removeTag("luaqiuwenTC")
								room:setPlayerFlag(tp, "-luarenyux")
								if card then
									player:addMark("luarenyuF")
								end
							end
						else
							room:writeToConsole("luarenyu ai test failed")
						end
					end
				end
			end
		end

	end

}
wakasagihime:addSkill(luashizhu)
wakasagihime:addSkill(luarenyu)

luayequCard = sgs.CreateSkillCard{
	name = "luayequ" ,
	will_throw = false, 
	target_fixed = true,
	handling_method = sgs.Card_MethodNone,
	on_use = function(self, room, source, targets)
		room:setPlayerMark(source, "@luayequA", 0)
		room:setPlayerMark(source, "@luayequB", 1)

		local x = self:getSubcards():length()
		local reasonA = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, source:objectName(), "luayequ", "")

		local moveK = sgs.CardsMoveStruct()
		moveK.card_ids = self:getSubcards()
		moveK.to = nil
		moveK.to_place = sgs.Player_DrawPile
		moveK.reason = reasonA
		room:moveCardsAtomic(moveK, true)

		local card_ids = room:getNCards(x)
		room:askForGuanxing(source, card_ids, sgs.Room_GuanxingDownOnly)
		
	end 
}
luayequVS = sgs.CreateViewAsSkill{
	name = "luayequ" ,
	n = 99,
	view_filter = function(self, selected, to_select)
		return #selected <= sgs.Self:getHp()
	end ,
	view_as = function(self, cards)
		if #cards ~= sgs.Self:getHp() then return nil end
		local card = luayequCard:clone() 
		for _, acard in ipairs(cards) do
			card:addSubcard(acard)
		end			 
		return card
	end ,
	enabled_at_play = function(self, target)
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return string.startsWith(pattern, "@@luayequ")
	end
} 
luayequ = sgs.CreateTriggerSkill{
	name = "luayequ" ,
	global = true, 
	view_as_skill = luayequVS,
	events = {sgs.TargetConfirmed} ,
	on_trigger = function(self, event, player, data)
		if event == sgs.TargetConfirmed then 
			local use = data:toCardUse()
			if not use.from then return false end
			local room = use.from:getRoom()
			if (player:objectName() ~= use.from:objectName()) or (not use.card:isKindOf("Slash")) then return false end 
			for _, mystia in sgs.qlist(room:findPlayersBySkillName("luayequ")) do 
				if mystia:getMark("@luayequA") > 0 and room:askForUseCard(mystia, "@@luayequ", "@luayequ") then
					use.to = sgs.SPlayerList()
					data:setValue(use) 
					return false 
				end 
			end  
		end 
	end
}
luayequ3 = sgs.CreateTriggerSkill{
	name = "#luayequ3" ,
	global = true, 
	frequency = sgs.Skill_Compulsory, 
	events = {sgs.EventAcquireSkill, sgs.EventLoseSkill, sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		if event == sgs.EventAcquireSkill then 
			local room = player:getRoom()
			if event == sgs.EventAcquireSkill and data:toString() == "luayequ" then
				room:setPlayerMark(player, "@luayequA", 1)
				room:setPlayerMark(player, "@luayequB", 0)
			end 
		elseif event == sgs.EventLoseSkill then 
			local room = player:getRoom()
			if event == sgs.EventLoseSkill and data:toString() == "luayequ" then
				room:setPlayerMark(player, "@luayequA", 0)
				room:setPlayerMark(player, "@luayequB", 0)
			end 
		elseif event == sgs.EventPhaseChanging then 
			local room = player:getRoom()
			local cp = room:getCurrent()
			if cp:getMark("CurrentPlayer") == 0 then 
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					room:setPlayerMark(p, "CurrentPlayer", 0)
				end
				room:setPlayerMark(cp, "CurrentPlayer", 1)
			end  
		end 
	end
}
luayequ2 = sgs.CreateDistanceSkill{
	name = "#luayequ2", 
	correct_func = function(self, from, to) 
		local mystiaC = 0
		for _, mystia in sgs.qlist(to:getSiblings()) do
			if mystia:hasSkill("luayequ") and mystia:getMark("@luayequB") > 0 then
				mystiaC = mystiaC + 1
			end 
		end
		if to:hasSkill("luayequ") and to:getMark("@luayequB") > 0 then mystiaC = mystiaC + 1 end  
		if from:getMark("CurrentPlayer") > 0 then 
			return mystiaC
		else
			return 0
		end 
	end
}

luaqueshiCard = sgs.CreateSkillCard{
	name = "luaqueship",
	target_fixed = false,
	will_throw = false,
	handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		return #targets == 0 and to_select:hasSkill("luaqueshi")
	end,
	on_effect = function(self, effect)
		local source = effect.from
		local target = effect.to 
		local room = source:getRoom() 

		local _data = sgs.QVariant()
		_data:setValue(source)
 
		local function getCardList(intlist)
			local ids = sgs.CardList()
			for _, id in sgs.qlist(intlist) do
				ids:append(sgs.Sanguosha:getCard(id))
			end
			return ids
		end

		if target:getMark("@luayequB") > 0 then
			room:setPlayerMark(target, "@luayequA", 1)
			room:setPlayerMark(target, "@luayequB", 0) 
		else
			room:setPlayerMark(target, "@luayequA", 0)
			room:setPlayerMark(target, "@luayequB", 1) 
		end 
		room:obtainCard(target, self, false)
		local discard_ids = room:getDrawPile()
		if discard_ids:length() < 4 then room:swapPile() end 

		if discard_ids:length() >= 4 and room:askForSkillInvoke(target, self:objectName(), _data) then
			room:writeToConsole("Hello World! 2024-12-7 08:24:56")
			local x = discard_ids:length()
			local card_ids = sgs.IntList()
			card_ids:append(discard_ids:at(x - 1))
			card_ids:append(discard_ids:at(x - 2))
			card_ids:append(discard_ids:at(x - 3))
			card_ids:append(discard_ids:at(x - 4))

			room:writeToConsole("Hello World! 2024-12-7 08:24:56 " .. card_ids:length())
			room:fillAG(card_ids, target) 

			local to_get = sgs.IntList()
			local to_throw = sgs.IntList()
			while not card_ids:isEmpty() do
				local card_id = room:askForAG(target, card_ids, false, "shelie")
				card_ids:removeOne(card_id)
				to_get:append(card_id)--弃置剩余所有符合花色的牌(原文：throw the rest cards that matches the same suit)
				local card = sgs.Sanguosha:getCard(card_id)
				local suit = card:getSuit()
				room:takeAG(target, card_id, false)
				local _card_ids = card_ids
				for i = 0, 150 do--这一句不加的话 涉猎很多牌可能会bug，150可以改，数值越大，越精准，一般和你涉猎的牌数相等是没有bug的
					for _,id in sgs.qlist(_card_ids) do
						local c = sgs.Sanguosha:getCard(id)
						if c:getSuit() == suit and id ~= card_id then
							card_ids:removeOne(id)
							room:takeAG(nil, id, false)
							to_throw:append(id)
						end
					end
				end
			end
			local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			if not to_get:isEmpty() then
				dummy:addSubcards(getCardList(to_get))
				source:obtainCard(dummy)
			end 
			dummy:clearSubcards()
			--[[
			if not to_throw:isEmpty() then
				for _,id in sgs.qlist(to_throw) do
					local card_idsX = sgs.IntList()
					card_idsX:append(id)
					room:askForGuanxing(source, card_idsX, sgs.Room_GuanxingDownOnly)
				end 
			end]]--
			dummy:deleteLater()
			room:clearAG()
		end
	end 
}
luaqueshiVS = sgs.CreateViewAsSkill{
	name = "luaqueshi",
	n = 1,
	view_filter = function(self, cards, to_select)
		return (#cards == 0)
	end ,
	view_as = function(self, cards)
		if #cards == 0 then return end 
		local card_1 =  luaqueshiCard:clone()
		for _, c in ipairs(cards) do
			card_1:addSubcard(c)
		end
		return card_1
	end,
	enabled_at_play = function(self, player)
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luaqueshi"
	end
}
luaqueshi = sgs.CreateTriggerSkill{
	name = "luaqueshi",
	frequency = sgs.Skill_NotFrequent, 
	view_as_skill = luaqueshiVS,
	global = true,
	events = { sgs.EventPhaseChanging },
	on_trigger = function(self, event, player, data, room)
		local change = data:toPhaseChange() 
		local mystia = room:findPlayerBySkillName("luaqueshi")
		if not mystia then return end 
		if mystia and mystia:isAlive() then  
			if not player:isSkipped(change.to) and change.to == sgs.Player_Draw then 
				if room:askForUseCard(player, "@@luaqueshi", "@luaqueshi" , -1, sgs.Card_MethodNone) then  
					player:skip(change.to) 
				end 
			end 
		end
	end
}

mystia:addSkill(luayequ2)
mystia:addSkill(luayequ3)
mystia:addSkill(luayequ)
mystia:addSkill(luaqueshi)

sgs.LoadTranslationTable{
	["pay21"] = "孤星对月", --注意这里每次要加逗号
	["kagerou"] = "今泉影狼",	 
	["#kagerou"] = "孤独的狼人",		
	["designer:kagerou"] = "Paysage",	
	["luawangyue"] = "望月",
	[":luawangyue"] = "限定技，锁定技，一名角色的任意阶段开始时，你增加一点体力。若你未受伤，则：①若你手牌数不小于上限，你摸一张牌；②若你装备数量不小于手牌数，你弃一张牌并移动一名角色区域里的一张牌。每当你一名角色造成伤害后，你重置此技能。",
	["luayinxi"] = "影袭",
	["luayinxiF"] = "你可以对你之前“影袭”指定的那个角色使用一张【杀】",
	["luayinxi-invoke"] = "你可以发动“影袭”<br/> <b>操作提示</b>: 选择一名角色→点击确定<br/>",
	[":luayinxi"] = "锁定技，你计算与其他角色的距离时，始终-1。回合结束时，你须指定一名角色（不公示），其于其下个弃牌阶段若未弃置【杀】，则你可以对其使用一张【杀】（不可被闪避）",
	
	["shinmyoumaru"] = "少名针妙丸",	 
	["#shinmyoumaru"] = "辉针之光",		
	["designer:shinmyoumaru"] = "Paysage",	
	["luahuizhen"] = "辉针",		
	[":luahuizhen"] = "每回合限一次，你造成或受到伤害后，你可以令所有角色对受伤角色使用一张牌并令你摸一张牌。锁定技，你的回合内，或是所有其他角色的结束阶段，所有角色的最后一张手牌均视为【杀】。",
	["luabaochui"] = "宝槌",		
	[":luabaochui"] = "主公技，你可以与一名手牌数比你少且手牌数为1的角色交换手牌，然后获得弃牌堆中的【万宝槌】。锁定技，游戏开始时，你获得【万宝槌】。",
	["@luahuizhen"] = "你可以对受伤角色使用一张牌并令小碗摸一张牌。",

	["seija"] = "鬼人正邪",	 
	["#seija"] = "逆转的天邪鬼",		
	["designer:seija"] = "Paysage",
	["luanigong"] = "逆弓",		 
	["@luanigong"] = "你可以发动“逆弓”",
	["~luanigong"] = "选择任意目标→点击确定",
	[":luanigong"] = "结束阶段，你可以与任意名其他角色按你指定的顺序拼点。赢的角色必须将拼点牌当【杀】使用。", 
	["luanigongX"] = "请选择要拼点的一个目标",		
	["luanigongY"] = "请选择【杀】指定的目标",		
	  
	["luafanze"] = "反则",
	[":luafanze"] = "每轮开始时，你可令一名角色于本轮获得以下效果：「当你成为【杀】/【决斗】的唯一目标时，"
		.. "你每满足一项（体力值为一/装备数为一/手牌数为一），摸一张牌。然后若你手牌数不少于使用者，反转此牌的目标与使用者。」",
	["luafanzeA"] = "请选择要获得效果的角色", 

	["mystia"] = "米斯蒂娅",	 
	["#mystia"] = "夜雀",		
	["designer:mystia"] = "Paysage",
	["illustrator:mystia"] = "久苍穹",
	["luayequ"] = "夜曲",		
	[":luayequ"] = "转化技①：【杀】指定目标后，你可以将至少X张牌置于牌堆底，令此【杀】无效（X为你体力值）。②：当前回合角色与其他角色的距离+1。",
	["@luayequ"] = "你可以发动“夜曲”",
	["~luayequ"] = "选择X张牌→点击确定",
	["luaqueshi"] = "雀食",		
	["luaqueship"] = "雀食（摸牌效果）",		
	[":luaqueshi"] = "一名角色可以跳过其摸牌阶段，交给你一张牌并转化“夜曲”。然后你可以展示牌堆底的四张牌，令其获得其中每种花色的牌各一张。",
	["@luaqueshi"] = "你可以发动“雀食”",
	["~luaqueshi"] = "选择一张牌→选择老板娘→点击确定",

	["tokiko"] = "朱鹭子",
	["tokikoN"] = "朱鹭子",
	["#tokiko"] = "读书的妖怪",
	["#tokikoN"] = "读书的妖怪",
	["designer:tokiko"] = "Paysage",
	["illustrator:tokiko"] = "会帆",
	["luajieao"] = "桀骜",
	["jieao"] = "桀骜",
	[":luajieao"] = "出牌阶段限一次，你可以和一名其他角色拼点，若你赢，你对其造成一点伤害；若你没赢，视为其对你使用了一张方块【杀】。",
	["luajinlun"] = "鹮羽",
	[":luajinlun"] = "你受到至少一点伤害的回合结束时，你可以令一名角色弃置所有手牌并执行一个额外的回合。",

	["sekibanki"] = "赤蛮奇",
	["#sekibanki"] = "柳树下的杜拉罕",
	["designer:sekibanki"] = "Paysage",
	["illustrator:sekibanki"] = "まくわうに",
	["lualushou"] = "辘首",
	["lualushouA"] = "你可以弃置一张基本牌令此【杀】不可被闪避。",
	["lualushouB"] = "你可以弃置一张锦囊牌，摸一张牌，并为此【杀】额外指定一个目标。",
	["lualushouC"] = "你可以弃置一张装备牌，若此【杀】造成伤害，你回复等量的体力。",
	[":lualushou"] = "当你使用【杀】指定其他角色为目标后，你可以弃置一张：①基本牌：此【杀】不可被闪避；②锦囊牌；你摸一张牌，然后此【杀】额外指定一个目标；③装备牌：此【杀】若造成伤害，你回复等量的体力。",
	["luayanguang"] = "眼光",	
	["yanguang"] = "眼光",
	[":luayanguang"] = "出牌阶段限一次，你可以流失一点体力，视为你使用了一张【杀】（无限制）。",

	["reiko"] = "堀川雷鼓",
	["#reiko"] = "梦幻的打击乐手",
	["designer:reiko"] = "Paysage",
	["illustrator:reiko"] = "伊吹のつ",
	["lualeimin"] = "雷鸣",
	[":lualeimin"] = "锁定技，你的【杀】均视为雷【杀】。每当你受到一点雷电伤害后，你摸三张牌。",
	["luabagu"] = "八鼓",
	[":luabagu"] = "出牌阶段限2X次，你可以弃置一张雷【杀】并对一名角色造成一点雷电伤害（X为你的装备牌数）。",

	["wakasagihime"] = "若鹭姬",
	["#wakasagihime"] = "秘境的人鱼",
	["illustrator:wakasagihime"] = "りすたる",
	["designer:wakasagihime"] = "Paysage",
	["luashizhu"] = "拾珠",
	[":luashizhu"] = "限定技，你可以展示弃牌堆顶的最多五张牌，获得其中与最后一张牌同花色的那些牌。若你本回合未使用、打出或弃置所获得牌，则此回合结束时你可以重置一个限定技，否则你流失一点体力。",
	["luarenyu"] = "人鱼",
	["@luarenyu"] = "选择要因“人鱼”交出去的那张牌",
	["@luarenyu2"] = "选择要因“人鱼”获得刚刚那张牌的角色",
	["@luarenyu3"] = "你可以使用这张亮起的牌",
	[":luarenyu"] = "限定技发动后，你可以交给一名角色一张牌并令其使用之，然后你手牌上限永久+1。",

}