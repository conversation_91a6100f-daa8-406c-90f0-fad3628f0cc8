--[[两者的差别列出如下：
			表t					列表l
索引		t[i]				l:at(i-1)
长度		#t					l:length()
插入		table.insert(t,foo)	l:append(foo)
迭代算子	ipairs(t)			sgs.qlist(l)
                                sgs.IntList()
--]]

Pay = require "paysage" --加载价值模组
math.randomseed(tostring(os.time()):reverse():sub(1, 7)) --------设置时间种子

--sgs.ai_armor_value 对于持有xx技能防具增值

local luacaihuo_skill={} -- 初始化 luacaihuo_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 luacaihuo_skill 只是为了出于习惯
luacaihuo_skill.name="luacaihuo" -- 设置 name
table.insert(sgs.ai_skills, luacaihuo_skill) -- 把这个表加入到 sgs.ai_skills 中

luacaihuo_skill.getTurnUseCard = function(self)
	
	if #self.enemies > 0 and not self.player:hasFlag("luacaihuo_used") then 
		return sgs.Card_Parse("#luacaihuo:.:")
	end 
end 

sgs.ai_skill_use_func["#luacaihuo"] = function(cardS, use, self)
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByUseValue(cards, true)
		
	local use_card
	for _,card in ipairs(cards) do
		local can_use = true
		for i = 0, 300 do
			local str = "luacaihuo" .. tostring(i) 
			if self.player:getMark(str) > 0 then 
				if card:getId() == i then return false end 
				local dummy = sgs.Sanguosha:getCard(i)
				if dummy then 
					if dummy:objectName() == card:objectName() then can_use = false end 
				end 
			end 
		end 	
    	local slash = sgs.Sanguosha:cloneCard("snatch", sgs.Card_SuitToBeDecided, -1)
        slash:addSubcard(card:getEffectiveId())
        if not slash:isAvailable(self.player) then can_use = false end 
		slash:deleteLater()
		if can_use then 
			use_card = card
			break 
		end 
	end 
	
	if not use_card then return end 
	local targets
	local slash = sgs.Sanguosha:cloneCard("snatch")
	slash:setSkillName("luacaihuo")
	slash:addSubcard(use_card:getEffectiveId())	
	local dummy_use = { isDummy = true , to = sgs.SPlayerList() }
	self:useTrickCard(slash, dummy_use)	
	if dummy_use.card and dummy_use.to and dummy_use.to:length() > 0 then 		
		targets = dummy_use.to
	end 

	if not targets then return end 	
	use.card = sgs.Card_Parse("#luacaihuo:" .. use_card:getEffectiveId() .. ":") 
	if use.to then use.to = targets end
	return		
end 

function sgs.ai_cardsview_valuable.luacaihuo(self, class_name, player)
	if class_name ~= "Jink" then return end
	if player:getMark("@skill_invalidity") > 0 then return end
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)

	local use_card
	for _,card in ipairs(cards) do
		local can_use = true
		for i = 0, 300 do
			local str = "luacaihuo" .. tostring(i) 
			if self.player:getMark(str) > 0 then 
				if card:getId() == i then return false end 
				local dummy = sgs.Sanguosha:getCard(i)
				if dummy then 
					if dummy:objectName() == card:objectName() then can_use = false end 
				end 
			end 
		end 	
		if can_use then 
			use_card = card
			break 
		end 
	end 
	
	if not use_card then return end 
	
	return "#luacaihuo:" .. use_card:getEffectiveId() .. ":" .. "jink"	
end

sgs.ai_use_priority.luacaihuo = 4.7



local luazaie_skill = {}
luazaie_skill.name = "luazaie"
table.insert(sgs.ai_skills, luazaie_skill)
luazaie_skill.getTurnUseCard = function(self, inclusive)
	if self.player:hasUsed("#luazaie") then return end 	
	if (self.player:getMark("@joon") ~= 13) then return end 
	if #self.enemies == 0 then return end 
	return sgs.Card_Parse("#luazaie:.:") 
end 

sgs.ai_skill_use_func["#luazaie"] = function(card, use, self)
	if #self.enemies == 0 then return end 
	local enemies = self.enemies
	self:sort(enemies, "defense")
	use.card = sgs.Card_Parse("#luazaie:.:") 
	if use.to then 
		use.to:append(enemies[1]) 
		return 
	end 
end 

sgs.ai_card_intention.luazaie = 40
sgs.ai_use_priority.luazaie = 9

sgs.pay_ai_card.Peach.Luayuelong = function(self, card, use, mustusepeach) 
	if self.player:hasSkill("Luayuelong") and (self:getOverflow() <= 0) and card and card:getSkillName() ~= "Luayuelong" and not use.shoutu then return 2 end
end 
sgs.ai_skill_choice.Luayuelong = function(self, choices)
	local num = self.room:getTag("Luayuelong"):toInt() 
	self.room:writeToConsole("检测到了月胧弃牌，需弃置合计点数应为"..tostring(num))
	if self.player:getMark("@yueni") > 1 then return "Luayuelong1" end 
	local handcards = self.player:getHandcards()
	local allcards = {}
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		if self:getUseValue(c) < sgs.ai_keep_value.Peach  then 
			table.insert(allcards, c)
		end 
	end
	local equips = self.player:getEquips()
	for _,c in sgs.qlist(equips) do
		if not c:isKindOf("EightDiagram") then 
			table.insert(allcards, c)
		end 
	end	

	local i = 0
	for _,card in ipairs(allcards) do
		if not self.player:isJilei(card) then 
			i = i + card:getNumber()
		end 
	end		
	if num == 0 then return "Luayuelong2" end 
	if i > num then 
		return "Luayuelong1"
	end 
	return "Luayuelong2"
end
sgs.ai_skill_cardchosen.Luayuelong = function(self, who, flags)
	if who:objectName() == self.player:objectName() then
		if self.player:hasSkill("lirang") then return nil end 
		local handcards = self.player:getHandcards()
		local allcards = {}
		for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
			table.insert(allcards, c)
		end
		local equips = self.player:getEquips()
		for _,c in sgs.qlist(equips) do
			table.insert(allcards, c)
		end	
		local num = self.room:getTag("Luayuelong"):toInt() 
		self.room:writeToConsole("检测到了月胧弃牌，需弃置合计点数应为"..tostring(num))
		Pay.orderByQuick2(self, allcards, 1, #allcards)
		local discard_list = {} 
		for _,card in ipairs(allcards) do
			self.room:writeToConsole("卡ID为："..card:getId().."点数为："..card:getNumber())
			if (self:getUseValue(card) < (sgs.ai_keep_value.Peach - 0.35)) and card:getNumber() >= num then 
				table.insert(discard_list, c)
			end 
		end 
		if #discard_list > 0  then 
			self:sortByKeepValue(discard_list)
			return discard_list[1]
		else
			for _,card in ipairs(allcards) do
				if (self:getUseValue(card) < (sgs.ai_keep_value.Peach - 0.35)) then 
					table.insert(discard_list, c)
				end 
			end 
			if #discard_list > 0 then 	
				return discard_list[1]
			else
				return allcards[1]
			end 
		end 
	end
end
sgs.ai_skill_invoke.Luayuelong = function(self, data)
	local use = data:toCardUse()
	local target = use.to:at(0)
	if not self:isFriend(target) then return true end 
	return false 
end 

sgs.ai_skill_cardask["@tianzhao-slash"] = function(self, data)
	local shinki = self.room:findPlayerBySkillName("luatianzhao")
	if not shinki or not self:isFriend(shinki) then return "." end

	if self.player:hasSkills("luajianji|luashuangren|luahakurei|LuaBisha|Luashenqiang|Luayuelong") then
		if self:getCardsNum("Slash") == 1 and self:getFriendNumBySeat(self.player, shinki) > 1 then return "." end
	end
	local luatianzhaotargets = {}
	for _, player in sgs.qlist(self.room:getAllPlayers()) do
		if player:hasFlag("luatianzhaoTarget") then	--yun
			if self:isFriend(player) and not (self:needToLoseHp(player, sgs.luatianzhaosource, true, true) or self:getDamagedEffects(player, sgs.luatianzhaosource, true))	--yun
			then return "."
			end
			table.insert(luatianzhaotargets, player)
		end
	end

	if #luatianzhaotargets == 0 then
		return self:getCardId("Slash") or "."
	end

	self:sort(luatianzhaotargets, "defenseSlash")
	local slashes = self:getCards("Slash")
	for _, slash in ipairs(slashes) do
		for _, target in ipairs(luatianzhaotargets) do
			if not self:slashProhibit(slash, target, sgs.luatianzhaosource) and self:slashIsEffective(slash, target, sgs.luatianzhaosource) then
				return slash:toString()
			end
		end
	end
	return "."
end

sgs.ai_skill_cardask["@tianzhao-jink"] = function(self, data)
	local shinki = self.room:findPlayerBySkillName("luatianzhao")
	if (not shinki)  then return "." end
	--if self.player:hasSkill("LuaYuanzu") then return self:getCardId("Jink") or "." end
	if not self:isFriend(shinki) then return "." end
	if self:getCardsNum("Jink") > 2 then return self:getCardId("Jink") or "." end
	for _, friend in ipairs(self.friends_noself) do
		if self:hasEightDiagramEffect(friend) then return "." end
	end
	if not (self:isWeak() and self:getCardsNum("Jink") == 1 and not self:isWeak(shinki)) then
		if self:getCardId("Jink") and not self:isWeak(shinki) then
			local jink = sgs.Card_Parse(self:getCardId("Jink"))
			for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skill:objectName()]
				if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](self.player, jink, self) then
					return "."
				end
			end
		end
		if self:getOverflow(shinki) > 1 and not self:isWeak(shinki) then return "." end
		return self:getCardId("Jink") or "."
	end
end

sgs.ai_skill_cardask["@tianzhao-ana"] = function(self, data)
	local shinki = self.room:findPlayerBySkillName("luatianzhao")
	if not shinki or not self:isFriend(shinki) then return "." end

	if not self:isWeak() then
		return self:getCardId("Analeptic") or "."
	end
end

sgs.ai_skill_cardask["@tianzhao-peach"] = function(self, data)
	local shinki = self.room:findPlayerBySkillName("luatianzhao")
	if not shinki or not self:isFriend(shinki) then return "." end

	if not self:isWeak() then
		return self:getCardId("Peach") or "."
	end
end

local shenpan_skill = {}
shenpan_skill.name = "shenpan"
table.insert(sgs.ai_skills, shenpan_skill)
shenpan_skill.getTurnUseCard = function(self, inclusive)
	if not self.player:hasUsed("#shenpan") then
		return sgs.Card_Parse("#shenpan:.:")

	end
end
sgs.ai_skill_use_func["#shenpan"] = function(card, use, self)
	use.card = sgs.Card_Parse("#shenpan:.:")
	if use.to then
		use.to:append(self.player)
		return
	end
end

sgs.ai_use_priority.shenpan = 7.2

sgs.ai_skill_invoke.luatianzhaoA = function(self, data)
	local lieges = self.room:getAlivePlayers()
	if #self.friends_noself == 0 then return end
	if lieges:isEmpty() then return end
	local has_friend = false
	for _, p in sgs.qlist(lieges) do
		if self:isFriend(p) or sgs.evaluatePlayerRole(p) == "neutral" then
			has_friend = true
			break
		end
	end

	for _, friend in ipairs(self.friends_noself) do
		if self:getOverflow() <= 0 then return true end
	end
	local count = 0
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:isKindOf("Slash") then count = count + 1 end
	end
	return count < 2 and has_friend
end
sgs.ai_skill_invoke.luatianzhaoB = function(self, data)
	local lieges = self.room:getAlivePlayers()
	if lieges:isEmpty() then return end
	if #self.friends_noself == 0 then return end
	local has_friend = false
	for _, p in sgs.qlist(lieges) do
		if self:isFriend(p) or sgs.evaluatePlayerRole(p) == "neutral" then
			has_friend = true
			break
		end
	end

	for _, friend in ipairs(self.friends_noself) do
		if self:hasEightDiagramEffect(friend) then return true end
	end
	local count = 0
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:isKindOf("Jink") then count = count + 1 end
	end
	return count < 2 and has_friend
end
sgs.ai_skill_invoke.luatianzhaoC = function(self, data)
	local lieges = self.room:getAlivePlayers()
	if #self.friends_noself == 0 then return end
	if lieges:isEmpty() then return end
	local has_friend = false
	for _, p in sgs.qlist(lieges) do
		if self:isFriend(p) or sgs.evaluatePlayerRole(p) == "neutral" then
			has_friend = true
			break
		end
	end
	local count = 0
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:isKindOf("Analeptic") then count = count + 1 end
	end
	return count < 2 and has_friend
end
sgs.ai_skill_invoke.luatianzhaoD = function(self, data)
	if #self.friends_noself == 0 then return end
	if self.player:hasFlag("shenpaning") and not self.player:hasFlag("shenpaningtao") and not self:isWeak() then return end
	if self.player:hasFlag("shenpaning") and self.player:hasFlag("shenpaningtao") and #self.enemies < 2 then return end
	for _, friend in ipairs(self.friends_noself) do
		if getCardsNum("Peach", friend, self.player) > 0.5 then return true end
	end
	for _, friend in ipairs(self.friends_noself) do
		for _, card in sgs.list(friend:getHandcards()) do
			if card:isKindOf("Peach") then return true end
		end
	end

	return false
end

local luatianzhao_skill = {}
luatianzhao_skill.name = "luatianzhao"
table.insert(sgs.ai_skills, luatianzhao_skill)
luatianzhao_skill.getTurnUseCard = function(self)
	local lieges = self.room:getAlivePlayers()
	if lieges:isEmpty() then return end
	local has_friend
	for _, p in sgs.qlist(lieges) do
		if not self:isEnemy(p) then
			has_friend = true
			break
		end
	end
	if not has_friend then return end
	if (self.player:hasFlag("Global_luatianzhaoSlashFailed") or not self:slashIsAvailable())
		and self.player:hasFlag("Global_luatianzhaoPeachFailed") then return end
	if self.player:getMark("tianzhaoUse") > 3 then return end
	return sgs.Card_Parse("#luatianzhao:.:")
end

sgs.ai_skill_use_func["#luatianzhao"] = function(card, use, self)
	if self.player:hasFlag("tianzhaojiu") and not self.player:hasFlag("Global_luatianzhaoAnaFailed") then
		self.room:writeToConsole("shinki test ABC2")
		if self.player:hasFlag("Global_luatianzhaoAnaFailed") then self.room:writeToConsole("shinki test ABC3") end
		self.player:setFlags("Global_luatianzhaoAnaFailed") --总之，酒只能恰一次
		use.card = card
        self.room:setPlayerFlag(self.player, "-tianzhaojiu")
		if use.to then use.to:append(self.player) end
		return
	end
	self:sort(self.enemies, "defenseSlash")
	self.room:writeToConsole("shinki test ABC" .. "  ")
	--[[if not sgs.luatianzhaotarget then table.insert(sgs.ai_global_flags, "luatianzhaotarget") end
	sgs.luatianzhaotarget = {}]]--
	if not self.player:hasFlag("Global_luatianzhaoSlashFailed") and self:slashIsAvailable() then
		local dummy_use = { isDummy = true }
		dummy_use.to = sgs.SPlayerList()
		if self.player:hasFlag("slashTargetFix") then
			for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
				if p:hasFlag("SlashAssignee") then
					dummy_use.to:append(p)
				end
			end
		end
		local slash = sgs.Sanguosha:cloneCard("slash")
		self:useCardSlash(slash, dummy_use) 
		if dummy_use.card and dummy_use.to:length() > 0 then
			use.card = card
			for _, p in sgs.qlist(dummy_use.to) do
				slash:deleteLater()
				if use.to then use.to:append(p) end
				return
			end
		else
			slash:deleteLater()
		end
	end
	if not self.player:hasFlag("Global_luatianzhaoPeachFailed") and self.player:isWounded() then
		use.card = card
		if use.to then use.to:append(self.player) end
		return
	end
end


sgs.ai_use_value.luatianzhao = 8.5
sgs.ai_use_priority.luatianzhao = 3.56

function sgs.ai_cardsview_valuable.luatianzhao(self, class_name, player, need_lord)
	if self.player:getMark("tianzhaoUse") > 3 then return end
	if class_name == "Slash" and sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE
			and not player:hasFlag("Global_luatianzhaoSlashFailed") and (need_lord == false or player:hasSkill("luatianzhao")) then
		local current = self.room:getCurrent()
		if current:isAlive() and self:getOverflow(current) > 0 and not self:hasCrossbowEffect(current) then
			self.player:setFlags("stack_overflow_luatianzhao")
			local isfriend = self:isFriend(current, player)
			self.player:setFlags("-stack_overflow_luatianzhao")
			if isfriend then return "#luatianzhao:.:" end
		end

		local cards = player:getHandcards()
		local count = 0
		for _, card in sgs.qlist(cards) do
			if isCard("Slash", card, player) then count = count + 1 end
		end
		if count > 1 then return end

		local lieges = self.room:getAlivePlayers()
		if lieges:isEmpty() then return end
		local has_friend = false
		for _, p in sgs.qlist(lieges) do
			self.player:setFlags("stack_overflow_luatianzhao")
			has_friend = self:isFriend(p, player)
			self.player:setFlags("-stack_overflow_luatianzhao")
			if has_friend then break end
		end
		if has_friend then return "#luatianzhao:.:" end
	end
	local touse = false
	if class_name == "Analeptic" or class_name ==  "Peach" or class_name ==  "Analeptic+Peach" then
		for _, friend in ipairs(self.friends_noself) do
			for _, card in sgs.list(friend:getHandcards()) do
				if card:isKindOf("Peach") then touse = true end
			end
		end
		if class_name == "Analeptic" then
			for _, friend in ipairs(self.friends_noself) do
				for _, card in sgs.list(friend:getHandcards()) do
					if card:isKindOf("Analeptic") then touse = true end
				end
			end
		end
	end
	if not touse then return end
	if class_name == "Analeptic" and player:hasSkill("luatianzhao") and not player:hasFlag("Global_luatianzhaoAnaFailed") then
		self.room:setPlayerFlag(self.player, "tianzhaojiu")
		return "#luatianzhao:.:"
	end
	if class_name == "Peach" and player:hasSkill("luatianzhao") and not player:hasFlag("Global_luatianzhaoPeachFailed") then
		self.room:setPlayerFlag(self.player, "tianzhaojiu")
		return "#luatianzhao:.:"
	end
end

sgs.ai_skill_playerchosen.shenpan = function(self, targets)
	self:sort(self.enemies, "defense")
	for _, to in ipairs(self.enemies) do
		if targets:contains(to) then
			return to
		end
	end
	return nil
end

sgs.ai_skill_invoke.Luashenpan = function(self, data)
	self.room:writeToConsole("裁决运算中")
	local damage = data:toDamage()
	local invoke
	local to = damage.to
	for _, skill in sgs.qlist(to:getVisibleSkillList()) do
		if string.find(sgs.bad_skills, skill:objectName()) then return self:isFriend(to) end	--yun
	end
	local handcards = self.player:getCards("h")
	handcards = sgs.QList2Table(handcards) 
	local Paixu_table = Pay.SortCardByKeepValue(self,handcards,false,false,false) 
	--local p = #Paixu_table
	return Paixu_table[1][2] < 7.6
end 
sgs.ai_skill_cardask["@Luashenpan"] = function(self, data, pattern, target)
	self.room:writeToConsole("裁决卡运算中")
	local handcards = self.player:getCards("h")
	handcards = sgs.QList2Table(handcards) 
	local function huase2(card)
		local huase
		if card:getSuit() == sgs.Card_Heart then
			huase = "heart"
		elseif card:getSuit() == sgs.Card_Diamond then
			huase = "diamond"
		elseif card:getSuit() == sgs.Card_Club then
			huase = "club"
		elseif card:getSuit() == sgs.Card_Spade then
			huase = "spade"
		end
		return huase
	end 
	if #handcards == 0 then return "." end  
	local rhandcards = {}
	local huase1 = pattern:split("|")[2]:split(",")
	for _, card in ipairs(handcards) do
		if not self.player:isJilei(card) then 
			for _, huase in ipairs(huase1) do
				if huase2(card) == huase then table.insert(rhandcards, card) end 
			end 
		end 
	end 
	if #rhandcards == 0 then return "." end  
	self:sortByKeepValue(rhandcards)
	return "$" .. rhandcards[1]:getEffectiveId()
end 
sgs.ai_skill_cardask["@Luashenpan2"] = function(self, data, pattern, target)
	self.room:writeToConsole("裁决卡2运算中")
	for _, skill in sgs.qlist(self.player:getVisibleSkillList()) do
		if string.find(sgs.bad_skills, skill:objectName()) then return "." end	--yun
	end	
	local allcards = self.player:getCards("he")
	allcards = sgs.QList2Table(allcards) 	
	local new_all = {}
	local i = 0 
	local num = self.room:getTag("Luashenpan"):toInt() 	
	for _, c in ipairs(allcards) do	
		if (target:getWeapon() and target:getWeapon():isKindOf("Crossbow")) or target:hasSkill("paoxiao") then 
			i = 1
		end 		
		if i == 0 then 
			table.insert(new_all, c)
		end 
		i = 0
	end 
	local Heart_cards = {}
	local Club_cards = {}	
	local Diamond_cards = {}	
	local Spade_cards = {}
	if #new_all == 0 then return "." end 
	for _, c in ipairs(new_all) do	
		if c:getSuit() == sgs.Card_Heart then 
			table.insert(Heart_cards, c)
		end 
		if c:getSuit() == sgs.Card_Club then 
			table.insert(Club_cards, c)
		end 
		if c:getSuit() == sgs.Card_Diamond then 
			table.insert(Diamond_cards, c)
		end 
		if c:getSuit() == sgs.Card_Spade then 
			table.insert(Spade_cards, c)
		end 
	end 	
	self.room:writeToConsole("裁决卡2运算中 633")
	local p = 0
	local huase = pattern:split("|")[2]	
	if #Heart_cards > 0 then 
		p = p + 1 
		self:sortByKeepValue(Heart_cards)
	else
		if huase == "heart" then return "." end 
	end 
	if #Club_cards > 0 then 
		p = p + 1 
		self:sortByKeepValue(Club_cards)
	else
		if huase == "club" then return "." end 
	end 
	if #Diamond_cards > 0 then 
		p = p + 1
		self:sortByKeepValue(Diamond_cards)
	else
		if huase == "diamond" then return "." end 
	end 		
	if #Spade_cards > 0 then
		p = p + 1 
		self:sortByKeepValue(Spade_cards)
	else
		if huase == "spade" then return "." end 
	end 
	local y_handcards = target:getCards("h")	
	local hq 
	if num >= self.player:getHp() then
		hq = 1
	elseif target:isKongcheng() then 
		hq = 0
	else
		local w = y_handcards:length()
		hq = (56 - 15 * num - math.exp(num) + p * (p - w + 1 + num) * 3 - 16 * (w - num)) / 50 
		if hq < 0 then hq = 0 end 
		self.room:writeToConsole("裁决卡hq运算值为"..hq)
	end 
	if huase == "heart" and math.random() > hq then return "$" .. Heart_cards[1]:getEffectiveId() end 
	if huase == "club" and math.random() > hq then return "$" .. Club_cards[1]:getEffectiveId() end 
	if huase == "diamond" and math.random() > hq then return "$" .. Diamond_cards[1]:getEffectiveId() end 
	if huase == "spade" and math.random() > hq then return "$" .. Spade_cards[1]:getEffectiveId() end 
	return "."
end
sgs.ai_choicemade_filter.skillInvoke.luatianzhaoA = function(self, player, promptlist)
	if promptlist[#promptlist] == "yes" then
		sgs.tianzhaosource = player
	end
end
sgs.ai_choicemade_filter.skillInvoke.luatianzhaoB = function(self, player, promptlist)
	if promptlist[#promptlist] == "yes" then
		sgs.tianzhaosource = player
	end
end
sgs.ai_choicemade_filter.skillInvoke.luatianzhaoC = function(self, player, promptlist)
	if promptlist[#promptlist] == "yes" then
		sgs.tianzhaosource = player
	end
end
sgs.ai_choicemade_filter.skillInvoke.luatianzhaoD = function(self, player, promptlist)
	if promptlist[#promptlist] == "yes" then
		sgs.tianzhaosource = player
	end
end
sgs.ai_choicemade_filter.skillInvoke.luatianzhaoB = sgs.ai_choicemade_filter.skillInvoke.luatianzhaoA
sgs.ai_choicemade_filter.skillInvoke.luatianzhaoC = sgs.ai_choicemade_filter.skillInvoke.luatianzhaoA
sgs.ai_choicemade_filter.skillInvoke.luatianzhaoD = sgs.ai_choicemade_filter.skillInvoke.luatianzhaoA

sgs.ai_choicemade_filter.cardResponded["@tianzhao-slash"] = function(self, player, promptlist)
	if promptlist[#promptlist] ~= "_nil_" then
		if sgs.tianzhaosource and not (player:hasSkill("LuaYuanzu") and sgs.tianzhaosource:hasFlag("shenpaning")) then
			sgs.updateIntention(player, sgs.tianzhaosource, -80)
		end
		sgs.tianzhaosource = nil
	elseif sgs.tianzhaosource then
		local lieges = player:getRoom():getAlivePlayers()
		if lieges and not lieges:isEmpty() then
			if player:objectName() == lieges:last():objectName() then
				sgs.tianzhaosource = nil
			end
		end
	end
end

sgs.ai_choicemade_filter.cardResponded["@tianzhao-jink"] = function(self, player, promptlist)
	if promptlist[#promptlist] ~= "_nil_" then
		if sgs.tianzhaosource and not (self:hasEightDiagramEffect(player) and sgs.tianzhaosource:hasFlag("shenpaning"))
			and not (player:hasSkill("LuaYuanzu") and sgs.tianzhaosource:hasFlag("shenpaning")) then
			sgs.updateIntention(player, sgs.tianzhaosource, -80)
		end
		sgs.tianzhaosource = nil
	elseif sgs.tianzhaosource then
		local lieges = player:getRoom():getAlivePlayers()
		if lieges and not lieges:isEmpty() then
			if player:objectName() == lieges:last():objectName() then
				sgs.tianzhaosource = nil
			end
		end
	end
end

sgs.ai_choicemade_filter.cardResponded["@tianzhao-peach"] = function(self, player, promptlist)
	if promptlist[#promptlist] ~= "_nil_"  and sgs.tianzhaosource then
		sgs.updateIntention(player, sgs.tianzhaosource, -80)
		sgs.tianzhaosource = nil
	elseif sgs.tianzhaosource then
		local lieges = player:getRoom():getAlivePlayers()
		if lieges and not lieges:isEmpty() then
			if player:objectName() == lieges:last():objectName() then
				sgs.tianzhaosource = nil
			end
		end
	end
end

sgs.ai_choicemade_filter.cardResponded["@tianzhao-ana"] = sgs.ai_choicemade_filter.cardResponded["@tianzhao-peach"]

sgs.ai_skill_playerchosen.luahuapu = function(self, targets)
	return sgs.ai_skill_playerchosen.damage(self, targets)
end
sgs.ai_skill_use["@@luahuapu"] = function(self, prompt)
	local clubs = {}
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:getSuit() == sgs.Card_Club then
			table.insert(clubs, card:getEffectiveId())
		end
		if #clubs == 4 then break end
	end
	if #clubs == 1 and self:getOverflow() > 1 then return "." end
	if #clubs == 2 and not self.player:isWounded() and self:getOverflow() > 1 then return "." end
	if (#clubs == 3 or #clubs == 4) and #self.enemies == 0 and not self.player:isWounded() and self:getOverflow() > 1 then return "." end
	if #clubs == 1 or (#clubs == 2 and not self.player:isWounded())
			or ((#clubs == 3 or #clubs == 4) and #self.enemies == 0 and not self.player:isWounded()) then
		return "#luahuapu:".. clubs[1] .. ":"
		--return "#luahuapu:".. table.concat(clubs, "+") .. ":"
	elseif #clubs == 2 and (((#clubs == 3 or #clubs == 4) and #self.enemies == 0) or self:isWeak()) then
		return "#luahuapu:".. clubs[1]  .. "+" .. clubs[2] .. ":"
	else
		return "#luahuapu:".. table.concat(clubs, "+") .. ":"
	end
end
sgs.ai_skill_discard.luahuapu = function(self)
	local card_ids = {}
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(cards)
	for _, card in ipairs(cards) do
		table.insert(card_ids, card:getEffectiveId())
	end
	local clubs = {}
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:getSuit() == sgs.Card_Club then
			table.insert(clubs, card:getEffectiveId())
		end
	end
	if #clubs >= 5 or self:getOverflow() > 1 then
		return {card_ids[1], card_ids[2]}
	end
	if not (#self.enemies == 0 and not self.player:isWounded()) and #cards - #clubs > 0 then
		return {card_ids[1], card_ids[2]}
	end
end
sgs.ai_cardneed.luahuapu = function(to, card, self)
	return card:getSuit() == sgs.Card_Club
end





--早苗ai
sgs.ai_skill_invoke.luaqiji = function(self, data)
	return true
end
function sgs.ai_slash_prohibit.luaqiji(self, from, to, card)
	if to:hasSkill("luaqiji") and self:hasEightDiagramEffect(to) then
		if not IgnoreArmor(from, to) then
			return true
		end
	end
end
sgs.ai_skill_invoke.mishu9 = function(self, data)
	local judge = data:toJudge()
	local enemies = self.enemies
	if not self.player:isWounded() and judge.reason == "luaqiji" then return true end
	for _, enemy in ipairs(enemies) do	
		if not enemy:isKongcheng() then 
			return self:needRetrial(judge) 
		end 
	end
end 

function needmishu(self)
	local handcards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(handcards)	
	local jink = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
	local jink_v = self:getKeepValue(jink, true, true) - 0.75
	if self.player:isKongcheng() then return false end 
	if self.player:getHandcardNum() == 1 and (self.player:containsTrick("supply_shortage") or self.player:containsTrick("indulgence")) then return false end 
	if self.player:getLostHp() < 1 and self:getKeepValue(handcards[1]) > jink_v and (self:getOverflow() == 0) then return false end 
	for _, enemy in ipairs(self.enemies) do	--拆桃酒
		local cards = sgs.QList2Table(enemy:getHandcards())
		local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
		if #cards <= 2 and not enemy:isKongcheng() and not self:doNotDiscard(enemy, "h", true) 
			and not enemy:containsTrick("gainb") then
			for _, cc in ipairs(cards) do
				if (cc:hasFlag("visible") or cc:hasFlag(flag)) then
					return true 
				end
			end
		end
	end		
	for _, enemy in ipairs(self.enemies) do	
		if self:isWeak(enemy) then return true end 
	end
	return true
end 

sgs.ai_skill_choice.luaqiji2 = function(self, choices)
	if needmishu(self) then 
		for _, enemy in ipairs(self.enemies) do	--拆桃酒
			local cards = sgs.QList2Table(enemy:getHandcards())
			local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
			if #cards <= 2 and not enemy:isKongcheng() and not self:doNotDiscard(enemy, "h", true) 
				and not enemy:containsTrick("gainb") then
				for _, cc in ipairs(cards) do
					if (cc:hasFlag("visible") or cc:hasFlag(flag)) then
						return cc:getSuitString() 
					end
				end
			end
		end
		if not self.player:isKongcheng() then
			local cards = sgs.QList2Table(self.player:getHandcards())
			self:sortByKeepValue(cards)
			return cards[1]:getSuitString()
		end

		for _, enemy in ipairs(self.enemies) do	
			if self:isWeak(enemy) then return "diamond" end 
		end 
	end 
	
	local x = math.random()
	if x > 0.75 then return "spade" end 
	if x > 0.5 then return "club" end 
	if x > 0.25 then return "diamond" end 
	return "heart"
end
sgs.ai_skill_cardchosen.mishu9 = function(self, who, flags)
	if self.room:getTag("luaqiji2") and self.room:getTag("luaqiji2"):toString() ~= "" then
		local pattern = self.room:getTag("luaqiji2"):toString()
		pattern = pattern:split(",")
		for _,name in ipairs(pattern) do
			if who:objectName() == self.player:objectName() then
				for _, c in sgs.qlist(self.player:getHandcards()) do
					if (c:getSuitString() == name) then
						return c:getEffectiveId()
					end
				end
			else
				local cards = sgs.QList2Table(who:getHandcards())
				local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), who:objectName())
				for _, cc in ipairs(cards) do
					if (cc:hasFlag("visible") or cc:hasFlag(flag)) then
						if (cc:getSuitString() == name) then return cc:getEffectiveId() end
					end
				end
			end
		end
	end
end
sgs.ai_skill_playerchosen.mishu9 = function(self, targets)
	local enemies = self.enemies
	self:sort(enemies, "defense")

	if self.room:getTag("luaqiji2") and self.room:getTag("luaqiji2"):toString() ~= "" then 
		local pattern = self.room:getTag("luaqiji2"):toString()
		pattern = pattern:split(",")
		for _,name in ipairs(pattern) do
			if self.player:getLostHp() > 1 then
				for _, c in sgs.qlist(self.player:getHandcards()) do
					if (c:getSuitString() == name) then
						return self.player
					end
				end
			end
			for _, enemy in ipairs(enemies) do	--拆桃酒
				local cards = sgs.QList2Table(enemy:getHandcards())
				local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
				if #cards <= 2 and not enemy:isKongcheng() and not self:doNotDiscard(enemy, "h", true) 
					and not enemy:containsTrick("gainb") then
					for _, cc in ipairs(cards) do
						if (cc:hasFlag("visible") or cc:hasFlag(flag)) then
							if (cc:getSuitString() == name) then return enemy end 
						end
					end
				end
			end			
		end 
		
		for _,name in ipairs(pattern) do
			if (name == "diamond") or (name == "heart") then 
				for _, enemy in ipairs(enemies) do	
					if self:isWeak(enemy) and not enemy:isKongcheng() then return enemy end 
				end 				
			end 
		end 
	end 
	for _, enemy in ipairs(enemies) do	
		if not enemy:isKongcheng() then 
			return enemy
		end 
	end
end 

sgs.ai_playerchosen_intention.mishu9 = 20


function usewhatcard(self)
	local handcards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(handcards)	
	if #handcards == 0 then return end 
	local slash = {}
	slash[1] = "fire_slash"
	slash[2] = "thunder_slash"
	slash[3] = "slash"

	local aoe = {}
	aoe[1] = "archery_attack"
	aoe[2] = "savage_assault"
	
	local lostH = self.player:getLostHp()
	local usetime = lostH - self.player:usedTimes("luaqiji")

	for i = 1, 2 do
		local trick2 = sgs.Sanguosha:cloneCard(aoe[i], sgs.Card_SuitToBeDecided, -1)
		trick2:addSubcard(handcards[1]:getEffectiveId())	
		if self:getAoeValue(trick2, self.player) >= 80 then 
			return aoe[i]
		end 
	end 	

	local trick9 = sgs.Sanguosha:cloneCard("god_salvation", sgs.Card_SuitToBeDecided, -1)
	trick9:addSubcard(handcards[1]:getEffectiveId())	
	local value_o = self:godSalvationValue(trick9)
	self.room:writeToConsole(value_o)
	if (value_o > 25) and (usetime == 1) then 
		return "god_salvation"
	end 
	
	local basic1 = sgs.Sanguosha:cloneCard("peach", sgs.Card_SuitToBeDecided, -1)
	basic1:addSubcard(handcards[1]:getEffectiveId())	
	if (self.player:getLostHp() > 1) and (usetime == 1) then
		local dummyuse = { isDummy = true }
		self:useBasicCard(basic1, dummyuse)
		if dummyuse.card then 
			return "peach"  
		end 
	end 
	
	local trick = sgs.Sanguosha:cloneCard("duel", sgs.Card_SuitToBeDecided, -1)
	trick:addSubcard(handcards[1]:getEffectiveId())	
	if self:ceshi(trick, false, true) then return "duel" end 

	local trick8 = sgs.Sanguosha:cloneCard("ex_nihilo", sgs.Card_SuitToBeDecided, -1)
	trick8:addSubcard(handcards[1]:getEffectiveId())	
	if trick8:isAvailable(self.player) and (self.player:getHandcardNum() < usetime) then return "ex_nihilo" end 
			
	trick9 = sgs.Sanguosha:cloneCard("god_salvation", sgs.Card_SuitToBeDecided, -1)
	trick9:addSubcard(handcards[1]:getEffectiveId())	
	value_o = self:godSalvationValue(trick9)
	self.room:writeToConsole(value_o)
	if (value_o > 15) and (usetime == 1) then 
		return "god_salvation"
	end 
	
	basic1 = sgs.Sanguosha:cloneCard("peach", sgs.Card_SuitToBeDecided, -1)
	basic1:addSubcard(handcards[1]:getEffectiveId())	
	if (self.player:getLostHp() == 1) and (usetime == 1) then 
		local dummyuse = { isDummy = true }
		self:useBasicCard(basic1, dummyuse)
		if dummyuse.card then 
			return "peach"  
		end 
	end 

	local trick2 = sgs.Sanguosha:cloneCard("snatch", sgs.Card_SuitToBeDecided, -1)
	trick2:addSubcard(handcards[1]:getEffectiveId())	
	if self:ceshi(trick2, true) then return "snatch" end 

	for i = 1, 2 do
		trick2 = sgs.Sanguosha:cloneCard(aoe[i], sgs.Card_SuitToBeDecided, -1)
		trick2:addSubcard(handcards[1]:getEffectiveId())	
		if self:getAoeValue(trick2, self.player) >= 60 then 
			return aoe[i]
		end 
	end 	
	
	for i = 1, 3 do
		local basic2 = sgs.Sanguosha:cloneCard(slash[i], sgs.Card_SuitToBeDecided, -1)
		basic2:addSubcard(handcards[1]:getEffectiveId())	
		if self:YouMu() and (usetime > 1) then 
			local dummyuse = { isDummy = true }
			self:useBasicCard(basic2, dummyuse)
			if dummyuse.card then 
				return slash[i]  
			end 
		end 
	end 
		
	local trick3 = sgs.Sanguosha:cloneCard("dismantlement", sgs.Card_SuitToBeDecided, -1)   --
	trick3:addSubcard(handcards[1]:getEffectiveId())	
	if self:ceshi(trick3, true) then return "dismantlement" end 
	
	local trick4 = sgs.Sanguosha:cloneCard("snatch", sgs.Card_SuitToBeDecided, -1)
	trick4:addSubcard(handcards[1]:getEffectiveId())	
	if self:ceshi(trick4) then return "snatch" end 	
	
	local trick5 = sgs.Sanguosha:cloneCard("duel", sgs.Card_SuitToBeDecided, -1)
	trick5:addSubcard(handcards[1]:getEffectiveId())	
	if self:ceshi(trick5) then return "duel" end 

	local trick6 = sgs.Sanguosha:cloneCard("indulgence", sgs.Card_SuitToBeDecided, -1)
	trick6:addSubcard(handcards[1]:getEffectiveId())	
	if self:ceshi(trick6, false, false, true) then return "indulgence" end 

	for i = 1, 2 do
		local basic2 = sgs.Sanguosha:cloneCard(slash[i], sgs.Card_SuitToBeDecided, -1)
		basic2:addSubcard(handcards[1]:getEffectiveId())	
		local dummyuse = { isDummy = true, to = sgs.SPlayerList() }
		self:useBasicCard(basic2, dummyuse)
		if dummyuse.card and not dummyuse.to:isEmpty() then
			for _, p in sgs.qlist(dummyuse.to) do
				if self:isGoodChainTarget(p, self.player, nil, nil, dummyuse.card) then 
					return slash[i]  
				end 
			end 
		end 
	end 

	for i = 1, 3 do
		local basic2 = sgs.Sanguosha:cloneCard(slash[i], sgs.Card_SuitToBeDecided, -1)
		basic2:addSubcard(handcards[1]:getEffectiveId())	
		local dummyuse = { isDummy = true, to = sgs.SPlayerList() }
		self:useBasicCard(basic2, dummyuse)
		if dummyuse.card and not dummyuse.to:isEmpty() then
			for _, p in sgs.qlist(dummyuse.to) do
				if self:hasHeavySlashDamage(self.player, damage.card, p) then 
					return slash[i]  
				end 
			end 
		end 
	end 
	
	local trick7 = sgs.Sanguosha:cloneCard("supply_shortage", sgs.Card_SuitToBeDecided, -1)
	trick7:addSubcard(handcards[1]:getEffectiveId())	
	if self:ceshi(trick7) then return "supply_shortage" end 

	for i = 1, 2 do
		local trickX = sgs.Sanguosha:cloneCard(aoe[i], sgs.Card_SuitToBeDecided, -1)
		trickX:addSubcard(handcards[1]:getEffectiveId())
		if self:getAoeValue(trickX, self.player) >= 40 then
			return aoe[i]
		end 
	end 
	
	local basic5 = sgs.Sanguosha:cloneCard("peach", sgs.Card_SuitToBeDecided, -1)
	basic5:addSubcard(handcards[1]:getEffectiveId())
	if (self.player:getLostHp() > 1) and (usetime == 2) then 
		local dummyuse = { isDummy = true }
		self:useBasicCard(basic5, dummyuse)
		if dummyuse.card then 
			return "peach"  
		end 
	end 

	local trick80 = sgs.Sanguosha:cloneCard("ex_nihilo", sgs.Card_SuitToBeDecided, -1)
	trick80:addSubcard(handcards[1]:getEffectiveId())
	if trick80:isAvailable(self.player) and (self.player:getHandcardNum() <= self.player:getMaxCards()) then return "ex_nihilo" end
	
	for _, ecard in ipairs(self:getTurnUse(true)) do
		if ecard:isKindOf("Slash") then 	
			return "analeptic"
		end 
	end 
	
	self.room:writeToConsole("你这绝对出bug了，怎么回事")
	return "ex_nihilo"
end 


local luaqiji_skill = {}
luaqiji_skill.name = "luaqiji"
table.insert(sgs.ai_skills, luaqiji_skill)

luaqiji_skill.getTurnUseCard = function(self)	
	if not self.player:hasFlag("qiji9_success") then return end 	
	local lostH = self.player:getMaxHp() - self.player:getHp()
	if self.player:isKongcheng() or (not self.player:hasFlag("qiji9_success")) or (self.player:usedTimes("luaqiji") >= lostH) then return end
	if self.player:isKongcheng() then return end
	return sgs.Card_Parse("#luaqiji:.:")
end 
sgs.ai_skill_use_func["#luaqiji"] = function(card, use, self)
	--local usecard = self.player:getTag("luaqiji"):toCard()
	local handcards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(handcards)		
	local usecard
	local function dummy4(cardX)
		local dummy_use = { isDummy = true , to = sgs.SPlayerList()}
		self:useTrickCard(cardX, dummy_use)
		if dummy_use.to:length() > 0 then
			return true
		end
		return false
	end
	for _, ecard in ipairs(handcards) do
		if not ecard:isKindOf("ExNihilo") and not (ecard:isKindOf("Snatch") and dummy4(ecard))then
			usecard = ecard
		end
	end
	if not usecard then return end
	local userstring = usewhatcard(self)
	local taoluancard = sgs.Sanguosha:cloneCard(userstring, usecard:getSuit(), usecard:getNumber())
	
	taoluancard:setSkillName("luaqiji")
	self.room:writeToConsole("早苗测试")
	taoluancard:addSubcard(usecard:getId())
	
	local dummy_use = { isDummy = false , to = sgs.SPlayerList() }
	
	if taoluancard:getTypeId() == sgs.Card_TypeBasic then
		self:useBasicCard(taoluancard, dummy_use)
	else
		assert(taoluancard)
		--self.room:writeToConsole("早苗测试")
		self:useTrickCard(taoluancard, dummy_use)
	end
	if userstring == "jink" and userstring == "nullification" then return false end
	self.room:writeToConsole("早苗测试A")
	if not dummy_use.card then return end
	--self.room:writeToConsole("早苗测试B")
	self.room:writeToConsole("早苗测试B" .. userstring)
	if dummy_use.to and not dummy_use.to:isEmpty() then 
		if dummy_use.to[1] then 
			self.room:writeToConsole("早苗测试目标" .. dummy_use.to[1]:objectName()) 
		end 
	end 
	self.room:addPlayerHistory(self.player, "luaqiji")
	use.card = taoluancard
	if use.to then 
		use.to = dummy_use.to 
		if math.random() < 0.009 then self.player:speak("我都玩了⑨年了，技术不比你强？") end 
		return
	end 
	return
end 

sgs.ai_cardsview["luaqiji"] = function(self, class_name, player)
	local classname2objectname = {
		["Slash"] = "slash", ["Jink"] = "jink",
		["Peach"] = "peach", ["Analeptic"] = "analeptic",
		["Nullification"] = "nullification",
		["FireSlash"] = "fire_slash", ["ThunderSlash"] = "thunder_slash"
	}
	local name = classname2objectname[class_name]
	if not name then return end
	local no_have = true
	local cards = player:getCards("h")
	cards = sgs.QList2Table(cards)	--yun
	self:sortByKeepValue(cards)	
	for _,c in ipairs(cards) do	--yun
		if c:isKindOf(class_name) then
			no_have = false
			break
		end
	end
	if not no_have then return end
	if class_name == "Peach" and player:getMark("Global_PreventPeach") > 0 then return end
	--cards = sgs.QList2Table(cards)	--yun
	if not cards[1] then return end	--yun	1329有警告
	if cards[1]:isKindOf("Peach") and not self:OverFlowPeach(cards[1]) then return end  
	local suit = cards[1]:getSuitString()
	local number = cards[1]:getNumberString()
	local card_id = cards[1]:getEffectiveId()
	if player:hasSkill("luaqiji") and player:hasFlag("qiji9_success") and (player:usedTimes("luaqiji") < player:getLostHp()) then
		return (name..":luaqiji[%s:%s]=%d"):format(suit, number, card_id)
	end
end  --这是……划时代的创举！新时代的开启！2019年4月15日19:37:05

sgs.ai_skill_choice.luaqiji = function(self, choices)
	local choice = usewhatcard(self)
	if self.player:isKongcheng() then return end 
	self.room:writeToConsole("sanae decide to choose" .. choice)
	return choice
end 


function useshenjucard(self)
	local toUse = {}
	local cards = self:getTurnUse(true)
	for _, card in ipairs(cards) do
		if self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand and card:isKindOf("EquipCard") then 
			if self:getSameEquip(card, self.player) then 
				table.insert(toUse, card)
			end 
		end 
	end 
	local fcaerds = sgs.QList2Table(self.player:getCards("he"))
	local jink = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
	local jink_v = self:getKeepValue(jink, true, true) - 1
	for _, card in ipairs(fcaerds) do
		if (not table.contains(cards, card)) and self:getKeepValue(card) <= jink_v and (not table.contains(toUse, card))
			and (not card:isKindOf("Nullification")) then 
			table.insert(toUse, card)
		end 
	end 
	for _, card in ipairs(fcaerds) do
		if card:isKindOf("Slash") and (not table.contains(toUse, card)) then
			local dummy_use = { isDummy = true , to = sgs.SPlayerList() }
			self:useBasicCard(card, dummy_use)	
			if (not dummy_use.card) or (not dummy_use.to) or (dummy_use.to:length() == 0) then 
				table.insert(toUse, card)
			else
				if not self:YouMu2(dummy_use.to:at(0)) then table.insert(toUse, card) end 				
			end 
		end
	end 
	local jinks = self:getCardsNum("Jink")
	local jcount = 0
	for _, card in ipairs(fcaerds) do
		if (jcount == jinks - 1) or (jinks == 0) then break end 
		if card:isKindOf("Jink") and not table.contains(toUse, card) then 
			table.insert(toUse, card)
			jcount = jcount + 1
		end
	end 
	
	local peachs = self:getCardsNum("Peach")
	for _, card in ipairs(fcaerds) do
		if card:isKindOf("Peach") and not table.contains(toUse, card) and self:OverFlowPeach(card) then 
			table.insert(toUse, card)
		end
	end 	

	return toUse
end 

local luashenju_skill = {}
luashenju_skill.name = "luashenju"
table.insert(sgs.ai_skills, luashenju_skill)

luashenju_skill.getTurnUseCard = function(self)	
	local cards = self:getTurnUse(true)
	if (self.player:containsTrick("indulgence") and self.player:containsTrick("supply_shortage")) then return end 
	if self.player:hasFlag("forbidshenju") then return end 
	if self:canKillEnermyAtOnce(cards) then return end 
	if #useshenjucard(self) == 0 then return end 
	if (self.player:getEquips():length() > 3) and not (self.player:isWounded()) then return end 
	if self.player:containsTrick("supply_shortage") and self:getOverflow(target) > 0 then return end 
	return sgs.Card_Parse("#luashenju:.:")	
end 

sgs.ai_skill_use_func["#luashenju"] = function(card, use, self)
	local cards = useshenjucard(self)
	if #cards == 0 then return end 
	self:sortByKeepValue(cards)
	use.card = sgs.Card_Parse("#luashenju:".. cards[1]:getId() ..":") 
end 

sgs.ai_skill_choice.luashenju = function(self, choices)
	local x = self.player:getMaxCards() - self.player:getHandcardNum()
	if x >= 2 then return "indulgence" end 
	return "supply_shortage"
end 

sgs.ai_skill_choice.luashenjuq = function(self, choices)
	local mosthp = true
	local mosteqp = true
	for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if p:getHp() >= self.player:getHp() then mosthp = false ; break end 
	end 		
	for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if p:getEquips():length() >= self.player:getEquips():length() then mosteqp = false ; break end 
	end 
	if mosthp and not mosteqp then return "draw" end 
	if self.player:getHp() < 5 then return "recover" end
	return "draw"
end 
sgs.ai_use_priority.luashenju = 4.7
sgs.ai_cardneed.luashenju = function(to, card, self)
	return card:isKindOf("Crossbow") or card:isKindOf("Axe")
end 
sgs.ai_skill_use["@@lualianshe"] = function(self, prompt)
	if #self.enemies == 0 then return "." end 
	local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
    slash:deleteLater()
	local enemies = {}
	for _, enemy in ipairs(self.enemies) do
		if (not self.player:isCardLimited(slash, sgs.Card_MethodUse)) and self:slashIsEffective(slash, enemy, self.player)
			and (not self:slashProhibit(slash, enemy))
			and (not self.player:isProhibited(enemy, slash, enemy:getSiblings())) then 
			table.insert(enemies, enemy)
		end 
	end 
	self:sort(enemies, "defense")
	if #enemies == 0 then return "." end 
	local pa = self.player:getHandcardNum()
	local pb = self.player:getEquips():length()
	local pc = self.player:getHp()
	local x = 4
	for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if p:getHandcardNum() >= pa then x = x - 1 ; break end 
	end 
	for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if p:getEquips():length() >= pb then x = x - 1 ; break end 
	end 
	for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if p:getHp() >= pc then x = x - 1 ; break end 
	end 			
	if x > 2 then return "#lualianshe:.:->" .. enemies[1]:objectName()  end 
	local y = self.player:getCards("he"):length()
	if x > 1 and y < 3 then 
		local cards = sgs.QList2Table(self.player:getCards("he"))
		for _, card in ipairs(cards) do
			if card:isKindOf("sakura") or card:isKindOf("Peach") or card:isKindOf("SupplyShortage")
				or card:isKindOf("ExNihilo") or card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield")
					or card:isKindOf("RenwangShield") then 
				return "."
			end 
		end 
		return "#lualianshe:.:->" .. enemies[1]:objectName() 
	end 
end 

sgs.ai_skill_cardask["@lualianshe"] = function(self, prompt)
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	if #cards == 0 then return "." end
	return "$" .. cards[1]:getId()
end 

local luaduhuo_skill = {}
luaduhuo_skill.name = "luaduhuo"
table.insert(sgs.ai_skills, luaduhuo_skill)

luaduhuo_skill.getTurnUseCard = function(self)	
	if self.player:hasUsed("#luaduhuo") then return end 
	if self.player:isKongcheng() then return end 
	if self.enemies == 0 then 
		if self.player:getMark("luajiyuan") ~= 0 then return end 
		local count = 0
		for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
			if ((player:inMyAttackRange(self.player) and not self.player:hasArmorEffect("renwang_shield") and not self.player:hasArmorEffect("eight_diagram"))
				or (player:getHandcardNum() > 3) or player:hasSkills(sgs.straight_damage_skill)) and not self:isFriend(player) then 
				count = count + 1
			end 
		end 
		if count > 1 then return end 
		if self:isWeak() then return end 		
		return sgs.Card_Parse("#luaduhuo:.:") 
	end 
	
	local count = 0
	local cards = sgs.QList2Table(self.player:getHandcards())
	for _, card in ipairs(cards) do
		if card:getSuit() == sgs.Card_Heart or ((card:getSuit() == sgs.Card_Spade) and self.player:hasSkill("hongyan")) then 
			count = count + 1
			break
		end 
	end 
	for _, card in ipairs(cards) do
		if card:getSuit() == sgs.Card_Diamond then 
			count = count + 1
			break
		end 
	end 
	for _, card in ipairs(cards) do
		if card:getSuit() == sgs.Card_Club then 
			count = count + 1
			break
		end 
	end 
	for _, card in ipairs(cards) do
		if card:getSuit() == sgs.Card_Spade and not self.player:hasSkill("hongyan") then 
			count = count + 1
			break
		end 
	end 
	if count > 2 then return end 
	if #cards == 2 and (count == 2) then 
		for _, card in ipairs(cards) do
			if card:isKindOf("sakura") or card:isKindOf("Peach") or (card:isKindOf("Jink") and self.player:getLostHp() > 1)
				or card:isKindOf("ExNihilo") or card:isKindOf("EightDiagram") or (card:isKindOf("Nullification") and self:getCardsNum("Nullification") == 1)
					or card:isKindOf("RenwangShield") then 
				return 
			end 
		end 	
	end
	return sgs.Card_Parse("#luaduhuo:.:") 
end 

sgs.ai_skill_use_func["#luaduhuo"] = function(card, use, self)
	use.card = sgs.Card_Parse("#luaduhuo:.:") 		
	local enermy = self.enemies
	self:sort(enermy, "defense")
	if #enermy > 0 then 
		if use.to then use.to:append(enermy[1]) ; return end		
	end 
	if use.to then use.to:append(self.player) ; return end	
end 

function sgs.ai_skill_suit.luaduhuo(self)
	if self.player:hasFlag("gluaduhuo") then		
		local cards = sgs.QList2Table(self.player:getHandcards())
		self:sortByKeepValue(cards)
		local suit = cards[1]:getSuit()
		return suit
	else
		local tablep = {}
		tablep["heart"] = 0
		tablep["diamond"] = 0
		tablep["club"] = 0
		tablep["spade"] = 0
		
		local cards = sgs.QList2Table(self.player:getHandcards())
		for _, card in ipairs(cards) do
			if card:getSuit() == sgs.Card_Heart or ((card:getSuit() == sgs.Card_Spade) and self.player:hasSkill("hongyan")) then 
				tablep["heart"] = tablep["heart"] + 1
			end 
		end 	
		for _, card in ipairs(cards) do
			if card:getSuit() == sgs.Card_Diamond then 
				tablep["diamond"] = tablep["diamond"] + 1
			end 
		end 
		for _, card in ipairs(cards) do
			if card:getSuit() == sgs.Card_Club then --spade
				tablep["club"] = tablep["club"] + 1
			end 
		end 
		for _, card in ipairs(cards) do
			if card:getSuit() == sgs.Card_Spade and not self.player:hasSkill("hongyan") then 
				tablep["spade"] = tablep["spade"] + 1
			end 
		end 		
		
		local most = true
		for _, cardf in ipairs(cards) do
			local xp = tablep[cardf:getSuitString()]
			if xp then 
				for i, j in pairs(tablep) do				
					if j > xp then most = false ; break end 
				end 	
				if most then self.room:writeToConsole("桥姬花色测试" .. cardf:getSuitString()) ; return cardf:getSuit() end 
				most = true
			end 			
		end 
	end 
end 

sgs.ai_skill_cardchosen.luaduhuo = function(self, who, flags)
	if self.player:hasFlag("gluaduhuo") then
		local cards = sgs.QList2Table(self.player:getHandcards())
		self:sortByKeepValue(cards)
		return cards[1]:getEffectiveId()
	end 
end 

sgs.pay_ai_card.Peach.luajiyuan = function(self, card, use, mustusepeach) 
	if self.player:hasSkill("luajiyuan") and (self.player:getMark("luajiyuan") == 0) then	--pay 帕露西
		if self.player:getLostHp() == 1 then
			if self.player:getMark("@luaduhuo") == 2 then return 2 end
			local count = 0
			local cards = sgs.QList2Table(self.player:getHandcards())
			for _, cardH in ipairs(cards) do
				if cardH:getSuit() == sgs.Card_Heart or ((cardH:getSuit() == sgs.Card_Spade) and self.player:hasSkill("hongyan")) then
					count = count + 1
					break
				end
			end
			for _, cardH in ipairs(cards) do
				if cardH:getSuit() == sgs.Card_Diamond then
					count = count + 1
					break
				end
			end
			for _, cardH in ipairs(cards) do
				if cardH:getSuit() == sgs.Card_Club then
					count = count + 1
					break
				end
			end
			for _, cardH in ipairs(cards) do
				if cardH:getSuit() == sgs.Card_Spade and not self.player:hasSkill("hongyan") then
					count = count + 1
					break
				end
			end
			if count < 2 then return 2 end
		end
	end
end 
	
function basic_func(self, olayer)
	local pa = olayer:getHandcardNum()
	local pb = olayer:getEquips():length()
	local pc = olayer:getHp()
	local x = 5
	for _, p in sgs.qlist(self.room:getOtherPlayers(olayer)) do
		if p:getHandcardNum() < pa then x = x - 1 ; break end 
	end 
	for _, p in sgs.qlist(self.room:getOtherPlayers(olayer)) do
		if p:getEquips():length() < pb then x = x - 1 ; break end 
	end 
	for _, p in sgs.qlist(self.room:getOtherPlayers(olayer)) do
		if p:getHp() < pc then x = x - 1 ; break end 
	end 			
	return x
end 
	
function sortByCan(self, players)

    local compare_func = function(a, b)
        local v1 = basic_func(self, a)
        local v2 = basic_func(self, b)

        if v1 ~= v2 then
            return v1 > v2
        else
            return sgs.getDefense(a) < sgs.getDefense(b)
        end
    end

    table.sort(players, compare_func)
	return players
end

sgs.ai_card_intention.luaduhuo = 10

sgs.ai_use_priority.luaduhuo = function(self)
	if self.player:hasSkill("luaxinyan") and not self.player:hasUsed("#luaxinyan") then 
		local count = 0
		local cards = sgs.QList2Table(self.player:getHandcards())
		for _, card in ipairs(cards) do
			if card:getSuit() == sgs.Card_Heart or ((card:getSuit() == sgs.Card_Spade) and self.player:hasSkill("hongyan")) then 
				count = count + 1
				break
			end 
		end 
		for _, card in ipairs(cards) do
			if card:getSuit() == sgs.Card_Diamond then 
				count = count + 1
				break
			end 
		end 
		for _, card in ipairs(cards) do
			if card:getSuit() == sgs.Card_Club then 
				count = count + 1
				break
			end 
		end 
		for _, card in ipairs(cards) do
			if card:getSuit() == sgs.Card_Spade and not self.player:hasSkill("hongyan") then 
				count = count + 1
				break
			end 
		end 		
		if count == 1 then return 0.8 end 
	else
		return 0.3
	end 
	return 0.3
end 

local luaxinyan_skill = {}
luaxinyan_skill.name = "luaxinyan"
table.insert(sgs.ai_skills, luaxinyan_skill)

luaxinyan_skill.getTurnUseCard = function(self)	
	if self.player:hasUsed("#luaxinyan") then return end 
	return sgs.Card_Parse("#luaxinyan:.:") 
end 

sgs.ai_skill_use_func["#luaxinyan"] = function(card, use, self)
	local x = basic_func(self, self.player)
	if x <= 1 then 
		local friends = self.friends
		friends = sortByCan(self, friends)
		local y = basic_func(self, friends[1])
		if y <= 1 then 
			use.card = sgs.Card_Parse("#luaxinyan:.:") 	
			if use.to then use.to:append(self.player) ; return end			
		else
			use.card = sgs.Card_Parse("#luaxinyan:.:") 	
			if use.to then use.to:append(friends[1]) ; return end						
		end 
	else
		use.card = sgs.Card_Parse("#luaxinyan:.:") 	
		if use.to then use.to:append(self.player) ; return end			
	end 
end 

sgs.ai_card_intention.luaxinyan = -20
sgs.ai_use_priority.luaxinyan = 0.5

sgs.ai_skill_use["@@Luayuelong"] = function(self, prompt)
	--if self.player:hasSkill("Luayuelong") then
		local cards = sgs.QList2Table(self.player:getHandcards())
	local shouldUseWine = false
		self:sortByUseValue(cards)
		for _,card in ipairs(cards) do				
			if card:isKindOf("Slash") then
				local dummy_use = {isDummy = true}
				self:useBasicCard(card, dummy_use)
				if dummy_use.card then 
					shouldUseWine = true 
					break
				end
			end 
		end 		
		for _, card in ipairs(cards) do
			if card:isBlack() then 
				if card:getTypeId() == sgs.Card_TypeTrick and not card:isKindOf("Nullification") then
					local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
					self:useTrickCard(card, dummy_use)
					if dummy_use.card then
						if dummy_use.to:isEmpty() and not card:isKindOf("IronChain") then	--yun
							return "#Luayuelong:".. card:getId() ..":"
						else
							local target_objectname = {}
							for _, p in sgs.qlist(dummy_use.to) do
								table.insert(target_objectname, p:objectName())
							end
							return "#Luayuelong:".. card:getId() ..":->" .. table.concat(target_objectname, "+")
						end
					end
				elseif card:getTypeId() == sgs.Card_TypeBasic and not card:isKindOf("Jink") then 
					local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
					self:useTrickCard(card, dummy_use)
					if dummy_use.card then
						if dummy_use.to:isEmpty() then	--yun
							if card:isKindOf("Analeptic") then 
								if shouldUseWine then return dummy_use.card:toString() end 
							else
								return "#Luayuelong:".. card:getId() ..":"
							end 
						else
							local target_objectname = {}
							for _, p in sgs.qlist(dummy_use.to) do
								table.insert(target_objectname, p:objectName())
							end
							return "#Luayuelong:".. card:getId() ..":->" .. table.concat(target_objectname, "+")  
						end
					end					
				elseif card:getTypeId() == sgs.Card_TypeEquip then
					local dummy_use = { isDummy = true }
					self:useEquipCard(card, dummy_use)
					if dummy_use.card then
						return "#Luayuelong:".. card:getId() ..":"
					end
				end
			end 
		end
		for _, card in ipairs(cards) do
			if not (card:isBlack()) then 
				if card:getTypeId() == sgs.Card_TypeTrick and not card:isKindOf("Nullification") then
					local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
					self:useTrickCard(card, dummy_use)
					if dummy_use.card then
						if dummy_use.to:isEmpty() and not card:isKindOf("IronChain") then	--yun
							return "#Luayuelong:".. card:getId() ..":"
						else
							local target_objectname = {}
							for _, p in sgs.qlist(dummy_use.to) do
								table.insert(target_objectname, p:objectName())
							end
							return "#Luayuelong:".. card:getId() ..":->" .. table.concat(target_objectname, "+")
						end
					end
				elseif card:getTypeId() == sgs.Card_TypeBasic and not card:isKindOf("Jink") then 
					local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
					self:useTrickCard(card, dummy_use)
					if dummy_use.card then
						if dummy_use.to:isEmpty() then	--yun
							if card:isKindOf("Analeptic") then 
								if shouldUseWine then return "#Luayuelong:".. card:getId() ..":" end 
							else
								return "#Luayuelong:".. card:getId() ..":"
							end 
						else
							local target_objectname = {}
							for _, p in sgs.qlist(dummy_use.to) do
								table.insert(target_objectname, p:objectName())
							end
							return "#Luayuelong:".. card:getId() ..":->" .. table.concat(target_objectname, "+")
						end
					end					
				elseif card:getTypeId() == sgs.Card_TypeEquip then
					local dummy_use = { isDummy = true }
					self:useEquipCard(card, dummy_use)
					if dummy_use.card then
						return "#Luayuelong:".. card:getId() ..":"
					end
				end
			end 
		end		
	--end 
	return "."
end

function findweiwocard(self)
	local cards = sgs.QList2Table(self.player:getHandcards())
	local slash = sgs.Sanguosha:cloneCard("quanxiang", sgs.Card_NoSuit, 0)
	local slash_v = self:getUseValue(slash)
	self:sortByUseValue(cards,true)
	local toUse = {}
	
	local function indulgence(card)
		if card:isKindOf("Indulgence") then
			local dummy_use = { isDummy = true , to = sgs.SPlayerList() }
			self:useTrickCard(card, dummy_use)	
			if dummy_use.to:length() > 0 then 		
				if self:getIndulgenceValue(dummy_use.to:at(0)) > 1 then return true end 
				return false 
			end 
		end 
		return false
	end 
	
	for _, card in ipairs(cards) do
		if card:getSuit() == sgs.Card_Heart then
			if (not (card:isKindOf("Peach") and not self:OverFlowPeach(card))
				and not (card:isKindOf("Analeptic") and self:isWeak())
				and not (card:isKindOf("OffensiveHorse") and not self.player:getOffensiveHorse())
				and not (card:isKindOf("Wanbaochui"))
				and not (card:isKindOf("DefensiveHorse") and not self.player:getDefensiveHorse())
				and not card:isKindOf("ExNihilo") and not indulgence(card)) or self.player:containsTrick("gainb") then
				table.insert(toUse, card)
			end 	
		end 
	end 
	for _, card in ipairs(cards) do
		if card:getSuit() == sgs.Card_Spade and (not self.player:hasUsed("quanxiang")) then 
			if ((self:getUseValue(card) < slash_v)
				and not (card:isKindOf("Analeptic") and self:isWeak())
				and not (card:isKindOf("OffensiveHorse") and not self.player:getOffensiveHorse())
				and not (card:isKindOf("DefensiveHorse") and not self.player:getDefensiveHorse())
				and not card:isKindOf("ExNihilo")) or self.player:containsTrick("gainb")  then
				table.insert(toUse, card)
			end 	
		end 
	end
	return toUse
end 
local Luaweiwo_skill = {}
Luaweiwo_skill.name = "Luaweiwo"
table.insert(sgs.ai_skills, Luaweiwo_skill)

Luaweiwo_skill.getTurnUseCard = function(self)	 
	if #findweiwocard(self) > 0 then 
		self.room:writeToConsole("丰姬测试0")
		return sgs.Card_Parse("#Luaweiwo:.:") 
	end 
end
function sgs.ai_cardneed.Luaweiwo(to, card, self)
	return card:getSuit() == sgs.Card_Heart
end
sgs.ai_skill_use_func["#Luaweiwo"] = function(cardF, use, self)
	local toUse = findweiwocard(self)
	if #toUse == 0 then return end 
	
	for _, card in ipairs(toUse) do
		if card:getSuit() == sgs.Card_Heart then 
			local dismantlement = sgs.Sanguosha:cloneCard("dismantlement", card:getSuit(), card:getNumber())
			dismantlement:setSkillName("Luaweiwo")
			local dummy_use = { isDummy = true , to = sgs.SPlayerList()}
			self:useTrickCard(dismantlement, dummy_use)	
			self.room:writeToConsole("丰姬测试")
			if dummy_use.to:length() > 0 then 
				self.room:writeToConsole("丰姬测试2")
				use.card = sgs.Card_Parse("#Luaweiwo:".. card:getId() ..":") 
				if use.to then use.to:append(dummy_use.to:at(0)) ; return end	
			end 
		end 
	end 
	
	for _, card in ipairs(toUse) do
		if card:getSuit() == sgs.Card_Spade then 	
			local quanxiang = sgs.Sanguosha:cloneCard("quanxiang", card:getSuit(), card:getNumber())
			quanxiang:setSkillName("Luaweiwo")
			local dummy_use = { isDummy = true , to = sgs.SPlayerList()}
			self:useTrickCard(quanxiang, dummy_use)	
			if dummy_use.to:length() > 0 then 			
				use.card = sgs.Card_Parse("#Luaweiwo2:".. card:getId() ..":")
				if use.to then use.to:append(dummy_use.to:at(0)) ; return end			
			end 
		end 
	end 
end 

sgs.ai_use_priority.Luaweiwo = sgs.ai_use_priority.Dismantlement - 0.5

sgs.ai_skill_playerchosen.Luashanhai = function(self, targets)
	self.room:writeToConsole("山海测试")
	for _, player in sgs.qlist(targets) do
		if player:objectName() == self.player:objectName() then return player end 
	end
	self:sort(self.friends_noself, "defense")
	for _, friend in ipairs(self.friends_noself) do
		if targets:contains(friend) then
			return friend
		end			
	end 
	return nil
end 

sgs.ai_playerchosen_intention.Luashanhai = -20

local lualinglan_skill = {}
lualinglan_skill.name = "lualinglan"
table.insert(sgs.ai_skills, lualinglan_skill)

local function linglantarget(self)
	if self:isWeak() and self.player:isWounded() then return self.player end
	if self.player:isWounded() then
		if self.player:hasArmorEffect("silver_lion") then
			return self.player
		end
	end
	for _, friend in ipairs(self.friends) do
		if friend:isWounded() then
			for _,card in sgs.qlist(friend:getEquips()) do
				if card:getSuit() == sgs.Card_Club then
					return friend
				end
			end
			if not friend:isKongcheng() and (self:getKnownNum(friend) == friend:getHandcardNum()) then
				for _, card in sgs.list(friend:getHandcards()) do
					if card:getSuit() == sgs.Card_Club then
						return friend
					end
				end
			end
		end
	end
	local targets = {}
	local rcards = self:getTurnUse(true)
	for _,card in ipairs(rcards) do
		if card:isKindOf("Slash") then
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			self:useBasicCard(card, dummy_use)
			if dummy_use.card and dummy_use.to and (dummy_use.to:length() > 0) then
				for _, to in sgs.qlist(dummy_use.to) do
					table.insert(targets, to)
				end
			end
		end
		if card:isKindOf("Duel") then
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			self:useTrickCard(card, dummy_use)
			if dummy_use.card and dummy_use.to and (dummy_use.to:length() > 0) then
				for _, to in sgs.qlist(dummy_use.to) do
					table.insert(targets, to)
				end
			end
		end
	end
	self:sort(targets,"defense")
	for _, enemy in ipairs(targets) do
		if enemy:getLostHp() == 1 then
			for _,card in sgs.qlist(enemy:getEquips()) do
				if card:getSuit() == sgs.Card_Club then
					return enemy
				end
			end
			if not enemy:isKongcheng() and (self:getKnownNum(enemy) == enemy:getHandcardNum()) then
				for _, card in sgs.list(enemy:getHandcards()) do
					if card:getSuit() == sgs.Card_Club then
						return enemy
					end
				end
			end
		end
	end
	for _, friend in ipairs(self.friends) do
		if friend:isWounded() then
			return friend
		end
	end
	for _, enemy in ipairs(targets) do
		if enemy:getLostHp() == 1 then
			return enemy
		end
	end
end
local function linglancard(self)
	local toUse = {}
	local handcards = sgs.QList2Table(self.player:getHandcards())
	local slashcount = self:getCardsNum("Slash")
	local function SLAsh(slash)
		if slashcount > 1 then return false end
		local rcCount = 0
		for _, to in ipairs(self.enemies) do
			if not to:isWounded() and self:slashIsEffective(slash, to, self.player) then
				rcCount = rcCount + 1
			end
		end
		return rcCount > 0
	end

	self:sortByUseValue(handcards, true)
	for _, card in ipairs(handcards) do
		if card:getSuit() == sgs.Card_Club then
			local bool_3 = (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu"))
					and ((self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
					or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
			if not card:isKindOf("Duel") and not (card:isKindOf("AOE") and self:getAoeValue(card) > 15) and not (card:isKindOf("Crossbow") and slashcount > 2)
					and not bool_3
					and not card:isKindOf("SilverLion") and not (card:isKindOf("Slash") and (self:canKillEnermyAtOnce(false, card) or SLAsh(card))) then
				table.insert(toUse, card:getEffectiveId())
			end
		end
	end
	return toUse[1]
end
lualinglan_skill.getTurnUseCard = function(self)	 
	if linglancard(self) and not self.player:hasUsed("#lualinglan") then
		return sgs.Card_Parse("#lualinglan:.:") 
	end 
end

sgs.ai_skill_use_func["#lualinglan"] = function(card, use, self)
	local card_0 = linglancard(self)
	if not card_0 then return end
	local target = linglantarget(self)
	if not target then return end
	use.card = sgs.Card_Parse("#lualinglan:" .. card_0 .. ":")
	if use.to then use.to:append(target); return end
end 

--sgs.ai_card_intention.lualinglan = 20
sgs.ai_use_priority.lualinglan = sgs.ai_use_priority.ExNihilo - 0.7

sgs.ai_skill_choice.luajunying = function(self, choices)
	if self.player:containsTrick("indulgence") then return "Player_Judge" end
	if self.player:containsTrick("supply_shortage") then return "Player_Judge" end
	local card_0 = linglancard(self)
	local target = linglantarget(self)
	if card_0 and target and self.player:getHandcardNum() > 1 then return "Player_Play" end
	return "Player_Draw"
end

sgs.ai_need_damaged.Luayuelong = function(self, attacker, player)
	if not self:isWeak() then
		if self.player:hasSkill("Luayuelong") then
			for _, card in sgs.list(self.player:getHandcards()) do
				if not card:isKindOf("Jink") and not card:isKindOf("Nullification") then
					--self.room:writeToConsole("受兔ai测试")
					self.room:setPlayerFlag(self.player, "LuayuelongQ")
					if card:isKindOf("EquipCard") then
						local dummy_use = { isDummy = true , shoutu = true}
						self:useEquipCard(card, dummy_use)
						if dummy_use.card then
							return true
						end
					elseif card:isKindOf("TrickCard") then
						local dummy_use = { isDummy = true , shoutu = true}
						self:useTrickCard(card, dummy_use)
						if dummy_use.card then
							return true
						end
					elseif card:isKindOf("BasicCard") then
						local dummy_use = { isDummy = true , shoutu = true}
						self:useBasicCard(card, dummy_use)
						if dummy_use.card then
							return true
						end
					end
					self.room:setPlayerFlag(self.player, "-LuayuelongQ")
				end
			end
		end
	end
end

sgs.ai_skill_askforag.luashenju = function(self, card_ids)
	for _, id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(id)
		if card:isKindOf("EightDiagram") and not self:getSameEquip(card) then return id end
		if card:isKindOf("RenwangShield") and not self:getSameEquip(card) then return id end
		if card:isKindOf("OffensiveHorse") and not self:getSameEquip(card) then return id end
		if card:isKindOf("DefensiveHorse") and not self:getSameEquip(card) then return id end
	end
end

local luaguoshi_skill = {}
luaguoshi_skill.name = "luaguoshi"
table.insert(sgs.ai_skills, luaguoshi_skill)

luaguoshi_skill.getTurnUseCard = function(self)
	if self.player:isWounded() then
		return sgs.Card_Parse("#luaguoshi:.:")
	end
end
sgs.ai_use_priority.luaguoshi = 15
sgs.ai_skill_use_func["#luaguoshi"] = function(card, use, self)
	use.card = sgs.Card_Parse("#luaguoshi:.:")
	if use.to then use.to = sgs.SPlayerList()  return end
end

sgs.ai_skill_playerchosen.luaguoshi = function(self, players)
	local enemies = self.enemies
	self:sort(enemies, "defense")
	for _, enemy in ipairs(self.enemies) do
		if players:contains(enemy) and self:damageIsEffective(enemy, sgs.DamageStruct_Normal, self.player) then
			return enemy
		end
	end
end

sgs.ai_skill_use["@@luayueni"] = function(self, prompt)
	local shouldUseWine = false
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByUseValue(cards)
	for _,card in ipairs(cards) do
		if card:isKindOf("Slash") then
			local dummy_use = {isDummy = true}
			self:useBasicCard(card, dummy_use)
			if dummy_use.card then
				shouldUseWine = true
				break
			end
		end
	end
	for _, card in ipairs(cards) do
			if card:getTypeId() == sgs.Card_TypeTrick and not card:isKindOf("Nullification") then
				local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
				self:useTrickCard(card, dummy_use)
				if dummy_use.card then
					if dummy_use.to:isEmpty() and not card:isKindOf("IronChain") then	--yun
						return "#luayueni:".. card:getId() ..":"
					else
						local target_objectname = {}
						for _, p in sgs.qlist(dummy_use.to) do
							table.insert(target_objectname, p:objectName())
						end
						return "#luayueni:".. card:getId() ..":->" .. table.concat(target_objectname, "+")
					end
				end
			elseif card:getTypeId() == sgs.Card_TypeBasic and not card:isKindOf("Jink") then
				local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
				self:useTrickCard(card, dummy_use)
				if dummy_use.card then
					if dummy_use.to:isEmpty() then	--yun
						if card:isKindOf("Analeptic") then
							if shouldUseWine then return dummy_use.card:toString() end
						else
							return "#luayueni:".. card:getId() ..":"
						end
					else
						local target_objectname = {}
						for _, p in sgs.qlist(dummy_use.to) do
							table.insert(target_objectname, p:objectName())
						end
						return "#luayueni:".. card:getId() ..":->" .. table.concat(target_objectname, "+")
					end
				end
			elseif card:getTypeId() == sgs.Card_TypeEquip then
				local dummy_use = { isDummy = true }
				self:useEquipCard(card, dummy_use)
				if dummy_use.card then
					return "#luayueni:".. card:getId() ..":"
				end
			end
	end
	return "."
end

local lualianmeng_skill = {}
lualianmeng_skill.name = "lualianmeng"
table.insert(sgs.ai_skills, lualianmeng_skill)
lualianmeng_skill.getTurnUseCard = function(self)
	if self.player:getMark("@lianmeng") > 0 then
		return sgs.Card_Parse("#lualianmeng:.:")
	end

end
sgs.ai_use_priority.lualianmeng = 15
sgs.ai_skill_use_func["#lualianmeng"] = function(card, use, self)
	for _,friend in ipairs(self.friends) do
		if friend:hasSkill("luahuapu") then
			use.card = sgs.Card_Parse("#lualianmeng:.:")
			if use.to then use.to:append(friend) end
			return
		end
	end
	for _,friend in ipairs(self.friends) do
		if friend:hasSkill("luajulian") then
			use.card = sgs.Card_Parse("#lualianmeng:.:")
			if use.to then use.to:append(friend) end
			return
		end
	end
	for _,friend in ipairs(self.friends) do
		if friend:hasSkill("luajingjuan") then
			use.card = sgs.Card_Parse("#lualianmeng:.:")
			if use.to then use.to:append(friend) end
			return
		end
	end
	for _,friend in ipairs(self.friends) do
		if friend:isWounded() and friend:getHandcardNum() > 5 then
			use.card = sgs.Card_Parse("#lualianmeng:.:")
			if use.to then use.to:append(friend) end
			return
		end
	end
	for _,friend in ipairs(self.friends_noself) do
		if friend:isLord() and friend:getHandcardNum() > 3 and friend:isWounded() then
			use.card = sgs.Card_Parse("#lualianmeng:.:")
			if use.to then use.to:append(friend) end
			return
		end
	end
	if self:isWeak() then
		use.card = sgs.Card_Parse("#lualianmeng:.:")
		if use.to then use.to:append(self.player) end
		return
	end
	for _,friend in ipairs(self.friends) do
		if friend:getHandcardNum() >= 0 then
			use.card = sgs.Card_Parse("#lualianmeng:.:")
			if use.to then use.to:append(friend) end
			return
		end
	end
end
sgs.ai_card_intention.lualianmeng = -10
sgs.ai_skill_choice.lualianmeng = function(self, choices, data)
	local target = data:toPlayer()
	if not self:isFriend(target) then return "nonono" end
	if self:isWeak(target) and target:isWounded() then return "recover" end
	if self:getOverflow(target) <= 0 then return "draw" end
	if target:isWounded() then return "recover" end
	return "draw"
end
sgs.ai_skill_choice.lualianmeng2 = function(self, choices, data)
	local friend = data:toPlayer()
	if friend:hasSkill("luahuapu") or friend:hasSkill("luajulian") then
		return "club"
	end
end

local function useWanbangrCard(self)
	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end
	local nyasama = self.room:findPlayerBySkillName("luawanbang")
	local suit
	if nyasama:getMark("@lianmengheart") > 0 then
		suit = sgs.Card_Heart
	elseif nyasama:getMark("@lianmengdiamond") > 0 then
		suit = sgs.Card_Diamond
	elseif nyasama:getMark("@lianmengclub") > 0 then
		suit = sgs.Card_Club
	elseif nyasama:getMark("@lianmengspade") > 0 then
		suit = sgs.Card_Spade
	end
	local function Check_RR(card)
		if suit and card:getSuit() == suit then
			sgs.ai_use_priority.luawanbangr = 15
			return true
		end
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return end
		end
		if card:isKindOf("TrickCard") then
			if card:isKindOf("Nullification") then return true end
			if card:isKindOf("IronChain") and not shuxin then return true end
			if card:isKindOf("Dismantlement") or card:isKindOf("NeedMaribel") or card:isKindOf("FaithCollection") then return false end
			if card:isKindOf("Lightning") and not self:willUseLightning(card) then return true end
			if card:isKindOf("AOE") and self:getAoeValue(card) > 35 then return false end
			if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
			if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
			if self:ThreeCheck(card) then return false end
			local dummy_use = {isDummy = true}
			self:useTrickCard(card, dummy_use)
			if not dummy_use.card then return true end
			return false
		end
		if card:isKindOf("EquipCard") then --or card:isKindOf("DefensiveHorse")
			if card:isKindOf("EquipCard") then
				local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
				if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) and rinnosuke
						and rinnosuke:isAlive() and self:isFriend(rinnosuke) then return false end
			end
			if card:isKindOf("OffensiveHorse") then
				return true
			end
			if card:isKindOf("DefensiveHorse") then
				return true
			end
			if card:isKindOf("Armor") and self:needToThrowArmor() then return true end
			if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
				local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
						or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
				if not bool_3 then return true end
			end
			if card:isKindOf("Weapon") then
				local dummy_use = {isDummy = true}
				self:useEquipCard(card, dummy_use)
				if not dummy_use.card then return true end
				if dummy_use.card and self.player:getWeapon() then return true end
			end
		end
		if card:isKindOf("Hui") then
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			self:useBasicCard(card, dummy_use)
			if dummy_use.card then
				return true
			end
			return false
		end
		if card:isKindOf("Ofuda") then return false end
		if card:isKindOf("Analeptic") then return false end
		if card:isKindOf("Peach") then return false end
		local x0 = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
		if card:isKindOf("Jink") and x0 <= 1 then return false end
		if card:isKindOf("Slash") and self.player:hasSkills(sgs.need_slash_skill) and self:getCardsNum("Slash") < 2 then
			return false
		end
		return true
	end
	for _, card in sgs.qlist(self.player:getCards("he")) do
		if Check_RR(card) then return card end
	end
end
local luawanbangr_skill = {}
luawanbangr_skill.name = "luawanbangr"
table.insert(sgs.ai_skills, luawanbangr_skill)
luawanbangr_skill.getTurnUseCard = function(self)
	local nyasama = self.room:findPlayerBySkillName("luawanbang")
	if not nyasama or not nyasama:isAlive() then return end
	if not self:isFriend(nyasama) then return end
	if self.player:hasUsed("#luawanbangr") then return end
	return sgs.Card_Parse("#luawanbangr:.:")
end
sgs.ai_use_priority.luawanbangr = 0
sgs.ai_skill_use_func["#luawanbangr"] = function(card, use, self)
	local acard = useWanbangrCard(self)
	if not acard then return end
	use.card = sgs.Card_Parse("#luawanbangr:" .. acard:getEffectiveId()..":")
	if use.to then
		use.to = sgs.SPlayerList()
	end
	return
end
sgs.ai_card_intention.luawanbangr = function(self, card, from, tos)
	local nyasama = self.room:findPlayerBySkillName("luawanbang")
	sgs.updateIntention(from, nyasama, -20)
end

local function findluasanaexCard(self)
	local x = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
	local function Check_R(card)
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return end
		end
		if card:isKindOf("Peach") then return false end
		if x <= 1 and card:isKindOf("Jink") then return false end
		if card:isKindOf("EquipCard") then
			local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
			if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) and rinnosuke
					and rinnosuke:isAlive() and self:isFriend(rinnosuke) then return false end
		end
		if card:isKindOf("ExNihilo") or card:isKindOf("Duel") then return false end
		if card:isKindOf("Lightning") and self:willUseLightning(card) then return false end
		if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
		if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
		if card:isKindOf("Indulgence") then return false end
		if card:isKindOf("IronChain") and shuxin then return false end
		if (card:isKindOf("AOE") and self:getAoeValue(card) >= 45) then return false end
		if self:ThreeCheck(card) then return false end
		local bool_2 = card:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip))
		local bool_3 = (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu"))
				and ((self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
				or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
		if bool_3 or bool_2 then return false end
		return true
	end
	local handcard = {}
	for _, cardX in sgs.list(self.player:getHandcards()) do
		if cardX:hasFlag("luasanaess") then
			table.insert(handcard, cardX)
		end
	end
	if #handcard ~= 2 then self.room:writeToConsole(debug.traceback()) end
	for _, c in ipairs(handcard) do
		if Check_R(c) then
			for _, c2 in ipairs(handcard) do
				if Check_R(c2) and c2:getEffectiveId() ~= c:getEffectiveId() then
					return true
				end
			end
		end
	end
	return false
end

sgs.ai_skill_invoke.luasanaex = function(self, data)
	local handcard = {}
	for _, cardX in sgs.list(self.player:getHandcards()) do
		if cardX:hasFlag("luasanaess") then
			table.insert(handcard, cardX)
		end
	end
	if #handcard == 1 then return true end
	if #handcard == 2 then self.room:writeToConsole("sanae test222"); return findluasanaexCard(self) end
	return false
end


sgs.ai_skill_choice.luasanaexxxx = function(self, choices)
    local slash = {}
    slash[1] = "fire_slash"
    slash[2] = "thunder_slash"
    slash[3] = "slash"

    local aoe = {}
    aoe[1] = "archery_attack"
    aoe[2] = "savage_assault"

    local function AIAddSubcard()
        local handcard = sgs.IntList()
        for _, cardX in sgs.list(self.player:getHandcards()) do
            if cardX:hasFlag("luasanaess") then
                handcard:append(cardX:getEffectiveId())
            end
        end
        if handcard:isEmpty() then self.room:writeToConsole(debug.traceback()) end
        return handcard
    end
    for i = 1, 2 do
        local trick2 = sgs.Sanguosha:cloneCard(aoe[i], sgs.Card_SuitToBeDecided, -1)
        trick2:addSubcards(AIAddSubcard())
        if self:getAoeValue(trick2, self.player) >= 80 then
            return aoe[i]
        end
    end

    local trick9 = sgs.Sanguosha:cloneCard("god_salvation", sgs.Card_SuitToBeDecided, -1)
    trick9:addSubcards(AIAddSubcard())
    local value_o = self:godSalvationValue(trick9)
    self.room:writeToConsole(value_o)
    if (value_o >= 25) then
        return "god_salvation"
    end

    local basic1 = sgs.Sanguosha:cloneCard("peach", sgs.Card_SuitToBeDecided, -1)
    basic1:addSubcards(AIAddSubcard())
    if (self.player:getLostHp() > 1) and (self.player:getPhase() ~= sgs.Player_NotActive) then
        return "peach"
    end


    local trick = sgs.Sanguosha:cloneCard("duel", sgs.Card_SuitToBeDecided, -1)
    trick:addSubcards(AIAddSubcard())
    if self:ceshi(trick, false, true) then return "duel" end

    trick9 = sgs.Sanguosha:cloneCard("god_salvation", sgs.Card_SuitToBeDecided, -1)
    trick9:addSubcards(AIAddSubcard())
    value_o = self:godSalvationValue(trick9)
    self.room:writeToConsole(value_o)
    if (value_o > 15) then
        return "god_salvation"
    end

    basic1 = sgs.Sanguosha:cloneCard("peach", sgs.Card_SuitToBeDecided, -1)
    basic1:addSubcards(AIAddSubcard())
    if (self.player:getLostHp() == 1) and (self.player:getPhase() ~= sgs.Player_NotActive) then
        return "peach"
    end

    local trick2 = sgs.Sanguosha:cloneCard("snatch", sgs.Card_SuitToBeDecided, -1)
    trick2:addSubcards(AIAddSubcard())
    if self:ceshi(trick2, true) then return "snatch" end

    for i = 1, 2 do
        trick2 = sgs.Sanguosha:cloneCard(aoe[i], sgs.Card_SuitToBeDecided, -1)
        trick2:addSubcards(AIAddSubcard())
        if self:getAoeValue(trick2, self.player) >= 60 then
            return aoe[i]
        end
    end

    for i = 1, 3 do
        local basic2 = sgs.Sanguosha:cloneCard(slash[i], sgs.Card_SuitToBeDecided, -1)
        basic2:addSubcards(AIAddSubcard())
        if self:YouMu() then
            local dummyuse = { isDummy = true }
            self:useBasicCard(basic2, dummyuse)
            if dummyuse.card then
                return slash[i]
            end
        end
    end

    local trick3 = sgs.Sanguosha:cloneCard("dismantlement", sgs.Card_SuitToBeDecided, -1)   --
    trick3:addSubcards(AIAddSubcard())
    if self:ceshi(trick3, true) then return "dismantlement" end

    local trick4 = sgs.Sanguosha:cloneCard("snatch", sgs.Card_SuitToBeDecided, -1)
    trick4:addSubcards(AIAddSubcard())
    if self:ceshi(trick4) then return "snatch" end

    local trick5 = sgs.Sanguosha:cloneCard("duel", sgs.Card_SuitToBeDecided, -1)
    trick5:addSubcards(AIAddSubcard())
    if self:ceshi(trick5) then return "duel" end

    local trick6 = sgs.Sanguosha:cloneCard("indulgence", sgs.Card_SuitToBeDecided, -1)
    trick6:addSubcards(AIAddSubcard())
    if self:ceshi(trick6, false, false, true) and AIAddSubcard():length() == 1 then return "indulgence" end

    for i = 1, 2 do
        local basic2 = sgs.Sanguosha:cloneCard(slash[i], sgs.Card_SuitToBeDecided, -1)
        basic2:addSubcard(handcards[1]:getEffectiveId())
        local dummyuse = { isDummy = true, to = sgs.SPlayerList() }
        self:useBasicCard(basic2, dummyuse)
        if dummyuse.card and not dummyuse.to:isEmpty() then
            for _, p in sgs.qlist(dummyuse.to) do
                if self:isGoodChainTarget(p, self.player, nil, nil, dummyuse.card) then
                    return slash[i]
                end
            end
        end
    end

    for i = 1, 3 do
        local basic2 = sgs.Sanguosha:cloneCard(slash[i], sgs.Card_SuitToBeDecided, -1)
        basic2:addSubcards(AIAddSubcard())
        local dummyuse = { isDummy = true, to = sgs.SPlayerList() }
        self:useBasicCard(basic2, dummyuse)
        if dummyuse.card and not dummyuse.to:isEmpty() then
            for _, p in sgs.qlist(dummyuse.to) do
                if self:hasHeavySlashDamage(self.player, damage.card, p) then
                    return slash[i]
                end
            end
        end
    end

    local trick7 = sgs.Sanguosha:cloneCard("supply_shortage", sgs.Card_SuitToBeDecided, -1)
    trick7:addSubcards(AIAddSubcard())
    if self:ceshi(trick7) and AIAddSubcard():length() == 1 then return "supply_shortage" end

    for i = 1, 2 do
        local trickX = sgs.Sanguosha:cloneCard(aoe[i], sgs.Card_SuitToBeDecided, -1)
        trickX:addSubcards(AIAddSubcard())
        if self:getAoeValue(trickX, self.player) >= 40 then
            return aoe[i]
        end
    end

    self.room:writeToConsole("sanae BUG!")
    return "ex_nihilo"
end


sgs.ai_skill_invoke.luasanaez = function(self, data)
	return true
end

sgs.ai_skill_use["@@luasanaex"] = function(self, prompt, method)
	local card = self.player:getMark("sanaex") - 1
	card = sgs.Sanguosha:getCard(card)

	self.room:writeToConsole("sanae AItest" .. card:objectName())
	if card:targetFixed() then return "#luasanaex:.:" end
	local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
	if card:isKindOf("BasicCard") then
		self:useBasicCard(card, dummy_use)
		if dummy_use.card and dummy_use.to:length() > 0 then
			local targets = {}
			for _, p in sgs.qlist(dummy_use.to) do
				table.insert(targets, p:objectName())
			end
			return "#luasanaex:.:->" .. table.concat(targets, "+")
		end
	else
		self:useTrickCard(card, dummy_use)
		if dummy_use.card and dummy_use.to:length() > 0 then
			local targets = {}
			for _, p in sgs.qlist(dummy_use.to) do
				table.insert(targets, p:objectName())
			end
			return "#luasanaex:.:->" .. table.concat(targets, "+")
		end
	end
end




local luajunzhen_skill = {}
luajunzhen_skill.name = "luajunzhen"
table.insert(sgs.ai_skills, luajunzhen_skill)
luajunzhen_skill.getTurnUseCard = function(self)
	if not self:ALICE() then return end
	local bool = false
	for _, acard in sgs.qlist(self.player:getHandcards()) do
		if self:getSameEquip(acard, self.player) and self:getOverflow() > 0 and not self.player:containsTrick("gainb") then
			bool = true
		end
	end
	if bool then sgs.ai_use_priority.luajunzhen = 15 else sgs.ai_use_priority.luajunzhen = 2 end
	return sgs.Card_Parse("#luajunzhen:.:")
end
sgs.ai_skill_use_func["#luajunzhen"] = function(card, use, self)
	local x = 0
	for _, acard in sgs.qlist(self.player:getCards("he")) do
		if acard:isKindOf("EquipCard") then
			x = x + 1
		end
	end
	if card then self.room:writeToConsole("alice ana test3") end
	if not self.player:hasFlag("luajunzhen") or (self.player:getMark("@luajunzhen") > 1 and self.player:getMark("@guixu") > 0) then
		for _, friend in ipairs(self.friends) do
			if friend:isWounded() then
				use.card = sgs.Card_Parse("#luajunzhen:" .. self:ALICE():getEffectiveId()..":")
				if use.to then
					use.to:append(friend)
				end
				return
			end
		end
		local pb = self.player:getEquips():length()
		for _, enemy in ipairs(self.enemies) do
			if pb >= enemy:getHp() - 1 and self:damageIsEffective(self.player, sgs.DamageStruct_Normal, enemy)
				and self:AtomDamageCount2(enemy, self.player, sgs.DamageStruct_Normal, nil, 3) > 1 then
				local ofuda = sgs.Sanguosha:cloneCard("ofuda")
				for _, enemyX in ipairs(self.enemies) do
					if not self.player:isCardLimited(ofuda, sgs.Card_MethodUse) and enemyX:getMark("@ofudaa") == 0
							and not self.player:isProhibited(enemyX, ofuda, enemyX:getSiblings()) then
						use.card = sgs.Card_Parse("#luajunzhen:" .. self:ALICE():getEffectiveId()..":")
						if use.to then
							use.to:append(enemyX)
						end
						return
					end
				end
			end
		end
		local slash = sgs.Sanguosha:cloneCard("slash")
		local xplayer = self:Skadi(slash, nil, self.player, false)
		if xplayer and sgs.Slash_IsAvailable(self.player) then
			use.card = sgs.Card_Parse("#luajunzhen:" .. self:ALICE():getEffectiveId()..":")
			if use.to then
				use.to:append(xplayer)
			end
			return
		end
	else
		local cardA = self:ALICE(true)
		local y = self.player:getMark("@luajunzhen") + x
		if cardA and math.random() < 0.6 then
			for _, friend in ipairs(self.friends) do
				if friend:isWounded() then
					use.card = sgs.Card_Parse("#luajunzhen:" .. self:ALICE():getEffectiveId()..":")
					if use.to then
						use.to:append(friend)
					end
					return
				end
			end
			local slash = sgs.Sanguosha:cloneCard("slash")
			local xplayer = self:Skadi(slash, nil, self.player, false)
			if xplayer  and sgs.Slash_IsAvailable(self.player) and (self:isWeak(xplayer) or self:YouMu2(xplayer, true)) then
				use.card = sgs.Card_Parse("#luajunzhen:" .. self:ALICE():getEffectiveId()..":")
				if use.to then
					use.to:append(xplayer)
				end
				return
			end
		end
		if y > 2 then
			for _, friend in ipairs(self.friends) do
				if friend:isWounded() then
					use.card = sgs.Card_Parse("#luajunzhen:" .. self:ALICE():getEffectiveId()..":")
					if use.to then
						use.to:append(friend)
					end
					return
				end
			end
			local slash = sgs.Sanguosha:cloneCard("slash")
			local xplayer = self:Skadi(slash, nil, self.player, false)
			if xplayer and sgs.Slash_IsAvailable(self.player) and self:YouMu2(xplayer, true) then
				use.card = sgs.Card_Parse("#luajunzhen:" .. self:ALICE():getEffectiveId()..":")
				if use.to then
					use.to:append(xplayer)
				end
				return
			end
		end
	end
end
sgs.ai_use_priority.luajunzhen = 2

sgs.ai_skill_choice.luajunzhen = function(self, choices, data)
	local x = 0
	for _, acard in sgs.qlist(self.player:getCards("he")) do
		if acard:isKindOf("EquipCard") then
			x = x + 1
		end
	end
	local y = self.player:getMark("@luajunzhen") + x
	local target = data:toPlayer()
	if self:isEnemy(target) then
		if y > 2 and not target:isAllNude() and target:getMark("@ofudaa") == 0 and not self:YouMu2(target, true) then return "ofuda" end
		if choices:match("slash") then return "slash" end
		return "ofuda"
	else
		if self.room:getCurrent():objectName() == target:objectName() then
			if self.player:isWounded() then return "peach" end
			return "analeptic"
		end
		return "peach"
	end
end
sgs.ai_skill_playerchosen.luaguixu = function(self, targets, slashX)
	local targetlist = sgs.QList2Table(targets)
	if self.player:getRole() == "rebel" then
		for _, target in ipairs(targetlist) do
			if target:getRole() == "lord" and self:damageIsEffective(target, sgs.DamageStruct_Normal, self.player)
					and self:AtomDamageCount2(target, self.player, sgs.DamageStruct_Normal, nil, 3) > 1 then
				return target
			end
		end
	end
	if self.player:getMark("@luajunzhen") > 2 then
		self:sort(targetlist, "defense")
		for _, target in ipairs(targetlist) do
			if self:isEnemy(target) and self:damageIsEffective(target, sgs.DamageStruct_Normal, self.player)
					and self:AtomDamageCount2(target, self.player, sgs.DamageStruct_Normal, nil, 3) > 1 then
				return target
			end
		end
	end
	return nil
end

local luatingshi_skill = {}
luatingshi_skill.name = "luatingshi"
table.insert(sgs.ai_skills, luatingshi_skill)
luatingshi_skill.getTurnUseCard = function(self)
	if not self.player:hasFlag("luatingshi") then
		return sgs.Card_Parse("#luatingshi:.:")
	end
end

sgs.ai_skill_use_func["#luatingshi"] = function(X, use, self)

	use.card = sgs.Card_Parse("#luatingshi:.:")
	if use.to then
		use.to = sgs.SPlayerList()
		return
	end
end
sgs.ai_skill_cardask["@luatingshiA"] = function(self, data)
	local cardX = data:toCard()
	local value = 0
	local rcards = self:getTurnUse(true)
	local function CanUse(cardY, targetX)
		if not self.player:isCardLimited(cardY, sgs.Card_MethodUse) and not self.room:isProhibited(self.player, targetX, cardY)  then
			return true
		end
	end
	if cardX:isKindOf("Duel") then
		value = value + 20
		if self:YOUMU(cardX) then value = value + 20 end
	end
	if cardX:isKindOf("Dismantlement") or cardX:isKindOf("FaithCollection") or cardX:isKindOf("Snatch") then
		value = value + 15
		if self:YOUMU(cardX) then
			value = value + 15
		end
	end
	if cardX:isKindOf("ExNihilo") then
		value = value + 20
		if self:YOUMU(cardX) then value = value + 30 end
	end
	if cardX:isKindOf("FireAttack") then
		value = value + 10
		if self:YOUMU(cardX) then value = value + 5*self.player:getHandcardNum() end
	end
	if cardX:isKindOf("AOE") then
		value = value + self:getAoeValue(cardX) / 3
	end
	if cardX:isKindOf("Indulgence") then
		for _, enemy in ipairs(self.enemies) do
			value = math.max(value, self:getIndulgenceValue(enemy)*10 - 10)
		end
	end
	if cardX:isKindOf("SupplyShortage") then
		for _, enemy in ipairs(self.enemies) do
			value = math.max(value, self:getSupplyShortageValue(enemy, self.enemies)*7 - 10)
		end
	end
	if cardX:isKindOf("AmazingGrace") then
		value = value + self:getAmazingGraceValue(cardX)*5
	end
	if cardX:isKindOf("GodSalvation") then
		value = value + self:godSalvationValue(cardX)*10
	end
	if cardX:isKindOf("Peach") then
		value = value + 20
		if self:YOUMU(cardX) then value = value + 30 end
	end
	if cardX:isKindOf("Slash") then
		value = value + 10
		if self:YOUMU(cardX) and self:slashIsAvailable(self.player, cardX) then value = value + 30 end
	end
	if cardX:isKindOf("Hui") then
		if self:YOUMU(cardX) then value = value + 30 end
	end
	if self.room:getDiscardPile() and self.room:getDiscardPile():length() > 2 then
		local id_0 = self.room:getDiscardPile():at(0)
		local id_1 = self.room:getDiscardPile():at(1)
		local id_2 = self.room:getDiscardPile():at(2)
		if sgs.Sanguosha:getCard(id_0):isKindOf("BasicCard") then
			if cardX:isKindOf("Duel") or cardX:isKindOf("Dismantlement") or cardX:isKindOf("FaithCollection") or cardX:isKindOf("Snatch")
				or cardX:isKindOf("ExNihilo") or (cardX:isKindOf("FireAttack") and self.player:getHandcardNum() > 4) then
				value = value + 10
			end
		end
		if not (sgs.Sanguosha:getCard(id_0):isKindOf("BasicCard")) then
			if cardX:isKindOf("Slash") or cardX:isKindOf("Peach") or cardX:isKindOf("Ofuda") or cardX:isKindOf("Hui") then
				value = value + 10
			end
		end
		for _, ecard in ipairs(rcards) do
			if ecard:isKindOf("BasicCard") and cardX:isKindOf("BasicCard") and self:YOUMU(ecard) then
				if not (sgs.Sanguosha:getCard(id_0):isKindOf("BasicCard")) then
					value = value + 10
				end
			end
			if not ecard:isKindOf("BasicCard") and not cardX:isKindOf("BasicCard") and self:YOUMU(ecard) then
				if (sgs.Sanguosha:getCard(id_0):isKindOf("BasicCard")) then
					value = value + 10
				end
			end
		end
	end
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	-- Check_R
	if cardX:isKindOf("BasicCard") then
		local shuxin = false
		for _, friend in ipairs(self.friends) do
			if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
		end
		for _,cardO in ipairs(cards) do
			if not cardO:isKindOf("BasicCard") then
				if cardO:isKindOf("EquipCard") then
					local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
					if (self.room:getCardPlace(cardO:getId()) == sgs.Player_PlaceHand) and rinnosuke
							and rinnosuke:isAlive() and self:isFriend(rinnosuke) then value = value - 15 end
				end
				if cardO:isKindOf("ExNihilo") or cardO:isKindOf("Duel") then value = value - 15 end
				if cardO:isKindOf("Lightning") and self:willUseLightning(cardO) then value = value - 5 end
				if cardO:isKindOf("GodSalvation") and self:godSalvationValue(cardO) > 10 then value = value - 10 end
				if cardO:isKindOf("AmazingGrace") and self:getAmazingGraceValue(cardO) > 2 then value = value - 5 end
				if cardO:isKindOf("Indulgence") then value = value - 10 end
				if cardO:isKindOf("IronChain") and shuxin then value = value - 10 end
				if (cardO:isKindOf("AOE") and self:getAoeValue(cardO) > 50) then value = value - 10 end
				if self:ThreeCheck(cardO) then value = value - 10 end
				if cardO:isKindOf("Wanbaochui") then value = value - 15 end
				if cardO:isKindOf("WoodenOx") then	--yun
					if self.player:getPile("wooden_ox"):length() > 0 then
						value = value - 15
					end
				end
				local bool_2 = cardO:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(cardO:getId()) == sgs.Player_PlaceEquip))
				local bool_3 = (cardO:isKindOf("EightDiagram") or cardO:isKindOf("RenwangShield") or card:isKindOf("Tengu"))
						and ((self.room:getCardPlace(cardO:getId()) == sgs.Player_PlaceEquip)
						or ((self.room:getCardPlace(cardO:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
				if bool_3 or bool_2 then value = value - 10 end
				if value > 9 then
					for _,cardP in ipairs(cards) do
						if cardP:getEffectiveId() ~= cardO:getEffectiveId() and not cardP:isKindOf("BasicCard") then
							if cardP:isKindOf("EquipCard") then
								local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
								if (self.room:getCardPlace(cardP:getId()) == sgs.Player_PlaceHand) and rinnosuke
										and rinnosuke:isAlive() and self:isFriend(rinnosuke) then value = value - 15 end
							end
							if cardP:isKindOf("ExNihilo") or cardP:isKindOf("Duel") then value = value - 15 end
							if cardP:isKindOf("Lightning") and self:willUseLightning(cardP) then value = value - 5 end
							if cardP:isKindOf("GodSalvation") and self:godSalvationValue(cardP) > 10 then value = value - 10 end
							if cardP:isKindOf("AmazingGrace") and self:getAmazingGraceValue(cardP) > 2 then value = value - 5 end
							if cardP:isKindOf("Indulgence") then value = value - 10 end
							if cardP:isKindOf("IronChain") and shuxin then value = value - 10 end
							if (cardP:isKindOf("AOE") and self:getAoeValue(cardP) > 50) then value = value - 10 end
							if self:ThreeCheck(cardP) then value = value - 10 end
							if cardP:isKindOf("Wanbaochui") then value = value - 15 end
							if cardP:isKindOf("WoodenOx") then	--yun
								if self.player:getPile("wooden_ox"):length() > 0 then
									value = value - 15
								end
							end
							local bool_4 = cardP:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(cardP:getId()) == sgs.Player_PlaceEquip))
							local bool_5 = (cardP:isKindOf("EightDiagram") or cardP:isKindOf("RenwangShield"))
									and ((self.room:getCardPlace(cardP:getId()) == sgs.Player_PlaceEquip)
									or ((self.room:getCardPlace(cardP:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
							if bool_4 or bool_5 then value = value - 10 end
							if value > 9 then return "$" .. cardO:getEffectiveId() end
							return "."
						end
					end
					return "."
				else
					return "."
				end
			end
		end
	else
		for _,cardO in ipairs(cards) do
			if cardO:isKindOf("BasicCard") then
				if cardO:isKindOf("Peach") then value = value - 10 end
				if cardO:isKindOf("Jink") and self:getCardsNum("Jink") <= 1 and self:isWeak() then value = value - 5 end
				if cardO:isKindOf("Hui") then value = value - 5 end
				if value > 14 then
					for _,cardP in ipairs(cards) do
						if cardP:getEffectiveId() ~= cardO:getEffectiveId() and not cardP:isKindOf("BasicCard") then
							if cardP:isKindOf("Peach") then value = value - 10 end
							if cardP:isKindOf("Jink") and self:getCardsNum("Jink") <= 1 and self:isWeak() then value = value - 5 end
							if cardP:isKindOf("Hui") then value = value - 5 end
							if value > 14 then return "$" .. cardO:getEffectiveId() end
							return "."
						end
					end
					return "."
				else
					return "."
				end
			end
		end
	end
	return "."
end
sgs.ai_skill_cardask["@luatingshiB"] = function(self, data)
	local cardX = data:toCard()
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	if cardX:isKindOf("BasicCard") then
		for _,cardO in ipairs(cards) do
			if cardO:isKindOf("BasicCard") and cardO:getEffectiveId() ~= cardX:getEffectiveId() then return "$" .. cardO:getEffectiveId() end
		end
	end
	if not cardX:isKindOf("BasicCard") then
		for _,cardO in ipairs(cards) do
			if not cardO:isKindOf("BasicCard") and cardO:getEffectiveId() ~= cardX:getEffectiveId() then return "$" .. cardO:getEffectiveId() end
		end
	end
	return "."
end
sgs.ai_use_priority.luatingshi = 8
sgs.ai_card_intention.luatingshi = function(self, card, from, tos)
    for _, to in ipairs(tos) do
        if to:isLord() then
            sgs.updateIntention(from, to, -120)
        end
    end

end

sgs.ai_skill_playerchosen.luajiyuan2 = function(self, targets, slashX)
	return sgs.ai_skill_playerchosen.damage(self, targets)
end




















