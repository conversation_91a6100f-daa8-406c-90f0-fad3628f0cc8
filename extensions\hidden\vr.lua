extension_god = sgs.Package("god_ol", sgs.Package_GeneralPack)
extension_ol = sgs.Package("ol_heg", sgs.Package_GeneralPack)
ol_caocao = sgs.General(extension_god, "ol_caocao", "god", 3)
ol_guixin = sgs.CreateMasochismSkill{
	name = "ol_guixin",
	on_damaged = function(self, player, damage)
		local room = player:getRoom()
		local n = player:getMark("ol_guixinTimes")
		player:setMark("LuaGuixinTimes", 0)
		local data = sgs.QVariant()
		data:setValue(damage)
		for i = 0, damage.damage - 1, 1 do
			player:addMark("ol_guixinTimes")
			if player:askForSkillInvoke(self:objectName(), data) then
				room:broadcastSkillInvoke(self:objectName())
				player:setFlags("GuixinUsing")
				for _, p in sgs.qlist(room:getOtherPlayers(player)) do
					if not p:isAllNude() then
						local choices = {"h", "e", "j"}
						local copy = {"h", "e", "j"}
						for i = 1, 3 do
							if p:getCards(choices[i]):isEmpty() then
								table.removeOne(copy, choices[i])
							end
						end
						local _data = sgs.QVariant()
						_data:setValue(p)
						local choice = room:askForChoice(player, self:objectName(), table.concat(copy, "+"), _data)
						local card = p:getCards(choice):at(math.random(0,p:getCards(choice):length() - 1))
						room:obtainCard(player, card, sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_EXTRACTION, player:objectName()), room:getCardPlace(card:getId()) ~= sgs.Player_PlaceHand)
					end
				end
				player:turnOver()
				player:setFlags("-GuixinUsing")
			else
				break
			end
		end
		player:setMark("ol_guixinTimes", n)
	end
}
ol_caocao:addSkill(ol_guixin)
ol_caocao:addSkill("feiying")
ol_jiangwanfeiyi = sgs.General(extension_ol, "ol_jiangwanfeiyi", "shu", 3)
ol_shengxi = sgs.CreatePhaseChangeSkill{
	name = "ol_shengxi", 
	frequency = sgs.Skill_Frequent, 
	on_phasechange = function(self, event, player, data, room)
		if player:getPhase() == sgs.Player_Discard and player:getMark("damage_record-Clear") == 0 and room:askForSkillInvoke(player, self:objectName(), data) then
			room:broadcastSkillInvoke(self:objectName())
			player:drawCards(2, self:objectName())
		end
		return false
	end
}
ol_jiangwanfeiyi:addSkill(ol_shengxi)
ol_jiangwanfeiyi:addSkill("shoucheng")
ol_mayunlu = sgs.General(extension, "ol_mayunlu", "shu", 4, false)
ol_fengpo = sgs.CreateTriggerSkill{
	name = "ol_fengpo", 
	global = true, 
	events = {sgs.TargetSpecified, sgs.ConfirmDamage, sgs.CardFinished}, 
	on_trigger = function(self, event, player, data)
		local use = data:toCardUse()
		local room = player:getRoom()
		if event == sgs.TargetSpecified and player:objectName() == use.from:objectName() and use.from:hasSkill(self:objectName()) and player:getPhase() == sgs.Player_Play and use.to:length() == 1 and (use.card:isKindOf("Duel") or use.card:isKindOf("Slash")) and player:getMark("fengpo_play") == 0 then
			room:addPlayerMark(player, "fengpo_play")
			if use.to:first():isKongcheng() then return false end
			local _data = sgs.QVariant()
			_data:setValue(use.to:first())
			if room:askForSkillInvoke(player, self:objectName(), _data) then
				local choice = room:askForChoice(player, self:objectName(), "fengpo1+fengpo2", data)
				ChoiceLog(player, choice)
				room:broadcastSkillInvoke(self:objectName())
				local x = 0
				for _, card in sgs.qlist(use.to:first():getHandcards()) do
					if card:getSuit() == sgs.Card_Diamond then 
						x = x+1
					end
				end
				if choice == "fengpo1" then
					player:drawCards(x, self:objectName())
				else
					room:setCardFlag(use.card, self:objectName()..x)
				end
			end
		elseif event == sgs.ConfirmDamage then
			local damage = data:toDamage()
			for x = 1, 200 do
				if damage.card and damage.card:hasFlag(self:objectName()..x) then
					local log = sgs.LogMessage()
					log.type = "$fengpo"
					log.from = player
					log.card_str = damage.card:toString()
					log.arg = self:objectName()
					log.arg2 = x
					room:sendLog(log)
					damage.damage = damage.damage+x
					data:setValue(damage)
					break
				end
			end
		elseif event == sgs.CardFinished then
			for x = 1, 200 do
				if data:toCardUse().card:hasFlag(self:objectName()..x) then
					room:clearCardFlag(data:toCardUse().card)
					break
				end
			end
		end
		return false
	end
}
ol_mayunlu:addSkill(ol_fengpo)
ol_mayunlu:addSkill("mashu")
ol_lidian = sgs.General(extension_ol, "ol_lidian", "wei", 3, true, sgs.GetConfig("EnableHidden", true))--his xunxun is very important!
ol_xunxun = sgs.CreatePhaseChangeSkill{
	name = "ol_xunxun", 
	frequency = sgs.Skill_Frequent, 
	on_phasechange = function(self, player)
		local room = player:getRoom()
		if player:getPhase() == sgs.Player_Draw and room:askForSkillInvoke(player, self:objectName()) then
			local x = math.random(1,2)
			if player:hasSkill("xingzhao") and player:hasEquip() then
				x = x + 2
			end
			room:broadcastSkillInvoke(self:objectName(), x)
			local card_ids = room:getNCards(4)
			for i = 1, 2 do
				room:fillAG(card_ids, player)
				local id = room:askForAG(player, card_ids, false, self:objectName())
				card_ids:removeOne(id)
				room:moveCardTo(sgs.Sanguosha:getCard(id), player, sgs.Player_DrawPile)
				room:clearAG()
			end
			room:askForGuanxing(player, card_ids, sgs.Room_GuanxingDownOnly)
		end
		return false
	end
}
ol_lidian:addSkill(ol_xunxun)
ol_lidian:addSkill("wangxi")