# 诳言技能调试指南

## 🚨 当前问题

根据老板的反馈，诳言技能拼点成功后没有后续效果：

```
SP稀神探女 发动了"诳言"，目标是 杖刀偶磨弓
SP稀神探女 向 杖刀偶磨弓 发起了拼点
SP稀神探女 的拼点牌为 铁索连环[♠K]
杖刀偶磨弓 的拼点牌为 雷杀[♠4]
SP稀神探女 (对 杖刀偶磨弓) 拼点赢！
```

**问题**：拼点成功后应该有后续效果，但是没有。

## 🔧 已进行的修复

### 1. 修复 can_trigger 函数
```lua
-- 修复前
can_trigger = function(self, target)
    return target ~= nil
end,

-- 修复后
can_trigger = function(self, target)
    -- 检查是否有角色拥有诳言技能
    if not target then return false end
    local room = target:getRoom()
    for _, p in sgs.qlist(room:getAlivePlayers()) do
        if p:hasSkill("kuangyan") then
            return true
        end
    end
    return false
end,
```

### 2. 修复目标选择参数
```lua
-- 修复前
local chosen_target = room:askForPlayerChosen(uranomiya, room:getAlivePlayers(),
    "kuangyan", "@kuangyan-choose:" .. target:objectName(), false)

-- 修复后
local chosen_target = room:askForPlayerChosen(uranomiya, room:getAlivePlayers(),
    "kuangyan", "@kuangyan-choose:" .. target:objectName(), true)
```

### 3. 添加调试日志
```lua
-- 在拼点处理开始时添加
room:sendLog("#KuangyanPindianTriggered", pindian.from, "kuangyan")
```

## 🔍 调试步骤

### 第一步：检查技能是否触发
重新测试诳言技能，看是否出现以下日志：
```
SP稀神探女 的"诳言"拼点事件触发
```

如果**出现**这个日志：
- ✅ Pindian事件正确触发
- 问题在后续的处理逻辑中

如果**没有**这个日志：
- ❌ Pindian事件没有触发
- 需要检查事件监听设置

### 第二步：检查拼点成功判断
如果第一步的日志出现，继续看是否出现：
```
SP稀神探女 的"诳言"拼点成功，对 杖刀偶磨弓 发动效果
```

如果**出现**：
- ✅ 拼点成功判断正确
- 问题在目标选择或卡牌使用逻辑

如果**没有**：
- ❌ 拼点成功判断有问题
- 需要检查 `pindian.from_number > pindian.to_number` 逻辑

### 第三步：检查目标选择
如果前两步都正常，问题可能在：
1. `askForPlayerChosen` 没有正确选择目标
2. 选择的目标为 `nil`
3. 后续的卡牌使用逻辑有问题

## 🛠️ 可能的问题和解决方案

### 问题1：Pindian事件没有触发
**原因**：
- `kuangyan_pindian` 技能没有正确注册
- `can_trigger` 函数返回 false
- 事件监听设置错误

**解决方案**：
```lua
-- 确保技能正确注册
sp_uranomiya:addSkill(kuangyan_pindian)

-- 简化 can_trigger
can_trigger = function(self, target)
    return true
end,
```

### 问题2：拼点成功判断错误
**原因**：
- `pindian.from_number` 或 `pindian.to_number` 为 nil
- 数值比较逻辑错误

**解决方案**：
```lua
-- 添加调试信息
room:sendLog("#Debug", pindian.from, "kuangyan", sgs.QVariant(), 
    "from:" .. tostring(pindian.from_number) .. " to:" .. tostring(pindian.to_number))

-- 确保数值有效
if pindian.from_number and pindian.to_number and pindian.from_number > pindian.to_number then
```

### 问题3：目标选择失败
**原因**：
- `askForPlayerChosen` 返回 nil
- 目标列表为空
- 选择被取消

**解决方案**：
```lua
-- 添加默认目标
if not chosen_target then
    chosen_target = target  -- 默认选择拼点对象
end

-- 或者强制选择
local chosen_target = room:askForPlayerChosen(uranomiya, room:getAlivePlayers(),
    "kuangyan", "@kuangyan-choose:" .. target:objectName(), false)  -- false = 强制选择
```

### 问题4：卡牌使用失败
**原因**：
- 拼点牌已经不在正确的区域
- 卡牌使用条件不满足
- `room:useCard` 调用失败

**解决方案**：
```lua
-- 检查卡牌状态
if card and not card:isVirtualCard() and card:getId() > 0 then
    -- 确保卡牌在正确的位置
    if room:getCardPlace(card:getId()) ~= sgs.Player_PlaceUnknown then
        -- 执行使用
    end
end
```

## 🎯 推荐的调试版本

如果问题持续存在，建议使用以下简化版本进行测试：

```lua
kuangyan_pindian = sgs.CreateTriggerSkill{
    name = "#kuangyan_pindian",
    events = {sgs.Pindian},
    can_trigger = function(self, target)
        return true
    end,
    on_trigger = function(self, event, player, data, room)
        local pindian = data:toPindian()
        
        if pindian.reason == "kuangyan" then
            room:sendLog("#Debug", pindian.from, "kuangyan", sgs.QVariant(), "Pindian triggered!")
            
            if pindian.from_number > pindian.to_number then
                room:sendLog("#Debug", pindian.from, "kuangyan", sgs.QVariant(), "Pindian success!")
                
                -- 简单的效果：直接对拼点对象造成1点伤害
                local damage = sgs.DamageStruct()
                damage.from = pindian.from
                damage.to = pindian.to
                damage.damage = 1
                damage.reason = sgs.DamageStruct_Normal
                room:damage(damage)
            end
        end
        return false
    end,
    global = true
}
```

这个简化版本可以帮助确认Pindian事件是否正确触发。

## 📝 测试清单

- [ ] 重新加载扩展包
- [ ] 测试诳言技能拼点
- [ ] 检查是否出现调试日志
- [ ] 确认拼点成功后是否有效果
- [ ] 记录具体的错误信息

请按照这个调试指南进行测试，并告诉我具体的结果！
