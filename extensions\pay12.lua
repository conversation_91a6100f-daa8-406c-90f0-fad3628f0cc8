
module("extensions.pay12", package.seeall)
extension_Pay12 = sgs.Package("pay12", sgs.Package_CardPack)
LuaYao = sgs.CreateBasicCard{
	name = "<PERSON>a<PERSON>ao", -- objectName
	class_name = "<PERSON>a<PERSON>ao", -- ClassName
	will_throw = false, --必须
	suit = sgs.Card_NoSuitBlack, -- 花色
	number = 1, -- 点数
	subtype = "defense_card", -- 在【卡牌一览】中显现的【子类型】
	target_fixed = false, -- 和技能卡的用法一样
	can_recast = true, -- 是否能重铸
	filter = function(self, targets, to_select) -- 和技能卡的用法一样
		return to_select:objectName() ~= sgs.Self:objectName() and #targets < 1
				and to_select:isWounded()
	end,
	feasible = function(self, targets) -- 和技能卡的用法一样
		return #targets == 1
	end,
	available = function(self, player) -- 在什么条件下可以使用，和技能卡的enabled_at_play差不多
		return (not player:hasUsed("<PERSON>aYao"))
				or (self:getSkillName() == "luashengong")
	end,
	-- about_to_use = function(self, room, use) -- 1210的新东西，下面再解释
	-- end,
	-- on_use = function(self, room, source, targets) -- 和技能卡的用法一样
	-- for _, target in ipairs(targets) do
	-- room:damage(sgs.DamageStruct(self:objectName(), source, target))
	-- end
	-- end,
	on_effect = function(self, effect)
		local room = effect.to:getRoom()
		room:obtainCard(effect.to, self, false)
		room:recover(effect.to, sgs.RecoverStruct(effect.from))
	end,
}
LuaYao:setParent(extension_Pay12)


luaweijingCard1 = sgs.CreateTrickCard{
	name = "gaina",
	class_name = "gaina",
	target_fixed = false,
	subclass = sgs.LuaTrickCard_TypeDelayedTrick,
	filter = function(self, targets, to_select)
		return  #targets < 1
	end,
	feasible = function(self, targets)
		return #targets == 1
	end,
	available = function(self, player)
		return true
	end,
	on_effect = function(self, effect)
	end,
	is_cancelable = function(self, effect)
		return false
	end,
}

LuaShanguang = sgs.CreateTrickCard{
	name = "light",
	class_name = "light",
	target_fixed = false,
	subclass = sgs.LuaTrickCard_TypeDelayedTrick,
	filter = function(self, targets, to_select)
		return  #targets < 1
	end,
	feasible = function(self, targets)
		return #targets == 1
	end,
	available = function(self, player)
		return true
	end,
	on_effect = function(self, effect)
	end,
	is_cancelable = function(self, effect)
		return false
	end,
}

luamin = sgs.CreateTrickCard{
	name = "luamin",
	class_name = "luamin",
	target_fixed = false,
	subclass = sgs.LuaTrickCard_TypeDelayedTrick,
	filter = function(self, targets, to_select)
		return  #targets < 1
	end,
	feasible = function(self, targets)
		return #targets == 1
	end,
	available = function(self, player)
		return true
	end,
	on_effect = function(self, effect)
	end,
	is_cancelable = function(self, effect)
		return false
	end,
}
luaweijingCard2 = sgs.CreateTrickCard{
	name = "gainb",
	class_name = "gainb",
	target_fixed = false,
	subclass = sgs.LuaTrickCard_TypeDelayedTrick, -- LuaTrickCard_TypeNormal, LuaTrickCard_TypeSingleTargetTrick, LuaTrickCard_TypeDelayedTrick, LuaTrickCard_TypeAOE, LuaTrickCard_TypeGlobalEffect
	filter = function(self, targets, to_select)
		if #targets ~= 0 then return false end
		return true
	end,
	is_cancelable = function(self, effect)
		return false
	end,
}
 
yuzhi = sgs.CreateTrickCard{
	name = "yuzhi",
	class_name = "yuzhi",
	subtype = "single_target_trick",
	subclass = sgs.LuaTrickCard_TypeSingleTargetTrick,
	target_fixed = false,
	suit = sgs.Card_Spade,
	number = 13,
	filter = function(self, targets, to_select)
		if #targets >=2 then return false end
		return sgs.Self:inMyAttackRange(to_select)
	end,
	is_cancelable = function(self, effect)
		return true
	end,
	available = function(self, player)
		return not player:hasUsed("yuzhi")
	end,
	on_effect = function(self, effect)
		local source = effect.from
		local target = effect.to
		local room = source:getRoom()

		room:damage(sgs.DamageStruct(self:objectName(), source, target))
	end
}

sp_huogong = sgs.CreateTrickCard{
	name = "sp_huogong",
	class_name = "sp_huogong",
	subtype = "single_target_trick",
	subclass = sgs.LuaTrickCard_TypeSingleTargetTrick,
	target_fixed = false,
	suit = sgs.Card_Spade,
	number = 12,
	filter = function(self, targets, to_select)  
		local total_num = 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, sgs.Self, self) 
		local canBeTarget = false
		for _, card in sgs.qlist(to_select:getHandcards()) do
			if not card:hasFlag("sp_huogongC") then 
				canBeTarget = true 
				break
			end 
		end 
		return #targets < total_num and canBeTarget and (to_select:objectName() ~= sgs.Self:objectName() or not sgs.Self:isLastHandCard(self, true))
	end,
	is_cancelable = function(self, effect)
		return true
	end, 
	on_effect = function(self, effect)
		local source = effect.from
		local target = effect.to
		local room = source:getRoom()
		local patternX = ""
		local canBeTarget = false
		for _, card in sgs.qlist(effect.to:getHandcards()) do
			if not card:hasFlag("sp_huogongC") then 
				canBeTarget = true 
			else
				if patternX == "" then 
					patternX = "^" .. card:toString() 
				else
					patternX = patternX .. "+^" .. card:toString()  
				end 
			end 
		end 
		if not canBeTarget then return end 
		if patternX == "" then patternX = "." end 
		local card = room:askForCard(effect.to, patternX .. "|.|.|hand!", "@sp_huogong", sgs.QVariant(), sgs.Card_MethodNone) 
		if not card then return end  
		room:setCardFlag(card, "sp_huogongC")
		room:showCard(effect.to, card:getEffectiveId())
	
		local suit_str = card:getSuitString()
 
		if (effect.from:isAlive()) then
			local card_to_throw = room:askForCard(effect.from, ".|" .. suit_str .. "|.|.", "@sp_huogong2", sgs.QVariant(), sgs.Card_MethodDiscard)
			if card_to_throw then 
				room:throwCard(card_to_throw, effect.from, effect.from)
				if suit_str == "club" then 
					room:damage(sgs.DamageStruct(self, effect.from, effect.to, 2, sgs.DamageStruct_Fire))
				else
					room:damage(sgs.DamageStruct(self, effect.from, effect.to, 1, sgs.DamageStruct_Fire)) 
				end 
			else
				effect.from:setFlags("FireAttackFailed_" .. effect.to:objectName()) -- For AI
			end 
		end  
	end
}

LuaShanguang:setParent(extension_Pay12)
luamin:setParent(extension_Pay12)
yuzhi:setParent(extension_Pay12)
luaweijingCard1:setParent(extension_Pay12)
luaweijingCard2:setParent(extension_Pay12)
sp_huogong:setParent(extension_Pay12)



--[[
extension_pay_h = sgs.Package("pay12")
morino_hon = sgs.General(extension_pay_h,"morino_hon","qun",4, false, false, false)
shnva = sgs.General(extension_pay_h,"shnva","qun",4, false, false, false)
--eclipse = sgs.General(extension,"eclipse","qun",3,false,true,false)

beihuanCard = sgs.CreateSkillCard{
	name = "luabeihuan",
	will_throw = false,
	filter = function(self, targets, to_select)
		local card = sgs.Self:getTag("luabeihuan")
		local response = false
		if card then 
			card = card:toCard()
		else		
			response = true			
			card = sgs.Sanguosha:cloneCard(self:getUserString())
			card = sgs.self
		end 
		if not card then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		if self:getUserString() and self:getUserString() ~= "" then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		card:addSubcards(self:getSubcards())
		card:setSkillName(self:objectName())

		if card:isKindOf("BasicCard") and (sgs.Self:getMark("luabeihuan1") == 0) then return false end
		if (not card:isKindOf("BasicCard")) and (sgs.Self:getMark("luabeihuan1") ~= 0) then return false end
		--if card:isKindOf("BasicCard") then card = kcard:clone() end 
		if card and card:targetFixed() then
			if card:isKindOf("ExNihilo") or card:isKindOf("Peach") or card:isKindOf("Analeptic") then return to_select:objectName() == sgs.Self:objectName() end
			return true
		end
		if response then return true end 
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		return (card and card:targetFilter(qtargets, to_select, sgs.Self) and not sgs.Self:isProhibited(to_select, card, qtargets)) or self:getUserString()
	end,
	feasible = function(self, targets)
		local card = sgs.Self:getTag("luabeihuan")
		local response = false
		if card then 
			card = card:toCard()
		else
			response = true
			card = sgs.Sanguosha:cloneCard(self:getUserString())
		end 
		if not card then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		if self:getUserString() and self:getUserString() ~= "" then card = sgs.Sanguosha:cloneCard(self:getUserString());response = true end 
		card:addSubcards(self:getSubcards())
		card:setSkillName(self:objectName())
		--if card:isKindOf("BasicCard") then card = kcard:clone() end

		if card:isKindOf("BasicCard") and (sgs.Self:getMark("luabeihuan1") == 0) then return false end
		if (not card:isKindOf("BasicCard")) and (sgs.Self:getMark("luabeihuan1") ~= 0) then return false end
		if card and #targets > 0 and
				(card:isKindOf("ExNihilo") or card:isKindOf("Peach") or card:isKindOf("Analeptic")) then return targets[1]:objectName() == sgs.Self:objectName() end
		if response then return true end 
		local qtargets = sgs.PlayerList()
		for _, p in ipairs(targets) do
			qtargets:append(p)
		end
		return card and card:targetsFeasible(qtargets, sgs.Self)
	end,	
	on_validate = function(self, card_use)
		local use_card = sgs.Sanguosha:cloneCard(self:getUserString())
		card_use.from:getRoom():writeToConsole("森之虫测试")
		use_card:addSubcards(self:getSubcards())
		use_card:setSkillName(self:objectName())
		local available = true
		for _,p in sgs.qlist(card_use.to) do
			if card_use.from:isProhibited(p,use_card) then
				available = false
				break
			end
		end
		available = available and use_card:isAvailable(card_use.from)
		if not available then return nil end
		if use_card:isKindOf("BasicCard") and (card_use.from:getMark("luabeihuan1") == 0) then return nil end
		if (not use_card:isKindOf("BasicCard")) and (card_use.from:getMark("luabeihuan1") ~= 0) then return nil end
		card_use.from:getRoom():writeToConsole("森之虫测试")
		return use_card		
	end,
	on_validate_in_response = function(self, user)
		local room = user:getRoom()
		room:writeToConsole("森之虫响应测试1")
		local aocaistring = self:getUserString()
		local use_card = sgs.Sanguosha:cloneCard(self:getUserString(), sgs.Card_NoSuit, -1)
		if string.find(aocaistring, "+")  then
			local uses = {}
			for _, name in pairs(aocaistring:split("+"))do
				table.insert(uses, name)
			end
			room:writeToConsole("森之虫响应测试2")
			local name = room:askForChoice(user, "luabeihuan", table.concat(uses, "+"))
			use_card = sgs.Sanguosha:cloneCard(name, sgs.Card_NoSuit, -1)
		end
		use_card:addSubcards(self:getSubcards())
		use_card:setSkillName("luabeihuan")

		return use_card	
	end
}
luabeihuan = sgs.CreateViewAsSkill{
	name = "luabeihuan",
	n = 2,
	view_filter = function(self, selected, to_select)
		if (sgs.Self:getMark("luabeihuan1") == 0) then 
			return to_select:isKindOf("BasicCard") 
		else
			if to_select:isKindOf("BasicCard") then return false end 
			return true 
		end 
	end,
	view_as = function(self, cards)
		if #cards < 2 then return nil end 
		if (sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY) then			
			local c = sgs.Self:getTag("luabeihuan"):toCard()
			if c then
				local card = beihuanCard:clone()
				for _, acard in ipairs(cards) do
					card:addSubcard(acard)
				end			
				card:setUserString(c:objectName())	
				return card			
			end
		else
			local pattern = sgs.Sanguosha:getCurrentCardUsePattern()
			if pattern == "slash" then 
				pattern = "slash+thunder_slash+fire_slash"
			end
			local acard = beihuanCard:clone()
			if #cards ~= 2 then return end
			for _, bcard in ipairs(cards) do
				acard:addSubcard(bcard)
			end			
			if pattern == "peach+analeptic" and sgs.Self:hasFlag("Global_PreventPeach") then 
				pattern = "analeptic" 
			end
			acard:setUserString(pattern)
			return acard		
		end 
		return nil
	end,
	enabled_at_play = function(self, player)
		return not player:isKongcheng()
	end,
	enabled_at_response = function(self, player, pattern)
		if player:isKongcheng() or string.sub(pattern, 1, 1) == "." or string.sub(pattern, 1, 1) == "@" then
			return false
		end
		if (player:getMark("luabeihuan1") == 0) and (pattern ~= "nullification") then return false end 
		if pattern == "peach" and player:hasFlag("Global_PreventPeach") then return false end
		return true
	end,	
	enabled_at_nullification = function(self, player)		
		if (player:getMark("luabeihuan1") == 1) then return false end 
		return not player:isKongcheng() 
	end
}
luabeihuan2 = sgs.CreateTriggerSkill{
	name = "#luabeihuan2",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.BeforeCardsMove},
	on_trigger = function(self, event, player, data, room)
		local move = data:toMoveOneTime()
		if move.from and move.from:objectName() == player:objectName() and move.from:hasSkill(self:objectName())
				and (move.from_places:contains(sgs.Player_PlaceEquip) or move.from_places:contains(sgs.Player_PlaceHand)) then
			if move.reason.m_skillName == "luabeihuan" then
				room:writeToConsole("luabeihuan2 ceshi")
				if player:getMark("luabeihuan1") == 0 then
					room:setPlayerMark(player, "luabeihuan1", 1)
					player:gainMark("@fushi")
					player:loseAllMarks("@huan")
					player:loseAllMarks("@bei")
					player:gainMark("@bei")
				else
					room:setPlayerMark(player, "luabeihuan1", 0)
					player:gainMark("@fushi")
					player:loseAllMarks("@huan")
					player:loseAllMarks("@bei")
					player:gainMark("@huan")
				end
			end
		end
	end
}
luabeihuan:setGuhuoDialog("lr")


lualihecard = sgs.CreateSkillCard{
	name = "lualihe",
	target_fixed = false,
	filter = function(self, targets, to_select)
		if to_select:objectName() ~= sgs.Self:objectName() then
			return #targets < 1
		end
		return false
	end,
	on_effect = function(self, effect)
		local x = effect.from:getMark("@fushi")
		effect.from:loseAllMarks("@fushi")
		if x > 0 then 
			if not effect.from:getRoom():askForDiscard(effect.to, self:objectName(), x, x, true, true) then 
				effect.from:getRoom():damage(sgs.DamageStruct(self:objectName(), effect.from, effect.to, 1, sgs.DamageStruct_Normal))
			end 
		end 
	end
}
lualiheVS = sgs.CreateZeroCardViewAsSkill{
	name = "lualihe",
	response_pattern = "@@lualihe",
	view_as = function()
		return lualihecard:clone()
	end,

	enabled_at_play = function(self, player)
		return false
	end
	
}
lualihe = sgs.CreateTriggerSkill{
	name = "lualihe",
	events = {sgs.EventPhaseStart},
	view_as_skill = lualiheVS,
	can_trigger = function(self, target)
		return target and (target:getMark("@fushi") > 0)
	end,
	on_trigger = function(self, event, player, data)
		if player:getPhase() == sgs.Player_Finish  then
			player:getRoom():askForUseCard(player, "@@lualihe", "@lualihe")
		end 
	end 	
}

morino_hon:addSkill(luabeihuan)
morino_hon:addSkill(luabeihuan2)
morino_hon:addSkill(lualihe)

luacpCard = sgs.CreateSkillCard{
	name = "luacp",
	filter = function(self, selected, to_select)
		if #selected == 0 then
			return true
		end
	end,
	on_effect = function(self, effect)
		effect.from:getRoom():setPlayerMark(effect.to, "@cp2", 1)
        effect.from:loseAllMarks("@cp")
	end
}

luacpVS = sgs.CreateZeroCardViewAsSkill{
	name = "luacp",
	view_as = function(self, cards)
		return luacpCard:clone()
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@cp") > 0
	end
}

luacp = sgs.CreateTriggerSkill{
	name = "luacp",
	frequency = sgs.Skill_Limited,
	limit_mark = "@cp",
	view_as_skill = luacpVS,
	global = true,
	events = {sgs.EnterDying, sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			--room:writeToConsole("shnva test K")
			for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if not p:hasSkill("luacpa") then room:attachSkillToPlayer(p, "luacpa") end
				end
			end
		else
			local dying = data:toDying()
			if dying.who:getMark("@cp2") > 0 then
				room:setPlayerMark(dying.who, "@cp2", 0)
			end
		end
	end
}

luaxitang = sgs.CreateTriggerSkill{
	name = "luaxitang" ,
	events = {sgs.HpRecover} ,
	frequency = sgs.Skill_Compulsory ,
	global = true,
	on_trigger = function(self, event, player, data)
		if event == sgs.HpRecover then
			local room = player:getRoom()
			local recover = data:toRecover()
			if recover.who and (recover.who:hasSkill("luaxitang")) and not player:hasSkill("luajiejie") then
				room:handleAcquireDetachSkills(player, "luajiejie")
			end
		end
	end
}
shnva:addSkill(luacp)
shnva:addSkill(luaxitang)
]]--
sgs.LoadTranslationTable{
	["pay12"] = "朝花夕拾", --注意这里每次要加逗号
	["morino_hon"] = "森之虫",
	["#morino_hon"]= "爱讲故事的狐妖",
	["designer:morino_hon"] = "Paysage",
	["illustrator:morino_hon"] = "森の虫",
	["luabeihuan"] = "悲欢",
	[":luabeihuan"] = "转化技，①你可以将两张基本牌当做任意非延时类锦囊使用；②你可以将两张非基本牌当做任意基本牌使用。你因“悲欢”使用或打出牌时，你获得一个“浮世”标记。",
	["lualihe"] = "离合",
	[":lualihe"] = "回合结束阶段，你可以弃置所有的“浮世”标记，令一名其他角色选择一项：弃置X张牌或受到你对其造成的1点伤害（X为你弃置的标记数）。",
	["@lualihe"] = "你可以发动“离合”",
	["~lualihe"] = "选择一名角色→点击确定",
	["sp_huogong"] = "火攻",
	["@sp_huogong"] = "请从亮起的牌中选择一张展示",
	["@sp_huogong2"] = "请从亮起的牌中选择一张弃置",
	["shnva"] = "シンノバ",
	["#shnva"]= "中流砥柱",
	["designer:shnva"] = "Paysage",
	["illustrator:shnva"] = "シンノバ",
	["luacp"] = "CP",
	["luacpa"] = "CP",
	[":luacp"] = "限定技，出牌阶段，你可以指定一名角色，之后的游戏中，每名角色于自身出牌阶段限一次，可以弃置一张牌令其回复一点体力，直到其进入濒死状态为止。",
	["luaxitang"] = "喜糖",
	[":luaxitang"] = "锁定技，每当你令一名角色回复体力后，你令其获得“结界”。",
}
return {extension_Pay12}