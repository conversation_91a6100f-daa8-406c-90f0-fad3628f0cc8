# QSanguosha-v2 界面美化完整指南

## 概述

QSanguosha-v2提供了丰富的界面自定义功能，包括背景图片更换、按钮图案自定义、皮肤系统等。本指南将详细介绍如何全面美化游戏界面，打造个性化的游戏体验。

## 第一部分：游戏背景图片更换

### 背景图片类型

#### 1. 游戏桌面背景
- **默认背景**: 游戏对局时的桌面背景
- **势力背景**: 根据不同势力显示的背景
- **自定义背景**: 用户自定义的背景图片

#### 2. 主界面背景
- **启动页背景**: 游戏启动时的背景
- **菜单背景**: 主菜单界面背景

### 方法一：通过配置文件更换（推荐）

#### 1. 修改 config.ini 文件

打开游戏根目录下的 `config.ini` 文件，找到以下配置项：

```ini
[General]
# 游戏桌面背景图片
GameBackgroundImage=image/system/backdrop/tableBg8.jpg
# 主界面背景图片  
BackgroundImage=image/system/backdrop/tableBg8.jpg
# 启用自动背景切换
EnableAutoBackgroundChange=true
```

#### 2. 配置说明

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `GameBackgroundImage` | 游戏对局时的背景图片 | `image/system/backdrop/tableBg8.jpg` |
| `BackgroundImage` | 主界面背景图片 | `image/system/backdrop/tableBg8.jpg` |
| `EnableAutoBackgroundChange` | 是否启用自动背景切换 | `true` |

#### 3. 更换步骤

1. **选择背景图片**：
   - 准备你想要的背景图片（建议JPG格式）
   - 图片分辨率建议：1024x768 或更高
   - 文件大小建议：小于2MB

2. **放置图片文件**：
   ```
   image/system/backdrop/your_background.jpg
   ```

3. **修改配置**：
   ```ini
   GameBackgroundImage=image/system/backdrop/your_background.jpg
   BackgroundImage=image/system/backdrop/your_background.jpg
   ```

4. **重启游戏**：保存配置文件后重启游戏即可生效

### 方法二：通过皮肤系统更换

#### 1. 皮肤配置文件

QSanguosha-v2使用JSON格式的皮肤配置文件：

- `skins/defaultSkin.image.json` - 默认皮肤
- `skins/fulldefaultSkin.image.json` - 完整默认皮肤
- `skins/compactSkin.image.json` - 紧凑皮肤

#### 2. 修改皮肤配置

打开对应的皮肤配置文件，找到背景相关配置：

```json
{
    "tableBg": "image/system/backdrop/tableBg.jpg",
    "tableBgwei": "image/system/backdrop/wei.jpg",
    "tableBgshu": "image/system/backdrop/shu.jpg", 
    "tableBgwu": "image/system/backdrop/wu.jpg",
    "tableBgqun": "image/system/backdrop/qun.jpg",
    "tableBggod": "image/system/backdrop/god.jpg"
}
```

#### 3. 背景类型说明

| 配置项 | 说明 |
|--------|------|
| `tableBg` | 默认桌面背景 |
| `tableBgwei` | 魏势力背景 |
| `tableBgshu` | 蜀势力背景 |
| `tableBgwu` | 吴势力背景 |
| `tableBgqun` | 群势力背景 |
| `tableBggod` | 神势力背景 |

### 方法三：使用现有背景图片

#### 1. 查看可用背景

游戏自带多个背景图片，位于 `image/system/backdrop/` 目录：

```
image/system/backdrop/
├── tableBg.jpg      # 默认背景
├── tableBg0.jpg     # 背景选项0
├── tableBg1.jpg     # 背景选项1
├── tableBg2.jpg     # 背景选项2
├── tableBg3.jpg     # 背景选项3
├── tableBg4.jpg     # 背景选项4
├── tableBg5.jpg     # 背景选项5
├── tableBg6.jpg     # 背景选项6
├── tableBg7.jpg     # 背景选项7
├── tableBg8.jpg     # 背景选项8 (当前默认)
├── wei.jpg          # 魏势力背景
├── shu.jpg          # 蜀势力背景
├── wu.jpg           # 吴势力背景
├── qun.jpg          # 群势力背景
└── god.jpg          # 神势力背景
```

#### 2. 快速更换

直接修改 `config.ini` 中的路径：

```ini
# 使用背景1
GameBackgroundImage=image/system/backdrop/tableBg1.jpg

# 使用魏势力背景
GameBackgroundImage=image/system/backdrop/wei.jpg

# 使用蜀势力背景  
GameBackgroundImage=image/system/backdrop/shu.jpg
```

## 第二部分：按钮图案自定义

### 按钮系统架构

#### 1. 按钮类型分类

| 按钮类型 | 用途 | 配置位置 |
|---------|------|----------|
| **Platter按钮** | 确认、取消、弃牌、托管 | `image/system/button/platter/` |
| **技能按钮** | 各种技能类型按钮 | `image/system/button/skill/` |
| **卡牌按钮** | 手牌相关操作 | `image/system/button/handcard/` |
| **回放按钮** | 录像控制按钮 | `image/system/button/replay/` |
| **容器按钮** | 卡牌和玩家容器按钮 | `image/system/button/card_container/` |
| **主菜单按钮** | 初始界面按钮 | QSS样式控制 |

#### 2. 按钮状态系统

每个按钮都有多种状态，对应不同的图片：

| 状态 | 文件名 | 说明 |
|------|--------|------|
| `normal` | `normal.png` | 正常状态 |
| `hover` | `hover.png` | 鼠标悬停状态 |
| `down` | `down.png` | 按下状态 |
| `disabled` | `disabled.png` | 禁用状态 |
| `down-hover` | `down-hover.png` | 按下时悬停状态 |

### 游戏内按钮自定义

#### 1. 通过皮肤配置文件更换

在皮肤配置文件中，按钮配置格式如下：

```json
{
    // 基础按钮配置
    "button-platter": "image/system/button/platter/%1/%2.png",
    "button-skill": "image/system/button/skill/%1/%3-%2.png",
    "button-handcard": "image/system/button/handcard/%1/%2.png",
    "button-replay": "image/system/button/replay/%1/%2.png",
    "button-card_container": "image/system/button/card_container/%1/%2.png",
    "button-player_container": "image/system/button/player_container/%1/%2.png",
    
    // 按钮背景
    "dashboardButtonSetBg": "image/system/button/platter/bg.png"
}
```

#### 2. 参数说明

| 参数 | 说明 | 示例值 |
|------|------|--------|
| `%1` | 按钮类型/子目录 | `confirm`, `cancel`, `skill` |
| `%2` | 按钮状态 | `normal`, `hover`, `down`, `disabled` |
| `%3` | 技能按钮尺寸 | `1`, `2`, `3` (宽、中、窄) |

#### 3. 直接替换按钮图片文件

**Platter按钮更换**：

```
image/system/button/platter/
├── confirm/           # 确认按钮
│   ├── normal.png     # 正常状态
│   ├── hover.png      # 悬停状态
│   ├── down.png       # 按下状态
│   └── disabled.png   # 禁用状态
├── cancel/            # 取消按钮
├── discard/           # 弃牌按钮
├── trust/             # 托管按钮
└── bg.png             # 按钮背景
```

**更换步骤**：
1. 准备新的按钮图片（PNG格式，透明背景）
2. 保持原有的文件名和目录结构
3. 替换对应的图片文件
4. 重启游戏查看效果

### 主菜单按钮自定义

#### 1. QSS样式表修改

主菜单按钮主要通过 `qss/sanguosha.qss` 文件控制：

```css
/* 通用按钮样式 */
QPushButton {
    background-image: url(image/system/button/normal.png);
    border: none;
    color: white;
    font-size: 14px;
}

QPushButton:hover {
    background-image: url(image/system/button/hover.png);
}

QPushButton:pressed {
    background-image: url(image/system/button/pressed.png);
}

/* 特定按钮样式 */
QPushButton#startButton {
    background-image: url(image/system/button/start_normal.png);
    width: 120px;
    height: 40px;
}

QPushButton#startButton:hover {
    background-image: url(image/system/button/start_hover.png);
}
```

#### 2. 添加自定义按钮样式

```css
/* 自定义开始游戏按钮 */
QPushButton[objectName="startGameButton"] {
    background-image: url(image/custom/start_button_normal.png);
    border: none;
    width: 150px;
    height: 50px;
}

QPushButton[objectName="startGameButton"]:hover {
    background-image: url(image/custom/start_button_hover.png);
}

QPushButton[objectName="startGameButton"]:pressed {
    background-image: url(image/custom/start_button_pressed.png);
}
```

## 第三部分：高级美化技巧

### 1. 自动背景切换

启用自动背景切换功能：

```ini
EnableAutoBackgroundChange=true
```

当启用此功能时，游戏会根据以下规则自动切换背景：
- 根据当前游戏模式
- 根据主公的势力
- 根据游戏进程

### 2. 势力背景自动切换

在皮肤配置文件中设置不同势力的背景：

```json
{
    "tableBgwei": "image/system/backdrop/your_wei_bg.jpg",
    "tableBgshu": "image/system/backdrop/your_shu_bg.jpg",
    "tableBgwu": "image/system/backdrop/your_wu_bg.jpg",
    "tableBgqun": "image/system/backdrop/your_qun_bg.jpg"
}
```

### 3. 创建自定义皮肤

1. **复制现有皮肤配置**：
   ```
   cp skins/defaultSkin.image.json skins/mySkin.image.json
   ```

2. **修改皮肤列表**：
   编辑 `skins/skinList.json`：
   ```json
   {
       "mySkin": {
           "roomImageConfigFile": "skins/mySkin.image.json"
       }
   }
   ```

3. **在游戏中选择皮肤**：
   游戏设置 → 界面设置 → 皮肤选择

## 第四部分：图片制作规范

### 1. 背景图片要求

| 属性 | 要求 | 建议 |
|------|------|------|
| 格式 | JPG, PNG | JPG（文件更小） |
| 分辨率 | 最小800x600 | 1024x768或1920x1080 |
| 文件大小 | 无硬性限制 | 小于5MB |
| 色彩模式 | RGB | RGB |

### 2. 按钮图片要求

| 属性 | 要求 | 建议 |
|------|------|------|
| 格式 | PNG | 支持透明背景 |
| 色彩模式 | RGBA | 32位色彩 |
| 背景 | 透明 | 便于叠加效果 |

### 3. 按钮尺寸规范

#### Platter按钮尺寸：
- 确认/取消按钮：61×75px
- 弃牌按钮：33×81px
- 托管按钮：36×35px

#### 技能按钮尺寸：
- 宽按钮：134×19px
- 中按钮：66×19px
- 窄按钮：44×19px

#### 主菜单按钮尺寸：
- 主要按钮：150×50px
- 次要按钮：120×40px
- 小按钮：80×30px

### 4. 设计建议

#### 背景设计：
- **色调**：避免过于鲜艳的颜色，以免影响游戏界面可读性
- **对比度**：确保游戏UI元素在背景上清晰可见
- **主题**：建议选择与三国主题相关的图片
- **构图**：避免中央区域过于复杂，为游戏界面留出空间

#### 按钮设计：
- **视觉一致性**：保持整体风格统一
- **状态区分**：不同状态要有明显的视觉差异
- **可读性**：确保按钮文字清晰可读
- **主题匹配**：与游戏整体主题协调

## 第五部分：完整主题制作

### 1. 主题目录结构

```
themes/my_theme/
├── backgrounds/
│   ├── main_bg.jpg        # 主界面背景
│   ├── game_bg.jpg        # 游戏背景
│   ├── wei_bg.jpg         # 魏势力背景
│   ├── shu_bg.jpg         # 蜀势力背景
│   ├── wu_bg.jpg          # 吴势力背景
│   └── qun_bg.jpg         # 群势力背景
├── buttons/
│   ├── platter/           # 游戏控制按钮
│   ├── skill/             # 技能按钮
│   └── menu/              # 主菜单按钮
├── skins/
│   └── my_theme.image.json # 皮肤配置
├── qss/
│   └── my_theme.qss       # 样式文件
└── config.ini             # 主题配置
```

### 2. 主题配置文件

创建 `themes/my_theme/config.ini`：

```ini
[Theme]
Name=我的自定义主题
Version=1.0
Author=你的名字

[Paths]
BackgroundPath=backgrounds/
ButtonPath=buttons/
SkinFile=skins/my_theme.image.json
QSSFile=qss/my_theme.qss

[Settings]
AutoBackgroundChange=true
DefaultBackground=game_bg.jpg
```

### 3. 主题安装和使用

1. **安装主题**：
   - 将主题文件夹放到游戏目录
   - 修改相应的配置文件

2. **应用主题**：
   - 在游戏设置中选择自定义皮肤
   - 或直接修改config.ini指向新的资源

## 故障排除

### 1. 背景不显示

**可能原因**：
- 图片路径错误
- 图片文件损坏
- 图片格式不支持

**解决方法**：
1. 检查文件路径是否正确
2. 确认图片文件存在且可以正常打开
3. 尝试使用JPG格式
4. 重启游戏

### 2. 按钮显示异常

**可能原因**：
- 图片尺寸不正确
- 透明度设置问题
- 皮肤配置错误

**解决方法**：
1. 调整图片到正确尺寸
2. 检查透明背景设置
3. 验证JSON配置语法

### 3. 游戏启动失败

**可能原因**：
- 配置文件语法错误
- 图片文件过大导致内存不足

**解决方法**：
1. 恢复默认配置
2. 使用较小的图片文件
3. 检查config.ini语法

## 总结

QSanguosha-v2的界面美化系统提供了丰富的自定义选项：

1. **背景美化**：
   - 配置文件快速更换
   - 皮肤系统灵活配置
   - 势力背景自动切换

2. **按钮美化**：
   - 游戏内按钮图片替换
   - 主菜单按钮QSS样式
   - 完整的状态系统支持

3. **主题制作**：
   - 完整的主题包制作
   - 统一的视觉风格
   - 便于分享和管理

选择适合你的方法，打造独一无二的游戏界面体验！

## 快速参考

### 重要文件路径
```
配置文件：
├── config.ini                          # 主配置文件
├── skins/skinList.json                 # 皮肤列表
├── skins/defaultSkin.image.json        # 默认皮肤配置
├── qss/sanguosha.qss                   # 样式文件
└── skins/defaultSkin.layout.json       # 布局配置

背景图片：
└── image/system/backdrop/              # 背景图片目录
    ├── tableBg.jpg                     # 默认背景
    ├── tableBg0.jpg ~ tableBg8.jpg     # 可选背景
    ├── wei.jpg                         # 魏势力背景
    ├── shu.jpg                         # 蜀势力背景
    ├── wu.jpg                          # 吴势力背景
    ├── qun.jpg                         # 群势力背景
    └── god.jpg                         # 神势力背景

按钮图片：
└── image/system/button/                # 按钮图片根目录
    ├── platter/                        # 控制按钮
    ├── skill/                          # 技能按钮
    ├── handcard/                       # 手牌按钮
    ├── replay/                         # 回放按钮
    └── card_container/                 # 容器按钮
```
