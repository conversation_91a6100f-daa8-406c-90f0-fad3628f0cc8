--hahaha

math.randomseed(tostring(os.time()):reverse():sub(1, 7)) --------设置时间种子

Pay = require "paysage" --加载价值模组

local bonus_skill={} -- 初始化 bonus_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 bonus_skill 只是为了出于习惯
bonus_skill.name="bonus" -- 设置 name
table.insert(sgs.ai_skills, bonus_skill) -- 把这个表加入到 sgs.ai_skills 中

bonus_skill.getTurnUseCard = function(self) --考虑留杀的情况，防嘲讽等
-- 这个函数的作用仅仅是让 AI 在出牌阶段考虑使用技能卡的可能性
-- 至于对谁使用，子卡是什么，这里先不用管
	local sakura = self:getCards("sakura")
	if (not sakura) or (#sakura == 0) then return end 
	if self.player:hasUsed("#bonus") then return end 
	if (self:getOverflow() > 0) and not self:isWeak() then
		return sgs.Card_Parse("#bonus:.:") 
	end 
end 

sgs.ai_skill_use_func["#bonus"] = function(card, use, self)
	local sakura = self:getCards("sakura")
	if (not sakura) or (#sakura == 0) then return end 
	self:sortByKeepValue(sakura)
	use.card = sgs.Card_Parse("#bonus:" .. sakura[1]:getEffectiveId()..":")
	return	
end 

function wrigglecard(self)
	local cards = sgs.QList2Table(self.player:getHandcards())
	local toUse = {}
	local function shuxin()
		for _, friend in ipairs(self.friends) do
			if friend:hasSkill(sgs.shuxin_skill) then return true end 
		end 			
	end 
	local count = 0
	for _, card in ipairs(cards) do
		if card:isKindOf("TrickCard") then 
			if card:isKindOf("AOE") and self:getAoeValue(card) <= 30 then 
				table.insert(toUse, card)
			end 
			if card:isKindOf("AmazingGrace") or card:isKindOf("Dismantlement") or card:isKindOf("SupplyShortage")
				or (card:isKindOf("Lightning") and self:getFinalRetrial() ~= 1) or card:isKindOf("FireAttack")
				or (card:isKindOf("IronChain") and not shuxin()) then table.insert(toUse, card) end 

			if card:isKindOf("Nullification") then 
				count = count + 1
				if count > 0 then table.insert(toUse, card) end 
			end 
		end 
	end 
	return toUse
end 
local luayingdeng_skill = {}
luayingdeng_skill.name = "luayingdeng"
table.insert(sgs.ai_skills, luayingdeng_skill)
luayingdeng_skill.getTurnUseCard = function(self, inclusive)
	if self.player:getMark("yingdengx") > 1 then return end 
	local friends = self.friends
	self:sort(friends, "defense")
	local boolr = (friends[1]:objectName() == self.player:objectName()) and (self.player:getHp() >= 2)
	if self:isWeak(friends[1]) and friends[1]:isWounded() and (self.player:getMark("@yingdeng") > 0) and not boolr then 
		return sgs.Card_Parse("#luayingdeng:.:")
	end 
	local x = #wrigglecard(self)
	if x == 0 then 
		local x2 = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
		if self.player:getHp() == 1 then return end 
		if self.player:getHp() == 2 and (x2 == 0) then return end
	end 
	return sgs.Card_Parse("#luayingdeng:.:")
end 

sgs.ai_skill_use_func["#luayingdeng"] = function(card, use, self)
	local friends = self.friends
	self:sort(friends, "defense")
	local boolr = (friends[1]:objectName() == self.player:objectName()) and (self.player:getHp() >= 2)
	if self:isWeak(friends[1]) and friends[1]:isWounded() and (self.player:getMark("@yingdeng") > 0) and not boolr then 
		use.card = sgs.Card_Parse("#luayingdeng:.:")
		if use.to then use.to:append(self.player); return end		
	end 
	local cards = wrigglecard(self)
	self:sortByUseValue(cards, true)
	if #cards == 0 then 
		use.card = sgs.Card_Parse("#luayingdeng:.:")
		if use.to then use.to:append(self.player); return end	
	else
		use.card = sgs.Card_Parse("#luayingdeng:" .. cards[1]:getEffectiveId()..":")	
		if use.to then use.to:append(self.player); return end	
	end 
end 

sgs.ai_skill_choice.luayingdeng = function(self, choices)
	local friends = self.friends
	self:sort(friends, "defense")
	local boolr = (friends[1]:objectName() == self.player:objectName()) and (self.player:getHp() >= 2)
	if self:isWeak(friends[1]) and friends[1]:isWounded() and (self.player:getMark("@yingdeng") > 0) and not boolr then 
		return "yingdeng2"
	end 
	return "yingdeng1"
end 

sgs.ai_skill_playerchosen.luayingdeng = function(self, targets)
	local friends = self.friends
	self:sort(friends, "defense") 
	for _, friend in ipairs(friends) do
		if targets:contains(friend) then return friend end 
	end 
end 

sgs.ai_use_priority.luayingdeng = sgs.ai_use_priority.Dismantlement + 0.5

sgs.ai_skill_invoke.lualianpo = function(self, data)
	return true 
end 
sgs.ai_skill_invoke.luachongqun = function(self, data)
	local function getcardn(player)
		local x = player:getHandcardNum() 
		x = x + player:getEquips():length()
		return x 
	end 
	local enemies = self.enemies
	if #enemies == 0 then return false end 
	self:sort(enemies, "defense")
	local target = self.room:getTag("chongqunt"):toPlayer()
	local pa = target:getLostHp() + self.player:getMark("@yingdeng")
	if pa > 2 then 
		if enemies[1]:objectName() == target:objectName() 
			and (#self.friends_noself > 0) and ((getcardn(target) >= pa) or (getcardn(target) > 2)) then 
			return true 
		end 
		if self:isEnemy(target) and (getcardn(target) == pa) then 
			return true 
		end 
		if self:isFriend(target) then 
			return true 
		end 
	end 
end 

sgs.ai_skill_choice.luachongqun = function(self, choices)
	local target = self.room:getTag("chongqunt"):toPlayer()	
	if self:isEnemy(target) then return "discard" end 
	return "draw"
end 


sgs.ai_skill_cardask["kxslash"] = function(self, data)
	self.room:writeToConsole("yuggi test")
	local clownpiece = self.room:findPlayerBySkillName("luakuangxiang")
	
	if self:isFriend(clownpiece) then 
		--if self:isWeak(clownpiece) then return "." end 
		local patten = self.room:getTag("luakx"):toString()
		patten = patten:split("+")
		if table.contains(patten, "peach") then
			return "." 
		end 
	end 
	return true 
end 

local function Clownadj(self, player, bool) --获得该玩家攻击范围内角色数量,bool == true 表示只计敌人
	local count = 0
	local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
	for _, to in sgs.qlist(self.room:getAllPlayers()) do
		if player:inMyAttackRange(to) and not self:slashProhibit(dummy, to, player) then 
			if bool then 
				if self:isEnemy(to, player) then count = count + 1 end 
			else	
				count = count + 1 
			end
		end 
	end 
	return count
end

sgs.pay_ai_card.Peach.luayuekuang = function(self, card, use, mustusepeach) 
	local function Clownpiece()
		local enemies = self.enemies
		self:sort(enemies, "defenseSlash")
		local function adj(player, bool) --获得该玩家攻击范围内角色数量,bool == true 表示只计敌人
			local count = 0
			for _, to in sgs.qlist(self.room:getAllPlayers()) do
				if player:inMyAttackRange(to) and not self:slashProhibit(dummy, to, player) then
					if bool then
						if self:isEnemy(to, player) then count = count + 1 end
					else
						count = count + 1
					end
				end
			end
			return count
		end

		for __, enemy2 in ipairs(enemies) do
			local x = self.player:getLostHp() - enemy2:getHandcardNum() + 1
			local slashes_1 = self:getCards("Slash")
			if enemy2:getHandcardNum() < 3 and (getCardsNum("Slash", enemy2, self.player) == 0) and (adj(enemy2, true) == 0) and x <= 2
					and self:slashIsEffective(slashes_1, enemy2, enemy2) and not self:slashProhibit(slashes_1, enemy2, enemy2)
			then return enemy2 end
		end
	end

	if self.player:hasSkill("luayuekuang") and Clownpiece() then 
		return 1 
	end
end
	
sgs.ai_skill_playerchosen.luayuekuang = function(self, targets)
	local enemies = self.enemies
	self:sort(enemies, "defenseSlash")
	local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)

	local friends1 = self.friends
	self:sort(friends1, "defenseSlash2")
	for __, friend in ipairs(friends1) do
		local x = self.player:getHp() - friend:getHandcardNum()
		if targets:contains(friend) and friend:getHandcardNum() < 3 and x > 0 and not self:slashIsEffective(dummy, friend, friend)
		then return friend end
	end

	local function Prohibit(target, slash)
		for _, askill in sgs.qlist(target:getVisibleSkillList(true)) do
			local filter = sgs.ai_slash_prohibit[askill:objectName()]
			if filter and type(filter) == "function" and filter(self, target, target, slash) then return true end
		end
	end
	for __, enemy2 in ipairs(enemies) do
		local x = self.player:getHp() - enemy2:getHandcardNum()
		if targets:contains(enemy2) and (enemy2:getHandcardNum() < 3 or enemy2:containsTrick("gaina"))
				and (getCardsNum("Slash", enemy2, self.player) < 1)
				and (Clownadj(self, enemy2, true) == 0 or enemy2:containsTrick("gaina")) and x <= 2
			and self:slashIsEffective(dummy, enemy2, enemy2) and not Prohibit(enemy2, dummy)
				then return enemy2 end 
	end
	self.room:writeToConsole("clown ai test 194")
	self:sort(friends1, "defenseSlash2")	
	for __, friend in ipairs(friends1) do --这里是考虑补牌
		local x = self.player:getHp() - friend:getHandcardNum()
		if targets:contains(friend) and friend:getHandcardNum() < 3 and x >= 3
			and ((not self:isWeak(friend)) or (self:getCardsNum("Peach") > 0)) then return friend end 
	end 	
	self.room:writeToConsole("clown ai test 195")
	for __, friend in ipairs(friends1) do --这里是考虑补牌
		local x = self.player:getHp() - friend:getHandcardNum()
		if targets:contains(friend) and friend:getHandcardNum() < 3 and x >= 2 and (Clownadj(self, friend, true) > 0)
			and ((not self:isWeak(friend)) or (self:getCardsNum("Peach") > 0)) then return friend end 
	end 	
	for __, friend in ipairs(friends1) do
		local x = self.player:getHp() - friend:getHandcardNum()
		if targets:contains(friend) and friend:getHandcardNum() < 3 and x >= 2 and (Clownadj(self, friend, false) > 0)
			and ((not self:isWeak(friend)) or (self:getCardsNum("Peach") > 0)) then return friend end 
	end

	for __, friend in ipairs(friends1) do
		local x = self.player:getHp() - friend:getHandcardNum()
		if targets:contains(friend) and friend:getHandcardNum() < 3 and x > 0 and sgs.isGoodHp(friend, true)
				and self:needToLoseHp(friend) then return friend end
	end

	for __, enemy2 in ipairs(enemies) do
		local x = self.player:getHp() - enemy2:getHandcardNum()
		if targets:contains(enemy2) and (enemy2:getHandcardNum() < 3 or enemy2:containsTrick("gaina")) and (getCardsNum("Slash", enemy2, self.player) < 0.5)
			and self:slashIsEffective(dummy, enemy2, enemy2) and x <= 1
				then return enemy2 end 
	end 
	

	for __, friend in ipairs(friends1) do
		local x = self.player:getHp() - friend:getHandcardNum()
		if targets:contains(friend) and x >= 1 and (Clownadj(self, friend, true) > 0) and (getCardsNum("Slash", friend, self.player) > 0.5)
			and ((not self:isWeak(friend)) or (self:getCardsNum("Peach") > 0)) then return friend end 
	end 	
	--self.room:writeToConsole("克劳恩ai测试218")

	--self.room:writeToConsole("克劳恩ai测试223")
	for __, friend in ipairs(friends1) do
		local x = self.player:getHp() - friend:getHandcardNum()
		if targets:contains(friend) and friend:getHandcardNum() < 3 and x > 0 and Prohibit(friend, dummy)
			and ((not self:isWeak(friend)) or (self:getCardsNum("Peach") > 0)) then return friend end 
	end 		
	--self.room:writeToConsole("克劳恩ai测试228")
	for __, friend in ipairs(friends1) do
		local x = self.player:getHp() - friend:getHandcardNum()
		if targets:contains(friend) and friend:getHandcardNum() < 3 and x >= 3 then return friend end
	end
	self.room:writeToConsole("clown ai test 257")
	friends1 = self.friends
	self:sort(friends1, "defenseSlash2")		
	for __, friend in ipairs(friends1) do --这里是考虑出杀	
		if (getCardsNum("Slash", friend, self.player) > 0.9) and targets:contains(friend)
			and (Clownadj(self, friend, true) > 0) then return friend end
	end 
	return nil 
end
local luakuangxiang_skill = {}
luakuangxiang_skill.name = "luakuangxiang"
table.insert(sgs.ai_skills, luakuangxiang_skill)
luakuangxiang_skill.getTurnUseCard = function(self) 
	return sgs.Card_Parse("#luakuangxiang:.:")
end
sgs.ai_skill_use_func["#luakuangxiang"] = function(cardF, use, self)
	local target = sgs.ai_skill_playerchosen.luayuekuang(self, self.room:getAlivePlayers())
	if #self:Hiziri() > 2 and self:getOverflow() <= 0 then
		use.card = sgs.Card_Parse("#luakuangxiang:.:")
		if use.to then use.to:append(self.player) end
		return
	end
	self:sort(self.friends_noself, "handcard2")
	for _, friend in ipairs(self.friends_noself) do
		if friend:isWounded() then
			use.card = sgs.Card_Parse("#luakuangxiang:.:")
			if use.to then use.to:append(friend) end
			return
		end
	end
	self:sort(self.friends, "defense")
	for _, friend in ipairs(self.friends) do
		if self:getOverflow() <= 0 then
			use.card = sgs.Card_Parse("#luakuangxiang:.:")
			if use.to then use.to:append(friend) end
			return
		end 
	end
end
sgs.ai_use_priority.luakuangxiang = 0
sgs.ai_card_intention.luakuangxiang = -20
local function findkuangxiangCard(self, cardX)
	local x = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end
	local function Check_R(card)
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return end
		end
		if card:isKindOf("Peach") or card:isKindOf("Analeptic") then return false end
		if x <= 1 and card:isKindOf("Jink") then return false end
		if card:isKindOf("EquipCard") then
			local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
			if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) and rinnosuke
					and rinnosuke:isAlive() and self:isFriend(rinnosuke) then return false end
		end
		if card:isKindOf("ExNihilo") or card:isKindOf("Duel") then return false end
		if card:isKindOf("Lightning") and self:willUseLightning(card) then return false end
		if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
		if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
		if card:isKindOf("Indulgence") then return false end
		if card:isKindOf("IronChain") and shuxin then return false end
		if (card:isKindOf("AOE") and self:getAoeValue(card) > 50) then return false end
		if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
		local bool_2 = card:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip))
		local bool_3 = (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu"))
				and ((self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
				or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
		if bool_3 or bool_2 then return false end
		for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
			local callback = sgs.ai_cardneed[skill:objectName()]
			if type(callback) == "function" and callback(self.player, card, self) then
				return false
			end
		end
		return true
	end
	if cardX then
		local allcards = sgs.QList2Table(self.player:getCards("he"))
		for _,card2 in ipairs(allcards) do
			if card2:getSuit() == cardX:getSuit() and card2:getId() ~= cardX:getId() then
				if Check_R(card2) then return card2 end
			end
		end
	else
		local allcards = sgs.QList2Table(self.player:getCards("he"))
		for _, card in ipairs(allcards) do
			for _,card2 in ipairs(allcards) do
				if card2:getSuit() == card:getSuit() and card2:getId() ~= card:getId() then
					if Check_R(card) then return card end
				end
			end
		end
	end
end
sgs.ai_skill_cardask["@luakuangxiangA"] = function(self, data)
		local to_give = findkuangxiangCard(self)
		if not to_give then return "." end
		if not self.player:isWounded() then return "." end
		if sgs.isGoodHp(self.player) and self.player:hasSkills(sgs.need_maxhp_skill) then return "." end
		if self:shouldUseRende(true) and self.player:isWounded() then
			return "$" .. to_give:getEffectiveId()
		end
	return "."
end
sgs.ai_skill_cardask["@luakuangxiangB"] = function(self, data)
	local cardX = self.player:getMark("kuangxiangx") - 1
	if cardX and cardX >= 0 then
		cardX = sgs.Sanguosha:getCard(cardX)
		if cardX and cardX:objectName() then
			local to_give = findkuangxiangCard(self, cardX)
			if not to_give then return "." end
			self.room:writeToConsole("huadie test" .. cardX:objectName() .. cardX:getId())
			return "$" .. to_give:getEffectiveId()
		end
	end
	return "."
end





sgs.ai_need_damaged.luafengmo = function(self, attacker, player)
	if player:hasSkill("luafengmo") and player:getLostHp() >= 3 then return true end 
	return false
end 


sgs.ai_skill_playerchosen.luafengmo = function(self, targets)
	local count = self.player:getMark("@luafengmoa")
	if count < 3 then return self.player end 
	if #self.enemies > 0 then return self.enemies[1] end 
	return self.player
end 

sgs.ai_skill_choice.luafengmo = function(self, choices, data)
	local target = self.room:getTag("fengmoT"):toPlayer()
	if self:isEnemy(target) then 
		return "decrease"
	end
	return "plus"
end 



local luaxiehui_skill = {} -- 初始化 LuaShengong_skill 为空表
luaxiehui_skill.name = "luaxiehui" -- 设置 name
table.insert(sgs.ai_skills, luaxiehui_skill) -- 把这个表加入到 sgs.ai_skills 中   

local function findfengmocard(self, what)
	local cards = sgs.QList2Table(self.player:getCards("he"))
	local redcards = {}  --, blackcards 
	local blackcards = {}  --,  
	local count = self.player:getMark("@luafengmoa") - self.player:getMark("@luafengmob")
	
	for _, card in ipairs(cards) do
		if card:isBlack() and (card:getNumber() == 13) then 
			table.insert(blackcards, card)
		end 
		if card:isRed() and (card:getNumber() == 13) and not self:OverFlowPeach(card)
			and not card:isKindOf("ExNihilo") and not card:isKindOf("Duel") 
			and not ((card:isKindOf("Jink") or card:isKindOf("Analeptic")) and self.player:getHp() == 1) then 
			table.insert(redcards, card)
		end 
	end 	
	self:sortByKeepValue(redcards)
	self:sortByKeepValue(blackcards)
	if what then 
		return redcards
	else
		return blackcards
	end 
end 

luaxiehui_skill.getTurnUseCard = function(self)
	if #findfengmocard(self, true) > 0 then 
		local card = findfengmocard(self, true)[1]
		local suit = card:getSuitString()
		local number = card:getNumberString()
		local card_id = card:getEffectiveId()
		local card_str = ("xiehuia:luaxiehui[%s:%s]=%d"):format(suit, number, card_id)
		local slash = sgs.Card_Parse(card_str)
		assert(slash)
		return slash
	end 
	if (#self.enemies > 0) then 
		if #findfengmocard(self, false) > 0 then 
			local card = findfengmocard(self, false)[1]
			local suit = card:getSuitString()
			local number = card:getNumberString()
			local card_id = card:getEffectiveId()
			local card_str = ("xiehuib:luaxiehui[%s:%s]=%d"):format(suit, number, card_id)
			local slash = sgs.Card_Parse(card_str)
			assert(slash)
			return slash
		end 
		return
	end 
end 

function SmartAI:useCardxiehuia(card, use)	
	use.card = card
	if use.to then
		use.to:append(self.player)
	end
end 

function SmartAI:useCardxiehuib(card, use)	
	local enemies = self.enemies
	self:sort(enemies, "defense2")
	for _, target in ipairs(enemies) do
		if not target:isWounded() then 
			use.card = card
			if use.to then
				use.to:append(target)
			end
			return
		end 
	end 
	for _, target in ipairs(enemies) do
		use.card = card
		if use.to then
			use.to:append(target)
		end
		return
	end 
end 

sgs.ai_use_priority.luaxiehui = 12
sgs.ai_use_priority.xiehuia = 12
sgs.ai_use_priority.xiehuib = 12
sgs.ai_card_intention.xiehuia = -20
sgs.ai_card_intention.xiehuib = 20

sgs.ai_cardneed.luaqiuwen = function(to, card)
	if to:hasSkill("luaqiuwen") then 
		return (card:isBlack() or ((card:getSuit() == sgs.Card_Heart) and to:hasSkill("luachuanming")))
			and not card:isKindOf("BasicCard")
	end 
end

sgs.ai_skill_invoke.luajinxi = function(self, data)
	return true 
end
sgs.ai_need_damaged.luajinxi = function (self, attacker, player)
	if not player:faceUp() and (player:hasSkill("luajinxi") and player:getPile("yuanqi"):length() ~= 0)
			and self:isFriend(attacker, player) then
		return true
	end
	return false
end
local luajinxi_skill = {} -- 初始化 LuaShengong_skill 为空表
luajinxi_skill.name = "luajinxi" -- 设置 name
table.insert(sgs.ai_skills, luajinxi_skill) -- 把这个表加入到 sgs.ai_skills 中   

local function findluajinxicard(self)
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)	
	local to_use = {}
	for _,acard in ipairs(cards) do
		if (acard:getSuit() == sgs.Card_Diamond) then 
			local bool_1 = acard:isKindOf("Weapon") and ((not self.player:getWeapon()) or self.room:getCardPlace(acard:getId()) == sgs.Player_PlaceEquip) 
			local bool_2 = acard:isKindOf("Peach") or acard:isKindOf("Analeptic")
			if not bool_1 and not bool_2 then 
				table.insert(to_use, acard)
			end 
		end 		
	end 	
	return to_use
end 
luajinxi_skill.getTurnUseCard = function(self)
	local cards = findluajinxicard(self)
	if cards and (#cards > 1) and (self.player:getPile("yuanqi"):length() == 0)  then 
		return sgs.Card_Parse("#luajinxi:.:")
	end 
	
end 

sgs.ai_skill_use_func["#luajinxi"] = function(X, use, self)
	local cards = findluajinxicard(self)
	local card_ids = {}
	
	for _, card in ipairs(cards) do
		table.insert(card_ids, card:getId())
	end 
	if cards and (#cards > 1) and (self.player:getPile("yuanqi"):length() == 0)  then 	
		use.card = sgs.Card_Parse("#luajinxi:".. table.concat(card_ids, "+")..":")	
		if use.to then use.to:append(self.player) end
		return		
	end 
end 

sgs.ai_need_damaged.luajinxi = function (self, attacker, player)
	if not player:hasSkill("luajinxi") then return false end
	if player:getHp() == 1 and player:getPile("yuanqi") and (player:getPile("yuanqi"):length() > 0) and not self.player:faceUp() then 
		return true 
	end 
end 

local function findluaqiuwencard(self)
	local cards = self.player:getCards("he")
	self.room:writeToConsole("aq ai test P")
	cards = sgs.QList2Table(cards)	
	self:sortByKeepValue(cards)
	local to_use = {}
	for _,acard in ipairs(cards) do --Check_R
		if (acard:isBlack() or ((acard:getSuit() == sgs.Card_Heart) and self.player:hasSkill("luachuanming"))) and not acard:isKindOf("BasicCard") then

			local bool_1 = acard:isKindOf("ExNihilo")
			local bool_2 = acard:isKindOf("Weapon") and ((not self.player:getWeapon()) or ((self.room:getCardPlace(acard:getId()) == sgs.Player_PlaceEquip) and not acard:isKindOf("YitianSword"))) 
			local bool_3 = (acard:isKindOf("EightDiagram") or acard:isKindOf("RenwangShield") or acard:isKindOf("Tengu"))
				and ((self.room:getCardPlace(acard:getId()) == sgs.Player_PlaceEquip)
				or ((self.room:getCardPlace(acard:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
			local bool_4 = false
			if acard:isKindOf("TrickCard") then
				if acard:isKindOf("Indulgence") then
					for _, enemy in ipairs(self.enemies) do
						if self:getIndulgenceValue(enemy) > 4 then bool_4 = true end
					end
				elseif acard:isKindOf("AOE") and self:getAoeValue(acard) > 40 then
					bool_4 = true
				elseif acard:isKindOf("IronChain") then
					for _, friend in ipairs(self.friends) do
						if friend:hasSkills(sgs.shuxin_skill) then bool_4 = true end
					end
				elseif not acard:isKindOf("Dismantlement") and self:ThreeCheck(acard) then bool_4 = true
				else
					local dummy_use = { isDummy = true }
					self:useTrickCard(acard, dummy_use)
					if acard:isKindOf("Snatch") and dummy_use.card then bool_4 = true end
					if acard:isKindOf("Duel") and dummy_use.card then bool_4 = true end
					if acard:isKindOf("quanxiang") and dummy_use.card then bool_4 = true end

				end
			end
			if acard:isKindOf("Wanbaochui") then bool_4 = false end
			if (not bool_1) and (not bool_2) and (not bool_3) and (not bool_4) then
				table.insert(to_use, acard) 
			end 
		end 		
	end 	
	return to_use
end
local function findluaqiuwencard2(self, acard)
	if acard:isKindOf("ExNihilo") then return true end
	if acard:isKindOf("Wanbaochui") then return true end
	if acard:isKindOf("Weapon") and (not self.player:getWeapon() or acard:isKindOf("YitianSword")) then return true end
	if (acard:isKindOf("EightDiagram") or acard:isKindOf("RenwangShield"))
			and ((self.room:getCardPlace(acard:getId()) == sgs.Player_PlaceEquip)
			or ((self.room:getCardPlace(acard:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor()) then return true end
	if acard:isKindOf("TrickCard") then
		if acard:isKindOf("Indulgence") then
			for _, enemy in ipairs(self.enemies) do
				if self:getIndulgenceValue(enemy) > 4 then return true end
			end
		elseif acard:isKindOf("AOE") and self:getAoeValue(acard) > 40 then
			return true
		elseif acard:isKindOf("IronChain") then
			for _, friend in ipairs(self.friends) do
				if friend:hasSkills(sgs.shuxin_skill) then bool_4 = true end
			end
		else
			local dummy_use = { isDummy = true }
			self:useTrickCard(acard, dummy_use)
			if acard:isKindOf("Snatch") and dummy_use.card then return true end
			if acard:isKindOf("FaithCollection") and dummy_use.card then return true end
			if acard:isKindOf("Duel") and dummy_use.card then return true end
			if acard:isKindOf("quanxiang") and dummy_use.card then return true end
		end
	end
	if acard:isKindOf("Slash") then
		local kplayer = self:YouMu()
		if kplayer and kplayer:getHp() <= 2 then return true end
	end
end
sgs.ai_skill_cardask["@qiuwen"] = function(self, data, pattern, target)
	local to_use = findluaqiuwencard(self)
	self:sortByKeepValue(to_use)
	local slash = sgs.Sanguosha:cloneCard("slash")
	if self.room:getTag("uaqiuwen") and self.room:getTag("uaqiuwen"):toString() ~= "" 
		and to_use and (#to_use > 0) then 
		local uaqiuwen = self.room:getTag("uaqiuwen"):toString()
		uaqiuwen = uaqiuwen:split("|")		
		local qiuwen_list = {}
		local qiuwen_list2 = {}
		for i = 1,#uaqiuwen do 
			table.insert(qiuwen_list, tonumber(uaqiuwen[i]))
		end
		for _, id in ipairs(qiuwen_list) do
			table.insert(qiuwen_list2, sgs.Sanguosha:getCard(id))
		end 
		self:sortByUseValue(qiuwen_list2)
		for _, card in ipairs(qiuwen_list2) do
			local dummy_use = {isDummy = true}
            if card:isKindOf("BasicCard") then
                self:useBasicCard(card, dummy_use)
            elseif card:isKindOf("EquipCard") then
                self:useEquipCard(card, dummy_use)
            elseif card:isKindOf("TrickCard") then
                self:useTrickCard(card, dummy_use)
            end
            if dummy_use.card then
				if findluaqiuwencard2(self, dummy_use.card) then
					return "$" .. to_use[1]:getId()
				end 
			end 
			local xr = self:getCardsNum("Peach") + self:getCardsNum("Analeptic")			
			if card:isKindOf("Peach") and xr < 2 then return "$" .. to_use[1]:getId() end 
			
		end 
	end
	return "."
end 

sgs.ai_skill_askforag.luaqiuwen = function(self, card_ids) --
	--[[local to_use = findluaqiuwencard(self)
	if #to_use == 1 then 
		local cards = {}
		for i, card_id in ipairs(card_ids) do
			local card = sgs.Sanguosha:getCard(card_id)
			table.insert(cards, card)
		end 
		self:sortByUseValue(cards)
		return cards[1]:getId()
	else]]--
		local cards = {}
		for i, card_id in ipairs(card_ids) do
			local card = sgs.Sanguosha:getCard(card_id)
			local dummy_use = {isDummy = true}
            if card:isKindOf("BasicCard") then
                self:useBasicCard(card, dummy_use)
            elseif card:isKindOf("EquipCard") then
                self:useEquipCard(card, dummy_use)
            elseif card:isKindOf("TrickCard") then
                self:useTrickCard(card, dummy_use)
            end
            if dummy_use.card then
				if findluaqiuwencard2(self, dummy_use.card) then
					table.insert(cards, card)
				end 
			end 
			
		end 
		self:sortByUseValue(cards)
		if #cards == 0 then 
			cards = {}
			for i, card_id in ipairs(card_ids) do
				local card = sgs.Sanguosha:getCard(card_id)
				table.insert(cards, card)
			end 
			self:sortByUseValue(cards)
			return cards[1]:getId()		
		else
			return cards[1]:getId()	
		end 
	--end
end

sgs.ai_skill_use["akyuu"] = function(self, prompt, pattern)

	local useslash
	local cardX = self.room:getTag("luaqiuwenTC"):toCard()
	if not cardX then
		if not tonumber(pattern) then return "." end
		cardX = sgs.Sanguosha:getCard(tonumber(pattern))
	end
	if not cardX then return "." end
	self.room:writeToConsole("Akyuu ai test")
	if cardX:isKindOf("Jink") or cardX:isKindOf("Nullification") or cardX:isKindOf("sakura") then return "." end 
	local cardsd = self.player:getCards("he")
	cardsd = sgs.QList2Table(cardsd)	
	for _, card in ipairs(cardsd) do
		if card:getId() == cardX:getId() then useslash = card end 
	end 
	if not useslash then return "." end 
	local dummy_use = {isDummy = true ,to = sgs.SPlayerList()}
	if useslash:isKindOf("BasicCard") then
		self:useBasicCard(useslash, dummy_use)
	elseif useslash:isKindOf("EquipCard") then
		self:useEquipCard(useslash, dummy_use)
	elseif useslash:isKindOf("TrickCard") then
		self:useTrickCard(useslash, dummy_use)
	end	
	if dummy_use.card then 
		self.room:writeToConsole("阿求ai测试2")
		if dummy_use.to then
			local targets = {}
			for _, p in sgs.qlist(dummy_use.to) do
				table.insert(targets, p:objectName())
			end
			return useslash:toString() .. "->" .. table.concat(targets, "+")
		else
			return useslash:toString()
		end
	end 
	return "."
end 
sgs.ai_use_priority.luajinxi = 12


function findhuadieCard(self, cardX)
	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end 
	end 	
	
	local function compID(cards, id)
		for _,card in ipairs(cards) do
			if card:getId() == id then return true end 
		end 
		return false 
	end 

	local cards = sgs.QList2Table(self.player:getCards("he"))
	local sakura = self:getCards("sakura")	
	local rp = self:getCardsNum("Jink") + #sakura
	local to_give = {}
	local slash_c = 0
	local Eternity = self.room:findPlayerBySkillName("luahuadie")
	if not self:isFriend(Eternity) then return to_give end 
	local p = 0
	if Eternity:getPile("die") then 
		p = Eternity:getHp() - Eternity:getPile("die"):length()
	else
		p = Eternity:getHp()
	end 
	
	if self.player:hasSkill("luahuadie") then 
		local rcards = self:getTurnUse(true)
		self:sortByUseValue(cards, true)
		for _,card in ipairs(cards) do
			if not compID(rcards, card:getId()) then table.insert(to_give, card) end 
		end 
		if #to_give > 1 then return to_give end 
	end 
	
	for _,card in ipairs(cards) do
		local boolK = true
		for _, askill in sgs.qlist(self.player:getVisibleSkillList(true)) do
			local callback = sgs.ai_cardneed[askill:objectName()]
			if type(callback)=="function" and callback(self.player, card, self) then
				boolK = false
			end
		end
		if ((not cardX) or card:getId() ~= cardX:getId()) and not compID(to_give, card:getId()) and boolK then
			if card:isKindOf("Peach") then
				if p >= 2 then table.insert(to_give, card) end 
			end 		
			if (card:isKindOf("Jink") or card:isKindOf("sakura"))and rp > 1 then table.insert(to_give, card) end 
			if card:isKindOf("Slash") then 
				local dummy_use = {isDummy = true}
				self:useBasicCard(card, dummy_use)
				if not dummy_use.card then 
					table.insert(to_give, card) 
				else
					if self.player:hasSkills(sgs.need_slash_skill) then 
						if slash_c == 0 then 
							slash_c = slash_c + 1 
						else
							table.insert(to_give, card) 
						end 				
					end 
					table.insert(to_give, card) 
				end
			end 
			if card:isKindOf("Analeptic") then 
				if self:isWeak() then table.insert(to_give, card) end 
			end 
			if card:isKindOf("TrickCard") then 
				if card:isKindOf("IronChain") and not shuxin then table.insert(to_give, card) end 
				if card:isKindOf("Lightning") and not self:willUseLightning(card) then table.insert(to_give, card) end 
				local dummy_use = {isDummy = true}
				self:useTrickCard(card, dummy_use)
				if not dummy_use.card then table.insert(to_give, card) end 		
			end 
			if card:isKindOf("EquipCard") and not compID(to_give, card:getId()) then --or card:isKindOf("DefensiveHorse") 
				if card:isKindOf("OffensiveHorse") then 
					if self.player:getOffensiveHorse() then table.insert(to_give, card) end 				
				end 	
				if card:isKindOf("DefensiveHorse") then 
					if self.player:getDefensiveHorse() then table.insert(to_give, card) end 				
				end 
				if card:isKindOf("Armor") and self:needToThrowArmor() then table.insert(to_give, card) end 
				if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
					local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
						or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
					if not bool_3 then table.insert(to_give, card) end 	
				end 
				if card:isKindOf("Weapon") then 
					local dummy_use = {isDummy = true}
					self:useEquipCard(card, dummy_use)
					if not dummy_use.card then table.insert(to_give, card) end 	
					if dummy_use.card and self.player:getWeapon() then table.insert(to_give, self.player:getWeapon()) end 				
				end 
			end 
		end 
	end 
	return to_give
end 
sgs.ai_skill_cardask["@luahuadie"] = function(self, data)
	local cardX = self.player:getMark("luahuadiex") - 1
	local Eternity = self.room:findPlayerBySkillName("luahuadie")
	if not self:isFriend(Eternity) then return "." end 
	self.room:writeToConsole("化蝶测试" .. self.player:objectName())
	if cardX and cardX >= 0 then 
		cardX = sgs.Sanguosha:getCard(cardX)
		if cardX and cardX:objectName() then 
			local to_give = findhuadieCard(self, cardX)
			self.room:writeToConsole("化蝶测试" .. cardX:objectName() .. cardX:getId())			
			if not to_give or (#to_give < 1) then to_give = sgs.QList2Table(self.player:getCards("he")) end 
		
			if self.player:getPhase() == sgs.Player_Play or self.player:getPhase() == sgs.Player_Draw
				or self.player:getPhase() == sgs.Player_Judge or self.player:getPhase() == sgs.Player_Start then 
				self:sortByUseValue(to_give, true)
				self.room:writeToConsole("化蝶测试" .. to_give[1]:objectName() .. to_give[1]:getId())
				if to_give[1]:getEffectiveId() ~= cardX:getEffectiveId() then 
					return "$" .. to_give[1]:getEffectiveId()
				else
					return "$" .. to_give[2]:getEffectiveId()
				end 
			else
				self:sortByKeepValue(to_give)
				if to_give[1]:getEffectiveId() ~= cardX:getEffectiveId() then 
					return "$" .. to_give[1]:getEffectiveId()
				else
					return "$" .. to_give[2]:getEffectiveId()
				end 
			end 
		end 
	else
		local to_give = findhuadieCard(self)
		if not to_give then return "." end 
		if (#to_give < 2) and (not self:willSkipPlayPhase()) then return "." end 
		if #to_give < 2 then to_give = sgs.QList2Table(self.player:getCards("he")) end 
		if self:shouldUseRende(true) and self.player:isWounded() then
			if self.player:getPhase() == sgs.Player_Play or self.player:getPhase() == sgs.Player_Draw
				or self.player:getPhase() == sgs.Player_Judge or self.player:getPhase() == sgs.Player_Start then 
				self:sortByUseValue(to_give, true)
				return "$" .. to_give[1]:getEffectiveId()
			else
				self:sortByKeepValue(to_give)
				return "$" .. to_give[1]:getEffectiveId()
			end 
		end 
	end 
	return "."
end

sgs.ai_skill_use["@@luazhenchi"] = function(self, data, method)
	local mostDraw = 0
	local mostDrawP
	local bool_0 = false
	for _, friend in ipairs(self.friends) do
		if friend:getMark("@luayi") > mostDraw then mostDraw = friend:getMark("@luayi"); mostDrawP = friend:objectName() end
		if friend:hasSkills(sgs.shuxin_skill) then bool_0 = true; break end
	end
	self.room:writeToConsole("zhenchi test")
	self:sort(self.friends, "defense")
	if self.friends[1]:getMark("@luayi") >= mostDraw - 1 and self.friends[1]:getMark("@luayi") > 1 then mostDraw = self.friends[1]:getMark("@luayi"); mostDrawP = self.friends[1]:objectName() end
	local enemy_num = self:getEnemyNumBySeat(self.room:getCurrent(), self.player, self.player, true, false)
	if self.room:getCurrent():objectName() == self.player:objectName() then enemy_num = #self.enemies end
	if mostDraw == 0 then return end
	self.room:writeToConsole("zhenchi test2")
	local cardsA = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cardsA)
	if self.player:getHp() > 4 then return "." end
	if mostDraw > 2 then
		return "#luazhenchi:".. cardsA[1]:getEffectiveId() .. ":->" .. mostDrawP
	end
	for _, card in ipairs(cardsA) do
		self.room:writeToConsole("zhenchi test3")
		local bool1 = false
		if card:isKindOf("TrickCard") then
			local dummy_use = { isDummy = true }
			self:useTrickCard(card, dummy_use)
			if card:isKindOf("Snatch") and dummy_use.card then bool1 = true end
			if card:isKindOf("quanxiang") and dummy_use.card then bool1 = true end
			if card:isKindOf("SupplyShortage") and dummy_use.card then bool1 = true end
		end
		local bool_2 = card:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip))
		local bool_3 = (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu"))
				and ((self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
				or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())

		if not (card:isKindOf("Peach") and (not self:OverFlowPeach(card) or (mostDraw == 1))) and not bool1  and not bool_3
			and not (card:isKindOf("Jink") and self:isWeak() and (enemy_num > 0)) and not card:isKindOf("ExNihilo")
			and not (card:isKindOf("AOE") and (self:getAoeValue(card, self.player) >= 30))
			and not (card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 8)
			and not ((card:isKindOf("Jink") or card:isKindOf("Analeptic")) and mostDraw == 1) and not (card:isKindOf("IronChain") and bool_0)
			and not ((card:isKindOf("Snatch") or card:isKindOf("Analeptic") or card:isKindOf("IronChain") or bool_2
				or card:isKindOf("OffensiveHorse") or card:isKindOf("DefensiveHorse")) and mostDraw == 1) then
			return "#luazhenchi:".. card:getEffectiveId() .. ":->" .. mostDrawP
		end
	end
end

local luahuadie_skill = {}
luahuadie_skill.name = "luahuadie"
table.insert(sgs.ai_skills, luahuadie_skill)
luahuadie_skill.getTurnUseCard = function(self)
	if self.player:getHandcardNum() < 2 and self.player:getPile("luadie"):length() > 0 then
		return sgs.Card_Parse("#luahuadie:.:") 
	end 
end

sgs.ai_skill_use_func["#luahuadie"] = function(card, use, self)	
	use.card = sgs.Card_Parse("#luahuadie:.:" ) 
	if use.to then use.to:append(self.player) end
	return
end 

sgs.ai_use_priority.luahuadie = 15




sgs.ai_cardneed.luabenwo = function(to, card)
	return (card:isKindOf("Weapon") and not to:getWeapon())
		or (card:isKindOf("OffensiveHorse") and not to:getOffensiveHorse()) 
		or (card:isKindOf("Slash") and to:getHandcardNum() <= to:getHp()) 
end 

local function Not_get_bug(toUse, card)
	for _, cardX in ipairs(toUse) do
		if cardX:getId() == card:getId() then return false end
	end
	return true
end
function findqiangweicard(self, cardf)
	local cards = cardf
	local toUse = {}
	local fcaerds = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(fcaerds)
	for _, card in ipairs(fcaerds) do
		if self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand and card:isKindOf("EquipCard") then 
			local dummy_use = {isDummy = true}
			self:useEquipCard(card, dummy_use)
			if self:getSameEquip(card, self.player) and dummy_use.card and Not_get_bug(toUse, card) then
				table.insert(toUse, self:getSameEquip(card, self.player))
			elseif self:getSameEquip(card, self.player) and Not_get_bug(toUse, card) then
				table.insert(toUse, card)
			end 
		end 
	end 	

	local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_Spade, 7)
	local jink_v = self:getKeepValue(slash, true, true) - 0.5
	--self.room:writeToConsole("恋恋测试a  " .. jink_v)
	for _, card in ipairs(fcaerds) do
		if  Not_get_bug(toUse, card) and self:getKeepValue(card) <= jink_v and not table.contains(toUse, card) then
			table.insert(toUse, card)
		end 
	end 
	
	local jinks = self:getCardsNum("Jink")
	local jcount = 0
	for _, card in ipairs(fcaerds) do
		if (jcount == jinks - 1) or (jinks == 0) then break end 
		if card:isKindOf("Jink") and not Not_get_bug(toUse, card) then
			table.insert(toUse, card)
			jcount = jcount + 1
		end
	end 

	if self.player:getHp() > 3 then
		local jcount1 = 0
		for _, card in ipairs(fcaerds) do
			if card:isKindOf("Jink") then
				jcount1 = jcount1 + 1
			end
		end
		if jcount1 > 1 then toUse = {} end
	end
	return toUse
end 
local luaqiangwei_skill = {}
luaqiangwei_skill.name = "luaqiangwei"
table.insert(sgs.ai_skills, luaqiangwei_skill)
luaqiangwei_skill.getTurnUseCard = function(self)
	if (self.player:getMark("@qiangweiw") == 0) then 
		local cardf = self:getTurnUse(true)
		local fcaerds = sgs.QList2Table(self.player:getCards("he"))
		local cards = findqiangweicard(self, cardf)
		local damage = false
		local length = self.player:getHp() - 1
		if #fcaerds < length then return end 
		for _, card in ipairs(cardf) do
			if card:isKindOf("Weapon") then return end 
			--self.room:writeToConsole("恋恋测试1" .. card:objectName())
			if card:isKindOf("FireAttack") or card:isKindOf("Slash") or card:isKindOf("Duel") then 
				damage = true 
			end 
			if not self.player:isChained() then 
				if (card:isKindOf("ThunderSlash") or card:isKindOf("FireSlash")) then 
					local dummy_use = { isDummy = true , to = sgs.SPlayerList() }
					self:useBasicCard(card, dummy_use)	
					
					if dummy_use.card and dummy_use.to and dummy_use.to:length() > 0 then 		
						for _, p in sgs.qlist(dummy_use.to) do
							if (p:isChained() or (self.player:hasSkill("luabenwo") and not p:inMyAttackRange(self.player) and not p:isChained())) then return end 
						end 
					end 
				end 
			end 
		end 	
		if self.player:getHp() <= 1 then 
			return sgs.Card_Parse("#luaqiangwei:.:") 
		end 
		if self.player:getHp() >= 2 then 
			if #fcaerds >= length - 1 then 
				if self.player:isChained() and #cards >= length - 1 then 
					return sgs.Card_Parse("#luaqiangwei:.:") 
				end 
				if self.player:hasArmorEffect("vine") and self:isGoodChainTarget(self.player)
					and #cards >= length - 1 then 
					return sgs.Card_Parse("#luaqiangwei:.:") 
				end 
			end 
			if (self.player:getHandcardNum() > self.player:getHp()) and damage then
				local count = self.player:getHandcardNum() - self.player:getHp() - 1
				if count < self.player:getHp() - 1 then return end 
			end 
			if cards and #cards >= length then return sgs.Card_Parse("#luaqiangwei:.:") end 
		end 
	end 
end

sgs.ai_skill_use_func["#luaqiangwei"] = function(X, use, self)
	local cardf = self:getTurnUse(true)
	local toUse = findqiangweicard(self, cardf)
	local length = self.player:getHp() - 1
	if (not toUse) and (length > 0) then return end 
	if length > 0 then 
		local fcaerds = sgs.QList2Table(self.player:getCards("he"))
		self:sortByKeepValue(fcaerds)
		if #toUse < length then 
			for _, card in ipairs(fcaerds) do
				if not table.contains(toUse, card)  then 		
					table.insert(toUse, card)
				end 
			end 
		end 
		if #toUse < length then return end
	end 
	if length > 0 then 
		local newUse = {}
		for _, card in ipairs(toUse) do
			table.insert(newUse, card:getId())
			if #newUse == length then break end 
		end 
		use.card = sgs.Card_Parse("#luaqiangwei:".. table.concat(newUse, "+")..":")
	else
		use.card = sgs.Card_Parse("#luaqiangwei:.:")
	end 
	if use.to then use.to:append(self.player); return end	
end 

sgs.ai_use_priority.luaqiangwei = 15



local luaqingnang_skill = {}
luaqingnang_skill.name = "luaqingnang"
table.insert(sgs.ai_skills, luaqingnang_skill)
luaqingnang_skill.getTurnUseCard = function(self)
	if self.player:getHandcardNum() < 1 then return nil end
	if self.player:hasUsed("#luaqingnang") then return nil end
	if self.player:getHandcardNum() == 0 then return nil end
	return sgs.Card_Parse("#luaqingnang:.:") 

end

sgs.ai_skill_use_func["#luaqingnang"] = function(card, use, self)
	local cards = self.player:getHandcards()
	cards = sgs.QList2Table(cards)
	
	local compare_func = function(a, b)
		local v1 = self:getKeepValue(a) + ( a:isRed() and 50 or 0 ) + ( a:isKindOf("Peach") and 50 or 0 )
		local v2 = self:getKeepValue(b) + ( b:isRed() and 50 or 0 ) + ( b:isKindOf("Peach") and 50 or 0 )
		return v1 < v2
	end
	table.sort(cards, compare_func)
	
	use.card = sgs.Card_Parse("#luaqingnang:" .. cards[1]:getEffectiveId() ..":")
	
	local arr1, arr2 = self:getWoundedFriend(false, true)
	local target

	if #arr1 > 0 and (self:isWeak(arr1[1]) or self:getOverflow() >= 1) and arr1[1]:getHp() < getBestHp(arr1[1]) then target = arr1[1] end
	if target then
		if use.to then use.to:append(target) end
		return
	end
	if self:getOverflow() > 0 and #arr2 > 0 then
		for _, friend in ipairs(arr2) do
			if not (friend:hasSkills("hunzi|yhunzi|longhun")) then	--yun
				if use.to then use.to:append(friend) end
				return
			end
		end
	end
end

sgs.ai_use_priority.luaqingnang = 6.2
sgs.ai_card_intention.luaqingnang = -100

local luayuzhi_skill = {}
luayuzhi_skill.name = "luayuzhi"
table.insert(sgs.ai_skills, luayuzhi_skill)
luayuzhi_skill.getTurnUseCard = function(self)
	local cards = self:getTurnUse(true)
	if self.player:hasUsed("yuzhi") then return end
	self:sortByUseValue(cards)
	local useSlash
	for _, card in ipairs(cards) do
		if card:isKindOf("Slash") then useSlash = card; break end
	end 		



	if not useSlash then return nil end
	local suit = useSlash:getSuitString()
	local number = useSlash:getNumberString()
	local card_id = useSlash:getEffectiveId()
	local card_str = ("yuzhi:luayuzhi[%s:%s]=%d"):format(suit, number, card_id)
	local skillcard = sgs.Card_Parse(card_str)

	assert(skillcard)

	return skillcard
end



sgs.ai_skill_use["@@luayongye"] = function(self, prompt)
	local cards = sgs.QList2Table(self.player:getCards("he"))
	local indulgence = sgs.Sanguosha:cloneCard("indulgence")
	local value_1 = self:getUseValue(indulgence) + 0.5
	self.room:writeToConsole("永夜测试")
	local supplyshortage = sgs.Sanguosha:cloneCard("supply_shortage")
	local value_2 = self:getUseValue(supplyshortage) + 0.5
	for _, card in ipairs(cards) do
		for i = 1, 10 do
			local str = "luayongye" .. i
			if self.player:getMark(str) == card:getEffectiveId() and (((self:getUseValue(card) <= value_1) and card:isRed()) or ((self:getUseValue(card) <= value_2) and card:isBlack())) then 
				self.room:writeToConsole("永夜测试2")
				if card:isRed() then 
					indulgence:addSubcard(card:getId())
					local dummy_use = { isDummy = true , to = sgs.SPlayerList() }
					self:useTrickCard(indulgence, dummy_use)		
					if dummy_use.card and dummy_use.to and (dummy_use.to:length() > 0) then 
						self.room:writeToConsole("永夜测试3")
						
						indulgence:setSkillName("luayongye")
	
						return indulgence:toString() .. "->" .. dummy_use.to:at(0):objectName()  -- "#luayongye:" .. card:getId() .. ":->" .. :objectName()	
					end 
				else
					supplyshortage:addSubcard(card:getId())
					local dummy_use = { isDummy = true , to = sgs.SPlayerList() }
					self:useTrickCard(supplyshortage, dummy_use)		
					if dummy_use.card and dummy_use.to and (dummy_use.to:length() > 0) then 
						self.room:writeToConsole("永夜测试3")
						supplyshortage:setSkillName("luayongye")
						return supplyshortage:toString() .. "->" .. dummy_use.to:at(0):objectName()  --"#luayongye:" .. card:getId() .. ":->" .. dummy_use.to:at(0):objectName()	
					end 				
				end 
			end 
		end 	
	end 
end 


sgs.ai_skill_invoke.lualongwen = function(self, data)
	local damage = data:toDamage()
	local victim = damage.to
	local x = 0
	for _, c in sgs.qlist(self.player:getJudgingArea()) do
		if c:isKindOf("longwen") then
			x = x + 1
		end
	end

	local caijue = false
	local shikieiki = self.room:findPlayerBySkillName("luapanjue")
	if shikieiki and not self:isFriend(shikieiki) and not shikieiki:isKongcheng() then caijue = true end
	if self:isWeak() and caijue then return false end
	if not self.player:hasFlag("luamoulue") and self.player:hasSkill("luamoulue") and self:isFriend(victim) then
		if self.player:getHp() == 1 and not caijue then return true end -- 死猪不怕开水烫2020年2月8日18:32:36
		if damage.nature ~= sgs.DamageStruct_Normal then
			if not damage.chain and victim:isChained() and not self:isGoodChainTarget(victim, damage.from, damage.nature, damage.damage, damage.card)
				and damage.damage > 1 then
				return true
			end
		end
		if self.player:getHp() - x <= 1 then return false end
		if self:isWeak(damage.to) then return true end
		if self.player:containsTrick("longwen") then return false end
	end
end
sgs.ai_choicemade_filter.skillInvoke["lualongwen"] = function(self, player, promptlist)
	local kitchou = self.room:findPlayerBySkillName("lualongwen")
	local to = kitchou:getTag("lualongwen"):toPlayer()
	if not to then self.room:writeToConsole(debug.traceback()) end
	if promptlist[#promptlist] == "yes" then
		sgs.updateIntention(kitchou, to, -40)
	end
end

sgs.ai_skill_invoke.luamoulue = true
sgs.ai_skill_playerchosen.luamoulue2 = function(self, targets)
	self.room:writeToConsole("moulueceshiW " .. self.player:getGeneralName())
	if self:Kitcho() then return self:Kitcho() end
	return self.player
end

sgs.ai_skill_playerchosen.luamoulue3 = function(self, targets)
	if self:Skadi() then return self:Skadi() end
	return self.enemies[1]
end
sgs.ai_slash_prohibit.luamoulue = function(self, from, to, card)
	if not to:hasFlag("luamoulue") and self:isEnemy(from, to) then
		for _, enemyx in ipairs(self.enemies) do
			if enemyx:hasWeapon("pundarika") then return true end
		end
	end
end

sgs.ai_skill_playerchosen.luamoulue4 = function(self, targets)
	local dismantlement = sgs.Sanguosha:cloneCard("dismantlement")
	dismantlement:setSkillName("luatianhu")
	local dummy_use = { isDummy = false, to = sgs.SPlayerList() }
	self:useTrickCard(dismantlement, dummy_use)
	if dummy_use.card and dummy_use.card:isKindOf("Dismantlement") and dummy_use.to:length() > 0 then
		return dummy_use.to:at(0)
	end
end

sgs.ai_skill_invoke.luaanyuan = function(self, targets)
	local teimu = self.room:findPlayerBySkillName("luaanyuan")
	if teimu and teimu:isAlive() and self:isFriend(teimu) then
		return true
	end
end

local function findWXCard(self, card_0)
	if self.player:isCardLimited(card_0, sgs.Card_MethodDiscard) then return false end
	local x = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
	local bool_k = (self.player:getHp() <= 2 or (self.player:getHandcardNum() <= 3 and x <= 1))
	if (card_0:isKindOf("Peach") or card_0:isKindOf("ExNihilo")) and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
		return false
	end
	if (card_0:isKindOf("Indulgence") or card_0:isKindOf("SupplyShortage")) and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
		return false
	end
	if card_0:isKindOf("Duel") and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
		return false
	end
	if card_0:isKindOf("Snatch") or card_0:isKindOf("Dismantlement") then
		local use = { isDummy = true }
		self:useCardSnatchOrDismantlement(card_0, use)
		if use.card then return false end
	end
	if (card_0:isKindOf("AOE") and self:getAoeValue(card_0) > 35) then return false end
	if card_0:isKindOf("Lightning") and not self:willUseLightning(card_0) then return true end
	local bool_2 = card_0:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(card_0:getId()) == sgs.Player_PlaceEquip))
	local bool_3 = (card_0:isKindOf("EightDiagram") or card_0:isKindOf("RenwangShield") or card_0:isKindOf("Tengu"))
		and ((self.room:getCardPlace(card_0:getId()) == sgs.Player_PlaceEquip)
		or ((self.room:getCardPlace(card_0:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
	if (bool_2 or bool_3) then return false end
	if card_0:isKindOf("Slash") then return true end
	if (not (card_0:isKindOf("Jink") and bool_k) and not (isCard("Peach", card_0, self.player) and self.player:isWounded())) then
		return true
	end

	for _, askill in sgs.qlist(self.player:getVisibleSkillList(true)) do
		local callback = sgs.ai_cardneed[askill:objectName()]
		if type(callback)=="function" and callback(self.player, card_0, self) then
			return false
		end
	end
	return true
end

sgs.ai_skill_invoke.luaweixinqw = function(self, targets)
	local teimu = self.room:findPlayerBySkillName("luaanyuan")
	self.room:writeToConsole("weixin test")
	if teimu and teimu:isAlive() then
		local rp
		for _, p in sgs.qlist(self.room:getAlivePlayers()) do
			if p:getMark("@suide") > 0 then rp = p;break end
		end
		self.room:writeToConsole("weixin testA")
		if rp and rp:isAlive() and self:isEnemy(rp) then
			self.room:writeToConsole("weixin testB")
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			if not self:slashIsEffective(slash, rp) then
				if self:getOverflow(teimu) >= 0 then return false end
				if self:getOverflow() < 0 then return false end
			end
			self.room:writeToConsole("weixin testC")
			for _, card_0 in sgs.list(self.player:getHandcards()) do
				if findWXCard(self, card_0) then return true end
			end
		end
	end
end

function sgs.ai_cardneed.luaanyuan(to, card)
	return card:isKindOf("Slash")
end

local luasuide_skill = {} -- 初始化 LuaShengong_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 LuaShengong_skill 只是为了出于习惯
luasuide_skill.name="luasuide" -- 设置 name
table.insert(sgs.ai_skills, luasuide_skill) -- 把这个表加入到 sgs.ai_skills 中   self:HasGou(false, player)
luasuide_skill.getTurnUseCard = function(self)
	local rp
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:getMark("@suide") > 0 then rp = p;break end
	end
	if rp then return end
	if #self.friends > 0 then
		return sgs.Card_Parse("#luasuide:.:")
	end
end
sgs.ai_skill_use_func["#luasuide"] = function(card, use, self)
	for _, card_0 in sgs.list(self.player:getHandcards()) do
		if card_0:isKindOf("Slash") and not card_0:isRed() and not self.player:isCardLimited(card_0, sgs.Card_MethodDiscard) then
			self:sort(self.enemies, "defenseSlash")
			local f_slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			for _, friend in ipairs(self.friends) do
				for _, enemy in ipairs(self.enemies) do
					if not self:slashProhibit(f_slash, enemy, friend) then
						use.card = sgs.Card_Parse("#luasuide:" .. card_0:getEffectiveId()..":")
						if use.to then
							use.to:append(enemy)
							return
						end
					end
				end
			end
		end
	end
end

sgs.ai_use_priority.luasuide = 1.38

sgs.ai_skill_discard.luaweixinqw = function(self, discard_num, min_num, optional, include_equip)
	for _, card_0 in sgs.list(self.player:getHandcards()) do
		if findWXCard(self, card_0) then return card_0:getId() end
	end
end

sgs.ai_skill_use["@@luatianhuang"] = function(self, data, method)
	local friendsN = {}
	for _, friend in ipairs(self.friends) do
		table.insert(friendsN, friend:objectName())
	end
	return "#luatianhuang:.:->" .. table.concat(friendsN, "+")
end

local function BUGG(self, card)
	if card:isKindOf("EquipCard") then
		local dummy_use = {isDummy = true}
		self:useEquipCard(card, dummy_use)
		if dummy_use.card then return true end
	elseif card:isKindOf("ExNihilo") then
		return true
	elseif card:isKindOf("Lightning") and self:willUseLightning(card) then
		return true
	elseif card:isKindOf("IronChain") and self:isChained() then
		return true
	elseif card:isKindOf("Peach") and self.player:getLostHp() > 1 then
		return true
	elseif card:isKindOf("Analeptic") and self.player:getLostHp() > 0 then
		return true
	end
	return false
end
local luayingdengr_skill = {} -- 初始化 LuaShengong_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 LuaShengong_skill 只是为了出于习惯
luayingdengr_skill.name = "luayingdengr" -- 设置 name
table.insert(sgs.ai_skills, luayingdengr_skill)
luayingdengr_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luayingdengr") then return end
	local wriggle = self.room:findPlayerBySkillName("luayingdeng")
	if (not wriggle) or (not wriggle:isAlive()) then return end
	if wriggle:isKongcheng() then return end
	return sgs.Card_Parse("#luayingdengr:.:")
end
sgs.ai_skill_use_func["#luayingdengr"] = function(cardQ, use, self)
	local wriggle = self.room:findPlayerBySkillName("luayingdeng")
	if self.player:hasSkill("luayingdeng") then
		if self.player:isWounded() then
			if self.player:hasSkill("luachongqun2") then
				if (not self:Wriggle()) or (self.player:isWounded() and self.player:getHp() == 1) then
					use.card = sgs.Card_Parse("#luayingdengr:.:")
					if use.to then
						use.to:append(self.player)
						return
					end
				end
			else
				for _, card in sgs.list(self.player:getHandcards()) do
					if BUGG(self, card) then
						use.card = sgs.Card_Parse("#luayingdengr:.:")
						if use.to then
							use.to:append(self.player)
							return
						end
					end
				end
			end
		end
	else
		if wriggle:isWounded() or (((self:getFriendNumBySeat(self.player, wriggle) <= 1) or (wriggle:getHandcardNum() > 1)
			or (self:getOverflow() <= 0 and self.player:getMark("@luatianshii") <= 1)) and wriggle:hasSkill("luachongqun2")) then
			local bool1 = self.player:hasSkill("luayuechong") and (self:getFriendNumBySeat(self.player, wriggle) > 1)
			if not bool1 then
				use.card = sgs.Card_Parse("#luayingdengr:.:")
				if use.to then
					use.to:append(self.player)
					return
				end
			end
		end
	end
end

sgs.ai_use_priority.luayingdengr = 16

function sgs.ai_slash_prohibit.luafenxing(self, from, to)
	local daiyousei = self.room:findPlayerBySkillName("niaoxiang")
	if self:isFriend(to, from) then return false end
	if to:hasSkill("luafenxing") and to:getMark("@LuaYizuo") > 0 and daiyousei and daiyousei:isAlive() and to:getHandcardNum() > 1 then
		return true
	end
end


local luashidan_skill = {}
luashidan_skill.name = "luashidan"
table.insert(sgs.ai_skills, luashidan_skill)
luashidan_skill.getTurnUseCard = function(self, inclusive)
	if not self.player:hasUsed("#luashidan") and self.player:getMark("@luamaoyou") > 0 then
		local count = 0
		for _, ecard in sgs.qlist(self.player:getHandcards()) do
			if (ecard:isKindOf("SingleTargetTrick") and not ecard:isKindOf("DelayedTrick") and #self.enemies > 1)
					or (ecard:isKindOf("Slash") and #self.enemies > 1)
					or (ecard:isKindOf("Peach") and self.player:isWounded() and #self.friends > 1)then count = count + 1 end
		end
		if count > 1 or (self:Skadi(nil, nil, nil, true) and count == 1) then
			return sgs.Card_Parse("#luashidan:.:")
		end
	end
end
sgs.ai_skill_use_func["#luashidan"] = function(card, use, self)
	use.card = sgs.Card_Parse("#luashidan:.:")
	if use.to then
		use.to = sgs.SPlayerList()
		return
	end
end
sgs.pay_ai_card.Peach.luamaoyou = function(self, card, use, mustusepeach) 
	if self:getCardsNum("Slash") >= 1 and self.player:getHp() >= 2 and #self.friends > 1 and self.player:hasSkill("luamaoyou")
			and not self.player:hasSkill("luashidan") and self.player:getMark("@luamaoyou") >= 1 and (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) then return 2 end
end


local function findHongBeiTarget(self)
	for  _, aplayer in sgs.qlist(self.room:getAlivePlayers()) do
		if aplayer:getMark("@lualongyan") > 0 and not aplayer:isKongcheng() then
			return aplayer
		end
	end
	if not self.player:isKongcheng() then
		if self.player:isWounded() then --要输
			local enemies = self.enemies
			self:sort(enemies, "handcard2")	--拆乐/兵
			for _, enemy in ipairs(enemies) do
				if not enemy:isKongcheng() then
					local to_card = self:getMinCard()
					local x = enemy:getHandcardNum()
					x = (x * x)/30
					local k = math.random()
					if not (self:getMaxCard(enemy) and to_card:getNumber() > self:getMaxCard(enemy):getNumber()) then
						if self:getMinCard(enemy) and to_card:getNumber() <= self:getMinCard(enemy):getNumber()
								and (self:getKnownNum(enemy) == enemy:getHandcardNum()) then return enemy end
						if to_card:getNumber() > 8 and (k > (0.6 + x)) then return enemy end
						if to_card:getNumber() > 6 and (k > (0.25 + x)) then return enemy end
						if to_card:getNumber() <= 6 and (k > (0.1 + x)) then return enemy end
					end
				end
			end
		end
		local friends = self.friends_noself
		self:sort(friends, "defense")	--拆乐/兵
		for _, player in ipairs(friends) do  --要赢
			if player:isWounded() and not player:isKongcheng()
				and getCardsNum("Peach", player) < player:getHandcardNum() - 0.2 then
				local to_card = self:getMaxCard()
				local x = 5 - player:getHandcardNum()
				x = (x * x)/30

				local k = math.random()
				if not (self:getMinCard(player) and to_card:getNumber() <= self:getMinCard(player):getNumber()) then
					if self:getMaxCard(player) and to_card:getNumber() > self:getMaxCard(player):getNumber()
							and (self:getKnownNum(player) == player:getHandcardNum()) then return player  end
					if to_card:getNumber() > 10 and (k > (0.1 + x)) then return player end
					if to_card:getNumber() > 8 and (k > (0.25 + x)) then return player end
					if to_card:getNumber() > 6 and (k > (0.6 + x)) then return player end
				end
			end
		end
		if self.player:isWounded() then --要输
			for _, player in ipairs(friends) do
				if not player:isKongcheng() and getCardsNum("Peach", player) < player:getHandcardNum() - 0.2 then
					local to_card = self:getMaxCard()
					local x = 5 - player:getHandcardNum()
					x = (x * x)/30
					local k = math.random()
					--[[
					if self:getMaxCard(player) and to_card:getNumber() > self:getMaxCard(player):getNumber() then return end
					if self:getMinCard(player) and to_card:getNumber() <= self:getMinCard(player):getNumber()
							and (self:getKnownNum(player) == player:getHandcardNum()) then return player end
					if to_card:getNumber() > 10 then return end]]--
					if to_card:getNumber() > 8 and (k > (0.6 + x)) then return player end
					if to_card:getNumber() > 6 and (k > (0.25 + x)) then return player end
					if to_card:getNumber() <= 6 and (k > (0.1 + x)) then return player end
				end
			end
		end
	end
	for _, enemy in ipairs(self.enemies) do
		if not enemy:isWounded() and not enemy:isKongcheng() then
			return enemy
		end
	end
	for _, enemy in ipairs(self.enemies) do
		if not enemy:isKongcheng() then return enemy end
	end
end
local luahongpei_skill = {}
luahongpei_skill.name = "luahongpei"
table.insert(sgs.ai_skills, luahongpei_skill)
luahongpei_skill.getTurnUseCard = function(self, inclusive)
	if self.player:hasUsed("#luahongpei") then return end
	return sgs.Card_Parse("#luahongpei:.:")
end

sgs.ai_skill_use_func["#luahongpei"] = function(card, use, self)
	use.card = sgs.Card_Parse("#luahongpei:.:")
	local target = findHongBeiTarget(self)
	if use.to then
		use.to:append(target)
		return
	end
end

function sgs.ai_skill_pindian.luahongpei(minusecard, self, requestor, maxcard, mincard)

	local cards = sgs.QList2Table(self.player:getHandcards())
	local function compare_func1(a, b)
		return a:getNumber() > b:getNumber()
	end
	table.sort(cards, compare_func1)
	local table2 = {}
	if self.player:objectName() == requestor:objectName() then
		local table3 = {}
		local req
		for _, p in sgs.qlist(self.room:getOtherPlayers(self.player)) do
			if p:hasFlag("luahongpei_Target") then
				req = p
				break
			end
		end
		if req and self.player:isWounded() then
			for _, card in ipairs(cards) do
				if card:getNumber() < 8 then table.insert(table3, card) end
			end
			self:sortByKeepValue(table3)
			if #table3 > 0 then return table3[1] end
			return self:getMinCard()
		end
		if req then
			for _, card in ipairs(cards) do
				if card:getNumber() > 5 then table.insert(table3, card) end
			end
			self:sortByKeepValue(table3)
			if #table3 > 0 then return table3[1] end
			return self:getMaxCard()
		end
	end
	if requestor and self:isFriend(requestor) and self.player:isWounded() then
		for _, card in ipairs(cards) do
			if card:getNumber() < 7 then table.insert(table2, card) end
		end
		self:sortByKeepValue(table2)
		if #table2 > 0 then return table2[1] end
		return self:getMinCard()
	elseif requestor then
		for _, card in ipairs(cards) do
			if card:getNumber() > 10 then table.insert(table2, card) end
		end
		self:sortByKeepValue(table2)
		if #table2 > 0 then return table2[1] end
	end
	return self:getMaxCard()
end

sgs.ai_skill_use["@@lualongyan"] = function(self, prompt)
	local count_func = function(splayer)
		local count = 0
		local aplayer = splayer
		for i = 1,10 do
			aplayer = aplayer:getNextAlive()
			count = count + 1
			if aplayer:objectName() == self.player:objectName() then break end
		end
		return count
	end
	local compare_func = function(a, b)
		return count_func(a) < count_func(b)
	end

	if self.player:getMark("@lualongyan") >= 1 then
		local lord = self.room:getLord()
		if lord and lord:isAlive() and self.role == "rebel" and count_func(lord) < self.room:getAlivePlayers():length() / 2 - 1 then
			return "#lualongyan:.:->" .. lord:objectName()
		end
		local enemies = self.enemies
		table.sort(enemies, compare_func)
		for _, player in ipairs(enemies) do
			if self:AtomDamageCount2(player, self.player, sgs.DamageStruct_Fire, nil) > 1 then
				return "#lualongyan:.:->" .. player:objectName()
			end
		end
		for _, player in ipairs(enemies) do
			if self:damageIsEffective(player, sgs.DamageStruct_Fire) then
				return "#lualongyan:.:->" .. player:objectName()
			end
		end
	end
end
sgs.ai_use_priority.lualongyan = 9.5
sgs.ai_card_intention.lualongyan = 100

sgs.pay_ai_card.Peach.lualongyan = function(self, card, use, mustusepeach)
	if self.player:getLostHp() == 1 and self.player:getMark("@longyan") > 0 then return 1 end 
	return 0 
end 

sgs.ai_skill_cardask["@luahongpei"] = function(self, data, pattern, target)
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(cards)
	return "$" .. cards[1]:getEffectiveId()
end

sgs.ai_skill_invoke.luaduxin1 = function(self, data)
    local target = data:toPlayer()
	if target and not self:isFriend(target) then return true end
	local zp = target:getNextAlive()
	if target and zp and zp:isAlive() and not self.player:isChained() and self:isFriend(target)
		and self.player:distanceTo(zp) == 1 and self:isEnemy(zp) then
		return true
	end
	if self:isFriend(target) then return false end
	return true
end
sgs.ai_skill_invoke.luaduxin2 = function(self, data)
	local target = data:toPlayer()
	if self.player:getMark("@huiyi") > 0 and not target:isChained() then
		return true
	end
	if self:isFriend(target) and not target:isChained() then return false end
	if self:isEnemy(target) and target:isChained() then return false end
	return true
end

local luahuiyi_skill = {}
luahuiyi_skill.name = "luahuiyi"
table.insert(sgs.ai_skills, luahuiyi_skill)
luahuiyi_skill.getTurnUseCard = function(self, inclusive)
	if self.player:getMark("@huiyi") >= 1 then
		return sgs.Card_Parse("#luahuiyi:.:")
	end
end
sgs.ai_skill_use_func["#luahuiyi"] = function(card, use, self)
	local count = 0
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:isChained() and (not p:isKongcheng() or self:isEnemy(p)) then count = count + 1 end
	end
	for _, friend in ipairs(self.friends) do
		if self:isWeak(friend) then count = count + 0.5 end
		if friend:isKongcheng() and friend:isChained() then count = count - 0.75 end
	end
	if count >= self.room:getAlivePlayers():length() / 2 then
		use.card = sgs.Card_Parse("#luahuiyi:.:")
		if use.to then
			use.to = sgs.SPlayerList()
			return
		end
	end
end
sgs.ai_use_priority.luahuiyi = 8

sgs.ai_skill_invoke.luashanyang = function(self, data)
	local satori = self.room:getLord()
	local shuxin = false
	for _, enemy in ipairs(self.enemies) do
		if enemy:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end
	if self.player:hasSkill("luamaobu") then return true end
	if self.player:hasSkill("LuaJubian") and not self:isWeak() then return true end
	if self:isFriend(satori) and satori:getMark("@huiyi") > 0 then return true end
	if self:isFriend(satori) then
		for _,p in sgs.qlist(self.room:getAlivePlayers()) do
			if self.player:distanceTo(p) == 1 and satori:distanceTo(p) ~= 1 and not self:isFriend(p) and not shuxin then
				return true
			end
		end
	end
	return false
end

sgs.ai_skill_playerchosen.luashanyang = function(self, targets, slashX)
	local targetlist = sgs.QList2Table(targets)
	for _, target in ipairs(targetlist) do	--杀敌
		if not self:isFriend(target) then return target end
	end
end
























