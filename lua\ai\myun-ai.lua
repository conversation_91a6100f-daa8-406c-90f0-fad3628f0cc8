
sgs.ai_skill_invoke.yzhuisha = function(self, data)
	local target = data:toPlayer()
	return not self:isFriend(target)
end

sgs.ai_skill_invoke.yyexi = function(self, data)
	if self.player:isSkipped(sgs.Player_Play) then
		return false
	end
	if #self.enemies < 1 then return false end
	return true
end

local ynongquan_skill = {}
ynongquan_skill.name = "ynongquan"
table.insert(sgs.ai_skills, ynongquan_skill)
ynongquan_skill.getTurnUseCard = function(self)
	if not self.player:hasFlag("ynongquan") then return end
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
    local ncard_id = self.player:getMark("ynongquan")
    local ncard = sgs.Sanguosha:getCard(ncard_id)
	local card
	if not ncard then return nil end
	if self:needToThrowArmor() then card = self.player:getArmor() end
	if not card then
		self:sortByUseValue(cards, true)
		for _,acard in ipairs(cards)  do
			if self:getUseValue(acard) < self:getUseValue(ncard) then
				card = acard
				break
			end
		end
	end
	if not card then return nil end
	local skillcard = sgs.Sanguosha:cloneCard(ncard:objectName(), card:getSuit(), card:getNumber())
	skillcard:addSubcard(card)
	skillcard:setSkillName("ynongquan")
	assert(skillcard)
	return skillcard
end

local yfeidao_skill = {}
yfeidao_skill.name = "yfeidao"
table.insert(sgs.ai_skills, yfeidao_skill)
yfeidao_skill.getTurnUseCard = function(self)
	local weapon = self.player:getWeapon()
	if not weapon then
		local cards = self.player:getHandcards()
		for _, card in sgs.qlist(cards) do
			if card:isKindOf("Weapon") then
				weapon = card
				break
			end
		end	
	end
	if not weapon then return end
	local dummy_use = {isDummy = true}
	local card = self:getCard("Slash")
	if card and self:slashIsAvailable() then
		local dummy_use = { isDummy = true }
		self:useBasicCard(card, dummy_use)
		if dummy_use.card then
			return
		end
	end
	return sgs.Card_Parse("#yfeidao:" .. weapon:getId().. ":")	
end

sgs.ai_skill_use_func["#yfeidao"] = function(card, use, self)	
	self:sort(self.enemies, "hp")
	for _, enemy in ipairs(self.enemies) do
		if self:objectiveLevel(enemy) > 3 and not self:cantbeHurt(enemy) and self:damageIsEffective(enemy) then
			use.card = card			
			if use.to then
				use.to:append(enemy)
			end
			return
		end
	end
end

sgs.ai_use_priority.yfeidao = 0
sgs.ai_use_priority.yfeidaocard = 0
sgs.ai_card_intention.yfeidaocard = 80
sgs.ai_cardneed.yfeidao = sgs.ai_cardneed.weapon

local yjieliang_skill = {}
yjieliang_skill.name = "yjieliang"
table.insert(sgs.ai_skills, yjieliang_skill)
yjieliang_skill.getTurnUseCard = function(self)
	local cards = self.player:getCards("h")
	cards = sgs.QList2Table(cards)

	local card
	self:sortByUseValue(cards, true)
	for _,acard in ipairs(cards)  do
		if acard:getSuit() == sgs.Card_Spade then
			card = acard
			break
		end
	end

	if not card then return nil end
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	local card_str = ("snatch:yjieliang[spade:%s]=%d"):format(number, card_id)
	local skillcard = sgs.Card_Parse(card_str)
	assert(skillcard)
	return skillcard
end

local yjuedou_skill={}
yjuedou_skill.name = "yjuedou"
table.insert(sgs.ai_skills,yjuedou_skill)
yjuedou_skill.getTurnUseCard = function(self)
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	local card
	self:sortByUseValue(cards, true)

	for _,acard in ipairs(cards)  do
		if (acard:getSuit() == sgs.Card_Spade) then
			card = acard
			break
		end
	end

	if not card then return nil end
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	local card_str = ("duel:yjuedou[spade:%s]=%d"):format(number, card_id)
	local skillcard = sgs.Card_Parse(card_str)
	assert(skillcard)
	return skillcard
end

sgs.ai_skill_invoke.ytuwei = function(self, data)
	local target = data:toPlayer()
	if target then target:setFlags("AI_ytuweiTarget") end
	return true
end

sgs.ai_skill_choice.ytuwei = function(self, choices) 
	local target
	for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if player:hasFlag("AI_ytuweiTarget") then
			target = player
			target:setFlags("-AI_ytuweiTarget")
		end
	end
	
	if self:isFriend(target) then
		if self:needKongcheng(target) and target:getHandcardNum() == 1 then
			return "hand"
		end
		return "draw"
	end
	
	if self:isEnemy(target) then				
		if self:doNotDiscard(target, "h", true) then
			return "draw"
	    else 
			return "hand"
		end
	end

	return "draw"
end

local yzhaoxiang_skill = {}
yzhaoxiang_skill.name = "yzhaoxiang"
table.insert(sgs.ai_skills, yzhaoxiang_skill)
yzhaoxiang_skill.getTurnUseCard = function(self)
	local equips = {}
	
	local eCard
	local hasCard = {0, 0, 0, 0}
	for _, card in sgs.qlist(self.player:getCards("he")) do
		if card:isKindOf("EquipCard") and not card:isKindOf("WoodenOx") then 
			hasCard[sgs.ai_get_cardType(card)] = hasCard[sgs.ai_get_cardType(card)] + 1
			table.insert(equips, card)
		end		
	end
	
	for _, card in ipairs(equips) do
		if hasCard[sgs.ai_get_cardType(card)] > 1 or sgs.ai_get_cardType(card) > 3 then 
			eCard = card 
			break
		end
		if not eCard then eCard = card end
	end
	if not eCard and self.player:getTreasure() and self.player:getTreasure():isKindOf("WoodenOx") and self.player:getPile("wooden_ox"):length() < 2 then  eCard = self.player:getTreasure() end
	
	if not eCard then return end
	
	local suit = eCard:getSuitString()
	local number = eCard:getNumberString()
	local card_id = eCard:getEffectiveId()
	local card_str = ("savage_assault:yzhaoxiang[%s:%s]=%d"):format(suit, number, card_id)
	local skillcard = sgs.Card_Parse(card_str)
	
    assert(skillcard)
    return skillcard
end

local yquanshi_skill = {}
yquanshi_skill.name = "yquanshi"
table.insert(sgs.ai_skills, yquanshi_skill)
yquanshi_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#yquanshi") then return end
	local card_str = "#yquanshi:.:"
	local yquanshi = sgs.Card_Parse(card_str)
	assert(yquanshi)
	return yquanshi 
end

sgs.ai_skill_use_func["#yquanshi"] = function(card, use, self)
	local targets, friends, enemies, others = {}, {}, {}, {}
	for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if self.player:canSlash(player) and self.player:getHp() > player:getHp() then 
			table.insert(targets, player)			
			if self:isFriend(player) then
				table.insert(friends, player)
			elseif self:isEnemy(player) and not self:doNotDiscard(player) then
				table.insert(enemies, player)
			elseif not self:doNotDiscard(player) then
				table.insert(others, player)
			end
		end
	end
	
	if #targets == 0 then return end	
	local target
	
	self:sort(enemies, "defense")
	for _, enemy in ipairs(enemies) do
		if self:getDangerousCard(enemy) then
			target = enemy
			break
		end
	end

	if not target then
		for _, friend in ipairs(friends) do
			if self:needToThrowArmor(friend) then
				target = friend
				break
			end
		end
	end
	if not target then
		for _, friend in ipairs(friends) do
			if friend:hasSkill("kongcheng") and friend:getHandcardNum() == 1 and self:getEnemyNumBySeat(self.player, friend) > 0
			  and friend:getHp() <= 2 then
				target = friend
				break
			end
		end
	end
	
	if not target and #enemies == 0 and #others == 0 then return end
	if not target then
		for _, enemy in ipairs(enemies) do
			if self:getValuableCard(enemy) and not self:doNotDiscard(enemy, "e") then
				target = enemy
				break
			end
		end
	end
	if not target then
		for _, enemy in ipairs(enemies) do
			local cards = sgs.QList2Table(enemy:getHandcards())
			local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
			if #cards <= 2 and not self:doNotDiscard(enemy, "h") then
				for _, cc in ipairs(cards) do
					if (cc:hasFlag("visible") or cc:hasFlag(flag)) and (cc:isKindOf("Peach") or cc:isKindOf("Analeptic")) then
						target = enemy
						break
					end
				end
			end
		end
	end
	if not target then
		for _, enemy in ipairs(enemies) do
			if self:hasSkills(sgs.concentrated_skill,enemy) and not self:doNotDiscard(enemy, "e") then
				local equips = { enemy:getDefensiveHorse(), enemy:getArmor(), enemy:getOffensiveHorse(), enemy:getWeapon() }
				if enemy:getDefensiveHorse() then
					target = enemy
					break
				end
				if not target then
					if enemy:getArmor() and not enemy:hasArmorEffect("silver_lion") then
						target = enemy
						break	
					end
				end
				if not target then
					for _ , equip in ipairs(equips) do
						if equip and equip:isRed() and enemy:hasSkill("jijiu") then 
							target = enemy
							break
						end
					end
				end
			end
		end
	end
	if not target then
		for _, enemy in ipairs(enemies) do
			if not self:doNotDiscard(enemy, "e") then
				target = enemy
				break
			end
		end	
	end
	if not target then
		for _, enemy in ipairs(enemies) do
			if not enemy:isKongcheng() and not self:doNotDiscard(enemy, "h") then
				target = enemy
				break
			end
		end
	end
	if not target then
		for _, enemy in ipairs(enemies) do
			if not self:doNotDiscard(enemy) then
				target = enemy
				break
			end
		end
	end
	
	if not target then
		for _, other in ipairs(others) do
			if not self:doNotDiscard(other) then
				target = other
				break
			end
		end
	end

	if not target then return end

	if use.to then
		use.to:append(target)
	end
	use.card = card
end

sgs.ai_use_priority.yquanshi = 9.51
sgs.dynamic_value.control_card.yquanshi = true

local ytiaodou_skill = {}
ytiaodou_skill.name = "ytiaodou"
table.insert(sgs.ai_skills, ytiaodou_skill)
ytiaodou_skill.getTurnUseCard = function(self)
	local card_str = "#ytiaodou:.:"
	local ytiaodou = sgs.Card_Parse(card_str)
	assert(ytiaodou)
	return ytiaodou 	
end

sgs.ai_skill_use_func["#ytiaodou"] = function(card, use, self)
		local hand_card, cards
		cards = self.player:getHandcards()
		
		for _, card in sgs.qlist(cards) do
			if card:isKindOf("Analeptic") then
				hand_card = card
				break
			end
		end
		if not hand_card then
			for _, card in sgs.qlist(cards) do
				if card:isKindOf("Peach") then
					hand_card = card
					break
				end
			end
		end

		if not hand_card then return end

        self:sort(self.enemies, "hp")		
		for _, enemy in ipairs(self.enemies) do	
			if self:objectiveLevel(enemy) > 3 and enemy:isNude() and not self:cantbeHurt(enemy) and self:damageIsEffective(enemy) then
				use.card = sgs.Card_Parse("#ytiaodou:" .. hand_card:getId().. ":")
				if use.to then
					use.to:append(enemy)
				end
				break
			end
			
            local redcard =0
			local ecard = 0
			local dicard = 0
		    local ecards = enemy:getCards("e")
		    ecards = sgs.QList2Table(ecards)

			for _, zcard in ipairs(ecards) do  
		        if zcard then ecard = ecard + 1 end
			    if zcard:getSuit() == sgs.Card_Diamond then dicard = dicard + 1 end
			    if zcard:isRed() then redcard = redcard + 1 end
	        end

			if self:objectiveLevel(enemy) > 3 and not self:cantbeHurt(enemy) and self:damageIsEffective(enemy)
			  and enemy:isKongcheng() and not (enemy:hasSkill("wusheng") and redcard) 
			  and not (enemy:hasSkill("nosgongqi") and ecard) and not (enemy:hasSkill("longhun") and dicard) 
			  and not enemy:hasArmorEffect("silver_lion") then
				use.card = sgs.Card_Parse("#ytiaodou:" .. hand_card:getId().. ":")
				if use.to then
					use.to:append(enemy)
				end
				break
			end

			if self:objectiveLevel(enemy) > 3 and not self:cantbeHurt(enemy) and self:damageIsEffective(enemy)
			  and getCardsNum("Slash", enemy) < 1 and not enemy:hasSkill("wusheng") and not enemy:hasSkill("longdan") 
			  and not enemy:hasSkill("wushen") and not enemy:hasSkill("nosgongqi") and not enemy:hasSkill("longhun")
			  and not ((enemy:hasWeapon("spear") or enemy:hasSkill("fuhun")) and enemy:getHandcardNum() > 1) and not enemy:hasArmorEffect("silver_lion") then
				use.card = sgs.Card_Parse("#ytiaodou:" .. hand_card:getId().. ":")
				if use.to then
					use.to:append(enemy)
				end
				break
			end
		end
	
end

sgs.ai_use_priority.ytiaodou = 3.3
sgs.ai_card_intention.ytiaodoucard = 100
sgs.dynamic_value.damage_card.ytiaodou = true

sgs.yjieliang_suit_value = 
{
	spade = 3.9
}

sgs.yjuedou_suit_value = 
{
	spade = 3.9
}

sgs.yjinshen_suit_value = 
{
	spade = 4.9
}

sgs.yduomou_suit_value = 
{
	club = 3.9
}

sgs.yyuanlao_keep_value = 
{
	Peach = 6,
	Analeptic = 5.8,
	Jink = 5.1,
	Crossbow = 5,
	Axe = 4.9
}

sgs.yzhaoxiang_keep_value = 
{
	Peach = 6,
	Jink = 5.1,
	Weapon = 4.9,	
	Armor = 5,
	OffensiveHorse = 4.8,
	DefensiveHorse = 5
}

sgs.yfeidao_keep_value = 
{
	Peach = 6,
	Jink = 5.1,
	Weapon = 5
}

sgs.yqijiang_keep_value = 
{
	DefensiveHorse = 5.9,
	OffensiveHorse = 5.9
}

sgs.ai_cardneed.yqijiang = sgs.ai_cardneed.equip

local yshengwang_skill = {}
yshengwang_skill.name = "yshengwang"
table.insert(sgs.ai_skills, yshengwang_skill)
yshengwang_skill.getTurnUseCard = function(self)
	if self.player:hasFlag("yshengwangused") then return end
	return sgs.Card_Parse("#yshengwang:.:")
end

sgs.ai_skill_use_func["#yshengwang"] = function(card, use, self)
	local targets = {}
	self:sort(self.enemies, "defense")
	for _, enemy in ipairs(self.enemies) do
		if (self:getCardsNum("Peach") < getCardsNum("Peach", enemy)) 
		  and self.player:getHandcardNum() > 0 and enemy:getHandcardNum() > 0 and not self:doNotDiscard(enemy, "h", true) then
			table.insert(targets, enemy)
		end
	end
        
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(cards)
	self:sort(self.enemies, "handcard")
	self:sort(self.friends_noself, "handcard")
	for _, friend in ipairs(self.friends_noself) do
		if ((self:needKongcheng(friend) and not friend:hasSkill("kongcheng") and friend:getHandcardNum() < 3) or friend:hasSkill("tuntian")) 
			and friend:getHandcardNum() > 0 and not friend:hasSkill("manjuan") then
				table.insert(targets, friend)
		end
	end

	if self.player:getHandcardNum() > 0 then
		for _, enemy in ipairs(self.enemies) do
			if not isCard("Peach", cards[1], enemy) and not isCard("Nullification", cards[1], enemy) and not isCard("Analeptic", cards[1], enemy)
			  and enemy:getHandcardNum() > 0 and (not self:doNotDiscard(enemy, "h", true) or enemy:hasSkill("manjuan")) then
				table.insert(targets, enemy)
			end
		end
	end  

	if #targets == 0 then return end
	if use.to then
		targets[1]:setFlags("yshengwang_target")
		use.to:append(targets[1])
	end
	use.card = card
end

sgs.ai_skill_choice.yshengwang = function(self, choices)
	local target
	for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if player:hasFlag("yshengwang_target") then
			target = player
			target:setFlags("-yshengwang_target")
		end
	end
	if self:doNotDiscard(target, "h", true) and not self:isFriend(target) then return "no_sw" end
	return "yes_sw"
end

sgs.ai_use_priority.yshengwang = 0

sgs.ai_skill_invoke.yqinsheng = function(self, data)
    if (self.player:getHp() < 2) then return false end
	for _, friend in ipairs(self.friends_noself) do
		if friend:isWounded()  then
			return true
		end
	end
end

sgs.ai_skill_use["@@yqinsheng"] = function(self, prompt)
	self:updatePlayers(true)
    self:sort(self.friends_noself, "hp")
	for _, friend in ipairs(self.friends_noself) do
        if friend:isWounded() then 
			if self.player:getLostHp() == 1 and friend:getHp() <= self.player:getHp() and self.player:getSeat()~= friend:getSeat()
				and not self:needToLoseHp(friend, self.player, false, false, true) then
				return "#yqinsheng:.:->" .. friend:objectName()
			elseif friend:getHp() < self.player:getHp() and not self:needToLoseHp(friend, self.player, false, true, true) then
				return "#yqinsheng:.:->" .. friend:objectName()
			end
		end
	end	
    return "."
end

sgs.ai_card_intention.yqinsheng = - 100
sgs.dynamic_value.benefit["#yqinsheng"] = true

local yduomou_skill = {}
yduomou_skill.name = "yduomou"
table.insert(sgs.ai_skills, yduomou_skill)
yduomou_skill.getTurnUseCard = function(self)
	local cards = self.player:getCards("h")
	cards = sgs.QList2Table(cards)
	local card
	self:sortByUseValue(cards, true)

	for _,acard in ipairs(cards)  do
		if (acard:getSuit() == sgs.Card_Club) then
			card = acard
			break
		end
	end

	if not card then return nil end
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	local card_str = ("collateral:yduomou[club:%s]=%d"):format(number, card_id)
	local skillcard = sgs.Card_Parse(card_str)
	assert(skillcard)
	return skillcard
end

yjuezhan_skill = {}
yjuezhan_skill.name = "yjuezhan"
table.insert(sgs.ai_skills, yjuezhan_skill)
yjuezhan_skill.getTurnUseCard = function(self)
	local cards = self.player:getCards("h")
	cards = sgs.QList2Table(cards)

	local red_card
	self:sortByUseValue(cards, true)

	for _,card in ipairs(cards)  do
		if card:isKindOf("TrickCard") then
			red_card = card
			break
		end
	end

	if red_card then
		local suit = red_card:getSuitString()
		local number = red_card:getNumberString()
		local card_id = red_card:getEffectiveId()
		local card_str = ("duel:yjuezhan[%s:%s]=%d"):format(suit, number, card_id)
		local slash = sgs.Card_Parse(card_str)

		assert(slash)
		return slash
	end
end

sgs.ai_filterskill_filter.yjuezhan = function(card, card_place)
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	if card:isKindOf("TrickCard") then return ("duel:yjuezhan[%s:%s]=%d"):format(suit, number, card_id) end
end

sgs.ai_skill_discard.yjinshen = function(self, discard_num, min_num, optional, include_equip)
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)	
	for _, card in ipairs(cards) do
		if card:getSuit() == sgs.Card_Spade then return card:getEffectiveId() end
	end
	return {}
end

sgs.ai_skill_invoke.ywushu = function(self, data)
	local card_id = self.player:getMark("ywushu")
    local card = sgs.Sanguosha:getCard(card_id)     
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	
	local cd = cards[1]	
	if cd:isKindOf("Analeptic") or cd:isKindOf("Peach") then return false end
	
	for _, skill in sgs.qlist(self.player:getVisibleSkillList()) do
		local callback = sgs.ai_cardneed[skill:objectName()]
		if type(callback) == "function" and callback(self.player, cd, self) then
			return false
		end
		if type(callback) == "function" and callback(self.player, card, self) then
			return true
		end
	end
	
	if self:isValuableCard(cd, self.player) then return false end
	if self:isValuableCard(card, self.player) then return true end
	return false
end

sgs.ai_skill_discard.ywushu = function(self, discard_num, min_num, optional, include_equip)
	local card_id = self.player:getMark("ywushu")
    local card = sgs.Sanguosha:getCard(card_id)     
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	
	local cd = cards[1]
	local id = cd:getEffectiveId()
	if cd:isKindOf("Analeptic") or cd:isKindOf("Peach") then return {} end
	
	for _, skill in sgs.qlist(self.player:getVisibleSkillList()) do
		local callback = sgs.ai_cardneed[skill:objectName()]
		if type(callback) == "function" and callback(self.player, cd, self) then
			return {}
		end
		if type(callback) == "function" and callback(self.player, card, self) then
			return id
		end
	end
	
	if self:isValuableCard(cd, self.player) then return {} end
	if self:isValuableCard(card, self.player) then return id end

	return {}
end

yfuzhou_skill = {}
yfuzhou_skill.name = "yfuzhou"
table.insert(sgs.ai_skills, yfuzhou_skill)
yfuzhou_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#yfuzhou") then return end
	local card_str = "#yfuzhou:.:"
	local yfuzhou = sgs.Card_Parse(card_str)
	assert(yfuzhou)
	return yfuzhou 
end

sgs.ai_skill_use_func["#yfuzhou"] = function(card, use, self)
	if #self.enemies == 0 then return end
	self:sort(self.enemies, "hp")
	
	local givecard = {}
	local index = 0
	local target
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	self:sortByUseValue(cards, true)
	
	for _, fcard in ipairs(cards) do
		if fcard:getSuit() == sgs.Card_Spade and fcard:getNumber() > 1 and fcard:getNumber() < 10 then
			table.insert(givecard, fcard:getId())
			index = index + 1
			if index == 3 then break end
		end
	end
	if index < 1 then return end

	local jinxuandi = self.room:findPlayerBySkillName("wuling")
	if jinxuandi and jinxuandi:getMark("@earth") > 0 then index = 1 end
	
	if index > 1 then
		for _, enemy in ipairs(self.enemies) do
			if not enemy:hasArmorEffect("silver_lion")
			  and not (enemy:hasSkills("tianxiang|ol_tianxiang") and (getKnownCard(enemy, self.player, "diamond", false) + getKnownCard(enemy, self.player, "club", false) ~= player:getHandcardNum()))
			  and self:objectiveLevel(enemy) > 3 and self:damageIsEffective(enemy, sgs.DamageStruct_Thunder)
			  and not (enemy:isChained() and not self:isGoodChainTarget(enemy)) then
				target = enemy
				break
			end
		end
	end
	if not target then
		local hand_card
		for _, fcard in ipairs(cards) do
			if fcard:getSuit() == sgs.Card_Spade and fcard:getNumber() > 1 and fcard:getNumber() < 10 then
				hand_card = fcard
				break
			end
		end
		if not hand_card then return end
		for _, enemy in ipairs(self.enemies) do
			if not (enemy:hasSkills("tianxiang|ol_tianxiang") and (getKnownCard(enemy, self.player, "diamond", false) + getKnownCard(enemy, self.player, "club", false) ~= player:getHandcardNum()))
			  and self:objectiveLevel(enemy) > 3 and self:damageIsEffective(enemy, sgs.DamageStruct_Thunder)
			  and not (enemy:isChained() and not self:isGoodChainTarget(enemy)) then
				target = enemy
				if use.to then
					self.player:speak("装逼遭雷劈啊！")
					use.to:append(target)
				end
				use.card = sgs.Card_Parse("#yfuzhou:" .. hand_card:getId().. ":")
				return
			end
		end
	end
	
	if not target then return end
	if use.to then
		self.player:speak("装逼遭雷劈啊！")
		use.to:append(target)
	end
	use.card = sgs.Card_Parse("#yfuzhou:" .. table.concat(givecard, "+").. ":")
	return 		
end

sgs.ai_use_priority.yfuzhou = 8.49
sgs.ai_card_intention["#yfuzhou"] = 80
sgs.dynamic_value.damage_card.yfuzhou = true

sgs.ai_view_as.ywuxiang = function(card, player, card_place)
	if player:getHandcardNum() > player:getHp() then return end
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	if card_place == sgs.Player_PlaceHand then
		if card:isBlack() then return ("slash:ywuxiang[%s:%s]=%d"):format(suit, number, card_id) end
		if card:isRed() then return ("jink:ywuxiang[%s:%s]=%d"):format(suit, number, card_id) end
	end
end

function sgs.ai_cardneed.ywuxiang(to, card)
	return to:getHandcardNum() < to:getHp() and to:getCards("h"):length() < 2 and card:isRed()
end

function sgs.ai_slash_prohibit.yqinxue(self, from, to, card)
	if self:isFriend(to) then return false end
	if from:hasSkill("tieji") then return false end
	if self:canLiegong(to, from) then return false end
	if self:isEnemy(to) and self:hasEightDiagramEffect(to) and not IgnoreArmor(from, to) then return true end
end

function sgs.ai_slash_prohibit.ywushen(self, from, to, card)
	if from and from:hasSkill("jueqing") then return false end
	if from and from:hasSkill("nosqianxi") and from:distanceTo(to) == 1 then return false end
	if from:hasFlag("NosJiefanUsed") then return false end
	if from:hasSkill("chuanxin") and (to:getEquips():length() > 0 or to:getMark("@chuanxin") < 1) then return false end
	if to:getHp() <= 1 and to:hasSkill("ywushen") and card:isBlack() then return true end
end

function sgs.ai_cardneed.yqinxue(to, card, self)
	return (isCard("Jink", card, to) and getKnownCard(to, self.player, "Jink", true) == 0)
		or (card:isKindOf("EightDiagram") and not (self.player:hasArmorEffect("eight_diagram") or getKnownCard(to, self.player, "EightDiagram", false) > 0))
end

sgs.ai_cardneed.yuanlv = function(to, card)
	return to:getHandcardNum() < 3 and (card:getTypeId() == sgs.Card_Basic or card:isKindOf("Nullification"))
end

function sgs.ai_cardneed.yduomou(to, card, self)
	return card:getSuit() == sgs.Card_Club and not self:hasSuit("club", true, to)
end

function sgs.ai_cardneed.yjuedou(to, card)
	return card:getSuit() == sgs.Card_Spade and not to:hasSkill("hongyan")
end

function sgs.ai_cardneed.yjieliang(to, card)
	return card:getSuit() == sgs.Card_Spade and not to:hasSkill("hongyan")
end

function sgs.ai_cardneed.yjinshen(to, card, self)
	return card:getSuit() == sgs.Card_Spade and not self:hasSuit("spade", true, to)
end

function sgs.ai_cardneed.yfuzhou(to, card, self)				
	return card:getSuit() == sgs.Card_Spade and card:getNumber() >= 2 and card:getNumber() <= 9 and not to:hasSkill("hongyan")
end

function sgs.ai_cardneed.ytiangang(to, card, self)
	return isCard("Slash", card, to) and getKnownCard(to, self.player, "Slash", true) == 0
end

function sgs.ai_cardneed.yshanzhan(to, card, self)
	return isCard("Slash", card, to) and getKnownCard(to, self.player, "Slash", true) == 0
end

function sgs.ai_cardneed.ytiaodou(to, card, self)
	return card:isKindOf("Peach") or card:isKindOf("Analeptic")
end

function sgs.ai_cardneed.yjuezhan(to, card, self)
	return card:isKindOf("TrickCard")
end

sgs.ai_cardneed.ytuodao = function(to, card)
	return card:isKindOf("Jink")
end

sgs.ai_cardneed.yzhaoxiang = sgs.ai_cardneed.equip

sgs.ai_suit_priority.yfuzhou= "club|diamond|heart|spade"
sgs.ai_suit_priority.yduomou= "spade|diamond|heart|club"
sgs.ai_suit_priority.yjuedou= "club|diamond|heart|spade"
sgs.ai_suit_priority.yjieliang= "club|diamond|heart|spade"
sgs.ai_suit_priority.yjinshen= "club|diamond|heart|spade"

local yxianzhou_skill = {}
yxianzhou_skill.name = "yxianzhou"
table.insert(sgs.ai_skills, yxianzhou_skill)
yxianzhou_skill.getTurnUseCard = function(self)
	if self.player:getMark("@handover") <= 0 then return end
	if self.player:getEquips():isEmpty() then return end
	return sgs.Card_Parse("#yxianzhou:.:")
end
sgs.ai_skill_use_func["#yxianzhou"] = function(card, use, self)
	if self:isWeak() and self.player:isWounded() then
		self:sort(self.friends_noself)
		for _, friend in ipairs(self.friends_noself) do
			if friend:hasSkills(sgs.need_equip_skill) and not hasManjuanEffect(friend) then
				use.card = card
				if use.to then use.to:append(friend) end
				return
			end
		end
		for _, friend in ipairs(self.friends_noself) do
			if not hasManjuanEffect(friend) then
				use.card = card
				if use.to then use.to:append(friend) end
				return
			end
		end
		self:sort(self.friends)
		for _, target in sgs.qlist(self.room:getOtherPlayers(self.player)) do
			local canUse = true
			for _, friend in ipairs(self.friends) do
				if target:inMyAttackRange(friend) and self:damageIsEffective(friend, nil, target)
					and not self:getDamagedEffects(friend, target) and not self:needToLoseHp(friend, target, false, true) then
					canUse = false
					break
				end
			end
			if canUse then
				use.card = card
				if use.to then use.to:append(target) end
				return
			end
		end
	end
	
	if #self.friends_noself == 0 then return end
	local function cmp_AttackRange(a, b)
		local ar_a = a:getAttackRange()
		local ar_b = b:getAttackRange()
		if ar_a == ar_b then
			return sgs.getDefense(a) < sgs.getDefense(b)
		else
			return ar_a > ar_b
		end
	end
	table.sort(self.friends_noself, cmp_AttackRange)
	
	local killer
	self:sort(self.enemies, "hp")
	for _, enemy in ipairs(self.enemies) do
		local canUse = false
		for _, friend in ipairs(self.friends_noself) do
			if friend:inMyAttackRange(enemy) and self:damageIsEffective(enemy, nil, friend)
				and not self:getDamagedEffects(enemy, friend) and not self:needToLoseHp(enemy, friend)
				and self:isWeak(enemy) then
				canUse = true
				killer = friend
				break
			end
		end
		if canUse then
			use.card = card
			if use.to then use.to:append(killer) end
			return
		end
	end

	if (self.player:getEquips():length() > 2 or self.player:getEquips():length() > #self.enemies) and sgs.turncount > 2 then
		for _, enemy in ipairs(self.enemies) do
			local canUse = false
			for _, friend in ipairs(self.friends_noself) do
				if friend:inMyAttackRange(enemy) and self:damageIsEffective(enemy, nil, friend)
					and not self:getDamagedEffects(enemy, friend) and not self:needToLoseHp(enemy, friend) then
					canUse = true
					killer = friend
					break
				end
			end
			if canUse then
				use.card = card
				if use.to then use.to:append(killer) end
				return
			end
		end
	end
end

sgs.ai_use_priority.yxianzhouCard = 4.9
sgs.ai_card_intention.yxianzhouCard = function(self, card, from, tos)
	if not from:isWounded() then sgs.updateIntentions(from, tos, -10) end
end

sgs.ai_skill_use["@@yxianzhou"] = function(self, prompt)
	local prompt = prompt:split(":")
	local n = self.player:getMark("yxianzhou")
	local current = self.room:getCurrent()
	if self:isWeak(current) and self:isFriend(current) and current:isWounded() then return "." end
	local targets = {}
	self:sort(self.enemies, "hp")
	for _, enemy in ipairs(self.enemies) do
		if self.player:inMyAttackRange(enemy) and self:damageIsEffective(enemy, nil, self.player)
			and not self:getDamagedEffects(enemy, self.player) and not self:needToLoseHp(enemy, self.player, false, true) then
			table.insert(targets, enemy:objectName())
			if #targets == n then break end
		end
	end
	if #targets < n then
		self:sort(self.friends_noself)
		self.friends_noself = sgs.reverse(self.friends_noself)
		for _, friend in ipairs(self.friends_noself) do
			if self.player:inMyAttackRange(friend) and self:damageIsEffective(friend, nil, self.player)
				and (self:getDamagedEffects(friend, self.player) or self:needToLoseHp(friend, self.player, false, true)) then
				table.insert(targets, friend:objectName())
				if #targets == n then break end
			end
		end
	end

	if #targets > 0 then
		return "#yxianzhoudmg:.:->" .. table.concat(targets, "+")
	end
	return "."
end

sgs.ai_card_intention.yxianzhouDamageCard = function(self, card, from, tos)
	for _, to in ipairs(tos) do
		if self:damageIsEffective(to, nil, from) and not self:getDamagedEffects(to, from) and not self:needToLoseHp(to, from, false, true) then
			sgs.updateIntention(from, to, 10)
		end
	end
end

sgs.ai_need_damaged.yhunzi = function(self, attacker, player)
	if not player:hasSkill("chanyuan") and player:hasSkill("yhunzi") and player:getMark("yhunzi") == 0 and self:getEnemyNumBySeat(self.room:getCurrent(), player, player, true) < player:getHp()
		and (player:getHp() > 2 or (player:getHp() == 2 and player:faceUp())) then
		return true
	end
	return false
end

sgs.ai_skill_playerchosen["ychenqing"] = function(self, targets)
    local victim = self.room:getCurrentDyingPlayer()
    local help = false
    local careLord = false
    if victim then
        if self:isFriend(victim) then
            help = true
        elseif self.role == "renegade" and victim:isLord() and self.room:alivePlayerCount() > 2 then
            help = true
            careLord = true
        end
    end
    local friends, enemies = {}, {}
    for _,p in sgs.qlist(targets) do
        if self:isFriend(p) then
            table.insert(friends, p)
        else
            table.insert(enemies, p)
        end
    end
    local compare_func = function(a, b)
        local nA = a:getCardCount(true)
        local nB = b:getCardCount(true)
        if nA == nB then
            return a:getHandcardNum() > b:getHandcardNum()
        else
            return nA > nB
        end
    end
    if help and #friends > 0 then
        table.sort(friends, compare_func)
        for _,friend in ipairs(friends) do
            if not hasManjuanEffect(friend) then
                return friend
            end
        end
    end
    if careLord and #enemies > 0 then
        table.sort(enemies, compare_func)
        for _,enemy in ipairs(enemies) do
            if sgs.evaluatePlayerRole(enemy) == "loyalist" then
                return enemy
            end
        end
    end
    if #enemies > 0 then
        for _,enemy in ipairs(enemies) do
            if hasManjuanEffect(enemy) and not self:needToThrowCard(enemy, "e") then
                return enemy
            end
        end
    end
    if #friends > 0 then
        self:sort(friends, "defense")
        for _,friend in ipairs(friends) do
            if not hasManjuanEffect(friend) and (self:needToThrowCard(friend, "e")
				or (friend:hasSkill("tuntian") and friend:getPhase() == sgs.Player_NotActive)
				or friend:hasSkill("hongde")) then
					return friend
            end
        end
    end
	return
end

sgs.ai_skill_discard["ychenqing"] = function(self, discard_num, min_num, optional, include_equip)
    local victim = self.room:getCurrentDyingPlayer()
    local help = false
    if victim then
        if self:isFriend(victim) then
            help = true
        elseif self.role == "renegade" and victim:isLord() and self.room:alivePlayerCount() > 2 then
            help = true
        end
    end
    local cards = self.player:getCards("he")
    cards = sgs.QList2Table(cards)
    self:sortByKeepValue(cards)
    if help then
        local peach_num = 0
        local spade, heart, club, diamond = nil, nil, nil, nil
        for _,c in ipairs(cards) do
            if isCard("Peach", c, self.player) then
                peach_num = peach_num + 1
            else
                local suit = c:getSuit()
                if not spade and suit == sgs.Card_Spade then
                    spade = c:getEffectiveId()
                elseif not heart and suit == sgs.Card_Heart then
                    heart = c:getEffectiveId()
                elseif not club and suit == sgs.Card_Club then
                    club = c:getEffectiveId()
                elseif not diamond and suit == sgs.Card_Diamond then
                    diamond = c:getEffectiveId()
                end
            end
        end
        if peach_num + victim:getHp() <= 0 then
            if spade and heart and club and diamond then
                return {spade, heart, club, diamond}
            end
        end
    end
    return self:askForDiscard("dummy", discard_num, min_num, optional, include_equip)
end

sgs.ai_skill_invoke.ybaobian = function(self, data)
	local to = data:toDamage().to
	if self:isFriend(to) and to:getKingdom() == self.player:getKingdom() then 
		if to:getHandcardNum() < to:getMaxHp() then return true end
		if self:getDamagedEffects(to, self.player) or self:needToLoseHp(to, self.player, nil, true) then return false end
		return true 
	end
	if self:isEnemy(to) and to:getKingdom() ~= self.player:getKingdom() then 
		local n = to:getHandcardNum() - math.max(to:getHp(), 0)
		if n > 1 then return true end
		return not self:doNotDiscard(to, "h")
	end
	return false
end

sgs.ai_skill_invoke.yinqin = function(self, data)
	local k = self.player:getKingdom()
	local shu = k == "shu"
	local wei = k == "wei"
	self:sort(self.enemies, "defense")
	local need, neednt
	for _, enemy in ipairs(self.enemies) do
		if enemy:getKingdom() == k then
			need = true
			if enemy:getHandcardNum() - math.max(enemy:getHp(), 0) > 0 and self.player:canSlash(enemy) 
				and self:slashIsEffective(sgs.Sanguosha:cloneCard("slash"), enemy, self.player)
				and self.player:inMyAttackRange(enemy) and not self:doNotDiscard(enemy, "h")
				and sgs.isGoodTarget(enemy, self.enemies, self, true) then
					return true
			end
		end
	
		if (shu and enemy:getKingdom() == "wei") or (wei and enemy:getKingdom() == "shu") then
			neednt = true
			if enemy:getHandcardNum() - math.max(enemy:getHp(), 0) > 0 and self.player:canSlash(enemy) 
				and self:slashIsEffective(sgs.Sanguosha:cloneCard("slash"), enemy, self.player)
				and self.player:inMyAttackRange(enemy) and not self:doNotDiscard(enemy, "h")
				and sgs.isGoodTarget(enemy, self.enemies, self, true) then
					return false
			end
		end
	end
	for _, friend in ipairs(self.friends_noself) do
		if (shu and friend:getKingdom() == "wei") or (wei and friend:getKingdom() == "shu") then
			if friend:getHandcardNum() < friend:getMaxHp() and self.player:canSlash(friend) 
				and self.player:inMyAttackRange(friend) then
					return true
			end
		end
	
		if friend:getKingdom() == k then
			if friend:getHandcardNum() < friend:getMaxHp() and self.player:canSlash(friend) 
				and self.player:inMyAttackRange(friend) then
					return false
			end
		end
	end
	if need then return true
	elseif neednt then return false
	else
		return (math.random(0, 1) == 0)
	end
end

function SmartAI:getTWBaobian(to, from)
	if not to or not from then return 0 end
	if from:hasSkill("jueqing") then return 0 end
	if not from:hasSkill("ybaobian") or self:isEnemy(to, from) then return 0 end
	if from:getKingdom() ~= to:getKingdom() then return 0 end
	local n = to:getMaxHp() - to:getHandcardNum()
	return n
end

sgs.ai_choicemade_filter.skillInvoke.ybaobian = function(self, player, promptlist)
	local to
	for _, p in sgs.qlist(self.room:getOtherPlayers(player)) do
		if p:hasFlag("ybaobian") then
			to = p
			break
		end
	end
	if to then
		local intention = 60
		if self:getDamagedEffects(to, player) or self:needToLoseHp(to, player, nil, true) then intention = 0 end
		if promptlist[3] == "yes" then
			if to:getKingdom() == player:getKingdom() and to:getMaxHp() - to:getHandcardNum() > 0 then
				intention = -intention
			end
			sgs.updateIntention(player, to, intention)
		else
			if self:needToThrowCard(to, "h") then intention = 0 end
			if to:getKingdom() ~= player:getKingdom() then
				intention = -intention
			end
			sgs.updateIntention(player, to, intention)
		end
	end
end

sgs.ai_skill_choice.yliangzhu = function(self, choices, data)
	local current = self.room:getCurrent()
	if self:isFriend(current) then
		return "letdraw"
	end
	return "draw"
end

sgs.ai_choicemade_filter.skillChoice.yliangzhu = function(self, player, promptlist)
	local choice = promptlist[#promptlist]
	local current = self.room:getCurrent()
	if not current then return end
	if choice == "letdraw" then sgs.updateIntention(player, current, -10) end
end

sgs.ai_skill_use["@@yfangtong"] = function(self, prompt)
    local pile = self.player:getPile("fang")
	local piles = {}
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	if pile:length() < 2 or (#cards == 0) then
		return "."
	end
	local dmg = 3
	sgs.fangtong_to = nil
	local jinxuandi = self.room:findPlayerBySkillName("wuling")
	if jinxuandi and jinxuandi:getMark("@earth") > 0 and not self.player:hasSkill("jueqing") then dmg = 1 end
	self:sort(self.enemies, "defense")
	local players = self.room:getOtherPlayers(self.player)
    players = sgs.QList2Table(players)
	for _, enemy in ipairs(self.enemies) do
		if #players == 1 or (sgs.turncount > 2 and #self.enemies == 1) then sgs.fangtong_to = enemy break end
		if (not enemy:hasArmorEffect("silver_lion") and not self.player:hasSkill("jueqing")) and dmg > 1
		and not (enemy:hasSkills("tianxiang|ol_tianxiang") and (getKnownCard(enemy, self.player, "diamond", false) + getKnownCard(enemy, self.player, "club", false) ~= player:getHandcardNum()))
		and self:objectiveLevel(enemy) > 3 and self:damageIsEffective(enemy, sgs.DamageStruct_Thunder)
		and not (enemy:isChained() and not self:isGoodChainTarget(enemy)) then
			sgs.fangtong_to = enemy
			break
		end
	end
	if not sgs.fangtong_to and not self:needToThrowArmor() then return "." end
	for _, card_id in sgs.qlist(pile) do
		table.insert(piles, sgs.Sanguosha:getCard(card_id))
	end
	self:sortByKeepValue(cards)
	self.fangtong = {}
	for _,card1 in ipairs(cards) do
		for __,card2 in ipairs(piles) do
			for __,card3 in ipairs(piles) do
				if card2:getId() == card3:getId() then
				elseif card1:getNumber() + card2:getNumber() + card3:getNumber() > 36 then
				elseif card1:getNumber() + card2:getNumber() + card3:getNumber() == 36 then
					table.insert(self.fangtong, card2:getId())
					table.insert(self.fangtong, card3:getId())
					return "#yfangtong:" .. card1:getId()..":"
				elseif pile:length() > 2 then
					for __,card4 in ipairs(piles) do
						if card2:getId() == card4:getId() or card3:getId() == card4:getId() then
						elseif card1:getNumber() + card2:getNumber() + card3:getNumber() < 12 then
						elseif card1:getNumber() + card2:getNumber() + card3:getNumber() + card4:getNumber() > 36 then
						elseif card1:getNumber() + card2:getNumber() + card3:getNumber() + card4:getNumber() == 36 then
							table.insert(self.fangtong, card2:getId())
							table.insert(self.fangtong, card3:getId())
							table.insert(self.fangtong, card4:getId())
							return "#yfangtong:" .. card1:getId()..":"
						elseif pile:length() > 3 then
							for __,card5 in ipairs(piles) do
								if card2:getId() == card5:getId() or card3:getId() == card5:getId() or card4:getId() == card5:getId() then
								elseif card1:getNumber() + card2:getNumber() + card3:getNumber() + card4:getNumber() < 23 then
								elseif card1:getNumber() + card2:getNumber() + card3:getNumber() + card4:getNumber() + card5:getNumber() > 36 then
								elseif card1:getNumber() + card2:getNumber() + card3:getNumber() + card4:getNumber() + card5:getNumber() == 36 then
									table.insert(self.fangtong, card2:getId())
									table.insert(self.fangtong, card3:getId())
									table.insert(self.fangtong, card4:getId())
									table.insert(self.fangtong, card5:getId())
									return "#yfangtong:" .. card1:getId()..":"
								end
							end
						end
					end
				end
			end
		end
	end
	if self:needToThrowArmor() then return "#yfangtong:" .. self.player:getArmor():getId()..":" end
end

sgs.ai_skill_askforag.yfangtong = function(self, card_ids)
	local ids = self.fangtong
	for _, id in ipairs(card_ids) do
		if table.contains(ids, id) then return id end
	end
	return -1
end

sgs.ai_skill_playerchosen.yfangtong = function(self, targets)
	return sgs.fangtong_to
end