﻿<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.1//EN"
    "http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en">
<head>
<meta http-equiv="Content-Type" content="application/xhtml+xml; charset=UTF-8" />
<meta name="generator" content="AsciiDoc 8.6.3" />
<title>樊城之战</title>
<style type="text/css">
/* Sans-serif font. */
h1, h2, h3, h4, h5, h6,
div.title, caption.title,
thead, p.table.header,
div#toctitle,
span#author, span#revnumber, span#revdate, span#revremark,
div#footer {
  font-family: Arial,Helvetica,sans-serif;
}

/* Serif font. */
div.sectionbody {
  font-family: Georgia,"Times New Roman",Times,serif;
}

/* Monospace font. */
tt {
  font-size: inherit;
}

body {
  margin: 1em 5% 1em 5%;
}

a {
  color: blue;
  text-decoration: underline;
}
a:visited {
  color: fuchsia;
}

em {
  font-style: italic;
  color: navy;
}

strong {
  font-weight: bold;
  color: #083194;
}

tt {
  font-size: inherit;
  color: navy;
}

h1, h2, h3, h4, h5, h6 {
  color: #527bbd;
  margin-top: 1.2em;
  margin-bottom: 0.5em;
  line-height: 1.3;
}

h1, h2, h3 {
  border-bottom: 2px solid silver;
}
h2 {
  padding-top: 0.5em;
}
h3 {
  float: left;
}
h3 + * {
  clear: left;
}

div.sectionbody {
  margin-left: 0;
}

hr {
  border: 1px solid silver;
}

p {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

ul, ol, li > p {
  margin-top: 0;
}
ul > li     { color: #aaa; }
ul > li > * { color: black; }

pre {
  padding: 0;
  margin: 0;
}

span#author {
  color: #527bbd;
  font-weight: bold;
  font-size: 1.1em;
}
span#email {
}
span#revnumber, span#revdate, span#revremark {
}

div#footer {
  font-size: small;
  border-top: 2px solid silver;
  padding-top: 0.5em;
  margin-top: 4.0em;
}
div#footer-text {
  float: left;
  padding-bottom: 0.5em;
}
div#footer-badges {
  float: right;
  padding-bottom: 0.5em;
}

div#preamble {
  margin-top: 1.5em;
  margin-bottom: 1.5em;
}
div.tableblock, div.imageblock, div.exampleblock, div.verseblock,
div.quoteblock, div.literalblock, div.listingblock, div.sidebarblock,
div.admonitionblock {
  margin-top: 1.0em;
  margin-bottom: 1.5em;
}
div.admonitionblock {
  margin-top: 2.0em;
  margin-bottom: 2.0em;
  margin-right: 10%;
  color: #606060;
}

div.content { /* Block element content. */
  padding: 0;
}

/* Block element titles. */
div.title, caption.title {
  color: #527bbd;
  font-weight: bold;
  text-align: left;
  margin-top: 1.0em;
  margin-bottom: 0.5em;
}
div.title + * {
  margin-top: 0;
}

td div.title:first-child {
  margin-top: 0.0em;
}
div.content div.title:first-child {
  margin-top: 0.0em;
}
div.content + div.title {
  margin-top: 0.0em;
}

div.sidebarblock > div.content {
  background: #ffffee;
  border: 1px solid #dddddd;
  border-left: 4px solid #f0f0f0;
  padding: 0.5em;
}

div.listingblock > div.content {
  border: 1px solid #dddddd;
  border-left: 5px solid #f0f0f0;
  background: #f8f8f8;
  padding: 0.5em;
}

div.quoteblock, div.verseblock {
  padding-left: 1.0em;
  margin-left: 1.0em;
  margin-right: 10%;
  border-left: 5px solid #f0f0f0;
  color: #777777;
}

div.quoteblock > div.attribution {
  padding-top: 0.5em;
  text-align: right;
}

div.verseblock > pre.content {
  font-family: inherit;
  font-size: inherit;
}
div.verseblock > div.attribution {
  padding-top: 0.75em;
  text-align: left;
}
/* DEPRECATED: Pre version 8.2.7 verse style literal block. */
div.verseblock + div.attribution {
  text-align: left;
}

div.admonitionblock .icon {
  vertical-align: top;
  font-size: 1.1em;
  font-weight: bold;
  text-decoration: underline;
  color: #527bbd;
  padding-right: 0.5em;
}
div.admonitionblock td.content {
  padding-left: 0.5em;
  border-left: 3px solid #dddddd;
}

div.exampleblock > div.content {
  border-left: 3px solid #dddddd;
  padding-left: 0.5em;
}

div.imageblock div.content { padding-left: 0; }
span.image img { border-style: none; }
a.image:visited { color: white; }

dl {
  margin-top: 0.8em;
  margin-bottom: 0.8em;
}
dt {
  margin-top: 0.5em;
  margin-bottom: 0;
  font-style: normal;
  color: navy;
}
dd > *:first-child {
  margin-top: 0.1em;
}

ul, ol {
    list-style-position: outside;
}
ol.arabic {
  list-style-type: decimal;
}
ol.loweralpha {
  list-style-type: lower-alpha;
}
ol.upperalpha {
  list-style-type: upper-alpha;
}
ol.lowerroman {
  list-style-type: lower-roman;
}
ol.upperroman {
  list-style-type: upper-roman;
}

div.compact ul, div.compact ol,
div.compact p, div.compact p,
div.compact div, div.compact div {
  margin-top: 0.1em;
  margin-bottom: 0.1em;
}

div.tableblock > table {
  border: 3px solid #527bbd;
}
thead, p.table.header {
  font-weight: bold;
  color: #527bbd;
}
tfoot {
  font-weight: bold;
}
td > div.verse {
  white-space: pre;
}
p.table {
  margin-top: 0;
}
/* Because the table frame attribute is overriden by CSS in most browsers. */
div.tableblock > table[frame="void"] {
  border-style: none;
}
div.tableblock > table[frame="hsides"] {
  border-left-style: none;
  border-right-style: none;
}
div.tableblock > table[frame="vsides"] {
  border-top-style: none;
  border-bottom-style: none;
}


div.hdlist {
  margin-top: 0.8em;
  margin-bottom: 0.8em;
}
div.hdlist tr {
  padding-bottom: 15px;
}
dt.hdlist1.strong, td.hdlist1.strong {
  font-weight: bold;
}
td.hdlist1 {
  vertical-align: top;
  font-style: normal;
  padding-right: 0.8em;
  color: navy;
}
td.hdlist2 {
  vertical-align: top;
}
div.hdlist.compact tr {
  margin: 0;
  padding-bottom: 0;
}

.comment {
  background: yellow;
}

.footnote, .footnoteref {
  font-size: 0.8em;
}

span.footnote, span.footnoteref {
  vertical-align: super;
}

#footnotes {
  margin: 20px 0 20px 0;
  padding: 7px 0 0 0;
}

#footnotes div.footnote {
  margin: 0 0 5px 0;
}

#footnotes hr {
  border: none;
  border-top: 1px solid silver;
  height: 1px;
  text-align: left;
  margin-left: 0;
  width: 20%;
  min-width: 100px;
}

div.colist td {
  padding-right: 0.5em;
  padding-bottom: 0.3em;
  vertical-align: top;
}
div.colist td img {
  margin-top: 0.3em;
}

@media print {
  div#footer-badges { display: none; }
}

div#toc {
  margin-bottom: 2.5em;
}

div#toctitle {
  color: #527bbd;
  font-size: 1.1em;
  font-weight: bold;
  margin-top: 1.0em;
  margin-bottom: 0.1em;
}

div.toclevel1, div.toclevel2, div.toclevel3, div.toclevel4 {
  margin-top: 0;
  margin-bottom: 0;
}
div.toclevel2 {
  margin-left: 2em;
  font-size: 0.9em;
}
div.toclevel3 {
  margin-left: 4em;
  font-size: 0.9em;
}
div.toclevel4 {
  margin-left: 6em;
  font-size: 0.9em;
}

</style>
<script type="text/javascript">
/*<![CDATA[*/
window.onload = function(){asciidoc.footnotes();}
var asciidoc = {  // Namespace.

/////////////////////////////////////////////////////////////////////
// Table Of Contents generator
/////////////////////////////////////////////////////////////////////

/* Author: Mihai Bazon, September 2002
 * http://students.infoiasi.ro/~mishoo
 *
 * Table Of Content generator
 * Version: 0.4
 *
 * Feel free to use this script under the terms of the GNU General Public
 * License, as long as you do not remove or alter this notice.
 */

 /* modified by Troy D. Hanson, September 2006. License: GPL */
 /* modified by Stuart Rackham, 2006, 2009. License: GPL */

// toclevels = 1..4.
toc: function (toclevels) {

  function getText(el) {
    var text = "";
    for (var i = el.firstChild; i != null; i = i.nextSibling) {
      if (i.nodeType == 3 /* Node.TEXT_NODE */) // IE doesn't speak constants.
        text += i.data;
      else if (i.firstChild != null)
        text += getText(i);
    }
    return text;
  }

  function TocEntry(el, text, toclevel) {
    this.element = el;
    this.text = text;
    this.toclevel = toclevel;
  }

  function tocEntries(el, toclevels) {
    var result = new Array;
    var re = new RegExp('[hH]([2-'+(toclevels+1)+'])');
    // Function that scans the DOM tree for header elements (the DOM2
    // nodeIterator API would be a better technique but not supported by all
    // browsers).
    var iterate = function (el) {
      for (var i = el.firstChild; i != null; i = i.nextSibling) {
        if (i.nodeType == 1 /* Node.ELEMENT_NODE */) {
          var mo = re.exec(i.tagName);
          if (mo && (i.getAttribute("class") || i.getAttribute("className")) != "float") {
            result[result.length] = new TocEntry(i, getText(i), mo[1]-1);
          }
          iterate(i);
        }
      }
    }
    iterate(el);
    return result;
  }

  var toc = document.getElementById("toc");
  var entries = tocEntries(document.getElementById("content"), toclevels);
  for (var i = 0; i < entries.length; ++i) {
    var entry = entries[i];
    if (entry.element.id == "")
      entry.element.id = "_toc_" + i;
    var a = document.createElement("a");
    a.href = "#" + entry.element.id;
    a.appendChild(document.createTextNode(entry.text));
    var div = document.createElement("div");
    div.appendChild(a);
    div.className = "toclevel" + entry.toclevel;
    toc.appendChild(div);
  }
  if (entries.length == 0)
    toc.parentNode.removeChild(toc);
},


/////////////////////////////////////////////////////////////////////
// Footnotes generator
/////////////////////////////////////////////////////////////////////

/* Based on footnote generation code from:
 * http://www.brandspankingnew.net/archive/2005/07/format_footnote.html
 */

footnotes: function () {
  var cont = document.getElementById("content");
  var noteholder = document.getElementById("footnotes");
  var spans = cont.getElementsByTagName("span");
  var refs = {};
  var n = 0;
  for (i=0; i<spans.length; i++) {
    if (spans[i].className == "footnote") {
      n++;
      // Use [\s\S] in place of . so multi-line matches work.
      // Because JavaScript has no s (dotall) regex flag.
      note = spans[i].innerHTML.match(/\s*\[([\s\S]*)]\s*/)[1];
      noteholder.innerHTML +=
        "<div class='footnote' id='_footnote_" + n + "'>" +
        "<a href='#_footnoteref_" + n + "' title='Return to text'>" +
        n + "</a>. " + note + "</div>";
      spans[i].innerHTML =
        "[<a id='_footnoteref_" + n + "' href='#_footnote_" + n +
        "' title='View footnote' class='footnote'>" + n + "</a>]";
      var id =spans[i].getAttribute("id");
      if (id != null) refs["#"+id] = n;
    }
  }
  if (n == 0)
    noteholder.parentNode.removeChild(noteholder);
  else {
    // Process footnoterefs.
    for (i=0; i<spans.length; i++) {
      if (spans[i].className == "footnoteref") {
        var href = spans[i].getElementsByTagName("a")[0].getAttribute("href");
        href = href.match(/#.*/)[0];  // Because IE return full URL.
        n = refs[href];
        spans[i].innerHTML =
          "[<a href='#_footnote_" + n +
          "' title='View footnote' class='footnote'>" + n + "</a>]";
      }
    }
  }
}

}
/*]]>*/
</script>
</head>
<body class="article">
<div id="header">
<h1>樊城之战</h1>
</div>
<div id="content">
<div id="preamble">
<div class="sectionbody">
<div class="listingblock">
<div class="title"><br>身份配置</div>
<div class="content">
<pre><tt>主公：关羽
忠臣：华佗(旧版)
反贼：曹仁、SP庞德、徐晃
内奸：吕蒙</tt></pre>
</div></div>
<div class="listingblock">
<div class="title">行动顺序</div>
<div class="content">
<pre><tt>6人局随机位置</tt></pre>
</div></div>
<div class="listingblock">
<div class="title">初始装备</div>
<div class="content">
<pre><tt>关羽：青龙偃月刀，赤兔马
华佗：骅骝
曹仁：仁王盾</tt></pre>
</div></div>
</div>
</div>
<div class="sect1">
<h2 id="_">剧情</h2><br>
<div class="sectionbody">
<div class="sidebarblock">
<div class="content">
<div class="title">曹仁支援</div>
<div class="paragraph"><p>出牌阶段限两次。曹仁可以将一张基本牌交给徐晃或SP庞德。</p></div>
</div></div>
<div class="sidebarblock">
<div class="content">
<div class="title">抬榇战关羽</div>
<div class="quoteblock">
<div class="content">德拜谢回家，令匠人造一木榇。次日，请诸友赴席，列榇于堂。众亲友见之，皆惊问曰：“将军出师，何用此不祥之物？”德举杯谓亲友曰：“吾受魏王厚恩，誓以死报。今去樊城与关某决战，我若不能杀彼，必为彼所杀；即不为彼所杀，我亦当自杀。故先备此榇，以示无空回之理。”众皆嗟叹。</div>
<div class="attribution">
&#8212; 三国演义第74回
</div></div>
<div class="paragraph"><p><font color="green"><b>阶段技。</b></font>出牌阶段，SP庞德可以失去1点体力，视为对关羽使用一张【决斗】，此【决斗】不能被【无懈可击】响应。以此法使用的【决斗】结算时，关羽每次须打出两张【杀】。关羽杀死SP庞德无奖励牌。</p></div>
</div></div>
<div class="sidebarblock">
<div class="content">
<div class="title">水淹七军:</div>
<div class="quoteblock">
<div class="content">公曰：“非汝所知也。于禁七军不屯于广易之地，而聚于罾口川险隘之处；方今秋雨连绵，襄江之水必然泛涨；吾已差人堰住各处水口，待水发时，乘高就船，放水一淹，樊城罾口川之兵皆为鱼鳖矣。”关平拜服。</div>
<div class="attribution">
&#8212; 三国演义第74回
</div></div>
<div class="paragraph"><p><font color="red"><b>限定技。</b></font>出牌阶段，关羽可以弃置三张黑色手牌：若如此做，所有反贼弃置装备区的所有装备牌，然后选择一项：弃置两张手牌，或受到1点伤害。“水淹七军”发生后，曹仁手牌上限-1。</p></div>
</div></div>
<div class="sidebarblock">
<div class="content">
<div class="title">刮骨疗毒</div>
<div class="quoteblock">
<div class="content">佗曰：“当于静处立一标柱，上钉大环，请君侯将臂穿于环中，以绳系之，然后以被蒙其首。吾用尖刀割开皮肉，直至于骨，刮去骨上箭毒，用药敷之，以线缝其口，方可无事。但恐君侯惧耳。”公笑曰：“如此，容易！何用柱环？”</div>
<div class="attribution">
&#8212; 三国演义第75回
</div></div>
<div class="paragraph"><p><font color="blue"><b>锁定技。</b></font>每当华佗对关羽造成1点伤害时，关羽回复2点体力，然后华佗摸一张牌。</p></div>
</div></div>
<div class="sidebarblock">
<div class="content">
<div class="title">白衣渡江</div>
<div class="quoteblock">
<div class="content">权大悟，遂拜吕蒙为大都督，总制江东诸路军马；令孙皎在后接应粮草。蒙拜谢，点兵三万，快船八十余只，选会水者扮作商人，皆穿白衣，在船上摇橹，却将精兵伏于【舟冓】 【舟鹿】船中。&#8230; 约至二更，【舟冓】【舟鹿】中精兵齐出，将烽火台上官军缚倒，暗号一声，八十余船精兵俱起，将紧要去处墩台之军，尽行捉入船中，不曾走了一个。于是长驱大进，径取荆州，无人知觉。</div>
<div class="attribution">
&#8212; 三国演义第75回
</div></div>
<div class="paragraph"><p><font color="red"><b>限定技。</b></font>准备阶段开始时，吕蒙可以弃置两张装备区的牌：若如此做，吕蒙变身为神吕蒙且体力上限为3。若“水淹七军”已发生，曹仁手牌上限恢复。</p></div>
</div></div>
<div class="sidebarblock">
<div class="content">
<div class="title">长驱直入</div>
<div class="quoteblock">
<div class="content">操重赏三军，亲至四冢寨周围阅视，顾谓众将曰：“荆州兵围堑鹿角数重，徐公明深入其中，竟获全功。孤用兵三十余年，未敢长驱径入敌围。公明真胆识兼优者也！”众皆叹服。</div>
<div class="attribution">
&#8212; 三国演义第76回
</div></div>
<div class="paragraph"><p><font color="blue"><b>锁定技。</b></font>“水淹七军”发生后，徐晃与关羽的距离为1。</p></div>
</div></div>
<div class="sidebarblock">
<div class="content">
<div class="title">玉泉显圣</div>
<div class="quoteblock">
<div class="content">却说关公一魂不散，荡荡悠悠，直至一处，乃荆门州当阳县一座山，名为玉泉山。&#8230; 后往往于玉泉山显圣护民，乡人感其德，就于山顶上建庙，四时致祭。</div>
<div class="attribution">
&#8212; 三国演义第77回
</div></div>
<div class="paragraph"><p><font color="red"><b>限定技。</b></font>准备阶段开始时，若关羽当前体力值小于或等于2，则可以弃置其所有牌，然后摸三张牌，变身为神关羽，体力和体力上限为6。“抬榇战关羽”、“长驱直入”、“刮骨疗毒”依旧有效。</p></div>
</div></div>
</div>
</div>
</div>
<div id="footnotes"><hr /></div>
<div id="footer">
<div id="footer-text">
Last updated 2014-03-27 22:54:14 中国标准时间
</div>
</div>
</body>
</html>
