--hahaha

math.randomseed(tostring(os.time()):reverse():sub(1, 7)) --------设置时间种子

Pay = require "paysage" --加载价值模组


local LuaYizuo_skill = {}
LuaYizuo_skill.name = "LuaYizuo"
table.insert(sgs.ai_skills, LuaYizuo_skill)
LuaYizuo_skill.getTurnUseCard = function(self)
	if self.player:isKongcheng() then return end
	local mode = string.lower(global_room:getMode())
	if self.player:hasUsed("#LuaYizuo") or mode:find("04_1v3") then return end
	local cards = sgs.QList2Table(self.player:getHandcards())
	if self:canKillEnermyAtOnce(cards) then return end
	return sgs.Card_Parse("#LuaYizuo:.:")
end

local function findYizuoTarget(self, cards)
	local card, friend = self:getCardNeedPlayer(cards, false, 2)
	if friend then return friend end
	card, friend = self:getCardNeedPlayer(cards, false, 1)
	if friend then return friend end
	local friends = self.friends_noself
	self:sort(friends, "defense")
	if self:getOverflow(friends[1]) < 0 then return friends[1] end
	card, friend = self:getCardNeedPlayer(cards, false)
	if friend then return friend end
	return friends[1]
end
sgs.ai_skill_use_func["#LuaYizuo"] = function(cardT, use, self)
	local cards = sgs.QList2Table(self.player:getCards("he"))
	local cards_b = self:getTurnUse(true)
	self:sortByUseValue(cards, true)
	local target = findYizuoTarget(self, cards)
	if not target then return end
	local armorid = 0
	if self.player:getArmor() then armorid = self.player:getArmor():getId() end

	local togive = {}
	local function conTain(cardQ, cardsS)
		for _,c in ipairs(cardsS) do
			if cardQ:getEffectiveId() == c:getEffectiveId() then return true end
		end
	end
	local fq = self.player:getHandcardNum()
	for _, enemy in ipairs(self.enemies) do
		if enemy and enemy:hasWeapon("guding_blade") and not enemy:hasSkill("jueqing") and enemy:inMyAttackRange(self.player) then fq = fq - 1; break end
		if enemy and enemy:hasSkill("luayueshi") then fq = fq - 1; break end
		if enemy and enemy:hasSkill("lualihe") then fq = fq - 1; break end
	end

	for _,c in ipairs(cards) do
		local bool_0 = (c:isKindOf("Indulgence") or c:isKindOf("SupplyShortage") or c:isKindOf("Snatch") or c:isKindOf("AmazingGrace")) and conTain(c, cards_b)
		local bool_1 = c:isKindOf("Slash") and conTain(c, cards_b)
		local bool_2 = c:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(c:getId()) == sgs.Player_PlaceEquip))
		local bool_3 = (c:isKindOf("EightDiagram") or c:isKindOf("RenwangShield") or c:isKindOf("Tengu"))
				and ((self.room:getCardPlace(c:getId()) == sgs.Player_PlaceEquip)
				or ((self.room:getCardPlace(c:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
		if not (bool_3 and self:isWeak(target)) and not (bool_2 and target:getWeapon()) and not bool_1 and not bool_0 and not (fq == 0 and (self.room:getCardPlace(c:getId()) == sgs.Player_PlaceHand))
			and not ((c:isKindOf("Jink") or c:isKindOf("Analeptic") or c:isKindOf("Peach")) and self:isWeak()) then
			if (self.room:getCardPlace(c:getId()) == sgs.Player_PlaceHand) then
				fq = fq - 1
			end
			table.insert(togive, c)
			if self:willSkipPlayPhase(target) and #togive > self:getOverflow(target) then break end
			if #togive > 2 - self:getOverflow(target) then self.room:writeToConsole("daiyousei enough");break end
		end
	end

	local cardsX = {}
	for _,c in ipairs(cards) do
		if not conTain(c, togive) then table.insert(cardsX, c) end
	end
	cards = cardsX

	if #togive == 0 then
		local xr = self:getCardsNum("Peach") + self:getCardsNum("Analeptic") + self:getCardsNum("Jink")
		if xr > 1 then
			for _,c in ipairs(cards) do
				if (c:isKindOf("Jink") or c:isKindOf("Analeptic") or c:isKindOf("Peach")) and not (c:isRed() and target:hasSkill("luaxiongshi")) then
					table.insert(togive, c)
				end
			end
		end
	end

	cardsX = {}
	for _,c in ipairs(cards) do
		if not conTain(c, togive) then table.insert(cardsX, c) end
	end
	cards = cardsX

	while #cards > 0 do
		local card, friend = self:getCardNeedPlayer(cards, false, 2)
		if friend and friend:objectName() == target:objectName() then
			table.insert(togive, card)
		else
			break
		end
		cardsX = {}
		for _,c in ipairs(cards) do
			if not conTain(c, togive) then table.insert(cardsX, c) end
		end
		cards = cardsX
	end
	if #togive > 0 then
		cardsX = {}
		for _,c in ipairs(togive) do
			table.insert(cardsX, c:getEffectiveId())
		end
		use.card = sgs.Card_Parse("#LuaYizuo:".. table.concat(cardsX, "+") ..":")
		if use.to then
			use.to:append(target)
			return
		end
	end
end

sgs.ai_use_value.LuaYizuo = sgs.ai_use_value.RendeCard
sgs.ai_use_priority.LuaYizuo = sgs.ai_use_priority.RendeCard

sgs.ai_card_intention.LuaYizuo = sgs.ai_card_intention.RendeCard

sgs.ai_skill_invoke.LuaQiyuan2 = function(self, data)
	local daiyosei = self.room:findPlayerBySkillName("LuaQiyuan")
	if daiyosei and daiyosei:isAlive() and self:isFriend(daiyosei) then 
		local cirno
		for _, player in sgs.qlist(self.room:getAllPlayers()) do
			if player:getMark("@LuaYizuo") > 0 then
				cirno = player
			end
		end	
		local pcount = self:getCardsNum("Peach") + self:getCardsNum("Analeptic") 
		if (self.player:getMark("@LuaYizuo") > 0) and (not self:isWeak() or pcount > 0) then
			return true
		end 
		if cirno and cirno:isAlive() and not self:isWeak(cirno) then return false end 
		self:sort(self.friends, "defense")
		if self.friends[#self.friends]:objectName() ~= self.player:objectName() then return true end 
	end 
	return false 
end 



local Luasishu_skill={name="Luasishu"}
table.insert(sgs.ai_skills,Luasishu_skill) -- 注册义舍技能
Luasishu_skill.getTurnUseCard = function(self) --考虑留杀的情况，防嘲讽等
	return
end 

sgs.ai_skill_use["@Luasishu"] = function(self, prompt)
	local TrickTable = {}
	local Handcards = {}
	local handcards = self.player:getHandcards()
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		table.insert(Handcards, c)
	end
	for _,c in ipairs(Handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		if not self.player:isJilei(c) and c:isKindOf("TrickCard")  then 
			table.insert(TrickTable, c)
		end 
	end 
	if #TrickTable < 1 then 
		self.room:writeToConsole("司书卡无牌可用")
		return "."
	end
	local frieds = self.friends_noself
	if #frieds < 1 then 
		self.room:writeToConsole("司书卡无角色可用")
		return "."
	end
	self:sort(frieds, "defense")
	local Paixu_table = self:sortByUseValue(TrickTable)
	if not Paixu_table then return "." end
	if #Paixu_table < 1 then return "." end
	local current_phase = self.player:getMark("qiaobianPhase")
	local pay = self.player:getHandcardNum() -  self.player:getMaxCards()

	if current_phase == sgs.Player_Judge and not self.player:isSkipped(sgs.Player_Judge) then
		local p,phase = self:best_Koakuma_target_fun(frieds,current_phase)
		if phase and phase ~=0 and p~=0 then 
			self.room:writeToConsole("司书卡发动")
			return "#LuasishuCard:" .. Paixu_table[1]:getId()..":->"..p:objectName()
		end		
	elseif current_phase == sgs.Player_Draw and not self.player:isSkipped(sgs.Player_Draw) 
		and not self.player:hasSkills("tuxi|nostuxi") and not (Paixu_table[1]:isKindOf("ExNihilo") and pay <= -2) then
		local p,phase = self:best_Koakuma_target_fun(frieds,current_phase)
		if phase and phase ~=0 and p~=0 then 
			self.room:writeToConsole("司书卡发动")
			return "#LuasishuCard:" .. Paixu_table[1]:getId()..":->"..p:objectName()
		end		
	elseif current_phase == sgs.Player_Play and not self.player:isSkipped(sgs.Player_Play) then 
		local p,phase = self:best_Koakuma_target_fun(frieds,current_phase)
		if phase and phase ~=0 and p~=0 then 
			self.room:writeToConsole("司书卡发动")
			return "#LuasishuCard:" .. Paixu_table[1]:getId()..":->"..p:objectName()
		end	
	elseif current_phase == sgs.Player_Discard and not self.player:isSkipped(sgs.Player_Discard) then 
		if self:needBear() then return  end
		for _, enemy in ipairs(self.enemies) do
			if enemy:getHandcardNum() > enemy:getMaxCards() + 3  and not enemy:hasSkills("keji|conghui")  then 
				self.room:writeToConsole("司书卡坑人1")
				return "#LuasishuCard:" .. Paixu_table[1]:getId()..":->"..enemy:objectName()
			end 
			if (enemy:getHandcardNum() > enemy:getMaxCards() + 1) and Paixu_table[1][2] <= 6.5 and not enemy:hasSkills("keji|conghui") and self.player:getMark("@Luasishu") == 0 then 
				self.room:writeToConsole("司书卡坑人2")
				return "#LuasishuCard:" .. Paixu_table[1]:getId()..":->"..enemy:objectName()
			end 			
		end 
		if self.player:getHandcardNum() > self.player:getMaxCards() then
			local p,phase = self:best_Koakuma_target_fun(frieds,current_phase)
			if phase and phase ~=0 and p~=0 then 
				self.room:writeToConsole("司书卡发动")
				return "#LuasishuCard:" .. Paixu_table[1]:getId()..":->"..p:objectName()
			end				
		end 
	elseif current_phase == sgs.Player_Finish and not self.player:isSkipped(sgs.Player_Finish) then 
		local p,phase = self:best_Koakuma_target_fun(frieds,current_phase)
		if phase and phase ~=0 and p~=0 then 
			self.room:writeToConsole("司书卡发动")
			return "#LuasishuCard:" .. Paixu_table[1]:getId()..":->"..p:objectName()
		end			
	end 
	return "."
end 

sgs.ai_skill_invoke.luajingzhe = function(self, data)
	return true
end 
sgs.ai_skill_playerchosen.luajingzhe = function(self, targets)
	self.room:writeToConsole("惊蛰卡发动")
	local function Kazami_Yuka(num)
		local a=(0.04*num*num+0.1*num+0.3)
		self.room:writeToConsole("Kazami_Yuka=" .. a)
		return a
	end		
	local enemies1 = {}
	for _, enemy in ipairs(self.enemies) do
		if self:damageIsEffective(enemy, sgs.DamageStruct_Normal, self.player) then
			table.insert(enemies1, enemy)
		end
	end
	local players = {}
	for _, _player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if not self:isFriend(_player) then
			table.insert(players, _player)
		end
	end			
	local function getSlashCount(spplayer)
		local i=0
		local hc = spplayer:getHandcards()
		for _, cd in sgs.qlist(hc) do
			if cd:objectName() == "slash" then
				i=i+1
			end
		end
		return i
	end
	if math.random() > 0.5 then 
		self:sort(enemies1, "defense") 
		self:sort(players, "defense") 
	else
		self:sort(enemies1, "handcard") 
		self:sort(players, "defense") 
	end 
	local target = self.room:getTag("luajingzheTarget"):toPlayer()
	if target then 
		self.room:writeToConsole("惊蛰卡有目标1")
		local slashcount = getSlashCount(target)
		local slashcount2 = getSlashCount(self.player)
		local snum
		if #enemies1>0 then 
			snum = enemies1[1]:getHandcardNum() 
		else
			snum = players[1]:getHandcardNum() 
		end 	
		snum2 = target:getHandcardNum() 
		local rcx = snum - slashcount
		local rcx2 = snum2 - slashcount2
		local sprcx = 0
		if rcx > 0 then 
			sprcx=Kazami_Yuka(rcx) --风见幽香拟合公式
			if target:objectName() == self.player:objectName() then sprcx = math.pow(sprcx, 1.5) end 
			
			if #enemies1>0 then 
				if enemies1[1]:getHp() <=1 and sprcx > 0 then sprcx=0.1 end 
			else
				if players[1]:getHp() <=1 and sprcx > 0 then sprcx=0.2 end 
			end 
		end 	
		sprcx=1-sprcx
		self.room:writeToConsole("rcx=" .. rcx)
		self.room:writeToConsole("sprcx=" .. sprcx)
		if target:objectName() == self.player:objectName() or (self:isFriend(target) and #enemies1>0) then		
			if target:hasSkill("wushuang") or (math.random() < sprcx) then 
				self.room:writeToConsole("惊蛰卡自己人打非自己人")
				if #enemies1>0 then return enemies1[1] end 
				if #players>0 then return players[1] end 
			end 
		end 
		if not self:isEnemy(target) and #enemies1==0 then
			self.room:writeToConsole("惊蛰卡非自己人不乱打")
			return
		end 
		if not self:isFriend(target) and #enemies1>0 then 
			self.room:writeToConsole("惊蛰卡敌人内战")
			if target:objectName() ~= enemies1[1]:objectName() then return enemies1[1] end 
			if enemies1[2] then return enemies1[2] end 
			for _, _player2 in ipairs(players) do
				if _player2:objectName() ~= target:objectName() then 
					if _player2:objectName() ~= self.player:objectName() then 
						return _player2 
					end 
				end 
			end 
		end 
		if self:isEnemy(target) and (math.random() > Kazami_Yuka(rcx2)) then return self.player end 
		return
	end 
end 

sgs.ai_skill_invoke.luaxinsheng = function(self, data)
    if (self.player:getHp() < 2) then return false end
	for _, friend in ipairs(self.friends_noself) do
		if friend:isWounded()  then
			return true
		end
	end
end

sgs.ai_skill_use["@@luaxinsheng"] = function(self, prompt)
	self:updatePlayers(true)
    self:sort(self.friends_noself, "hp")
	for _, friend in ipairs(self.friends_noself) do
        if friend:isWounded() then 
			if self.player:getLostHp() == 1 and friend:getHp() <= self.player:getHp() and self.player:getSeat()~= friend:getSeat()
				and not self:needToLoseHp(friend, self.player, false, false, true) then
				return "#luaxinsheng:.:->" .. friend:objectName()
			elseif friend:getHp() < self.player:getHp() and not self:needToLoseHp(friend, self.player, false, true, true) then
				return "#luaxinsheng:.:->" .. friend:objectName()
			end
		end
	end	
    return "."
end



sgs.ai_card_intention.luaxinsheng = - 100
sgs.dynamic_value.benefit["#luaxinsheng"] = true


sgs.ai_card_intention.luayigong = function(self, card, from, tos)
	if not tos[1]:hasSkill("luashenbao") then
		sgs.updateIntention(from, tos[1], -40)
	end
end

--考虑这个牌序，如果使用南万而打算卸队友藤甲，那么应该优先先把南万用了
local luayigong_skill = {} -- 初始化 LuaShengong_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 LuaShengong_skill 只是为了出于习惯
luayigong_skill.name="luayigong" -- 设置 name
table.insert(sgs.ai_skills, luayigong_skill) -- 把这个表加入到 sgs.ai_skills 中   self:HasGou(false, player)
luayigong_skill.getTurnUseCard = function(self)
	if self.player:getEquips() and not self.player:getEquips():isEmpty() and self.player:usedTimes("#luayigong") < 2 then
		return sgs.Card_Parse("#luayigong:.:")
	end 
end 

local function luayigongP(self)
	local qlistA = sgs.IntList()
	local equips = sgs.QList2Table(self.player:getEquips())
	for _, equip in ipairs(equips) do
		qlistA:append(equip:getId())
	end
	local target = self:Nitori(qlistA, true, true)
	if target then return target end
	self.room:writeToConsole("bianbian test failed")
	return
end
sgs.ai_skill_use_func["#luayigong"] = function(card, use, self)
	local friend = luayigongP(self)
	if friend then 
		use.card = sgs.Card_Parse("#luayigong:.:")
		if use.to then 
			local opo = sgs.SPlayerList()
			opo:append(friend)
			use.to = opo
			return
		end		
	end 

end 

sgs.ai_skill_cardchosen.luayigong = function(self, who, flags)
	local target = self.room:getTag("luayigongTarget"):toPlayer()
	if flags == "e" then
		local qlistA = sgs.IntList()
		local equips = sgs.QList2Table(self.player:getEquips())
		for _, equip in ipairs(equips) do
			qlistA:append(equip:getId())
		end
		local card = self:Nitori(qlistA, true, true, true, true)
		if card and not self:getSameEquip(card, target) then
			return card:getEffectiveId()
		end
		for _, equipi in sgs.qlist(self.player:getEquips()) do
			local equip_index = -1
			local equip = equipi:getRealCard():toEquipCard()
			equip_index = equip:location()
			if equip_index ~= -1 then
				if not target:getEquip(equip_index) then
					return equip:getId()
				end				
			end 
		end 		
	end
end

sgs.ai_skill_askforag.luaxinjiang = function(self, card_ids)
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do --如果有读者看到这里，好好思考一下，为什么不是对队友搜索而是对全局搜索，我们留作习题请读者自证。2020年3月5日18:40:37
		if p:hasSkill("LuaShanguang") then
			for _, id in ipairs(card_ids) do
				local card = sgs.Sanguosha:getCard(id)
				if card:isKindOf("Axe") then
					return id
				end
			end
		end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("luatianzhao") then
			for _, id in ipairs(card_ids) do
				local card = sgs.Sanguosha:getCard(id)
				if card:isKindOf("Crossbow") then
					return id
				end
			end
		end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("luayexiao") then
			for _, id in ipairs(card_ids) do
				local card = sgs.Sanguosha:getCard(id)
				if card:isKindOf("GudingBlade") then
					return id
				end
			end
		end
	end
	for _, p in sgs.qlist(self.room:getAlivePlayers()) do
		if p:hasSkill("lualinglan") then
			for _, id in ipairs(card_ids) do
				local card = sgs.Sanguosha:getCard(id)
				if card:isKindOf("SilverLion") then
					return id
				end
			end
		end
	end
	for _, id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(id)
		if card:isKindOf("Wanbaochui") then
			return id
		end
	end
	for _, id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(id)
		if not self:getSameEquip(card) then
			return id
		end
	end
end

local function findTQCard(self, card_0)
	if self.player:isCardLimited(card_0, sgs.Card_MethodDiscard) then return false end
	local x = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
	local bool_k = (self.player:getHp() <= 2 or (self.player:getHandcardNum() <= 3 and x <= 1))
	if (card_0:isKindOf("Peach") or card_0:isKindOf("ExNihilo")) and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
		return false
	end
	if (card_0:isKindOf("Indulgence") or card_0:isKindOf("SupplyShortage")) and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
		return false
	end
	if (card_0:isKindOf("Duel") or card_0:isKindOf("IronChain")) and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
		return false
	end
	if card_0:isKindOf("Snatch") or card_0:isKindOf("Dismantlement") then
		local use = { isDummy = true }
		self:useCardSnatchOrDismantlement(card_0, use)
		if use.card then return false end
	end
	if (card_0:isKindOf("AOE") and self:getAoeValue(card_0) > 35) then return false end
	if card_0:isKindOf("Lightning") and not self:willUseLightning(card_0) then return true end
	local bool_2 = card_0:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(card_0:getId()) == sgs.Player_PlaceEquip))
	local bool_3 = (card_0:isKindOf("EightDiagram") or card_0:isKindOf("RenwangShield") or card_0:isKindOf("Tengu"))
			and ((self.room:getCardPlace(card_0:getId()) == sgs.Player_PlaceEquip)
			or ((self.room:getCardPlace(card_0:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
	if (bool_2 or bool_3) then return false end
	if card_0:isKindOf("Slash") then return true end
	if (not (card_0:isKindOf("Jink") and bool_k) and not (isCard("Peach", card_0, self.player) and self.player:isWounded())) then
		return true
	end

	for _, askill in sgs.qlist(self.player:getVisibleSkillList(true)) do
		local callback = sgs.ai_cardneed[askill:objectName()]
		if type(callback)=="function" and callback(self.player, card_0, self) then
			return false
		end
	end
	return true
end
sgs.ai_skill_discard.luatianqin = function(self, discard_num, min_num, optional, include_equip)
	local tsukumo = self.room:findPlayerBySkillName("luatianqin")
	if #self.enemies == 0 then return "." end
	if (not tsukumo) or (not tsukumo:isAlive()) then return "." end
	if not self:isFriend(tsukumo) then return "." end

	local shikieiki = self.room:findPlayerBySkillName("luapanjue")
	if shikieiki and self:isEnemy(shikieiki) and not shikieiki:isKongcheng() then return "." end

	if self:needToThrowArmor() then return self.player:getArmor():getId() end
	for _, card_0 in sgs.list(self.player:getHandcards()) do
		if findTQCard(self, card_0) then return card_0:getId() end
	end

	if self:getFinalRetrial(self.player) == 1 then
		if self.player:getOffensiveHorse() then return self.player:getOffensiveHorse():getId() end
		if self.player:getDefensiveHorse() then return self.player:getDefensiveHorse():getId() end
		if not self.player:isKongcheng() then
			local handcard = sgs.QList2Table(self.player:getHandcards())
			self:sortByKeepValue(handcard)
			return handcard[1]:getId()
		end
	end

	if self.player:getOffensiveHorse() then return self.player:getOffensiveHorse():getId() end
	if self.player:getDefensiveHorse() then return self.player:getDefensiveHorse():getId() end
	return "."
end
sgs.ai_skill_playerchosen.luatianqin = function(self, targets)
	local enermy = self.enemies
	self:sort(enermy, "defense")
	for _, to in ipairs(enermy) do
		if self:damageIsEffective(to, sgs.DamageStruct_Thunder, self.player) then
			return to
		end
	end
end
sgs.ai_skill_invoke.luatianqin = function(self, data)
	local tsukumo = self.room:findPlayerBySkillName("luatianqin")
	if self:isFriend(tsukumo) then return true end
end
sgs.ai_skill_invoke.luaxinjiang = true
sgs.ai_choicemade_filter.skillInvoke.luatianqin = function(self, player, promptlist)
	local tsukumo = self.room:findPlayerBySkillName("luatianqin")
	if promptlist[3] == "yes" then
		sgs.updateIntention(player, tsukumo, -20)
	end
end

--我要写吐了
local function MiyaoPanding(self, a)
	return a:getLostHp() >= self.player:getHp()
end
local function findmiyaoplayer(self)
	local enermy = self.enemies
	self:sort(enermy, "defense")
	for _, target in ipairs(enermy) do
		if target:getLostHp() == 1 and MiyaoPanding(self, target) and not target:hasSkills("guixin|yiji|jieming|neoganglie|ganglie|vsganglie|nosganglie|fangzhu|langgu|quanji|" ..
				"chengxiang|benyu|huituo|qianhuan|nosyiji|yaoming|ytchengxiang|chouce|ol_guixin|bushi|yuce|Luahuaimeng|luayuekuang|baobian" ..
				"LuaShenyin|luaxinshi|luayuetuan|luagongcheng|luafengmo|luayequ|luajinlun|Luayuelong") then
			return target
		end
	end

	local friends = self.friends
	self:sort(friends, "defense2")
	for _, target in ipairs(friends) do
		if target:getLostHp() > 1 and MiyaoPanding(self, target) and target:hasSkills("guixin|yiji|jieming|neoganglie|ganglie|vsganglie|nosganglie|fangzhu|langgu|quanji|" ..
				"chengxiang|benyu|huituo|qianhuan|nosyiji|yaoming|ytchengxiang|chouce|ol_guixin|bushi|yuce|Luahuaimeng|luayuekuang|baobian" ..
				"LuaShenyin|luaxinshi|luayuetuan|luagongcheng|luafengmo|luayequ|luajinlun|Luayuelong") and target:getHp() == 1 then
			return target
		end
	end

	for _, target in ipairs(enermy) do
		if target:getLostHp() == 2 and MiyaoPanding(self, target) and not target:hasSkills("guixin|yiji|jieming|neoganglie|ganglie|vsganglie|nosganglie|fangzhu|langgu|quanji|" ..
				"chengxiang|benyu|huituo|qianhuan|nosyiji|yaoming|ytchengxiang|chouce|ol_guixin|bushi|yuce|Luahuaimeng|luayuekuang|baobian" ..
				"LuaShenyin|luaxinshi|luayuetuan|luagongcheng|luafengmo|luayequ|luajinlun|Luayuelong")
			and not target:getMark("@luamiyao") >= target:getHp() then
			return target
		end
	end

	for _, target in ipairs(friends) do
		if target:getLostHp() > 1 and MiyaoPanding(self, target) and self:isWeak(target) then
			return target
		end
	end

end

local luamiyao_skill = {} -- 初始化 LuaShengong_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 LuaShengong_skill 只是为了出于习惯
luamiyao_skill.name = "luamiyao" -- 设置 name
table.insert(sgs.ai_skills, luamiyao_skill) -- 把这个表加入到 sgs.ai_skills 中   self:HasGou(false, player)
luamiyao_skill.getTurnUseCard = function(self)
	if self.player:usedTimes("#luamiyao") > 1 then return end
	if self.player:isKongcheng() then return end
	if findmiyaoplayer(self) then
		return sgs.Card_Parse("#luamiyao:.:")
	end
end
sgs.ai_skill_use_func["#luamiyao"] = function(card, use, self)
	local handcard = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(handcard)
	local target = findmiyaoplayer(self)
	if self.player:isKongcheng() then return end
	if not target then return end
	local card_0 = handcard[1]
	if self:isFriend(target) and card_0:isKindOf("Peach") and not target:hasSkills("guixin|yiji|jieming|neoganglie|ganglie|vsganglie|nosganglie|fangzhu|langgu|quanji|" ..
			"chengxiang|benyu|huituo|qianhuan|nosyiji|yaoming|ytchengxiang|chouce|ol_guixin|bushi|yuce|Luahuaimeng|luayuekuang|baobian" ..
			"LuaShenyin|luaxinshi|luayuetuan|luagongcheng|luafengmo|luayequ|luajinlun|Luayuelong") then return end

	use.card = sgs.Card_Parse("#luamiyao:".. handcard[1]:getId() ..":" )
	if use.to then use.to:append(target) end
	return

end

sgs.ai_card_intention.luamiyao = function(self, card, from, tos)
    for _,to in ipairs(tos) do
        if to:getLostHp() == 1 then
            if not to:hasSkills("guixin|yiji|jieming|neoganglie|ganglie|vsganglie|nosganglie|fangzhu|langgu|quanji|" ..
                    "chengxiang|benyu|huituo|qianhuan|nosyiji|yaoming|ytchengxiang|chouce|ol_guixin|bushi|yuce|Luahuaimeng|luayuekuang|baobian" ..
                    "LuaShenyin|luaxinshi|luayuetuan|luagongcheng|luafengmo|luayequ|luajinlun|Luayuelong") then
                sgs.updateIntention(from, to, 60)
            end
        else
            if not (to:getLostHp() < 3 and to:getMaxHp() > 7) then sgs.updateIntention(from, to, -60) end
        end
    end
end
sgs.ai_use_priority.luamiyao = 2
sgs.ai_skill_invoke.luashengong = sgs.ai_skill_invoke.liegong  --如何决定技能是否发动的一个实例

sgs.ai_skill_invoke.luasijian = function(self, data)
	local target = data:toPlayer()
	return self:isFriend(target) and not self:isWeak()
end


sgs.ai_skill_cardask["@luashengong"] = function(self, data, pattern)
	if self.player:isKongcheng() then return "." end
	local use = data:toCardUse()
	local from = self.room:findPlayerBySkillName("luashengong")
	if not self:slashIsEffective(use.card, self.player, from) then return "." end
	local cards
	local black = {}
	local red = {}
	local allcards = sgs.QList2Table(self.player:getCards("h"))
	for _, card in ipairs(allcards) do
		if card:isBlack() then table.insert(black, card) end
	end
	for _, card in ipairs(allcards) do
		if card:isRed() then table.insert(red, card) end
	end

	if pattern == "." then
		cards = sgs.QList2Table(self.player:getHandcards())
	elseif pattern == ".black" then
		cards = black
	elseif pattern == ".red" then
		cards = red
	end
	self:sortByKeepValue(cards)
	local x = self:getCardsNum("Jink")
	if x < 1 then return "." end
	if #cards < 1 then return "." end
	local function pandin(card_0)
		if not self:isWeak() then
			for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skill:objectName()]
				if type(callback) == "function" and callback(self.player, card_0, self) then
					return false
				end
			end
		end
		if card_0:isKindOf("Peach") or card_0:isKindOf("ExNihilo") or (card_0:isKindOf("Analeptic") and self:isWeak()) then return false end
		if x < 2 and card_0:isKindOf("Jink") then return false end
		return true
	end
	if pandin(cards[1]) then return "$" .. cards[1]:getEffectiveId() end
	if #cards > 1 and pandin(cards[2]) then return "$" .. cards[2]:getEffectiveId() end
	return "."
end

sgs.ai_card_intention.luaguiqiao = function(self, card, from, tos)
	if from:getMark("luaguiqiao") == 2 then
		sgs.updateIntention(from, self.room:getCurrent(), -60)
	end 
end
sgs.ai_card_intention.luaxinshi = -40
sgs.ai_skill_use["@@luaxinshi"] = function(self, data, method) --没有考虑给牌
	--if needBlack or needRed then return "#luaxinshi:.:->" .. self.player:objectName() "+" end
	if #self.friends == 0 then return "#luaxinshi:.:->" .. self.player:objectName() end

	local cards = {}
	for i = 0, 4 do
		local cardX = sgs.Sanguosha:getCard(self.room:getDiscardPile():at(i))
		table.insert(cards, cardX)
	end
	local card, friend = self:getCardNeedPlayer(cards)
	if card then
		return "#luaxinshi:.:->" .. friend:objectName() .. "+" ..  self.player:objectName()
	end
	local friends = self.friends_noself
	if #friends > 0 then
		if math.random() > 0.5 then
			self:sort(friends, "defense")
		else
			self:sort(friends, "handcard")
		end
		return "#luaxinshi:.:->" .. friends[1]:objectName()  .. "+" .. self.player:objectName()
	end
	return "#luaxinshi:.:->" .. self.player:objectName()
end 

sgs.ai_skill_askforag.luaxinshi = function(self, card_ids)
	local cards = {}
	for i, card_id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(card_id)
		table.insert(cards, card)
	end
	self:sortByUseValue(cards)

	local xo = self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt()
	local black = {}
	local red = {}
	local allcards = sgs.QList2Table(self.player:getCards("he"))
	for _, card in ipairs(allcards) do
		if card:isBlack() then table.insert(black, card) end
	end
	for _, card in ipairs(allcards) do
		if card:isRed() then table.insert(red, card) end
	end
	local needRed = false
	local needBlack = false

	if self.player:getMark("guiqiaotime") ~= xo and #black < 1 then
		needBlack = true
	end
	if self.player:getMark("guiqiaotime2") ~= xo and #red < 1 then
		needRed = true
	end
	if #card_ids == 5 and (needRed or needBlack) then
		for i, card_id in ipairs(card_ids) do
			local card = sgs.Sanguosha:getCard(card_id)
			if card:isRed() and needRed then return card_id end
			if card:isBlack() and needRed then return card_id end
		end
	end

	return cards[1]:getId()
end
sgs.ai_damage_effect["luaxinshi"] = function(self, to, nature, from)
	if to:hasSkill("luaxinshi") then
		local card_ids = sgs.IntList()
		if self.room:getDiscardPile():isEmpty() then return false end
		for i = 0, 3 do
			card_ids:append(self.room:getDiscardPile():at(i))
			if self.room:getDiscardPile():length() < i + 1 then break end
		end
		local cards = {}
		for _,id in sgs.qlist(card_ids) do
			table.insert(cards, sgs.Sanguosha:getCard(id))
		end
		for _, card in ipairs(cards) do
			if card:isKindOf("Peach") then
				if to:getHp() > 1 then
					return true
				end
				if self:isEnemy(to) then
					for _, enemy in ipairs(self.enemies) do
						if getCardsNum("Peach", self.player) >= 1 then
							return true
						end
					end
				end
				if self:isFriend(to) then
					for _, friend in ipairs(self.friends) do
						if getCardsNum("Peach", self.player) >= 1 then
							return true
						end
					end
				end
			end
			if card:isKindOf("Analeptic") then
				if to:getHp() == 1 then
					if self:isEnemy(to) then
						for _, enemy in ipairs(self.enemies) do
							if getCardsNum("Peach", self.player) >= 1 then
								return true
							end
						end
					end
					if self:isFriend(to) then
						for _, friend in ipairs(self.friends) do
							if getCardsNum("Peach", self.player) >= 1 then
								return true
							end
						end
					end
				end
			end
		end
	end
end
sgs.ai_skill_use["@@luaguiqiao"] = function(self, data, method)
	if self.player:getMark("luaguiqiao") == 2 then 
		if not method then method = sgs.Card_MethodUse end

		local cards = sgs.QList2Table(self.player:getCards("h"))
		self:sortByKeepValue(cards)
		self:updatePlayers()
		self:sort(self.enemies, "defense")
		
		local ida1 = self.player:getMark("luaguiqiao9") - 1
		if (not ida1) or (ida1 < 1) then
			return "." 
		end
		local carda1 = sgs.Sanguosha:getCard(ida1)
		local promptlist = {}
		table.insert(promptlist, carda1:objectName())

		
		local class_name = promptlist[1]
		if #cards == 0 then return "." end
		local card = cards[1]


		local suit = card:getSuitString()
		local number = card:getNumberString()
		local card_id = card:getEffectiveId()
		local card_str = ("%s:moshi[%s:%s]=%d"):format(class_name, suit, number, card_id)
		local use_card = sgs.Card_Parse(card_str)
		if self.player:isCardLimited(use_card, sgs.Card_MethodUse) then return "." end
		local value_0 = self:getUseValue(use_card) - 0.75
		if (use_card:targetFixed()) then
			return use_card:toString()
		else
			if string.find(class_name, "slash") then
				for _,enemy in ipairs(self.enemies) do
					if self.room:getCurrent():canSlash(enemy, use_card, false) and not self:slashProhibit(nil, enemy, self.room:getCurrent()) and self.room:getCurrent():inMyAttackRange(enemy)
					and sgs.getDefenseSlash(enemy, self) < 6 and self:slashIsEffective(use_card, enemy) and sgs.isGoodTarget(enemy, self.enemies, self) then
						return use_card:toString() .. "->" .. enemy:objectName()
					end
				end
			end
			if use_card:isNDTrick() then
				if not (card:isKindOf("Analeptic") and self.player:getHp() == 1) and not card:isKindOf("Duel") and not card:isKindOf("SilverLion")
						and not card:isKindOf("RenwangShield") and not card:isKindOf("EightDiagram") then
					local dummyuse = { isDummy = true, to = sgs.SPlayerList() }
					self:useTrickCard(use_card, dummyuse)
					local targets = {}
					if not dummyuse.to:isEmpty() then
						for _, p in sgs.qlist(dummyuse.to) do
							table.insert(targets, p:objectName())
						end	
						return use_card:toString() .. "->" .. table.concat(targets, "+")
					end
				end 
			end
		end
	elseif self.player:getMark("luaguiqiao") == 1 then 
		local xo = self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt()
		if self.player:getMark("guiqiaotime2") == xo then return "." end 
		local current = self.room:getCurrentDyingPlayer()
		local reds = Pay.GetAllRCardsBySelf(self)
		local reds_2 = {}
		self:sortByKeepValue(reds)
		local x = current:getMaxHp()
		local y = 0
		for _, card in ipairs(reds) do
			table.insert(reds_2, card:getEffectiveId())
			y = y + 1
			if y >= x then break end
			if y >= 3 then break end
		end
		if current:objectName() == self.player:objectName() then 
			if #reds < 2 then return "." end
			local count1 = self:getCardsNum("Peach") + self:getCardsNum("Analeptic")
			if count1 > 0 then return "." end
			return "#luaguiqiao:"..table.concat(reds_2, "+")..":"
		elseif self:isFriend(current) then
			if #reds < 1 then 
				if math.random() < 0.13 then self.player:speak("我之所以不救你是因为怕你吃桃太快噎着") end 
				return "." 
			end 
			local count1 = self:getCardsNum("Peach") + getCardsNum("Peach", current)
			if (self.player:getHp() == 1) and (count1 < 2) then
				if math.random() < 0.13 then self.player:speak("我之所以不救你是因为怕你吃桃太快噎着") end 
				return "." 
			end
			return "#luaguiqiao:"..table.concat(reds_2, "+")..":"
		end 
	end 
end
sgs.ai_card_intention.luaguiqiao = -30
sgs.ai_skill_cardask["@luaguiqiaok"] = function(self, data, pattern, target) --没有考虑防空城或补牌的情况
	--先暂时这么考虑，条件是比较宽松的

	
	local current = self.room:getCurrent()
	if self:isEnemy(current) then return "." end 
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	local black = {}
	for _,card in ipairs(cards) do
		if card:isBlack() then table.insert(black, card:getId()) end 
	end 
	if #black < 1 then return "." end	
	local ida1 = current:getMark("luaguiqiao9") - 1
	if (not ida1) or (ida1 < 1) then return "." end	
	
	local card0 = sgs.Sanguosha:getCard(ida1)
	if card0:isKindOf("Analeptic") then return "." end
	if current:isCardLimited(card0, sgs.Card_MethodUse) then return "." end
	local ida2 = current:getMark("luaguiqiao8") - 1
	
	--self.room:writeToConsole(ida1)
	--self.room:writeToConsole(card0:objectName())
	
	if (not ida2) or (ida2 < 1) or (#black == 1) then
		if card0:isKindOf("ExNihilo") or card0:isKindOf("Duel") then 
			return "$" .. black[1]
		end 
		if card0:isKindOf("AOE") and self:getAoeValue(card0, current) >= 60 then 
			return "$" .. black[1]
		end 		
		if card0:isKindOf("GodSalvation") then 
			local value_o = self:godSalvationValue(card0)
			if value_o >= 15 then 
				return "$" .. black[1]
			end 
		end 		
	else
		return "$" .. black[1]
	end 
	
	return "."
end

sgs.ai_card_intention.luashouhu = -30


sgs.ai_view_as.luahuixuan = function(card, player, card_place)
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	local shouldUsePeach = true
	--for _, friend in ipairs(self.frieds) do
		--if friend:isWeak() then shouldUsePeach = false end 
	--end 
	if player:isWounded() then shouldUsePeach = false end  
	if card_place ~= sgs.Player_PlaceSpecial and (not card:isKindOf("Peach") or shouldUsePeach) and not card:hasFlag("using") and card:isKindOf("BasicCard")
		and card_place ~= sgs.Player_PlaceEquip then
		return ("slash:luahuixuan[%s:%s]=%d"):format(suit, number, card_id)
	end
end

local function findhuixuancard(self)
	local cards = self.player:getCards("h")
	cards = sgs.QList2Table(cards)
	local red_card
	self:sortByUseValue(cards, true)
	local slashX = sgs.Sanguosha:cloneCard("slash")
	local slash_v = self:getUseValue(slashX)
	
	local useAll = false
	self:sort(self.enemies, "defense")
	for _, enemy in ipairs(self.enemies) do
		if enemy:getHp() == 1 and not enemy:hasArmorEffect("eight_diagram") and self.player:distanceTo(enemy) <= self.player:getAttackRange() and self:isWeak(enemy)
			and getCardsNum("Jink", enemy, self.player) + getCardsNum("Peach", enemy, self.player) + getCardsNum("Analeptic", enemy, self.player) == 0 then
			useAll = true
			break
		end
	end 
	
	local disCrossbow = false
	if self:getCardsNum("Slash") < 2 or self.player:hasSkill("paoxiao") then
		disCrossbow = true
	end 
	
	if not red_card then 	
		for _, card in ipairs(cards) do
			if  (not isCard("Peach", card, self.player) and not useAll)
				and (not isCard("Crossbow", card, self.player) and not disCrossbow)
				and card:isKindOf("BasicCard")
				and (self:getUseValue(card) <= slash_v or inclusive or self.player:hasSkill("paoxiao") or self:getOverflow() > 0	--yun
					or sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_Residue, self.player, sgs.Sanguosha:cloneCard("slash")) > 0) then
				red_card = card
				break
			end
		end
	end 
	return red_card
end 
local luahuixuan_skill = {}
luahuixuan_skill.name = "luahuixuan"
table.insert(sgs.ai_skills, luahuixuan_skill)
luahuixuan_skill.getTurnUseCard = function(self, inclusive) 
	local red_card = findhuixuancard(self)
	if red_card then
		return sgs.Card_Parse("#luahuixuan:.:")
	end
end

sgs.ai_skill_use_func["#luahuixuan"] = function(card, use, self)
	local red_card = findleishicard(self)
	if not red_card then return end 
	self.room:writeToConsole("tojiko test")
	local slash = sgs.Sanguosha:cloneCard("slash", red_card:getSuit(), red_card:getNumber())
	slash:setSkillName("luahuixuan")
	slash:addSubcard(red_card:getEffectiveId())	
	local dummy_use = { isDummy = false , to = sgs.SPlayerList() }
	self:useBasicCard(slash, dummy_use)	
	if dummy_use.card and dummy_use.card:isKindOf("Slash") and dummy_use.to and dummy_use.to:length() > 0 then 
		self.room:writeToConsole("tojiko test 2")
		use.card = sgs.Card_Parse("#luahuixuan:" .. red_card:getId() ..":") 
		if use.to then 
			use.to = dummy_use.to 
			return
		end 		
	end 
end 

sgs.ai_use_priority.luahuixuan = 6 


sgs.ai_skill_cardask["luashouhuy"] = function(self)
	local targetP 
	for _, player in sgs.qlist(self.room:getAllPlayers()) do
		if player:hasFlag("luashouhuyx") then targetP = player;break end 		
	end 
	local targetT = self.room:getTag("luashouhuy"):toPlayer()
	
	local slashes = self:getCards("Slash")
	self:sortByDynamicUsePriority(slashes)
	if not slashes then
		if self.player:hasSkill("luashouhu") and math.random() < 0.1 then self.player:speak("这个时候指望你野爹救你来了？") end 
		return "." 
	end 
	if #slashes == 0 then 
		if self.player:hasSkill("luashouhu") and math.random() < 0.1 then self.player:speak("这个时候指望你野爹救你来了？") end 
		return "." 
	end 
	if (not self:isEnemy(targetP)) and self:isEnemy(targetT) then 
		return slashes[1]:toString() or "." 
	end 
	return "."
end 
 
 

sgs.ai_skill_use["@@lualingxi"] = function(self, prompt)

	local cards = {}
	for _, c in sgs.qlist(self.player:getHandcards()) do
		if c:isKindOf("BasicCard") then
			table.insert(cards, c)
		end
	end
	self:sortByKeepValue(cards)
	if #cards == 0 then return "." end 
	local phaseJ = (self.player:getPhase() == sgs.Player_Play or self.player:getPhase() == sgs.Player_Draw 
		or self.player:getPhase() == sgs.Player_Start or self.player:getPhase() == sgs.Player_Judge)
    if phaseJ and (not self.player:hasSkill("luahuixuan") or not self.player:hasSkill("luashouhu")) then
			return "#lualingxi:".. cards[1]:getId()  .. ":->" .. self.player:objectName()

	end
	 
	local card, friend = self:getCardNeedPlayer(cards, false, 2)	
	if card and friend and friend:objectName() ~= self.player:objectName() then   
		return "#lualingxi:".. cards[1]:getId()  .. ":->" .. friend:objectName()
	end
	
	local frieds = self.friends
	self:sort(frieds, "defenseSlash")
	for _, friendR in ipairs(frieds) do
		if not (#cards <= 1 and phaseJ) and not friendR:hasSkill("luahuixuan") and not friendR:hasSkill("luashouhu") then 
			return "#lualingxi:".. cards[1]:getId()  .. ":->" .. friendR:objectName()
		end 
	end  
	for _, friendR in ipairs(frieds) do
		if not (#cards <= 1 and phaseJ) and (not friendR:hasSkill("luahuixuan") or not friendR:hasSkill("luashouhu"))
			and self:getOverflow(friendR) <= 0 then 
			return "#lualingxi:".. cards[1]:getId()  .. ":->" .. friendR:objectName()
		end 
	end  
end 

local luamimeng_skill = {}
luamimeng_skill.name = "luamimeng"
table.insert(sgs.ai_skills, luamimeng_skill)
luamimeng_skill.getTurnUseCard = function(self, inclusive)
	if self.player:hasUsed("#luamimeng") then return false end
	local sagume = self.room:findPlayerBySkillName("luanizhou")
	if sagume and sagume:isAlive() and self:isFriend(sagume) then
		local sp = self.room:getCurrent():getMark("@luatianshii")
		local x = sagume:getMark("luanizhou")
		if sp == x - 1 then
			sgs.ai_use_priority.luamimeng = 10
		else
			sgs.ai_use_priority.luamimeng = 0
		end
	end
	local num = self:getCardsNum("Jink") 
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	if self:isWeak() and (num == 1) then 
		if self:getOverflow() > 0 then 
			local bool = false 
			local p = self:getOverflow()
			for i = 1, p do
				if cards[i]:isKindOf("Jink") then bool = true end 
			end 
			if not bool then return end 
		else
			return
		end 
	end 
	return sgs.Card_Parse("#luamimeng:.:") 
end 

sgs.ai_skill_use_func["#luamimeng"] = function(card, use, self)
	local cards = self:getCards("Jink")
	self:sortByKeepValue(cards)
	for _, jink in ipairs(cards) do
		local slash = sgs.Sanguosha:cloneCard("ex_nihilo", sgs.Card_SuitToBeDecided, -1)
		slash:addSubcard(jink)
		slash:deleteLater()
		local frieds = self.friends
		self:sort(frieds, "defense")
		for _, friend in ipairs(self.friends) do
			if not friend:isCardLimited(slash, sgs.Card_MethodUse)
				and not friend:isProhibited(friend, slash, friend:getSiblings())
				and self:isWeak(friend) and (self:getOverflow(friend) <= 0) then 		
				use.card = sgs.Card_Parse("#luamimeng:" .. jink:getId() ..":") 
				if use.to then use.to:append(friend) ; return end	
			end 				
		end 
		for _, friend in ipairs(self.friends) do
			if not friend:isCardLimited(slash, sgs.Card_MethodUse)
				and not friend:isProhibited(friend, slash, friend:getSiblings())
				and friend:hasSkills(sgs.cardneed_skill) and (self:getOverflow(friend) <= 0) then 		
				use.card = sgs.Card_Parse("#luamimeng:" .. jink:getId() ..":") 
				if use.to then use.to:append(friend) ; return end	
			end 				
		end 
		if self:isWeak() or ((self.player:getHandcardNum() <= 1) and (self:getOverflow() < 0))
			and not self.player:isCardLimited(slash, sgs.Card_MethodUse) and not self.player:isProhibited(self.player, slash, self.player:getSiblings()) then 
			use.card = sgs.Card_Parse("#luamimeng:" .. jink:getId() ..":") 
			if use.to then use.to:append(self.player) ; return end				
		end 
		for _, friend in ipairs(self.friends) do
			if not friend:isCardLimited(slash, sgs.Card_MethodUse)
				and not friend:isProhibited(friend, slash, friend:getSiblings())
				and friend:hasSkills(sgs.cardneed_skill) then 		
				use.card = sgs.Card_Parse("#luamimeng:" .. jink:getId() ..":") 
				if use.to then use.to:append(friend) ; return end	
			end 				
		end 		
		if  (self:getOverflow() < 0)
			and not self.player:isCardLimited(slash, sgs.Card_MethodUse) and not self.player:isProhibited(self.player, slash, self.player:getSiblings()) then 
			use.card = sgs.Card_Parse("#luamimeng:" .. jink:getId() ..":") 
			if use.to then use.to:append(self.player) ; return end				
		end 	
		for _, friend in ipairs(self.friends) do
			if not friend:isCardLimited(slash, sgs.Card_MethodUse)
				and not friend:isProhibited(friend, slash, friend:getSiblings()) then 		
				use.card = sgs.Card_Parse("#luamimeng:" .. jink:getId() ..":") 
				if use.to then use.to:append(friend) ; return end	
			end 				
		end 				
	end 
end 

sgs.ai_skill_playerchosen.luamimeng = function(self, targets)
	local enemies = self.enemies
	self:sort(enemies, "defense")
	for _, to in ipairs(enemies) do
		if self:getValuableCard(to, true) and not to:isKongcheng() then return to end
	end 
	for _, to in ipairs(enemies) do
        local flag = string.format("%s_%s_%s", "visible", to:objectName(), self.player:objectName())
		local q = 0
		for _, acard in sgs.qlist(to:getHandcards()) do
			if (acard:hasFlag("visible") or acard:hasFlag(flag)) and acard:isKindOf("Jink") then q = q + 1 end  	
		end 	
		if q > 0 and to:getHandcardNum() < 4 and not to:isKongcheng() then return to end
	end 
	for _, acard in sgs.qlist(self.player:getHandcards()) do
		if acard:isKindOf("Slash") or acard:isKindOf("SavageAssault") or acard:isKindOf("ArcheryAttack")
			or acard:isKindOf("Duel") or acard:isKindOf("Dismantlement") or acard:isKindOf("Indulgence")
				or acard:isKindOf("SupplyShortage") then 
			for _, to in ipairs(enemies) do --挣扎吧！
				if (self:isWeak(to) or (acard:isKindOf("Slash") and self:hasHeavySlashDamage(self.player, acard, self.player))) and not to:isKongcheng()
					and not (to:isCardLimited(acard, sgs.Card_MethodUse) or to:isProhibited(to, acard, to:getSiblings())) then return to end 
			end 
			for _, to in ipairs(enemies) do 
				if (to:getHandcardNum() == 1) and not (to:isCardLimited(acard, sgs.Card_MethodUse) or to:isProhibited(to, acard, to:getSiblings())) then return to end 
			end 					
		end 
	end 
	local frieds = self.friends_noself
	
	for _, acard in sgs.qlist(self.player:getHandcards()) do
		if acard:isKindOf("Slash") or acard:isKindOf("SavageAssault") or acard:isKindOf("ArcheryAttack") or acard:isKindOf("IronChain")
			or acard:isKindOf("Duel") or acard:isKindOf("Dismantlement") or acard:isKindOf("Indulgence")
				or acard:isKindOf("SupplyShortage") then 
			for _, to in ipairs(enemies) do 
				if (to:getHandcardNum() <= 3) and not to:isKongcheng() and not (to:isCardLimited(acard, sgs.Card_MethodUse) or to:isProhibited(to, acard, to:getSiblings())) then return to end
			end 					
		end 
	end 

	for _, acard in sgs.qlist(self.player:getHandcards()) do
		for _, to in ipairs(enemies) do 
			if (to:isCardLimited(acard, sgs.Card_MethodUse) or to:isProhibited(to, acard, to:getSiblings())) and not to:isKongcheng() then return to end
		end 
	end 
	
	for _, acard in sgs.qlist(self.player:getHandcards()) do
		if acard:isKindOf("Slash") or acard:isKindOf("SavageAssault") or acard:isKindOf("ArcheryAttack") or acard:isKindOf("Jink") or acard:isKindOf("sakura")
			or acard:isKindOf("Duel") or acard:isKindOf("Dismantlement") or acard:isKindOf("Indulgence") or acard:isKindOf("Nullification")
				or acard:isKindOf("SupplyShortage") then 
			for _, to in ipairs(enemies) do 
				if (to:getHandcardNum() <= 1) and not to:isKongcheng() then return to end
			end 					
		end 
	end 
	
	for _, acard in sgs.qlist(self.player:getHandcards()) do
		if acard:isKindOf("Slash") or acard:isKindOf("SavageAssault") or acard:isKindOf("ArcheryAttack") or acard:isKindOf("IronChain")
			or acard:isKindOf("Duel") or acard:isKindOf("Dismantlement") or acard:isKindOf("Indulgence")
				or acard:isKindOf("SupplyShortage") then 
			for _, to in ipairs(enemies) do 
				if not (to:isCardLimited(acard, sgs.Card_MethodUse) or to:isProhibited(to, acard, to:getSiblings())) and not to:isKongcheng() then return to end
			end 					
		end 
	end 	
	return
end
sgs.ai_cardneed.luamimeng = function(to, card, self)
	return isCard("Jink", card, to)
end
sgs.ai_skill_cardchosen.luamimeng1 = function(self, who, flags)
	if self:isEnemy(who) then 
		if self:getValuableCard(who, true) then 
			if not (self:isWeak(who) and who:isWounded()) then 
				return self:getValuableCard(who, true)
			end 
		end 
	end 
	if self:isFriend(who) then 
		if (self:hasSkills(sgs.lose_equip_skill, who) and who:hasEquip()) then 		
			return self:getCardRandomly(who, "e")
		end 
		if self:needToThrowArmor(who) then 
			return who:getArmor():getEffectiveId()
		end 
	end 
	return self:getCardRandomly(who, "h")
end  
sgs.ai_skill_discard.luamimeng = function(self, discard_num, min_num, optional, include_equip)
	
	local target = self.room:getTag("luamimengTarget"):toPlayer()
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByUseValue(cards)
	if self:isEnemy(target) then 
		for _, acard in ipairs(cards) do
			if acard:isKindOf("Slash") and not (target:isCardLimited(acard, sgs.Card_MethodUse) or target:isProhibited(target, acard, target:getSiblings()))
				and self:slashIsEffective(acard, target, target) then 
				local togive = {}
				table.insert(togive, acard:getId())
				return togive
			end 
		end 
		for _, acard in ipairs(cards) do
			if (acard:isKindOf("SavageAssault") or acard:isKindOf("ArcheryAttack") or acard:isKindOf("Duel") or acard:isKindOf("Duel"))
				and not (target:isCardLimited(acard, sgs.Card_MethodUse) or target:isProhibited(target, acard, target:getSiblings())) then 
				local togive = {}
				table.insert(togive, acard:getId())
				return togive				
			end 
		end 
		for _, acard in ipairs(cards) do
			if (acard:isKindOf("Dismantlement")) and not (target:isCardLimited(acard, sgs.Card_MethodUse) or target:isProhibited(target, acard, target:getSiblings())) then 
				local togive = {}
				table.insert(togive, acard:getId())
				return togive				
			end 
		end 
		self:sortByUseValue(cards, true)
		for _, acard in ipairs(cards) do
			if (acard:isKindOf("Nullification") or acard:isKindOf("sakura") or acard:isKindOf("Collateral"))
				or (target:isCardLimited(acard, sgs.Card_MethodUse) or target:isProhibited(target, acard, target:getSiblings())) then
				local togive = {}
				table.insert(togive, acard:getId())
				return togive				
			end 
		end 
	elseif self:isFriend(target) then 
		cards = sgs.QList2Table(self.player:getCards("he"))
		for _, acard in ipairs(cards) do
			if acard:isKindOf("Peach") then 
				if self:isWeak(target) and target:isWounded() and not (target:isCardLimited(acard, sgs.Card_MethodUse) or target:isProhibited(target, acard, target:getSiblings()))
					and (target:getHandcardNum() == 1) then 
					local togive = {}
					table.insert(togive, acard:getId())
					return togive	
				end 
			end 
		end 
		self:sortByKeepValue(cards)
		for _, acard in ipairs(cards) do
			if (not (acard:isKindOf("Slash") or acard:isKindOf("SavageAssault") or acard:isKindOf("ArcheryAttack") or acard:isKindOf("Lightning") or acard:isKindOf("IronChain")
				or acard:isKindOf("Duel") or acard:isKindOf("Dismantlement") or acard:isKindOf("Indulgence") or acard:isKindOf("SupplyShortage"))) 
				or (target:isCardLimited(acard, sgs.Card_MethodUse) or target:isProhibited(target, acard, target:getSiblings())) then 
				local togive = {}
				table.insert(togive, acard:getId())
				return togive	
			end 
		end 			
	end 	
end 
sgs.ai_card_intention.luamimeng = -40
sgs.ai_use_priority.luamimeng = 12

local function FindZhonggongT(self)
	local discard_ids = self.room:getDrawPile()
	local trickcard = sgs.IntList()
	for _, id in sgs.qlist(discard_ids) do
		local card = sgs.Sanguosha:getCard(id)
		if card:isKindOf("EquipCard") then
			trickcard:append(id)
		end
	end
	discard_ids = self.room:getDiscardPile()
	for _, id in sgs.qlist(discard_ids) do
		local card = sgs.Sanguosha:getCard(id)
		if card:isKindOf("EquipCard") then
			trickcard:append(id)
		end
	end
	local target = self:Nitori(trickcard, false, false, true)
	if target then
		return target
	end
end
local luazhonggong_skill = {}
luazhonggong_skill.name = "luazhonggong"
table.insert(sgs.ai_skills,luazhonggong_skill)
luazhonggong_skill.getTurnUseCard = function(self)
	local slashes = {}
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:isKindOf("Slash") and not self.player:isCardLimited(card, sgs.Card_MethodDiscard) then
			table.insert(slashes, card)
		end
	end
	if #slashes > 1 then
		return sgs.Card_Parse("#luazhonggong:.:")
	end
end
sgs.ai_skill_use_func["#luazhonggong"] = function(cardF, use, self)
	local slashes = {}
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:isKindOf("Slash") and not self.player:isCardLimited(card, sgs.Card_MethodDiscard) then
			table.insert(slashes, card:getId())
		end
	end
	local enemies = self.enemies
	self:sort(enemies, "defense")
	if #slashes < 2 then return end
	for _, enemy in ipairs(enemies) do
		if self:damageIsEffective(enemy, nil, nil) and enemy:getHp() <= #slashes / 2
				and not (self:Skadi2(sgs.Sanguosha:getCard(slashes[1]), enemy, self.player, true) and (self:YouMu2(enemy, true) or enemy:hasSkill("luaxianfeng"))) then
			use.card = sgs.Card_Parse("#luazhonggong:".. slashes[1] .. "+" .. slashes[2] .. ":")
			if use.to then
				use.to:append(enemy)
				return
			end
		end
	end
	if #slashes > 2 and (FindZhonggongT(self) or self.player:containsTrick("gainb")) then
		use.card = sgs.Card_Parse("#luazhonggong:".. slashes[1] .. "+" .. slashes[2] .. "+" .. slashes[3] .. ":")
		if use.to then
			use.to:append(FindZhonggongT(self))
			return
		end
	else

		if (self:hasCrossbowEffect() or self:getCardsNum("Crossbow") > 0) then
			for _, enemy in ipairs(enemies) do
				local slash = sgs.Sanguosha:cloneCard("slash")
				if self:Skadi2(slash, enemy, self.player, true) then
					return
				end
			end
		end
		for _, enemy in ipairs(enemies) do
			if self:damageIsEffective(enemy, nil, nil)
					and not (self:Skadi2(sgs.Sanguosha:getCard(slashes[1]), enemy, self.player, true) and (self:YouMu2(enemy, true) or enemy:hasSkill("luaxianfeng"))) then
				use.card = sgs.Card_Parse("#luazhonggong:".. slashes[1] .. "+" .. slashes[2] .. ":")
				if use.to then
					use.to:append(enemy)
					return
				end
			end
		end
	end
end
sgs.ai_card_intention.luazhonggong = function(self, card, from, tos)  --杀仇恨值
	if card:getSubcards():length() > 2 then
		sgs.updateIntention(from, tos[1], -80)
	else
		sgs.updateIntention(from, tos[1], 80)
	end
end
sgs.ai_skill_askforag.luazhonggong = function(self, card_ids)
	local target = self.room:getTag("luazhonggongTP"):toPlayer()
	local function HASWeapon(name)
		for _, id in ipairs(card_ids) do
			local card = sgs.Sanguosha:getCard(id)
			if card:isKindOf(name) then return id end
		end
		return false
	end
	if target then
		if target:hasSkill("luatianzhao") and HASWeapon("Crossbow") then
			return HASWeapon("Crossbow")
		end
		if target:hasSkill("LuaShanguang") and HASWeapon("Axe") then
			return HASWeapon("Axe")
		end
		if target:hasSkill("luayexiao") and HASWeapon("GudingBlade") then
			return HASWeapon("GudingBlade")
		end
		if target:hasSkill("luayexiao") and HASWeapon("SilverLion") then
			return HASWeapon("SilverLion")
		end
		if HASWeapon("Wanbaochui") then
			return HASWeapon("Wanbaochui")
		end
	end

end
sgs.ai_use_priority.luazhonggong = 5
local luagongchengr_skill = {}
luagongchengr_skill.name = "luagongchengr"
table.insert(sgs.ai_skills,luagongchengr_skill)
luagongchengr_skill.getTurnUseCard = function(self)
	local slashes = {}
	if self.player:hasUsed("#luagongchengr") then return end
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:isKindOf("Slash") then
			table.insert(slashes, card)
		end
	end
	if #slashes > 0 then
		return sgs.Card_Parse("#luagongchengr:.:")
	end
end
sgs.ai_skill_use_func["#luagongchengr"] = function(cardF, use, self)
	local slashes = {}
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:isKindOf("Slash") then
			table.insert(slashes, card)
		end
	end
	local nitori = self.room:findPlayerBySkillName("luagongcheng")
	if (not nitori) or (not nitori:isAlive()) then return end
	if not self:isFriend(nitori) then return end
	if self.player:objectName() == nitori:objectName() then
		use.card = sgs.Card_Parse("#luagongchengr:".. slashes[1]:getEffectiveId() .. ":")
		if use.to then
			use.to = sgs.SPlayerList()
			return
		end
	end
	for _, card in ipairs(slashes) do
		local canUse = true
		for _, askill in sgs.qlist(self.player:getVisibleSkillList(true)) do
			local callback = sgs.ai_cardneed[askill:objectName()]
			if type(callback) == "function" and callback(self.player, card, self) then
				canUse = false
			end
		end
		if canUse then
			use.card = sgs.Card_Parse("#luagongchengr:".. slashes[1]:getEffectiveId() .. ":")
			if use.to then
				use.to = sgs.SPlayerList()
				return
			end
		end
	end
end

sgs.ai_use_priority.luagongchengr = 9
sgs.ai_card_intention.luagongchengr = function(self, card, from, tos)  --杀仇恨值
	local nitori = self.room:findPlayerBySkillName("luagongcheng")
	sgs.updateIntention(from, nitori, -40)
end

local lualiuzhi_skill = {}
lualiuzhi_skill.name = "lualiuzhi"
table.insert(sgs.ai_skills, lualiuzhi_skill)
lualiuzhi_skill.getTurnUseCard = function(self, inclusive)
	if self.player:hasFlag("lualiuzhio") then return end 	
	local p = #(self:getTurnUse(true))
	if self:getOverflow() > 0 and (p > 0) then return end 
	if self.player:usedTimes("#lualiuzhi") >= 2 then return end 
	if p > 1 then return end 
	return sgs.Card_Parse("#lualiuzhi:.:") 
end
sgs.ai_skill_use_func["#lualiuzhi"] = function(cardQ, use, self)
	local enermy = {}
	for _, to in ipairs(self.enemies) do
		table.insert(enermy, to)
	end 
	self:sort(enermy, "defense")	
	if #enermy == 0  then return end 
	local function pandin(acard)
		if acard:isKindOf("Slash") or acard:isKindOf("SavageAssault") or acard:isKindOf("ArcheryAttack") or acard:isKindOf("IronChain")
			or acard:isKindOf("Duel") or acard:isKindOf("Dismantlement") or acard:isKindOf("Indulgence")
				or acard:isKindOf("SupplyShortage") then 
			return false
		end 
		return true
	end 
	for _, to in ipairs(enermy) do
		local count = 0
	    for _, card in sgs.qlist(to:getHandcards()) do
			local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), to:objectName())
			if (card:hasFlag("visible") or card:hasFlag(flag)) and pandin(card) then
				count = count + 1 
			end 
		end 
		if count == to:getHandcardNum() then 
			use.card = sgs.Card_Parse("#lualiuzhi:.:") 
			if use.to and not to:isKongcheng() then 
				use.to:append(to) 
				return 
			end 
		end 
	end 
	for _, to in ipairs(enermy) do
		use.card = sgs.Card_Parse("#lualiuzhi:.:") 
		if use.to and not to:isKongcheng() then 
			use.to:append(to) 
			return 
		end 	
	end 
	for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if not self:isFriend(player) and not player:isKongcheng() then 
			use.card = sgs.Card_Parse("#lualiuzhi:.:") 
			if use.to then 
				use.to:append(to) 
				return 
			end 		
		end 
	end 
end 

sgs.ai_card_intention.lualiuzhi = 30
sgs.ai_use_priority.lualiuzhi = 9

function sgs.ai_cardneed.lualiuzhi(to, card)
	return card:isKindOf("Armor") and not to:getArmor() --and not self:needToThrowArmor()
end

sgs.ai_slash_prohibit.LuaQiyuan = function(self, from, to, card)
	if to:hasSkill("LuaQiyuan") then
		for _, enemy in ipairs(self.enemies) do
			if enemy:objectName() ~= to:objectName() then
				local nature = sgs.DamageStruct_Normal
				if card:isKindOf("ThunderSlash") then nature = sgs.DamageStruct_Thunder end
				if card:isKindOf("FireSlash") then nature = sgs.DamageStruct_Fire end
				if enemy:hasSkill("luafenxing") and not enemy:isKongcheng() then return true end
				if not self:damageIsEffective(from, nature, enemy) then
					return true
				end
			end
		end
	end
end

sgs.ai_skill_use["@@luaweijing"] = function(self, prompt)
	local function getplayerWeijinValue(player)

	end
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(cards)
	for _, c in ipairs(cards) do
		if c:getSuit() == sgs.Card_Spade then
			for _, friend in ipairs(self.friends) do
				if friend:hasSkill("LuaTaiji") and friend:getHandcardNum() > 2 and not friend:containsTrick("gainb") then
					return "#luaweijing:" .. c:getId() .. ":->" .. friend:objectName()
				end
			end
		end
	end
	for _, c in ipairs(cards) do
		if c:getSuit() == sgs.Card_Spade then
			for _, friend in ipairs(self.friends) do
				if friend:hasSkill("luasuiyue") and not friend:containsTrick("gainb") then
					return "#luaweijing:" .. c:getId() .. ":->" .. friend:objectName()
				end
			end
		end
	end
	for _, c in ipairs(cards) do
		if c:getSuit() == sgs.Card_Spade then
			for _, friend in ipairs(self.friends) do
				if self:getDangerousCard(friend) and not friend:containsTrick("gainb") then
					return "#luaweijing:" .. c:getId() .. ":->" .. friend:objectName()
				end
			end
		end
	end
	for _, c in ipairs(cards) do
		if c:getSuit() == sgs.Card_Spade then
			for _, friend in ipairs(self.friends) do
				if friend:hasSkill("luashende") and not friend:containsTrick("gainb") then
					return "#luaweijing:" .. c:getId() .. ":->" .. friend:objectName()
				end
			end
		end
	end
	for _, c in ipairs(cards) do
		if c:getSuit() == sgs.Card_Heart then
			if not (c:isKindOf("Peach") and not self:OverFlowPeach(c)) and not c:isKindOf("ExNihilo")
				and not c:isKindOf("Indulgence") and not c:isKindOf("Wanbaochui") then
				for _, enemy in ipairs(self.enemies) do
					if not enemy:containsTrick("gaina") and enemy:hasSkill("luayuyi") and #self.enemies < 3 then
						return "#luaweijing:" .. c:getId() .. ":->" .. enemy:objectName()
					end
				end
			end
		end
	end
	for _, c in ipairs(cards) do
		if c:getSuit() == sgs.Card_Spade then
			self:sort(self.friends, "defense")
			for _, friend in ipairs(self.friends) do
				if not friend:containsTrick("gainb") then
					return "#luaweijing:" .. c:getId() .. ":->" .. friend:objectName()
				end
			end
		end
	end
	for _, c in ipairs(cards) do
		if c:getSuit() == sgs.Card_Heart then
			if not (c:isKindOf("Peach") and not self:OverFlowPeach(c)) and not c:isKindOf("ExNihilo")
				and not c:isKindOf("Indulgence") and not c:isKindOf("Wanbaochui") then
				self:sort(self.enemies, "defense")
				for _, enemy in ipairs(self.enemies) do
					for _, skill in sgs.qlist(enemy:getVisibleSkillList(true)) do
						local callback = sgs.ai_cardneed[skill:objectName()]
						if not (type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](enemy, c, self)) then
							if not enemy:containsTrick("gaina") then
								return "#luaweijing:" .. c:getId() .. ":->" .. enemy:objectName()
							end
						end
					end
				end
			end
		end
	end
end

sgs.ai_skill_discard.luatanbao = function(self, discard_num, min_num, optional, include_equip)
	if discard_num > 2 then return "." end
	local nazrin = self.room:findPlayerBySkillName("luatanbao")
	if not nazrin then return "." end
	if not self:isFriend(nazrin) then return "." end
	local y_num = self:getCardsNum("Peach")
	local a_num = self:getCardsNum("Analeptic")
	local z_num = y_num + self.player:getHp()
	local function check_R(card)
		if (z_num <= self.player:getMaxHp()) and card:isKindOf("Peach") then return false end
		local bool_2 = card:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip))
		local bool_3 = (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu"))
				and ((self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
				or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
		if bool_2 or bool_3 then return false end
		if card:isKindOf("Peach") and self:getOverflow() <= 0 then return false end
		if card:isKindOf("Analeptic") and (a_num == 1) then return false end
		if card:isKindOf("AOE") and self:getAoeValue(card) >= 50 then return false end
		if card:isKindOf("ExNihilo") and card:isAvailable(self.player) then return false end
		if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
		if card:isKindOf("OffensiveHorse") and card:objectName() == "shanghai" then
			return false
		end
		if card:isKindOf("DefensiveHorse") and card:objectName() == "hongrai" then
			return false
		end
		if card:isKindOf("EquipCard") then
			local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
			if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) and rinnosuke
					and rinnosuke:isAlive() and self:isFriend(rinnosuke) then return false end
		end
		if card:isKindOf("Hui") then return false end
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return end
		end
		if card:isKindOf("Banquet") and #self.friends > 1 then return false end
		if card:isKindOf("Indulgence") then return false end
		if card:isKindOf("Duel") then return false end
		if card:isKindOf("Wanbaochui") then return false end
		if card:isKindOf("Pundarika") and self:shouldEPundarika(self.player) then return false end
		for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
			local callback = sgs.ai_cardneed[skill:objectName()]
			if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](self.player, card, self) then
				return false
			end
		end
		return true
	end

	local nyasama = self.room:findPlayerBySkillName("luawanbang")

	local suit
	if nyasama then
		if nyasama:getMark("@lianmengheart") > 0 then
			suit = sgs.Card_Heart
		elseif nyasama:getMark("@lianmengdiamond") > 0 then
			suit = sgs.Card_Diamond
		elseif nyasama:getMark("@lianmengclub") > 0 then
			suit = sgs.Card_Club
		elseif nyasama:getMark("@lianmengspade") > 0 then
			suit = sgs.Card_Spade
		end
	end

	local toDiscard = {}
	local cards = sgs.QList2Table(self.player:getCards("h"))
	self:sortByUseValue(cards, true)
	if nyasama and self:isFriend(nyasama) then
		for _, card_0 in ipairs(cards) do
			if card_0:getSuit() == suit then
				table.insert(toDiscard, card_0:getEffectiveId())
				if #toDiscard > 1 then break end
			end
		end
	end
	if #toDiscard < 2 then
		for _, card_0 in ipairs(cards) do
			if check_R(card_0) then
				table.insert(toDiscard, card_0:getEffectiveId())
				if #toDiscard > 1 then break end
			end
		end
	end
	if #toDiscard < 2 then return {} end
	for _, id in sgs.qlist(self.room:getDrawPile()) do
		local Acard = sgs.Sanguosha:getCard(id)
		if Acard:isKindOf("Wanbaochui") then
			return toDiscard
		end
		if Acard:isKindOf("Pundarika") and self:shouldEPundarika(self.player) then
			return toDiscard
		end
		if Acard:isKindOf("Indulgence") then
			for _, enemy in ipairs(self.enemies) do
				if self:getIndulgenceValue(enemy) > 3 then
					return toDiscard
				end
			end
		end
		if Acard:isKindOf("Snatch") and self:ThreeCheck(Acard) then
			return toDiscard
		end

		if Acard:isKindOf("Duel") then
			return toDiscard
		end
		for _, enemy in ipairs(self.enemies) do
			if self:getDangerousCard(enemy) and Acard:isKindOf("Dismantlement") then
				return toDiscard
			end
		end
		if Acard:isKindOf("SupplyShortage") then
			for _, enemy in ipairs(self.enemies) do
				if self:getSupplyShortageValue(enemy, self.enemies) > 3 then
					return toDiscard
				end
			end
		end
		if Acard:isKindOf("Peach") and not self:OverFlowPeach(Acard) then
			return toDiscard
		end
		if not self.player:getArmor() and (Acard:isKindOf("EightDiagram") or Acard:isKindOf("RenwangShield"))
				and self.player:getHp() < 4 then
			return toDiscard
		end
		if Acard:isKindOf("AOE") and self:getAoeValue(Acard) >= 50 then
			return toDiscard
		end
		local cunzai = false
		for _, card_0 in ipairs(cards) do
			for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skill:objectName()]
				if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](self.player, card_0, self) then
					cunzai = true
				end
			end
		end
		if not cunzai then
			for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skill:objectName()]
				if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](self.player, Acard, self) then
					return false
				end
			end
		end
	end
end
sgs.ai_skill_choice.luatanbao = function(self, choice, data)
	local target = data:toPlayer()
	--
	if not self:isFriend(target) then
		if self:needKongcheng(target) then
			for _, id in sgs.qlist(self.room:getDrawPile()) do
				local Acard = sgs.Sanguosha:getCard(id)
				if Acard:isKindOf("Jink") then
					local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
					dummy_0:addSubcard(id)
					target:obtainCard(dummy_0)
					return "forAI"
				end
			end
		end
		for _, id in sgs.qlist(self.room:getDrawPile()) do
			local Acard = sgs.Sanguosha:getCard(id)
			if Acard:isKindOf("Hui") then
				local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				dummy_0:addSubcard(id)
				target:obtainCard(dummy_0)
				return "forAI"
			end
		end
	end
	--
	if target:getMark("lualingbaiT") > 0 then
		local atLeastOne = false
		local cardsQ = target:getHandcards()
		local queshi
		for q = 1,20 do
			local str = self.player:getTag("lualingbai" .. q)
			if str and str:toString() and str:toString() ~= "" then
				atLeastOne = true
				str = str:toString():split("|")
				local matchA = false
				for _, card0 in sgs.qlist(cardsQ) do
					if card0:objectName() == str[1] and card0:getSuitString() == str[2] then
						cardsQ:removeOne(card0)
						matchA = true
						break
					end
				end
				if not matchA then
					if queshi then queshi = nil;break end
					self.room:writeToConsole("lualingbai AI queshi")
					queshi = str
				end
			end
		end
		for _, id in sgs.qlist(self.room:getDrawPile()) do
			local Acard = sgs.Sanguosha:getCard(id)
			if queshi and Acard:objectName() == queshi[1] and Acard:getSuitString() == queshi[2] then
				local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				dummy_0:addSubcard(id)
				target:obtainCard(dummy_0)
				return "forAI"
			end
		end
	end

	local cards = {}
	for _, id in sgs.qlist(self.room:getDrawPile()) do
		local Acard = sgs.Sanguosha:getCard(id)
		for _, skill in sgs.qlist(target:getVisibleSkillList(true)) do
			local callback = sgs.ai_cardneed[skill:objectName()]
			if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](target, Acard, self) then
				table.insert(cards, Acard)

			end
		end
	end
	if #cards > 0 then
		self:sortByUseValue(cards)
		local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
		dummy_0:addSubcard(cards[1]:getEffectiveId())
		target:obtainCard(dummy_0)
		return "forAI"
	end
	for _, id in sgs.qlist(self.room:getDrawPile()) do
		local Acard = sgs.Sanguosha:getCard(id)
		if Acard:isKindOf("Wanbaochui") then
			local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			dummy_0:addSubcard(id)
			target:obtainCard(dummy_0)
			return "forAI"
		end
	end
	for _, id in sgs.qlist(self.room:getDrawPile()) do
		local Acard = sgs.Sanguosha:getCard(id)
		if Acard:isKindOf("Pundarika") and self:shouldEPundarika(target) then
			local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			dummy_0:addSubcard(id)
			target:obtainCard(dummy_0)
			return "forAI"
		end
	end
	for _, id in sgs.qlist(self.room:getDrawPile()) do
		local Acard = sgs.Sanguosha:getCard(id)
		if Acard:isKindOf("AOE") and self:getAoeValue(Acard) >= 50 and not Acard:isKindOf("ReligionBattle") then
			local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			dummy_0:addSubcard(id)
			target:obtainCard(dummy_0)
			return "forAI"
		end
	end
	for _, id in sgs.qlist(self.room:getDrawPile()) do
		local Acard = sgs.Sanguosha:getCard(id)
		if Acard:isKindOf("Indulgence") then
			for _, enemy in ipairs(self.enemies) do
				if self:getIndulgenceValue(enemy) > 3 then
					local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
					dummy_0:addSubcard(id)
					target:obtainCard(dummy_0)
					return "forAI"
				end
			end
		end
	end
	for _, id in sgs.qlist(self.room:getDrawPile()) do
		local Acard = sgs.Sanguosha:getCard(id)
		if Acard:isKindOf("Peach") and target:isWounded() then
			local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			dummy_0:addSubcard(id)
			target:obtainCard(dummy_0)
			return "forAI"
		end
	end
	for _, id in sgs.qlist(self.room:getDrawPile()) do
		local Acard = sgs.Sanguosha:getCard(id)
		if Acard:isKindOf("Duel") then
			local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			dummy_0:addSubcard(id)
			target:obtainCard(dummy_0)
			return "forAI"
		end
	end
	for _, id in sgs.qlist(self.room:getDrawPile()) do
		local Acard = sgs.Sanguosha:getCard(id)
		if not target:getArmor() and (Acard:isKindOf("EightDiagram") or Acard:isKindOf("RenwangShield"))
				and target:getHp() < 4 then
			local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			dummy_0:addSubcard(id)
			target:obtainCard(dummy_0)
			return "forAI"
		end
	end
	for _, id in sgs.qlist(self.room:getDrawPile()) do
		local Acard = sgs.Sanguosha:getCard(id)
		if Acard:isKindOf("SupplyShortage") then
			for _, enemy in ipairs(self.enemies) do
				if self:getSupplyShortageValue(enemy, self.enemies) > 3 and target:distanceTo(enemy) == 1 then
					local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
					dummy_0:addSubcard(id)
					target:obtainCard(dummy_0)
					return "forAI"
				end
			end
		end
	end

	for _, id in sgs.qlist(self.room:getDrawPile()) do
		local Acard = sgs.Sanguosha:getCard(id)
		for _, enemy in ipairs(self.enemies) do
			if Acard:isKindOf("Snatch") and target:distanceTo(enemy) == 1 and self:getDangerousCard(enemy) then
				local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				dummy_0:addSubcard(id)
				target:obtainCard(dummy_0)
				return "forAI"
			end
		end
	end

	for _, id in sgs.qlist(self.room:getDrawPile()) do
		local Acard = sgs.Sanguosha:getCard(id)
		for _, enemy in ipairs(self.enemies) do
			if Acard:isKindOf("Dismantlement") and self:getDangerousCard(enemy) then
				local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				dummy_0:addSubcard(id)
				target:obtainCard(dummy_0)
				return "forAI"
			end
		end
	end
	for _, id in sgs.qlist(self.room:getDrawPile()) do
		local Acard = sgs.Sanguosha:getCard(id)
		if (Acard:objectName() == "hongrai" or Acard:objectName() == "shanghai") then
			local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			dummy_0:addSubcard(id)
			target:obtainCard(dummy_0)
			return "forAI"
		end
	end
	for _, id in sgs.qlist(self.room:getDrawPile()) do
		local Acard = sgs.Sanguosha:getCard(id)
		if (Acard:objectName() == "hongrai" or Acard:objectName() == "shanghai") then
			if self:ThreeCheck(Acard, 1, target) then
				local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				dummy_0:addSubcard(id)
				target:obtainCard(dummy_0)
				return "forAI"
			end
		end
	end
	return "forAI"
end

sgs.ai_skill_discard.lualingbai = function(self, discard_num, min_num, optional, include_equip)
	self.room:writeToConsole("lualingbai AI test")
	local jinka = 0
	local slashA = 0
	local jink_count = 0
	local toDiscard = {}
	for _, card in sgs.qlist(self.player:getHandcards()) do
		if card:isKindOf("Jink") then
			jink_count = jink_count + 1
		end
	end
	for _, card in sgs.qlist(self.player:getHandcards()) do
		if card:hasFlag("prelingbai") then
			table.insert(toDiscard, card:getEffectiveId())
		end
	end
	if #toDiscard > 0 then return toDiscard end
	local acards = {}
	local cards = sgs.QList2Table(self.player:getCards("h"))
	self:sortByKeepValue(cards)
	for _,card in ipairs(cards) do
		local insert = true
		for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
			local callback = sgs.ai_cardneed[skill:objectName()]
			if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](self.player, card, self) then
				insert = false
			end
		end
		if insert then
			table.insert(acards, card)
		end
	end
	for _,card in ipairs(acards) do
		if not card:isKindOf("Nullification") and not ((jinka == jink_count - 1) and card:isKindOf("Jink") and self.player:getHp() < 4)
			and not (card:isKindOf("Analeptic") and self:isWeak()) then
			local will_use = false
			if card:getTypeId() == sgs.Card_TypeTrick then
				if card:isKindOf("Indulgence") then will_use = true end
				local dummy_use = { isDummy = true }
				self:useTrickCard(card, dummy_use)
				if dummy_use.card then will_use = true end
			elseif card:getTypeId() == sgs.Card_TypeEquip then
				local dummy_use = { isDummy = true }
				self:useEquipCard(card, dummy_use)
				if dummy_use.card then will_use = true end
			else
				local dummy_use = { isDummy = true }
				self:useBasicCard(card, dummy_use)
				if dummy_use.card then will_use = true end
				if card:isKindOf("Slash") then
					slashA = slashA + 1
					if slashA > 1 and not self.player:hasSkills(sgs.need_slash_skill)
						and not self:hasCrossbowEffect() then will_use = false end
				end
			end
			if not will_use then
				table.insert(toDiscard, card:getEffectiveId())
				if card:isKindOf("Jink") then jinka = jinka + 1 end
				if #toDiscard >= self.player:getMaxCards() - 1 then break end
				if #toDiscard >= self.player:getMaxCards() - 2 and math.random() > 0.8 then break end
			end
		end
	end
	if #toDiscard > 0 then return toDiscard end
end

local lualingbai_skill = {}
lualingbai_skill.name = "lualingbai"
table.insert(sgs.ai_skills, lualingbai_skill)
lualingbai_skill.getTurnUseCard = function(self, inclusive)
	for _, friend in ipairs(self.friends) do
		if friend:getMark("lualingbaiT") > 0 and not friend:isKongcheng()
			and not self.player:hasUsed("#lualingbai") then
			sgs.ai_use_priority.lualingbai = 15
			return sgs.Card_Parse("#lualingbai:.:")
		end
	end
	if not self.player:hasUsed("#lualingbai") then
		return sgs.Card_Parse("#lualingbai:.:")
	end
end

sgs.ai_skill_use_func["#lualingbai"] = function(card, use, self)
	local friends = self.friends
	for _, friend in ipairs(friends) do
		if friend:getMark("lualingbaiT") > 0 and not friend:isKongcheng() then

			use.card = sgs.Card_Parse("#lualingbai:.:")
			if use.to then
				use.to:append(friend)
				return
			end
		end
	end
	self:sort(friends, "handcard2")
	for _, friend in ipairs(friends) do
		if friend:getHandcardNum() > 1 and self:getOverflow(friend) <= 0 and not friend:isKongcheng() then
			sgs.ai_use_priority.lualingbai = 0
			use.card = sgs.Card_Parse("#lualingbai:.:")
			if use.to then
				use.to:append(friend)
				return
			end
		end
	end
	for _, friend in ipairs(friends) do
		if friend:getHandcardNum() > 2 then
			sgs.ai_use_priority.lualingbai = 0
			use.card = sgs.Card_Parse("#lualingbai:.:")
			if use.to then
				use.to:append(friend)
				return
			end
		end
	end
	self:sort(friends, "defense2")
	for _, friend in ipairs(friends) do
		if not friend:isKongcheng() then
			sgs.ai_use_priority.lualingbai = 0
			use.card = sgs.Card_Parse("#lualingbai:.:")
			if use.to then
				use.to:append(friend)
				return
			end
		end
	end
end

sgs.ai_skill_askforag.luatanbao = function(self, card_ids)
	for _, card_id in ipairs(card_ids) do
		local card = sgs.Sanguosha:getCard(card_id)
		if card:isKindOf("Slash") then
			for _, card_id2 in ipairs(card_ids) do
				local card2 = sgs.Sanguosha:getCard(card_id)

			end
		end
	end
end


local function findhongyiTarget(self)
	local slash = sgs.Sanguosha:cloneCard("slash")

	local dummy_use = { isDummy = true , to = sgs.SPlayerList()}
	self:useBasicCard(slash, dummy_use)

	if dummy_use.card and dummy_use.card:isKindOf("Slash") and dummy_use.to and dummy_use.to:length() > 0 then
		return dummy_use.to
	end
	return
end
local function findhongyicard(self, target, cardsA)
	local cards = self.player:getCards("h")
	cards = sgs.QList2Table(cards)
	local red_card
	self:sortByUseValue(cards, true)

	local x = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
	local bool_k = (self.player:getHp() <= 2 and self.player:getHandcardNum() <= 3 and x <= 1)

	local useAll = false
	if target:getHp() == 1 and not target:hasArmorEffect("eight_diagram") and self:isWeak(target)
			and getCardsNum("Jink", target, self.player) + getCardsNum("Peach", target, self.player) + getCardsNum("Analeptic", target, self.player) == 0 then
		useAll = true
	end

	local function caonima(slash)
		local boolQ = false
		if (self.player:getHandcardNum() > 2 or self:getOverflow() > 0) then
			for _, card in ipairs(cardsA) do
				if card:getEffectiveId() == slash:getEffectiveId() then boolQ = true end
			end
		end
		if not boolQ then return true end
		return false
	end
	local function Check_F(card_0)
		self.room:writeToConsole("hongyi AI test 1.2")
		if card_0:isKindOf("Crossbow") then return false end
		if (card_0:isKindOf("Peach") or card_0:isKindOf("ExNihilo")) and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
			return false
		end
		if card_0:isKindOf("Duel") and not self.player:isCardLimited(card_0, sgs.Card_MethodUse) then
			return false
		end
		if card_0:isKindOf("Indulgence") and not useAll then
			for _, enemy in ipairs(self.enemies) do
				if self:getIndulgenceValue(enemy) > 3 then return false end
			end
		end
		if self:ThreeCheck(card_0) and not card_0:isKindOf("Dismantlement") then return false end

		if (card_0:isKindOf("AOE") and self:getAoeValue(card_0) > 35) then return false end
		if card_0:isKindOf("Lightning") and not self:willUseLightning(card_0) then return true end
		local bool_2 = card_0:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(card_0:getId()) == sgs.Player_PlaceEquip))
		local bool_3 = (card_0:isKindOf("EightDiagram") or card_0:isKindOf("RenwangShield") or card_0:isKindOf("Tengu"))
				and ((self.room:getCardPlace(card_0:getId()) == sgs.Player_PlaceEquip)
				or ((self.room:getCardPlace(card_0:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
		if (bool_2 or bool_3) and not useAll then return false end
		if card_0:isKindOf("Slash") and caonima(card_0) then return false end
		for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
			local callback = sgs.ai_cardneed[skill:objectName()]
			if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](self.player, card_0, self) then
				return false
			end
		end
		if (not (card_0:isKindOf("Jink") and bool_k)) or useAll then
			return true
		end
	end
	local disCrossbow = false
	if self:getCardsNum("Slash") < 2 or self.player:hasSkill("paoxiao") then
		disCrossbow = true
	end


	if not red_card then
		for _, card in ipairs(cards) do
			if (not isCard("Crossbow", card, self.player) and not disCrossbow)
					and (Check_F(card) or self.player:hasWeapon("pundarika")) and card:isRed() then
				self.room:writeToConsole("hongyi AI test 1.5")
				red_card = card
				break
			end
		end
	end
	return red_card
end
local luahongyia_skill = {}
luahongyia_skill.name = "luahongyia"
table.insert(sgs.ai_skills, luahongyia_skill)
luahongyia_skill.getTurnUseCard = function(self, inclusive)
	local torisumi = self.room:findPlayerBySkillName("luahongyi")
	local skill = torisumi and torisumi:isAlive() and (torisumi:getMark("@hongyired") > 0) or ((torisumi:getMark("@hongyired") == 0) and (torisumi:getMark("@hongyiblack") == 0))
	if skill and torisumi and self:isFriend(torisumi) then
		local cardsA = self:getTurnUse(true)
		local targets = findhongyiTarget(self, cardsA)
		self.room:writeToConsole("hongyi AI test 0.5")
		if (not targets) or (targets:isEmpty()) then return end
		self.room:writeToConsole("hongyi AI test 1")
		local red_card = findhongyicard(self, targets:at(0), cardsA)
		if red_card then
			self.room:writeToConsole("hongyi AI test 2")
			local suit = red_card:getSuitString()
			local number = red_card:getNumberString()
			local card_id = red_card:getEffectiveId()
			local card_str = ("slash:luahongyia[%s:%s]=%d"):format(suit, number, card_id)
			local slash = sgs.Card_Parse(card_str)
			assert(slash)
			return slash
		end
	end
end
sgs.ai_use_priority.luahongyia = 4.8


sgs.ai_cardsview["luahongyia"] = function(self, class_name, player)
	local torisumi = self.room:findPlayerBySkillName("luahongyi")
	if not torisumi then return end
	if not self:isFriend(torisumi) then return end
	if player:objectName() == self.player:objectName() then
		local function Check_R(card)
			if player:hasSkill("luahongyia") then
				if self:isWeak() then return true end
				for _, skill in sgs.qlist(player:getVisibleSkillList(true)) do
					local callback = sgs.ai_cardneed[skill:objectName()]
					if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](player, card, self) then
						return false
					end
				end
				if card:isKindOf("Wanbaochui") or card:isKindOf("Indulgence") or card:isKindOf("ExNihilo") then return false end
				local bool_3 = (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu"))
						and ((self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
						or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
				if bool_3 then return false end
				return true
			end
		end
		local skill = (torisumi:getMark("@hongyired") > 0) or ((torisumi:getMark("@hongyired") == 0) and (torisumi:getMark("@hongyiblack") == 0))
		if skill then
			local abc = string.lower(class_name)
			if (abc == "slash") then
				local pcards = sgs.QList2Table(self.player:getCards("h"))
				for _,card in ipairs(pcards) do
					if card:isRed() and Check_R(card) then
						local suit = card:getSuitString()
						local number = card:getNumberString()
						local card_id = card:getEffectiveId()
						return (abc .. ":luahongyia[%s:%s]=%d"):format(suit, number, card_id)
					end
				end
			end
		else
			local abc = string.lower(class_name)
			if (abc == "jink") then
				local pcards = sgs.QList2Table(self.player:getCards("h"))
				for _,card in ipairs(pcards) do
					if card:isBlack() and Check_R(card) then
						local suit = card:getSuitString()
						local number = card:getNumberString()
						local card_id = card:getEffectiveId()
						return (abc .. ":luahongyia[%s:%s]=%d"):format(suit, number, card_id)
					end
				end
			end
		end
	end
end

local function findbestjinlunTarget(self)
	local function shouyi(playerX)
		local true_handcard_num = playerX:getHandcardNum()
		if playerX:containsTrick("gainb") then true_handcard_num = 0 end
		if self:isFriend(playerX) then
			local w = 3 - true_handcard_num
			if playerX:containsTrick("gaina") then w = w - 2 end
			w = w - math.floor(getCardsNum("Peach", playerX, self.player))
			w = w - math.floor(getCardsNum("Hui", playerX, self.player))
			if w > 0 then
				if playerX:hasSkills("luasanaex|luacaihuo|luajunying|luasuiyue|luaboli|luayouqu|Luaxianzhe|Luazonghuo|luajiangsui" ..
						"luashikong|luayequ|luashizhu|luafenghua|lualiuzhi|luawangshi|LuaFeixiang|LuaLeishi|luaxieli|luakuangxiang")
						or (playerX:hasSkill("LuaTaiji") and playerX:getHp() > 2)
						or (playerX:hasSkills("luashaojie|luashiji") and playerX:getHp() < 3)
						or (playerX:hasSkill("luayigong") and playerX:getEquips():length() > 1) then
					w = w + 1
				end
			end
			if self:isWeak(playerX) then
				w = w - math.floor(getCardsNum("Analeptic", playerX, self.player))
			end
			w = w - 0.5*math.floor(getCardsNum("Jink", playerX, self.player))
			w = w - 0.5*math.floor(getCardsNum("Analeptic", playerX, self.player))
			return w
		else
			local w = true_handcard_num - 4
			if playerX:containsTrick("gaina") then w = w + 2 end
			if w > 0 then
				if playerX:hasSkills("luasanaex|luacaihuo|luajunying|luasuiyue|luaboli|luayouqu|Luaxianzhe|Luazonghuo|luajiangsui" ..
						"luashikong|luayequ|luashizhu|luafenghua|lualiuzhi|luawangshi|LuaFeixiang|LuaLeishi|luaxieli|luakuangxiang")
						or (playerX:hasSkill("LuaTaiji") and playerX:getHp() > 2)
						or (playerX:hasSkills("luashaojie|luashiji") and playerX:getHp() < 3)
						or (playerX:hasSkill("luayigong") and playerX:getEquips():length() > 1) then
					w = w - 1
				end
			end
			return w
		end
	end
	local func = function(a, b)
		return shouyi(a) > shouyi(b)
	end
	local toList = sgs.QList2Table(self.room:getAlivePlayers())
	table.sort(toList, func)
	if shouyi(toList[1]) > 1 then return toList[1] end
end
local luafushen_skill = {}
luafushen_skill.name = "luafushen"
table.insert(sgs.ai_skills, luafushen_skill)
luafushen_skill.getTurnUseCard = function(self, inclusive)
	if self.player:getMark("@hongyyi") >= 1 and self.player:usedTimes("#luafushen") < 2 then
		return sgs.Card_Parse("#luafushen:.:")
	end
end
sgs.ai_skill_use_func["#luafushen"] = function(card, use, self)
	local function Cac(playser)
		local x = 4 - playser:getHp()
		x = math.min(playser:getLostHp(), x)
		if playser:getMark("@chenti") > 0 then x = x + 0.5 end
		return x
	end
	local compare_func = function(a, b)
		local v1 = Cac(a)
		local v2 = Cac(b)
		return v1 > v2
	end

	use.card = sgs.Card_Parse("#luafushen:.:")
	if use.to then
		use.to = sgs.SPlayerList()
		return
	end

	local kp = findbestjinlunTarget(self)
	if kp then

	end
end
sgs.ai_skill_choice.luafushen = function(self, choices)
	return "luajinlun"
end
sgs.ai_skill_use["luafushena"] = function(self, prompt, pattern)  --:toString()
	self.room:writeToConsole("fushenb test")
	return self:getCardId("Jink")
end
sgs.ai_use_priority.luafushen = 2

