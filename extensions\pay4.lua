

extension_pay_d = sgs.Package("pay4")




daiyousei = sgs.General(extension_pay_d,"daiyousei","luaxing",3,false,false,false)
daiyouseiA = sgs.General(extension_pay_d,"daiyouseiA","luaxing",3,false,true,true)
daiyouseiB = sgs.General(extension_pay_d,"daiyouseiB","luaxing",3,false,true,true)
daiyouseiC = sgs.General(extension_pay_d,"daiyouseiC","luaxing",3,false,true,true)
daiyouseiD = sgs.General(extension_pay_d,"daiyouseiD","luaxing",3,false,true,true)

koakuma = sgs.General(extension_pay_d,"koakuma","luahong",4,false,false,false)
koakumaA = sgs.General(extension_pay_d,"koakumaA","luahong",4,false,true,true)
koakumaB = sgs.General(extension_pay_d,"koakumaB","luahong",4,false,true,true)

white = sgs.General(extension_pay_d,"white","luayao",3,false,false,false)
whiteA = sgs.General(extension_pay_d,"whiteA","luayao",3,false,true,true)
whiteC = sgs.General(extension_pay_d,"whiteC","luayao",3,false,true,true)
whiteD = sgs.General(extension_pay_d,"whiteD","luayao",3,false,true,true)
benben = sgs.General(extension_pay_d,"benben","luaxing",3,false,false,false)
sp_meili = sgs.General(extension_pay_d,"sp_meili","luacai",4,false,false,false) 
keine = sgs.General(extension_pay_d,"keine","luacai",3,false,false,false)
keineA = sgs.General(extension_pay_d,"keineA","luacai",4,false,true,true) 
keineB = sgs.General(extension_pay_d,"keineB","luacai",4,false,true,true) 
aunn = sgs.General(extension_pay_d,"aunn","luacai",3,false,false,false)
doremi = sgs.General(extension_pay_d,"doremi","luayue",3,false,false,false)
nitori = sgs.General(extension_pay_d,"nitori","luafeng",3,false,false,false)
hina = sgs.General(extension_pay_d,"hina","luafeng",4,false,false,false)
hinaA = sgs.General(extension_pay_d,"hinaA","luafeng",4,false,true,true)
hinaB = sgs.General(extension_pay_d,"hinaB","luafeng",4,false,true,true)

nazrin = sgs.General(extension_pay_d,"nazrin","lualian",3,false,false,false)

LuaYizuoCard = sgs.CreateSkillCard{
	name = "LuaYizuo" ,
	will_throw = false ,
	handling_method = sgs.Card_MethodNone ,
	filter = function(self, selected, to_select)
		return (#selected == 0) and (to_select:objectName() ~= sgs.Self:objectName())
	end ,
	on_use = function(self, room, source, targets)
		local all_players = room:getAllPlayers()
		for _, player in sgs.qlist(all_players) do
			if player:getMark("@LuaYizuo") > 0 and player:getMark("LuaYizuo" .. source:objectName()) > 0 then
				player:loseAllMarks("@LuaYizuo")
				room:setPlayerMark(targets[1], "LuaYizuo" .. source:objectName(), 0)
			end
		end		
		local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_GIVE, source:objectName(), targets[1]:objectName(), "LuaYizuo", "")
		room:obtainCard(targets[1], self, reason, false)
		targets[1]:gainMark("@LuaYizuo")
		room:setPlayerMark(targets[1], "LuaYizuo" .. source:objectName(), 1)
	end
}
LuaYizuo = sgs.CreateViewAsSkill{
	name = "LuaYizuo" ,
	n = 99 ,
	view_filter = function(self, selected, to_select)
		return true
	end ,
	view_as = function(self, cards)
		if #cards == 0 then return nil end
		local yizuo_card = LuaYizuoCard:clone()
		for _, c in ipairs(cards) do
			yizuo_card:addSubcard(c)
		end
		return yizuo_card
	end ,
	enabled_at_play = function(self, player)
		return (not player:isKongcheng()) and not player:hasUsed("#LuaYizuo")
	end
}

Yizuo = sgs.CreateTriggerSkill{
	name = "#Yizuo" ,
	events = {sgs.HpRecover, sgs.Damaged},
	global = true, --究极重要！全场技能必备，花了5个小时买一场教训！
	frequency = sgs.Skill_NotFrequent,
	on_trigger = function(self, event, player, data, room)
		if event == sgs.HpRecover then
			local recover = data:toRecover()
			for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				if player:getMark("@LuaYizuo") > 0 and player:isAlive() 
					and player:getMark("LuaYizuo" .. p:objectName()) > 0 then
					room:sendCompulsoryTriggerLog(p, self:objectName())
					p:drawCards(recover.recover, self:objectName())
				end
			end
		elseif event == sgs.Damaged then
			local damage = data:toDamage()
			if damage.damage == 0 then return false end
			if damage and damage.damage and damage.damage > 0 and damage.to and damage.to:getMark("@LuaYizuo") > 0 then
				--for i = 1, damage.damage, 1 do
					for _, daiyousei in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
						if damage.to:getMark("LuaYizuo" .. daiyousei:objectName()) > 0 then
							daiyousei:drawCards(damage.damage, self:objectName())
						end 
					end 
				--end
			end
		-- elseif event == sgs.HpLost then
			-- if player:getMark("@LuaYizuo") > 0 then
				-- for _, daiyousei in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
					-- daiyousei:drawCards(1, self:objectName())
				-- end 
			-- end 
		end 
		return false
	end
}

LuaQiyuan = sgs.CreateTriggerSkill{
	name = "LuaQiyuan" ,
	events = {sgs.DamageInflicted} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if (event == sgs.DamageInflicted) then
			local damage = data:toDamage()
			if not damage.to then return false end 
			if damage.transfer then return false end 
			if damage.to:objectName() ~= player:objectName() then return false end 
			if not damage.to:hasSkill("LuaQiyuan") then return false end 
			if damage.card and damage.card:isKindOf("Slash") then
				local target
				for _, p in sgs.qlist(room:getOtherPlayers(player)) do
					if room:askForSkillInvoke(p, "LuaQiyuan2") then
						target = p 
						break
					end 
				end
				if (not target) or target:isDead() then return false end
				player:removeQinggangTag(damage.card)
				local newdamage = damage
				newdamage.to = target
				newdamage.transfer = true
				room:damage(newdamage) -- 未处理胆守 
				target:drawCards(1) 
				room:setPlayerFlag(target, "luaaoshuNull")
				room:askForUseCard(target, "TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand", "@LuaQiyuan", -1, sgs.Card_MethodUse, false)
				room:setPlayerFlag(target, "-luaaoshuNull")
				return true				
			end
		end
	end ,
	can_trigger = function(self, player)
		return player
	end
}
daiyousei:addSkill(Yizuo)
daiyousei:addSkill(LuaYizuo)
daiyousei:addSkill(LuaQiyuan)

daiyouseiA:addSkill(Yizuo)
daiyouseiA:addSkill(LuaYizuo)
daiyouseiA:addSkill(LuaQiyuan)

daiyouseiB:addSkill(Yizuo)
daiyouseiB:addSkill(LuaYizuo)
daiyouseiB:addSkill(LuaQiyuan)

daiyouseiC:addSkill(Yizuo)
daiyouseiC:addSkill(LuaYizuo)
daiyouseiC:addSkill(LuaQiyuan)

daiyouseiD:addSkill(Yizuo)
daiyouseiD:addSkill(LuaYizuo)
daiyouseiD:addSkill(LuaQiyuan)

Luasishu2 = sgs.CreateTriggerSkill{
	name = "#Luasishu2" ,
	events = {sgs.EventPhaseStart} ,
	frequency = sgs.Skill_Compulsory ,
	on_trigger = function(self, event, player, data)
		if player:getPhase() == sgs.Player_RoundStart and player:getMark("@Luasishu") > 0  then
			local count = player:getLostHp() + 1 
			local room = player:getRoom()
			for i = 1, count, 1 do
				local card_id = room:drawCard()
				local card = sgs.Sanguosha:getCard(card_id)
				local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_SHOW, player:objectName(), "", self:objectName(), "")
				room:moveCardTo(card, player, sgs.Player_PlaceTable, reason, true)
				room:getThread():delay()
				if not card:isKindOf("TrickCard") then
					room:throwCard(card_id, nil)
					room:setEmotion(player, "bad")
				else
					room:obtainCard(player, card_id)
					room:setEmotion(player, "good")
				end
			end
			player:loseAllMarks("@Luasishu")
		end
	end
}

LuasishuCard = sgs.CreateSkillCard{
	name = "LuasishuCard",
	target_fixed = false,
	will_throw = false,
	filter = function(self, targets, to_select)
		return to_select:objectName() ~= sgs.Self:objectName()
	end,
	feasible = function(self, targets)
		return #targets == 1
	end,
	on_effect = function(self, effect) 
		local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_GIVE, effect.from:objectName(), effect.to:objectName(), "Luasishu", "")
		local room = effect.to:getRoom()
		room:obtainCard(effect.to, self, reason, false)	
		local phase = effect.from:getMark("qiaobianPhase")
		local old_phase = effect.to:getPhase()
		
		local thread = room:getThread()
		effect.to:setPhase(phase)
		room:broadcastProperty(effect.to, "phase")
		if not thread:trigger(sgs.EventPhaseStart, room, effect.to) then
			thread:trigger(sgs.EventPhaseProceeding, room, effect.to)
		end
		thread:trigger(sgs.EventPhaseEnd, room, effect.to)
		effect.to:setPhase(old_phase)
		room:broadcastProperty(effect.to, "phase")

	end,
}
LuasishuVS = sgs.CreateViewAsSkill{
	name = "Luasishu",
	n = 1,
	view_filter = function(self, selected, to_select)
		return to_select:isKindOf("TrickCard")
	end ,	
	view_as = function(self, cards)
		if #cards == 0 then return false end
		local card_1 =  LuasishuCard:clone()
		for _, c in ipairs(cards) do
			card_1:addSubcard(c)
		end
		return card_1		
	end,
	enabled_at_play = function(self, player)
		return false
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@Luasishu"
	end
}
Luasishu = sgs.CreateTriggerSkill{
	name = "Luasishu",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseChanging},
	view_as_skill = LuasishuVS,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local change = data:toPhaseChange()
		local nextphase = change.to
		room:setPlayerMark(player, "qiaobianPhase", nextphase)
		local index = 0
		if nextphase == sgs.Player_Judge then
			index = 1
		elseif nextphase == sgs.Player_Draw then
			index = 2
		elseif nextphase == sgs.Player_Play then
			index = 3
		elseif nextphase == sgs.Player_Discard then
			index = 4
		elseif nextphase == sgs.Player_Finish then
			index = 5	
		elseif nextphase == sgs.Player_Start then
			index = 6	
		end
		if index > 0 then
			if not player:isSkipped(nextphase) and room:askForUseCard(player, "@Luasishu", "sishu", index) then				
				player:skip(nextphase)
				player:gainMark("@Luasishu", 1)
			end
		end
		return false
	end,
	can_trigger = function(self, target)
		if target then
			if target:hasSkill(self:objectName()) and target:isAlive() then
				return not target:isKongcheng()
			end
		end
		return false
	end
}
koakuma:addSkill(Luasishu)
koakuma:addSkill(Luasishu2)

koakumaA:addSkill(Luasishu)
koakumaA:addSkill(Luasishu2)

koakumaB:addSkill(Luasishu)
koakumaB:addSkill(Luasishu2)

Luabaochun = sgs.CreateFilterSkill{
	name = "Luabaochun",	
	view_filter = function(self, to_select)
		local room = sgs.Sanguosha:currentRoom()
		local place = room:getCardPlace(to_select:getEffectiveId())
		return (to_select:getSuit() == sgs.Card_Heart) and (place == sgs.Player_PlaceHand)
	end,	
	view_as = function(self, card)
		local slash = sgs.Sanguosha:cloneCard("peach", card:getSuit(), card:getNumber())
		slash:setSkillName(self:objectName())
		local _card = sgs.Sanguosha:getWrappedCard(card:getId())
		_card:takeOver(slash)
		_card:setModified(true)
		return _card
	end
}
luajingzhe = sgs.CreateTriggerSkill{
	name = "luajingzhe" ,
	events = {sgs.HpRecover, sgs.EventPhaseChanging} , 
	global = true,
	on_trigger = function(self, event, player, data)
		if event == sgs.HpRecover then
			--local recover = data:toRecover()
			local room = player:getRoom()
			local recover = data:toRecover()
			for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				local phase = p:getPhase()
				if phase ~= sgs.Player_NotActive then 
					if player:isAlive() then
						player:addMark("luajingzheP")
					end  
				end 
			end
		elseif event == sgs.EventPhaseChanging then 
			local room0 = player:getRoom()
			local LilyWhite = room0:findPlayerBySkillName("luajingzhe")
			local change = data:toPhaseChange()
			if LilyWhite and LilyWhite:isAlive() then
				local room = LilyWhite:getRoom()
				if change.to == sgs.Player_NotActive and player:objectName() == LilyWhite:objectName() and room:getCurrent():objectName() == LilyWhite:objectName() then
					if room:askForSkillInvoke(LilyWhite, "luajingzhe") then
						if not LilyWhite:isWounded() then 
							LilyWhite:drawCards(1)
							room:setPlayerProperty(LilyWhite, "maxhp", sgs.QVariant(LilyWhite:getMaxHp() + 1))
						end 
						for _, _playerX in sgs.qlist(room:getAlivePlayers()) do 
							if _playerX:getMark("luajingzheP") > 0 then   
								local hasUsed = false
								local duel = sgs.Sanguosha:cloneCard("duel", sgs.Card_NoSuit, 0)
								duel:setSkillName(self:objectName())						
								local playerdata = sgs.QVariant()
								playerdata:setValue(_playerX)
								room:setTag("luajingzheTarget", playerdata)							
								local players = sgs.SPlayerList()
								for _, _playerY in sgs.qlist(room:getOtherPlayers(_playerX)) do
									if not _playerY:isCardLimited(duel, sgs.Card_MethodUse) then
										players:append(_playerY)
									end
								end	
								if _playerX:isAlive() and _playerX:getHp() > 0 and not _playerX:isCardLimited(duel, sgs.Card_MethodUse) and players:length() > 0 then
									local target = room:askForPlayerChosen(LilyWhite, players, "luajingzhe", "luajingzheD", true, true) 
									if target then 
										room:useCard(sgs.CardUseStruct(duel, _playerX, target))	
										hasUsed = true
									end 
								end
								room:removeTag("luajingzheTarget")	
								if not hasUsed then 
									duel:deleteLater()
								end 
							end 
						end  
					end 
					for _, _playerX in sgs.qlist(room:getAlivePlayers()) do 
						if _playerX:getMark("luajingzheP") > 0 then   
							room:setPlayerMark(_playerX, "luajingzheP", 0)
						end 
					end 
				end
			end 
		end
		return false
	end
}
luaxinshengcard = sgs.CreateSkillCard{
	name = "luaxinsheng",	
	filter = function(self, targets, to_select)
		return not (#targets > 0 or to_select:objectName() == sgs.Self:objectName() or not to_select:isWounded())
	end,	
	on_use = function(self, room, source, targets)		
		local recov = sgs.RecoverStruct()
		recov.recover = 1
		recov.who = source
		room:setEmotion(targets[1], "recover")
		room:recover(targets[1], recov)
	end
}

luaxinshengViewAsSkill = sgs.CreateViewAsSkill{
	name = "luaxinsheng",	
	n = 0,	
	view_filter = function()
		return false
	end,	
	view_as = function()
		return luaxinshengcard:clone()
	end,	
	enabled_at_play = function()
		return false
	end,
	enabled_at_response = function(self, player, pattern)
        return pattern == "@@luaxinsheng"
	end,

}

luaxinsheng = sgs.CreateTriggerSkill{
	name = "luaxinsheng",
	events = {sgs.PreHpRecover},
	view_as_skill = luaxinshengViewAsSkill,
	frequency = sgs.Skill_NotFrequent,
        on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:getHp() > 0 then
			if (not room:askForSkillInvoke(player, "luaxinsheng", data)) then return false end
			if room:askForUseCard(player, "@@luaxinsheng", "@luaxinsheng") then
				return true
			end
		end    
	end
}
white:addSkill(Luabaochun)
white:addSkill(luajingzhe)
white:addSkill(luaxinsheng)

whiteA:addSkill(Luabaochun)
whiteA:addSkill(luajingzhe)
whiteA:addSkill(luaxinsheng)

whiteC:addSkill(Luabaochun)
whiteC:addSkill(luajingzhe)
whiteC:addSkill(luaxinsheng)

whiteD:addSkill(Luabaochun)
whiteD:addSkill(luajingzhe)
whiteD:addSkill(luaxinsheng)

luayigongcard = sgs.CreateSkillCard{
	name = "luayigong",
	target_fixed = false,
	filter = function(self, targets, to_select)
		if to_select:objectName() ~= sgs.Self:objectName() then
			return #targets < 1
		end
		return false
	end,
	on_effect = function(self, effect)
		local source = effect.from
		local room = source:getRoom()

		local tag = sgs.QVariant()
		tag:setValue(effect.to)
		room:setTag("luayigongTarget", tag)
		local card_id = room:askForCardChosen(source, source, "e", self:objectName())
		local card = sgs.Sanguosha:getCard(card_id)
		local place = room:getCardPlace(card_id)
		local equip_index = -1
		if place == sgs.Player_PlaceEquip then
			local equip = card:getRealCard():toEquipCard()
			equip_index = equip:location()
		end
		local tos = sgs.SPlayerList()
		local list = room:getAlivePlayers()
		for _,p in sgs.qlist(list) do
			if equip_index ~= -1 then
				if (not p:getEquip(equip_index)) and p:objectName() == effect.to:objectName() then
					tos:append(p)
				end
			end
		end
		local to = room:askForPlayerChosen(source, tos, "luayigong")
		if to then
			local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, source:objectName(), self:objectName(), "")
			room:moveCardTo(card, source, to, place, reason)
			to:drawCards(1)
            source:drawCards(1)
		end
		room:removeTag("luayigongTarget")
	end
}
luayigong = sgs.CreateZeroCardViewAsSkill{
	name = "luayigong",
	
	view_as = function()
		return luayigongcard:clone()
	end,

	enabled_at_play = function(self, player)
		return (not player:getEquips():isEmpty()) and player:usedTimes("#luayigong") < 2
	end
}

luatianqin = sgs.CreateTriggerSkill{
	name = "luatianqin",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if player:getPhase() == sgs.Player_Play and room:getCurrent():objectName() == player:objectName()
			and player:getEquips():length() > 2 then
			for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				if room:askForSkillInvoke(player, self:objectName()) and room:askForDiscard(player, self:objectName(), 1, 1, true, true) then
					local judge = sgs.JudgeStruct()
					judge.pattern = ".|spade"
					judge.good = true
					judge.reason = "luatianqin"
					judge.who = player
					room:judge(judge)
					if judge:isGood() then
						local target = room:askForPlayerChosen(p, room:getAlivePlayers(), "luatianqin", "luatianqin2", true, true)
						if target then
							room:damage(sgs.DamageStruct(self:objectName(), p, target, 2, sgs.DamageStruct_Thunder))
						end
					end
				end
			end
		end
	end
}
luaxinjiang = sgs.CreateTriggerSkill{
	name = "luaxinjiang",
	global = true,
	events = {sgs.TurnStart},
	frequency = sgs.Skill_Frequent,
	on_trigger = function(self, event, player, data, room)
		if room:getCurrent():isLord() and player:getMark("@extra_turn") == 0 and room:getCurrent():objectName() == player:objectName() then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName("luaxinjiang")) do
				local discard_ids = room:getDrawPile()
				local trickcard = sgs.IntList()
				for _, id in sgs.qlist(discard_ids) do
					local card = sgs.Sanguosha:getCard(id)
					if card:isKindOf("EquipCard") then
						trickcard:append(id)
					end
				end
				if trickcard:length() > 0 and room:askForSkillInvoke(p2, self:objectName(),data) then
					room:fillAG(trickcard, p2)
					local card_id = room:askForAG(p2, trickcard, false, "luaxinjiang")
					local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
					dummy_0:addSubcard(card_id)
					room:clearAG()
					p2:obtainCard(dummy_0)
				end
			end
		end
	end
}
benben:addSkill(luayigong)
benben:addSkill(luatianqin)
benben:addSkill(luaxinjiang)

weijingcard = sgs.CreateSkillCard{
	name = "luaweijing",
	will_throw = false,
	on_effect = function(self,effect)
		effect.from:drawCards(1)
		local room = effect.from:getRoom()
		local card = sgs.Sanguosha:getCard(self:getSubcards():at(0))
		if card:getSuit() == sgs.Card_Heart then
			local dummy = sgs.Sanguosha:cloneCard("gaina")
			dummy:addSubcards(self:getSubcards())
			dummy:setSkillName(self:objectName())
			room:useCard(sgs.CardUseStruct(dummy, effect.from, effect.to))
		else
			local dummy = sgs.Sanguosha:cloneCard("gainb")
			dummy:addSubcards(self:getSubcards())
			dummy:setSkillName(self:objectName())
			room:useCard(sgs.CardUseStruct(dummy, effect.from, effect.to))
		end
	end
}
luaweijingVS = sgs.CreateViewAsSkill{
	name = "luaweijing" ,
	n = 1 ,
	view_filter = function(self, selected, to_select)
		if #selected ~= 0 then return false end
		return (to_select:getSuit() == sgs.Card_Heart or to_select:getSuit() == sgs.Card_Spade ) 
	end ,
	view_as = function(self, cards)
		if #cards ~= 1 then return nil end
		local luaweijing_Card2 = weijingcard:clone()
		luaweijing_Card2:addSubcard(cards[1])
		return luaweijing_Card2
	end ,
	enabled_at_play = function()
		return false
	end ,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luaweijing"
	end
}
luaweijing = sgs.CreateTriggerSkill {
	name = "luaweijing",
	view_as_skill = luaweijingVS,
	events = { sgs.EventPhaseStart, sgs.Damaged },
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			if player:getPhase() ~= sgs.Player_Finish then
				return false
			end
		end
		room:askForUseCard(player, "@@luaweijing", "@luaweijing")
	end
}
luaweijing2 = sgs.CreateTriggerSkill{
	name = "#luaweijing" ,
	frequency = sgs.Skill_Compulsory ,
	global = true,
	events = {sgs.BeforeCardsMove} ,
	on_trigger = function(self, event, player, data)
		if event == sgs.BeforeCardsMove then 
			local move = data:toMoveOneTime()
			local place = move.to_place
			if move.to and (move.to:objectName() == player:objectName()) and move.reason.m_skillName ~= "luaweijing"
				and move.to:containsTrick("gaina") and (place == sgs.Player_PlaceHand or place == sgs.Player_PlaceEquip) then
				--local i = 0
				-- local weijing_card = move.card_ids
				-- -- local original_move = move.card_ids
				-- for _,card_id in sgs.qlist(move.card_ids) do
					-- move.from_places:removeAt(i)
					-- i = i + 1
				-- end	
				--if i > 0 then
				local room = player:getRoom()
				-- for _, meili in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
					-- if meili and meili:isAlive() and meili:hasSkill("luaweijing") then 						
						-- local ids = weijing_card
						-- local moveX = sgs.CardsMoveStruct()
						-- moveX.card_ids = ids
						-- moveX.from = player
						-- moveX.to = meili
						-- moveX.to_place = sgs.Player_PlaceHand
						-- moveX.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, player:objectName(), "luaweijing", "")
						-- room:moveCardsAtomic(moveX, true)			
					-- end 
				-- end 
				move.card_ids = sgs.IntList()
				local abc = sgs.IntList()
				for _,card_X in sgs.qlist(player:getJudgingArea()) do  --getEngineCard
					if card_X:isKindOf("gaina") then abc:append(card_X:getId()) end
				end		
				room:fillAG(abc)
				local card_id = room:askForAG(player, abc, false, "luaweijing")
				--card_id = sgs.Sanguosha:getEngineCard(card_id):getId()
				abc:removeOne(card_id)
				room:takeAG(player, card_id, false)
				room:clearAG()		
				local moveA = sgs.CardsMoveStruct()
				moveA.card_ids = sgs.IntList()
				moveA.from = nil
				moveA.from_place = sgs.Player_PlaceJudge
				moveA.card_ids:append(card_id)
				moveA.to = player
				moveA.to_place = sgs.Player_PlaceHand
				moveA.reason = sgs.CardMoveReason(move.reason.m_reason, player:objectName(), "luaweijing", "")
				room:moveCardsAtomic(moveA, true)	--我也很尽力了，只能伪实现
				data:setValue(move)		
				return true				
			elseif move.from and (move.from:objectName() == player:objectName()) and move.reason.m_skillName ~= "luaweijing"
				and move.from:containsTrick("gainb") then
				if bit32.band(move.reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON) == sgs.CardMoveReason_S_REASON_DISCARD then
					local room = player:getRoom()
					local i = 0
					local old_card_ids = {}
					for _,card_idX in sgs.qlist(move.card_ids) do
						table.insert(old_card_ids, card_idX)
					end
					for _, card_idY in ipairs(old_card_ids) do
						if room:getCardOwner(card_idY):objectName() == move.from:objectName() then
							local place2 = move.from_places:at(i)
							if place2 == sgs.Player_PlaceHand or place2 == sgs.Player_PlaceEquip then
								move.card_ids:removeOne(card_idY)
								move.from_places:removeAt(i)
							end
						end
						i = i + 1
					end
					move.card_ids = sgs.IntList()
					local abc = sgs.IntList()
					for _,card_X in sgs.qlist(player:getJudgingArea()) do  --getEngineCard
						if card_X:isKindOf("gainb") then abc:append(card_X:getId()) end

					end
					room:fillAG(abc)
					local card_id = room:askForAG(player, abc, false, "luaweijing")
					--card_id = sgs.Sanguosha:getEngineCard(card_id):getId()
					abc:removeOne(card_id)
					room:takeAG(player, card_id, false)
					room:clearAG()
					local moveA = sgs.CardsMoveStruct()
					moveA.card_ids = sgs.IntList()
					moveA.from = nil
					moveA.from_place = sgs.Player_PlaceJudge
					moveA.card_ids:append(card_id)
					moveA.to = move.to
					moveA.to_place = move.to_place
					moveA.reason = sgs.CardMoveReason(move.reason.m_reason, player:objectName(), "luaweijing", "")
					room:moveCardsAtomic(moveA, true)	--我也很尽力了，只能伪实现
					data:setValue(move)
					return true
				end
			end
		end 
		return false
	end,
	can_trigger = function(self,target)
		if target and target:isAlive() then			
			if (target:containsTrick("gaina") or target:containsTrick("gainb")) then return true end
		end
		return false
	end,
}
sp_meili:addSkill(luaweijing)
sp_meili:addSkill(luaweijing2)

luaxinshiCard = sgs.CreateSkillCard{
	name = "luaxinshi" ,
	will_throw = true,
	filter = function(self, targets, to_select)
		return (#targets <= 1)
	end ,
	on_use = function(self, room, source, targets)
		if #targets == 1 then targets[2] = targets[1] end
		local card_ids = sgs.IntList()
		if room:getDiscardPile():isEmpty() then return false end
		for i = 0, 3 do
			card_ids:append(room:getDiscardPile():at(i))
			if room:getDiscardPile():length() < i + 1 then break end
		end
		local pq = 1
		while (not card_ids:isEmpty()) do
			room:fillAG(card_ids)
			local card_id = room:askForAG(targets[pq], card_ids, false, self:objectName())
			card_ids:removeOne(card_id)
			room:clearAG()
			local card = sgs.Sanguosha:getCard(card_id)

			local move = sgs.CardsMoveStruct()
			move.from = nil
			move.from_place = sgs.Player_DiscardPile
			move.to = targets[pq]
			move.to_place = sgs.Player_PlaceHand
			move.card_ids = sgs.IntList()
			move.card_ids:append(card_id)
			move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, "luaxinshi")
			room:moveCardsAtomic(move, true)
			local ab_card_ids = sgs.IntList()
			for _,id in sgs.qlist(card_ids) do
				local c = sgs.Sanguosha:getCard(id)
				if c:isBlack() ~= card:isBlack() then
					ab_card_ids:append(id)
				end
			end
			card_ids = ab_card_ids 
			pq = pq + 1
			room:broadcastInvoke("clearAG")
		end
    end
}
luaxinshiVS = sgs.CreateZeroCardViewAsSkill{
	name = "luaxinshi", 
	response_pattern = "@@luaxinshi", 
	view_as = function()
		return luaxinshiCard:clone()
	end
}


luaxinshi = sgs.CreateTriggerSkill{
	name = "luaxinshi" ,
	events = {sgs.Damaged} ,
	view_as_skill = luaxinshiVS,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local damage = data:toDamage()
		if damage.damage == 0 then return false end
		local players = room:getOtherPlayers(player)
		--for i = 0, damage.damage - 1, 1 do
			if room:askForUseCard(player, "@@luaxinshi", "@luaxinshi") then 	
				--room:setPlayerFlag(player, "luaxinshiused")
			end 
		--end

	end,
	can_trigger = function(self,target)
		if target and target:isAlive() then			
			return target:hasSkill("luaxinshi")
		end
		return false
	end,
}


luaguiqiaoCard = sgs.CreateSkillCard{
	name = "luaguiqiao" ,
	will_throw = true,
	target_fixed = true, 
	on_use = function(self, room, source, targets)
		if source:getMark("luaguiqiao") == 1 then 
			local x = self:getSubcards():length()
			local target = room:getTag("luaguiqiaoTarget")
			if target and target:toPlayer() then 
				target = target:toPlayer()
				room:setPlayerProperty(target, "hp", sgs.QVariant(x)) -- "没准勾玉就是幽灵的手办之类的玩意儿。"
				room:damage(sgs.DamageStruct(self:objectName(), nil, source))
			end 									
		end 
    end
}

luaguiqiaoVS = sgs.CreateViewAsSkill{
	name = "luaguiqiao",	
	n = 999,	
	view_filter = function(self, selected, to_select)
		if sgs.Self:getMark("luaguiqiao") == 1 then 
			return (not sgs.Self:isJilei(to_select)) and to_select:isRed()
		elseif sgs.Self:getMark("luaguiqiao") == 2 then 
			if (sgs.Self:getMark("luaguiqiao9") == 0) then return false end 
			local id = sgs.Self:getMark("luaguiqiao9") - 1
			local card = sgs.Sanguosha:getCard(id)
			local ori = card:objectName()
			local a = sgs.Sanguosha:cloneCard(ori, sgs.Card_SuitToBeDecided, -1)
			a:addSubcard(to_select)
			return a:isAvailable(sgs.Self)
		end 
	end,	
	view_as = function(self, cards)
		if #cards < 1 then return nil end
		if (sgs.Self:getMark("luaguiqiao") == 2) then 
			if (#cards > 1) then return end 
			if (sgs.Self:getMark("luaguiqiao9") == 0) then return false end 
			local id = sgs.Self:getMark("luaguiqiao9") - 1
			local card = sgs.Sanguosha:getCard(id)
			local ori = card:objectName()
			local a = sgs.Sanguosha:cloneCard(ori, sgs.Card_SuitToBeDecided, -1)
			for _,cardA in pairs(cards) do
				a:addSubcard(cardA)
			end
			a:setSkillName("moshi")
			return a				
		end 
		local luaguiqiao = luaguiqiaoCard:clone()
		for _, c in ipairs(cards) do			
			luaguiqiao:addSubcard(c)
		end
		return luaguiqiao
	end,	
	enabled_at_play = function()
		return false
	end,
	enabled_at_response = function(self, player, pattern)
        return pattern == "@@luaguiqiao"
	end,
}

luaguiqiao = sgs.CreateTriggerSkill{
	name = "luaguiqiao",
	global = true,
	events = {sgs.Dying, sgs.EventPhaseChanging, sgs.CardUsed},
	view_as_skill = luaguiqiaoVS,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				if (player:getMark("luaguiqiao9") == 0) then return end 
				for _, p in sgs.qlist(room:findPlayersBySkillName("luaguiqiao")) do		
					local room2 = p:getRoom()
					local to_give = room2:askForCard(p, ".black", "@luaguiqiaok", data, self:objectName())
					if to_give then 
						room2:throwCard(to_give, p, p)
						player:drawCards(1)
						local xo = room:getLord():getMark("@clock_time") + 1
						room2:setPlayerMark(p, "guiqiaotime", xo)

						room2:setPlayerMark(player, "luaguiqiao", 2)
						if room:askForUseCard(player, "@@luaguiqiao", "@luaguiqiaoXX") then 
							if (player:getMark("luaguiqiao8") ~= 0) then 
								room:setPlayerMark(player, "luaguiqiao9", player:getMark("luaguiqiao8"))
								room:askForUseCard(player, "@@luaguiqiao", "@luaguiqiaoYY") 
							end 
						end
					end
					room2:setPlayerMark(player, "luaguiqiao", 0)
					room2:setPlayerMark(player, "luaguiqiao9", 0)
					room2:setPlayerMark(player, "luaguiqiao8", 0)
				end 
			end 
        elseif (event == sgs.CardUsed) and (player:getPhase() == sgs.Player_Play) then 
			local use = data:toCardUse()
			if use.from:objectName() ~= player:objectName() then return false end 
			for _, p in sgs.qlist(room:findPlayersBySkillName("luaguiqiao")) do
				local xo = room:getLord():getMark("@clock_time") + 1
				if p:getMark("guiqiaotime") ~= xo then
					if (use.card:isKindOf("SkillCard") or use.card:isKindOf("EquipCard")) then return false end 
					if use.card:isKindOf("TrickCard") and not use.card:isNDTrick() then return false end 
					if (player:getMark("luaguiqiao9") == 0) then 
						local id = use.card:getId() + 1
						room:setPlayerMark(player, "luaguiqiao9", id)
					else
						if (player:getMark("luaguiqiao8") == 0) then 
							local id = use.card:getId() + 1
							room:setPlayerMark(player, "luaguiqiao8", id)
						end 
					end 
				end
			end 	
		elseif event == sgs.Dying and player:hasSkill("luaguiqiao") then 
			for _, p2 in sgs.qlist(room:findPlayersBySkillName("luaguiqiao")) do	
				local xo = room:getLord():getMark("@clock_time") + 1
				if p2:getMark("guiqiaotime2") ~= xo then 			
					local room2 = p2:getRoom()
					local dying = data:toDying()
					local _player = dying.who
					local p = _player
					local playerdata = sgs.QVariant()
					playerdata:setValue(p)
					room2:setTag("luaguiqiaoTarget", playerdata)		
					room2:setPlayerMark(p2, "luaguiqiao", 1)
					if room2:askForUseCard(p2, "@@luaguiqiao", "@luaguiqiao") then 				
						room2:setPlayerMark(p2, "guiqiaotime2", xo)				
					end 		
					room2:setPlayerMark(p2, "luaguiqiao", 0)
					room2:removeTag("luaguiqiaoTarget")	
				end 
			end 
		end 

	end,
	can_trigger = function(self,target)
		return true
	end,
}

keine:addSkill(luaxinshi)
keine:addSkill(luaguiqiao)

--keineA:addSkill(luaxinshi)
--keineA:addSkill(luaguiqiao)

luashouyekCard = sgs.CreateSkillCard{
	name = "luashouyek" ,
	will_throw = true,
	target_fixed = true, 
	on_use = function(self, room, source, targets)
		local id = self:getSubcards():at(0)
		room:writeToConsole("luashouyek test" .. id);
		room:setPlayerMark(source, "luashouyekID", id + 1)
		 
	end
}
luashouyekVS = sgs.CreateViewAsSkill{
	name = "luashouyek", 
	n = 1,  
	view_filter = function(self, selected, to_select)
		return #selected < 1 and (to_select:isKindOf("BasicCard") or (to_select:isKindOf("TrickCard") and not to_select:isKindOf("AOE")))
	end, 
	view_as = function(self, cards) 
		if #cards == 1 then 
			local card = luashouyekCard:clone()
			for _, cd in ipairs(cards) do
				card:addSubcard(cd)
			end	
			return card		
		end 
	end, 
	enabled_at_play = function(self, player)
		return false
	end,
	enabled_at_response = function(self, player, pattern)
        return pattern == "@@luashouyek"
	end,
}
luashouyek = sgs.CreateTriggerSkill{
	name = "luashouyek",
	global = true, 
	events = {sgs.TurnStart}, 
	view_as_skill = luashouyekVS,
	--frequency = sgs.Skill_Compulsory,
	on_trigger = function(self, event, player, data, room) 
		
		for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
			for _, p in sgs.qlist(room:getAllPlayers()) do
				if not p:hasSkill("luashouyej") then room:writeToConsole("jiantin test" .. p:objectName()); room:attachSkillToPlayer(p, "luashouyej") end
			end
		end
		if room:getCurrent():isLord() and player:getMark("@extra_turn") == 0 and room:getCurrent():objectName() == player:objectName() then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName("luashouyek")) do 
				room:setPlayerMark(p2, "luashouyekID", -1)
				if room:askForUseCard(p2, "@@luashouyek", "@luashouyek") then 	
					p2:addMark("luatouchui", room:getAlivePlayers():length())
				end 
			end
		end
	end
} 

luatouchuiCard = sgs.CreateSkillCard{
	name = "luatouchui",
	filter = function(self, targets, to_select)
		return (#targets < sgs.Self:getMark("luatouchui")) and (to_select:isKongcheng() or to_select:getEquips():isEmpty() or to_select:getJudgingArea():isEmpty())
	end,
	on_effect = function(self, effect) 
		local room = effect.to:getRoom()
		local choices = {}
		
		if effect.to:isKongcheng() then
			table.insert(choices, "PlaceHand")
		end 
		if effect.to:getEquips():isEmpty() then
			table.insert(choices, "PlaceEquip") 
		end
		if effect.to:getJudgingArea():isEmpty() then
			table.insert(choices, "PlaceDelayedTrick") 
		end 
		local choice = room:askForChoice(effect.from, "luatouchui", table.concat(choices, "+")) 
		local card2
		if choice == "PlaceEquip" then
			card2 = room:askForCard(effect.from, "EquipCard|.|.|.", "@luatouchuiA", sgs.QVariant(), sgs.Card_MethodNone) 
		elseif choice == "PlaceDelayedTrick" then
			card2 = room:askForCard(effect.from, "DelayedTrick|.|.|.", "@luatouchuiB", sgs.QVariant(), sgs.Card_MethodNone)
		elseif choice == "PlaceHand" then
			card2 = room:askForCard(effect.from, ".|.|.|hand", "@luatouchuiC", sgs.QVariant(), sgs.Card_MethodNone)
		end     
		local to = effect.to
		if to and card2 then
			local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_TRANSFER, effect.from:objectName(), self:objectName(), "")
			if choice == "PlaceDelayedTrick" then
				room:moveCardTo(card2, effect.from, to, sgs.Player_PlaceDelayedTrick, reason) 
			elseif choice == "PlaceEquip" then
				room:moveCardTo(card2, effect.from, to, sgs.Player_PlaceEquip, reason)
			elseif choice == "PlaceHand" then
				room:moveCardTo(card2, effect.from, to, sgs.Player_PlaceHand, reason)					
			end 
			room:damage(sgs.DamageStruct("luatouchui", effect.from, effect.to, 1))
		end  
	end
}

luatouchuiVS = sgs.CreateZeroCardViewAsSkill{
	name = "luatouchui",
	response_pattern = "@@luatouchui",
	view_as = function(self, cards)
		return luatouchuiCard:clone()
	end
}
luatouchui = sgs.CreateTriggerSkill{
	name = "luatouchui",
	global = true,
	events = {sgs.EventPhaseChanging},
	view_as_skill = luatouchuiVS,
	on_trigger = function(self, event, player, data, room)
		local change = data:toPhaseChange()
		if change.to ~= sgs.Player_NotActive then return false end
		if room:getCurrent():getNextAlive():isLord() and not room:getCurrent():isLord() and room:getCurrent():objectName() == player:objectName() then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName("luatouchui")) do 
				if p2:getMark("luatouchui") > 0 and room:askForUseCard(p2, "@@luatouchui", "@luatouchui") then 	
				
				end 
			end
		end
	end
}
keineA:addSkill(luashouyek) 
keineA:addSkill(luatouchui)
 
keineB:addSkill(luashouyek) 
keineB:addSkill(luatouchui)

lualingxiCard = sgs.CreateSkillCard{
	name = "lualingxi",
	target_fixed = false,  --slashTargetFixToOne
	will_throw = false,
	handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select) 
		return #targets == 0
	end ,
	on_use = function(self, room, source, targets)
		local target = targets[1]
		local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_GIVE, source:objectName(), target:objectName(), "lualingxi","")
		room:moveCardTo(self, target, sgs.Player_PlaceHand, reason) 
		if target:hasSkill("luahuixuan") then 
			room:acquireSkill(target, "luashouhu", true) 
		elseif target:hasSkill("luashouhu") then 
			room:acquireSkill(target, "luahuixuan", true) 
		else
			local choice = room:askForChoice(source, "lualingxi","luahuixuan+luashouhu")
			if choice == "luahuixuan" then room:acquireSkill(target, "luahuixuan", true) end 
			if choice == "luashouhu" then room:acquireSkill(target, "luashouhu", true) end
		end 	
	end
}
lualingxiVS = sgs.CreateOneCardViewAsSkill{
	name = "lualingxi",
	--filter_pattern = ".|.|.|hand",
	view_filter = function(self, card) 
    	return card:isKindOf("BasicCard")
	end,
	view_as = function(self, originalCard)
		local caihuo_card = lualingxiCard:clone()
		caihuo_card:addSubcard(originalCard:getId())
		return caihuo_card
	end,
	enabled_at_play = function(self, player)
		return false
	end, 
	enabled_at_response = function(self, player, pattern)
		return string.startsWith(pattern, "@@lualingxi")
	end
}

lualingxi = sgs.CreateTriggerSkill{
	name = "lualingxi",
	view_as_skill = lualingxiVS, 
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			if player:getPhase() ~= sgs.Player_RoundStart and player:getPhase() ~= sgs.Player_NotActive
				and room:askForUseCard(player, "@@lualingxi", "@lualingxi") then 

			end
		end
	end
}

luahuixuan = sgs.CreateOneCardViewAsSkill{
	name = "luahuixuan", 
	view_filter = function(self, card)
		if sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_PLAY then
			local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
			slash:addSubcard(card:getEffectiveId())
			slash:setSkillName("luahuixuan")
			slash:deleteLater()
			return slash:isAvailable(sgs.Self) and card:isKindOf("BasicCard")
		end
		return card:isKindOf("BasicCard")
	end,
	view_as = function(self, originalCard)
		local Leishi_card = luahuixuanCard:clone()
		Leishi_card:addSubcard(originalCard:getId())
		return Leishi_card
	end, 
	enabled_at_play = function(self, player)
		return sgs.Slash_IsAvailable(player)
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "slash"
	end 
}
luahuixuanCard = sgs.CreateSkillCard{
	name = "luahuixuan",
	target_fixed = false,  --slashTargetFixToOne
	will_throw = false,
	handling_method = sgs.Card_MethodNone,
	filter = function(self, targets, to_select)
		if to_select:objectName() == sgs.Self:objectName() then return false end
		if sgs.Self:hasFlag("slashTargetFixToOne") then
			local target
			for _, p in sgs.qlist(sgs.Self:getSiblings()) do
				if p:hasFlag("SlashAssignee") then target = p end
			end
			if not target then return false end
			if to_select:objectName() ~= target:objectName() then return false end
		end
		local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_SuitToBeDecided, -1)
		slash:addSubcard(self:getSubcards():first())
		slash:setSkillName("luahuixuan")
		slash:deleteLater()
		if #targets >= 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, sgs.Self, slash) then return false end
		return sgs.Self:canSlash(to_select, slash, true)
	end,
	on_validate = function(self,carduse)
		local source = carduse.from
		local target = carduse.to:first()
		local room = source:getRoom()
		local card = sgs.Sanguosha:getCard(self:getSubcards():first())
		if source:canSlash(target, nil, true) then
			local slash = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
			slash:setSkillName(self:objectName())
			slash:addSubcard(card:getEffectiveId())
			source:drawCards(1)
			room:detachSkillFromPlayer(source, "luahuixuan")
			return slash
		end
	end,
	on_validate_in_response = function(self, user)
		local room = user:getRoom()
		local jink = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, -1)
		jink:addSubcard(self:getSubcards():first())
		user:drawCards(1) 
		room:detachSkillFromPlayer(user, "luahuixuan")
		jink:setSkillName(self:objectName())
		return jink
	end
}
luashouhu2 = sgs.CreateTriggerSkill{
	name = "#luashouhu", 
	global = true,
	events = {sgs.TargetConfirmed, sgs.CardFinished} ,
	on_trigger = function(self, event, player, data)
		if event == sgs.TargetConfirmed then 
			local room = player:getRoom()
			local use = data:toCardUse()
			if use.card:isKindOf("Slash") and use.from and use.to then
				for _, t in sgs.qlist(use.to) do
					local breaks = false
					local id = use.card:getId() + 2
					local tr = t:getRoom()
					local i = 1
					for r = 1, 19 do --我这叫优化，懂吗
						if (t:getMark("luashouhu" .. tostring(r)) == id) and (t:getMark("luashouhu" .. tostring(r + 1)) == 0)  then breaks = true end
					end 
					if not breaks then 
						while t:getMark("luashouhu" .. tostring(i)) ~= 0 do 
							i = i + 1 
						end
						
						local str = "luashouhu" .. "|" .. tostring(i) .. "|" .. tostring(id)
						local playerdata = sgs.QVariant() 
						playerdata:setValue(use.from)
						tr:setTag(str, playerdata)	
						tr:setPlayerMark(t, "luashouhu" .. tostring(i), id)
						--tr:writeToConsole("阿吽测试" .. i)
					end 
				end 
			end
			return false
		elseif event == sgs.CardFinished then 
			local room = player:getRoom()
			--room:writeToConsole("阿吽清除测试")
			local use = data:toCardUse()
			local card = use.card
			if card:isKindOf("Slash") then
				for _, p in sgs.qlist(room:getAllPlayers()) do
					local tr = p:getRoom()
					local id = card:getId() + 2
					--tr:writeToConsole("阿吽清除测试")
					for i = 1, 19 do --我这叫优化，懂吗
						if p:getMark("luashouhu" .. tostring(i)) == id then  
							tr:setPlayerMark(p, "luashouhu" .. tostring(i), 0)
							tr:removeTag("luashouhu" .. "|" .. tostring(i) .. "|" .. tostring(id))

						end 
					end 
				end 
			end			
		end 
	end 
}
luashouhu = sgs.CreateTriggerSkill{
	name = "luashouhu",
	global = true,
	frequency = sgs.NotFrequent,
	events = {sgs.CardAsked},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local pattern = data:toStringList()[1]
		local prompt = data:toStringList()[2]
		if (pattern ~= "jink") then return false end
		local i = 1
		while player:getMark("luashouhu" .. tostring(i)) ~= 0 do 
			i = i + 1 
		end 
		i = i - 1 --当前结算的一定是最新的【杀】,id必取最后一个
		if i == 0 then return false end 
		
		local id = player:getMark("luashouhu" .. tostring(i))
		local slasher = room:getTag("luashouhu" .. "|" .. tostring(i) .. "|" .. tostring(id)):toPlayer()

		for _, p in sgs.qlist(room:getAllPlayers()) do
			if p:hasFlag("luashouhuyx") then room:setPlayerFlag(p, "-luashouhuyx") end 		
		end 
		room:setPlayerFlag(player, "luashouhuyx")		
		for _, p in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
			if player:distanceTo(p) <= 1 and slasher and slasher:objectName() ~= p:objectName() then
				local playerdata = sgs.QVariant() --ai用
				playerdata:setValue(slasher)
				room:setTag("luashouhuy", playerdata)				
				if slasher:objectName() ~= p:objectName() and room:askForUseSlashTo(p, slasher, "luashouhuy", false) then
					local jink = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
					jink:setSkillName(self:objectName())
					room:provide(jink)
					room:removeTag("luashouhuy")
					room:detachSkillFromPlayer(p, "luashouhu")

					return true					
				end 
				room:removeTag("luashouhuy") 
			end 
		end
	end
} 
aunn:addSkill(lualingxi)
aunn:addSkill(luahuixuan)
aunn:addSkill(luashouhu2)
aunn:addSkill(luashouhu)

luamimengCard = sgs.CreateSkillCard{
	name = "luamimeng", 
	will_throw = false,
	filter = function(self, targets, to_select)
    	local slash = sgs.Sanguosha:cloneCard("ex_nihilo", sgs.Card_SuitToBeDecided, -1)
        slash:addSubcard(self:getSubcards():at(0))
        slash:deleteLater()
		if sgs.Self:isCardLimited(slash, sgs.Card_MethodUse) then return end 
		if to_select:isProhibited(to_select, slash, to_select:getSiblings()) then return false end
		return (#targets == 0)  
	end ,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		local card = self:getSubcards():at(0)	
		local slash = sgs.Sanguosha:cloneCard("ex_nihilo", sgs.Card_SuitToBeDecided, -1)
		slash:addSubcard(self:getSubcards():at(0))
		room:useCard(sgs.CardUseStruct(slash, effect.from, effect.to))

	end 
}
luamimeng = sgs.CreateOneCardViewAsSkill{
	name = "luamimeng",
	filter_pattern = "Jink",
	view_as = function(self,card)
		local skillcard = luamimengCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
	enabled_at_play = function(self,player)
		return not player:isKongcheng() and not player:hasUsed("#luamimeng")
	end,
}
luamimeng2 = sgs.CreateTriggerSkill{
	name = "#luamimeng",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.TargetConfirming, sgs.PreCardUsed},
	on_trigger = function(self, event, player, data)
        local room = player:getRoom()
		if event == sgs.PreCardUsed then 
			use = data:toCardUse()
			card = use.card
			if use.from:hasFlag("luamimengAOE") then
				use.to = sgs.SPlayerList()	
				use.to:append(use.from)
				room:setPlayerFlag(use.from, "-luamimengAOE")
			end 
			data:setValue(use)
			return false	
		end 
		return false		
	end,
	can_trigger = function(self, target)
		return target
	end
}
luaganmeng = sgs.CreateDistanceSkill{
	name = "luaganmeng",
	correct_func = function(self, from, to)
		if to:hasSkill("luaganmeng") then
			return 1
		end
	end,
}
luaemeng = sgs.CreateTriggerSkill{
	name = "luaemeng",
	frequency = sgs.Skill_Frequent,
	events = {sgs.CardUsed},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local use = data:toCardUse()
		if use.card and use.card:isKindOf("ExNihilo") and use.from and use.from:hasSkill("luaemeng") and room:askForSkillInvoke(player, self:objectName(), data) then
			local targets = sgs.SPlayerList()
			for _, target in sgs.qlist(room:getAlivePlayers()) do
				if not target:isNude() then
					targets:append(target)
				end
			end
			if not targets:isEmpty() then
				local target = room:askForPlayerChosen(player, targets, "luamimeng", "luamimengP", false, true)
				if target then
					local cardid = room:askForCardChosen(player, target, "h", "luamimeng1")
					room:obtainCard(player, cardid, false)
					local playerdata = sgs.QVariant() -- ai用
					playerdata:setValue(target)
					room:setTag("luamimengTarget", playerdata)
					local card = room:askForExchange(player, "luamimeng", 1, 1, true)
					room:obtainCard(target, card, false)
					if not target:isKongcheng() then
						cardid = room:askForCardChosen(player, target, "h", "luamimeng2")

						card = sgs.Sanguosha:getCard(cardid)
						if card:isKindOf("Jink") or card:isKindOf("Nullification") or card:isKindOf("sakura") or card:isKindOf("Collateral")
								or target:isCardLimited(card, sgs.Card_MethodUse) or target:isProhibited(target, card, target:getSiblings()) then
							room:obtainCard(player, cardid, false)
						else
							if card:targetFixed() then
								if not card:isKindOf("EquipCard") then room:setPlayerFlag(target, "luamimengAOE") end
								room:useCard(sgs.CardUseStruct(card, target, sgs.SPlayerList()))
							else
								room:useCard(sgs.CardUseStruct(card, target, target))
							end
						end
					end
					room:removeTag("luamimengTarget")
				end
			end
		end
	end
}

doremi:addSkill(luamimeng)
doremi:addSkill(luaemeng)
doremi:addSkill(luaganmeng)
doremi:addSkill(luamimeng2)
--[[
zhonggongCard = sgs.CreateSkillCard{
	name = "luazhonggong", 
	will_throw = false,
	filter = function(self, targets, to_select)		
		return (#targets == 0)  
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		local room2 = effect.to:getRoom()
		local cardid = self:getSubcards():at(0)		
		local card = sgs.Sanguosha:getCard(cardid)
		local bool = false
		if not effect.to:isCardLimited(card, sgs.Card_MethodUse) then 		
			local i = 1
			while effect.to:getMark("zhongzhuang" .. tostring(i)) ~= 0 do 
				i = i + 1 
			end 
			room2:setPlayerMark(effect.to, "zhongzhuang" .. tostring(i), cardid)	
			room:useCard(sgs.CardUseStruct(card, effect.to, effect.to))
			bool = true
		end 
		effect.from:drawCards(1)
	end 
}
luazhonggong = sgs.CreateOneCardViewAsSkill{
	name = "luazhonggong",
	filter_pattern = "EquipCard",
	view_as = function(self,card)
		local skillcard = zhonggongCard:clone()
		skillcard:addSubcard(card)
		return skillcard
	end,
}

zhongzhuang2 = sgs.CreateMaxCardsSkill{
	name = "#zhongzhuang", 
	frequency = sgs.Skill_Compulsory,
	extra_func = function(self, target)
		if target:hasSkill("zhongzhuang") and target:getEquips():length() > 0 then
			return 1
		else
			return 0
		end
	end
}
zhongzhuang3 = sgs.CreateTriggerSkill{
	name = "#zhongzhuang2",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DrawNCards},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		if player:hasSkill("zhongzhuang") and player:getEquips():length() > 2 then
			local count = data:toInt() + 1
			data:setValue(count)
		end
	end
}
zhongzhuang4 = sgs.CreateTargetModSkill{
	name = "#zhongzhuang3",
	frequency = sgs.Skill_Compulsory,
	pattern = "Slash",
	residue_func = function(self, player)
		if player:hasSkill("zhongzhuang") and player:getEquips():length() > 1 then
			return 1
		else
			return 0
		end
	end,
}
zhongzhuang6 = sgs.CreateDistanceSkill{
	name = "#zhongzhuang5",
	correct_func = function(self, from, to)
		if to:hasSkill("luazhonggong") then
			local x = 0
			for _, p in sgs.qlist(sgs.Self:getAliveSiblings()) do
				if p:hasSkill("zhongzhuang") then x = x + 1 end 
			end 
			return x
		end
	end,
}
zhongzhuang5 = sgs.CreateTriggerSkill{
	name = "#zhongzhuang4" ,
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.CardsMoveOneTime then
			local room = player:getRoom()
			local move = data:toMoveOneTime()
			if (move.from and move.from:objectName() == player:objectName() and move.from_places:contains(sgs.Player_PlaceEquip)) then 
				for _, id in sgs.qlist(move.card_ids) do
					for i = 1, 19 do --我这叫优化，懂吗
						if id == player:getMark("zhongzhuang" .. tostring(i))  then 
							room:setPlayerMark(player, "zhongzhuang" .. tostring(i), 0)							
						end 
					i = i + 1 
					end 
				end 	
				local bool = true
				for i = 1, 19 do --我这叫优化，懂吗
					if player:getMark("zhongzhuang" .. tostring(i)) ~= 0 then 
					bool = false
					end 
					i = i + 1 
				end 	
				if bool and player:hasSkill("zhongzhuang") then room:detachSkillFromPlayer(player, "zhongzhuang") end 
			elseif (move.to and move.to:objectName() == player:objectName() and move.to_place == sgs.Player_PlaceEquip) then
				for _, id in sgs.qlist(move.card_ids) do
					for i = 1, 19 do --我这叫优化，懂吗
						if id == player:getMark("zhongzhuang" .. tostring(i)) then 
							if not player:hasSkill("zhongzhuang") then 
								room:acquireSkill(player, "zhongzhuang")		
							end 
						end 
						i = i + 1 							
					end 
				end 
			end
		end 
	end 
}]]--
zhonggongCard = sgs.CreateSkillCard{
	name = "luazhonggong",
	filter = function(self, targets, to_select)
		return (#targets == 0)
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		if self:getSubcards():length() == 2 then
			local damage = sgs.DamageStruct()
			damage.from = effect.from
			damage.to = effect.to
			damage.damage = 1
			damage.nature = sgs.DamageStruct_Normal
			effect.from:getRoom():damage(damage)
		else
			local discard_ids = room:getDrawPile()
			local trickcard = sgs.IntList()
			for _, id in sgs.qlist(discard_ids) do
				local card = sgs.Sanguosha:getCard(id)
				if card:isKindOf("EquipCard") then
					trickcard:append(id)
				end
			end
			discard_ids = room:getDiscardPile()
			for _, id in sgs.qlist(discard_ids) do
				local card = sgs.Sanguosha:getCard(id)
				if card:isKindOf("EquipCard") then
					trickcard:append(id)
				end
			end
			if trickcard:length() > 0 then
				room:fillAG(trickcard, effect.from)
				local _data = sgs.QVariant()
				_data:setValue(effect.to)
				room:setTag("luazhonggongTP", _data)
				local card_id = room:askForAG(effect.from, trickcard, false, "luazhonggong")
				room:removeTag("luazhonggongTP")
				local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
				dummy_0:addSubcard(card_id)
				room:clearAG()
				effect.to:obtainCard(dummy_0)
			end
		end
	end

}
luazhonggong = sgs.CreateViewAsSkill{
	name = "luazhonggong",
	n = 3,
	view_filter = function(self, selected, to_select)
		return to_select:isKindOf("Slash")
	end,
	view_as = function(self, cards)
		if #cards == 0 then return end
		if #cards == 1 then return end
		local huisheng = zhonggongCard:clone()
		for _, c in ipairs(cards) do
			huisheng:addSubcard(c)
		end
		return huisheng
	end
}
luagongcheng = sgs.CreateTriggerSkill{
	name = "luagongcheng",
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if not p:hasSkill("luagongchengr") then room:writeToConsole("jiantin test" .. p:objectName()); room:attachSkillToPlayer(p, "luagongchengr") end
				end
			end
		end
	end
}

nitori:addSkill(luazhonggong)
nitori:addSkill(luagongcheng)

liuzhiCard = sgs.CreateSkillCard{
	name = "lualiuzhi",
	filter = function(self, targets, to_select)
		if (#targets ~= 0) then return false end
		return not to_select:isKongcheng() and to_select:objectName() ~= sgs.Self:objectName() 
	end ,
	on_effect = function(self, effect)
		local hina = effect.from
		local target = effect.to
		local room = hina:getRoom()
		local card_id = room:askForCardChosen(effect.from, effect.to, "h", "lualiuzhi")
		card = sgs.Sanguosha:getCard(card_id)
		room:setCardFlag(card, "lualiuzhi")
		if card:isKindOf("Jink") or card:isKindOf("Nullification") or card:isKindOf("sakura") or card:isKindOf("Collateral") or (card:isKindOf("Peach") and not hina:isWounded())
			or target:isCardLimited(card, sgs.Card_MethodUse) or target:isProhibited(hina, card, hina:getSiblings()) then 
			room:obtainCard(effect.from, card_id, false)
			room:setPlayerFlag(hina, "Global_PlayPhaseTerminated")
		else
			if card:targetFixed() then 
				if card:isKindOf("AOE") or card:isKindOf("AmazingGrace") or card:isKindOf("GodSalvation") then 
					room:setPlayerFlag(hina, "lualiuzhiAOE")
					room:useCard(sgs.CardUseStruct(card, target, sgs.SPlayerList()))
				else
					room:useCard(sgs.CardUseStruct(card, target, hina))
				end 
			else
				room:useCard(sgs.CardUseStruct(card, target, hina))
			end 
		end 		
	end
}
lualiuzhi3 = sgs.CreateTriggerSkill{
	name = "#lualiuzhi2" ,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.Damaged} ,
	on_trigger = function(self, event, player, data)
		local damage = data:toDamage()
		if damage.damage == 0 then return false end
		if damage.to and damage.to:getPhase() == sgs.Player_Play and damage.to:hasSkill("lualiuzhi") then damage.to:getRoom():setPlayerFlag(damage.to, "lualiuzhio") end 
		return false
	end,
	can_trigger = function(self, target)
		return true
	end ,
	priority = 10
}
lualiuzhi2 = sgs.CreateTriggerSkill{
	name = "#lualiuzhi",
	frequency = sgs.Skill_Compulsory,
	events = {sgs.TargetConfirming, sgs.PreCardUsed},
	on_trigger = function(self, event, player, data)
        local room = player:getRoom()
		if event == sgs.PreCardUsed then 
			use = data:toCardUse()
			card = use.card
			if use.from:hasFlag("lualiuzhiAOE") then
				use.to = sgs.SPlayerList()	
				use.to:append(use.from)
				room:setPlayerFlag(use.from, "-lualiuzhiAOE")
			end 
			data:setValue(use)
			return false	
		end 
		return false		
	end,
	can_trigger = function(self, target)
		return target
	end
}
lualiuzhi = sgs.CreateViewAsSkill{
	name = "lualiuzhi" ,
	n = 0 ,
	view_filter = function(self, selected, to_select)
		return true
	end ,
	view_as = function(self, cards)
		if #cards ~= 0 then return nil end	
		return liuzhiCard:clone()
	end ,
	enabled_at_play = function(self, player)
		return not player:hasFlag("lualiuzhio") and player:usedTimes("#lualiuzhi") < 2
	end
}

hina:addSkill(lualiuzhi)
hina:addSkill(lualiuzhi2)
hina:addSkill(lualiuzhi3)

hinaA:addSkill(lualiuzhi)
hinaA:addSkill(lualiuzhi2)
hinaA:addSkill(lualiuzhi3)

hinaB:addSkill(lualiuzhi)
hinaB:addSkill(lualiuzhi2)
hinaB:addSkill(lualiuzhi3)

luatanbao = sgs.CreateTriggerSkill{
	name = "luatanbao",
	frequency = sgs.Skill_NotFrequent,
	events = {sgs.CardsMoveOneTime},
	global = true,
	on_trigger = function(self, event, X, data, room)
		if event == sgs.CardsMoveOneTime then
			local move = data:toMoveOneTime()
			if not move.to then return false end
			local _moveto
			for _, p in sgs.qlist(room:getAlivePlayers()) do
				if move.to:objectName() == p:objectName() then
					_moveto = p
					break
				end
			end
			local x = move.card_ids:length()
			if (move.to_place == sgs.Player_PlaceHand or move.to_place == sgs.Player_PlaceEquip)
				and x > 1 and _moveto:objectName() == X:objectName() then
				for _, player in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
					if player:getMark("luatanbao") < 3 and room:askForDiscard(_moveto, self:objectName(), x, x, true, false, "@luatanbao") then
						room:setPlayerMark(player, "luatanbao", player:getMark("luatanbao") + 1)
						local weapons = {}
						local armors = {}
						local horse = {}
						local treasures = {}
						local basics = {}
						local delay_tricks = {}
						local multiple_target_tricks = {}
						local single_target_tricks = {}
						for _, id in sgs.qlist(room:getDrawPile()) do
							local Acard = sgs.Sanguosha:getCard(id)
							if Acard:isKindOf("Weapon") then
								table.insert(weapons, Acard:objectName())
							end
							if Acard:isKindOf("Armor") then
								table.insert(armors, Acard:objectName())
							end
							if Acard:isKindOf("DefensiveHorse") then
								table.insert(horse, Acard:objectName())
							end
							if Acard:isKindOf("OffensiveHorse") then
								table.insert(horse, Acard:objectName())
							end
							if Acard:isKindOf("Treasure") then
								table.insert(treasures, Acard:objectName())
							end
							if Acard:isKindOf("DelayedTrick") and not table.contains(delay_tricks, Acard:objectName()) then
								table.insert(delay_tricks, Acard:objectName())
							end
							if Acard:isKindOf("SingleTargetTrick") and not table.contains(single_target_tricks, Acard:objectName()) then
								table.insert(single_target_tricks, Acard:objectName())
							end
							if not Acard:isKindOf("SingleTargetTrick") and not Acard:isKindOf("DelayedTrick") and Acard:isKindOf("TrickCard")
									and not table.contains(multiple_target_tricks, Acard:objectName()) then
								table.insert(multiple_target_tricks, Acard:objectName())
							end
							if Acard:isKindOf("BasicCard") and not table.contains(basics, Acard:objectName()) then
								table.insert(basics, Acard:objectName())
							end
						end
						local choice1, choice2, choice3
						local zong = {}
						table.insert(zong, "forAI")
						local fen_A = {}
						local fen_B = {}
						if #weapons > 0 then table.insert(fen_A, "Weapon") end
						if #armors > 0 then table.insert(fen_A, "Armor") end
						if #horse > 0 then table.insert(fen_A, "Horse") end
						if #treasures > 0 then table.insert(fen_A, "Treasure") end
						if #delay_tricks > 0 then table.insert(fen_B, "delay_trick") end
						if #single_target_tricks > 0 then table.insert(fen_B, "single_target_trick") end
						if #multiple_target_tricks > 0 then table.insert(fen_B, "multiple_target_trick") end
						if #weapons + #armors + #horse + #treasures > 0 then table.insert(zong, "EquipCard") end
						if #delay_tricks + #single_target_tricks + #multiple_target_tricks > 0 then table.insert(zong, "TrickCard") end
						if #basics > 0 then table.insert(zong, "BasicCard") end
						local _data = sgs.QVariant()
						_data:setValue(_moveto)
						choice1 = room:askForChoice(player, "luatanbao", table.concat(zong, "+"), _data)
						if choice1 == "forAI" then return false end
						if choice1 == "EquipCard" then
							choice2 = room:askForChoice(player, "luatanbao", table.concat(fen_A, "+"))
							if choice2 == "Weapon" then
								choice3 = room:askForChoice(player, "luatanbao", table.concat(weapons, "+"))
							elseif choice2 == "Armor" then
								choice3 = room:askForChoice(player, "luatanbao", table.concat(armors, "+"))
							elseif choice2 == "Horse" then
								choice3 = room:askForChoice(player, "luatanbao", table.concat(horse, "+"))
							elseif choice2 == "Treasure" then
								choice3 = room:askForChoice(player, "luatanbao", table.concat(treasures, "+"))
							end
						elseif choice1 == "TrickCard" then
							choice2 = room:askForChoice(player, "luatanbao", table.concat(fen_B, "+"))
							if choice2 == "delay_trick" then
								choice3 = room:askForChoice(player, "luatanbao", table.concat(delay_tricks, "+"))
							elseif choice2 == "single_target_trick" then
								choice3 = room:askForChoice(player, "luatanbao", table.concat(single_target_tricks, "+"))
							elseif choice2 == "multiple_target_trick" then
								choice3 = room:askForChoice(player, "luatanbao", table.concat(multiple_target_tricks, "+"))
							end
						else
							choice3 = room:askForChoice(player, "luatanbao", "slash+jink+analeptic+peach+ofuda+hui")
						end
						if choice3 then
							local ids = sgs.IntList()
							for _, id in sgs.qlist(room:getDrawPile()) do
								local Acard = sgs.Sanguosha:getCard(id)
								if Acard:objectName() == choice3 then
									ids:append(id)
								end
							end
							if ids:length() > 0 then
								room:fillAG(ids, _moveto)
								local card_id = room:askForAG(_moveto, ids, false, "luazhishu")
								room:takeAG(_moveto, card_id, false)
								local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
								dummy_0:addSubcard(card_id)
								room:clearAG()
								_moveto:obtainCard(dummy_0)
							end
						end
					end
				end
			end
		end
	end
}
luatanbao2 = sgs.CreateTriggerSkill{
	name = "#luatanbao2" ,
	frequency = sgs.Skill_Compulsory,
	global = true, --究极重要！全场技能必备，花了5个小时买一场教训！
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		if event == sgs.EventPhaseChanging then
			local room = player:getRoom()
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				for _, p in sgs.qlist(room:findPlayersBySkillName("luatanbao")) do
					room:setPlayerMark(p, "luatanbao", 0)
				end
			end
		end
	end
}

lualingbaiCard = sgs.CreateSkillCard{
	name = "lualingbai",
	filter = function(self, targets, to_select)
		return (#targets == 0) and not to_select:isKongcheng()
	end,
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		local cards = room:askForExchange(effect.to, self:objectName(), 99, 1, false, "@lualingbai", true)
		local idsA = sgs.IntList()
		local idsB = cards:getSubcards()
		local atLeastOne = false 
		if cards then
			local card_ids = sgs.IntList()
			local str = "lualingbai"
			for _, id in sgs.qlist(cards:getSubcards()) do
				local acard = sgs.Sanguosha:getCard(id)
				str = str .. "|" .. tostring(id) 
				card_ids:append(id)
			end 
			effect.to:setTag("lualingbaiT", sgs.QVariant(str)) 
			room:fillAG(card_ids)
			room:getThread():delay()
			room:getThread():delay()
			room:clearAG()
		end 
		
	end
}
lualingbaiVS = sgs.CreateZeroCardViewAsSkill{
	name = "lualingbai",
	view_as = function(self, cards)
		return lualingbaiCard:clone()
	end,
	enabled_at_play = function(self, player)
		return not player:hasUsed("#lualingbai")
	end,
}
lualingbai = sgs.CreateTriggerSkill{
	name = "lualingbai" ,
	global = true,
	view_as_skill = lualingbaiVS,
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local change = data:toPhaseChange() 
		if change.to == sgs.Player_NotActive then 
			if room:getCurrent():getTag("lualingbaiT"):toString() and room:getCurrent():getTag("lualingbaiT"):toString() ~= ""
				and room:askForSkillInvoke(room:getCurrent(), "lualingbaiC", data) then 
				local ualingbai = room:getCurrent():getTag("lualingbaiT"):toString() 
				room:writeToConsole("Hello World!" .. ualingbai)
				ualingbai = ualingbai:split("|")
				if #ualingbai <= 1 then room:getCurrent():removeTag("lualingbaiT"); return end 
				local canActivate = false
				for _, idStr in ipairs(ualingbai) do
					if tonumber(idStr) then 
						canActivate = false
						for _, card in sgs.list(room:getCurrent():getHandcards()) do
							if tonumber(idStr) == card:getEffectiveId() then 
								canActivate = true
							end 
						end
						if not canActivate then room:getCurrent():removeTag("lualingbaiT"); return end 
					end 
				end 

				local card_ids = sgs.IntList()
				for _, idStr in ipairs(ualingbai) do 
					if tonumber(idStr) then 
						for _, card in sgs.list(room:getCurrent():getHandcards()) do
							if tonumber(idStr) == card:getEffectiveId() then  
								card_ids:append(tonumber(idStr))
								room:setCardFlag(card, "lingbai") 
							end 
						end 
					end 
				end  
				if not room:getCurrent():hasSkill("lualingbai3") then room:acquireSkill(room:getCurrent(), "lualingbai3", false) end 
				room:fillAG(card_ids)
				room:getThread():delay()
				room:getThread():delay()
				room:clearAG()
				room:filterCards(room:getCurrent(), room:getCurrent():getHandcards(), true) 
				room:getCurrent():removeTag("lualingbaiT")
			end  
		end 
	end,
}

nazrin:addSkill(luatanbao)
nazrin:addSkill(luatanbao2)
nazrin:addSkill(lualingbai)


sgs.LoadTranslationTable{
	["pay4"] = "华鸟风月", --注意这里每次要加逗号
	["daiyousei"] = "大妖精",
	["daiyouseiA"] = "大妖精",
	["daiyouseiB"] = "大妖精",
	["daiyouseiC"] = "大妖精",
	["daiyouseiD"] = "大妖精",
	["illustrator:daiyousei"] = "猫箸",
	["illustrator:daiyouseiA"] = "純珪一",
	["illustrator:daiyouseiB"] = "いちは",
	["illustrator:daiyouseiC"] = "雨森くろま",
	["illustrator:daiyouseiD"] = "あめらる",

	["#daiyousei"]= "自然之化身",	
	["#Yizuo"] = "祝福",
	["LuaYizuo"] = "祝福",
	[":LuaYizuo"] = "出牌阶段限一次，你可以交给一名其他角色任意张牌，若如此做，其每回复一点体力或受到一点伤害后，你摸一张牌，直到你下次发动此技能为止。",
	["LuaQiyuan"] = "祈愿",
	["LuaQiyuan2"] = "祈愿（替大妖精挡伤害）",
	["@LuaQiyuan"] = "你可以使用一张牌",
	[":LuaQiyuan"] = "一名其他角色可以转移你受到的一次【杀】造成的伤害，然后其可以摸一张牌并使用一张牌。",

	["koakuma"] = "小恶魔",
	["koakumaA"] = "小恶魔",
	["koakumaB"] = "小恶魔",
	["#koakuma"]= "大图书馆的司书",		
	["Luasishu"] = "司书",	
	["sishu"] = "你可以发动“司书”",	
	["~Luasishu1"] = "选择一张锦囊牌→选择一名角色→点击确定→跳过判定阶段并令该角色于此时执行一个额外的此阶段。",	
	["~Luasishu2"] = "选择一张锦囊牌→选择一名角色→点击确定→跳过摸牌阶段并令该角色于此时执行一个额外的此阶段。",	
	["~Luasishu3"] = "选择一张锦囊牌→选择一名角色→点击确定→跳过出牌阶段并令该角色于此时执行一个额外的此阶段。",	
	["~Luasishu4"] = "选择一张锦囊牌→选择一名角色→点击确定→跳过弃牌阶段并令该角色于此时执行一个额外的此阶段。",	
	["~Luasishu5"] = "选择一张锦囊牌→选择一名角色→点击确定→跳过回合结束阶段并令该角色于此时执行一个额外的此阶段。",	
	["~Luasishu6"] = "选择一张锦囊牌→选择一名角色→点击确定→跳过准备阶段并令该角色于此时执行一个额外的此阶段。",	
	[":Luasishu"] = "你可以交给一名其他角色一张锦囊牌，跳过你的一个阶段并令该角色于此时执行一个额外的此阶段。发动过此技能的下个准备阶段，你可以亮出牌堆顶的X+1张牌，并获得其中的全部锦囊牌。（X为你已损失体力值）",
	
	["white"] = "莉莉白",	
	["whiteA"] = "莉莉白",
	["whiteB"] = "莉莉白",
	["whiteC"] = "莉莉白",
	["whiteD"] = "莉莉白",
	["#white"]= "春之妖精",
	["Luabaochun"] = "报春",		
	[":Luabaochun"] = "锁定技，你的红桃手牌均视为【桃】",
	["luajingzhe"] = "惊蛰",	
	["luajingzheD"] = "请选择要被【决斗】的对象。",	
	[":luajingzhe"] = "结束阶段：①若你未受伤，你增加一点体力上限并摸一张牌。②依座次，所有此回合回复过体力的角色执行动作：●视为对你选择的一名角色使用了一张【决斗】",
	["luaxinsheng"] = "心声",	
	["@luaxinsheng"] = "你可以发动“心声”",	
	["~luaxinsheng"] = "你可以转移给其他角色回复体力。",	
	[":luaxinsheng"] = "你回复体力时，可以将其转移给其他角色。",	
	
	["benben"] = "八桥&弁弁",
	["#benben"]= "古琴的付丧神",
	["luayigong"] = "移宫",
	[":luayigong"] = "出牌阶段限两次，你可以移动你装备区里的一张牌至一名其他角色，然后与其各摸一张牌。 ",
	["luatianqin"] = "天琴",
	[":luatianqin"] = "装备区有两张牌以上的角色出牌阶段开始时，其可以弃置一张牌并判定，若为黑桃，则你对一名角色造成两点雷电伤害。",
	[":luatianqin2"] = "对一名角色造成两点雷电伤害",
	--["@luaxinjiang"] = "请发动“%dest”来修改 %src 的 %arg 判定",
	["luaxinjiang"] = "星降",	
	[":luaxinjiang"] = "锁定技，每轮开始时，你可以获得摸牌堆中的一张装备牌。",
	
	["sp_meili"] = "玛艾露贝莉·赫恩",	
	["#sp_meili"]= "魔术师",
	["designer:sp_meili"] = "Paysage",	
	["luaweijing"] = "伪境",	
	[":luaweijing"] = "结束阶段，或是你受到伤害后，你可以摸一张牌，并：\
		①将一张红桃牌置于一名角色的判定区，其获得牌时，改为获得此牌代替；\
		②将一张黑桃牌置于一名角色的判定区，其弃置牌时，改为弃置此牌代替。",

	["erin"] = "八意永琳",	
	["#erin"]= "月之头脑",	
	["LuaYao"]= "药",		
	["designer:erin"] = "Paysage",	
	["luamiyao"] = "秘药",	
	[":luamiyao"] = "出牌阶段限两次，你可以重铸一张牌，令一名已损失体力值不小于你体力值的角色回复体力至满（最多至5）。此后其每个回合开始时其受到一点伤害。",
	["luashengong"] = "神弓",	
	["@luashengong"] = "弃置亮起牌中的一张，否则你不能闪避此【杀】",
	[":luashengong"] = "你使用【杀】指定目标后，其须弃置一张与此【杀】颜色不同的牌，否则此【杀】不可被响应。锁定技，你使用【杀】无距离限制。",
	["luasijian"] = "思兼",
	[":luasijian"] = "一名角色流失体力后，你可以受到一点伤害并令其回复一点体力。",

	["keine"] = "上白泽慧音", 
	["#keine"]= "知识与历史的半兽",
	["luaxinshi"] = "秘史",	
	[":luaxinshi"] = "你每受到一点伤害后，你可以翻开弃牌堆顶的四张牌，选一名角色获得其中一种颜色的一张牌，另一名角色获得另一种颜色的一张牌。",
	["@luaxinshi"] = "你可以发动“秘史”",
	["~luaxinshi"] = "选择1~2名角色→点击确定",
	["luaguiqiao"] = "归桥",	
	[":luaguiqiao"] = "每轮各限一次：一名角色进入濒死状态时，你可以弃置X张红色牌将其体力改为X，然后你受到1点伤害；一名角色的回合结束时，你可以弃置1张黑色牌，令其摸一张牌并发动“默识”。",		
	["luaguiqiao2"] = "归桥",
	["@luaguiqiaok"] = "你可以弃置1张黑色牌，令当前回合角色摸一张牌并发动“默识”",
	["@luaguiqiao"] = "你可以发动“归桥”",	
	["~luaguiqiao"] = "(请指定合法的角色作为目标)",	
	["@luaguiqiaoXX"] = "你可以将一张手牌当作本回合使用的第一张牌使用", 
	["@luaguiqiaoYY"] = "你可以将一张手牌当作本回合使用的第二张牌使用",
	
	["keineA"] = "上白泽慧音",
	["keineB"] = "上白泽慧音",
    ["#keineA"] = "史学家",
    ["designer:keineA"] = "Paysage",
    ["illustrator:keineA"] = "lumo1121",
    ["luashouyek"] = "授业",
    ["luashouyej"] = "授业",
	[":luashouyej"] = "出牌阶段限一次,你可以将一张牌当作慧音指定的牌来使用（你可以在慧音武将牌上查看这张牌具体是什么），并令她摸一张牌。",
    [":luashouyek"] = "每轮开始时，你可以弃置一张基本牌或单目标锦囊牌，之后此轮所有角色于其出牌阶段限一次,其可以将一张牌当作此牌使用，并令你摸一张牌。",
	["@luashouyek"] = "你可以发动“授业”",	
	["~luashouyek"] = "选择要弃置的牌→点击确定",		
    ["luatouchui"] = "头槌",
    [":luatouchui"] = "发动“授业”的这轮结束时，你可以选择某区域内没有牌的至多X名角色，将你一张牌置于此区域，并对其造成一点伤害（X为此轮未以“授业”使用牌的角色数）。",
	["@luatouchui"] = "你可以发动“头槌”",	
	["~luatouchui"] = "选择某区域内没有牌的角色→点击确定",	 
	["@luatouchuiA"] = "请选择一张装备牌",	
	["@luatouchuiB"] = "请选择一张延时类锦囊牌",	
	["@luatouchuiC"] = "请选择一张基本牌",	
	["PlaceDelayedTrick"] = "判定区",
	["PlaceDelayedTrick"] = "判定区",
	["PlaceDelayedTrick"] = "判定区",
	
		
	["aunn"] = "高丽野阿吽",
	["#aunn"]= "心有灵犀",		
	["designer:aunn"] = "Paysage",
	["lualingxi"] = "灵犀",		
	[":lualingxi"] = "你的任意阶段开始时，你可以将一张基本牌交给一名角色，令其获得“回旋”或“守护”。",
	["@lualingxi"] = "你可以发动“灵犀”",
	["~lualingxi"] = "选择1名角色和一张基本牌→点击确定",
	["luashouhu"] = "守护",		
	[":luashouhu"] = "你距离1内的角色需要使用一张【闪】时，你可以对来源使用一张【杀】视为其使用了此【闪】，然后失去本技能。",
	["luahuixuan"] = "回旋",	
	["luashouhuy"] = "你可以对此【杀】的使用者使用一张【杀】来替当前目标打出【闪】",	
	[":luahuixuan"] = "你可以将一张基本牌当【杀】使用或打出，然后摸一张牌并失去本技能。",
	
	["doremi"] = "哆来咪·苏伊特",
	["#doremi"]= "梦之支配者",		
	["designer:doremi"] = "Paysage",
	["luamimeng"] = "迷梦",		
	["mimeng"] = "迷梦",		
	["luamimengP"] = "请选择要获得牌的那个对象（一般是敌方）。",		
	[":luamimeng"] = "出牌阶段限一次，你可以将一张【闪】当【无中生有】对一名角色使用。",
	["luaemeng"] = "噩梦",
	[":luaemeng"] = "你使用【无中生有】指定目标后，你可以与一名其他角色交换一张手牌，然后选其一张手牌令其对自身使用之。若不能如此做，你获得此牌。",
	["luaganmeng"] = "绀梦",		
	[":luaganmeng"] = "<b>锁定技</b>，其他角色计算的与你的距离+1。",

	["nitori"] = "河城荷取",	
	["#nitori"]= "水中的技师",	
	["luazhonggong"] = "铸械",
	[":luazhonggong"] = "出牌阶段，你可以：①弃置三张【杀】，获得一张装备牌并交给一名角色；②弃置两张【杀】，对一名角色造成一点伤害。",
	["luagongcheng"] = "工程",
	[":luagongcheng"] = "每名角色于其出牌阶段限一次，其可以交给你一张【杀】并摸一张牌。",
	
	["hina"] = "键山雏",	
	["hinaA"] = "键山雏",
	["hinaB"] = "键山雏",
	["#hina"]= "秘神流雏",
	["lualiuzhi"] = "流雏",		
	[":lualiuzhi"] = "出牌阶段最多两次，若你没有受到伤害，你可以展示一名角色的一张手牌并令其对你使用之。若不能如此做，你获得此牌并结束本阶段。",

	["nazrin"] = "娜兹玲",
	["#nazrin"] = "探宝大将",
	["designer:nazrin"] = "Paysage",
	["luatanbao"] = "探宝",
	["@luatanbao"] = "你可以等同于刚刚获得的量的手牌，来发动“探宝”",
	[":luatanbao"] = "每回合限三次，一名角色获得一张以上的牌后，其可以弃置等量的手牌，然后你从摸牌堆中选一张牌令其获得之。",
	["lualingbai"] = "灵摆",
	["@lualingbai"] = "请展示任意张牌（这些牌将有机会改为【无中生有】）",
	[":lualingbai"] = "出牌阶段限一次，你可以令一名角色展示任意张手牌。该角色的下个回合结束时，再次展示这些牌，改为红桃K【无中生有】。",
	["lualingbaiC"] = "灵摆（修改手牌）",


}
return {extension_pay_d}