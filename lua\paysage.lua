--这是老子专用的判断自身卡牌价值的函数包
Pay = {} --定义一个模组
--[[
require “xxx” 某个模块后
1.如果该模块自身有返回值，且模块加载成功，那么require 的返回值就是该模块的返回值 
2.如果模块没有返回值，如果require加载模块成功，就返回ture 
3.require 内部将返回值储存在：package.loaded表中。 
]]--

math.randomseed(tostring(os.time()):reverse():sub(1, 7)) --------设置时间种子
--[[
怂函数。包含了所有要怂的场面,bool为简易判定,bool2为是否考虑钴锭
怂函数只表示一种需要闪和手牌苟命的情况
]]--
function Pay.Song(self,splayer,bool,bool2)
	local value_7 = 0
	local value_8 = 0
	local value_9 = 0
	local value_6 = 0
	if (splayer:inMyAttackRange(self.player) or splayer:getHandcardNum() > 4) and not self:isFriend(splayer) 	then --找找自己会被捅的可能）
		value_7 = value_7 + 1
		if splayer:hasWeapon("guding_blade") then value_8 = value_8 + 1 end
		if splayer:hasSkills("luoyi|nosluoyi") then value_9 = value_9 + 1 end --有裸衣怂一下
		if (splayer:getWeapon() and splayer:getWeapon():isKindOf("Crossbow")) or splayer:hasSkill("paoxiao") then value_6 = value_6 + 1 end 
	end 
	if bool then 
		if (value_9 > 0) or ((value_8 > 0) and bool2) or (value_6 > 0) then 
			return 1 
		else
			return 0
		end 
	else
		return value_7,value_8,value_9,value_6
	end 
	return 0 
end 
--[[
利用快排返回指定list中点数最大的牌
]]--
function Pay.partition2(self, list, left, right)
    local high = right
    local low = left
	local low2 = low
	local high2 = high
	if high == low then return end 
    local pivotKey = list[low]:getNumber() -- 定义一个中介值,它总是数组第1个
    local function Swapp(list,xiabiao1,xiabiao2)
		if not list[xiabiao1] then return false end 
		if not list[xiabiao2] then return false end 
		local n = list[xiabiao2]
		list[xiabiao2] = list[xiabiao1]
		list[xiabiao1] = n
	end 
	local p = 1
    -- 下面将中介值移动到列表的中间
    -- 当左索引与右索引相邻时停止循环
    while low < high do
		self.room:writeToConsole("快排第"..p.."次")
		for i = low2 , high2 do 
			self.room:writeToConsole(list[i]:getNumber())
		end 
        -- 假如当前右值大于等于中介值则右索引左移
        -- 否则交换中介值和右值位置
        while low < high and list[high]:getNumber() <= pivotKey do
            high = high - 1
        end
        Swapp(list, low, high)

        -- 假如当前左值小于等于中介值则左索引右移
        -- 否则交换中介值和左值位置
        while low < high and list[low]:getNumber() >= pivotKey do
            low = low + 1
        end
        Swapp(list, low, high)
		p = p + 1
    end
	local id = list[low]:getId() --验证快排有效性
	self.room:writeToConsole("卡ID为："..id.."点数为："..list[low]:getNumber().."此时排列手牌中的大小位置是："..low)
		for i = low2 , high2 do 
			self.room:writeToConsole(list[i]:getNumber())
		end 
    return low
end
-- --[[--
-- -   orderByQuick: 快速排序
-- -   @param: list, low, high - 参数描述
-- -    @return: list - table
-- ]]
function Pay.orderByQuick2(self, list, left, right)
	local low = left
	local high = right 
    if low < high then
        -- 返回列表中中介值所在的位置，该位置左边的值都小于等于中介值，右边的值都大于等于中介值
        local pivotKeyIndex = Pay.partition2(self, list, low, high)
		if not pivotKeyIndex then return end 
        -- 分别将中介值左右两边的列表递归快排
        Pay.orderByQuick2(self, list, low, pivotKeyIndex - 1)
        Pay.orderByQuick2(self, list, pivotKeyIndex + 1, high)
    end
end

function Pay.huase(card)
	local huase
	if card:getSuit() == sgs.Card_Heart then
		huase = "heart"
	elseif card:getSuit() == sgs.Card_Diamond then
		huase = "diamond"
	elseif card:getSuit() == sgs.Card_Club then
		huase = "club"
	elseif card:getSuit() == sgs.Card_Spade then
		huase = "spade"
	end
	return huase
end 
function Pay.GetAllCardsByPlayer(player)
	local RedTable = {}
	local allcards = {}
	local handcards = player:getHandcards()
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		table.insert(allcards, c)
	end
	local equips = player:getEquips()
	for _,c in sgs.qlist(equips) do
		table.insert(allcards, c)
	end	--得到所有牌数量
	return allcards
end 
function Pay.GetAllRCardsByPlayer(player)
	local RedTable = {}
	local allcards = {}
	local handcards = player:getHandcards()
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		table.insert(allcards, c)
	end
	local equips = player:getEquips()
	for _,c in sgs.qlist(equips) do
		table.insert(allcards, c)
	end	--得到所有牌数量
	for _,c in ipairs(allcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		if not player:isJilei(c) then 
			if c:isRed() then table.insert(RedTable, c) end 
		end 
	end 
	return RedTable
end 
function Pay.GetAllRCardsBySelf(self)
	local RedTable = {}
	local allcards = {}
	local handcards = self.player:getHandcards()
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		table.insert(allcards, c)
	end
	local equips = self.player:getEquips()
	for _,c in sgs.qlist(equips) do
		table.insert(allcards, c)
	end	--得到所有牌数量
	for _,c in ipairs(allcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		if not self.player:isJilei(c) then 
			if c:isRed() then table.insert(RedTable, c) end 
		end 
	end 
	return RedTable
end 
--[[
验证此名玩家是否是卖血狗
]]--
function Pay.Maixuegou(self,player)
	for _, sk in sgs.qlist(player:getVisibleSkillList()) do
		local skill = sk:objectName()
		if sk and not sk:isLordSkill() and sk:getFrequency() ~= sgs.Skill_Wake then
			if string.find(sk:getDescription(), "每当你受到") and string.find(sk:getDescription(), "伤害后") then  
				self.room:writeToConsole("找到一名卖血武将，他是"..player:objectName().."卖血技能是"..skill)
				return true
			end
		end 
	end 
	return false 
end 
function Pay.Maixuegoulist(self)
	local plist = sgs.PlayerList()
	for _, splayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do	
		if Pay.Maixuegou(self,splayer) then plist:append(splayer) end 
	end 
	return plist
end 
--[[
刚函数。包含了留杀的情况
]]--
function Pay.Gang(self,splayer,bool,bool2)
	local value_1 = 0
	for _, splayer2 in sgs.qlist(self.room:getOtherPlayers(self.player)) do	--找找自己友军数量  
		if self:isFriend(splayer2) then 
			value_1 = value_1 + 1
		end 
	end 
	value_1 = math.sqrt(value_1/10) - (self.player:getHp()/10)
	if splayer:hasSkill("tiaoxin") and (self.player:inMyAttackRange(splayer) or self.player:canSlash(splayer)) and not self:isFriend(splayer) then return 1 end 
	if splayer:hasSkill("zhanjue") then 
		if math.random() > value_1 then 
			return 1 
		end 
	end 
	return 0 
end

--[[
杀函数。此时用什么杀比较好，是不是一个使用杀的时机
]]--
function Pay.DummyUseSlash(self)
	local shouldUseSlash = false
	local card = sgs.Sanguosha:cloneCard("fire_slash")
	local dummy_use = {isDummy = true}
	self:useBasicCard(card, dummy_use)
	if dummy_use.card then 
		shouldUseSlash = true 

	end		
	if not shouldUseSlash then 
		card = sgs.Sanguosha:cloneCard("thunder_slash")
		local dummy_use = {isDummy = true}
		self:useBasicCard(card, dummy_use)
		if dummy_use.card then 
			shouldUseSlash = true 

		end		
	end 
	if not shouldUseSlash then 
		card = sgs.Sanguosha:cloneCard("slash")
		local dummy_use = {isDummy = true}
		self:useBasicCard(card, dummy_use)
		if dummy_use.card then 
			shouldUseSlash = true 

		end		
	end 
	if shouldUseSlash then 
		self.room:writeToConsole("AI此时如果有杀则可以杀")
		return card
	end 
	return false 
end 
function Pay.Slash_value(self,func1,func2,func3,num,first,id)
	local keep_value = 5
	local bool = false 
	local renwangdun = false 
	local value_1 = 0 
	local value_2 = 0 	
	local value_3 = 0 	
	local value_4 = 0 
	local value_5 = 0 
	local value_6 = 0 
	local value_7 = 0 
	local value_8 = 0 
	local value_9 = 0 
	for _, splayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do	--找找自己攻击范围内非友军数量
		if self.player:inMyAttackRange(splayer) and not self:isFriend(splayer) then 
			if ((Pay.GetCardNameFromCard(splayer:getArmor(),true) == "renwangdun") or splayer:hasArmorEffect("renwang_shield")) and not IgnoreArmor(self.player, splayer) then value_1 = value_1 + 1 end --统计仁王盾数量
			if ((Pay.GetCardNameFromCard(splayer:getArmor(),true) == "tengjia") or splayer:hasArmorEffect("vine")) and not IgnoreArmor(self.player, splayer) then value_3 = value_3 + 1 end --统计藤甲数量
			if splayer:hasSkill("wenjiu") then value_4 = value_4 + 1 end --统计温酒数量
			if splayer:hasSkill("ranshang") then value_5 = value_5 + 1 end --统计燃殇数量 	self.player:isKongcheng()
			if splayer:isKongcheng() and not splayer:hasSkill("kongcheng") then value_6 = value_6 + 1 end 
			if splayer:hasSkill("chouhai") and splayer:isKongcheng() and not splayer:hasSkill("kongcheng") then value_7 = value_7 + 1 end --统计仇海数量
			if splayer:hasSkill("tiaoxin") then value_8 = value_8 + 1 end --统计挑衅数量
			value_2 = value_2 + 1 
		end 		
	end 
	if value_2 == 0 then bool = true end 
	if bool then keep_value = 2 end 
	local slash_table = {} --统计杀数量
	local handcards = self.player:getHandcards()
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		if Pay.GetCardNameFromCard(c,true) == "sha" then table.insert(slash_table, c) end 
		if Pay.GetCardNameFromCard(c,true) == "jiu" then value_9 = value_9 + 1 end 
	end		
	if value_9 > 0 then keep_value = keep_value + 1 end 
	if (slash_cardsnum == 0) or self:hasCrossbowEffect() or (self:getCardsNum("Crossbow") > 0) 
		then keep_value = keep_value + 0.5 
	else
		local slash_count = #slash_table
		if not bool then slash_count = slash_count - 1 end 
		if not first then keep_value = keep_value - slash_count*0.5 end 
	end 
	if value_6 > 0 then 
		keep_value = keep_value + 0.3
	end 
	if self.player:hasWeapon("guding_blade") then --老子砍死你个玩意  
		if value_6 > 0 then keep_value = keep_value + 1.2 end 
	end 
	if self.player:hasWeapon("axe") and value_6 == 0 then 
		keep_value = keep_value + 0.25
	end 
	if self.player:hasWeapon("yitian_sword") then 
		keep_value = keep_value + 0.15
	end 
	if value_7 > 0  then keep_value = keep_value + 1 end 
	if func1 then keep_value = func1(self,keep_value) end 	
	if id and (((id > 110) and (id < 116)) or ((id > 125) and (id < 130)) or (id == 244)) then keep_value = keep_value + 0.25 end --雷杀的价值更高
	if id and ((id == 143) or (id == 150) or (id == 151) or (id == 140) or (id == 137)) then
		keep_value = keep_value + 0.5 
		if value_3 > 0 then keep_value = keep_value + 0.35 end 
		if value_5 > 0 then keep_value = keep_value + 0.25 end 
	end --火杀的价值
	if id and (((id > 20) and (id < 30)) or (id == 143) or (id == 150) or (id == 151) or (id == 140) or (id == 137) or (id == 190) or (id == 191) 
		or (id == 212) or (id == 215) or (id == 219) or (id == 230)) then 
		keep_value = keep_value + 0.2 
		if value_1 == value_2 then keep_value = keep_value + 0.4 end --所有敌人全部有仁王盾 enemy:hasArmorEffect("vine")
		if value_4 > 0 then keep_value = keep_value + 0.4 end 
	end --红杀的价值
	if value_8 > 0  then 
		keep_value = keep_value + 0.4 
		if #slash_table == 1 and (math.random() > 0.3) then keep_value = keep_value + 0.7 end --虚晃一枪
	end 
	--举例，有敌人没手牌穿藤甲，此时自己手上只有一张火杀，其保留价值是 7.55
	--self.room:writeToConsole("此时该玩家的杀价值为"..keep_value)		
	return keep_value 
end 
function Pay.Jink_value(self,func1,func2,func3,num,first,id)
	local keep_value = 7.5
	local jink_table = {} --统计闪数量
	local handcards = self.player:getHandcards()
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		if Pay.GetCardNameFromCard(c,true) == "shan" then table.insert(jink_table, c) end 
	end			--self.player:hasArmorEffect("eight_diagram")
	if (Pay.GetCardNameFromCard(self.player:getArmor(),true) == "baguazhen") or self.player:hasArmorEffect("eight_diagram") then keep_value = keep_value - 0.5 end --统计八卦阵数量
	if (Pay.GetCardNameFromCard(self.player:getArmor(),true) == "renwangdun") or self.player:hasArmorEffect("renwang_shield") then keep_value = keep_value - 0.6 end --统计仁王盾数量	
	if (Pay.GetCardNameFromCard(self.player:getArmor(),true) == "tengjia") or self.player:hasArmorEffect("vine") then keep_value = keep_value + 0.5 end --统计藤甲数量
	if not first then
		local n = 2
		local const = #jink_table - 1
		for i = 1, const do 
			keep_value = keep_value - n 
			n = n + 1 
		end 		
	end 
	--self.room:writeToConsole("此时该玩家的闪价值为"..keep_value)
	return keep_value 
end
function Pay.Analeptic_value(self,func1,func2,func3,num,first,id)
	local keep_value = 4.5
	local analeptic_table = {} --统计酒数量
	local handcards = self.player:getHandcards()
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		if Pay.GetCardNameFromCard(c,true) == "jiu" then table.insert(analeptic_table, c) end 
	end	
	if not first then
		local n = 2
		local const = #analeptic_table - 1
		for i = 1, const do 
			keep_value = keep_value - n 
			n = n + 1 
		end 		
	end --攻击范围内没有敌人，两张酒也比杀值钱，正确
	if func1 then keep_value = func1(self,keep_value) end 
	if self.player:getHp() == 1 then keep_value = keep_value + 4 end 
	return keep_value 
end 
function Pay.Peach_value(self,func1,func2,func3,num,first)
	local keep_value = 10
	if self.player:getHp() == self.player:getMaxHp() then keep_value = 7 end 
	local peach_table = {} --统计桃数量
	local handcards = self.player:getHandcards()
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		if Pay.GetCardNameFromCard(c,true) == "tao" then table.insert(peach_table, c) end 
	end	
	local peach_count = #peach_table - self.player:getLostHp() - 1
	local n = 1
	if peach_count > 0 then 
		for i = 1,peach_count do 
			keep_value = keep_value - n
			n = n + 1 
		end 
	end 
	if func1 then keep_value = func1(self,keep_value) end 
	if func2 then value = func2(self,value) end 	
	--self.room:writeToConsole("此时该玩家的桃价值为"..keep_value)	
	return keep_value 
end 
function Pay.Crossbow_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 5.5
	local slash_table = {} --统计杀数量
	local handcards = self.player:getHandcards()
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		if Pay.GetCardNameFromCard(c,true) == "sha" then table.insert(slash_table, c) end 
	end		
	if first and #slash_table > 1 then
		local const = #slash_table - 1
		for i = 1, const do 
			keep_value = keep_value + 1
			n = n + 1 
		end 		
	end 
	if self.player:getWeapon() then keep_value = keep_value - 0.5 end 
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	
end 
function Pay.DoubleSword_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 4.5
	local rangefix = 0
	local value_1 = 0 
	local value_2 = 0 --计算假设这是武器，扔掉之后我攻击范围内的敌人数量
	local value_3 = 0
	if equip then
		local card = self.player:getWeapon():getRealCard():toWeapon()
		rangefix = rangefix + card:getRange() - self.player:getAttackRange(false)
	end
	for _, splayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do	--找找自己攻击范围内非友军数量  
		if self.player:inMyAttackRange(splayer) and not self:isFriend(splayer) 	then 
			value_1 = value_1 + 1
			if splayer:isMale() ~= self.player:isMale() then 
				value_3 = value_3 + 1 --统计异性角色数量
			end 
		end 
		if equip and self.player:inMyAttackRange(splayer, rangefix) and not self:isFriend(splayer) then 
			value_2 = value_2 +1 
		end 
	end 
	
	if value_1 == 0 and not equip and not self.player:getWeapon() then keep_value = keep_value + 1.25 end 
	if equip and value_2 == 0 then keep_value = keep_value + 2 end 
	if not equip and self.player:getWeapon() then keep_value = keep_value - 1.5 end
	if equip and value_3 ~= 0 then keep_value = keep_value + 1 end 
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	
end 
function Pay.QinggangSword_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 4.5
	local rangefix = 0
	local value_1 = 0 
	local value_2 = 0 --计算假设这是武器，扔掉之后我攻击范围内的敌人数量
	local value_3 = 0
	if equip then
		local card = self.player:getWeapon():getRealCard():toWeapon()
		rangefix = rangefix + card:getRange() - self.player:getAttackRange(false)
	end
	for _, splayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do	--找找自己攻击范围内非友军数量  
		if self.player:inMyAttackRange(splayer) and not self:isFriend(splayer) 	then 
			value_1 = value_1 + 1
			-- if splayer:isMale() ~= self.player:isMale() then 
				-- value_3 = value_3 + 1 --统计异性角色数量
			-- end 
		end 
		if equip and self.player:inMyAttackRange(splayer, rangefix) and not self:isFriend(splayer) then 
			value_2 = value_2 +1 
		end 
	end 
	
	if value_1 == 0 and not equip and not self.player:getWeapon() then keep_value = keep_value + 1.25 end 
	if equip and value_2 == 0 then keep_value = keep_value + 2 end 
	if not equip and self.player:getWeapon() then keep_value = keep_value - 1.5 end --已经有武器了
	--if equip and value_3 ~= 0 then keep_value = keep_value + 1 end 
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	
end 
function Pay.qinglong_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 4
	local rangefix = 0
	local value_1 = 0 
	local value_2 = 0 --计算假设这是武器，扔掉之后我攻击范围内的敌人数量
	local value_3 = 0
	if equip then
		local card = self.player:getWeapon():getRealCard():toWeapon()
		rangefix = rangefix + card:getRange() - self.player:getAttackRange(false)
	end
	for _, splayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do	--找找自己攻击范围内非友军数量  
		if self.player:inMyAttackRange(splayer) and not self:isFriend(splayer) 	then 
			value_1 = value_1 + 1
			-- if splayer:isMale() ~= self.player:isMale() then 
				-- value_3 = value_3 + 1 --统计异性角色数量
			-- end 
		end 
		if equip and self.player:inMyAttackRange(splayer, rangefix) and not self:isFriend(splayer) then 
			value_2 = value_2 +1 
		end 
	end 
	
	if value_1 == 0 and not equip and not self.player:getWeapon() then keep_value = keep_value + 1.25 end 
	if equip and value_2 == 0 then keep_value = keep_value + 2 end 
	if not equip and self.player:getWeapon() then keep_value = keep_value - 1.5 end --已经有武器了
	--if equip and value_3 ~= 0 then keep_value = keep_value + 1 end 
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	
end 
function Pay.Spear_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 4
	local rangefix = 0
	local value_1 = 0 
	local value_2 = 0 --计算假设这是武器，扔掉之后我攻击范围内的敌人数量
	local value_3 = 0
	if equip then
		local card = self.player:getWeapon():getRealCard():toWeapon()
		rangefix = rangefix + card:getRange() - self.player:getAttackRange(false)
	end
	for _, splayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do	--找找自己攻击范围内非友军数量  
		if self.player:inMyAttackRange(splayer) and not self:isFriend(splayer) 	then 
			value_1 = value_1 + 1
			-- if splayer:isMale() ~= self.player:isMale() then 
				-- value_3 = value_3 + 1 --统计异性角色数量
			-- end 
		end 
		if equip and self.player:inMyAttackRange(splayer, rangefix) and not self:isFriend(splayer) then 
			value_2 = value_2 +1 
		end 
	end 
	
	if value_1 == 0 and not equip and not self.player:getWeapon() then keep_value = keep_value + 1.25 end 
	if equip and value_2 == 0 then keep_value = keep_value + 2 end 
	if not equip and self.player:getWeapon() then keep_value = keep_value - 1.5 end --已经有武器了
	--if equip and value_3 ~= 0 then keep_value = keep_value + 1 end 
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	
end 
function Pay.Axe_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 4.5
	local rangefix = 0
	local value_1 = 0 
	local value_2 = 0 --计算假设这是武器，扔掉之后我攻击范围内的敌人数量
	local value_3 = 0
	if equip then
		local card = self.player:getWeapon():getRealCard():toWeapon()
		rangefix = rangefix + card:getRange() - self.player:getAttackRange(false)
	end
	for _, splayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do	--找找自己攻击范围内非友军数量  
		if self.player:inMyAttackRange(splayer) and not self:isFriend(splayer) 	then 
			value_1 = value_1 + 1
			-- if splayer:isMale() ~= self.player:isMale() then 
				-- value_3 = value_3 + 1 --统计异性角色数量
			-- end 
		end 
		if equip and self.player:inMyAttackRange(splayer, rangefix) and not self:isFriend(splayer) then 
			value_2 = value_2 +1 
		end 
	end 
	
	if value_1 == 0 and not equip and not self.player:getWeapon() then keep_value = keep_value + 1.25 end 
	if equip and value_2 == 0 then keep_value = keep_value + 2 end 
	if not equip and self.player:getWeapon() then keep_value = keep_value - 1.5 end --已经有武器了
	--if equip and value_3 ~= 0 then keep_value = keep_value + 1 end 
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	
end 
function Pay.Halberd_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 4.5
	local rangefix = 0
	local value_1 = 0 
	local value_2 = 0 --计算假设这是武器，扔掉之后我攻击范围内的敌人数量
	local value_3 = 0
	if equip then
		local card = self.player:getWeapon():getRealCard():toWeapon()
		rangefix = rangefix + card:getRange() - self.player:getAttackRange(false)
	end
	for _, splayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do	--找找自己攻击范围内非友军数量  
		if self.player:inMyAttackRange(splayer) and not self:isFriend(splayer) 	then 
			value_1 = value_1 + 1
			-- if splayer:isMale() ~= self.player:isMale() then 
				-- value_3 = value_3 + 1 --统计异性角色数量
			-- end 
		end 
		if equip and self.player:inMyAttackRange(splayer, rangefix) and not self:isFriend(splayer) then 
			value_2 = value_2 +1 
		end 
	end 
	
	if value_1 == 0 and not equip and not self.player:getWeapon() then keep_value = keep_value + 1.25 end 
	if equip and value_2 == 0 then keep_value = keep_value + 2 end 
	if not equip and self.player:getWeapon() then keep_value = keep_value - 1.5 end --已经有武器了
	--if equip and value_3 ~= 0 then keep_value = keep_value + 1 end 
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	
end 
function Pay.Kylin_bow_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 4.5
	local rangefix = 0
	local value_1 = 0 
	local value_2 = 0 --计算假设这是武器，扔掉之后我攻击范围内的敌人数量
	local value_3 = 0
	if equip then
		local card = self.player:getWeapon():getRealCard():toWeapon()
		rangefix = rangefix + card:getRange() - self.player:getAttackRange(false)
	end
	for _, splayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do	--找找自己攻击范围内非友军数量  
		if self.player:inMyAttackRange(splayer) and not self:isFriend(splayer) 	then 
			value_1 = value_1 + 1
			-- if splayer:isMale() ~= self.player:isMale() then 
				-- value_3 = value_3 + 1 --统计异性角色数量
			-- end 
		end 
		if equip and self.player:inMyAttackRange(splayer, rangefix) and not self:isFriend(splayer) then 
			value_2 = value_2 +1 
		end 
	end 
	
	if value_1 == 0 and not equip and not self.player:getWeapon() then keep_value = keep_value + 1.25 end 
	if equip and value_2 == 0 then keep_value = keep_value + 2 end 
	if not equip and self.player:getWeapon() then keep_value = keep_value - 1.5 end --已经有武器了
	--if equip and value_3 ~= 0 then keep_value = keep_value + 1 end 
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	
end 
function Pay.God_salvation_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 4
	local all_players = self.room:getAllPlayers()
	for _, player in sgs.qlist(all_players) do --获得所有角色的办法
		if player:isWounded()  then --判断是否已经受伤
			if self:isFriend(player) then 
				keep_value = keep_value + 2
			else
				keep_value = keep_value -1.7
			end 
		end
	end
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	
end 
function Pay.Snatch_value(self,func1,func2,func3,num,first,id)
	local keep_value = 9
	local value_1 = 0 
	local enemies1 = self.enemies
	if #enemies1 == 0 then keep_value = keep_value - 1 end 
	for _, enemy in ipairs(enemies1) do
		if (self.player:distanceTo(enemy) <= 1 or self.player:hasSkill("Luatannang")) and not enemy:isNude() then  
			local bool = true 
			if id and ((id > 83) or (id < 87)) and enemy:hasSkills("noswuyan|qianxun|weimu|yqingmin") then bool = false end 
			if bool then value_1 = value_1 + 1 end 
		end 
	end 
	if value_1 == 0 then keep_value = keep_value - 2.5 end 
	local friend2 = self.friends_noself
	for _, friend in ipairs(friend2) do
		if (self.player:distanceTo(friend) <= 1 or self.player:hasSkill("Luatannang")) and not friend:isAllNude() then  
			local equiplist = friend:getCards("e")
			if equiplist and not equiplist:isEmpty() then
				if friend:hasArmorEffect("silver_lion") and friend:isWounded() then
					if keep_value < 9  then keep_value = keep_value + 1.5 end 
				end
			end
			if (friend:getCards("j"):length() > 0 and not friend:containsTrick("YanxiaoCard")) then 
				if keep_value < 9  then keep_value = keep_value + 2 end 
			end 
			if self:hasSkills(sgs.lose_equip_skill, friend) then 
				if keep_value < 9  then keep_value = keep_value + 2 end 
			end
			if self:needKongcheng(friend) and friend:getHandcardNum() == 1 then 
				if keep_value < 9  then keep_value = keep_value + 1 end 
			end 
		end 
	end 

	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value
end 
function Pay.Amazing_grace_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 5.5
	local all_players = self.room:getAllPlayers()
	for _, player in sgs.qlist(all_players) do --获得所有角色的办法
		if player:isAlive()  then --判断是否存活
			if self:isFriend(player) then 
				keep_value = keep_value + 1
			else
				keep_value = keep_value -0.7
			end 
		end
	end
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	--
end 
function Pay.Savage_assault_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 6.5
	local all_players = self.room:getAllPlayers()
	for _, player in sgs.qlist(all_players) do --获得所有角色的办法
		if player:isAlive() and not ((Pay.GetCardNameFromCard(player:getArmor(),true) == "tengjia") or player:hasArmorEffect("vine")) 
			and not (player:hasSkill("huoshou") or player:hasSkill("juxiang") or (player:hasSkill("Luanilin") and not player:isKongcheng())) then --如果目标有技能祸首、巨象then --判断是否存活,有藤甲不计入此列
			if self:isFriend(player) then 
				keep_value = keep_value - 0.4 
				if player:isKongcheng() then keep_value = keep_value - 0.2 end 
				if player:getHp() == 1 and ((not player:hasSkill("Luachongsheng")) or (player:getMaxHp() > 1)) then keep_value = keep_value - 0.2 end  --考虑了妹红技能
				if (player:getKingdom() == "wei") and (player:getMaxHp() == 3) then keep_value = keep_value + 0.15 end --考虑魏国卖血
			else
				keep_value = keep_value + 0.4
				if player:isKongcheng() then keep_value = keep_value + 0.25 end 
				if player:getHp() == 1 and ((not player:hasSkill("Luachongsheng")) or (player:getMaxHp() > 1)) then keep_value = keep_value + 0.3 end  --考虑了妹红技能
			end 
		end
	end
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	--
end 
function Pay.Archery_attack_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 6.75
	local value_1 = 0 
	local slash_table = {} --统计杀数量
	local handcards = self.player:getHandcards()
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		if Pay.GetCardNameFromCard(c,true) == "sha" then table.insert(slash_table, c) end 
	end		
	local analeptic_table = {} --统计酒数量
	local handcards = self.player:getHandcards()
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		if Pay.GetCardNameFromCard(c,true) == "jiu" then table.insert(analeptic_table, c) end 
	end	
	for _, splayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do	--找找自己攻击范围内非友军数量  
		if self.player:inMyAttackRange(splayer) and not self:isFriend(splayer) 	then 
			value_1 = value_1 + 1
		end 
	end 
	if (#slash_table > 0) and (#analeptic_table > 0) and (value_1 > 0)  then keep_value = keep_value + 0.5 end --万箭齐发+酒杀
	local all_players = self.room:getAllPlayers()
	for _, player in sgs.qlist(all_players) do --获得所有角色的办法
		if player:isAlive() and not ((Pay.GetCardNameFromCard(player:getArmor(),true) == "tengjia") or player:hasArmorEffect("vine"))  then --如果目标有技能祸首、巨象then --判断是否存活,有藤甲不计入此列
			if self:isFriend(player) then 
				keep_value = keep_value - 0.4 
				if player:isKongcheng() then keep_value = keep_value - 0.2 end 
				if player:getHp() == 1 and ((not player:hasSkill("Luachongsheng")) or (player:getMaxHp() > 1)) then keep_value = keep_value - 0.2 end  --考虑了妹红技能
				if (player:getKingdom() == "wei") and (player:getMaxHp() == 3) then keep_value = keep_value + 0.15 end --考虑魏国卖血
			else
				keep_value = keep_value + 0.4
				if player:isKongcheng() then keep_value = keep_value + 0.25 end 
				if player:getHp() == 1 and ((not player:hasSkill("Luachongsheng")) or (player:getMaxHp() > 1)) then keep_value = keep_value + 0.3 end  --考虑了妹红技能
			end 
		end
	end
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	--
end 
function Pay.Moon_spear_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 4
	local rangefix = 0
	local value_1 = 0 
	local value_2 = 0 --计算假设这是武器，扔掉之后我攻击范围内的敌人数量
	local value_3 = 0
	if equip then
		local card = self.player:getWeapon():getRealCard():toWeapon()
		rangefix = rangefix + card:getRange() - self.player:getAttackRange(false)
	end
	for _, splayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do	--找找自己攻击范围内非友军数量  
		if self.player:inMyAttackRange(splayer) and not self:isFriend(splayer) 	then 
			value_1 = value_1 + 1
			-- if splayer:isMale() ~= self.player:isMale() then 
				-- value_3 = value_3 + 1 --统计异性角色数量
			-- end 
		end 
		if equip and self.player:inMyAttackRange(splayer, rangefix) and not self:isFriend(splayer) then 
			value_2 = value_2 +1 
		end 
	end 	
	if value_1 == 0 and not equip and not self.player:getWeapon() then keep_value = keep_value + 1.25 end 
	if equip and value_2 == 0 then keep_value = keep_value + 2 end 
	if not equip and self.player:getWeapon() then keep_value = keep_value - 1.5 end --已经有武器了
	--if equip and value_3 ~= 0 then keep_value = keep_value + 1 end 
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	
end 
function Pay.Duel_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 7
	local value_1 = 0 
	local value_2 = 0 --这些爸爸决斗不得
	local slash_table = {} --统计杀数量
	local handcards = self.player:getHandcards()
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		if Pay.GetCardNameFromCard(c,true) == "sha" then table.insert(slash_table, c) end 
	end		
	for _, splayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do	--找非友军数量  
		if key1 == 0 then 
			if not self:isFriend(splayer) then 
				local handcards = math.floor(splayer:getHandcardNum()/3)
				if #slash_table > handcards then 
					keep_value = keep_value + 0.6 
					key = key + 1 
				end 
				if splayer:getHp() == 1 then 
					keep_value = keep_value + 0.6 
					key = key + 1 
				end 
				if splayer:hasSkills("wusheng|wushuang|wushen|longdan|longhun") and handcards > 0 then value_2 = value_2 + 1 end 
			end 
		end 
	end 	
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	
end --:isKindOf("Collateral")
function Pay.Collateral_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 6.75
	local value_1 = 0 
	local value_2 = 0 --找装备着武器的敌军数量
	for _, splayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do	--找找自己攻击范围内非友军数量  
		if self.player:inMyAttackRange(splayer) and not self:isFriend(splayer) 	then 
			value_1 = value_1 + 1
			
		end 
		if not self:isFriend(splayer) and splayer:getWeapon() then value_2 = value_2 + 1 end 
	end 
	if value_1 == 0 then keep_value = keep_value + 0.75 end 
	if value_2 == 0 then  keep_value = keep_value - 2.75 end 
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	
end  
function Pay.OffensiveHorse_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 5.6
	local ma_table = {} --统计ma数量
	local handcards = self.player:getHandcards()
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		if Pay.GetCardNameFromCard(c,true) == "-1" then table.insert(ma_table, c) end 
	end		
	if not equip and self.player:getOffensiveHorse() then 
		keep_value = keep_value - 2.5
	end 
	if not self.player:getOffensiveHorse() and (#ma_table > 1) then 
		keep_value = keep_value - 2.5
	end 
	if equip then keep_value = keep_value + 0.25 end 
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	
end 
function Pay.DabaoHanshu(val,a,b,c,d,e,f,g)
	if type(val) ~= "number" then 
		return val(a,b,c,d,e,f,g)
	else
		return val
	end 
end 
function Pay.DefensiveHorse_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 5.1
	local ma_table = {} --统计ma数量
	local handcards = self.player:getHandcards()
	for _,c in sgs.qlist(handcards) do  --ipairs是table的迭代算子，sgs.qlist是list的迭代算子
		if Pay.GetCardNameFromCard(c,true) == "+1" then table.insert(ma_table, c) end 
	end		
	if not equip and self.player:getDefensiveHorse() then 
		keep_value = keep_value - 2.5
	end 
	if not self.player:getDefensiveHorse() and (#ma_table > 1) then 
		keep_value = keep_value - 2.5
	end 
	if equip then keep_value = keep_value + 0.25 end 
	if func1 then keep_value = func1(self,keep_value) end 
	return keep_value 	
end 
function Pay.Vine_value(self,func1,func2,func3,num,first,equip)
	local keep_value = 6.5 
	if self.player:getArmor() and not (self.player:hasArmorEffect("silver_lion") and self.player:isWounded()) then 
		keep_value = keep_value - 1.5	
	end
	if self:hasSkills(sgs.lose_equip_skill, player) then return 3.8 end
	if not self:damageIsEffective(player, sgs.DamageStruct_Fire) then keep_value = keep_value + 3 end
	if self.player:hasSkill("sizhan") then keep_value = keep_value + 0.5 end
	
	local fslash = sgs.Sanguosha:cloneCard("fire_slash")
	local tslash = sgs.Sanguosha:cloneCard("thunder_slash")
	--if player:isChained() and (not self:isGoodChainTarget(player, self.player, nil, nil, fslash) or not self:isGoodChainTarget(player, self.player, nil, nil, tslash)) then keep_value = keep_value - 3 end

	for _, enemy in ipairs(self:getEnemies(player)) do
		if enemy:hasSkills("luanji|luanji_po|yzhaoxiang") then return 2 end
		if (enemy:canSlash(player) and enemy:hasWeapon("fan")) or enemy:hasSkills("huoji|longhun|shaoying|zonghuo|wuling|ol_xueji")
		  or (enemy:hasSkill("yeyan") and enemy:getMark("@flame") > 0) then keep_value = keep_value - 5 end
		if getKnownCard(enemy, player, "FireSlash", true) >= 1 or getKnownCard(enemy, player, "FireAttack", true) >= 1 or
			getKnownCard(enemy, player, "fan") >= 1 then keep_value = keep_value - 3 end
	end
	return keep_value 	
end 
Card_Name_table = {}
for i = 1,250 do 
	local  p = i - 1 
	if (p < 30) or ((p > 110) and (p < 116)) 
		or ((p > 125) and (p < 130)) or ((p > 124) and (p < 129)) then
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "sha"
		Card_Name_table[i][2] = Pay.Slash_value
	end 
	if (p == 137) or (p == 140) or (p == 143) or (p == 150) or (p == 151) 
		or (p == 172) or (p == 174) or (p == 177) or (p == 175) or (p == 190) or (p == 191) 
		or (p == 212) or (p == 215) or (p == 219) or (p == 230) then
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "sha"
		Card_Name_table[i][2] = Pay.Slash_value	
	end 	
	if ((p > 196) and (p < 205) and (p ~= 200)) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "sha"
		Card_Name_table[i][2] = Pay.Slash_value		
	end 
	if (p > 29) and (p < 45) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "shan"
		Card_Name_table[i][2] = Pay.Jink_value		
	end 
	if ((p > 140) and (p < 146) and (p ~= 143)) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "shan"
		Card_Name_table[i][2] = Pay.Jink_value			
	end 	
	if ((p > 151) and (p < 158) and (p ~= 155)) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "shan"
		Card_Name_table[i][2] = Pay.Jink_value		
	end 
	if (p == 182) or (p == 185) or (p == 208) or (p == 209) or (p == 213) 	
		or (p == 214) or (p == 216) or (p == 217) or (p == 231) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "shan"
		Card_Name_table[i][2] = Pay.Jink_value		
	end 
	if (p > 44) and (p < 53) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "tao"
		Card_Name_table[i][2] = Pay.Peach_value	
	end 
	if (p == 138) or (p == 139) or (p == 148) or (p == 149) or (p == 183) 	
		or (p == 184) or (p == 189) or (p == 218) or (p == 232) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "tao"
		Card_Name_table[i][2] = Pay.Peach_value	
	end 
	if (p == 110) or (p == 116) or (p == 123) or (p == 129) or (p == 155) 	
		or (p == 243) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "jiu"
		Card_Name_table[i][2] = Pay.Analeptic_value	
	end 
	if (p == 53) or (p == 54) or (p == 169) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "zhugeliannv"
		Card_Name_table[i][2] = Pay.Crossbow_value	
	end 
	if (p == 55) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "shuanggujian"
		Card_Name_table[i][2] = Pay.DoubleSword_value
	end 	
	if (p == 56) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "qinggangjian"
		Card_Name_table[i][2] = Pay.QinggangSword_value
	end 	
	if (p == 57) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "qinglong"
		Card_Name_table[i][2] = Pay.qinglong_value
	end 
	if (p == 58) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "zhangba"
		Card_Name_table[i][2] = Pay.Spear_value
	end 	
	if (p == 59) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "guanshifu"
		Card_Name_table[i][2] = Pay.Axe_value
	end 	
	if (p == 60) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "fangtianhuaji"
		Card_Name_table[i][2] = Pay.Halberd_value
	end 	
	if (p == 61) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "qilingong"
		Card_Name_table[i][2] = Pay.Kylin_bow_value
	end 
	if (p == 62) or (p == 63) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "baguazhen"
		Card_Name_table[i][2] = 7.75
	end 	
	if (p == 65) or (p == 66) or (p == 64) or (p == 159) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "+1"
		Card_Name_table[i][2] = Pay.DefensiveHorse_value
	end 	
	if (p == 67) or (p == 68) or (p == 69) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "-1"
		Card_Name_table[i][2] = Pay.OffensiveHorse_value
	end 
	if (p == 70) or (p == 71) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "wugufengdeng"
		Card_Name_table[i][2] = Pay.Amazing_grace_value
	end 	
	if (p == 72) or (p == 73) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "taoyuanjieyi"
		Card_Name_table[i][2] = Pay.God_salvation_value
	end 
	if (p == 73) or (p == 74) or (p == 75) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "nanmanruqin"
		Card_Name_table[i][2] = Pay.Savage_assault_value
	end 
	if (p == 76) or (p == 73) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "wanjianqifa"
		Card_Name_table[i][2] = Pay.Archery_attack_value
	end 
	if (p == 161) or (p == 162) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "yinyueqiang"
		Card_Name_table[i][2] = Pay.Moon_spear_value
	end 	
	if (p == 77) or (p == 78) or (p == 79) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "juedou"
		Card_Name_table[i][2] = Pay.Duel_value
	end 
	if (p > 79) and (p < 84) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "wuzhongshengyou"
		Card_Name_table[i][2] = 10	
	end 
	if (p > 83) and (p < 89) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "shunshouqianyang"
		Card_Name_table[i][2] = Pay.Snatch_value	
	end 
	if (p > 88) and (p < 95) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "guohechaiqiao"
		Card_Name_table[i][2] = 7	
	end 
	if (p > 94) and (p <= 97) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "jiedaosharen"
		Card_Name_table[i][2] = Pay.Collateral_value	
	end 
	if ((p > 97) and (p < 100)) or (p == 107) or (p == 120) or (p == 134) or (p == 146) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "wuxiekeji"
		Card_Name_table[i][2] = 6.75	
	end 
	if (p > 99) and (p < 103) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "lebusishu"
		Card_Name_table[i][2] = 8	
	end 
	if (p == 103) or (p == 106) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "shandian"
		Card_Name_table[i][2] = 3.75
	end 
	if (p == 104) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "hanbingjian"
		Card_Name_table[i][2] = 4
	end 
	if (p == 105) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "renwangdun"
		Card_Name_table[i][2] = 7
	end 
	if (p == 108) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "gudingdao"
		Card_Name_table[i][2] = 4
	end 
	if (p == 109) or (p == 122) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "tengjia"
		Card_Name_table[i][2] = Pay.Vine_value
	end 
	if (p == 117) or (p == 124) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "bingliangcunduan"
		Card_Name_table[i][2] = 6.7
	end 
	if ((p > 129) and (p < 134)) or (p == 118) or (p == 119) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "tiesuolianhuan"
		Card_Name_table[i][2] = 5.5
	end 
	if (p == 121) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "baiyinshizi"
		Card_Name_table[i][2] = 6
	end 
	if (p == 147) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "zhuqueyushan"
		Card_Name_table[i][2] = 4.5
	end 
	if (p == 160) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "muniuliuma"
		Card_Name_table[i][2] = 6
	end 
	if (p == 220) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "yitianjian"
		Card_Name_table[i][2] = 5
	end 
	if ((p >= 135) and (p < 138)) or (p == 158) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "huogong"
		Card_Name_table[i][2] = 5	
	end 
	if (p == 226) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "houzi"
		Card_Name_table[i][2] = 9
	end 	
	if (p == 227) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "kuangfengjia"
		Card_Name_table[i][2] = 5
	end 	
	if (p == 228) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "yangxiujian"
		Card_Name_table[i][2] = 5
	end 	
	if (p == 229) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "wudaogang"
		Card_Name_table[i][2] = 5
	end 	
	if (p >= 249) then 
		Card_Name_table[i] = {}	
		Card_Name_table[i][1] = "quanxiang"
		Card_Name_table[i][2] = 0
	end 
end 
ABCDE_ROOM = 0
function Pay.GetCardNameFromCard(card,name,func)
	if not card then return end 
	local id = card:getId() + 1 
	if ABCDE_ROOM and ABCDE_ROOM ~=0 then ABCDE_ROOM:writeToConsole("验证有效性中"..id) end 
	if not Card_Name_table[id] then return end 
	if Card_Name_table[id][1] and name then return Card_Name_table[id][1] end 
	if Card_Name_table[id][1] and func then return Card_Name_table[id][2] end 
	-- if card:getId() < 0 then return false end 
	-- if (card:getId() < 30) or ((card:getId() > 110) and (card:getId() < 116)) 
		-- or ((card:getId() > 125) and (card:getId() < 130)) or ((card:getId() > 124) and (card:getId() < 129)) then return "sha" end 
	-- if (card:getId() == 137) or (card:getId() == 140) or (card:getId() == 143) or (card:getId() == 150) or (card:getId() == 151) 
		-- or (card:getId() == 172) or (card:getId() == 174) or (card:getId() == 177) or (card:getId() == 175) or (card:getId() == 190) or (card:getId() == 191) 
		-- or (card:getId() == 212) or (card:getId() == 215) or (card:getId() == 219) or (card:getId() == 230)then return "sha" end 
	-- if ((card:getId() > 196) and (card:getId() < 205) and (card:getId() ~= 200)) then return "sha" end 
	-- if (card:getId() > 29) and (card:getId() < 45) then return "shan" end 
	-- if ((card:getId() > 140) and (card:getId() < 146) and (card:getId() ~= 143)) then return "shan" end 	
	-- if ((card:getId() > 151) and (card:getId() < 158) and (card:getId() ~= 155)) then return "shan" end 
	-- if (card:getId() == 182) or (card:getId() == 185) or (card:getId() == 208) or (card:getId() == 209) or (card:getId() == 213) 	
		-- or (card:getId() == 214) or (card:getId() == 216) or (card:getId() == 217) or (card:getId() == 231) then return "shan" end 
	-- if (card:getId() > 44) and (card:getId() < 53) then return "tao" end 
	-- if (card:getId() == 138) or (card:getId() == 139) or (card:getId() == 148) or (card:getId() == 149) or (card:getId() == 183) 	
		-- or (card:getId() == 184) or (card:getId() == 189) or (card:getId() == 218) or (card:getId() == 232) then return "tao" end 
	-- if (card:getId() == 110) or (card:getId() == 116) or (card:getId() == 123) or (card:getId() == 129) or (card:getId() == 155) 	
		-- or (card:getId() == 243) then return "jiu" end 
	-- if (card:getId() == 53) or (card:getId() == 54) or (card:getId() == 169) then return "zhugeliannv" end 
	-- if (card:getId() == 55) then return "shuanggujian" end 	
	-- if (card:getId() == 56) then return "qinggangjian" end 	
	-- if (card:getId() == 105) or (card:getId() == 195) then return "renwangdun" end 	
	-- if (card:getId() == 109) or (card:getId() == 122) then return "tengjia" end 
	-- if (card:getId() == 62) or (card:getId() == 63) or (card:getId() == 207) then return "baguazhen" end 
end 
--[[--
-   partition: 获得快排中介值位置
-   @param: list, low, high - 参数描述
-   @return: pivotKeyIndex - 中介值索引
]]
function Pay.partition(self, list, left, right)
    local high = right
    local low = left
	local low2 = low
	local high2 = high
	if high == low then return end 
    local pivotKey = list[low][2] -- 定义一个中介值,它总是数组第1个
    local function Swapp(list,xiabiao1,xiabiao2)
		if not list[xiabiao1] then return false end 
		if not list[xiabiao2] then return false end 
		local n = list[xiabiao2]
		list[xiabiao2] = list[xiabiao1]
		list[xiabiao1] = n
	end 
	local p = 1
    -- 下面将中介值移动到列表的中间
    -- 当左索引与右索引相邻时停止循环
    while low < high do
		self.room:writeToConsole("快排第"..p.."次")
		for i = low2 , high2 do 
			self.room:writeToConsole(list[i][2])
		end 
        -- 假如当前右值大于等于中介值则右索引左移
        -- 否则交换中介值和右值位置
        while low < high and list[high][2] >= pivotKey do
            high = high - 1
        end
        Swapp(list, low, high)

        -- 假如当前左值小于等于中介值则左索引右移
        -- 否则交换中介值和左值位置
        while low < high and list[low][2] <= pivotKey do
            low = low + 1
        end
        Swapp(list, low, high)
		p = p + 1
    end
	local id = list[low][3]:getId() --验证快排有效性
	self.room:writeToConsole("卡ID为："..id.."名为："..list[low][1].."技能卡价值为："..list[low][2].."此时排列手牌中的价值序号是："..low)
		for i = low2 , high2 do 
			self.room:writeToConsole(list[i][2])
		end 
    return low
end
-- --[[--
-- -   orderByQuick: 快速排序
-- -   @param: list, low, high - 参数描述
-- -    @return: list - table
-- ]]
function Pay.orderByQuick(self, list, left, right)
	local low = left
	local high = right 
    if low < high then
        -- 返回列表中中介值所在的位置，该位置左边的值都小于等于中介值，右边的值都大于等于中介值
        local pivotKeyIndex = Pay.partition(self, list, low, high)
		if not pivotKeyIndex then return end 
        -- 分别将中介值左右两边的列表递归快排
        Pay.orderByQuick(self, list, low, pivotKeyIndex - 1)
        Pay.orderByQuick(self, list, pivotKeyIndex + 1, high)
    end
end
--[[
你需要传入一个table来控制某张卡的价值，table1里面存入卡名，table2里面存决定是哪个方面（1、2、3），table3里面存一个控制函数
]]--
function Pay.SortCardByKeepValue(self,cardlist,table1,table2,huase_func)
	ABCDE_ROOM = self.room
	self.room:writeToConsole("验证有效性中")
	local keep_value_table = {}
	local ex_table = {}
	local handcards = cardlist
	for _, card in ipairs(handcards) do	
		table.insert(ex_table, card) 
	end 
	for i = 1,#ex_table do --基本的整理好价值
		keep_value_table[i] = {}
		keep_value_table[i][1] = Pay.GetCardNameFromCard(ex_table[i],true)
		keep_value_table[i][2] = Pay.GetCardNameFromCard(ex_table[i],false,true) --这里存储的是一个函数
		keep_value_table[i][3] = ex_table[i]
		local mowei = false 
		if keep_value_table[i][1] == "sha" then mowei = keep_value_table[i][3]:getId() end 
		if keep_value_table[i][1] == "shunshouqianyang" then mowei = keep_value_table[i][3]:getId() end 
		if self.player:getWeapon() and self.player:getWeapon():getId() == keep_value_table[i][3]:getId() then mowei = true end 
		if self.player:getArmor() and self.player:getArmor():getId() == keep_value_table[i][3]:getId() then mowei = true end 
		if self.player:getDefensiveHorse() and self.player:getDefensiveHorse():getId() == keep_value_table[i][3]:getId() then mowei = true end 
		if self.player:getOffensiveHorse() and self.player:getOffensiveHorse():getId() == keep_value_table[i][3]:getId() then mowei = true end 
		if table1 and table1[1] == keep_value_table[i][1] and type(keep_value_table[i][2]) ~= "number" then 
			if table1[2] == 1 then 
				keep_value_table[i][2] = keep_value_table[i][2](self,table1[3],false,false,0,false,mowei)
			end 
			if table1[2] == 2 then 
				keep_value_table[i][2] = keep_value_table[i][2](self,false,table1[3],false,0,false,mowei)
			end 	
			if table1[2] == 3 then 
				keep_value_table[i][2] = keep_value_table[i][2](self,false,false,table1[3],0,false,mowei)
			end 	
		else
			if type(keep_value_table[i][2]) ~= "number" then 
				if keep_value_table[i][2] == nil then self.room:writeToConsole("1220遇到空值，原卡id是" .. keep_value_table[i][3]:getId()) end 
				keep_value_table[i][2] = keep_value_table[i][2](self,false,false,false,0,false,mowei)
			end 
		end 
	end 
	Pay.orderByQuick(self, keep_value_table, 1, #keep_value_table) --快！速！排！序！法！
	for i = 1,#keep_value_table do --验证快排有效性
		local id = keep_value_table[i][3]:getId() + 1 
		self.room:writeToConsole("最后一次验证有效性中")
		self.room:writeToConsole("卡ID为："..id.."名为："..keep_value_table[i][1].."技能卡价值为："..keep_value_table[i][2].."此时排列手牌中的价值序号是："..i)
	end 
	return keep_value_table
end 
--[[考虑是否把一张卡用掉还是给掉
返回值为真表示老子自己要用
dummy_use表示一种虚拟的计算，看看假设我要用这张卡的话，情况如何
getEnemyNumBySeat表示我和指定玩家之间的玩家中有几个敌人
]]
function Pay.GiveOrUse(self,card,friend)
	local num = math.random()*10
	local num1 = self.player:getHandcardNum() - self.player:getMaxCards() + 1 
	if self.player:isWounded() then num1 = num1 + (2 - self.player:getHp()) end 
	num2 = num1*num1
	if Pay.GetCardNameFromCard(card,true) == "wuzhongshengyou" and ((num2 <= num) or (num1 < 0)) then return true end 
	if not self.player:getArmor() or (self.player:hasArmorEffect("silver_lion") and self.player:isWounded()) then 
		if card:isKindOf("Armor") then return true end 		
	end
	if card:isAvailable(self.player) and Pay.GetCardNameFromCard(card,true) == "nanmanruqin" and (Pay.Savage_assault_value(self,false,false,false,0,false,false) > 6.7) then return true end 
	if card:isAvailable(self.player) and Pay.GetCardNameFromCard(card,true) == "wanjianqifa" and (Pay.Archery_attack_value(self,false,false,false,0,false,false) > 6.7) then return true end 
	if friend then 
		if not self.player:getOffensiveHorse() and Pay.GetCardNameFromCard(card,true) == "-1"
			and (friend:getOffensiveHorse() or math.random() > 0.5) then return true end 
		if not self.player:getDefensiveHorse() and Pay.GetCardNameFromCard(card,true) == "+1"
			and (friend:getDefensiveHorse() or math.random() > 0.5) then return true end 
		if card:isAvailable(self.player) and (card:isKindOf("Slash") or card:isKindOf("Duel") or card:isKindOf("Snatch") or card:isKindOf("Dismantlement")) then
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			local cardtype = card:getTypeId()
			self["use" .. sgs.ai_type_name[cardtype + 1] .. "Card"](self, card, dummy_use)
			if dummy_use.card and dummy_use.to:length() > 0 then
				if card:isKindOf("Slash") or card:isKindOf("Duel") then
					local t1 = dummy_use.to:first()
					if dummy_use.to:length() > 1 then 
						return true 
					elseif t1:getHp() == 1 or sgs.card_lack[t1:objectName()]["Jink"] == 1
							or t1:isCardLimited(sgs.Sanguosha:cloneCard("jink"), sgs.Card_MethodResponse) then return true
					end
				elseif (card:isKindOf("Snatch") or card:isKindOf("Dismantlement")) and self:getEnemyNumBySeat(self.player, friend) > 0 then
					local hasDelayedTrick
					for _, p in sgs.qlist(dummy_use.to) do
						if self:isFriend(p) and (self:willSkipDrawPhase(p) or self:willSkipPlayPhase(p)) then hasDelayedTrick = true break end
					end
					if hasDelayedTrick then return true end
				end
			end
		elseif card:isAvailable(self.player) and self:getEnemyNumBySeat(self.player, friend) > 0 and (card:isKindOf("Indulgence") or card:isKindOf("SupplyShortage")) then
			local dummy_use = { isDummy = true }
			self:useTrickCard(card, dummy_use)
			if dummy_use.card then return true end
		elseif card:isAvailable(self.player) and self:isFriend(self.player:getNextAlive()) and Pay.GetCardNameFromCard(card,true) == "wugufengdeng" and #self.friends >= #self.enemies then
			return true 
		end
	else
		for _, friend2 in ipairs(self.friends_noself) do
			if not self.player:getOffensiveHorse() and Pay.GetCardNameFromCard(card,true) == "-1"
				and (friend2:getOffensiveHorse() or math.random() > 0.5) then return true end 
			if not self.player:getDefensiveHorse() and Pay.GetCardNameFromCard(card,true) == "+1"
				and (friend2:getDefensiveHorse() or math.random() > 0.5) then return true end 
			if card:isAvailable(self.player) and (card:isKindOf("Slash") or card:isKindOf("Duel") or card:isKindOf("Snatch") or card:isKindOf("Dismantlement")) then
				local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
				local cardtype = card:getTypeId()
				self["use" .. sgs.ai_type_name[cardtype + 1] .. "Card"](self, card, dummy_use)
				if dummy_use.card and dummy_use.to:length() > 0 then
					if card:isKindOf("Slash") or card:isKindOf("Duel") then
						local t1 = dummy_use.to:first()
						if dummy_use.to:length() > 1 then 
							return true 
						elseif t1:getHp() == 1 or sgs.card_lack[t1:objectName()]["Jink"] == 1
								or t1:isCardLimited(sgs.Sanguosha:cloneCard("jink"), sgs.Card_MethodResponse) then return true
						end
					elseif (card:isKindOf("Snatch") or card:isKindOf("Dismantlement")) and self:getEnemyNumBySeat(self.player, friend2) > 0 then
						local hasDelayedTrick
						for _, p in sgs.qlist(dummy_use.to) do
							if self:isFriend(p) and (self:willSkipDrawPhase(p) or self:willSkipPlayPhase(p)) then hasDelayedTrick = true break end
						end
						if hasDelayedTrick then return true end
					end
				end
			elseif card:isAvailable(self.player) and self:getEnemyNumBySeat(self.player, friend2) > 0 and (card:isKindOf("Indulgence") or card:isKindOf("SupplyShortage")) then
				local dummy_use = { isDummy = true }
				self:useTrickCard(card, dummy_use)
				if dummy_use.card then return true end
			elseif card:isAvailable(self.player) and self:isFriend(self.player:getNextAlive()) and Pay.GetCardNameFromCard(card,true) == "wugufengdeng" and #self.friends >= #self.enemies then
				return true 
			end		
		end 
	end 
end 
--[[
考虑是否有改判
--2表示敌人有改判，1表示自己人有改判
]]--
function Pay.NoJudgement(self,red)
	local panding1 = {}
	local panding2 = {}
	local i = 1
	local j = 1
	for _, aplayer in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if aplayer:hasSkills("guicai|guidao") and not aplayer:isKongcheng() then
			if self:isFriend(aplayer) and (not red or player:hasSkill("guicai")) then 
				panding1[i] = aplayer
				i = i + 1
			elseif not self:isFriend(aplayer) then
				panding2[i] = aplayer		
				j = j + 1				
			end 
		end
	end
	if #panding1 > 0 and #panding2 > 0 then 
		local Seat1 = (panding1[#panding1]:getSeat() - self.player():getSeat()) % #players 
		local Seat2 = (panding2[#panding2]:getSeat() - self.player():getSeat()) % #players 
		if Seat2 > Seat1 then return 2 end 
		return 1
	end 
	if #panding1 == 0 and #panding2 > 0 then return 2 end 
	if #panding2 == 0 and #panding1 > 0 then return 1 end 
	return 0
end 
return Pay