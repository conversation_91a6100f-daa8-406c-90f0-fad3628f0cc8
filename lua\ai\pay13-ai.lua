
math.randomseed(tostring(os.time()):reverse():sub(1, 7)) --------设置时间种子

local function useG<PERSON><PERSON><PERSON>(self, enermy)
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)

	local diamond_card

	self:sortByUseValue(cards, true)
	
	for _,card in ipairs(cards) do
		if (card:getSuit() == sgs.Card_Spade) then 
			local useAll = false
			local slash_0 = sgs.Sanguosha:cloneCard("fire_slash", card:getSuit(), card:getNumber())
			slash_0:addSubcard(card)
			slash_0:setSkillName("luaguihuor")		
			if self:canKillEnermyAtOnce(false, slash_0) then useAll = true end 
		
			local heavy_dmg = false 
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			self:useBasicCard(slash_0, dummy_use)
			if not dummy_use.to:isEmpty() then
				for _, playerX in sgs.qlist(dummy_use.to) do
					if self:hasHeavySlashDamage(self.player, slash_0, playerX) then heavy_dmg = true end 
				end 
			end 	
			if (not enermy) or heavy_dmg or useAll then 
				local snatch = sgs.Sanguosha:cloneCard("snatch")
				local dismantlement = sgs.Sanguosha:cloneCard("fire_slash")
				local val1 = self:getUseValue(snatch)
				local val0 = self:getUseValue(dismantlement) + 2
				local bool_0 = (self:getUseValue(card) <= val0) or (heavy_dmg and (self:getUseValue(card) <= val1))
				local bool_2 = card:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip))
				local bool_3 = (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu"))
					and ((self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
					or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
						
				if (((not self:OverFlowPeach(card)) and not isCard("ExNihilo", card, self.player)) or useAll)
				and not bool_3 and not bool_2
				and (bool_0 or sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_Residue, self.player, sgs.Sanguosha:cloneCard("fire_slash")) > 0) then
					diamond_card = card
					break
				end
			end 
		end 
	end
	return diamond_card
end 
local luaguihuor_skill = {} -- 初始化 shenpan_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 shenpan_skill 只是为了出于习惯
luaguihuor_skill.name = "luaguihuor" -- 设置 name
table.insert(sgs.ai_skills, luaguihuor_skill) -- 把这个表加入到 sgs.ai_skills 中
luaguihuor_skill.getTurnUseCard = function(self) 
	if self.player:hasUsed("#luaguihuor") then return false end 
	local sp_rin = self.room:findPlayerBySkillName("luaguihuo")
	if (not sp_rin) or (not sp_rin:isAlive())  then return false end 
	if self:isFriend(sp_rin) then
		if useGuihuo(self) then return sgs.Card_Parse("#luaguihuor:.:") end 
	else
		if useGuihuo(self, true) then return sgs.Card_Parse("#luaguihuor:.:") end 
	end 

end 

sgs.ai_skill_use_func["#luaguihuor"] = function(card, use, self)
	local usecard 
	local sp_rin = self.room:findPlayerBySkillName("luaguihuo")
	if (not sp_rin) or (not sp_rin:isAlive()) then return end 

	if self:isFriend(sp_rin) then
		usecard = useGuihuo(self) 
	else
		usecard = useGuihuo(self, true) 
	end 	

	local slash = sgs.Sanguosha:cloneCard("fire_slash", usecard:getSuit(), usecard:getNumber())
	slash:setSkillName("luaguihuor")
	slash:addSubcard(usecard:getEffectiveId())	
	local dummy_use = { isDummy = false , to = sgs.SPlayerList() }
	self:useBasicCard(slash, dummy_use)	
	if dummy_use.card and dummy_use.card:isKindOf("FireSlash") and dummy_use.to and dummy_use.to:length() > 0 then 
		use.card = sgs.Card_Parse("#luaguihuor:" .. usecard:getId() ..":") 
		if use.to then 
			use.to = dummy_use.to 
			return
		end 		
	end 	

end 

sgs.ai_use_priority.luaguihuor = 4

sgs.ai_skill_playerchosen.luaguihuor = function(self, targets)
    local targetlist = sgs.QList2Table(targets)	
	for _, target in ipairs(targetlist) do	--杀敌
        if self:isFriend(target) and target:objectName() ~= self.player:objectName() then return target end
    end	
	return self.player
end 

local luakuaiqing_skill = {} -- 初始化 shenpan_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 shenpan_skill 只是为了出于习惯
luakuaiqing_skill.name = "luakuaiqing" -- 设置 name
table.insert(sgs.ai_skills, luakuaiqing_skill) -- 把这个表加入到 sgs.ai_skills 中
luakuaiqing_skill.getTurnUseCard = function(self) 
	return sgs.Card_Parse("#luakuaiqing:.:") 
end 

sgs.ai_skill_use_func["#luakuaiqing"] = function(card, use, self)
	local pcards = sgs.QList2Table(self.player:getCards("h"))
	local jinks = {}
	local Peach = {}
	local slashes = {}
	for _, cardP in ipairs(pcards) do
		if cardP:isKindOf("Jink") and self.player:canDiscard(self.player, cardP:getEffectiveId()) then table.insert(jinks, cardP) end
	end
	for _, cardP in ipairs(pcards) do
		if cardP:isKindOf("Peach") and self.player:canDiscard(self.player, cardP:getEffectiveId()) then table.insert(Peach, cardP) end
	end
	for _, cardA in ipairs(pcards) do
		if cardA:isKindOf("Slash") and self.player:canDiscard(self.player, cardA:getEffectiveId()) then table.insert(slashes, cardA) end
	end 
	self:sortByUseValue(slashes)
	self:sortByKeepValue(jinks)
	local x = self.player:usedTimes("#luakuaiqing")
	local acards = self:getTurnUse(true)
	local y = self.player:getHandcardNum() - #acards
	local z = self.player:getMaxCards()

	if x == 0 then
		if y >= z then 
			if #acards ~= 0 then 
				self:sortByUseValue(pcards, true)
			else
				self:sortByKeepValue(pcards)
			end 
			if not (pcards[1]:isKindOf("Slash") or (self:hasCrossbowEffect() or self:getCardsNum("Crossbow") > 0)) and not pcards[1]:isKindOf("Duel")
				and not (pcards[1]:isKindOf("Banquet") and #self.friends > 1)
				and not (pcards[1]:isKindOf("Weapon") and not self.player:getWeapon()) then
				use.card = sgs.Card_Parse("#luakuaiqing:" .. pcards[1]:getId() ..":") 
				if use.to then 
					use.to = sgs.SPlayerList() 
					return
				end
			end 
		end 
		
		for _, cardD in ipairs(acards) do
			if #jinks > 0 then
				if cardD:isKindOf("Slash") then
					local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
					self:useBasicCard(cardD, dummy_use)
					if not dummy_use.to:isEmpty() then
						for _, playerX in sgs.qlist(dummy_use.to) do
							if not playerX:isKongcheng() and not self:YouMu2(playerX, true) then
								if (self:getCardsNum("Jink") > 1) or not self:isWeak() then
									use.card = sgs.Card_Parse("#luakuaiqing:" .. jinks[1]:getId() ..":")
									if use.to then
										use.to = sgs.SPlayerList()
										return
									end
								end
							end
						end
					end
				end
			end
			if #Peach > 0 then
				if cardD:isKindOf("Slash") then
					local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
					self:useBasicCard(cardD, dummy_use)
					if not dummy_use.to:isEmpty() then
						for _, playerX in sgs.qlist(dummy_use.to) do
							if ((self:isWeak(playerX) and (getCardsNum("Peach", playerX, self.player) >= 1))
									or (playerX:hasSkill("luafenxing") and playerX:getHp() == 1)
									or (playerX:hasLordSkill("luashenxing") and playerX:hasSkill("luaouxiang") and playerX:getHp() == 1))
									and self:YouMu2(playerX, true) and not playerX:isKongcheng() then
								use.card = sgs.Card_Parse("#luakuaiqing:" .. Peach[1]:getId() ..":")
								if use.to then
									use.to = sgs.SPlayerList()
									return
								end
							end
						end
					end
				end
			end
			if #slashes > 0 then
					if cardD:isKindOf("Duel") then
					use.card = sgs.Card_Parse("#luakuaiqing:" .. slashes[1]:getId() ..":")
					if use.to then
						use.to = sgs.SPlayerList()
						return
					end
				end
			end
		end 
	elseif x == 1 then
		if self.player:getHandcardNum() < 2 then return end
		if y >= z then 
			if #acards ~= 0 then 
				self:sortByUseValue(pcards, true)
			else
				self:sortByKeepValue(pcards)
			end
			if not (pcards[1]:isKindOf("Slash") or (self:hasCrossbowEffect() or self:getCardsNum("Crossbow") > 0)) and not pcards[1]:isKindOf("Duel")
					and not (pcards[1]:isKindOf("Banquet") and #self.friends > 1)
					and not (pcards[1]:isKindOf("Weapon") and not self.player:getWeapon()) then
				use.card = sgs.Card_Parse("#luakuaiqing:" .. pcards[1]:getId() .. "+" .. pcards[2]:getId() ..":")
				if use.to then 
					use.to = sgs.SPlayerList() 
					return
				end 
			end 
		end
		if self:getOverflow() <= 0 and #acards == 0 and self.player:containsTrick("gainb") then
			self:sortByKeepValue(pcards)
			use.card = sgs.Card_Parse("#luakuaiqing:" .. pcards[1]:getId() .. "+" .. pcards[2]:getId() ..":")
			if use.to then
				use.to = sgs.SPlayerList()
				return
			end
		end
	end 
end


sgs.ai_use_priority.luakuaiqing = 7

sgs.ai_choicemade_filter.skillInvoke.luajiejie = function(self, player, promptlist)
	if promptlist[3] == "yes" then
		sgs.updateIntention(player, self.room:getCurrent(), 40)
	else
		sgs.updateIntention(player, self.room:getCurrent(), -20)
	end
end
sgs.ai_skill_invoke.luajiejie = function(self, data)
	local target = data:toPlayer()
	if not self:isFriend(target) then
		local cards = sgs.QList2Table(target:getHandcards())
		self:sortByUseValue(cards)
		for _, card in ipairs(cards) do
			if card:isKindOf("Peach") or card:isKindOf("Analeptic") then return true end
		end
		for _, card in ipairs(cards) do
			if not card:isKindOf("Hui") then return true end
		end
	end
end
sgs.ai_skill_cardchosen.luajiejie = function(self, who, flags)
	local cards = sgs.QList2Table(who:getHandcards())
	self:sortByUseValue(cards)
	if cards[1]:isKindOf("Duel") or cards[1]:isKindOf("Indulgence") or cards[1]:isKindOf("ExNihilo") or cards[1]:isKindOf("Peach") then
		return cards[1]:getEffectiveId()
	end
	for _, card in ipairs(cards) do
		if card:isKindOf("Jink") then return card:getEffectiveId() end
	end
	return cards[1]:getEffectiveId()
end

local luazhulou_skill = {} -- 初始化 shenpan_skill 为空表
-- 这是一个本地变量，变量名并不重要，取成 shenpan_skill 只是为了出于习惯
luazhulou_skill.name = "luazhulou" -- 设置 name
table.insert(sgs.ai_skills, luazhulou_skill) -- 把这个表加入到 sgs.ai_skills 中
luazhulou_skill.getTurnUseCard = function(self) 
	local pcards = sgs.QList2Table(self.player:getCards("h"))
	local slashes = {}
	for _, card in ipairs(pcards) do
		if sgs.Sanguosha:getCard(card:getId()):isKindOf("Slash") and self.player:canDiscard(self.player, card:getEffectiveId()) then table.insert(slashes, card) end 
	end 	
	if (#slashes > 0) and self.player:getHandcardNum() ~= self.player:getHp() then 
		return sgs.Card_Parse("#luazhulou:.:") 
	end 
end 

sgs.ai_skill_use_func["#luazhulou"] = function(cardS, use, self)
	local pcards = sgs.QList2Table(self.player:getCards("h"))
	local x = self.player:usedTimes("#luazhulou") 
	local slashes = {}
	local analeptics = {}
	for _, card in ipairs(pcards) do
		if sgs.Sanguosha:getCard(card:getId()):isKindOf("Slash") and self.player:canDiscard(self.player, card:getEffectiveId()) then table.insert(slashes, card) end 
		if card:isKindOf("Analeptic") and not self.player:isCardLimited(card, sgs.Card_MethodUse) then table.insert(analeptics, card) end 
	end 	
	self:sortByKeepValue(slashes)
	local abc = #self:getTurnUse(true)
	if ((#slashes == 1) and x == 0) then 
		local fire_slash = sgs.Sanguosha:cloneCard("fire_slash")
		local dummy_use = { isDummy = true , to = sgs.SPlayerList() }
		self:useBasicCard(fire_slash, dummy_use)	
		if self:slashIsAvailable(self.player, fire_slash) then 
			if (#analeptics > 0) and (dummy_use.to:length() > 0) and (self.player:getHandcardNum() == self.player:getHp() + 2) then 
				self:sortByUseValue(analeptics, true)
				local analeptic = analeptics[1]
				if analeptic and self:shouldUseAnaleptic(dummy_use.to:at(0), dummy_use.card) then 
					if dummy_use.to:length() > 0 then 
						if self:isGoodChainTarget(dummy_use.to:at(0), self.player, nil, nil, fire_slash, 1) and (#(self:getChainedEnemies()) ~= #self.enemies) then 
							use.card = analeptic
							if use.to then 
								use.to = sgs.SPlayerList()
								return
							end 
						elseif (self:isGoodChainTarget(dummy_use.to:at(0), self.player, sgs.DamageStruct_Fire, 2, nil, 1) and self:YouMu2(dummy_use.to:at(0), true) and not dummy_use.to:at(0):isWounded() and not dummy_use.to:at(0):isChained())
							or (self:isGoodChainTarget(dummy_use.to:at(0), self.player, sgs.DamageStruct_Fire, 1, nil, 3) and self:YouMu2(dummy_use.to:at(0), true) and not dummy_use.to:at(0):isChained()) then 
							use.card = analeptic
							if use.to then 
								use.to = sgs.SPlayerList()
								return
							end 
						end 
					end 				
				end 
			end 
			if (self.player:getHandcardNum() == self.player:getHp() + 1) then 
				if dummy_use.to:length() > 0 then 
					if self:isGoodChainTarget(dummy_use.to:at(0), self.player, nil, nil, fire_slash, 1) and (#(self:getChainedEnemies()) ~= #self.enemies) then 
						use.card = sgs.Card_Parse("#luazhulou:" .. slashes[1]:getId() ..":") 
						if use.to then 
							if not dummy_use.to:at(0):isChained() then 
								use.to:append(dummy_use.to:at(0))
							else
								self:sort(self.enemies, "defense")
								for _, enemy in ipairs(self.enemies) do
									if not enemy:isChained() then use.to:append(dummy_use.to:at(0));break end 
								end 
							end 
							return
						end
					elseif (self:isGoodChainTarget(dummy_use.to:at(0), self.player, sgs.DamageStruct_Fire, 2, nil, 1) and self:YouMu2(dummy_use.to:at(0), true) and not dummy_use.to:at(0):isWounded() and not dummy_use.to:at(0):isChained())
						or (self:isGoodChainTarget(dummy_use.to:at(0), self.player, sgs.DamageStruct_Fire, 1, nil, 3) and self:YouMu2(dummy_use.to:at(0), true) and not dummy_use.to:at(0):isChained()) then 
						use.card = sgs.Card_Parse("#luazhulou:" .. slashes[1]:getId() ..":") 
						if use.to then 	
							use.to:append(dummy_use.to:at(0))
							return							
						end 
					end 
				end 
			end 
		end 
		local bool_0 = false 
		for _, friend in ipairs(self.friends) do
			if friend:hasSkills(sgs.shuxin_skill) then bool_0 = true; break end 
		end 	

		local bool_1 = false 
		for _, enemy in ipairs(self.enemies) do
			if enemy:hasSkills(sgs.shuxin_skill) then bool_1 = true; break end 
		end 

		local komeiji = self.room:findPlayerBySkillName("luaqiangwei")
		
		if bool_0 or (self.player:getHandcardNum() == self.player:getHp() and abc == 0 and self:getCardsNum("Jink") > 0) then 
			if #(self:getChainedEnemies()) > 0 then 
				local target 
				self:sort(self.enemies, "defense")
				for _, enemy in ipairs(self.enemies) do
					if not enemy:isChained() then target = enemy;break end 
				end 	
				if target then 
					use.card = sgs.Card_Parse("#luazhulou:" .. slashes[1]:getId() ..":") 
					if use.to then 
						use.to:append(target)
						return	
					end 
				end 
			end 
		end 
		if (self.player:getHandcardNum() == self.player:getHp() and abc == 0 and self:getCardsNum("Jink") > 0)
			or (bool_1 and #(self:getChainedFriends()) > 1 and math.random() > 0.65) then 
			if #(self:getChainedFriends()) > 0 then 
				local target
				self:sort(self.friends, "defense")
				for _, friend in ipairs(self.friends) do
					if friend:isChained() then target = friend;break end 
				end 	
				if target then 
					use.card = sgs.Card_Parse("#luazhulou:" .. slashes[1]:getId() ..":") 
					if use.to then 
						use.to:append(target)
						return	
					end 
				end 
			end 		
		end 
		if (komeiji and self:isFriend(komeiji) and komeiji:isChained() and komeiji:getMark("@qiangweiw") == 1 and (math.random() > 0.4 or self:isWeak(friend))) then 
			local target = komeiji;
			if target then 
				use.card = sgs.Card_Parse("#luazhulou:" .. slashes[1]:getId() ..":") 
				if use.to then 
					use.to:append(target)
					return	
				end 
			end 				
		end 
	else

		local count = 0
		count = #self.enemies - #(self:getChainedEnemies())
		count = count + #(self:getChainedFriends())
		if (count >= 2) or ((x ~= 0) and count > 0) then 
			local target 
			self:sort(self.enemies, "defense")
			self:sort(self.friends, "defense")
			for _, friend in ipairs(self.friends) do
				if friend:isChained() then target = friend;break end 
			end 	
			if not target then 
				for _, enemy in ipairs(self.enemies) do
					if not enemy:isChained() then target = enemy;break end 
				end 	
			end 
			if target then 
				use.card = sgs.Card_Parse("#luazhulou:" .. slashes[1]:getId() ..":") 
				self.room:writeToConsole("山女测试X")
				if use.to then 
					use.to:append(target)
					return	
				end 
			end 				
		end 
	end 

end 
sgs.ai_use_priority.luazhulou = 12
sgs.ai_card_intention.luazhulou = sgs.ai_card_intention.IronChain 

sgs.ai_skill_playerchosen.luazhulou1 = function(self, targets)
	self:sort(self.enemies, "defense")
	for _, enemy in ipairs(self.enemies) do
		if enemy:isChained() and (self:isGoodChainTarget(enemy, self.player, sgs.DamageStruct_Fire, 1, nil, 3)) then return enemy end 
	end 
	for _, enemy in ipairs(self.enemies) do
		if enemy:isChained() and not enemy:isWounded() then return enemy end 
	end 
	for _, enemy in ipairs(self.enemies) do
		if enemy:isChained() and (self:isGoodChainTarget(enemy, self.player, sgs.DamageStruct_Fire, 1, nil)) then return enemy end 
	end 	
	for _, enemy in ipairs(self.enemies) do
		return enemy
	end 
end 

sgs.ai_skill_playerchosen.luazhulou2 = function(self, targets)
	local target = self:findPlayerToDraw(true, 1)
	if target then return target end 
	return self.player
end 

sgs.ai_skill_playerchosen.luazhulou3 = function(self, targets)
	if #self.enemies > 0 then 
		local function getDiaC(enemy)
			local clubnum = 0
			for _, equip in sgs.qlist(enemy:getEquips()) do
				if equip:getSuit() == sgs.Card_Diamond then 
					clubnum = clubnum + 1 
				end 
			end 
			for _, card in sgs.qlist(enemy:getHandcards()) do
				local flag = string.format("%s_%s_%s", "visible", enemy:objectName(), self.room:getCurrent():objectName())
				if (card:hasFlag("visible") or card:hasFlag(flag)) and card:getSuit() == sgs.Card_Diamond then
					clubnum = clubnum + 1 
				end 
			end 
			return clubnum
		end 
		
		
		
		func = function(a, b)
			local c1 = getDiaC(a)
			local c2 = getDiaC(b)
			if c1 == c2 then
				return sgs.getDefense(a) < sgs.getDefense(b)	
			else
				return c1 > c2
			end
		end		
			
		table.sort(self.enemies, getDiaC)
		
		for _, enemy in ipairs(self.enemies) do	
			if getDiaC(enemy) > 1 then return enemy end 
		end 

		
		self:sort(self.enemies, "handcard2")
		for _, enemy in ipairs(self.enemies) do	
			if enemy:getHandcardNum() > 1 then return enemy end 
		end 

		self:sort(self.enemies, "defense")	
		return self.enemies[1]		
	else
		local players = sgs.QList2Table(self.room:getOtherPlayers(self.player))
		self:sort(players, "handcard2")
		for _, enemy in ipairs(players) do
			return enemy
		end 
	end 
end 

sgs.ai_playerchosen_intention["luazhulou3"] = function(self, from, to)
	sgs.updateIntention(from, to, 30)
end 

sgs.ai_playerchosen_intention["luazhulou2"] = function(self, from, to)
	if not self:needKongcheng(to) then sgs.updateIntention(from, to, -10) end 
end 

sgs.ai_skill_invoke.luaxinyang = function(self, data)
	local kanako = data:toPlayer()
	if not kanako then return false end 

	local wudi = true 
	for _, enemy in ipairs(self.enemies) do
		if self:damageIsEffective(self.player, sgs.DamageStruct_Normal, enemy) then wudi = false; break end 
	end 
	
	if self:isFriend(kanako) then 
		if self.player:objectName() == kanako:objectName() then return true end
		if self.player:getHandcardNum() >= 2 and self:getOverflow(kanako) > -2 then
			for _, acard in sgs.qlist(self.player:getHandcards()) do
				if acard:isKindOf("Peach") then return false end
			end
			for _, acard in sgs.qlist(self.player:getHandcards()) do
				for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
					local callback = sgs.ai_cardneed[skill:objectName()]
					if type(callback) == "function" and callback(self.player, acard, self) then
						return false
					end
				end
			end
		end
		if self.player:getMaxHp() == 1 and self.player:getHp() == 1 and not self:isWeak() then 
			if self.player:getHandcardNum() == 1 then return true end 
			return false 
		end 
		if (self.player:getMark("luaxinyang") > 0) or wudi then 
			if self:needKongcheng() and self.player:getHandcardNum() <= 2 and self:getOverflow(kanako) < 2 then return true end 
			if self.player:getHandcardNum() == 1 and self:getOverflow(kanako) == 0 then return true end 
		else
			if #self.friends == 2 then 
				if self.player:getHandcardNum() < 3 and self.player:getHp() < 2 and self:getOverflow(kanako) < 1 then return true end
			else
				if self:isWeak() and (self.player:getHandcardNum() <= 3 and not (self.player:hasSkill("luaqiji") and self.player:getLostHp() > 1)) then return true end
				if self.player:hasSkills(sgs.cardneed_skill) then 
					if self.player:getHandcardNum() == 1 and self.player:getHp() < 3 and self:getOverflow(kanako) < 2 then return true end 
					return false
				end 
				if self:getOverflow(kanako) < 0 then 
					if self.player:getHandcardNum() <= 2 and self.player:getHp() <= 3 then return true end 
				else
					if self.player:getHandcardNum() <= 1 then return true end 
				end 
			end 
		end 
	else
		if wudi or self.player:hasSkills(sgs.cardneed_skill) then return false end 
		if self:needKongcheng() and self.player:getHandcardNum() == 1 and self:getOverflow(kanako) > 0 then return true end
		if self.player:getHandcardNum() < 3 then
			for _, card in sgs.qlist(self.player:getHandcards()) do
				if card:isKindOf("Hui") then
					local bool_5 = true
					for _, card2 in sgs.qlist(self.player:getHandcards()) do
						if card2:isKindOf("ExNihilo") or card2:isKindOf("RenwangShield") or card2:isKindOf("Crossbow") or card2:isKindOf("Indulgence")
								or card2:isKindOf("EightDiagram") or card2:isKindOf("Snatch") then
							bool_5 = false
						end
					end
					if bool_5 then return true end
				end
			end
		end
		if self:isWeak() and self.player:getHandcardNum() == 1 and (self:getKeepValue(self.player:getHandcards():at(0)) < 3.31)
			and self:getOverflow(kanako) > 0 then return true end 
	end 

end 

sgs.ai_choicemade_filter.skillInvoke.luaxinyang = function(self, player, promptlist)
	local kanako = self.room:findPlayerBySkillName("luaxinyang")
	if promptlist[3] == "yes" then
		local x = self.player:getHandcardNum()
		if x > 1 then 
			sgs.updateIntention(player, kanako, -30)
		else
			if self:getOverflow(kanako) <= 0 then sgs.updateIntention(player, kanako, -30) end 
		end 
	end 
end 
sgs.ai_skill_invoke.luashenji = function(self, data)
	local kanako = self.room:findPlayerBySkillName("luashenji")
	if not kanako then self.room:writeToConsole("kanako bug") end
	sgs.updateIntention(from, kanako, -30)

end 

function sgs.ai_cardsview_valuable.luashende(self, class_name, player)
	if player:getPhase() == sgs.Player_Play then return end
	if player:getHandcardNum() <= player:getMaxCards() then return end

	local no_have = true
	local cardsK = self.player:getCards("h")
	cardsK = sgs.QList2Table(cardsK)	--yun
	self:sortByKeepValue(cardsK)
	for _,c in ipairs(cardsK) do	--yun
		if c:isKindOf(class_name) then
			no_have = false
			break
		end
	end
	if not no_have then return end
	for _, acard in sgs.qlist(self.player:getHandcards()) do
		if acard:isKindOf(class_name) and not self.player:isCardLimited(acard, sgs.Card_MethodDiscard) then return end 
	end 
	if self:getOverflow() > 2 then
		local dying = self.room:getCurrentDyingPlayer()
		if dying and dying:objectName() == self.player:objectName() then return "#luashende:.:" .. "analeptic"	end
		if dying and self:isFriend(dying) and self.player:containsTrick("gainb") then return "#luashende:.:" .. "analeptic"	end
		return
	end 
	if class_name == "Jink" and self.player:getHp() == 1 then 
		return "#luashende:.:" .. "jink"
	end 
	if class_name == "Slash" and sgs.Sanguosha:getCurrentCardUseReason() == sgs.CardUseStruct_CARD_USE_REASON_RESPONSE_USE then
		return "#luashende:.:" .. "slash"
	elseif (class_name == "Peach" and player:getMark("Global_PreventPeach") == 0) or class_name == "Analeptic" then
		local dying = self.room:getCurrentDyingPlayer()
		if dying and self:isFriend(dying) then
			local user_string = "peach+analeptic"
			if player:getMark("Global_PreventPeach") > 0 then 
				user_string = "analeptic" 
			else
				user_string = "peach" 
			end
			self.room:writeToConsole("kanako test4" .. player:getGeneralName())
			return "#luashende:.:" .. user_string				
		end
	end
end

sgs.ai_skill_invoke.luashende = function(self, data)
	local pattern = data:toString()
	pattern = pattern:gsub("^%l", string.upper)
	local no_have = true
	local cardsK = self.player:getCards("h")
	cardsK = sgs.QList2Table(cardsK)	--yun
	self:sortByKeepValue(cardsK)
	for _,c in ipairs(cardsK) do	--yun
		if c:isKindOf(pattern) then
			no_have = false
			break
		end
	end
	if not no_have then return end

	if self.player:getPhase() == sgs.Player_Play then return false end
	if self.player:getHp() == 1 then return true end 
	if self:getOverflow() == 1 then return true end 
end 


sgs.ai_skill_invoke.luashenji = function(self, data)
	local kanako = data:toPlayer()
	if self:isFriend(kanako) then return true end 
	return false 
end 

sgs.ai_choicemade_filter.skillInvoke.luashenji = function(self, player, promptlist)
	local kanako = self.room:findPlayerBySkillName("luaxinyang")
	if promptlist[3] == "yes" then 
		sgs.updateIntention(player, kanako, -30)
	end 
end

sgs.ai_skill_playerchosen.luahongyu = function(self, targets)
	local friends = self.friends
	self:sort(friends, "handcard")

	local cards = self:getTurnUse(true)
	local rewq = self.player:getHandcardNum() - #cards - 1
	local q = 0
	local fin_cards = sgs.QList2Table(self.player:getHandcards())
	for _, card in ipairs(fin_cards) do
		if not card:isKindOf("BasicCard") then
			local abc = false
			for _, cardX in ipairs(cards) do
				if card:getEffectiveId() == cardX:getEffectiveId() then abc = true end
			end
			if not abc then q = q + 1 end
		end
	end
	for _, friend in ipairs(friends) do
		if targets:contains(friend) and not friend:containsTrick("gaina") then
			local p = friend:getMaxCards() - friend:getHandcardNum()
			if q > 1 and p < 3 and friend:objectName() ~= self.player:objectName() and rewq > 1 then continue end
			if p > 1 then
				return friend
			end
		end
	end
	for _, friend in ipairs(friends) do
		if targets:contains(friend) then
			local p = friend:getMaxCards() - friend:getHandcardNum()
			if p == 1 and friend:containsTrick("gaina") and q <= 1 and rewq <= 1 then
				return friend
			end
		end
	end
end

sgs.ai_playerchosen_intention.luahongyu = function(self, from, to)
	sgs.updateIntention(from, to , -40)
end

sgs.ai_skill_playerchosen.luafengshou = function(self, targets)
	local cardlist = {}
	local luafengshou_list = self.room:getTag("lfengshou"):toString()
	luafengshou_list = luafengshou_list:split("|")
	for _, id in ipairs(luafengshou_list) do
		local card = sgs.Sanguosha:getCard(id)
		table.insert(cardlist, card)
	end
	local card, friendo = self:getCardNeedPlayer(cardlist, false, 3)
	if card and friendo then
		if targets:contains(friendo) then
			return friendo
		end
	end

	if targets:contains(self.player) then
		return self.player
	end 	
	self:sort(self.friends_noself, "defense")
	for _, friend in ipairs(self.friends_noself) do
		if targets:contains(friend) then
			return friend
		end 
	end 
	
	local players = sgs.QList2Table(self.room:getAlivePlayers())
	
	self:sort(players, "defense2")
	for _, friend in ipairs(players) do
		if targets:contains(friend) and not self:isEnemy(friend) then
			return friend
		end 	
	end 
	for _, friend in ipairs(players) do
		if targets:contains(friend) then
			return friend
		end 	
	end 	
end

sgs.ai_playerchosen_intention.luafengshou = function(self, from, to)
	if from:hasFlag("fengshouA") then
		sgs.updateIntention(from, to , -10)

	end
end

sgs.ai_skill_askforag.luafengshou = function(self, card_ids)
	local cardlist = {}
	local luafengshou_list = self.room:getTag("lfengshou"):toString()
	luafengshou_list = luafengshou_list:split("|")
	for _, id in ipairs(luafengshou_list) do
		local card = sgs.Sanguosha:getCard(id)
		table.insert(cardlist, card)
	end

	local target
	for _, ap in sgs.qlist(self.room:getAlivePlayers()) do
		if ap:hasFlag("fengshouT") then target = ap;break end
	end
	if self:isFriend(target) then
		local card, friendo = self:getCardNeedPlayer(cardlist, false, 3)
		if card and friendo then
			for _, card_id in ipairs(card_ids) do
				if card and card:getId() == card_id then return card_id end
			end
		end
		self:sortByKeepValue(cardlist, true)
		for _, card_id in ipairs(card_ids) do
			for _, cardS in ipairs(cardlist) do
				if cardS:getId() == card_id then return card_id end
			end
		end
	else
		self:sortByKeepValue(cardlist, true)
		for _, card_id in ipairs(card_ids) do
			for _, card in ipairs(cardlist) do
				if card:getId() == card_id then return card_id end
			end
		end
	end
end

sgs.ai_skill_playerchosen.luazhongyan = function(self, targets)
	self:sort(self.enemies, "defense")
	for _, enemy in ipairs(self.enemies) do
		if targets:contains(enemy) then return enemy end
	end
end

sgs.ai_use_priority.luanianli = function(self)
	local dismantlement = sgs.Sanguosha:cloneCard("dismantlement")
	local x = self:getUseValue(dismantlement)
	return x
end

local luanianli_skill = {}
luanianli_skill.name = "luanianli"
table.insert(sgs.ai_skills, luanianli_skill)
luanianli_skill.getTurnUseCard = function(self)
	if self.player:getPile("luanianli"):isEmpty() then return end
	local table_X = self:getTurnUse(true)
	local has_Slash = false
	for _, card in ipairs(table_X) do
		if card:isKindOf("Slash") then
			has_Slash = true
		end
	end
	if self.player:getPile("luanianli"):isEmpty() == 1 or (not has_Slash) then
		if #table_X <= 1 then return end

		local table_0 = {}
		local dismantlement = sgs.Sanguosha:cloneCard("dismantlement")
		dismantlement:setSkillName("luanianli")
		local slash = sgs.Sanguosha:cloneCard("slash")
		slash:setSkillName("luanianli")
		table.insert(table_0, slash)
		local death = self:canKillEnermyAtOnce(table_0, slash)

		local dummyuse = { isDummy = true, to = sgs.SPlayerList() }
		self:useTrickCard(dismantlement, dummyuse)
		if dummyuse.card and not death then
			for i = 0, self.player:getPile("luanianli"):length() - 1, 1 do
				local snatch = sgs.Sanguosha:getCard(self.player:getPile("luanianli"):at(i))
				local snatch_str = ("dismantlement:luanianli[%s:%s]=%d"):format(snatch:getSuitString(), snatch:getNumberString(), self.player:getPile("luanianli"):at(i))
				local jixisnatch = sgs.Card_Parse(snatch_str)
				for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
					if not self.room:isProhibited(self.player, player, jixisnatch) and self:hasTrickEffective(jixisnatch, player) then
						local suit = snatch:getSuitString()
						local number = snatch:getNumberString()
						local card_id = snatch:getEffectiveId()
						local card_str = ("dismantlement:luanianli[%s:%s]=%d"):format(suit, number, card_id)
						local snatch2 = sgs.Card_Parse(card_str)
						assert(snatch2)
						return snatch2
					end
				end
			end
		else
			for i = 0, self.player:getPile("luanianli"):length() - 1, 1 do
				local snatch = sgs.Sanguosha:getCard(self.player:getPile("luanianli"):at(i))
				local snatch_str = ("slash:luanianli[%s:%s]=%d"):format(snatch:getSuitString(), snatch:getNumberString(), self.player:getPile("luanianli"):at(i))
				local jixisnatch = sgs.Card_Parse(snatch_str)
				for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
					if self.player:canSlash(player, jixisnatch) and not self:slashProhibit(jixisnatch, player)
							and self:slashIsEffective(jixisnatch, player)then
						local suit = snatch:getSuitString()
						local number = snatch:getNumberString()
						local card_id = snatch:getEffectiveId()
						local card_str = ("slash:luanianli[%s:%s]=%d"):format(suit, number, card_id)
						local snatch2 = sgs.Card_Parse(card_str)
						assert(snatch2)
						return snatch2
					end
				end
			end
		end
	else
		for i = 0, self.player:getPile("luanianli"):length() - 1, 1 do
			local snatch = sgs.Sanguosha:getCard(self.player:getPile("luanianli"):at(i))
			local snatch_str = ("dismantlement:luanianli[%s:%s]=%d"):format(snatch:getSuitString(), snatch:getNumberString(), self.player:getPile("luanianli"):at(i))
			local jixisnatch = sgs.Card_Parse(snatch_str)
			for _, player in sgs.qlist(self.room:getOtherPlayers(self.player)) do
				if not self.room:isProhibited(self.player, player, jixisnatch) and self:hasTrickEffective(jixisnatch, player) then
					local suit = snatch:getSuitString()
					local number = snatch:getNumberString()
					local card_id = snatch:getEffectiveId()
					local card_str = ("dismantlement:luanianli[%s:%s]=%d"):format(suit, number, card_id)
					local snatch2 = sgs.Card_Parse(card_str)
					assert(snatch2)
					return snatch2
				end
			end
		end
	end
end

sgs.ai_view_as.luanianli = function(card, player, card_place)
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	if card_place == sgs.Player_PlaceSpecial and player:getPileName(card_id) == "luanianli" then
		return ("slash:luanianli[%s:%s]=%d"):format(suit, number, card_id)
	end
end

local luachaogan_skill = {}
luachaogan_skill.name = "luachaogan"
table.insert(sgs.ai_skills, luachaogan_skill)
luachaogan_skill.getTurnUseCard = function(self)
    if self.player:hasUsed("#luachaogan") then return end
    local q = self.player:getMark("luachaogan")
	local table_X = self:getTurnUse(true)
    local p = 0
	for _, card in ipairs(table_X) do
		if not card:isKindOf("EquipCard") and not card:isKindOf("IronChain") and not card:isKindOf("AOE") and not card:isKindOf("AmazingGrace")
			and not card:isKindOf("GodSalvation") then
			p = p + 1
		end
	end

	local has_Slash
	for _, card in ipairs(table_X) do
		if card:isKindOf("Slash") then
			has_Slash = card
		end
	end
	if q ~= 0 and p == 0 then self.room:writeToConsole("堇子测试1");return sgs.Card_Parse("#luachaogan:.:") end
	self.room:writeToConsole("堇子测试2")
	if q == 0 then
		return
	elseif q == 1 then
		if p > 0 then self.room:writeToConsole("堇子测试3");return end
	elseif q == 2 or q == 3 then
		self.room:writeToConsole("堇子测试4")
		local next_player = self.player:getNextAlive()
		if p > 0 and self:isFriend(next_player) and not next_player:containsTrick("YanxiaoCard") then
			local judge = next_player:getCards("j")
			judge = sgs.QList2Table(judge)
			judge = sgs.reverse(judge)
			for judge_count, need_judge in ipairs(judge) do
				if need_judge:isKindOf("Indulgence") or need_judge:isKindOf("SupplyShortage") then return end
			end
		end
		if p > 0 and has_Slash then
			local dummy_use = { isDummy = true , to = sgs.SPlayerList() }
			self:useBasicCard(has_Slash, dummy_use)
			if dummy_use.card and dummy_use.to and (dummy_use.to:length() > 0) then
				for _, p in sgs.qlist(dummy_use.to) do
					if self:hasEightDiagramEffect(p) and not IgnoreArmor(self.player, p) then
						return
					end
				end
			end
		end
	end

	return sgs.Card_Parse("#luachaogan:.:")
end


sgs.ai_skill_use_func["#luachaogan"] = function(cardR, use, self)
	local table_X = self:getTurnUse(true)
	local has_Slash
	for _, card in ipairs(table_X) do
		if card:isKindOf("Slash") then
			has_Slash = card
		end
	end
	if has_Slash then
		local dummy_use = { isDummy = true , to = sgs.SPlayerList() }
		self:useBasicCard(has_Slash, dummy_use)
		if dummy_use.card and dummy_use.to and (dummy_use.to:length() > 0) then
			for _, p in sgs.qlist(dummy_use.to) do
				if self:hasEightDiagramEffect(p) and not IgnoreArmor(self.player, p) then
					self.room:setPlayerFlag(self.player, "luxun")
				end
			end
		end
	end

	use.card = sgs.Card_Parse("#luachaogan:.:")
	if use.to then use.to = sgs.SPlayerList() end
	return

end

sgs.ai_use_priority.luachaogan = 3.7

sgs.ai_skill_askforag.luachaogan = function(self, card_ids)
	local next_player = self.player:getNextAlive()
	local ids = card_ids
	local ind = false
	local sup = false
	local bagua = false
	local cards = {}
	for _, id in ipairs(ids) do
		if next_player:containsTrick("Indulgence") and not next_player:containsTrick("YanxiaoCard") and self:isFriend(next_player) and not ind
			and sgs.Sanguosha:getCard(id):getSuit() == sgs.Card_Heart then ind = true;continue end
		if next_player:containsTrick("SupplyShortage") and not next_player:containsTrick("YanxiaoCard") and self:isFriend(next_player) and not sup
			and sgs.Sanguosha:getCard(id):getSuit() == sgs.Card_Club  then sup = true;continue end
		if self.player:hasFlag("luxun") and not bagua and sgs.Sanguosha:getCard(id):isBlack() then bagua = true;continue end
		table.insert(cards, sgs.Sanguosha:getCard(id))
	end
	self:sortByUseValue(cards, true)
	return cards[1]:getEffectiveId()
end
sgs.ai_skill_turnUse["luachaogan"] = function(self, turnUse)
	if self.player:hasSkill("luachaogan") and self:getOverflow() < 1 and self.player:getMark("luachaogan") == 0 then
		local x = 0
		for _, card in ipairs(turnUse) do
			if not card:isKindOf("SkillCard") then x = x + 1 end
		end
		if x <= 1 then
			if not self:canKillEnermyAtOnce(turnUse) then
				return {}
			end
		end
	end
	return turnUse
end

sgs.pay_ai_card.Peach.luahongtu = function(self, card, use, mustusepeach)
	if self.player:hasSkill("luahongtu") and not mustusepeach then
		if self.player:getMark("@luahongtu") < self.player:getHp() - 1 then
			if self.player:getPhase() == sgs.Player_Play and (self:getOverflow() <= 0) then return 2 end
		end
	end
end 
sgs.ai_skill_playerchosen.luahongtu = function(self, targets)
	self.room:writeToConsole("hongtu test")
	return sgs.ai_skill_playerchosen.luajianjic(self, targets)
end
sgs.ai_skill_turnUse["luahongtu"] = function(self, turnUse)
	local turnUse2 = {}
	local V = 0
	for _, cardD in sgs.qlist(self.player:getHandcards()) do
		if cardD:isKindOf("EquipCard") and not self.player:isCardLimited(cardD, sgs.Card_MethodUse) and not table.contains(turnUse, cardD) then
			V = V + 1
		end
	end
	local pj = #turnUse + V
	local hp = self.player:getHp()
	local max = self.player:getMaxCards()
	local function CCheck(card_9, name)
		local dummy_use = { isDummy = true, extra_target = 1, to = sgs.SPlayerList() }
		if card_9:isKindOf("Slash") then
			self:useCardSlash(card_9, dummy_use)
		else
			self:useTrickCard(card_9, dummy_use)
		end
		if dummy_use.card and dummy_use.card:isKindOf(name) and dummy_use.to and (dummy_use.to:length() > 1) then
			return true
		end
		return false
	end
	if self.player:getMark("@luahongtu") == 0 then
		if pj > self.player:getHp() then self.room:setPlayerFlag(self.player, "UseAll"); self.room:writeToConsole("现在的策略是尽可能地多用牌") end
		if self.player:getMark("@luahongtu") == hp - 1 then
			for _,c in ipairs(turnUse) do
				if (c:isKindOf("Duel") and CCheck(c, "Duel")) or (c:isKindOf("ExNihilo") and #self.friends_noself > 0) or (c:isKindOf("Snatch") and CCheck(c, "Snatch")) then
					table.insert(turnUse2, c)
					break
				end
			end
		end
		if #turnUse2 == 0 then
			if self.player:getMark("@luahongtu") == hp - 1 then
				for _,c in ipairs(turnUse) do
					if (c:isKindOf("Peach") and self.player:isWounded() and #self.friends_noself > 0) then
						table.insert(turnUse2, c)
						break
					end
				end
			end
		end
		if #turnUse2 == 0 then
			for _,c in ipairs(turnUse) do
				if c:isKindOf("Slash") then
					local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
					self:useCardSlash(c, dummy_use)
					if dummy_use.card and dummy_use.card:isKindOf("Slash") and dummy_use.to and (dummy_use.to:length() > 0)
							and not self:YouMu2(dummy_use.to:at(0), true) then
						table.insert(turnUse2, c)
						break
					end
				end
			end
		end
		if #turnUse2 == 0 and pj < hp - 2 then
			for _,c in ipairs(turnUse) do
				if c:isKindOf("Duel") or c:isKindOf("ExNihilo") or c:isKindOf("Snatch") then
					table.insert(turnUse2, c)
					break
				end
			end
		end
		if #turnUse2 == 0 then
			if self.player:getMark("@luahongtu") == hp - 1 then
				for _,c in ipairs(turnUse) do
					if c:isKindOf("Dismantlement") then
						table.insert(turnUse2, c)
						break
					end
				end
			end
		end
		if #turnUse2 == 0 then
			for _,c in ipairs(turnUse) do
				if c:isKindOf("Slash") then
					table.insert(turnUse2, c)
					break
				end
			end
		end
		if #turnUse2 == 0 then
			if self.player:getMark("@luahongtu") == hp - 1 then
				for _,c in ipairs(turnUse) do
					if c:isKindOf("FireAttack") or c:isKindOf("quanxiang") then
						table.insert(turnUse2, c)
						break
					end
				end
			end
		end
		for _,c in ipairs(turnUse) do
			if not table.contains(turnUse2, c) then
				table.insert(turnUse2, c)
			end
		end
		return turnUse2
	end
	if self.player:getMark("@luahongtu") == 1 then
		if pj < hp - 1 and self:getOverflow() <= 0 then
			return {}
		end
	end
	if self.player:getMark("@luahongtu") == 2 then
		if not self:canKillEnermyAtOnce(turnUse, nil, false, 1) then
			if pj < hp + 1 - 2 and self:getOverflow() <= 0 then
				return {}
			end
		end
	end
	if self.player:getMark("@luahongtu") == hp - 1 then
		for _,c in ipairs(turnUse) do
			if (c:isKindOf("Duel") and CCheck(c, "Duel")) or (c:isKindOf("ExNihilo") and #self.friends_noself > 0) or (c:isKindOf("Snatch") and CCheck(c, "Snatch")) then
				table.insert(turnUse2, c)
				break
			end
		end
		if #turnUse2 == 0 then
			for _,c in ipairs(turnUse) do
				if (c:isKindOf("Peach") and self.player:isWounded() and #self.friends_noself > 0) then
					table.insert(turnUse2, c)
					break
				end
			end
		end
		if #turnUse2 == 0 then
			for _,c in ipairs(turnUse) do
				if (c:isKindOf("Slash") and CCheck(c, "Slash")) or (c:isKindOf("Dismantlement") and CCheck(c, "Dismantlement")) then
					table.insert(turnUse2, c)
					break
				end
			end
		end
		if #turnUse2 == 0 then
			for _,c in ipairs(turnUse) do
				if c:isKindOf("FireAttack") or c:isKindOf("quanxiang") then
					table.insert(turnUse2, c)
					break
				end
			end
		end
		for _,c in ipairs(turnUse) do
			if not table.contains(turnUse2, c) then
				table.insert(turnUse2, c)
			end
		end
		return turnUse2
	end
	return turnUse
end

local function ZhishuPanding(self, card, hei_hua, close_p) --return false表示这张牌还是用了比较好
	local non_val = true
	for _, id in sgs.qlist(self.player:getPile("Shu")) do
		if sgs.Sanguosha:getCard(id):isKindOf("ExNihilo") then non_val = false end
	end
	if card:isKindOf("ExNihilo") and close_p and self:isFriend(close_p) and non_val and not self.player:hasFlag("Wuzhong") then
		self.room:setPlayerFlag(self.player, "Wuzhong")
		return true
	end
	if card:isKindOf("Dismantlement") and not (hei_hua) then
		card:setSkillName("luazhishu")
		local dummy_use = { isDummy = true , to = sgs.SPlayerList()}
		self:useTrickCard(card, dummy_use)
		if (dummy_use.card) and (dummy_use.to:length() > 0) then
			return false
		end
	end
	if card:isKindOf("Snatch") then
		if close_p and self:isEnemy(close_p) and not (hei_hua) then return false end
		if not self.player:hasSkill("luahanling") then return false end
		card:setSkillName("luazhishu")
		local dummy_use = { isDummy = true , to = sgs.SPlayerList()}
		self:useTrickCard(card, dummy_use)
		if (dummy_use.card) and (dummy_use.to:length() > 0) then
			return false
		end
	end
	if card:isKindOf("Duel") then
		if close_p and self:isEnemy(close_p) and not (hei_hua) then return false end
		if not self.player:hasSkill("luahanling") then return false end
		for _, enemy in ipairs(self.enemies) do
			if self:isWeak(enemy) then return false end
		end
	end
	if card:isKindOf("Indulgence") then
		if not self.player:hasSkill("luahanling") then return false end
		local v = 0
		for _, friend in ipairs(self.friends) do
			v = self:getIndulgenceValue(friend)
			if v > 3 then return false end
		end
	end
	if card:isKindOf("SupplyShortage") or card:isKindOf("quanxiang") then
		if not self.player:hasSkill("luahanling") then return false end
	end
	if card:isKindOf("quanxiang") then
		if self:getMaxCard() and 10 <= self:getMaxCard():getNumber() then return false end
	end
	local bool_3 = (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu"))
			and ((not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
	if bool_3 then
		return false
	end

	if card:isKindOf("EquipCard") and close_p and self:isEnemy(close_p) and close_p:hasSkill("luahongcai") then return false end
	if card:isKindOf("Weapon") then
		if not self.player:getWeapon() then return false end
		if self.player:getWeapon():isKindOf("YitianSword") then return false end
		local weaponrange = self.player:getWeapon():getRealCard():toWeapon():getRange()
		if weaponrange > 2 then return true end
	end
	if card:isKindOf("AOE") then
		if self:getAoeValue(card) > 40 then return false end
		if self:getAoeValue(card) > 20 and not self.player:hasSkill("luahanling") then return false end
	end
	if card:isKindOf("GodSalvation") then
		if self:godSalvationValue(card) > 10 then return false end
		if self:godSalvationValue(card) > 5 and not self.player:hasSkill("luahanling") then return false end
	end
	return true
end
sgs.ai_skill_turnUse["luazhishu"] = function(self, turnUse)
	local discard_ids = self.room:getDiscardPile()
	local trickcard = sgs.IntList()
	for _, id in sgs.qlist(discard_ids) do
		local card = sgs.Sanguosha:getCard(id)
		if card:isKindOf("ExNihilo") or card:isKindOf("Peach") then
			trickcard:append(id)
		end
	end

	local close_f = self.player:getNextAlive()
	local close_p
	while true do
		if close_f:faceUp() and close_f:objectName() ~= self.player:objectName() and not self:willSkipPlayPhase(close_f) then
			if not close_p then close_p = close_f end
			if self:isFriend(close_f) then break end
		end
		if close_f:objectName() == self.player:objectName() then close_f = nil; break end
		close_f = close_f:getNextAlive()
	end
	local ph
	if close_f then
		ph = self:getEnemyNumBySeat(self.player, close_f, self.player)
	else
		ph = #self.enemies
	end

	local hei_hua = self.player:getPile("Shu") and self.player:getPile("Shu"):length() == 3 and self.player:hasSkill("luayaojuan")
	if self.player:hasSkill("luahanling") and discard_ids:length() > 0 then
		--好tm几把复杂，不写了
		local turnUse_p = {}
		local NturnUse_p = {}
		for _, card in ipairs(turnUse) do
			if card:isKindOf("SkillCard") or card:isKindOf("BasicCard") or card:isKindOf("FireAttack") or (self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceHand) then
				table.insert(turnUse_p, card)
			else
				if not ZhishuPanding(self, card, hei_hua, close_p) then table.insert(turnUse_p, card);else table.insert(NturnUse_p, card) end
			end
		end
		local y = self.player:getHandcardNum() - #turnUse_p - self.player:getMaxCards()
		self:sortByUseValue(NturnUse_p)
		if y <= 1 then return turnUse_p end
		if y == 2 and #NturnUse_p > 0 then table.insert(turnUse_p, NturnUse_p[1]); return turnUse_p end
		if y == 5 and #NturnUse_p > 0 then table.insert(turnUse_p, NturnUse_p[1]); return turnUse_p end
		return turnUse_p
	elseif self.player:hasSkill("luayaojuan") then
		if self.player:getPile("Shu") and self.player:getPile("Shu"):length() > 3 then return turnUse end
		local turnUse_p = {}
		local NturnUse_p = {}
		for _, card in ipairs(turnUse) do
			if card:isKindOf("SkillCard") or card:isKindOf("BasicCard") or card:isKindOf("ExNihilo") or card:isKindOf("FireAttack") or (self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceHand) then
				table.insert(turnUse_p, card)
			else
				if not ZhishuPanding(self, card, hei_hua, close_p) then table.insert(turnUse_p, card);else table.insert(NturnUse_p, card) end
			end
		end
		local y = self.player:getHandcardNum() - #turnUse_p - self.player:getMaxCards()
		local q = 4 - self.player:getPile("Shu"):length()
		if y <= q then return turnUse_p end
		local f = #NturnUse_p
		for _, card in ipairs(NturnUse_p) do
			table.insert(turnUse_p, card)
			y = y - 1
			if y <= q then break end
		end
		return turnUse_p
	end
end

local function findHanlingCard(self)
	local kosuzu = self.room:findPlayerBySkillName("luahanling")
	local qizhi = kosuzu:getPile("Shu")
	local haopai
	for _, id in sgs.qlist(qizhi) do
		local c = sgs.Sanguosha:getCard(id)
		if c:isKindOf("Wanbaochui") then haopai = c end
		if c:isKindOf("ExNihilo") or c:isKindOf("Duel") then haopai = c end
		if c:isKindOf("AOE") and (c:isKindOf("AOE") and self:getAoeValue(c) > 55) then haopai = c end
		if c:isKindOf("GodSalvation") and self:godSalvationValue(c) > 10 then haopai = c end
		if c:isKindOf("Snatch") then
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			self:useTrickCard(c, dummy_use)
			if (dummy_use.card) and (dummy_use.to:length() > 0) then
				haopai = c
			end
		end
		if self:cardNeed(c) >= 8 then haopai = c end
	end

	if not self:isFriend(kosuzu) then
		local cards = sgs.QList2Table(self.player:getHandcards())
		self:sortByUseValue(cards, true)
		if kosuzu:getPile("Shu") and (kosuzu:getPile("Shu"):length() > 3 and kosuzu:hasSkill("luayaojuan")
				and kosuzu:getPile("Shu"):length() < 7) or haopai then
			local toCard = cards[1]
			for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skill:objectName()]
				if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](self.player, toCard, self) then
					return
				end
			end
			if toCard:isKindOf("Snatch") then
				local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
				self:useTrickCard(toCard, dummy_use)
				if dummy_use.card and (dummy_use.to:length() >= 1) then
					return false
				end
			end
			if not toCard:isKindOf("Peach") and not toCard:isKindOf("ExNihilo")
				and not toCard:isKindOf("YitianSword") and not toCard:isKindOf("Nullification") then
				return toCard
			end
		else
			local toCard = cards[1]
			for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skill:objectName()]
				if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](self.player, toCard, self) then
					return
				end
			end
			if not toCard:isKindOf("Peach") and not toCard:isKindOf("ExNihilo") and not toCard:isKindOf("Snatch") and not (toCard:isKindOf("EightDiagram") or toCard:isKindOf("RenwangShield"))
					and not toCard:isKindOf("YitianSword") and not toCard:isKindOf("SupplyShortage") and not toCard:isKindOf("quanxiang")
					and not (toCard:isKindOf("Jink") and self:getCardsNum("Jink") <= 1) and not toCard:isKindOf("Nullification") then
				return toCard
			end
		end
	else
		local haopai2
		for _, id in sgs.qlist(qizhi) do
			local c = sgs.Sanguosha:getCard(id)
			if c:isKindOf("ExNihilo") then haopai2 = c end
			if c:isKindOf("Wanbaochui") then haopai2 = c end
			if c:isKindOf("AOE") and (c:isKindOf("AOE") and self:getAoeValue(c) > 35) then haopai2 = c end
			if c:isKindOf("GodSalvation") and self:godSalvationValue(c) > 8 then haopai2 = c end
			if c:isKindOf("Snatch") or c:isKindOf("Dismantlement") or c:isKindOf("FireAttack") or c:isKindOf("quanxiang") then
				local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
				self:useTrickCard(c, dummy_use)
				if (dummy_use.card) and (dummy_use.to:length() > 0) then
					haopai2 = c
				end
			end
			if self:cardNeed(c) >= 6 then haopai2 = c end
		end
		if haopai2 then
			local cards = sgs.QList2Table(self.player:getHandcards())
			self:sortByKeepValue(cards)
			local toCard = cards[1]
			if not (toCard:isKindOf("Jink") and self.player:getHp() < 3) and not (toCard:isKindOf("Peach") and not kosuzu:isWounded() and self:getOverflow(kosuzu) > 1)
				and not (toCard:isKindOf("Analeptic") and self:isWeak() and self.player:getHp() == 1) then
				return cards[1]
			end
		end
	end
end
local luahanling2_skill = {}
luahanling2_skill.name = "luahanling2"
table.insert(sgs.ai_skills, luahanling2_skill)
luahanling2_skill.getTurnUseCard = function(self)
	if self.player:isKongcheng() then return end
	if self.player:hasUsed("#luahanling") then return end
	local kosuzu = self.room:findPlayerBySkillName("luahanling")
	if not kosuzu then return end
	if not kosuzu:isAlive() then return end
	local qizhi = kosuzu:getPile("Shu")
	if (not qizhi) or (qizhi:length() < 1) then return end
	return sgs.Card_Parse("#luahanling:.:")
end

sgs.ai_skill_use_func["#luahanling"] = function(card, use, self)
	local card0 = findHanlingCard(self)
	if not card0 then return end
	use.card = sgs.Card_Parse("#luahanling:".. card0:getId() ..":")
	local kosuzu = self.room:findPlayerBySkillName("luahanling")
	if use.to then
		use.to = sgs.SPlayerList()
		return
	end
	return
end
sgs.ai_use_priority.luahanling = 5
sgs.ai_skill_askforag.luahanling = function(self, card_ids)
	local cards = {}
	for _, card_id in ipairs(card_ids) do
		table.insert(cards, sgs.Sanguosha:getCard(card_id))
	end
	self:sortByCardNeed(cards, true)
	return cards[1]:getId()
end

local luayaojuan_skill = {}
luayaojuan_skill.name = "luayaojuan"
table.insert(sgs.ai_skills, luayaojuan_skill)
luayaojuan_skill.getTurnUseCard = function(self)
	if self.player:getPile("Shu") and self.player:getPile("Shu"):length() < 4 then return end
	return sgs.Card_Parse("#luayaojuan:.:")
end

sgs.ai_skill_use_func["#luayaojuan"] = function(card, use, self)
	if #self.enemies == 0 then return end
	local enemies = self.enemies
	self:sort(enemies, "defense")
	use.card = sgs.Card_Parse("#luayaojuan:.:")
	if use.to then
		use.to:append(enemies[1])
		return
	end
end

sgs.ai_card_intention.luayaojuan = 100
sgs.ai_use_priority.luayaojuan = 6

sgs.ai_skill_choice.luayaojuan = function(self, choices)
	if self.player:hasSkill("luahanling") then return "luahanling" end
	if self.player:hasSkill("luazhishu") then return "luazhishu" end
	return "luayaojuan"
end
sgs.ai_skill_invoke.luazhishu = true

sgs.ai_skill_askforag.luazhishu = function(self, card_ids)
	local cards = {}
	for _, card_id in ipairs(card_ids) do
		table.insert(cards, sgs.Sanguosha:getCard(card_id))
	end
	self:sortByCardNeed(cards)
	for _, card in ipairs(cards) do
		if card:isKindOf("Peach") and not self:OverFlowPeach(card) then return card:getId() end
	end
	for _, card in ipairs(cards) do
		if card:isKindOf("ExNihilo") then return card:getId() end
	end
	return cards[1]:getId()
end

local function sidiecard(self)
	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(cards)

	local fq = 0
	for _, enemy in ipairs(self.enemies) do
		if enemy and enemy:hasWeapon("guding_blade") and not enemy:hasSkill("jueqing") and enemy:inMyAttackRange(self.player) then fq = fq + 1; break end
		if enemy and enemy:hasSkill("luayueshi") then fq = fq + 1; break end
		if enemy and enemy:hasSkill("lualihe") then fq = fq + 1; break end
	end

	local function pandin(card)
		if not (card:isKindOf("Jink") and self.player:getMaxHp() > 2) and not card:isKindOf("ExNihilo") and not card:isKindOf("Indulgence")
				and not (card:isKindOf("Analeptic") and self.player:getMaxHp() > 1 and self.player:getHp() == 1)
				and not card:isKindOf("Peach") then
			return true
		end
		return false
	end
	if self.player:getMaxHp() == 1 then
		if pandin(cards[1]) then
			local cardsS = {}
			table.insert(cardsS, cards[1]:getEffectiveId())
			return cardsS
		end
	end
	if self.player:getMaxHp() == 2 then
		if #cards > 1 and pandin(cards[1]) and pandin(cards[2]) then
			local cardsS = {}
			table.insert(cardsS, cards[1]:getEffectiveId())
			table.insert(cardsS, cards[2]:getEffectiveId())
			return cardsS
		end
	end
	if self.player:getMaxHp() == 3 then
		if #cards > 2 + fq and pandin(cards[1]) and pandin(cards[2]) and pandin(cards[3])
			and not (cards[1]:isKindOf("Jink") and cards[2]:isKindOf("Jink")) and not (cards[3]:isKindOf("Jink") and cards[1]:isKindOf("Jink"))
				and not (cards[3]:isKindOf("Jink") and cards[2]:isKindOf("Jink")) then
			local cardsS = {}
			table.insert(cardsS, cards[1]:getEffectiveId())
			table.insert(cardsS, cards[2]:getEffectiveId())
			table.insert(cardsS, cards[3]:getEffectiveId())
			return cardsS
		end
	end
end
local luasidie_skill = {}
luasidie_skill.name = "luasidie"
table.insert(sgs.ai_skills, luasidie_skill)
luasidie_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luasidie") then return end
	if #self.enemies == 0 then return end
	return sgs.Card_Parse("#luasidie:.:")
end

sgs.ai_skill_use_func["#luasidie"] = function(card, use, self)
	local touse = sidiecard(self)
	if not touse then return end
	self:sort(self.enemies, "hp")
	use.card = sgs.Card_Parse("#luasidie:" .. table.concat(touse, "+") .. ":")
	if use.to then use.to:append(self.enemies[1]) end
	return
end

sgs.ai_card_intention.luasidie = 40

sgs.ai_skill_invoke.luazaowu2 = function(self, data)
	local keiki = self.room:findPlayerBySkillName("luazaowu")
	if keiki and self:isFriend(keiki) then return true end
	return false
end
sgs.ai_skill_use["@@luazaowu"] = function(self, prompt) -- 我有一个绝妙而大胆的想法
	local x = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end
	local targetX
	for _, friend in ipairs(self.friends) do
		if friend:hasFlag("luazaowu") then shuxin = true end
	end
	if not targetX or (not targetX:isAlive()) then return end
	if not self:isFriend(targetX) then end
	if targetX:hasSkill("luasanaey") then end
	local count = 0
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:getSuit() == sgs.Card_Heart then
			count = count + 1
		end
	end
	local function Check_R(card)
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return end
		end
		if card:isKindOf("Peach") or card:isKindOf("Analeptic") then return false end
		if x <= 1 and card:isKindOf("Jink") then return false end
		if card:isKindOf("EquipCard") then
			local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
			if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) and rinnosuke
					and rinnosuke:isAlive() and self:isFriend(rinnosuke) then return false end
		end
		if card:isKindOf("ExNihilo") or card:isKindOf("Duel") then return false end
		if card:isKindOf("Lightning") and self:willUseLightning(card) then return false end
		if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
		if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
		if card:isKindOf("Indulgence") then return false end
		if card:isKindOf("IronChain") and shuxin then return false end
		if (card:isKindOf("AOE") and self:getAoeValue(card) > 50) then return false end
		if self:ThreeCheck(card) then return false end
		local bool_2 = card:isKindOf("Weapon") and ((not self.player:getWeapon()) or (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip))
		local bool_3 = (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu"))
				and ((self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
				or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor())
		if bool_3 or bool_2 then return false end
		return true
	end
	local handcard = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(handcard)
	for _, c in ipairs(handcard) do
		if Check_R(c) then
			for _, c2 in ipairs(handcard) do
				if Check_R(c2) and c2:getEffectiveId() ~= c:getEffectiveId() and c2:getSuit() == c:getSuit()
					and not (c2:getSuit() == sgs.Card_Heart and self.player:hasLordSkill("luashenxing") and count <=2) then
					return "#luazaowu:".. c:getId() .. "+" .. c2:getId() .. ":"
				end
			end
		end
	end
end

sgs.ai_skill_choice.luaouxiang = function(self, choices)
	local keiki = self.room:findPlayerBySkillName("luaouxiang")
	if keiki and self:isFriend(keiki) then return "giveCard" end
	if keiki and not self:isFriend(keiki) and self.player:containsTrick("gaina") then
		for _, card in sgs.qlist(self.player:getCards("he")) do
			if not (card:getSuit() == sgs.Card_Heart and keiki:hasLordSkill("luashenxing"))
					and not card:isKindOf("Peach") and not card:isKindOf("ExNihilo") and not card:isKindOf("Analeptic")
					and not card:isKindOf("Duel") and not card:isKindOf("Indulgence") then
				local coulduse = true
				for _, askill in sgs.qlist(self.player:getVisibleSkillList(true)) do
					local filter = sgs.ai_cardneed[askill:objectName()]
					if filter and type(filter) == "function" and sgs.ai_cardneed[askill:objectName()](self.player, card, self) then coulduse = false end
				end
				if coulduse then return "giveCard" end
			end
		end
	end
	return "discard"
end
sgs.ai_skill_invoke.luaouxiang = function(self, data)
	return true
end
sgs.ai_skill_cardchosen["luaouxiang"] = function(self, who, flags)
	local keiki = self.room:findPlayerBySkillName("luaouxiang")
	if keiki and self:isFriend(keiki) then
		if keiki:hasLordSkill("luashenxing") then
			for _, card in sgs.list(self.player:getHandcards()) do
				if card:getSuit() == sgs.Card_Heart and not card:isKindOf("ExNihilo") then
					for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
						local callback = sgs.ai_cardneed[skill:objectName()]
						if not (type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](self.player, card, self)) then
							return card:getEffectiveId()
						end
					end
				end
			end
		end
		for _, card in sgs.list(self.player:getHandcards()) do
			if card:isKindOf("Peach") or card:isKindOf("Analeptic") then return card:getEffectiveId() end
		end

		if keiki:hasLordSkill("luashenxing") then
			local hasTao = false
			local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), keiki:objectName())
			for _, cc in sgs.qlist(keiki:getHandcards()) do
				if (cc:hasFlag("visible") or cc:hasFlag(flag)) and (cc:isKindOf("Peach") or cc:getSuit() == sgs.Card_Heart) then
					hasTao = true
				end
			end
			if not hasTao then
				for _, card in sgs.list(self.player:getHandcards()) do
					if card:getSuit() == sgs.Card_Heart then
						return card:getEffectiveId()
					end
				end
			end
		end
	else
		if self.player:containsTrick("gaina") then
			local cards = self.player:getCards("he")
			self:sortByKeepValue(cards)
			for _,card in sgs.qlist(cards) do
				if not (card:getSuit() == sgs.Card_Heart and keiki:hasLordSkill("luashenxing"))
						and not card:isKindOf("Peach") and not card:isKindOf("ExNihilo") and not card:isKindOf("Analeptic")
						and not card:isKindOf("Duel") and not card:isKindOf("Indulgence") then
					local coulduse = true
					for _, askill in sgs.qlist(self.player:getVisibleSkillList(true)) do
						local filter = sgs.ai_cardneed[askill:objectName()]
						if filter and type(filter) == "function" and sgs.ai_cardneed[askill:objectName()](self.player, card, self) then coulduse = false end
					end
					if coulduse then return card:getEffectiveId() end
				end
			end
		end
	end
	return self:askForDiscard("luaouxiang", 1, 1, false, true)
end

local luashenxing_skill = {}
luashenxing_skill.name = "luashenxing"
table.insert(sgs.ai_skills, luashenxing_skill)
luashenxing_skill.getTurnUseCard = function(self, inclusive)
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
	self:sortByKeepValue(cards)
	for _,card in ipairs(cards) do
		if card:getSuit() == sgs.Card_Heart then
			local suit = card:getSuitString()
			local number = card:getNumberString()
			local card_id = card:getEffectiveId()
			local card_str = ("peach:luashenxing[%s:%s]=%d"):format(suit, number, card_id)
			local slash = sgs.Card_Parse(card_str)
			assert(slash)
			return slash
		end
	end
end

sgs.ai_view_as.luashenxing = function(card, player, card_place)
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	if not player:hasLordSkill("luashenxing") then return false end
	if card_place ~= sgs.Player_PlaceSpecial and card:getSuit() == sgs.Card_Heart and player:getMark("Global_PreventPeach") == 0 then
		return ("peach:luashenxing[%s:%s]=%d"):format(suit, number, card_id)
	end
end

sgs.ai_damage_effect["luaouxiang"] = function(self, to, nature, from)
	if to:hasSkill("luaouxiang") then
		local count = 0
		for _, enemy in ipairs(self.enemies) do
			count = count + enemy:getHandcardNum()
		end
		if count > 4 and #self.enemies > 1 then return true end
	end
end
local function findGejiCard(self)
	local cards = sgs.QList2Table(self.player:getCards("he"))
	local toUseAll = self:Yumemi(cards, 7)
	local compare_func = function(a, b)
		return #a < #b
	end

	table.sort(toUseAll, compare_func)

	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end

	if #toUseAll < 1 then return end
	for i = 1, #toUseAll do
		if #toUseAll[i] == 1 then
			local friends = self.friends
			self:sort(friends, "hp2")
			for _, friend in ipairs(friends) do
				if friend:getLostHp() > 1 then
					return toUseAll[i]
				end
			end
		elseif #toUseAll[i] == 2 then
			local value = 3
			local qiyin = false
			local Check_R -- 必要，标记用
			local nazrin = self.room:findPlayerBySkillName("luatanbao")
			for _, card in ipairs(toUseAll[i]) do
				local cardP = sgs.Sanguosha:getCard(card:getEffectiveId())
				if cardP:getNumber() > 7 then
					if not qiyin then
						qiyin = true
						value = value + 1
					else
						value = value - 1
					end
				end
				if not self.player:isCardLimited(card, sgs.Card_MethodUse) then
					if cardP:isKindOf("Peach") or cardP:isKindOf("Indulgence") or cardP:isKindOf("Duel") or cardP:isKindOf("ExNihilo") then
						value = value - 2
						if self.player:getMark("@luageji2") > 0 then value = value - 2 end
					elseif cardP:isKindOf("EquipCard") then
						if cardP:isKindOf("EquipCard") then
							local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
							if (self.room:getCardPlace(cardP:getId()) == sgs.Player_PlaceHand) and rinnosuke
									and rinnosuke:isAlive() and self:isFriend(rinnosuke) then value = value - 1.2 end
						end
					elseif nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
						if card:hasFlag("prelingbai") then value = value - 2 end
					elseif cardP:isKindOf("Jink") then
						value = value - 1.2
						if self:isWeak() then value = value - 0.4 end
					elseif cardP:isKindOf("Analeptic") then
						value = value - 1.2
						if self:isWeak() then value = value - 0.8 end
					elseif cardP:isKindOf("Ofuda") or (cardP:isKindOf("IronChain") and shuxin) then
						value = value - 1.6
					elseif not cardP:isKindOf("Dismantlement") and self:ThreeCheck(card) then
						value = value - 2
					elseif cardP:isKindOf("AOE") then
						value = value - 1 - self:getAoeValue(cardP)*0.015
					elseif cardP:isKindOf("RenwangShield") and cardP:isKindOf("EightDiagram") then
						value = value - 1.9
					elseif cardP:isKindOf("Crossbow") and #self:getCards("Slash") > 2 then
						value = value - 2
					elseif cardP:isKindOf("Wanbaochui") then
						value = value - 2
					elseif cardP:isKindOf("Banquet") and #self.friends > 1 then
						value = value - 1.4
					elseif cardP:isKindOf("Hui") then
						local use = { isDummy = true }
						self:useBasicCard(cardP, use)
						if not use.card then value = value - 2 end
					else
						value = value - 1
					end
				end
			end
			local abc = #self:getTurnUse(true)
			if self:getOverflow() > 0 and abc == 0 then value = value + 1.2 end
			if self.player:containsTrick("gainb") then value = value + 6 end
			if value >= 0 then return toUseAll[i] end
		elseif #toUseAll[i] >= 3 then
			local value = 4.5
			local qiyin = false
			local Check_R -- 必要，标记用
			local nazrin = self.room:findPlayerBySkillName("luatanbao")
			for _, card in ipairs(toUseAll[i]) do
				local cardP = sgs.Sanguosha:getCard(card:getEffectiveId())
				if cardP:getNumber() > 7 then
					if not qiyin then
						qiyin = true
						value = value + 1
					end
				end
				if not self.player:isCardLimited(card, sgs.Card_MethodUse) then
					if cardP:isKindOf("Peach") or cardP:isKindOf("Indulgence") or cardP:isKindOf("Duel") then
						if self.player:getMark("@luageji2") > 0 then value = value - 2 end
						value = value - 2
					elseif cardP:isKindOf("EquipCard") then
						if cardP:isKindOf("EquipCard") then
							local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
							if (self.room:getCardPlace(cardP:getId()) == sgs.Player_PlaceHand) and rinnosuke
									and rinnosuke:isAlive() and self:isFriend(rinnosuke) then value = value - 1.2 end
						end
					elseif nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
						if card:hasFlag("prelingbai") then value = value - 2 end
					elseif cardP:isKindOf("Jink") then
						value = value - 1.2
						if self:isWeak() then value = value - 0.4 end
					elseif cardP:isKindOf("Analeptic") then
						value = value - 1.2
						if self:isWeak() then value = value - 0.8 end
					elseif cardP:isKindOf("Ofuda") or (cardP:isKindOf("IronChain") and shuxin) then
						value = value - 1.6
					elseif not cardP:isKindOf("Dismantlement") and self:ThreeCheck(card) then
						value = value - 2
					elseif cardP:isKindOf("AOE") then
						value = value - 1 - self:getAoeValue(cardP)*0.015
					elseif cardP:isKindOf("RenwangShield") and cardP:isKindOf("EightDiagram") then
						value = value - 1.9
					elseif cardP:isKindOf("Crossbow") and #self:getCards("Slash") > 2 then
						value = value - 2
					elseif cardP:isKindOf("Wanbaochui") then
						value = value - 2
					elseif cardP:isKindOf("Banquet") and #self.friends > 1 then
						value = value - 1.2
					elseif cardP:isKindOf("Hui") then
						local use = { isDummy = true }
						self:useBasicCard(cardP, use)
						if not use.card then value = value - 2 end
					else
						value = value - 1
					end
				end
			end
			local abc = #self:getTurnUse(true)
			if self:getOverflow() > 0 and abc == 0 then value = value + 1.4 end
			if self.player:containsTrick("gainb") then value = value + 10 end
			if value >= 0 then return toUseAll[i] end
		end
	end
end
local luageji_skill = {}
luageji_skill.name = "luageji"
table.insert(sgs.ai_skills, luageji_skill)
luageji_skill.getTurnUseCard = function(self, inclusive)
	for _, friend in ipairs(self.friends) do
		if friend:isWounded() then
			return sgs.Card_Parse("#luageji:.:")
		end
	end
end
sgs.ai_skill_use_func["#luageji"] = function(card, use, self)
	local toUse = findGejiCard(self)
	if not toUse or (#toUse == 0) then return end
	local toUse2 = {}
	for _, cardX in ipairs(toUse) do
		table.insert(toUse2, cardX:getEffectiveId())
	end
	use.card = sgs.Card_Parse("#luageji:".. table.concat(toUse2, "+")  ..":")
	if use.to then
		if #toUse == 1 then
			local friends = self.friends
			self:sort(friends, "hp2")
			for _, friend in ipairs(friends) do
				if friend:getLostHp() > 1 then
					use.to:append(friend)
					break
				end
			end
			return
		elseif #toUse == 2 then
			local friends = self.friends
			self:sort(friends, "defense2")
			for _, friend in ipairs(friends) do
				if friend:isWounded() and (friend:getLostHp() > 1 or not friend:hasSkill("luaxiaohua")) then
					use.to:append(friend)
					break
				end
			end
			return
		else
			local friends = self.friends
			self:sort(friends, "defense")
			for _, friend in ipairs(friends) do
				if friend:isWounded() and (friend:getLostHp() > 1 or not friend:hasSkill("luaxiaohua")) then
					use.to:append(friend)
					break
				end
			end
			return
		end
	end
end

sgs.ai_card_intention.luageji = -50
sgs.ai_use_priority.luageji = 6

function sgs.ai_cardneed.luageji(to, card, self)
	return card:getNumber() == 7
end

sgs.ai_skill_playerchosen.luageji = function(self, targets, slashX)
	local target = sgs.ai_skill_playerchosen.luajianjic(self, targets)
	if target then return target end
end


local luatianhu_skill = {}
luatianhu_skill.name = "luatianhu"
table.insert(sgs.ai_skills, luatianhu_skill)
luatianhu_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luatianhu") then return end
	return sgs.Card_Parse("#luatianhu:.:")
end
sgs.ai_skill_use_func["#luatianhu"] = function(X, use, self)
	if self.player:isKongcheng() then
		use.card = sgs.Card_Parse("#luatianhu:.:")
		if use.to then
			use.to:append(self.player)
			return
		end
	end
	for _, friend in ipairs(self.friends_noself) do
		if friend:isKongcheng() then
			use.card = sgs.Card_Parse("#luatianhu:.:")
			if use.to then
				use.to:append(friend)
				return
			end
		end
	end
	self:sort(self.friends, "defense")
	for _, friend in ipairs(self.friends) do
		use.card = sgs.Card_Parse("#luatianhu:.:")
		if use.to then
			use.to:append(friend)
			return
		end
	end
end
sgs.ai_use_priority.luatianhu = 0
sgs.ai_card_intention.luatianhu = -20


local function getBlackCard(self, target, enemy, kill)
	local ex_slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
	local ex_peach = sgs.Sanguosha:cloneCard("peach", sgs.Card_NoSuit, 0)

	local shuxin = false
	for _, friend in ipairs(self.friends) do
		if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
	end

	local function Check_R(card)
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return end
		end
		if card:isKindOf("TrickCard") then
			if card:isKindOf("Nullification") then return false end
			if card:isKindOf("IronChain") and not shuxin then return true end
			if card:isKindOf("Dismantlement") or card:isKindOf("NeedMaribel") then return true end
			if card:isKindOf("Lightning") and not self:willUseLightning(card) then return true end
			if card:isKindOf("AOE") and self:getAoeValue(card) > 35 then return false end
			if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
			if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
			if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
			local dummy_use = {isDummy = true}
			self:useTrickCard(card, dummy_use)
			if not dummy_use.card then return true end
			return false
		end
		if card:isKindOf("BasicCard") then
			if card:isKindOf("Analeptic") then
				if not target:hasSkills("tieji|liegong|kofliegong|xiemu|lieren|badao|luashaojie|Luayuelong|luahakurei|luajingdan|tieji|luahuaifei|Luashenqiang") then
					return false
				end
			end
			return true
		end
		if card:isKindOf("EquipCard") then --or card:isKindOf("DefensiveHorse")
			if card:isKindOf("EquipCard") then
				local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
				if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) and rinnosuke
						and rinnosuke:isAlive() and self:isFriend(rinnosuke) then return false end
			end
			if card:isKindOf("OffensiveHorse") then
				return true
			end
			if card:isKindOf("DefensiveHorse") then
				return true
			end
			if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
				local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
						or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
				if not bool_3 then return true end
			end
			if card:isKindOf("Weapon") then
				local dummy_use = {isDummy = true}
				self:useEquipCard(card, dummy_use)
				if not dummy_use.card then return true end
				if dummy_use.card and self.player:getWeapon() then return true end
			end
		end
	end

	local cards = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(cards)
	for _, Acard in sgs.list(cards) do
		ex_peach:addSubcard(Acard)
		if Acard:isRed() and not target:isCardLimited(ex_peach, sgs.Card_MethodUse) and self:isWeak(target) then
			return Acard
		end
	end

	for _, Acard in sgs.list(cards) do
		ex_slash:addSubcard(Acard)
		if Acard:isBlack() and not target:isCardLimited(ex_slash, sgs.Card_MethodUse) and Check_R(Acard) and (not enemy or self:slashIsEffective(Acard, enemy, target))
				and (kill or target:hasFlag("KillAtOnce") or target:hasSkills("tieji|liegong|kofliegong|xiemu|lieren|badao|luashaojie|Luayuelong|luahakurei|luajingdan|tieji|luahuaifei|Luashenqiang")) then
			return Acard
		end
	end

	for _, Acard in sgs.list(cards) do
		ex_peach:addSubcard(Acard)
		if Acard:isRed() and not target:isCardLimited(ex_peach, sgs.Card_MethodUse) and target:isWounded() then
			return Acard
		end
	end

	for _, Acard in sgs.list(cards) do
		ex_slash:addSubcard(Acard)
		if Acard:isBlack() and not target:isCardLimited(ex_slash, sgs.Card_MethodUse) and Check_R(Acard) and (not enemy or self:slashIsEffective(Acard, enemy, target)) then
			return Acard
		end
	end

end
sgs.ai_skill_playerchosen.luatianyan = function(self, targets, slashX)
	targets = sgs.QList2Table(targets)
	local ex_slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_Spade, 0)
	for _, target in ipairs(targets) do
		if self:isFriend(target) and target:isAlive() then
			for _, enemy in ipairs(self.enemies) do
				local damage_C = self:AtomDamageCount2(enemy, target, false, ex_slash)
				if enemy:getHp() <= damage_C and not enemy:hasArmorEffect("eight_diagram") and target:distanceTo(enemy) <= target:getAttackRange() 	--yun
						and not (enemy:hasSkill("luajinxi") and enemy:getPile("yuanqi"):length() ~= 0)
						and not (enemy:hasSkill("luafenxing") and not enemy:isKongcheng())
						and getBlackCard(self, target, enemy, true) and self:damageIsEffective(enemy, sgs.DamageStruct_Normal, target)
						and getCardsNum("Jink", enemy, target) + getCardsNum("Peach", enemy, target) + getCardsNum("Analeptic", enemy, target) == 0 then
					self.room:setPlayerFlag(target, "KillAtOnce")
					return target
				end
			end
		end
	end

	for _, target in ipairs(targets) do
		if self:isFriend(target) and target:isAlive()
				and target:hasSkills("tieji|liegong|kofliegong|xiemu|lieren|badao|luashaojie|Luayuelong|luahakurei|luajingdan|tieji|luahuaifei|Luashenqiang") then
			for _, enemy in ipairs(self.enemies) do
				if not enemy:hasArmorEffect("eight_diagram") and target:distanceTo(enemy) <= target:getAttackRange()
						and getBlackCard(self, target, enemy, true) --yun
						and not enemy:hasArmorEffect("renwang_shield") and self:damageIsEffective(enemy, sgs.DamageStruct_Normal, target) then
					return target
				end
			end
		end
	end

	self:sort(self.friends, "defense")
	self:sort(targets, "defense")
	for _, target in ipairs(targets) do
		if self:isFriend(target) then
			return target
		end
	end

end

sgs.ai_skill_cardask["@luatianyan2"] = function(self, data, pattern, target)
	local slasher = self.room:getTag("luatianyanTP"):toPlayer()
	local card = getBlackCard(self, slasher)
	if not card then return end
	return "$" .. card:getEffectiveId()
end

sgs.ai_playerchosen_intention.luatianyan = -40

sgs.ai_skill_playerchosen.luasanjieA = function(self, targets)
	local to = self:findPlayerToDraw(false, 1)
	if to then return to end
	self:sort(self.friends_noself, "handcard")
	for _, target in ipairs(self.friends_noself) do
		return target
	end
	return nil
end

sgs.ai_skill_playerchosen.luasanjieB = function(self, targetsX)
	self:sort(self.enemies, "defense")
	self.room:writeToConsole("luasanjieB test")
	for _, friend in ipairs(self.friends) do
		if not friend:getCards("j"):isEmpty() and not friend:containsTrick("YanxiaoCard") and self:card_for_qiaobian(friend, ".") then
			return friend:objectName()
		end
	end

	for _, enemy in ipairs(self.enemies) do
		if not enemy:getCards("j"):isEmpty() and enemy:containsTrick("YanxiaoCard") and self:card_for_qiaobian(enemy, ".") then
			return enemy:objectName()
		end
	end

	for _, friend in ipairs(self.friends_noself) do
		if not friend:getCards("e"):isEmpty() and self:hasSkills(sgs.lose_equip_skill, friend) and self:card_for_qiaobian(friend, ".") then
			return friend:objectName()
		end
	end
	local targets = {}
	for _, enemy in ipairs(self.enemies) do
		if self:card_for_qiaobian(enemy, ".") then
			table.insert(targets, enemy)
		end
	end

	if #targets > 0 then
		self:sort(targets, "defense")
		return targets[#targets]
	end
end
sgs.ai_skill_playerchosen.luasanjieC = function(self, targets)
	local who = self.room:getTag("luasanjieTarget"):toPlayer()
	self.room:writeToConsole("luasanjieC test")
	if who then
		if not self:card_for_qiaobian(who, "target") then self.room:writeToConsole("NULL") end
		return self:card_for_qiaobian(who, "target")
	end
end
sgs.ai_skill_playerchosen.luasanjieD = function(self, targets)
	self.room:writeToConsole("luasanjieD test")
	for _,junko in sgs.qlist(self.room:findPlayersBySkillName("LuaChunguang")) do
		if self:isFriend(junko) then return junko end
	end
	self:sort(self.friends_noself, "handcard")
	for _, target in ipairs(self.friends_noself) do
		return target
	end
	return nil
end

sgs.ai_skill_discard.luajianta = function(self, discard_num, min_num, optional, include_equip)
	local saki = self.room:findPlayerBySkillName("luajianta")
	if self.player:getMark("@luajianta") == 0 and self:isFriend(saki) then
		local countX = 0
		for _, enemy in ipairs(self.enemies) do
			if enemy:getMark("@luajianta") > 0 then
				countX = countX + 1
			end
		end
		if countX > 1 and self:getOverflow() <= 1 then return "." end
		if countX > 2 then return "." end
		if countX == 1 and (math.random() > 0.7 or self.player:getCards("he"):length() < 3)
			and not self:needToThrowArmor() and not self:needKongcheng() then return "." end
		local to_gain = {}
		if not saki:hasSkill("kuanggu") then
			table.insert(to_gain, "kuanggu")
		end
		if not saki:hasSkill("luajuezhan") then
			table.insert(to_gain, "luajuezhan")
		end
		if not saki:hasSkill("mashu") then
			table.insert(to_gain, "mashu")
		end
		if not saki:hasSkill("luawushuang") then
			table.insert(to_gain, "luawushuang")
		end
		if not saki:hasSkill("luayingzi") then
			table.insert(to_gain, "luayingzi")
		end
		if saki and saki:isAlive() and self:isFriend(saki) and #to_gain > 0 then
			local shuxin = false
			for _, friend in ipairs(self.friends) do
				if friend:hasSkill(sgs.shuxin_skill) then shuxin = true end
			end
			local function Check_R(card)
				for _, skill in sgs.qlist(self.player:getVisibleSkillList(true)) do
					local callback = sgs.ai_cardneed[skill:objectName()]
					if type(callback) == "function" and sgs.ai_cardneed[skill:objectName()](self.player, card, self) then
						return false
					end
				end
				local nazrin = self.room:findPlayerBySkillName("luatanbao")
				if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
					if card:hasFlag("prelingbai") then return end
				end
				if card:isKindOf("TrickCard") then
					if card:isKindOf("IronChain") and not shuxin then return true end
					if card:isKindOf("Nullification") and not self:NullificationIsImportant() then return true end
					if card:isKindOf("Lightning") and not self:willUseLightning(card) then return true end
					if card:isKindOf("Duel") or card:isKindOf("ExNihilo") then return false end
					if card:isKindOf("AOE") and self:getAoeValue(card) > 35 then return false end
					if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
					if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
					if card:isKindOf("Snatch") or card:isKindOf("Banquet") then return false end
					if (card:isKindOf("Dismantlement") or card:isKindOf("SupplyShortage")) and not table.contains(to_gain, "luayingzi") then return false end
					return true
				end
				if card:isKindOf("EquipCard") then
					if card:isKindOf("Treasure") then return false end
					local rinnosuke = self.room:findPlayerBySkillName("luaqinjian")
					if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand) and rinnosuke
							and rinnosuke:isAlive() and self:isFriend(rinnosuke) then return false end
					if card:isKindOf("OffensiveHorse") and card:objectName() ~= "shanghai" then
						return true
					end
					if card:isKindOf("DefensiveHorse") and card:objectName() ~= "hongrai" then
						return true
					end
					if card:isKindOf("Armor") and self:needToThrowArmor() then return true end
					if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
						local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
								or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
						if not bool_3 then return true end
					end
					if card:isKindOf("Weapon") then
						local dummy_use = {isDummy = true}
						self:useEquipCard(card, dummy_use)
						if not dummy_use.card then return true end
						if dummy_use.card and self.player:getWeapon() then return true end
					end
				end
				if card:isKindOf("Analeptic") and self:getCardsNum("Analeptic") < 2 then return false end
				if card:isKindOf("Peach") then return false end
				if card:isKindOf("Slash") and self.player:hasSkills(sgs.need_slash_skill) and not table.contains(to_gain, "luayingzi") then return false end
				local x0 = self:getCardsNum("Analeptic") + self:getCardsNum("Peach") + self:getCardsNum("Jink")
				if card:isKindOf("Jink") and x0 <= 1 then return false end
				return true
			end
			local cards = sgs.QList2Table(self.player:getCards("he"))
			self:sortByKeepValue(cards)
			for _,card in ipairs(cards) do
				if Check_R(card) then return card:getEffectiveId() end
			end
		end
	end
	if self.player:getMark("@luajianta") == 0  then return {} end
end
sgs.ai_skill_choice.luajianta = function(self, choice)
	if not self.player:hasSkill("luayingzi") then return "luayingzi" end
	local Damage = false
	local cards = self:getTurnUse(true)
	for _, card in ipairs(cards) do
		if card:isKindOf("Slash") or card:isKindOf("Duel") or card:isKindOf("AOE") then Damage = true end
	end
	if not self.player:hasSkill("kuanggu") and Damage then return "kuanggu" end
	if not self.player:hasSkill("luajuezhan") then return "luajuezhan" end
	if not self.player:hasSkill("kuanggu") then return "kuanggu" end
	if not self.player:hasSkill("luawushuang") then return "luawushuang" end
	if not self.player:hasSkill("mashu") then return "mashu" end
end
sgs.ai_skill_invoke.luachongfeng = function(self, data)
	local saki = self.room:findPlayerBySkillName("luachongfeng")
	if saki and saki:isAlive() and self:isFriend(saki) then
		return true
	end
end
sgs.ai_choicemade_filter.skillInvoke.luachongfeng = function(self, player, promptlist)
	local saki = self.room:findPlayerBySkillName("luachongfeng")
	if promptlist[3] == "yes" then
		sgs.updateIntention(player, saki, -40)
	else
		sgs.updateIntention(player, saki, 20)
	end
end



