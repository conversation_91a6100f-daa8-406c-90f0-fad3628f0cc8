---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by <PERSON>sage.
--- DateTime: 2021/7/18 16:02
---


extension_pay_aa = sgs.Package("pay16")

mike = sgs.General(extension_pay_aa,"mike","luafeng",3,false,false,false)
tsukasa = sgs.General(extension_pay_aa,"tsukasa","luafeng",3,false,false,false)
sannyo = sgs.General(extension_pay_aa,"sannyo","luafeng",4,false,false,false)
megumu = sgs.General(extension_pay_aa,"megumu$","luafeng",4,false,false,false)
chimata = sgs.General(extension_pay_aa,"chimata","god",4,false,false,false)
takane = sgs.General(extension_pay_aa,"takane","luafeng",3,false,false,false)
momoyo = sgs.General(extension_pay_aa,"momoyo","luafeng",6,false,false,false,5)


luayinkeCard = sgs.CreateSkillCard{
    name = "luayinke",
    will_throw = false,
    filter = function(self, targets, to_select)
        return (#targets == 0) and not to_select:isKongcheng() and to_select:objectName() ~= sgs.Self:objectName()
    end,
    on_effect = function(self, effect)
        local room = effect.from:getRoom()
        local card = room:askForCard(effect.to, ".|.|.|hand!", "@luayinkegive", sgs.QVariant(), sgs.Card_MethodNone)
        room:obtainCard(effect.from, card, false)
        room:obtainCard(effect.to, self, false)
        local flag = string.format("%s_%s_%s","visible", effect.from:objectName(), effect.to:objectName())
        sgs.Sanguosha:getCard(self:getSubcards():first()):setFlags(flag)
        local suit1 = sgs.Sanguosha:getCard(self:getSubcards():first()):getSuit()
        if card:getSuit() == suit1 then
            room:setPlayerMark(effect.to, "@mikemoney", 1)
        end
    end
}
luayinkeVS = sgs.CreateOneCardViewAsSkill{
    name = "luayinke",
    filter_pattern = ".|.|.|hand",
    view_as = function(self,card)
        local skillcard = luayinkeCard:clone()
        skillcard:addSubcard(card)
        return skillcard
    end,
    enabled_at_play = function(self,player)
        return false
    end,
    enabled_at_response = function(self, player, pattern)
        return pattern == "@@luayinke"
    end
}

luayinke = sgs.CreateTriggerSkill{
    name = "luayinke",
    frequency = sgs.Skill_NotFrequent,
    events = {sgs.EventPhaseStart},
    view_as_skill = luayinkeVS,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if player:getPhase() == sgs.Player_Start or player:getPhase() == sgs.Player_Finish then
            room:askForUseCard(player, "@@luayinke", "@luayinke")
        end
    end
}
luayinke2 = sgs.CreateTriggerSkill {
    name = "#luayinke",
    events = {sgs.TargetSpecified},
    can_trigger = function(self, target)
        return target and target:isAlive()
    end,
    on_trigger = function(self, event, player, data)
        local use = data:toCardUse()
        local room = player:getRoom()
        if use.to:length() == 0 then return false end
		if not use.from then return false end 
        if not use.from:hasSkill("luayinke") then return false end
        if use.card:isKindOf("EquipCard") then return false end
        if use.card:isKindOf("SkillCard") then return false end
        for _, p in sgs.qlist(use.to) do
            if p:objectName() ~= player:objectName() then
                return false
            end
        end
        local players = sgs.SPlayerList()
        for _, p in sgs.qlist(room:getAlivePlayers()) do
            if p:getMark("@mikemoney") > 0 then
                players:append(p)
            end
        end
        if players:length() == 0 then return false end
        local targetX = room:askForPlayerChosen(player, players, "luayinke", "luayinke", true)
        if targetX then
            local toUseCard = sgs.Sanguosha:cloneCard(use.card:objectName(), sgs.Card_NoSuit, 0)
            local _data = sgs.QVariant()
            _data:setValue(targetX)
            if use.card:isKindOf("DelayedTrick") or player:isProhibited(targetX, toUseCard) or player:isCardLimited(toUseCard, sgs.Card_MethodUse) then
                room:damage(sgs.DamageStruct(self:objectName(), player, targetX))
                return false
            end
            local choice = room:askForChoice(player, self:objectName(),  "luayinke1+luayinke2", _data)
            if choice == "luayinke1" then 
                room:useCard(sgs.CardUseStruct(toUseCard, player, targetX)) 
            else
                room:damage(sgs.DamageStruct(self:objectName(), player, targetX))
            end
        end
    end
}

luazhaocai = sgs.CreateViewAsSkill{
    name = "luazhaocai",
    n = 3,
    view_filter = function(self, selected, to_select)
        for _,ca in sgs.list(selected) do
            if ca:getSuit() == to_select:getSuit() then return false end
        end
        return #selected < 3
    end,
    view_as = function(self, cards)
        if #cards < 3 then return end
        local acard = sgs.Sanguosha:cloneCard("ex_nihilo", sgs.Card_NoSuit, 0)
        for _, cd in ipairs(cards) do
            acard:addSubcard(cd)
        end
        acard:setSkillName(self:objectName())
        return acard
    end

}
mike:addSkill(luayinke)
mike:addSkill(luayinke2)
mike:addSkill(luazhaocai)

lualicaiCard = sgs.CreateSkillCard{
    name = "lualicai",
    target_fixed = true,
    on_use = function(self, room, source, targets)
        local target = room:getCurrent()
        target:drawCards(1)
        room:setPlayerMark(target, "@luahuhu", 1)
    end
}
lualicaiVS = sgs.CreateViewAsSkill{
    name = "lualicai" ,
    n = 1 ,
    view_filter = function(self, selected, to_select)
        return not sgs.Self:isJilei(to_select)
    end ,
    view_as = function(self, cards)
        if #cards ~= 1 then return nil end
        local lualicaiCC = lualicaiCard:clone()
        lualicaiCC:addSubcard(cards[1])
        return lualicaiCC
    end ,
    enabled_at_play = function()
        return false
    end ,
    enabled_at_response = function(self, player, pattern)
        return pattern == "@@lualicai"
    end
}
lualicai = sgs.CreateTriggerSkill{
    name = "lualicai", --必须
    global = true,
    view_as_skill = lualicaiVS,
    events = {sgs.EventPhaseStart }, --技能触发时机,必须
    on_trigger = function(self, event, player, data) --必须
        local room = player:getRoom()
        if player:getPhase() == sgs.Player_Play then
            if player:objectName() == room:getCurrent():objectName() and not player:hasSkill("lualicai") then
                local tsukasa = player:getRoom():findPlayerBySkillName("lualicai")
                if tsukasa and tsukasa:isAlive() and tsukasa:getMark("luahuhu2") == 0 and  room:askForUseCard(tsukasa, "@@lualicai", "@lualicai") then

                end
            end
        end
    end
}
lualicai2 = sgs.CreateTriggerSkill{
    name = "#lualicai2" ,
    global = true,
    events = {sgs.TurnStart, sgs.EventPhaseStart, sgs.Death} ,
    on_trigger = function(self, event, player, data)
        local room = player:getRoom()
        if event == sgs.EventPhaseStart then
            if player:getPhase() == sgs.Player_Discard and player:objectName() == room:getCurrent():objectName() then
                local tsukasa = player:getRoom():findPlayerBySkillName("lualicai")
                if not tsukasa then return false end
                if not tsukasa:isAlive() then return false end
                local target = room:getCurrent()
                if not target then return false end
                if not target:isAlive() then return false end
                if target:getMark("@luahuhu") == 0 then return false end
                local zuiduo2 = target:getHandcardNum() > tsukasa:getHandcardNum()
                local zuiduo = true
                for  _, kplayer in sgs.qlist(room:getAlivePlayers()) do
                    if kplayer:getHandcardNum() > target:getHandcardNum() then zuiduo = false end
                end
                if zuiduo2 then
                    local card_id = room:askForCard(target, ".!", "@lualicai2", data, sgs.Card_MethodNone)
                    if card_id then
                        local dummy_0 = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
                        dummy_0:addSubcard(card_id)
                        room:clearAG()
                        tsukasa:obtainCard(dummy_0)
                    end
                end
                if zuiduo then
                    tsukasa:drawCards(1)
					room:setPlayerFlag(target, "luaaoshuNull")
                    room:askForUseCard(target, "TrickCard+^Nullification,BasicCard+^Jink,EquipCard|.|.|hand", "@lualicai3")
					room:setPlayerFlag(target, "-luaaoshuNull")
                end
                if (not zuiduo) and (not zuiduo2) then
                    room:setPlayerMark(tsukasa, "luahuhu2", target:getSeat() + 1)
                end
                room:setPlayerMark(target, "@luahuhu", 0)
            end
        elseif event == sgs.TurnStart then
            local tsukasa = player:getRoom():findPlayerBySkillName("lualicai")
            if tsukasa and tsukasa:isAlive() and room:getCurrent():getSeat() + 1 == tsukasa:getMark("luahuhu2") then
                room:setPlayerMark(tsukasa, "luahuhu2", 0)
            end
        else
            local tsukasa = player:getRoom():findPlayerBySkillName("lualicai")
            local death = data:toDeath()
            if tsukasa and tsukasa:isAlive() and death.who:getSeat() + 1 == tsukasa:getMark("luahuhu2") then
                room:setPlayerMark(tsukasa, "luahuhu2", 0)
            end
        end
        return false
    end ,
    can_trigger = function(self, target)
        return target
    end ,
    priority = 1
}
luaguansha = sgs.CreateTriggerSkill{
    name = "luaguansha",
    frequency = sgs.Skill_Compulsory,
    events = {sgs.DamageForseen},
    on_trigger = function(self, event, player, data)
        local damage = data:toDamage()
        local room = player:getRoom()
        if event == sgs.DamageForseen then
            if damage.to and (damage.to:hasSkill("luaguansha")) then
                if player:objectName() ~= damage.to:objectName() then return false end
                if player:getHandcardNum() < room:getCurrent():getHandcardNum()
                    and player:getMark("luaguansha2") == 0 then
                    damage.damage = damage.damage - 1
                    room:notifySkillInvoked(damage.to, "luaguansha")
                    room:setPlayerMark(player, "luaguansha2", 1)
                    data:setValue(damage)
                end
            end
            return false
        end
    end
}
luaguansha2 = sgs.CreateTriggerSkill{
    name = "#luaguansha2",
    global = true,
    priority = 10,
    events = {sgs.TurnStart },
    on_trigger = function(self, event, player, data, room)
        local tsukasa = player:getRoom():findPlayerBySkillName("lualicai")
        if tsukasa and tsukasa:isAlive() then
            room:setPlayerMark(tsukasa, "luaguansha2", 0)
        end
    end
}
tsukasa:addSkill(lualicai)
tsukasa:addSkill(lualicai2)
tsukasa:addSkill(luaguansha)
tsukasa:addSkill(luaguansha2)


luayanmeng = sgs.CreateTriggerSkill{
	name = "luayanmeng",
	frequency = sgs.Skill_Compulsory,
	global = true,
	events = {sgs.Damaged, sgs.PreDamageDone}, 
	on_trigger = function(self, event, player, data, room)
		local damage = data:toDamage()
		local damagef = damage.from
		if event == sgs.PreDamageDone and damagef and damagef:isAlive() and damagef:hasSkill(self:objectName()) and damage.card then  
			local sannyo = room:findPlayerBySkillName("luayanmeng")
			if sannyo:getPhase() ~= sgs.Player_NotActive and damagef:getPhase() ~= sgs.Player_NotActive then
				damage.to:setTag("invoke_luayanmeng", sgs.QVariant((not damage.card:isKindOf("AOE"))))
			end 
		elseif event == sgs.Damaged and player and player:isAlive() and player:getTag("invoke_luayanmeng") then
			local sannyo = room:findPlayerBySkillName("luayanmeng")
			local invoke = player:getTag("invoke_luayanmeng"):toBool() 
			player:setTag("invoke_luayanmeng", sgs.QVariant(false))
			if invoke then 
				room:recover(player, sgs.RecoverStruct(sannyo))
				if not player:isAllNude() then
					local card_id = room:askForCardChosen(sannyo, player, "hej", "luayanmeng")
					if card_id then
						local function throw(id)
							local dummy = sgs.Sanguosha:cloneCard("jink")
							dummy:addSubcard(id)
							room:throwCard(dummy, nil, player)
						end
					    local ids = sgs.IntList()
						ids:append(card_id)
						room:fillAG(ids)
						room:getThread():delay(1500)
						room:clearAG()
						local card2 = sgs.Sanguosha:getCard(card_id)
						room:setCardFlag(card2, "visible") 
						if player:isCardLimited(card2, sgs.Card_MethodUse) then throw(ids:at(0)); return false end
						if card2:isKindOf("Jink") or card2:isKindOf("sakura") or card2:isKindOf("Nullification") then
							throw(ids:at(0))
						else
							local p = room:askForPlayerChosen(sannyo, room:getAlivePlayers(), "luayanmeng", "luayanmengX", true, true)
							if p then
								if card2:isKindOf("AOE") or card2:isKindOf("AmazingGrace") or card2:isKindOf("GodSalvation") then
									room:setPlayerFlag(player, "qucaiAOE")
									room:setPlayerFlag(p, "qucaiAOEs") 
									room:useCard(sgs.CardUseStruct(card2, player, sgs.SPlayerList()))
									room:setPlayerFlag(player, "-qucaiAOE")
									room:setPlayerFlag(p, "-qucaiAOEs")
								else  
									local Carddata2 = sgs.QVariant() -- ai用
									Carddata2:setValue(sgs.Sanguosha:getCard(ids:at(0)))
									room:setTag("luayanmengTC", Carddata2)
									room:removeTag("luayanmengTC") 
									room:useCard(sgs.CardUseStruct(card2, player, p)) 
								end 
							end 
						end
					end 
				end 
			end 
		elseif event == sgs.Damaged then
			
		end
		return false
	end
} 
luayanmeng3 = sgs.CreateTriggerSkill{
    name = "#luayanmeng3",
    global = true,
    events = {sgs.TurnStart, sgs.EventPhaseEnd, sgs.Death},
    on_trigger = function(self, event, player, data, room)
        local sannyo = room:findPlayerBySkillName("luayanmeng")
		if not sannyo then return false end
		if not sannyo:isAlive() then
			for _, p in sgs.qlist(room:getAllPlayers()) do
				room:detachSkillFromPlayer(p, "luayanmeng2")
			end
			return false
		end
		if event == sgs.TurnStart then
			if room:getCurrent():hasSkill("luayanmeng") then
				if room:getCurrent():objectName() == player:objectName() then
					for _, p2 in sgs.qlist(room:findPlayersBySkillName("luayanmeng")) do
						for _, p in sgs.qlist(room:getAllPlayers()) do 
							if not p:hasSkill("luayanmeng2") then room:acquireSkill(p, "luayanmeng2", false) end
							room:filterCards(p, p:getCards("h"), true)
						end
					end
				end
			end
		elseif event == sgs.EventPhaseEnd then
			if room:getCurrent():getPhase() == sgs.Player_Finish then
				for _, p in sgs.qlist(room:getAllPlayers()) do
					room:detachSkillFromPlayer(p, "luayanmeng2")
				end 
			end
		end
	end
}
sannyo:addSkill(luayanmeng)
sannyo:addSkill(luayanmeng3)

luaqunxingCard = sgs.CreateSkillCard{
	name = "luaqunxing",
	will_throw = false,
	target_fixed = true,
	handling_method = sgs.Card_MethodNone,
	on_use = function(self, room, source, targets)
		local function MoveToPlaceUnknown2DrawPile(id)
			local move = sgs.CardsMoveStruct()
			move.from = nil
			move.from_place = sgs.Player_PlaceUnknown
			move.to = nil
			move.to_place = sgs.Player_DrawPile
			move.card_ids = sgs.IntList()
			move.card_ids:append(id)
			move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_UNKNOWN, source:objectName())
			room:moveCardsAtomic(move, true)
		end 
		local function YouMuCheck(card, target)
            if card:isKindOf("Hui") or card:isKindOf("Ofuda") then
                return true
            elseif card:isKindOf("FaithCollection") then
                return not target:isNude()
            elseif card:isKindOf("Banquet") then
                return not target:containsTrick("banquet")
            end
			return false
        end
		if source:getPhase() == sgs.Player_Start then 
			local x = self:getSubcards():length()
			local card_ids = room:getNCards(source:getHandcardNum()) --罪魁祸首
			if x == 0 then 
				for _,id in sgs.qlist(card_ids) do 
					local cardX = sgs.Sanguosha:getCard(id)   
					room:setCardFlag(cardX, "megumuT") 
				end
				
				local dummy3 = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
				dummy3:addSubcards(card_ids)
				local reason = sgs.CardMoveReason()
				room:moveCardTo(dummy3, nil, nil,  sgs.Player_PlaceUnknown, reason, true) 
				
				local pq = card_ids:length() - 1 
				while pq >= 0 do
					MoveToPlaceUnknown2DrawPile(card_ids:at(pq))
					pq = pq - 1
				end 				
				return 
			end 
			local arrayA = {}
			for i = 0, card_ids:length() - 1 do
				arrayA[i + 1] = card_ids:at(i)
			end  
			local obtained = sgs.IntList()
			for i = 1, x do
				room:fillAG(card_ids,source)
				local id1 = room:askForAG(source,card_ids,false,self:objectName()) 
				card_ids:removeOne(id1)
				for j = 1, #arrayA do 
					if arrayA[j] == id1 then arrayA[j] = -1 end 
				end 
				obtained:append(id1) 
				room:clearAG(source)
			end  
			local dummy = sgs.Sanguosha:cloneCard("jink",sgs.Card_NoSuit,0)
			for _,id in sgs.qlist(obtained) do
				dummy:addSubcard(id)
			end
			source:obtainCard(dummy, false)
			
			local dummy3 = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
			dummy3:addSubcards(card_ids)
			local reason = sgs.CardMoveReason()
			room:moveCardTo(dummy3, nil, nil,  sgs.Player_PlaceUnknown, reason, true) 
			
			local dummy2 = sgs.Sanguosha:cloneCard("jink",sgs.Card_NoSuit,0)
			local card_ids2 = self:getSubcards()
			local pq = card_ids2:length() - 1 
			for j = #arrayA, 1, -1 do 
				if arrayA[j] >= 0 then 
					MoveToPlaceUnknown2DrawPile(arrayA[j])
					local card1 = sgs.Sanguosha:getCard(arrayA[j])
					room:setCardFlag(card1, "megumuT")  
				else
					local move = sgs.CardsMoveStruct()
					move.from = source
					move.from_place = sgs.Player_PlaceHand
					move.to = nil
					move.to_place = sgs.Player_DrawPile
					move.card_ids = sgs.IntList()
					move.card_ids:append(card_ids2:at(pq))
					move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, source:objectName())
					room:moveCardsAtomic(move, true)
					local card1 = sgs.Sanguosha:getCard(card_ids2:at(pq))
					room:setCardFlag(card1, "megumuT")
					pq = pq - 1
				end 
			end  --应该没有bug 2022年10月28日01:09:36 Perhaps  -- 确实没有bug 2023年12月9日17:27:04
		else 
			local card_ids = room:getNCards(12)
			local card_ids2 = sgs.IntList()
			local array3 = {}
			for i = 0, card_ids:length() - 1 do
				array3[i + 1] = card_ids:at(i)
			end  
			for _,id in sgs.qlist(card_ids) do 
				local cardx = sgs.Sanguosha:getCard(id)
				if cardx:hasFlag("megumuT") then
					room:writeToConsole("megumu test card3 " .. id)
					card_ids2:append(id)
				end 
			end 
			
			if card_ids2:length() == 0 then return end 
			
			if source:getMark("luaqunxingY") == 0 then
				local to_givelist = sgs.SPlayerList()
				to_givelist:append(source)
				if not room:askForPlayerChosen(source, to_givelist, self:objectName(), "luaqunxingY-invoke", true, true) then
					source:addMark("luaqunxingY")
				end  
			end 
			
			room:fillAG(card_ids2,source) 
			local id1 = room:askForAG(source, card_ids2, false, self:objectName()) 
			local card2 = sgs.Sanguosha:getCard(id1) 
			card_ids:removeOne(id1)
			room:clearAG(source)
			local dummy3 = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
			dummy3:addSubcards(card_ids)
			local reason = sgs.CardMoveReason()
			room:moveCardTo(dummy3, nil, nil, sgs.Player_PlaceUnknown, reason, true) 
			
			local pq = card_ids:length() - 1 
			while pq >= 0 do
				MoveToPlaceUnknown2DrawPile(card_ids:at(pq))
				pq = pq - 1
			end 
			
			local hasUsed = false
			local canSlashX = ((Weapon and Weapon:isKindOf("Crossbow")) or source:canSlashWithoutCrossbow()) and sgs.Slash_IsAvailable(source)
			if not (card2:isKindOf("Jink") or card2:isKindOf("sakura") or card2:isKindOf("Nullification") or source:isCardLimited(card2, sgs.Card_MethodUse)) 
				and not card2:isKindOf("Collateral") 
				and ((not card2:isKindOf("Slash")) or canSlashX) then 
				
				if card2:targetFixed() then  
					if (not card2:isKindOf("Peach")) or (source:isWounded()) then 
						room:useCard(sgs.CardUseStruct(card2, source, sgs.SPlayerList()))
						hasUsed = true
					end 
				else	  
					local available_targets = sgs.SPlayerList()
					for _, p2 in sgs.qlist(room:getOtherPlayers(source)) do
					    if (card2:targetFilter(sgs.PlayerList(), p2, source) or YouMuCheck(card2, p2)) and not room:isProhibited(source, p2, card2) then
                            available_targets:append(p2)
                        end
					end 
					if available_targets:length() > 0 then
						local p = room:askForPlayerChosen(source, available_targets, "luaqunxing", "luaqunxingX", true, false)  
						if card2:isKindOf("IronChain") then
							available_targets:removeOne(p)
							if available_targets:length() > 0 then
								local p2 = room:askForPlayerChosen(source, available_targets, "luaqunxing", "luaqunxingX", true, false) 
								if p2 then 
									local targets_list = sgs.SPlayerList()
									targets_list:append(p)
									targets_list:append(p2)
									room:useCard(sgs.CardUseStruct(card2, source, targets_list))
								else
									room:useCard(sgs.CardUseStruct(card2, source, p), true)  
									hasUsed = true  
								end 
							else
								room:useCard(sgs.CardUseStruct(card2, source, p), true)  
								hasUsed = true 
							end 
						else 
							if p then  
								room:useCard(sgs.CardUseStruct(card2, source, p), true)  
								hasUsed = true
							end 
						end 
						
					end 
				end 
			end 
			
			if hasUsed == false then
				for j = #array3, 1, -1 do 
					local move = sgs.CardsMoveStruct()
					move.from = nil
					move.from_place = sgs.Player_DrawPile
					move.to = nil
					move.to_place = sgs.Player_PlaceUnknown 
					move.card_ids = sgs.IntList()
					move.card_ids:append(array3[j])
					move.reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, source:objectName())
					room:moveCardsAtomic(move, false)				
				end 
				for j = #array3, 1, -1 do 
					MoveToPlaceUnknown2DrawPile(array3[j])
				end 	
			end 
		end  
	end
}

luaqunxingVS = sgs.CreateViewAsSkill{
	name = "luaqunxing",
	n = 99,
	view_filter = function(self, selected, to_select)
		return true
	end,
	view_as = function(self, cards) 
		local card = luaqunxingCard:clone()
		for _, cd in ipairs(cards) do
			card:addSubcard(cd)
		end
		return card
	end ,
	enabled_at_play = function(self, player)
		return player:getMark("luaqunxingX") > 0
	end,
	enabled_at_response = function(self, player, pattern)
		return pattern == "@@luaqunxing"
	end

}

luaqunxing = sgs.CreateTriggerSkill{
    name = "luaqunxing", 
	view_as_skill = luaqunxingVS,
    events = {sgs.EventPhaseStart, sgs.EventPhaseChanging},
    on_trigger = function(self, event, player, data, room)
		local to_givelist = sgs.SPlayerList()
		to_givelist:append(player)
		if event == sgs.EventPhaseStart and player:getPhase() == sgs.Player_Start and player:hasSkill("luaqunxing") 
			and not player:isKongcheng() and room:askForPlayerChosen(player, to_givelist, self:objectName(), "luaqunxing-invoke", true, true) then
			room:setPlayerMark(player, "luaqunxingX", 1)
			local x = player:getHandcardNum()
			local card_ids = room:getNCards(x)
			room:askForGuanxing(player, card_ids, sgs.Room_GuanxingUpOnly)
			room:setPlayerCardLimitation(player, "use", ".|.|.|hand", true)
			room:askForUseCard(player, "@@luaqunxing", "@luaqunxing")
		elseif event == sgs.EventPhaseChanging then
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				room:setPlayerMark(player, "luaqunxingX", 0)
			end  
		end 
	end
}
luaqunxing3 = sgs.CreateTriggerSkill{
	name = "#luaqunxing3", 
	global = true, 
	priority = 99,
	events = {sgs.CardsMoveOneTime}, 
	on_trigger = function(self, event, player, data, room)
		local move = data:toMoveOneTime()
		if move.to_place ~= sgs.Player_PlaceUnknown and move.to_place ~= sgs.Player_PlaceTable and move.to_place ~= sgs.Player_PlaceWuGu
			and move.to_place ~= sgs.Player_DrawPile then 
			for _, id in sgs.qlist(move.card_ids) do
				local cardx = sgs.Sanguosha:getCard(id)
				if cardx:hasFlag("megumuT") then 
					room:writeToConsole("megumu test card2 " .. id)
					cardx:setFlags("-megumuT")
				end 
			end 
		end 
	end
}

luahonggguang2 = sgs.CreateTriggerSkill{
	name = "#luahonggguang2" ,
	frequency = sgs.Skill_Compulsory ,
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		if change.to == sgs.Player_Draw and player:hasSkill("luahonggguang") then player:skip(change.to) end
		return false
	end
}

luahonggguang = sgs.CreateTriggerSkill{
	name = "luahonggguang" ,
	global = true,
	frequency = sgs.Skill_NotFrequent ,
	events = {sgs.CardsMoveOneTime, sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom() 
		if event == sgs.CardsMoveOneTime then
			local Megumu = room:findPlayerBySkillName("luahonggguang")
			if Megumu and Megumu:isAlive() then
				local move = data:toMoveOneTime()
				if not move.from then return false end 
				local bool = false
				local j = 0
				for _,card_id in sgs.qlist(move.card_ids) do
					local place = move.from_places:at(j)
					if place == sgs.Player_PlaceHand or place == sgs.Player_PlaceEquip then
						bool = true 
					end 
					j = j + 1
				end

				local _movefrom
				for _, p in sgs.qlist(room:getAlivePlayers()) do
					if move.from:objectName() == p:objectName() then
						_movefrom = p
						break
					end
				end
				local reason = move.reason
				local basic = bit32.band(reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)
				if not _movefrom then return false end 
				if not _movefrom:isAlive() or _movefrom:objectName() ~= player:objectName() or _movefrom:getPhase() == sgs.Player_Discard then return false end 
				if bool and (basic == sgs.CardMoveReason_S_REASON_DISCARD) then
					room:setPlayerMark(_movefrom, "honggguang", 1)
				end 
			end 
		else
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive then
				if player:objectName() == room:getCurrent():objectName() then 
					for _, Megumu in sgs.qlist(room:findPlayersBySkillName("luahonggguang")) do
						for _, p in sgs.qlist(room:getAlivePlayers()) do
							if p:getMark("honggguang") > 0 and room:askForSkillInvoke(p, self:objectName()) then
								Megumu:drawCards(1)
							end 
							room:setPlayerMark(p, "honggguang", 0)
						end 
					end 
				end 
			end
		end
	end
}

luadiaoling = sgs.CreateTriggerSkill{
	name = "luadiaoling$" ,
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local xo = room:getLord():getMark("@clock_time") + 1
		local change = data:toPhaseChange()
		if player:isLord() and change.to == sgs.Player_Start and player:getMark("luadiaoling") ~= xo then 
			local p = room:askForPlayerChosen(player, room:getOtherPlayers(player), self:objectName(), "luadiaoling-invoke", true, true)
			if p and p:isAlive() then 
				room:setPlayerMark(player, "luadiaoling", xo)
				local roomx = p:getRoom()
				local thread = roomx:getThread()
				local old_phase = p:getPhase()
				p:setPhase(sgs.Player_Start)	
				roomx:broadcastProperty(p, "phase")
				if not thread:trigger(sgs.EventPhaseStart, roomx, p) then
					thread:trigger(sgs.EventPhaseProceeding, roomx, p)
				end
				thread:trigger(sgs.EventPhaseEnd, roomx, p)
				p:setPhase(old_phase)
				roomx:broadcastProperty(p, "phase")
			end 
		end 
	end
}

megumu:addSkill(luaqunxing)
megumu:addSkill(luaqunxing3)
megumu:addSkill(luahonggguang)
megumu:addSkill(luahonggguang2)
megumu:addSkill(luadiaoling)

--askForSkillInvoke
--ai_skill_invoke
luashichang = sgs.CreateTriggerSkill{
	name = "luashichang" ,
	global = true,
	frequency = sgs.Skill_NotFrequent ,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local Chimata = room:findPlayerBySkillName("luashichang")
		if (player:getPhase() == sgs.Player_Play) and Chimata and Chimata:isAlive()
			and not (player:isNude() and Chimata:isNude()) and room:askForSkillInvoke(Chimata, self:objectName()) then
			local choices = {"luashichang1"}
			if not player:isNude() then
				table.insert(choices, "luashichang2")
			end
			local choice = room:askForChoice(Chimata, "luashichang", table.concat(choices, "+"))
			if choice == "luashichang1" then
				room:askForDiscard(Chimata, self:objectName(), 1, 1, false, true)
				Chimata:drawCards(1)
			else
				local card = sgs.Sanguosha:getCard(room:askForCardChosen(Chimata, player, "he", self:objectName(), true, sgs.Card_MethodDiscard)) 
				room:throwCard(card, player, Chimata)
				if card:getSuit() == sgs.Card_Diamond then
					player:drawCards(2)
				else 
					player:drawCards(1)
				end 
				
			end 
		end 
	end 
}
luahonghuanx = sgs.CreateTriggerSkill{
	name = "luahonghuanx" ,
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.CardsMoveOneTime},
	on_trigger = function(self, event, player, data)
		local room = player:getRoom()
		local Chimata = room:findPlayerBySkillName("luahonghuanx")
		if Chimata and Chimata:isAlive() then
			local move = data:toMoveOneTime()
			local reason = move.reason
			if reason.m_playerId == Chimata:objectName() and move.card_ids:length() > 0 then 
				if not move.from then return false end 
				local reason = move.reason
				local basic = bit32.band(reason.m_reason, sgs.CardMoveReason_S_MASK_BASIC_REASON)	
				if (basic == sgs.CardMoveReason_S_REASON_DISCARD) then
					local old_card_ids = {}
					for _,card_idX in sgs.qlist(move.card_ids) do	
						table.insert(old_card_ids, card_idX)
					end 	
					local i = 0				
					for _, id in ipairs(old_card_ids) do		
						local cardA = sgs.Sanguosha:getCard(id)	
						if cardA and cardA:hasFlag("basicrule1") then
							cardA:setFlags("-basicrule1") 
							Chimata:addToPile("luahonghuanx", id)
							move.card_ids:removeOne(id)
							move.from_places:removeAt(i)	
						end 
						i = i + 1
					end 
				end
				data:setValue(move) 
				
				local AmazingGrace = sgs.Sanguosha:cloneCard("amazing_grace")
				if Chimata:getPile("luahonghuanx"):length() >= room:getAlivePlayers():length() and not Chimata:isCardLimited(AmazingGrace, sgs.Card_MethodUse) then 
					local dummy3 = sgs.Sanguosha:cloneCard("jink", sgs.Card_NoSuit, 0)
					local abcList = sgs.IntList()
					local pq = Chimata:getPile("luahonghuanx"):length() - 1 
					while pq >= 0 do
						abcList:append(Chimata:getPile("luahonghuanx"):at(pq))
						pq = pq - 1
					end 			
					dummy3:addSubcards(abcList)
					local reason = sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, Chimata:objectName(), "luahonghuanx", "")
					room:moveCardTo(dummy3, nil, sgs.Player_DrawPile, reason, true)	
					room:useCard(sgs.CardUseStruct(AmazingGrace, Chimata, sgs.SPlayerList())) 
				else
					AmazingGrace:deleteLater()
				end 
			end 
		end 
		return false 
	end
}
luahonghuanx2 = sgs.CreateTriggerSkill{
	name = "#luahonghuanx2" ,
	frequency = sgs.Skill_Compulsory ,
	events = {sgs.EventPhaseChanging} ,
	on_trigger = function(self, event, player, data)
		local change = data:toPhaseChange()
		if change.to == sgs.Player_Draw and player:hasSkill("luahonghuanx") then player:skip(change.to) end
		if change.to == sgs.Player_Discard and player:hasSkill("luahonghuanx") then player:skip(change.to) end
		return false
	end
}
chimata:addSkill(luashichang)
chimata:addSkill(luahonghuanx)
chimata:addSkill(luahonghuanx2)

lualiediCard = sgs.CreateSkillCard{
    name = "lualiedi", 
	target_fixed = false,
	filter = function(self, targets, to_select)
		return (sgs.Self:inMyAttackRange(to_select) or to_select:objectName() == sgs.Self:objectName()) and #targets <= 0
	end, 
	on_effect = function(self, effect)
		local room = effect.from:getRoom()
		local playerX = effect.to
		local patternA = 2
		local patternB = 1
		room:setPlayerMark(effect.from, "@lualiedi", 0)
		local lord = room:getLord() 
		room:setPlayerMark(lord, "lualiediStop", 0)
		for i = 1, 9999 do
			if not room:askForDiscard(playerX, self:objectName(), patternA, patternA, true, false, "@luafajue" .. patternB) then 
				room:damage(sgs.DamageStruct(self:objectName(), effect.from, playerX, patternB))
				--patternA = patternA + 1
			else
				patternB = patternB + 1
			end 
			if lord:getMark("lualiediStop") > 0 then 
				room:setPlayerMark(lord, "lualiediStop", 0)
				break 
			end 

			playerX = playerX:getNextAlive()
			room:getThread():delay(800)
		end 
	end 
}
lualiediVS = sgs.CreateViewAsSkill{
	name = "lualiedi", 
	n = 2,
	view_filter = function(self, selected, to_select)
		return to_select:isKindOf("EquipCard")
	end,
	view_as = function(self, cards)
		if #cards <= 1 then return end
		local card = lualiediCard:clone()
		for _, cd in ipairs(cards) do
			card:addSubcard(cd)
		end
		return card
	end,
	enabled_at_play = function(self, player)
		return player:getMark("@lualiedi") > 0
	end
} 
lualiedi = sgs.CreateTriggerSkill{
	name = "lualiedi",
	frequency = sgs.Skill_Limited,
	events = {sgs.Dying, sgs.GameStart},
	limit_mark = "@lualiedi",
	view_as_skill = lualiediVS,
	on_trigger = function()
	end
}
lualiedi5 = sgs.CreateTriggerSkill{
	name = "#lualiedi5" ,
	global = true,
	events = {sgs.Dying } ,
	on_trigger = function(self, event, player, data) 
		if event == sgs.Dying then 
			local room = player:getRoom()
			local dying = data:toDying()
			local _player = dying.who
			local lord = room:getLord() 
			room:setPlayerMark(lord, "lualiediStop", 1)
		end 
		return false
	end ,
	priority = 10
}

luafajue = sgs.CreateTriggerSkill{
	name = "luafajue",
	global = true,
	frequency = sgs.Skill_Compulsory, 
	events = {sgs.DamageInflicted, sgs.EventPhaseChanging},
	on_trigger = function(self, event, player, data)
		if event == sgs.DamageInflicted then 
			local room = player:getRoom()
			local damage = data:toDamage()
			local _player = damage.to
			
			for _, Momoyo in sgs.qlist(room:findPlayersBySkillName("luafajue")) do 
				if Momoyo and Momoyo:isAlive() then
					if damage.damage and _player and _player:getHp() <= damage.damage then
						local killer = damage.from
						if killer and killer:objectName() == Momoyo:objectName() then 
							local target = room:askForPlayerChosen(Momoyo, room:getAlivePlayers(), self:objectName(), "luafajue-invoke", true)
							if target then
								room:setPlayerProperty(target, "hp", sgs.QVariant(target:getHp() + 1))
								room:setPlayerMark(Momoyo, "@luafajue", Momoyo:getMark("@luafajue") + 1)
							end 
						end 
					end 
				end 
			end 
		else
			local room = player:getRoom()
			local change = data:toPhaseChange()
			if change.to == sgs.Player_NotActive and player:hasSkill("luafajue") and player:objectName() == room:getCurrent():objectName() then
				while player:getMark("@luafajue") >=3 do 
					local limit_skill = {"yeyan", "luanwu", "fuli", "jiefan", "lualianmeng", "luaguixu", "luaguixu", "luacp", "luasushen", "luageming",
						"lualiedi", "lualianai","Luayuanxing", "lualongyan", "luahuiyi"}
					local choices = {}
					for i = 1,#limit_skill do
						if not player:hasSkill(limit_skill[i]) then
							table.insert(choices, limit_skill[i])
						end 
					end 
					local choice = room:askForChoice(player, "luafajue", table.concat(choices, "+"))
					room:acquireSkill(player, choice)
					room:setPlayerMark(player, "@luafajue", math.max(player:getMark("@luafajue") - 3, 0))
				end  
			end 
		end 
	end
}
momoyo:addSkill(lualiedi)
momoyo:addSkill(lualiedi5)
momoyo:addSkill(luafajue)

local OncePerTurn = {   --未实现的有 大妖精【祝福】 高丽野阿吽【守护】 娜兹铃【灵摆】 秦心【心舞】
	sunquan = {"zhiheng"},
	huanggai = {"kurou"},
	sunshangxiang = {"jieyin"},
	chengong = {"mingce"},
	masu = {"xinzhan"},
	wuguotai = {"ganlu"},

	shinki = {"shenpan"},
	parsee = {"luaduhuo"},
	medicine = {"lualinglan"},
	sumireko = {"luachaogan"},
	ran = {"luatianhu"},
	
	reimu = {"luahakurei"},
	fujiwara = {"luahonghun"},
	suika = {"luasuiyue"},
	jz_reimu = {"luaboli"},
	tokiko = {"luajieao"},
 
	sekibanki = {"luayanguang"}, 
	doremi = {"luamimeng"},
	yuyuko = {"luayouqu"},
	patchouli = {"Luaxianzhe"},

	rumia = {"luayueshi"},
	tewi = {"luaxingyun"},
	tatara = {"luajinglian"},
	futo = {"Luazonghuo"},
	sp_suwako = {"luajiangsui"},

	toyosatomimi = {"luawangshi"},
	mihana = {"luashenjun"},
	sangetsusei = {"luaxieli"}, 
}
local function XXXXOOOO()
	local result = {}
	for rowKey, row in pairs(OncePerTurn) do
		table.insert(result, rowKey)
	end 
	return result
end 
local function GetAvailableGeneralsForShisi(takane, room)  
	local all  = XXXXOOOO() 
	local jingjie_exs = {}
	local Hs_String = takane:getTag("Shisi_exs"):toString()
	if Hs_String and Hs_String ~= "" then
		jingjie_exs = Hs_String:split("+")
	end 
	for i = 1, #jingjie_exs do 
		local jingjie_exs2 = {}
		jingjie_exs2 = jingjie_exs[i]:split("|") 
		if #jingjie_exs2 > 0 and jingjie_exs2[2] == "Heart" then 
			table.removeOne(all, jingjie_exs2[1])
		end 	
	end
	for _,player in sgs.qlist(room:getAlivePlayers()) do
		local name = player:getGeneralName()
		if sgs.Sanguosha:isGeneralHidden(name) then
			local fname = sgs.Sanguosha:findConvertFrom(name);
			if fname ~= "" then name = fname end
		end
		table.removeOne(all,name)

		if player:getGeneral2() == nil then continue end

		name = player:getGeneral2Name();
		if sgs.Sanguosha:isGeneralHidden(name) then
			local fname = sgs.Sanguosha:findConvertFrom(name);
			if fname ~= "" then name = fname end
		end
		table.removeOne(all,name)
	end 
	room:writeToConsole("jingjieX " .. all[1])
	return all 
end 
local function checkShisi(takane, room) 
	local jingjie_exs4 = {} 
	local jingjie_exs = {}
	local Hs_String = takane:getTag("Shisi_exs"):toString()
	if Hs_String and Hs_String ~= "" then
		jingjie_exs = Hs_String:split("+")
	end 
	for i = 1, #jingjie_exs do 
		local jingjie_exs2 = {}
		jingjie_exs2 = jingjie_exs[i]:split("|")
		if #jingjie_exs2 > 0 and jingjie_exs2[2] == "Heart" then 
			table.insert(jingjie_exs4, jingjie_exs2[1])
		end 
	end
	return jingjie_exs4
end
local function remove1GeneralTK(takane, suit, room, generalname)
	local Hs_String = takane:getTag("Shisi_exs"):toString()
	if Hs_String and Hs_String ~= "" then 
		local modifiedString = "" 
		local stringToRemove = generalname .. "|Heart"
		modifiedString = Hs_String:gsub("%+"..stringToRemove, "") 
		if modifiedString == Hs_String then
			modifiedString = Hs_String:gsub(stringToRemove, "")
		end
		room:writeToConsole("remove1General" .. modifiedString)
		takane:setTag("Shisi_exs", sgs.QVariant(modifiedString))
	end 
end 

luashisiCard = sgs.CreateSkillCard{
	name = "luashisi", 
	target_fixed = true,  
	on_use = function(self, room, takane, targets) 
		local Hs_String = takane:getTag("Shisi_exs"):toString()
		if Hs_String and Hs_String ~= "" then 
			local choice = room:askForChoice(takane, self:objectName(), "luashisi1+luashisi2")
			if choice == "luashisi1" then
				local all = GetAvailableGeneralsForShisi(takane, room)
				local rand = math.random(1, #all)
				local testM = {}  --测试用
				for j = 1, #all do
					for k = 1, #all do
						if all[j] == testM[k] then 
							rand = j
							break;
						end 
					end 
				end 
				if Hs_String and Hs_String ~= "" then 
					Hs_String = Hs_String .. "+" .. all[rand] .. "|Heart" 
				else
					Hs_String = all[rand] .. "|Heart" 
				end
				room:writeToConsole("ShisiX " .. Hs_String)
				takane:setTag("Shisi_exs", sgs.QVariant(Hs_String))
				local jingjie_exs4 = checkShisi(takane, room)
				room:askForGeneral(takane, "huben+" .. table.concat(jingjie_exs4,"+")) 
			else
				local jingjie_exs4 = checkShisi(takane, room)
				local GeneralK = room:askForGeneral(takane, "huben+" .. table.concat(jingjie_exs4,"+")) 
				if GeneralK and GeneralK ~= "" and GeneralK ~= "huben" then 
					if OncePerTurn[GeneralK] and #OncePerTurn[GeneralK] > 0 then   
						local to = room:askForPlayerChosen(takane, room:getAlivePlayers(), self:objectName(), "luashisiGive", false, true)
						if to and not to:hasSkill(OncePerTurn[GeneralK][1]) then
							room:writeToConsole("Hello World! " .. OncePerTurn[GeneralK][1])
							remove1GeneralTK(takane, sgs.Card_Heart, room, GeneralK) 
							room:acquireSkill(to, OncePerTurn[GeneralK][1])
 
							local loseSkill = to:getTag("luashisiL")
							if loseSkill:toString() and loseSkill:toString() ~= "" then 
								to:setTag("luashisiL", sgs.QVariant(loseSkill:toString() .. "|" .. OncePerTurn[GeneralK][1])) 
							else
								to:setTag("luashisiL", sgs.QVariant(OncePerTurn[GeneralK][1]))  
							end  

						end 
					end 
				end 
			end 
		else
			local all = GetAvailableGeneralsForShisi(takane, room)
			local rand = math.random(1, #all)
			local testM = {}  --测试用
			for j = 1, #all do
				for k = 1, #all do
					if all[j] == testM[k] then 
						rand = j
						break;
					end 
				end 
			end 
			if Hs_String and Hs_String ~= "" then 
				Hs_String = Hs_String .. "+" .. all[rand] .. "|Heart" 
			else
				Hs_String = all[rand] .. "|Heart" 
			end
			room:writeToConsole("ShisiX " .. Hs_String)
			takane:setTag("Shisi_exs", sgs.QVariant(Hs_String))
			local jingjie_exs4 = checkShisi(takane, room)
			room:askForGeneral(takane, "huben+" .. table.concat(jingjie_exs4,"+")) 
		end 
	end 
}
luashisi = sgs.CreateViewAsSkill{
	name = "luashisi",
	n = 1,
	view_filter = function(self, selected, to_select)
		return not to_select:isKindOf("Slash") and to_select:isKindOf("BasicCard")
	end,
	view_as = function(self, cards)
		if #cards == 0 then return end 
		local huisheng = luashisiCard:clone()
		for _, c in ipairs(cards) do
			huisheng:addSubcard(c)
		end
		return huisheng
	end
}
luashisi2 = sgs.CreateTriggerSkill{
	name = "#luashisi",
	global = true,
	frequency = sgs.Skill_Compulsory,
	events = {sgs.DamageInflicted},
	on_trigger = function(self, event, player, data, room)
		local damage = data:toDamage()
		local loseSkill = player:getTag("luashisiL")
		if event == sgs.DamageInflicted and damage.to and loseSkill and loseSkill:toString() and loseSkill:toString() ~= ""
			and damage.to:objectName() == player:objectName() then
			loseSkill = loseSkill:toString()
			loseSkill = loseSkill:split("|")
			for _, name in ipairs(loseSkill) do
				if player:hasSkill(name) then
					room:detachSkillFromPlayer(player, name)
				end
			end 
			player:removeTag("luashisiL")
		end
	end
}
luasenyu = sgs.CreateTriggerSkill{
	name = "luasenyu",
	global = true,
	events = {sgs.EventPhaseStart},
	on_trigger = function(self, event, player, data, room)
		if event == sgs.EventPhaseStart then
			for _, p2 in sgs.qlist(room:findPlayersBySkillName(self:objectName())) do
				for _, p in sgs.qlist(room:getAllPlayers()) do
					if not p:hasSkill("luasenyur") then room:writeToConsole("luasenyu test" .. p:objectName()); room:attachSkillToPlayer(p, "luasenyur") end
				end
			end
		end
	end
}

takane:addSkill(luashisi)
takane:addSkill(luashisi2)
takane:addSkill(luashisi)
takane:addSkill(luasenyu)

sgs.LoadTranslationTable {
    ["pay16"] = "光风霁月", --注意这里每次要加逗号

    ["mike"] = "豪德寺三花", 
	["designer:mike"] = "Paysage",
    ["#mike"]= "大吉猫咪",
    ["luayinke"] = "引客",
    [":luayinke"] = "准备或结束阶段，你可以与一名其他角色交换一张手牌，若以此法交换的两张牌花色相同，则你标记该角色。你使用非装备牌指定自己为目标后，你可以视为对一名标记角色使用了此牌，或是对其造成一点伤害。",
    ["luayinke1"] = "视为对一名标记角色使用了此牌",
    ["luayinke2"] = "对其造成一点伤害",
    ["luazhaocai"] = "招财",
    [":luazhaocai"] = "你可以将三张不同花色的牌当【无中生有】使用。",
    ["@luayinke"] = "你可以发动“引客”",
    ["~luayinke"] = "选择要交换的一张手牌→选择一名角色→点击确定",
    ["@luayinkegive"] = "三花要求你给出一张手牌做交换，请选择一张手牌",

    ["tsukasa"] = "菅牧典",
	["designer:tsukasa"] = "Paysage",
    ["#tsukasa"]= "管狐",
    ["lualicai"] = "理财",
    [":lualicai"] = "其他角色出牌阶段开始时，你可以弃置一张牌，令其摸一张牌。弃牌阶段开始时若其手牌大于你，则需要交给你一张牌；若其手牌为全场最多，则你摸一张牌，其使用一张牌。若均不满足，则“理财”失效直到其下个回合开始。",
    ["@lualicai"] = "你可以发动“理财”",
    ["~lualicai"] = "选择要弃置的一张手牌→点击确定",
    ["@lualicai2"] = "请交给 菅牧典 一张牌",
    ["@lualicai3"] = "你可以使用一张牌",
    ["luaguansha"] = "管纱",
    [":luaguansha"] = "锁定技，当前回合角色手牌比你多的场合，你每回合受到的首次伤害-1。",
	
    ["takane"] = "山城高岭",
	["illustrator:takane"] = "民",
	["designer:takane"] = "Paysage",
    ["#takane"]= "深山商戶",
    ["luashisi"] = "市司", 
    ["luashisiGive"] = "请选择要获得技能的角色", 
    ["luashisi1"] = "抽选角色", 
    ["luashisi2"] = "分配技能", 
    [":luashisi"] = "出牌阶段，你可以弃置一张非【杀】基本牌，然后选择一项：①抽选一个记述有“出牌阶段限一次”的技能；"
		.. "②从你已抽选的技能中选一个交予一名角色（其下次受到伤害后失去该技能）。", 
	["luasenyu"] = "森域", 
    [":luasenyu"] = "每名角色于其出牌阶段限一次，其可以交给你一张非【杀】基本牌并摸一张牌。",
	
	["sannyo"] = "驹草山如",
	["illustrator:sannyo"] = "降旗原",
	["designer:sannyo"] = "Paysage",
    ["#sannyo"]= "山女郎",
    ["luayanmeng"] = "烟梦",
    ["luayanmeng2"] = "烟梦",
    [":luayanmeng"] = "锁定技，你的回合内：①群体锦囊以外，受到你造成伤害的角色回复一点体力，然后你展示其区域内的一张牌，令其获得并对一名角色使用之；②所有角色的【闪】均视为【杀】。",
    ["luayanmengX"] = "请指定 烟梦 使用牌的目标",

	["megumu"] = "饭纲丸龙",
	["illustrator:megumu"] = "hisona",
	["designer:megumu"] = "Paysage",
    ["#megumu"]= "光风霁月",
    ["luaqunxing"] = "群星", 
    [":luaqunxing"] = "准备阶段，你可以调整牌堆顶的X张牌的顺序，选其中任意张与手牌交换，交换后需展示之。若如此做，这回合你可以使用展示的牌，但不能使用手牌（X为你手牌数）。"
		.. "<br/> <font color=\"red\">暂时不能以此法使用【借刀杀人】</font> ",
	["luaqunxing-invoke"] = "你可以发动“群星”<br/> <b>操作提示</b>: 点击确定即可。 之后会弹出卡牌框让你对牌堆顶的牌进行排序。<br/>",
	["luaqunxingY-invoke"] = "<b>操作提示</b>: 点击确定即可。 之后会弹出卡牌框，你从这些牌中点击一张使用。<br/> 若你不想再看到这条提示，请点击取消。<br/> ",
	["@luaqunxing"] = "请选择要交换的手牌（可不选，不能点取消！）",
	["~luaqunxing"] = "选择要交换的手牌（先选的会置于牌堆较上方）→点击确定。<br/> 之后你于出牌阶段请再点击 群星 技能按钮来使用牌。",
    ["luaqunxingX"] = "请指定 你刚刚点选的那张卡牌 的使用目标",
    ["luahonggguang"] = "虹光", 
    ["luahonggguang2"] = "虹光", 
    [":luahonggguang"] = "一名角色于弃牌阶段以外弃牌后，其可于此回合结束时令你摸一张牌。锁定技，你始终跳过摸牌阶段。", 
    ["luadiaoling"] = "调令", 
    [":luadiaoling"] = "主公技，每轮限一次，你的准备阶段开始前，你可以令一名其他角色执行一个额外的准备阶段。", 
    ["luadiaoling-invoke"] = "你可以发动“调令”<br/> <b>操作提示</b>: 点击一名其他角色，再点击确定。<br/>", 
	
	["chimata"] = "天弓千亦",
	["illustrator:chimata"] = "久苍穹",
    ["#chimata"]= "集市之神",
	["designer:chimata"] = "Marisa",
    ["luashichang"] = "市场", 
    ["luashichang1"] = "你弃置一张牌然后摸一张牌", 
    ["luashichang2"] = "你弃置其一张牌，然后其摸一张牌", 
    [":luashichang"] = "一名角色出牌阶段开始时，你可以选择一项；①：你弃置一张牌然后摸一张牌；②你确认并弃置其一张牌，然后其摸一张牌（若弃置方块牌则改为摸两张）。",
    ["luahonghuanx"] = "虹环",
    [":luahonghuanx"] = "锁定技，被你弃置的牌移置你的武将牌上，以此法放置的牌总计不少于存活人数时，你须将这些牌至于牌堆顶，并视为你使用了一张【五谷丰登】。你始终跳过摸牌阶段与弃牌阶段。",
	
	["momoyo"] = "姬虫百百世",
	["illustrator:momoyo"] = "东方归言录官方",
    ["#momoyo"]= "吞天裂地",
	["designer:momoyo"] = "Paysage",
    ["lualiedi"] = "裂地",  
    [":lualiedi"] = "限定技，你可以指定一名攻击范围内的角色并弃置两张装备牌，依座次从其开始，直到有角色濒死为止，所有角色选择一项：①弃置2张手牌，令选项②的伤害+1。②受到你对其造成的1点伤害。",
	["@lualiedi"] = "弃置比上一名角色点数大的牌或者受到姬虫百百世对你造成的一点伤害。",  
    ["luafajue"] = "发掘", 
    ["luafajue-invoke"] = "你可以发动“发掘”<br/> <b>操作提示</b>: 选择一名角色→点击确定<br/>",  
    [":luafajue"] = "锁定技，你造成致命伤害时，你令一名角色增加一点体力。依此法每增加三点体力，你于此回合结束时获得/重置一个限定技。",
	
	["@luafajue1"] = "你可以弃牌，或者点击取消受到1点伤害",
	["@luafajue2"] = "你可以弃牌，或者点击取消受到2点伤害",
	["@luafajue3"] = "你可以弃牌，或者点击取消受到3点伤害",
	["@luafajue4"] = "你可以弃牌，或者点击取消受到4点伤害",
	["@luafajue5"] = "你可以弃牌，或者点击取消受到5点伤害",
	["@luafajue6"] = "你可以弃牌，或者点击取消受到6点伤害",
	["@luafajue7"] = "你可以弃牌，或者点击取消受到7点伤害",
	["@luafajue8"] = "你可以弃牌，或者点击取消受到8点伤害",
	["@luafajue9"] = "你可以弃牌，或者点击取消受到9点伤害",
	
	
	
	
}
return {extension_pay_aa}