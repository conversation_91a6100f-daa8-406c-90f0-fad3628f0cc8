
math.randomseed(tostring(os.time()):reverse():sub(1, 7)) --------设置时间种子

sgs.ai_skill_playerchosen.luazhuwu = function(self, targets)
	if self.player:getHp() == 1 then 
		return self.player
	end
	local friends = self.friends
	local all_players = sgs.QList2Table(self.room:getAlivePlayers())
	self:sort(all_players, "hp2")
	self:sort(friends, "defense")
	local zuiduo = true
	for _, ap in sgs.qlist(self.room:getOtherPlayers(self.player)) do
		if self.player:getHp() + 1 <= ap:getHp() then zuiduo = false end
	end
	if self.player:getHp() == self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt() and not zuiduo then return self.player end

	if self.player:getMark("@zhuwu") > 0 then
		return self.player
	end
	for _, friend in ipairs(friends) do
		if self:hasSkills(sgs.need_maxhp_skill, friend) then
			return friend
		end
	end
	if self.player:getHp() > self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt() and not zuiduo then return self.player end

	for _, friend in ipairs(friends) do
		if friend:getMark("@zhuwu") > 0 then return friend end
	end
	for _, enemy in ipairs(self.enemies) do
		if enemy:isChained() and self:isGoodChainTarget(enemy, self.player, sgs.DamageStruct_Fire, 1) then return enemy end
	end
 	if zuiduo and self.player:getHp() >= self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt() then
		if self:isFriend(all_players[2]) then return all_players[2] end
		if not self:isFriend(all_players[2]) and all_players[2]:getMark("@zhuwu") == 0 then return all_players[2] end
		end
	for _, friend in ipairs(friends) do
		return friend
	end
end
sgs.ai_skill_cardask["@luakuangwang"] = function(self, data)
	local target = data:toPlayer()
	self.room:writeToConsole("target test Name" .. target:getGeneralName())
	local n = self.player:getMark("luakuangwangai")
	local has_another_f = false
	local has_another_e = false
	local miyoi = self.room:findPlayerBySkillName("lualiangxiao")
	if n > 1 then
		local kp = target
		for i = 1, n - 1 do
			local np = kp:getNextAlive()
			if self:isFriend(np) then has_another_f = true end
			if self:isEnemy(np) then has_another_e = true end
			kp = np
		end
	end
	local function CanUse(card, targetX)
		if not self.player:isCardLimited(card, sgs.Card_MethodUse) and not self.room:isProhibited(self.player, targetX, card) then
			if card:isKindOf("TrickCard") and not self:hasTrickEffective(card, targetX, self.player) then
				return false
			end
			if card:isKindOf("Slash") and not self:slashIsEffective(card, targetX, self.player) then
				return false
			end
			return true
		end
	end
	if self:isFriend(target) then
		local bool_P = miyoi and miyoi:isAlive() and self:isFriend(miyoi) and target:getMark("lualiangxiaoA") ~= self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt() + 1
		for _, card in sgs.qlist(self.player:getHandcards()) do
			if card:isKindOf("ExNihilo") and (self:getOverflow(target) <= 0 or not has_another_f) and CanUse(card, target) then return "$" .. card:getEffectiveId() end
		end
		for _, card in sgs.qlist(self.player:getHandcards()) do
			if (card:isKindOf("Peach") or (card:isKindOf("GodSalvation") and self:godSalvationValue(card) <= 15))
					and target:isWounded() and not has_another_f and CanUse(card, target) then return "$" .. card:getEffectiveId() end
		end
		for _, card in sgs.qlist(self.player:getHandcards()) do
			if (card:isKindOf("Peach") or (card:isKindOf("GodSalvation") and self:godSalvationValue(card) <= 15))
					and target:getLostHp() >= 2 and CanUse(card, target) then return "$" .. card:getEffectiveId() end
		end
		for _, card in sgs.qlist(self.player:getHandcards()) do
			if card:isKindOf("Banquet") and CanUse(card, target) then return "$" .. card:getEffectiveId() end
		end
		for _, card in sgs.qlist(self.player:getHandcards()) do
			if card:isKindOf("EquipCard") and not self:getSameEquip(card, target)
					and self:evaluateArmor(card, target) < -4 and CanUse(card, target) then return "$" .. card:getEffectiveId() end
		end
		for _, card in sgs.qlist(self.player:getHandcards()) do
			if card:isKindOf("Analeptic") and self.player:getMark("drank") == 0 and CanUse(card, target)
				and bool_P then return "$" .. card:getEffectiveId() end
		end
		for _, card in sgs.qlist(self.player:getHandcards()) do
			if card:isKindOf("Analeptic") and self.player:getMark("drank") == 0 and CanUse(card, target)
					and (target:objectName() == self.player:objectName()) then
				for _, slash in ipairs(self:getCards("Slash")) do
					for _, enemy in ipairs(self.enemies) do
						if self.player:inMyAttackRange(enemy) and self:shouldUseAnaleptic(enemy, slash) and CanUse(slash, enemy) then
							return "$" .. card:getEffectiveId()
						end
					end
				end
			end
		end
	elseif self:isEnemy(target) then
		for _, card in sgs.qlist(self.player:getHandcards()) do
			if card:isKindOf("Hui") and CanUse(card, target) and (target:getHp() <= 2 or target:getHandcardNum() <= 2 or not has_another_f) then return "$" .. card:getEffectiveId() end
		end
		for _, card in sgs.qlist(self.player:getHandcards()) do
			if (card:isKindOf("SavageAssault") or card:isKindOf("ArcheryAttack")) and self:getAoeValue(card) <= 40 and CanUse(card, target) then return "$" .. card:getEffectiveId() end
		end
		for _, card in sgs.qlist(self.player:getHandcards()) do
			if card:isKindOf("Slash") and CanUse(card, target) and not self:getDamagedEffects(target, self.player, true)
				and not self:needToLoseHp(target, self.player, true) then return "$" .. card:getEffectiveId() end
		end
		for _, card in sgs.qlist(self.player:getHandcards()) do
			if card:isKindOf("Snatch") and self.player:distanceTo(target) > 1 then return "$" .. card:getEffectiveId() end
		end
	end
	return "."
end

sgs.ai_skill_invoke.luakuangwang = function(self, data)
	local n = self.player:getHp()
	if n > 2 then return true end 
end 


sgs.ai_skill_invoke.luashiji = function(self, data)
	local Remilia = self.room:getTag("luashijip"):toPlayer()
	
	if not Remilia then self.room:writeToConsole("error");return false end 
	local ids = Remilia:getRoom():getTag("luashiji"):toString()
	ids = ids:split("+")	
	if not self:isFriend(Remilia) then
		for _, id in ipairs(ids) do
			local card = sgs.Sanguosha:getCard(id)
			if card:isKindOf("Hui") then return true end
		end
		return false
	end
				
	local hasRed
	local allcards = sgs.QList2Table(self.player:getCards("he")) 
	for _,acard in ipairs(allcards) do
		if acard:isRed() then hasRed = true end 
	end 

	local hasBlack
    allcards = sgs.QList2Table(self.player:getCards("he"))
	for _,acard in ipairs(allcards) do
		if acard:isBlack() then hasBlack = true end 
	end 
	
	local skill = (self.player:getMark("@qishured") > 0) or ((self.player:getMark("@qishured") == 0) and (self.player:getMark("@qishublack") == 0))


	if (self.player:getHp() > 1 and self.player:hasSkill("luashijie"))
		or (self.player:getHp() == 1 and (self.player:hasSkill("luaqishu") and (self.player:getMark("@qishublack") > 0)) and hasBlack) then
		if #ids > 2 then return true end 
		for _, id in ipairs(ids) do
			local card = sgs.Sanguosha:getCard(id)
			for _, skillX in sgs.qlist(Remilia:getVisibleSkillList(true)) do
				local callback = sgs.ai_cardneed[skillX:objectName()]
				if type(callback) == "function" and sgs.ai_cardneed[skillX:objectName()](Remilia, card, self) then
					return true
				end
			end
			if (isCard("Peach", card, Remilia) and Remilia:objectName() ~= self.player:objectName()) or (isCard("Analeptic", card, Remilia) and Remilia:getHp() == 1)
				or isCard("ExNihilo", card, Remilia) then
				return true 
			end 
			if (not self:hasEightDiagramEffect(Remilia) and not Remilia:hasArmorEffect("renwang_shield")) and (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield")) then 
				return true 
			end
			if card:isKindOf("Wanbaochui") then return true end
			if (isCard("Slash", card, Remilia) and Remilia:hasSkills(sgs.need_slash_skill)) then return true end
			if (Remilia:hasSkill("Luashenqiang") or Remilia:hasSkill("luahakurei")) and isCard("Slash", card, Remilia) and card:isRed() then return true end 
			if (isCard("Dismantlement", card, Remilia) or isCard("Snatch", card, Remilia) or isCard("Duel", card, Remilia)) then
				return true 
			end 
			if card:isKindOf("AOE") and self:getAoeValue(card) >= 25 then 
				return true 
			end 
		end 		
	end 
	
end 

sgs.ai_skill_invoke.luaxiaosa = function(self, data)
	return true 
end 

luaqishu_skill = {}
luaqishu_skill.name = "luaqishu"
table.insert(sgs.ai_skills,luaqishu_skill)
luaqishu_skill.getTurnUseCard = function(self)
	local skill = (self.player:getMark("@qishured") > 0) or ((self.player:getMark("@qishured") == 0) and (self.player:getMark("@qishublack") == 0))
	local cards = self.player:getCards("he")
	cards = sgs.QList2Table(cards)
		
	if skill then 
		local diamond_card

		self:sortByUseValue(cards,true)

		local disCrossbow = false
		if self:getCardsNum("Slash") < 2 or self.player:hasSkill("paoxiao") then
			disCrossbow = true
		end

		
		for _,card in ipairs(cards)  do
			local useAll = false
			local slash_0 = sgs.Sanguosha:cloneCard("slash", card:getSuit(), card:getNumber())
			slash_0:addSubcard(card)
			slash_0:setSkillName("LuaYuanzu")		
			if self:canKillEnermyAtOnce(false, slash_0) then useAll = true end 
		
			local heavy_dmg = false 
			local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
			self:useBasicCard(slash_0, dummy_use)
			if not dummy_use.to:isEmpty() then
				for _, playerX in sgs.qlist(dummy_use.to) do
					if self:hasHeavySlashDamage(self.player, slash_0, playerX) then heavy_dmg = true end 
				end 
			end 	
			local snatch = sgs.Sanguosha:cloneCard("snatch")
			local dismantlement = sgs.Sanguosha:cloneCard("slash")
			local val1 = self:getUseValue(snatch)
			local val0 = self:getUseValue(dismantlement) + 1
			local bool_0 = (self:getUseValue(card) <= val0) or (heavy_dmg and (self:getUseValue(card) <= val1))
			
			if card:isRed()
			and (((not self:OverFlowPeach(card)) and not isCard("ExNihilo", card, self.player)) or useAll)
			and (not isCard("Crossbow", card, self.player) and not disCrossbow)
			and (bool_0 or inclusive or sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_Residue, self.player, sgs.Sanguosha:cloneCard("slash")) > 0) then
				diamond_card = card
				break
			end
		end


		if not diamond_card then return nil end
		local number = diamond_card:getNumberString()
		local card_id = diamond_card:getEffectiveId()
		local card_str = ("slash:luaqishu[spade:%s]=%d"):format(number, card_id)
		local slash = sgs.Card_Parse(card_str)
		assert(slash)

		return slash
	else
		local card

		self:sortByUseValue(cards,true)

		for _,acard in ipairs(cards)  do
			if acard:isBlack() then
				card = acard
				break
			end
		end

		if not card then return nil end
		local number = card:getNumberString()
		local card_id = card:getEffectiveId()
		local card_str = ("analeptic:luaqishu[spade:%s]=%d"):format(number, card_id)
		local analeptic = sgs.Card_Parse(card_str)

		if sgs.Analeptic_IsAvailable(self.player, analeptic) then
			assert(analeptic)
			return analeptic
		end
	end 
end

sgs.ai_view_as.luaqishu = function(card, player, card_place, class_name)
	local suit = card:getSuitString()
	local number = card:getNumberString()
	local card_id = card:getEffectiveId()
	if player:getHp() > 1 or card_place == sgs.Player_PlaceSpecial then return end
	
	local skill = (player:getMark("@qishured") > 0) or ((player:getMark("@qishured") == 0) and (player:getMark("@qishublack") == 0))
	local hasRed
	local allcards = sgs.QList2Table(player:getCards("he")) 
	for _,acard in ipairs(allcards) do
		if acard:isRed() then hasRed = true end 
	end 
	
	local room = player:getRoom()
	local cCount = 1 --self:getEnemyNumBySeat(room:getCurrent(), player)
	if room:getCurrent():objectName() == player:objectName() then cCount = 0 end 
	if card:isRed() and skill then 
		local abc = string.lower(class_name)
		if (abc == "slash") or (abc == "jink") then 
			return (abc .. ":luaqishu[%s:%s]=%d"):format(suit, number, card_id) 
		end 
	elseif card:isBlack() and not skill then 
		local abc = string.lower(class_name)
		if hasRed and (math.random() > 0.2*cCount) and abc == "nullification" then 
			return (abc .. ":luaqishu[%s:%s]=%d"):format(suit, number, card_id) 
		end 
		if abc == "analeptic" then 
			return ("analeptic:luaqishu[%s:%s]=%d"):format(suit, number, card_id)
		end 
	end 
end

sgs.ai_skill_invoke.luajingdan = sgs.ai_skill_invoke.liegong  --如何决定技能是否发动的一个实例

local function Flan_Check_R(self)
	local allcardX = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(allcardX)
	local allcards = {}
	for _, card in ipairs(allcardX) do
		if not self.player:isCardLimited(card, sgs.Card_MethodDiscard) then
			table.insert(allcards, card)
		end
	end
	for _, enemy in ipairs(self.enemies) do
		if self:damageIsEffective(enemy, sgs.DamageStruct_Normal, self.player) and self:isWeak(enemy)
				and not (self:getDamagedEffects(enemy, self.player) or self:needToLoseHp(enemy, self.player, nil, false)) then
			return allcards[1]
		end
	end
	local function Check_R(card)
		local nazrin = self.room:findPlayerBySkillName("luatanbao")
		if nazrin and nazrin:isAlive() and self:isFriend(nazrin) then
			if card:hasFlag("prelingbai") then return false end
		end
		if card:isKindOf("EquipCard") then --or card:isKindOf("DefensiveHorse")
			if card:isKindOf("OffensiveHorse") and card:objectName() ~= "shanghai" then
				return true
			end
			if card:isKindOf("DefensiveHorse") and card:objectName() ~= "hongrai" then
				return true
			end
			if card:isKindOf("Armor") and self:needToThrowArmor() then return true end
			if (card:isKindOf("EightDiagram") or card:isKindOf("RenwangShield") or card:isKindOf("Tengu")) then
				local bool_3 = (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip)
						or ((self.room:getCardPlace(card:getId()) ~= sgs.Player_PlaceEquip) and not self:hasEightDiagramEffect(self.player) and not self.player:hasArmorEffect("renwang_shield")) or self:needToThrowArmor(target)
				if not bool_3 then return true end
			end
			if card:isKindOf("Weapon") then
				if (self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip) then return false end
				local dummy_use = {isDummy = true}
				self:useEquipCard(card, dummy_use)
				if not dummy_use.card then return true end
				if dummy_use.card and self.player:getWeapon() then return true end
			end
		end
		if card:isKindOf("AOE") and self:getAoeValue(card) > 35 then return false end
		if card:isKindOf("GodSalvation") and self:godSalvationValue(card) > 10 then return false end
		if card:isKindOf("AmazingGrace") and self:getAmazingGraceValue(card) > 2 then return false end
		if not card:isKindOf("Dismantlement") and self:ThreeCheck(card) then return false end
		if card:isKindOf("ExNihilo") then return false end
		if card:isKindOf("Slash") then return false end
		if card:isKindOf("Analeptic") and self:getOverflow() > 0 then return false end
		return true
	end
	for _, card in ipairs(allcards) do
		if Check_R(card) then return card end
	end
end

sgs.ai_skill_cardask["@luajingdan"] = function(self, data, pattern, target)
	local danmu = sgs.Sanguosha:cloneCard("yuzhi", sgs.Card_NoSuit, 0)
	local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
	self:useTrickCard(danmu, dummy_use)
	if dummy_use.card and not dummy_use.to:isEmpty() then
		local card = Flan_Check_R(self)
		if card then return "$" .. card:getEffectiveId() end
	end
	return "."
end

sgs.ai_skill_use["@@luahuimie"] = function(self, data, method)
	self:sort(self.enemies,"defense")
	local danmu = sgs.Sanguosha:cloneCard("yuzhi", sgs.Card_NoSuit, 0)
	local targets = {}
	for _, enemy in ipairs(self.enemies) do
		if self.player:inMyAttackRange(enemy,  - sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_DistanceLimit, self.player, danmu))
				and #targets < 1 + sgs.Sanguosha:correctCardTarget(sgs.TargetModSkill_ExtraTarget, self.player, danmu)
			and self:damageIsEffective(enemy, sgs.DamageStruct_Normal, enemy) then
			table.insert(targets, enemy:objectName())
		end
	end
	if #targets > 0 then return "#luahuimie:.:->" .. table.concat(targets, "+") end
end

sgs.ai_skill_discard.luahuimie = function(self, discard_num, min_num, optional, include_equip)
	local card = Flan_Check_R(self)
	if card then return card:getEffectiveId() end
	return "."
end



sgs.ai_choicemade_filter.skillInvoke.luashiji = function(self, player, promptlist)	
	if promptlist[3] == "yes" then
		local Remilia = self.room:getTag("luashijip"):toPlayer()
		self.room:writeToConsole(Remilia:getGeneralName())
		sgs.updateIntention(player, Remilia, -30)
	end 
end

sgs.ai_skill_choice.luahouhu = function(self, choices, data)
	return "forbid_houhu:no"
end 
sgs.ai_skill_invoke.luahouhu = function(self, data)
	local damage = data:toDamage()
	local target = damage.from
	if self.player:getHp() == 1 then return false end 
	local q = self:getEnemyNumBySeat(self.room:getCurrent(), self.player, self.player, true)*0.1 + 0.3
	local r = self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt()
	r = r - math.floor(r/6)*6
	if self.player:getHp() == r then return false end 
	if self.player:getHp() > r and self.player:getHp() == 2 then return false end 		
	if self.player:getHp() > r and self.player:getHp() == 3 and math.random() < q then return false end 		

	local notA = false
	for _, to in ipairs(self.enemies) do
		if to:hasSkills("faen|Luafadeng") then notA = true end
	end
	if target then 
		if self:isEnemy(target) and not target:faceUp() and not notA then return false end
		if self:isEnemy(target) and target:hasSkill("Luaxianzhe") then return false end
		if self:isFriend(target) and target:faceUp() then return false end
		if not self:isFriend(target) and not self:isEnemy(target) then return false end 
		return true
	end 
end 
sgs.ai_choicemade_filter.skillInvoke.luahouhu = function(self, player, promptlist)	
	if promptlist[3] == "yes" then
		local Remilia = self.room:getTag("luahouhup"):toPlayer()
		self.room:writeToConsole(Remilia:getGeneralName())
		if Remilia:faceUp() then 
			sgs.updateIntention(player, Remilia, 50)
		else
			sgs.updateIntention(player, Remilia, -50)
		end 
	end 
end

sgs.ai_skill_playerchosen.luamiyi = function(self, targets) 
	local friends = self.friends_noself
	self:sort(friends, "defense")	
	
	local enemies = self.enemies
	self:sort(enemies, "handcard2")
	
	local r = self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt()
	r = r - math.floor(r/6)*6
	for _, friend in ipairs(friends) do
		local p = r - friend:getHandcardNum() 
		if p > 2 and targets:contains(friend) then 
			return p
		end 
	end 
	for _, enemy in ipairs(enemies) do
		local p = r - enemy:getHandcardNum() 
		if p < -2 and targets:contains(enemy) then 
			return p
		end 
	end 	
	self.room:writeToConsole("摩多罗测试2")
	local q = r - self.player:getHandcardNum() 
	if q > 0 and targets:contains(self.player) then return self.player end 
	
	for _, friend in ipairs(friends) do
		local p = r - friend:getHandcardNum() 
		if p > 0 and targets:contains(friend) then 
			return p
		end 
	end 
	for _, enemy in ipairs(enemies) do
		local p = r - enemy:getHandcardNum() 
		if p < 0 and targets:contains(enemy) then 
			return p
		end 
	end 	
	
	return nil 
end 

sgs.ai_skill_cardask["@@luamiyi"] = function(self, data, pattern, target)
	local wq = self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt()
	local r = wq - math.floor(wq/6)*6	

	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByKeepValue(cards)
	self.room:writeToConsole("摩多罗测试")
	if self:getKeepValue(cards[1]) < 4 and self.player:getHp() == r then 
		return "#luamiyi:".. cards[1]:getId() ..":->".. self.player:objectName()	
	end 

	local mai_satono = self.room:findPlayerBySkillName("luajiawei")
	if mai_satono and self:isFriend(mai_satono) then 
		if (mai_satono:getHp() == wq or mai_satono:getHp() == wq - 1) 
			and not (cards[1]:isKindOf("ExNihilo") or cards[1]:isKindOf("Snatch") or cards[1]:isKindOf("Indulgence")
				or cards[1]:isKindOf("RenwangShield") or cards[1]:isKindOf("EightDiagram")) then 
			return "#luamiyi:".. cards[1]:getId() ..":->".. mai_satono:objectName()	
		end 
	end
end 

sgs.ai_card_intention.luamiyi = -30
sgs.ai_skill_turnUse["luamiyi"] = function(self, turnUse)
	if self.player:hasSkill("luamiyi") then
		local r = self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt()
		r = r - math.floor(r/6)*6
		if (r == self.player:getHp() - 1) and (r == self.player:getHandcardNum()) and self:getOverflow() <= 0 then
			local TurnUse = {}
			for  _, card in ipairs(turnUse) do
				if card:isKindOf("Peach") or card:isKindOf("ExNihilo")
						or card:isKindOf("Snatch") then table.insert(TurnUse, card);return TurnUse end
			end
			if (not self:canKillEnermyAtOnce(turnUse)) then
				return TurnUse
			end
		end
	end
end

sgs.ai_skill_use["@@lualueying"] = function(self, data, pattern, targetF)
	local handcard = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(handcard)
	local function suitable(card_0)
		for _, card in ipairs(handcard) do
			if ((card_0:objectName() == card:objectName()) or (card_0:isKindOf("Slash") and card:isKindOf("Slash")))
				and card:getId() ~= card_0:getId() then
				return card
			end
		end
		return false
	end

	local enemies = self.enemies
	self:sort(enemies, "defense")
	local target
	local slash = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
	slash:setSkillName("lualueying")
	for _, enemy in ipairs(enemies) do
		if self.player:canSlash(enemy, slash, true) and self:slashIsEffective(slash, enemy, self.player)
			and not self:slashProhibit(slash, enemy) and not (enemy:hasSkill("anxian") and not enemy:isKongcheng()) then target = enemy;break
		end
	end
	if target then
		self.room:writeToConsole("斯卡蒂ai测试2")
		for _, card in ipairs(handcard) do
			self.room:writeToConsole("斯卡蒂ai测试3")
			if suitable(card) then
				self.room:writeToConsole("斯卡蒂ai测试4")
				if (self:getOverflow() > 0) then return "#lualueying:" .. card:getId() .. "+" .. suitable(card):getId() .. ":->".. target:objectName() end
				if not card:isKindOf("Peach") and not card:isKindOf("ExNihilo")
						and not ((self.player:getHp() < 3) and (getCardsNum("Jink", self.player) < 3) and card:isKindOf("Jink")) then
					return "#lualueying:" .. card:getId() .. "+" .. suitable(card):getId() .. ":->".. target:objectName()
				end
			end
		end
	end
	return "."
end

local luelangchao_skill = {}
luelangchao_skill.name = "luelangchao"
table.insert(sgs.ai_skills, luelangchao_skill)

function useluelangchaoCard(self)
	local cards = sgs.QList2Table(self.player:getCards("he"))
	self:sortByUseValue(cards, true)
	for _, card in ipairs(cards) do
		if card:isBlack() then
			if (self.player:hasArmorEffect("silver_lion") and self.player:isWounded()) then
				return self.player:getArmor()
			end
			if card:isKindOf("EquipCard") and self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand then
				if self:getSameEquip(card, self.player) and self:getSameEquip(card, self.player):isBlack() then
					return self:getSameEquip(card, self.player)
				end
			end
			if card:isKindOf("OffensiveHorse") and self.room:getCardPlace(card:getId()) == sgs.Player_PlaceHand
					and not self:WeaponUse(card, nil, 0, true) then return card end
			if card:isKindOf("OffensiveHorse") and self.room:getCardPlace(card:getId()) == sgs.Player_PlaceEquip then
				for _,enemy in ipairs(self.enemies) do
					if self.player:distanceTo(enemy) == 1 and not enemy:getDefensiveHorse()
							and not enemy:hasSkill("feiying") then
						return card
					end
				end
			end
			if card:isKindOf("SavageAssault") and (self:getAoeValue(card) <= 30) then
				return card
			end
			if card:isKindOf("TrickCard") then
				local dummy_use = { isDummy = true, to = sgs.SPlayerList() }
				self:useTrickCard(card, dummy_use)
				if not dummy_use.card then
					return card
				end
			end
		end
	end
	return
end
luelangchao_skill.getTurnUseCard = function(self)
	if not self.player:hasUsed("#luelangchao") and not self.player:isNude()  then
		return sgs.Card_Parse("#luelangchao:.:")
	end
end
sgs.ai_skill_use_func["#luelangchao"] = function(card, use, self)
	local toUse = useluelangchaoCard(self)
	if toUse then
		use.card = sgs.Card_Parse("#luelangchao:" .. toUse:getEffectiveId() ..":")
		if use.to then
			use.to = sgs.SPlayerList()
			return
		end
	end
end

sgs.ai_use_priority.luelangchao = sgs.ai_use_priority.ExNihilo - 1
sgs.ai_card_intention.luatanxi = 20
sgs.ai_skill_cardask["luaqijinr"] = function(self, data)
	self.room:writeToConsole("luaqijinr test")
	local grani
	for _, ap in sgs.qlist(self.room:getAlivePlayers()) do
		if ap:hasFlag("SlashAssignee") then
			grani = ap
			break
		end

	end
	if not self.player:hasSkills("tieji|liegong|kofliegong|xiemu|lieren|badao|Luayuelong|luahakurei|luajingdan|tieji|luahuaifei") then
		local count = 0
		local cards = sgs.QList2Table(self.player:getHandcards())
		for _, cc in ipairs(cards) do
			if cc:isKindOf("Slash") then
				count = count + 1
			end
		end
		if count <= 1 and not grani:isWounded() then return "." end
	end
	if not self:isFriend(grani) then
		local slashes = sgs.QList2Table(self.player:getHandcards())
		self:sortByKeepValue(slashes)
		for _, slash in ipairs(slashes) do
			if slash:isKindOf("Slash") and (not self:slashProhibit(slash, grani)) and self:slashIsEffective(slash, grani) then return slash:toString() end
		end
		for _, slash in ipairs(slashes) do
			if slash:isKindOf("Slash") then return slash:toString() end
		end
	end

	return "."
end

sgs.ai_aoe_value.luaqijin = function(self, card, to, from, sj_num)
	if (card:isKindOf("ArcheryAttack") or card:isKindOf("SavageAssault")) then
		local p = self:getEnemyNumBySeat(self.player, to, to)
		if self:isEnemy(to) and self.player:getMark("lualianpo") > 0 then p = p + 1 end
		if (((p == 1) and self:isEnemy(to)) or (p == 0)) and to:getHp() > 1 then
			return 40
		end
	end
	return 0
end
sgs.ai_skill_invoke.luaqijina = function(self, data)
	return true
end
sgs.ai_skill_invoke.luaqijinb = function(self, data)
	if self:willSkipPlayPhase() and self:getOverflow() >= -1 then
		return false
	end
	return true
end

sgs.ai_skill_use["@@luatanxi"] = function(self, prompt)
	local slash = sgs.Sanguosha:cloneCard("slash")

	local function targetC()
		local enemies = self.enemies
		self:sort(enemies, "handcard2")

		local function func2(enemy)
			local count = 0
			local cards = sgs.QList2Table(enemy:getHandcards())
			local flag = string.format("%s_%s_%s", "visible", self.player:objectName(), enemy:objectName())
			for _, cc in ipairs(cards) do
				if (cc:hasFlag("visible") or cc:hasFlag(flag)) and (cc:isKindOf("Slash")) then
					count = count + 1
				end
			end
			return count
		end

		local function sorty(a,b)
			return func2(a) > func2(b)
		end

		table.sort(enemies, sorty)

		for _, enemy in ipairs(enemies) do
			local x = self:AtomDamageCount2(self.player, enemy, nil, slash)
			if (x < self.player:getHp() or ((self:getCardsNum("Jink") > 0) and not enemy:hasWeapon("axe")))
					and not enemy:hasSkills("tieji|liegong|kofliegong|xiemu|lieren|badao|Luayuelong|luahakurei|luajingdan|tieji|luahuaifei") then
				return enemy
			end
		end
	end

	local target = targetC()
	if target then
		return "#luatanxi:.:->" .. target:objectName()
	end
end

local luashenjun_skill = {}
luashenjun_skill.name = "luashenjun"
table.insert(sgs.ai_skills, luashenjun_skill)
luashenjun_skill.getTurnUseCard = function(self, inclusive)
	if self.player:hasUsed("#luashenjun") then return end
	if self.player:isWounded() then return end
	if self.player:getHp() == 1 then return end

	return sgs.Card_Parse("#luashenjun:.:")
end

sgs.ai_skill_use_func["#luashenjun"] = function(cardX, use, self)
	local cards = sgs.QList2Table(self.player:getHandcards())
	local num = self.player:getHp() - 1
	self:sortByUseValue(cards)

	local function FoRbid(playerX, cardS)
		local dummy = sgs.Sanguosha:cloneCard(cardS:objectName(), sgs.Card_NoSuit, 0)
		if self.room:isProhibited(self.player, playerX, dummy) then return true end
		return false
	end
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:isKindOf("Hui") then
			if #self.enemies > 0 then
				use.card = sgs.Card_Parse("#luashenjun:".. card:getId() ..":" )
				if use.to then
					for _, friend in ipairs(self.enemies) do
						if not FoRbid(friend, card) then
							use.to:append(friend)
							if use.to:length() >= num then break end
						end
					end
				end
			end
		end
	end
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:isKindOf("ExNihilo") and #self.friends_noself > 0 then
			use.card = sgs.Card_Parse("#luashenjun:".. card:getId() ..":" )
			if use.to then
				for _, friend in ipairs(self.friends) do
					if not FoRbid(friend, card) then
						use.to:append(friend)
						if use.to:length() >= num then break end
					end
				end
			end
			return
		end
		if card:isKindOf("Peach") or card:isKindOf("GodSalvation") then
			local count = 0
			for _, friend in ipairs(self.friends) do
				if friend:isWounded() and not FoRbid(friend, card) then count = count + 1 end
			end
			if count > math.min(1, num) then
				use.card = sgs.Card_Parse("#luashenjun:".. card:getId() ..":" )
				if use.to then
					for _, friend in ipairs(self.friends) do
						if friend:isWounded() and not FoRbid(friend, card) then use.to:append(friend) end
						if use.to:length() >= num then break end
					end
				end
				return
			end
		end
		if card:isKindOf("AmazingGrace") then
			local count = 0
			for _, friend in ipairs(self.friends) do
				if not FoRbid(friend, card) then count = count + 1 end
			end
			if count > math.min(2, num) then
				use.card = sgs.Card_Parse("#luashenjun:".. card:getId() ..":" )
				if use.to then
					for _, friend in ipairs(self.friends) do
						if not FoRbid(friend, card) then use.to:append(friend) end
						if use.to:length() >= num then break end
					end
				end
				return
			end
		end
		if card:isKindOf("Duel") then
			local dummy = sgs.Sanguosha:cloneCard("duel", sgs.Card_NoSuit, 0)
			local dummy_use = { isDummy = true, extra_target = num, to = sgs.SPlayerList() }
			self:useTrickCard(dummy, dummy_use)
			if dummy_use.card and dummy_use.to:length() > 1 then
				use.card = sgs.Card_Parse("#luashenjun:".. card:getId() ..":" )
				if use.to then
					use.to = dummy_use.to
				end
				return
			end
		end
		if card:isKindOf("Dismantlement") or card:isKindOf("Snatch") then
			local dummy = sgs.Sanguosha:cloneCard("dismantlement", sgs.Card_NoSuit, 0)
			local dummy_use = { isDummy = true, extra_target = num, to = sgs.SPlayerList() }
			self:useTrickCard(dummy, dummy_use)
			if dummy_use.card and dummy_use.to:length() > 3 then
				use.card = sgs.Card_Parse("#luashenjun:".. card:getId() ..":" )
				if use.to then
					use.to = dummy_use.to
				end
				return
			end
		end
		if card:isKindOf("Slash") then
			local count = 0
			local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			for _, enemy in ipairs(self.enemies) do
				if not FoRbid(enemy, card) and self.player:canSlash(enemy, dummy, false) and self:slashIsEffective(dummy, enemy, self.player)
						and not self:slashProhibit(dummy, enemy) and not (enemy:hasSkill("anxian") and not enemy:isKongcheng()) then count = count + 1 end
			end
			if count > 2 then
				self.room:writeToConsole("shenjun test2")
				use.card = sgs.Card_Parse("#luashenjun:".. card:getId() ..":" )
				if use.to then
					for _, enemy in ipairs(self.enemies) do
						if not FoRbid(enemy, card) and self.player:canSlash(enemy, dummy, false) and self:slashIsEffective(dummy, enemy, self.player)
								and not self:slashProhibit(dummy, enemy) and not (enemy:hasSkill("anxian") and not enemy:isKongcheng()) then use.to:append(enemy) end
						if use.to:length() >= num then break end
					end
				end
				return
			end
		end
		if card:isKindOf("SavageAssault") or card:isKindOf("ReligionBattle") then
			local count = 0
			for _, enemy in ipairs(self.enemies) do
				if not FoRbid(enemy, card) then count = count + 1 end
			end
			if count > 1 then
				use.card = sgs.Card_Parse("#luashenjun:".. card:getId() ..":" )
				if use.to then
					for _, enemy in ipairs(self.enemies) do
						if not FoRbid(enemy, card) then use.to:append(enemy) end
						if use.to:length() >= num then break end
					end
				end
				return
			end
		end
		if card:isKindOf("ArcheryAttack") then
			local count = 0
			for _, enemy in ipairs(self.enemies) do
				if not FoRbid(enemy, card) then count = count + 1 end
			end
			if count > 2 then
				use.card = sgs.Card_Parse("#luashenjun:".. card:getId() ..":" )
				if use.to then
					for _, enemy in ipairs(self.enemies) do
						if not FoRbid(enemy, card) then use.to:append(enemy) end
						if use.to:length() >= num then break end
					end
				end
				return
			end
		end
	end
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:isKindOf("AmazingGrace") then
			local count = 0
			for _, friend in ipairs(self.friends) do
				if not FoRbid(friend, card) then count = count + 1 end
			end
			if count > 1 then
				use.card = sgs.Card_Parse("#luashenjun:".. card:getId() ..":" )
				if use.to then
					for _, friend in ipairs(self.friends) do
						if not FoRbid(friend, card) then use.to:append(friend) end
						if use.to:length() >= num then break end
					end
				end
				return
			end
		end
		if card:isKindOf("Dismantlement") then
			local dummy = sgs.Sanguosha:cloneCard("dismantlement", sgs.Card_NoSuit, 0)
			local dummy_use = { isDummy = true, extra_target = num, to = sgs.SPlayerList() }
			self:useTrickCard(dummy, dummy_use)
			if dummy_use.card and dummy_use.to:length() > 1 then
				use.card = sgs.Card_Parse("#luashenjun:".. card:getId() ..":" )
				if use.to then
					use.to = dummy_use.to
				end
				return
			end
		end
		if card:isKindOf("Slash") then
			local count = 0
			local dummy = sgs.Sanguosha:cloneCard("slash", sgs.Card_NoSuit, 0)
			for _, enemy in ipairs(self.enemies) do
				if not FoRbid(enemy, card) and self.player:canSlash(enemy, dummy, false) and self:slashIsEffective(dummy, enemy, self.player)
					and not self:slashProhibit(dummy, enemy) and not (enemy:hasSkill("anxian") and not enemy:isKongcheng()) then count = count + 1 end
			end
			if count > 1 then
				self.room:writeToConsole("shenjun test2")
				use.card = sgs.Card_Parse("#luashenjun:".. card:getId() ..":" )
				if use.to then
					for _, enemy in ipairs(self.enemies) do
						if not FoRbid(enemy, card) and self.player:canSlash(enemy, dummy, false) and self:slashIsEffective(dummy, enemy, self.player)
							and not self:slashProhibit(dummy, enemy) and not (enemy:hasSkill("anxian") and not enemy:isKongcheng()) then use.to:append(enemy) end
						if use.to:length() >= num then break end
					end
				end
				return
			end
		end
		if card:isKindOf("SavageAssault") then
			local count = 0
			for _, enemy in ipairs(self.enemies) do
				if not FoRbid(enemy, card) then count = count + 1 end
			end
			if count > 1 then
				use.card = sgs.Card_Parse("#luashenjun:".. card:getId() ..":" )
				if use.to then
					for _, enemy in ipairs(self.enemies) do
						if not FoRbid(enemy, card) then use.to:append(enemy) end
						if use.to:length() >= num then break end
					end
				end
				return
			end
		end
		if card:isKindOf("ArcheryAttack") then
			local count = 0
			for _, enemy in ipairs(self.enemies) do
				if not FoRbid(enemy, card) then count = count + 1 end
			end
			if count > 1 then
				use.card = sgs.Card_Parse("#luashenjun:".. card:getId() ..":" )
				if use.to then
					for _, enemy in ipairs(self.enemies) do
						if not FoRbid(enemy, card) then use.to:append(enemy) end
						if use.to:length() >= num then break end
					end
				end
				return
			end
		end
	end
end

sgs.ai_use_priority.luashenjun = 12

sgs.ai_skill_playerchosen.luamingming = function(self, targets)
	for _, friend in ipairs(self.friends) do
		if targets:contains(friend) then return friend end
	end
end

local function LUCID(self, card_0)
	for _, card in sgs.list(self.player:getHandcards()) do
		if card:objectName() == card_0:objectName() and card:getId() ~= card_0:getId() then
			return card
		end
	end
	return false
end
local luajiance_skill = {}
luajiance_skill.name = "luajiance"
table.insert(sgs.ai_skills,luajiance_skill)
luajiance_skill.getTurnUseCard = function(self)
	if self.player:hasUsed("#luajiance") then return end
	for _, card in sgs.list(self.player:getHandcards()) do
		if LUCID(self, card) then return sgs.Card_Parse("#luajiance:.:") end
	end
end
local function luajianceSubcard(self)
	local handcard = sgs.QList2Table(self.player:getHandcards())
	self:sortByKeepValue(handcard)

	local nyasama = self.room:findPlayerBySkillName("lualianmeng")
	if nyasama and nyasama:isAlive() and self:isFriend(nyasama) then
		local suit
		if nyasama:getMark("@lianmengheart") > 0 then
			suit = sgs.Card_Heart
		elseif nyasama:getMark("@lianmengdiamond") > 0 then
			suit = sgs.Card_Diamond
		elseif nyasama:getMark("@lianmengclub") > 0 then
			suit = sgs.Card_Club
		elseif nyasama:getMark("@lianmengspade") > 0 then
			suit = sgs.Card_Spade
		end
		if suit then
			for _, cardA in ipairs(handcard) do
				if cardA:getSuit() == suit and LUCID(self, cardA) then return cardA, LUCID(self, cardA) end
			end
		end
	end

	for _, cardA in ipairs(handcard) do
		if LUCID(self, cardA) then return cardA, LUCID(self, cardA) end
	end
end
local function ABCDE(self, cardA, targets)
	local count = 0
	for _, enemy in ipairs(targets) do
		if not self.room:isProhibited(self.player, enemy, cardA) and not self.player:isCardLimited(cardA, sgs.Card_MethodUse)
				and self:hasTrickEffective(cardA, enemy, self.player) then
			count = count + 1
		end
	end
	return count
end
local function usewhatcard2(self, weiji, weiji2)
	if weiji2 and ABCDE(self, sgs.Sanguosha:cloneCard("savage_assault"), self.friends) > 1
		and #self.enemies > 1 then
		return "savage_assault"
	end
	if weiji and #self.friends > 1 then
		local count = 0
		for _, friend in ipairs(self.friends) do
			if friend:isWounded() then
				count = count + 1
			end
		end
		if count > 1 then
			return "god_salvation"
		end
	end

	local count2 = 0
	for _, friend in ipairs(self.friends) do
		if not friend:containsTrick("banquet") then
			count2 = count2 + 1
		end
	end

	--if self.player:getLostHp() < 2 and not (self.player:getLostHp() == 1 and self:getCardsNum("Peach") < 3) then

		if self:getOverflow() > 2 and #self.friends_noself > 1 then
			return "ex_nihilo"
		end

		if ABCDE(self, sgs.Sanguosha:cloneCard("dismantlement"), self.friends) > 1 then
			local snatch = sgs.Sanguosha:cloneCard("dismantlement")
			snatch:setSkillName("luajiance")
			local dummy_use = { isDummy = true , to = sgs.SPlayerList()}
			self:useTrickCard(snatch, dummy_use)
			if dummy_use.card and dummy_use.to:length() > 0 then
				return "snatch"
			end

		end


		if self:getCardsNum("Peach") <= 3 and #self.friends > 1 then
			return "ex_nihilo"
		end

		if ABCDE(self, sgs.Sanguosha:cloneCard("savage_assault"), self.friends) > 1
				and #self.enemies > 1 then
			if math.random() > 0.4 then
				return "savage_assault"
			else
				return "archery_attack"
			end
		end


	--end

	if self.player:isWounded() then
		for _, friend in ipairs(self.friends_noself) do
			if friend:isWounded() then
				return "god_salvation"
			end
		end
	end
end
sgs.ai_skill_use_func["#luajiance"] = function(cardF, use, self)
	local weiji = false
	for _, friend in ipairs(self.friends) do
		if self:isWeak(friend) then
			weiji = true
		end
	end

	local weiji2 = false
	for _, enemy in ipairs(self.enemies) do
		if self:isWeak(enemy) then
			weiji2 = true
		end
	end

	local card1, card2 = luajianceSubcard(self)
	local patten = usewhatcard2(self, weiji, weiji2)
	if not patten then return end

	use.card = sgs.Card_Parse("#luajiance:" .. card1:getId() .. "+" .. card2:getId() ..":")
	if use.to then
		self.room:writeToConsole("jiance ai test " .. patten)
		self.room:setPlayerFlag(self.player, "luajiance|" .. patten)
		if patten == "savage_assault" or patten == "archery_attack" or patten == "indulgence" then
			local enemies = self.enemies
			self:sort(enemies, "defense")
			local cardA = sgs.Sanguosha:cloneCard(patten)
			for _, enemy in ipairs(enemies) do
				if  not self.room:isProhibited(self.player, enemy, cardA) and not self.player:isCardLimited(cardA, sgs.Card_MethodUse)
						and self:hasTrickEffective(cardA, enemy, self.player) then
					use.to:append(enemy)
					if use.to:length() > 1 then break end
				end
			end
		elseif patten == "god_salvation" then
			local friends = self.friends
			self:sort(friends, "defense")
			for _, friend in ipairs(self.friends) do
				if friend:isWounded() then use.to:append(friend) end
				if use.to:length() > 1 then break end
			end
		elseif patten == "ex_nihilo" and #self.friends_noself > 1 and self:getOverflow() >= 0 then
			local friends = self.friends_noself
			self:sort(friends, "handcard")
			for _, friend in ipairs(friends) do
				use.to:append(friend)
				if use.to:length() > 1 then break end
			end
		elseif patten == "snatch" then
			local snatch = sgs.Sanguosha:cloneCard("dismantlement")
			snatch:setSkillName("luajiance")
			local dummy_use = { isDummy = true , to = sgs.SPlayerList()}
			self:useTrickCard(snatch, dummy_use)
			if dummy_use.card and dummy_use.to:length() > 1 then
				use.to = dummy_use.to
			end
		else
			local friends = self.friends
			self:sort(friends, "defense")
			for _, friend in ipairs(friends) do
				use.to:append(friend)
				if use.to:length() > 1 then break end
			end
		end
		return
	end
end
sgs.ai_skill_choice.luajianceX = function(self, choices)
	self.room:writeToConsole("erin bugQ")
	if self.player:hasFlag("luajiance|archery_attack") then self.room:writeToConsole("erin bugA");return "archery_attack" end
	if self.player:hasFlag("luajiance|savage_assault") then self.room:writeToConsole("erin bugF");return "savage_assault" end
	if self.player:hasFlag("luajiance|indulgence") then self.room:writeToConsole("erin bugB");return "indulgence" end
	if self.player:hasFlag("luajiance|god_salvation") then self.room:writeToConsole("erin bugC");return "god_salvation" end
	if self.player:hasFlag("luajiance|ex_nihilo") then self.room:writeToConsole("erin bugD");return "ex_nihilo" end
	if self.player:hasFlag("luajiance|snatch") then self.room:writeToConsole("erin bugEs");return "snatch" end
	if self.player:hasFlag("luajiance|dismantlement") then self.room:writeToConsole("erin bugEs");return "dismantlement" end
	if self.player:hasFlag("luajiance|banquet") then self.room:writeToConsole("erin bugD");return "ex_nihilo" end
	self.room:writeToConsole("erin bug")
end
sgs.ai_use_priority.luajiance = 5.5

local luasushen_skill = {}
luasushen_skill.name = "luasushen"
table.insert(sgs.ai_skills,luasushen_skill)
luasushen_skill.getTurnUseCard = function(self)
	if self.player:getMark("@sushen") == 0 then return end
	return sgs.Card_Parse("#luasushen:.:")
end
sgs.ai_skill_use_func["#luasushen"] = function(cardF, use, self)
	local function Cac(playser)
		local x = 4 - playser:getHp()
		x = math.min(playser:getLostHp(), x)
		return x
	end
	local compare_func = function(a, b)
		local v1 = Cac(a)
		local v2 = Cac(b)
		return v1 > v2
	end

	table.sort(self.friends, compare_func)
	for _, friend in ipairs(self.friends) do
		local x = Cac(friend)
		if x > 1 then
			use.card = sgs.Card_Parse("#luasushen:.:")
			if use.to then
				use.to:append(friend)
			end
			return
		end
	end
	if self:isWeak() then
		for _, friend in ipairs(self.friends) do
			local x = Cac(friend)
			if x >= 1 then
				use.card = sgs.Card_Parse("#luasushen:.:")
				if use.to then
					use.to:append(friend)
				end
				return
			end
		end
	end
end
sgs.ai_use_priority.luasushen = 9.5

sgs.pay_ai_card.Peach.LuaChunguang = function(self, card, use, mustusepeach)
	if self.player:hasSkill("LuaChunguang") then
		if card:getNumber() < self.player:getHp() * 2 and card:getNumber() >= self.player:getLostHp() * 2 then return 2 end
	end
end 
sgs.ai_skill_choice.LuaChunguang = function(self, choices, data)
	local use = data:toCardUse()
	local card = use.card
	if card:isKindOf("AOE") or card:isKindOf("AmazingGrace") or card:isKindOf("GodSalvation") then
		return "remove"
	else
		return "add"
	end
end

sgs.ai_skill_playerchosen.LuaChunguang = function(self, targets)
	local cardX = self.room:getTag("LuaChunguangTC"):toCard()
	if cardX then
		if cardX:isKindOf("Slash") then
			local targetlist = sgs.QList2Table(targets)
			self:sort(targetlist, "defenseSlash")
			for _, target in ipairs(targetlist) do	--杀敌
				if self:isEnemy(target) and self:Skadi2(cardX, target) and targets:contains(target) then
					return target
				end
			end
		elseif cardX:isKindOf("Peach") or cardX:isKindOf("ExNihilo") then
			local targetlist = sgs.QList2Table(targets)
			self:sort(targetlist, "defense")
			for _, target in ipairs(targetlist) do
				if self:isFriend(target) and targets:contains(target) then
					return target
				end
			end
		elseif cardX:isKindOf("Duel") then
			local targetlist = sgs.QList2Table(targets)
			self:sort(targetlist, "defense")
			for _, target in ipairs(targetlist) do
				if self:isEnemy(target) and self:hasTrickEffective(cardX, target) and targets:contains(target)
						and self:damageIsEffective(target,sgs.DamageStruct_Normal) and not self.room:isProhibited(self.player, target, cardX) then
					return target
				end
			end
		elseif cardX:isKindOf("Dismantlement") or cardX:isKindOf("Snatch") or cardX:isKindOf("FaithCollection") then
			local dummy_use = { isDummy = true , extra_target = 99, to = sgs.SPlayerList() }
			self:useCardSnatchOrDismantlement(cardX, dummy_use)
			local targetlist = sgs.QList2Table(targets)
			if not dummy_use.card then self.room:writeToConsole("junko test snatch failed") end
			for _, p in sgs.qlist(dummy_use.to) do
				for _, target in ipairs(targetlist) do
					if target:objectName() == p:objectName() and targets:contains(target) then return target end
				end
			end
		end
	else
		self.room:writeToConsole("junko test failed")
	end
	return nil

end
sgs.ai_skill_playerchosen.LuaChunguang2 = function(self, targets)
	local cardX = self.room:getTag("LuaChunguangTC"):toCard()
	if cardX then
		if cardX:isKindOf("AOE") then
			local function compare_func2(a, b)
				return self:getAoeValueTo(cardX, a, self.player) < self:getAoeValueTo(cardX, b, self.player)
			end
			local targetlist = sgs.QList2Table(targets)
			table.sort(targetlist, compare_func2)
			for _, target in ipairs(targetlist) do
				if self:isFriend(target) then
					return target
				end
			end
		end
	elseif cardX:isKindOf("GodSalvation") then
		local targetlist = sgs.QList2Table(targets)
		self:sort(targetlist, "defense")
		for _, target in ipairs(targetlist) do
			if self:isEnemy(target) and target:isWounded() then
				return target
			end
		end
	elseif cardX:isKindOf("AmazingGrace") then
		local targetlist = sgs.QList2Table(targets)
		self:sort(targetlist, "defense")
		for _, target in ipairs(targetlist) do
			if self:isEnemy(target) then
				return target
			end
		end
	end
end
sgs.ai_skill_choice.LuaWeishi = function(self, choices, data)
	if self.player:getHp() > 2 then return "maxhp" end
	if not self.player:isWounded() then return "maxhp" end
	return "hp"
end

sgs.ai_cardneed.LuaWeishi = function(to, card, self)
	return card:isKindOf("Weapon") and not to:getWeapon()
end


