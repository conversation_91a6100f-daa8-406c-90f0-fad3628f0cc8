---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by Administrator.
--- DateTime: 2020/9/24 12:43
---

sgs.ai_skill_playerchosen.luajieyou = function(self, targets, slashX)
    local targetlist = sgs.QList2Table(targets)
    self:sort(targetlist, "handcard2")
    for _, target in ipairs(targetlist) do
        if self:isFriend(target) then
            local xo = self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt() + 1
            if target:getMark("lualiangxiaoA") ~= xo then
                for _, card in sgs.list(target:getHandcards()) do
                    if isCard("Analeptic", card, target) then
                        return target
                    end
                end
                if getCardsNum("Analeptic", target, self.player) >= 1 then
                    return target
                end
            end
        end
    end
    for _, target in ipairs(targetlist) do
        if self:isFriend(target) then
            return target
        end
    end
end

local lualiangxiao_skill = {}
lualiangxiao_skill.name = "lualiangxiao"
table.insert(sgs.ai_skills, lualiangxiao_skill)
lualiangxiao_skill.getTurnUseCard = function(self)
    if self.player:hasUsed("#lualiangxiao") then return end
    return sgs.Card_Parse("#lualiangxiao:.:")
end
sgs.ai_skill_use_func["#lualiangxiao"] = function(X, use, self)
    local cards = self.player:getHandcards()
    cards = sgs.QList2Table(cards)
    self:sortByKeepValue(cards)
    if not cards[1]:isKindOf("Peach") then
        self:sort(self.friends, "defense")
        self:sort(self.friends_noself, "defense")
        for _, friend in ipairs(self.friends_noself) do
            local xo = self.room:getLord():getRoom():getTag("TurnLengthCount"):toInt() + 1
            if friend:getMark("lualiangxiaoA") ~= xo then
                use.card = sgs.Card_Parse("#lualiangxiao:" .. cards[1]:getEffectiveId()..":")
                if use.to then use.to:append(friend) end
                return
            end
        end
        for _, friend in ipairs(self.friends) do
            use.card = sgs.Card_Parse("#lualiangxiao:" .. cards[1]:getEffectiveId()..":")
            if use.to then use.to:append(friend) end
            return
        end
    end
end
sgs.ai_use_priority.lualiangxiao = 4.2
sgs.ai_card_intention.lualiangxiao = -20

sgs.ai_skill_invoke.lualiangxiao = function(self, data)
    local target = data:toPlayer()
    if target and target:isAlive() then
        if self:isFriend(target) then return true end
        for _, skill in sgs.qlist(target:getVisibleSkillList()) do
            if not string.find(skill:getDescription(), sgs.Sanguosha:translate("luapaoying3"))
                and not target:hasSkill("Luaxianzhe") then
                return true
            end
        end
    end
end
