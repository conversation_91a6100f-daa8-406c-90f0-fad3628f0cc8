-- SP稀神探女扩
module("extensions.spspsp", package.seeall)
extension = sgs.Package("sp_package")

-- 创建SP稀神探女武将
sp_uranomiya = sgs.General(extension, "sp_uranomiya", "god", 3, false)

-- 诳言技能卡：与其他角色拼点
kuangyan_card = sgs.CreateSkillCard{
    name = "kuangyan",
    target_fixed = false,
    will_throw = false,
    filter = function(self, targets, to_select)
        return #targets == 0 and to_select:objectName() ~= sgs.Self:objectName()
    end,
    on_use = function(self, room, source, targets)
        room:notifySkillInvoked(source, "kuangyan")
        local success = source:pindian(targets[1], "kuangyan", nil)

        -- 检查拼点结果：只有稀神探女拼点成功时才继续
        local source_card_tag = source:getTag("KuangyanPindianCard")
        local target_card_tag = targets[1]:getTag("KuangyanPindianCard")
        local pindian_success = false

        if source_card_tag and target_card_tag then
            local source_card_id = source_card_tag:toInt()
            local target_card_id = target_card_tag:toInt()
            if source_card_id > 0 and target_card_id > 0 then
                local source_card = sgs.Sanguosha:getCard(source_card_id)
                local target_card = sgs.Sanguosha:getCard(target_card_id)
                if source_card:getNumber() > target_card:getNumber() then
                    pindian_success = true
                end
            end
        end

        if pindian_success then
            -- 拼点成功，让稀神探女选择拼点牌的使用目标
            local chosen_target = room:askForPlayerChosen(source, room:getAlivePlayers(),
                "kuangyan", "@kuangyan-choose:" .. targets[1]:objectName(), false)

            -- 如果没有选择目标，默认选择拼点对象
            if not chosen_target then
                chosen_target = targets[1]
            end

            if chosen_target then
                -- 获取拼点牌信息
                local source_card_tag = source:getTag("KuangyanPindianCard")
                local target_card_tag = targets[1]:getTag("KuangyanPindianCard")

                if source_card_tag and target_card_tag then
                    local source_card_id = source_card_tag:toInt()
                    local target_card_id = target_card_tag:toInt()

                    local cards_to_use = {}
                    if source_card_id > 0 then
                        table.insert(cards_to_use, sgs.Sanguosha:getCard(source_card_id))
                    end
                    if target_card_id > 0 then
                        table.insert(cards_to_use, sgs.Sanguosha:getCard(target_card_id))
                    end

                    local remaining_cards = {}

                    -- 拼点对象依次使用拼点牌
                    for _, card in ipairs(cards_to_use) do
                        if card and not card:isVirtualCard() then
                            local can_use = false

                            -- 使用正确的函数检查是否可以使用这张牌
                            local can_use = false

                            -- 检查角色是否被限制使用这张牌
                            if not targets[1]:isCardLimited(card, sgs.Card_MethodUse) then
                                -- 检查角色是否可以使用这张牌
                                if card:isAvailable(targets[1]) then
                                    -- 检查是否被禁止对目标使用
                                    if not targets[1]:isProhibited(chosen_target, card) then
                                        local use = sgs.CardUseStruct()
                                        use.from = targets[1]
                                        use.card = card

                                        -- 根据卡牌类型设置目标
                                        if card:getTypeId() == sgs.Card_TypeEquip then
                                            -- 装备牌装备给自己
                                            use.to:append(targets[1])
                                            room:useCard(use, false)
                                            can_use = true
                                        elseif card:targetFixed() then
                                            -- 无目标牌（如无中生有）
                                            room:useCard(use, false)
                                            can_use = true
                                        else
                                            -- 有目标的牌
                                            use.to:append(chosen_target)
                                            room:useCard(use, false)
                                            can_use = true
                                        end
                                    end
                                end
                            end

                            if not can_use then
                                table.insert(remaining_cards, card)
                            end
                        end
                    end

                    -- 只有第一张剩余牌可以当乐不思蜀对拼点对象自身使用
                    if #remaining_cards > 0 then
                        local first_remaining_card = remaining_cards[1]
                        local indulgence = sgs.Sanguosha:cloneCard("indulgence", first_remaining_card:getSuit(), first_remaining_card:getNumber())
                        indulgence:addSubcard(first_remaining_card:getId())
                        indulgence:setSkillName("kuangyan")

                        local indulgence_use = sgs.CardUseStruct()
                        indulgence_use.from = targets[1]  -- 拼点对象使用
                        indulgence_use.to:append(source)  -- 对拼点赢得者（稀神探女）使用
                        indulgence_use.card = indulgence
                        room:useCard(indulgence_use, false)
                    end
                end
            end
        end
    end
}

-- 诳言技能：零牌技能，响应@@kuangyan时可发动
kuangyan = sgs.CreateZeroCardViewAsSkill{
    name = "kuangyan",
    view_as = function()
        return kuangyan_card:clone()
    end,
    enabled_at_play = function(self, player)
        return false
    end,
    enabled_at_response = function(self, player, pattern)
        return pattern == "@@kuangyan"
    end
}

-- 诳言触发技：其他角色出牌阶段开始时触发
kuangyan_trigger = sgs.CreateTriggerSkill{
    name = "#kuangyan_trigger",
    events = {sgs.EventPhaseStart, sgs.PindianVerifying},
    view_as_skill = kuangyan,
    can_trigger = function(self, target)
        return target ~= nil
    end,
    on_trigger = function(self, event, player, data, room)
        if event == sgs.EventPhaseStart then
            if player:getPhase() == sgs.Player_Play then
                for _, p in sgs.qlist(room:getOtherPlayers(player)) do
                    if p:hasSkill("kuangyan") and p:canPindian(player) then
                        room:askForUseCard(p, "@@kuangyan", "@kuangyan-pindian:" .. player:objectName())
                    end
                end
            end
        elseif event == sgs.PindianVerifying then
            local pindian = data:toPindian()
            if pindian.reason == "kuangyan" then
                pindian.from:setTag("KuangyanPindianCard", sgs.QVariant(pindian.from_card:getId()))
                pindian.to:setTag("KuangyanPindianCard", sgs.QVariant(pindian.to_card:getId()))

                -- 拼点结算后处理拼点牌使用
                if pindian.from_card:getNumber() > pindian.to_card:getNumber() then
                    -- 稀神探女拼点成功，让其选择拼点牌的使用目标
                    local chosen_target = room:askForPlayerChosen(pindian.from, room:getAlivePlayers(),
                        "kuangyan", "@kuangyan-choose:" .. pindian.to:objectName(), false)

                    -- 如果没有选择目标，默认选择拼点对象
                    if not chosen_target then
                        chosen_target = pindian.to
                    end

                    if chosen_target then
                        local cards_to_use = {pindian.from_card, pindian.to_card}
                        local remaining_cards = {}

                        -- 拼点对象依次使用拼点牌，使用正确的判断逻辑
                        for _, card in ipairs(cards_to_use) do
                            if card and not card:isVirtualCard() then
                                local can_use = false

                                -- 使用正确的函数检查是否可以使用这张牌
                                if not pindian.to:isCardLimited(card, sgs.Card_MethodUse) then
                                    if card:isAvailable(pindian.to) then
                                        if not pindian.to:isProhibited(chosen_target, card) then
                                            local use = sgs.CardUseStruct()
                                            use.from = pindian.to
                                            use.card = card

                                            -- 根据卡牌类型设置目标
                                            if card:getTypeId() == sgs.Card_TypeEquip then
                                                -- 装备牌装备给自己
                                                use.to:append(pindian.to)
                                                room:useCard(use, false)
                                                can_use = true
                                            elseif card:targetFixed() then
                                                -- 无目标牌（如无中生有）
                                                room:useCard(use, false)
                                                can_use = true
                                            else
                                                -- 有目标的牌
                                                use.to:append(chosen_target)
                                                room:useCard(use, false)
                                                can_use = true
                                            end
                                        end
                                    end
                                end

                                if not can_use then
                                    table.insert(remaining_cards, card)
                                end
                            end
                        end

                        -- 只有第一张剩余牌可以当乐不思蜀对拼点赢得者使用
                        if #remaining_cards > 0 then
                            local first_remaining_card = remaining_cards[1]
                            local indulgence = sgs.Sanguosha:cloneCard("indulgence", first_remaining_card:getSuit(), first_remaining_card:getNumber())
                            indulgence:addSubcard(first_remaining_card:getId())
                            indulgence:setSkillName("kuangyan")

                            local indulgence_use = sgs.CardUseStruct()
                            indulgence_use.from = pindian.to  -- 拼点对象使用
                            indulgence_use.to:append(pindian.from)  -- 对拼点赢得者（稀神探女）使用
                            indulgence_use.card = indulgence
                            room:useCard(indulgence_use, false)
                        end
                    end
                end
            end
        end
        return false
    end,
    global = true
}


-- 天矢使用记录检测技能：全局监控角色使用牌情况
tianshi_record = sgs.CreateTriggerSkill{
    name = "#tianshi_record",
    events = {sgs.EventPhaseStart, sgs.CardUsed},
    can_trigger = function(self, target)
        return target ~= nil
    end,
    on_trigger = function(self, event, player, data, room)
        if event == sgs.EventPhaseStart then
            -- 任何角色回合开始时，给其设置"未使用牌"标记
            if player:getPhase() == sgs.Player_RoundStart then
                player:setFlags("tianshi_no_card_used")
            end
        elseif event == sgs.CardUsed then
            -- 角色使用牌时，移除"未使用牌"标记
            if player:hasFlag("tianshi_no_card_used") then
                player:setFlags("-tianshi_no_card_used")
            end
        end
        return false
    end,
    global = true
}

-- 天矢技能：拼点结算后将弃牌堆顶牌置于牌堆底，限定技让角色使用牌堆底牌
tianshi = sgs.CreateTriggerSkill{
    name = "tianshi",
    events = {sgs.Pindian, sgs.EventPhaseEnd},
    frequency = sgs.Skill_Limited,
    limit_mark = "@tianshi",
    can_trigger = function(self, target)
        return target ~= nil
    end,
    on_trigger = function(self, event, player, data, room)
        if event == sgs.Pindian then
            -- 拼点结算后，将弃牌堆顶的牌置于牌堆底
            local pindian = data:toPindian()
            -- 检查是否有稀神探女参与拼点
            local uranomiya = nil
            if pindian.from:hasSkill("tianshi") then
                uranomiya = pindian.from
            elseif pindian.to:hasSkill("tianshi") then
                uranomiya = pindian.to
            end

            if uranomiya and not room:getDiscardPile():isEmpty() then
                local top_card_id = room:getDiscardPile():first()
                local top_card = sgs.Sanguosha:getCard(top_card_id)

                -- 将弃牌堆顶的牌移动到牌堆底
                room:moveCardTo(top_card, nil, sgs.Player_DrawPile,
                    sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_PUT, uranomiya:objectName(), "tianshi", ""), false)

                -- 发送日志
                room:sendLog("#TianshiMove", uranomiya, "tianshi", sgs.QVariant(), top_card:getEffectiveId())
            end

        elseif event == sgs.EventPhaseEnd then
            -- 限定技：没有使用过牌的角色回合结束时
            if player:getPhase() == sgs.Player_Play then
                -- 检查是否还有"未使用牌"标记
                if player:hasFlag("tianshi_no_card_used") then
                    -- 寻找有天矢技能且有限定技标记的角色
                    for _, uranomiya in sgs.qlist(room:findPlayersBySkillName("tianshi")) do
                        if uranomiya:getMark("@tianshi") > 0 then
                            if room:askForSkillInvoke(uranomiya, "tianshi",
                                sgs.QVariant("limited:" .. player:objectName())) then
                                room:removePlayerMark(uranomiya, "@tianshi")
                                room:notifySkillInvoked(uranomiya, "tianshi")

                                -- 令其依次对自身使用牌堆底的牌，直到不能使用为止
                                local max_iterations = 50  -- 防止无限循环
                                local iterations = 0
                                local can_continue = true

                                while can_continue and iterations < max_iterations and not room:getDrawPile():isEmpty() do
                                    iterations = iterations + 1

                                    local bottom_card_id = room:getDrawPile():last()
                                    local bottom_card = sgs.Sanguosha:getCard(bottom_card_id)

                                    -- 将牌从牌堆底移到手牌
                                    room:moveCardTo(bottom_card, player, sgs.Player_PlaceHand,
                                        sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_GOTCARD,
                                        player:objectName(), "tianshi", ""))

                                    -- 使用正确的函数检查是否可以使用这张牌
                                    local can_use = false

                                    -- 检查角色是否被限制使用这张牌
                                    if not player:isCardLimited(bottom_card, sgs.Card_MethodUse) then
                                        -- 检查角色是否可以使用这张牌
                                        if bottom_card:isAvailable(player) then
                                            -- 让角色选择目标使用这张牌
                                            local chosen_target = room:askForPlayerChosen(player, room:getAlivePlayers(),
                                                "tianshi", "@tianshi-use:" .. bottom_card:objectName(), true)

                                            if chosen_target then
                                                -- 检查是否被禁止对目标使用
                                                if not player:isProhibited(chosen_target, bottom_card) then
                                                    local use = sgs.CardUseStruct()
                                                    use.from = player
                                                    use.card = bottom_card

                                                    -- 根据卡牌类型设置目标
                                                    if bottom_card:getTypeId() == sgs.Card_TypeEquip then
                                                        -- 装备牌装备给自己
                                                        use.to:append(player)
                                                        room:useCard(use, false)
                                                        can_use = true
                                                    elseif bottom_card:targetFixed() then
                                                        -- 无目标牌（如无中生有）
                                                        room:useCard(use, false)
                                                        can_use = true
                                                    else
                                                        -- 有目标的牌
                                                        use.to:append(chosen_target)
                                                        room:useCard(use, false)
                                                        can_use = true
                                                    end
                                                end
                                            end
                                        end
                                    end

                                    if not can_use then
                                        -- 如果牌不能使用，弃掉这张牌并结束技能
                                        room:moveCardTo(bottom_card, nil, sgs.Player_DiscardPile,
                                            sgs.CardMoveReason(sgs.CardMoveReason_S_REASON_NATURAL_ENTER,
                                            player:objectName(), "tianshi", ""))
                                        can_continue = false
                                    end
                                end

                                if iterations >= max_iterations then
                                    room:sendLog("#TianshiMaxIterations", uranomiya, "tianshi")
                                end
                            end
                        end
                    end
                end
            end
        end
        return false
    end,
    global = true
}

-- 添加技能到武将
sp_uranomiya:addSkill(kuangyan)
sp_uranomiya:addSkill(kuangyan_trigger)
sp_uranomiya:addSkill(tianshi_record)
sp_uranomiya:addSkill(tianshi)

sgs.LoadTranslationTable{
    ["sp_package"] = "SP稀神探女包",
    ["sp_uranomiya"] = "SP稀神探女",
    ["#sp_uranomiya"] = "天探女",

    -- 诳言技能
    ["kuangyan"] = "诳言",
    [":kuangyan"] = "其他角色的出牌阶段开始时，你可以与其拼点。若你赢，其对你指定的一名角色依此使用拼点牌，将余下的拼点牌当【乐不思蜀】对自身使用。",
    ["@kuangyan-pindian"] = "你可以发动\"诳言\"与 %src 拼点",
    ["@kuangyan-choose"] = "诳言：请选择 %src 拼点牌的使用目标",
    ["~kuangyan"] = "选择一名角色→点击确定",

    -- 天矢技能
    ["tianshi"] = "天矢",
    [":tianshi"] = "拼点结算后，将弃牌堆顶的牌置于牌堆底。限定技：没有使用过牌的角色回合结束时，你可以令其依次对自身使用牌堆底的牌（无限制），直到不能使用为止。",
    ["@tianshi"] = "天矢",
    ["@tianshi-limited"] = "天矢：你可以令 %src 依次使用牌堆底的牌",
    ["@tianshi-use"] = "天矢：请选择 %src 的使用目标",
    ["~tianshi"] = "点击确定发动天矢限定技",

    -- 天矢日志
    ["#TianshiMove"] = "%from 的\"%arg\"将弃牌堆顶的牌置于牌堆底",
    ["#TianshiMaxIterations"] = "%from 的\"%arg\"达到最大执行次数限制",
}
