-- SP稀神探女扩展包
module("extensions.spspsp", package.seeall)
extension = sgs.Package("sp_package")

-- 创建SP稀神探女武将
sp_uranomiya = sgs.General(extension, "sp_uranomiya", "god", 3, false)

-- 诳言技能卡：与其他角色拼点
kuangyan_card = sgs.CreateSkillCard{
    name = "kuangyan",
    target_fixed = false,
    will_throw = false,
    filter = function(self, targets, to_select)
        return #targets == 0 and to_select:objectName() ~= sgs.Self:objectName()
    end,
    on_use = function(self, room, source, targets)
        room:notifySkillInvoked(source, "kuangyan")
        local success = source:pindian(targets[1], "kuangyan", nil)
        if success then
            -- 拼点成功，让稀神探女选择拼点牌的使用目标
            local chosen_target = room:askForPlayerChosen(source, room:getAlivePlayers(),
                "kuangyan", "@kuangyan-choose:" .. targets[1]:objectName(), false)

            if chosen_target then
                -- 获取拼点牌信息
                local source_card_tag = source:getTag("KuangyanPindianCard")
                local target_card_tag = targets[1]:getTag("KuangyanPindianCard")

                if source_card_tag and target_card_tag then
                    local source_card_id = source_card_tag:toInt()
                    local target_card_id = target_card_tag:toInt()

                    local cards_to_use = {}
                    if source_card_id > 0 then
                        table.insert(cards_to_use, sgs.Sanguosha:getCard(source_card_id))
                    end
                    if target_card_id > 0 then
                        table.insert(cards_to_use, sgs.Sanguosha:getCard(target_card_id))
                    end

                    local remaining_cards = {}

                    -- 拼点对象依次使用拼点牌
                    for _, card in ipairs(cards_to_use) do
                        if card and not card:isVirtualCard() then
                            local can_use = false
                            local use = sgs.CardUseStruct()
                            use.from = targets[1]
                            use.card = card

                            if card:isKindOf("Slash") then
                                -- 杀：对选择的目标使用
                                use.to:append(chosen_target)
                                room:useCard(use, false)
                                can_use = true
                            elseif card:isKindOf("Peach") or card:isKindOf("Analeptic") then
                                -- 桃和酒：对选择的目标使用
                                use.to:append(chosen_target)
                                room:useCard(use, false)
                                can_use = true
                            elseif card:isKindOf("EquipCard") then
                                -- 装备牌：装备到选择的目标身上
                                use.to:append(chosen_target)
                                room:useCard(use, false)
                                can_use = true
                            elseif card:isKindOf("TrickCard") then
                                -- 锦囊牌：对选择的目标使用
                                if card:isKindOf("AOE") or card:targetFixed() then
                                    -- 群体锦囊或无目标锦囊
                                    room:useCard(use, false)
                                    can_use = true
                                else
                                    -- 单体锦囊
                                    use.to:append(chosen_target)
                                    room:useCard(use, false)
                                    can_use = true
                                end
                            end

                            if not can_use then
                                table.insert(remaining_cards, card)
                            end
                        end
                    end

                    -- 将所有剩余牌合并成一个乐不思蜀对稀神探女使用
                    if #remaining_cards > 0 then
                        -- 使用第一张剩余牌的花色和点数作为乐不思蜀的基础
                        local first_card = remaining_cards[1]
                        local indulgence = sgs.Sanguosha:cloneCard("indulgence", first_card:getSuit(), first_card:getNumber())

                        -- 将所有剩余牌作为子卡添加到乐不思蜀中
                        for _, remaining_card in ipairs(remaining_cards) do
                            indulgence:addSubcard(remaining_card:getId())
                        end
                        indulgence:setSkillName("kuangyan")

                        local indulgence_use = sgs.CardUseStruct()
                        indulgence_use.from = targets[1]  -- 拼点对象使用
                        indulgence_use.to:append(source)  -- 对稀神探女使用
                        indulgence_use.card = indulgence
                        room:useCard(indulgence_use, false)
                    end
                end
            end
        end
    end
}

-- 诳言技能：零牌技能，响应@@kuangyan时可发动
kuangyan = sgs.CreateZeroCardViewAsSkill{
    name = "kuangyan",
    view_as = function()
        return kuangyan_card:clone()
    end,
    enabled_at_play = function(self, player)
        return false
    end,
    enabled_at_response = function(self, player, pattern)
        return pattern == "@@kuangyan"
    end
}

-- 诳言触发技：其他角色出牌阶段开始时触发
kuangyan_trigger = sgs.CreateTriggerSkill{
    name = "#kuangyan_trigger",
    events = {sgs.EventPhaseStart, sgs.PindianVerifying},
    view_as_skill = kuangyan,
    can_trigger = function(self, target)
        return target ~= nil
    end,
    on_trigger = function(self, event, player, data, room)
        if event == sgs.EventPhaseStart then
            if player:getPhase() == sgs.Player_Play then
                for _, p in sgs.qlist(room:getOtherPlayers(player)) do
                    if p:hasSkill("kuangyan") and p:canPindian(player) then
                        room:askForUseCard(p, "@@kuangyan", "@kuangyan-pindian:" .. player:objectName())
                    end
                end
            end
        elseif event == sgs.PindianVerifying then
            local pindian = data:toPindian()
            if pindian.reason == "kuangyan" then
                pindian.from:setTag("KuangyanPindianCard", sgs.QVariant(pindian.from_card:getId()))
                pindian.to:setTag("KuangyanPindianCard", sgs.QVariant(pindian.to_card:getId()))
            end
        end
        return false
    end,
    global = true
}


-- 添加技能到武将
sp_uranomiya:addSkill(kuangyan)
sp_uranomiya:addSkill(kuangyan_trigger)

sgs.LoadTranslationTable{
    ["sp_package"] = "SP稀神探女包",
    ["sp_uranomiya"] = "SP稀神探女",
    ["#sp_uranomiya"] = "天矢之巫女",
    ["designer:sp_uranomiya"] = "QSanguosha-v2开发组",
    ["illustrator:sp_uranomiya"] = "东方Project",
    ["cv:sp_uranomiya"] = "无",

    -- 诳言技能
    ["kuangyan"] = "诳言",
    [":kuangyan"] = "其他角色的出牌阶段开始时，你可以与其拼点。若你赢，其对你指定的一名角色依此使用拼点牌，将余下的拼点牌当【乐不思蜀】对自身使用。",
    ["@kuangyan-pindian"] = "你可以发动\"诳言\"与 %src 拼点",
    ["@kuangyan-choose"] = "诳言：请选择 %src 拼点牌的使用目标",
    ["~kuangyan"] = "选择一名角色→点击确定",
    ["$kuangyan1"] = "哼哼，你上当了~",
    ["$kuangyan2"] = "我说的话，可不要全信哦~",

    -- 日志信息
    ["#KuangyanSuccess"] = "%from 的\"%arg\"拼点成功，对 %dest 发动效果",
    ["#KuangyanFailed"] = "%from 的\"%arg\"拼点失败",

    -- 死亡台词
    ["~sp_uranomiya"] = "我...我的箭矢...怎么会...",

    -- 天矢技能（翻译保留，但未实现）
    ["tianshi"] = "天矢",
    [":tianshi"] = "拼点结算后，将弃牌堆顶的牌置于牌堆底。限定技：没有使用过牌的角色回合结束时，你可以令其依次对自身使用牌堆底的牌（无限制），直到不能使用为止。",
    ["@tianshi"] = "天矢",
}
