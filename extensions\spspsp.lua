-- SP稀神探女扩展包
module("extensions.spspsp", package.seeall)
extension = sgs.Package("sp_package")

-- 创建SP稀神探女武将
sp_uranomiya = sgs.General(extension, "sp_uranomiya", "god", 3, false)

-- 诳言技能卡：与其他角色拼点，成功后造成伤害并使对方乐不思蜀
kuangyan_card = sgs.CreateSkillCard{
    name = "kuangyan",
    target_fixed = false,
    will_throw = false,
    filter = function(self, targets, to_select)
        return #targets == 0 and to_select:objectName() ~= sgs.Self:objectName()
    end,
    on_use = function(self, room, source, targets)
        room:notifySkillInvoked(source, "kuangyan")
        local success = source:pindian(targets[1], "kuangyan", nil)
        if success then
            -- 拼点成功，执行诳言技能效果
            -- 获取拼点牌信息
            local source_card_id = source:getTag("KuangyanPindianCard"):toInt()
            local target_card_id = targets[1]:getTag("KuangyanPindianCard"):toInt()

            -- 步骤1：选择拼点牌的使用目标
            local chosen_target = room:askForPlayerChosen(source, room:getAlivePlayers(),
                "kuangyan", "@kuangyan-target:" .. targets[1]:objectName(), false)

            if chosen_target then
                -- 步骤2：处理拼点牌
                local pindian_cards = {}
                if source_card_id > 0 then
                    table.insert(pindian_cards, sgs.Sanguosha:getCard(source_card_id))
                end
                if target_card_id > 0 then
                    table.insert(pindian_cards, sgs.Sanguosha:getCard(target_card_id))
                end

                local remaining_cards = {}

                -- 步骤3：依次使用拼点牌
                for _, card in ipairs(pindian_cards) do
                    if card and not card:isVirtualCard() then
                        local can_use = false

                        if card:isKindOf("Slash") then
                            -- 杀：对指定目标使用
                            local use = sgs.CardUseStruct()
                            use.from = targets[1]
                            use.card = card
                            use.to:append(chosen_target)
                            room:useCard(use, false)
                            can_use = true
                        elseif card:isKindOf("Peach") then
                            -- 桃：对指定目标使用
                            local use = sgs.CardUseStruct()
                            use.from = targets[1]
                            use.card = card
                            use.to:append(chosen_target)
                            room:useCard(use, false)
                            can_use = true
                        elseif card:isKindOf("Analeptic") then
                            -- 酒：对指定目标使用
                            local use = sgs.CardUseStruct()
                            use.from = targets[1]
                            use.card = card
                            use.to:append(chosen_target)
                            room:useCard(use, false)
                            can_use = true
                        elseif card:isKindOf("EquipCard") then
                            -- 装备牌：装备到选择的目标身上
                            local use = sgs.CardUseStruct()
                            use.from = targets[1]
                            use.card = card
                            use.to:append(chosen_target)
                            room:useCard(use, false)
                            can_use = true
                        elseif card:isKindOf("TrickCard") then
                            -- 锦囊牌：根据类型设置目标
                            if card:isKindOf("DelayedTrick") then
                                -- 延时锦囊：检查距离和目标有效性
                                if card:targetFilter(sgs.PlayerList(), chosen_target, targets[1]) then
                                    local use = sgs.CardUseStruct()
                                    use.from = targets[1]
                                    use.card = card
                                    use.to:append(chosen_target)
                                    room:useCard(use, false)
                                    can_use = true
                                end
                            elseif card:canRecast() and (card:objectName() == "zhuge_crossbow" or card:objectName() == "crossbow") then
                                -- 明确的重铸牌（诸葛连弩等）
                                local use = sgs.CardUseStruct()
                                use.from = targets[1]
                                use.card = card
                                room:useCard(use, false)
                                can_use = true
                            elseif card:isKindOf("AOE") then
                                -- 群体锦囊（万箭、南蛮等）
                                local use = sgs.CardUseStruct()
                                use.from = targets[1]
                                use.card = card
                                room:useCard(use, false)
                                can_use = true
                            else
                                -- 单体锦囊（铁索连环等）
                                local use = sgs.CardUseStruct()
                                use.from = targets[1]
                                use.card = card
                                use.to:append(chosen_target)
                                room:useCard(use, false)
                                can_use = true
                            end
                        end

                        -- 不能使用的牌加入剩余列表
                        if not can_use then
                            table.insert(remaining_cards, card)
                        end
                    end
                end

                -- 步骤4：将剩余牌转换为乐不思蜀对技能发出者使用
                for _, remaining_card in ipairs(remaining_cards) do
                    local indulgence = sgs.Sanguosha:cloneCard("indulgence", remaining_card:getSuit(), remaining_card:getNumber())
                    indulgence:addSubcard(remaining_card:getId())
                    indulgence:setSkillName("kuangyan")

                    local indulgence_use = sgs.CardUseStruct()
                    indulgence_use.from = targets[1]
                    indulgence_use.to:append(source)
                    indulgence_use.card = indulgence
                    room:useCard(indulgence_use, false)
                end
            end
        end
    end
}

-- 诳言技能：零牌技能，响应@@kuangyan时可发动
kuangyan = sgs.CreateZeroCardViewAsSkill{
    name = "kuangyan",
    view_as = function()
        return kuangyan_card:clone()
    end,
    enabled_at_play = function(self, player)
        return false
    end,
    enabled_at_response = function(self, player, pattern)
        return pattern == "@@kuangyan"
    end
}

-- 诳言触发技：其他角色出牌阶段开始时触发
kuangyan_trigger = sgs.CreateTriggerSkill{
    name = "#kuangyan_trigger",
    events = {sgs.EventPhaseStart, sgs.PindianVerifying},
    view_as_skill = kuangyan,
    can_trigger = function(self, target)
        return target ~= nil
    end,
    on_trigger = function(self, event, player, data, room)
        if event == sgs.EventPhaseStart then
            if player:getPhase() == sgs.Player_Play then
                for _, p in sgs.qlist(room:getOtherPlayers(player)) do
                    if p:hasSkill("kuangyan") and p:canPindian(player) then
                        room:askForUseCard(p, "@@kuangyan", "@kuangyan-pindian:" .. player:objectName())
                    end
                end
            end
        elseif event == sgs.PindianVerifying then
            -- 保存拼点牌信息
            local pindian = data:toPindian()
            if pindian.reason == "kuangyan" then
                pindian.from:setTag("KuangyanPindianCard", sgs.QVariant(pindian.from_card:getId()))
                pindian.to:setTag("KuangyanPindianCard", sgs.QVariant(pindian.to_card:getId()))
            end
        end
        return false
    end,
    global = true
}


-- 添加技能到武将
sp_uranomiya:addSkill(kuangyan)
sp_uranomiya:addSkill(kuangyan_trigger)

sgs.LoadTranslationTable{
    ["sp_package"] = "114514",
    ["sp_uranomiya"] = "SP稀神探女",
    
    ["kuangyan"] = "诳言",
    [":kuangyan"] = "其他角色的出牌阶段开始时，你可以与其拼点。若你赢，其对你指定的一名角色依此使用拼点牌，将余下的拼点牌当【乐不思蜀】对自身使用。",
    ["@kuangyan-pindian"] = "你可以发动\"诳言\"与 %src 拼点",
    ["@kuangyan-target"] = "请选择 %src 拼点牌的目标",
    ["~kuangyan"] = "选择一名角色→点击确定",
    
    ["tianshi"] = "天矢",
    [":tianshi"] = "拼点结算后，将弃牌堆顶的牌置于牌堆底。 限定技：没有使用过牌的角色回合结束时，你可以令其依次对自身使用牌堆底的牌（无限制），直到不能使用为止。",
    ["@tianshi"] = "天矢",
}
